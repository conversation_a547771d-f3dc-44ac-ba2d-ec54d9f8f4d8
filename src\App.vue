<template>
  <el-config-provider :locale="locale">
    <el-scrollbar height="100vh" ref="scroll">
      <router-view></router-view>
    </el-scrollbar>
  </el-config-provider>
  <!-- 主题-获取主题 -->
  <ThemeSetting />
</template>

<script setup>
import axios from 'axios'
import { onMounted, computed, ref, watch } from 'vue';
import { useStore } from 'vuex';

import { useRouter } from 'vue-router';

import { ElNotification } from 'element-plus'

import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import checkUpdateString from '../public/static/checkUpdate.json'

import { closeWindow } from '$supersetResource/js/projects/apricot/index.js'
const locale = zhCn

const store = useStore();


const scroll = ref(null);

const router = useRouter();

const checkTimer = ref(null);



const changeBodyWidth = () => {
};

const changeResize = () => {
  changeBodyWidth();
};

const checkIfAppUpdated = () => {
  // if (process.env.NODE_ENV === 'development') return
  if (!checkUpdateString) return
  if (!checkTimer.value) {
    checkTimer.value = setInterval(() => {
      axios.get(window.location.origin + window.location.pathname + `static/checkUpdate.json?t=` + +new Date(), {
        timeout: 5000,
        headers: {
          'Cache-Control': 'no-cache,no-store',
          'If-Modified-Since': '0',
        }
      }).then(res => {
        if (res) {
          const data = (res.data);
          // console.log(data)
          if (data != checkUpdateString) {
            const refresh = () => {
              window.location.reload();
            }
            const tempRefresh = '_temp' + (Math.random()).toString(16).slice(2)
            window[tempRefresh] = refresh

            ElNotification({
              showClose: true,
              type: 'warning',
              title: '系统通知',
              dangerouslyUseHTMLString: true,
              message: `<div>
                        页面已更新，请进行保存或数据备份后刷新页面。<br/>
                        <div><button class="el-button el-button--primary" type="button" onClick="${tempRefresh}()">立即刷新</button></div>
                    </div>`,
              duration: 0
            });
            clearInterval(checkTimer.value)
          }
          return data;
        } else {
          return [];
        }
      }, () => {
        return []
      })
    }, 60000);
  } else {
    clearTimeout(checkTimer.value)
  }
}

onMounted(() => {

  changeBodyWidth();
  window.addEventListener('resize', changeResize);

  checkIfAppUpdated()

  
  /* title 设置----------------------------------------------- */
  
  let configs = window.configs || {};
  let oTitle = document.getElementsByTagName('title')[0];
  if(configs.names && configs.names.systemName){
      oTitle.innerText = configs.names.systemName;
  }

  /* -----------------------------------------------title 设置 */


  let _beforeUnload_time = 0
  
  window.addEventListener("beforeunload", () => {
    _beforeUnload_time = new Date().getTime();
  });
  window.addEventListener("unload", () => {
    let _gap_time = new Date().getTime() - _beforeUnload_time; 
    if (_gap_time <= 5) {
      //关闭浏览器
      closeWindow()
    }
  });
});
</script>

<style lang="scss">
#app {
  position: relative;
  width: 100%;
  height: 100%;
  font-family: "Helvetica Neue", Helvetica, Tahoma, Arial, 'PingFang SC', 'Source Han Sans CN', 'Source Han Sans', 'Source Han Serif', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', 'Microsoft YaHei', sans-serif;
  font-size: $base-font-size-default;
  color: var(--theme-color);
}
</style>
