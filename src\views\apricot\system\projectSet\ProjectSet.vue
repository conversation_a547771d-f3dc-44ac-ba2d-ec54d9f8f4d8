<template>
    <div class="scope-projectSet c-container">
        <ul class="c-left">
            <li v-for="item in tabList" :key="item.id"><span @click="activeTab = item.id" :class="[item.id === activeTab ? 'avtive' : '']">{{item.name}}</span></li>
        </ul>
        <div class="c-right">
            <!-- <keep-alive> -->
                <component :is="activeTab"></component>
            <!-- </keep-alive> -->
            <!-- <SetHospital v-show="activeTab === 'SetCampus'"></SetHospital>
            <SetDeviceType v-show="activeTab === 'SetDeviceType'"></SetDeviceType>
            <SetMachineRoom v-show="activeTab === 'SetMachineRoom'"></SetMachineRoom>
            <SetItem v-show="activeTab === 'SetItem'"></SetItem>
            <SetNuclide v-show="activeTab === 'SetNuclide'"></SetNuclide>
            <SetTracer v-show="activeTab === 'SetTracer'"></SetTracer>
            <SetItemPosition v-show="activeTab === 'SetItemPosition'"></SetItemPosition>
            <SetTestMode v-show="activeTab === 'SetTestMode'"></SetTestMode>
            <SetAccessNumRule v-show="activeTab === 'SetAccessNumRule'"></SetAccessNumRule> -->
        </div>
    </div>
</template>
<script>
/** 项目设置 */
import { SetHospital, SetDeviceType, SetMachineRoom, SetNuclide, SetTracer, SetItemPosition, SetItem, SetTestMode, SetAccessNumRule } from './components/index.js'
export default {
    name: 'ProjectSet',
    components: {
        SetHospital,
        SetDeviceType,
        SetMachineRoom,
        SetNuclide,
        SetTracer,
        SetItemPosition,
        SetItem,
        SetTestMode,
        SetAccessNumRule
    },
    props: {
    },
    data() {
        return {
            tabList: [
                { id: 'SetHospital', name: '院区'},
                { id: 'SetDeviceType', name: '设备类型'},
                { id: 'SetMachineRoom', name: '机房'},
                { id: 'SetItem', name: '项目'},
                { id: 'SetNuclide', name: '核素'},
                { id: 'SetTracer', name: '示踪剂'},
                { id: 'SetItemPosition', name: '项目部位'},
                { id: 'SetTestMode', name: '检查方式'},
                { id: 'SetAccessNumRule', name: '核医学号规则'}
            ],
            activeTab: 'SetHospital'
        }
    },
    methods: {
    }
};
</script>
<style lang="scss" scoped>
.scope-projectSet{
    display: flex;
    .c-left{
        padding: 0px;
        margin-right: 5px;
        width: 180px;
        border-right: 1px solid #eee;
        overflow: hidden;
        padding-right: 15px;
        overflow: auto;
        li{
            list-style-type: none;
            line-height: 40px;
            span{
                cursor: pointer;
                color: #67696D;
                &.avtive{
                    color: #2384D3;
                }
                &:hover{
                    font-weight: bold;
                }
            }
        }
    }
    .c-right{
        flex: 1;
        overflow: hidden;
        .c-flex-context{
            height: 100%;
            display: flex;
            flex-direction: column;
            :deep(.c-form) {
                display: flex;
                flex-direction: column;
                .c-form-button {
                    text-align: right;
                    padding: 10px 0;
                    margin-left: 10px;
                    border-bottom: 1px solid #eee;
                }
                .el-form-item--mini.el-form-item {
                    margin-bottom: 0;
                }
                .el-input-number.el-input-number--mini {
                    width: 100%;
                }
                .c-form-button{
                    text-align: right;
                    padding: 10px 0;
                }
            }
            :deep(.c-flex-auto) {
                flex: 1;
                display: flex;
                flex-direction: column;
                margin-left: 10px;
                .c-search{
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    padding: 10px 0 0;
                    margin-left: -10px;
                }
                .c-content{
                    flex: 1;
                    height: 0;
                }
            }
            :deep(.el-table .el-table__row){
                .i-sort{
                    cursor: pointer;
                    font-size: 16px;
                    padding-left: 10px;
                }
            }
        }
    }
    :deep(.m-labelInput) {
        width: calc(100% - 10px);
        .el-select {
            width: 100%;
        }
    }
}
</style>
