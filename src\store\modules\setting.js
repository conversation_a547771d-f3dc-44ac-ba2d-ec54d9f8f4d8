/**
 * @description 主题全局配置状态
 * <AUTHOR>

const state = {
  isShowSettingDialog: false, // 是否打开主题设置
  isFullScreen: false, // 是否显示全屏
  isChangePwd: false, //是否显示改变密码
  isLockScreen: false, // 是否锁屏 
  theme: 'theme2',
  lang: 'zh-cn',
  // 是否显示全屏
  fullScreen: true,
};

const getters = {
  isFullScreen: (state) => state.isFullScreen,
  theme: (state) => state.theme,
  isShowSettingDialog: (state) => state.isShowSettingDialog,
  fullScreen: (state) => state.fullScreen,
  lang: (state) => state.lang,
  isChangePwd: (state) => state.isChangePwd,
  isLockScreen: (state) => state.isLockScreen
};

const mutations = {
   
  CHANGE_FULL_SCREEN: (state, flag) => {
    state.isFullScreen = flag;
  }, 
  SET_THEME: (state, theme) => {
    state.theme = theme;
  },
  CHANGE_SETTING_DRAWER: (state, flag) => {
    state.isShowSettingDialog = flag;
  },
  CHANGE_PWD: (state, flag) => {
    state.isChangePwd = flag;
  },
  CHANGE_LOCKSCREEN: (state, flag) => {
    state.isLockScreen = flag;
  }, 
  CHANGE_LANGUAGE: (state, lang) => {
    state.lang = lang;
  },
};

const actions = {
   
  /**
   * @description 切换是否全屏
   *  @param {boolean} flag true|false
   */
  changeFullScreen: ({ commit }, flag) => {
    commit('CHANGE_FULL_SCREEN', flag);
  },  
  /**
   * @description 设置主题
   * @param {strinng} theme 系统默认：blue|green|red|default
   */
  setTheme: ({ commit }, theme) => {
    commit('SET_THEME', theme);
  },

  /**
   * @description 是否打开主题设置
   * @param {boolean} flag true|false
   */
  setSettingDrawer: ({ commit }, flag) => {
    commit('CHANGE_SETTING_DRAWER', flag);
  },
  /**
   * 打开关闭修改密码
   */
  setChangePwd: ({ commit }, flag) => {
    commit('CHANGE_PWD', flag);
  },
  setLockScreen: ({ commit }, flag) => {
    commit('CHANGE_LOCKSCREEN', flag);
  }, 
 
  /**
   * @description 切换语言
   * @param {string} lang 语言 可选值： zh-cn|en
   */
  changeLanguage: ({ commit }, lang) => {
    commit('CHANGE_LANGUAGE', lang);
  },

  /**
   * @description 设置主题配置信息
   * @param {object} options 配置项
   */
  setSettingOptions: ({ commit }, options) => {
    commit('SET_SETTING_OPTIONS', options);
  },
};

export default {
	namespaced: true,
  getters,
  state,
  mutations,
  actions,
};
