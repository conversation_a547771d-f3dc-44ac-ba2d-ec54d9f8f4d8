const state = {
    appointmentDate: null,
    sNuclearNum: null,
    dayProjectTime: [],
    currentItemTreeData: {},
    currentItemTreeNode: {},
    planTimeItem: {},
    appointmentState: '',
    elementConfigData: {},
    iIsReserve: 0
}

const getters = {}

const mutations = {
    setAppointmentDate(state, payload) {
        state.appointmentDate = payload.appointmentDate
    },
    setNuclearNum(state, payload) {
        state.sNuclearNum = payload.sNuclearNum
    },
    setDayProjectTime(state, payload) {
        state.dayProjectTime = payload.dayProjectTime
    },
    setCurrentItemTreeNode(state, payload) {
        state.currentItemTreeData = payload.currentItemTreeData
        state.currentItemTreeNode = payload.currentItemTreeNode
    },
    setPlanTimeItem(state, payload) {
        state.planTimeItem = payload.planTimeItem
    },
    setAppointmentState(state, payload) {
        state.appointmentState = payload.appointmentState
    },
    setElementConfigData(state, payload) {
        state.elementConfigData = payload.elementConfigData
    },
    setIsReserve(state, payload) {
        state.iIsReserve = payload.iIsReserve
    }
}

const actions = {}

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
    modules: {}
}