import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'

export default {
    getTreeData() {
        return request({
            url: baseURL.apricot + '/stats/item/findAllStatsItem',
            method: 'post',
        });
    },
    findAllStatsFields() {
        return request({
            url: baseURL.apricot + '/stats/item/findAllStatsFields',
            method: 'post',
        });
    },
    findStatsItemById(params) {
        return request({
            url: baseURL.apricot + '/stats/item/findStatsItemById',
            method: 'post',
            params
        });
    },
    addCategory(params) {
        return request({
            url: baseURL.apricot + '/stats/category/addCategory',
            method: 'post',
            params
        });
    },
    editCategory(params) {
        return request({
            url: baseURL.apricot + '/stats/category/editCategory',
            method: 'post',
            params
        });
    },
    delCategory(params) {
        return request({
            url: baseURL.apricot + '/stats/category/delCategory',
            method: 'post',
            params
        });
    },

    addStatsItem(data) {
        return request({
            url: baseURL.apricot + '/stats/item/addStatsItem',
            method: 'post',
            data
        });
    },
    editStatsItem(data) {
        return request({
            url: baseURL.apricot + '/stats/item/editStatsItem',
            method: 'post',
            data
        });
    },
    delStatsItem(params) {
        return request({
            url: baseURL.apricot + '/stats/item/delStatsItem',
            method: 'post',
            params
        });
    },
    // 条件值查询
    queryConditionValue() {
        return request({
            url: baseURL.apricot + '/stats/query/queryConditionValue',
            method: 'post',
        });
    },
    // 统计查询
    queryStats(data) {
        return request({
            url: baseURL.apricot + '/stats/query/queryStats',
            method: 'post',
            data
        });
    },
    
}