<template>
     <template v-for="item in dataList" >
          <el-table-column 
          :key="item.prop"
          :prop="item.prop" 
          :sortable="item.sortable || defaultItem.sortable"
          :width="item.width"
          :min-width="item.minWidth || defaultItem.minWidth"
          :show-overflow-tooltip="item.tooltip != undefined ? item.showOverflowTooltip : defaultItem.showOverflowTooltip" 
          :label="isLang ? (item.label) : item.label"
          :align="item.align || defaultItem.align"
          :fixed="item.fixed"
          :formatter="item.formatter || defaultItem.formatter"
          v-if="item.show != undefined ? item.show : defaultItem.show">
               <template #header v-if="item.setting || item.prop == 'action'">
                    <span style="padding-right: 20px;">{{ item.label == 'action' ? '操作' : isLang ? (item.label) : item.label }}</span>
                    <XxTableConfigs 
                    v-model="dataList" 
                    :defaultItem="defaultItem" 
                    :isLang="isLang" 
                    :tableConfigField="item.prop" 
                    :storageKey="storageKey"></XxTableConfigs>
               </template>
               <template #default="scope" v-if="!item.formatter && !defaultItem.formatter">
                    <slot :name="item.prop" :row="scope.row" :$index="scope.$index">{{ scope.row[item.prop] }}</slot>
               </template>
          </el-table-column>
     </template>
</template>
<script>
export default {
     name: 'xxTableColumn',
     props: {
          modelValue: {
               type: Array,
               default: () => {
                    return []
               }
          },
          // 覆盖默认配置
          coverDefaultItem: { 
               type: Object,
               default: () => {
                    return {}
               }
          },
          isLang: { 
               type: Boolean,
               default: true
          },
          storageKey: String
     },
     computed: {
          dataList: {
               get: function() {
                    return this.modelValue;
               },
               set: function(val) {
                    this.$emit('update:modelValue', val);
               }
          }
     },
     data() {
          return {
               // 默认配置
               defaultItem: { 
                    show: true,
                    sortable: false,
                    width: 100,
                    showOverflowTooltip: true,
                    align: 'center',
                    minWidth: 100,
                    formatter: null,
                    setting: false // 格式化内容 function(row, column, cellValue, index)
               }
          }
     },
     mounted() {
         this.defaultItem = Object.assign({}, this.defaultItem, this.coverDefaultItem)
     }
}
</script>
