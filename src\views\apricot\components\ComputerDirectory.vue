<template>
    <slot :openDialog="openDialog"></slot>
    <el-dialog append-to-body :modelValue="d_showDir_v" class="t-default my-dialog" width="900px"
        :close-on-click-modal="false" title="目录" @close="d_showDir_v = false">
        <div v-loading="treeLoading" style="height: 60vh;overflow: auto;padding: 15px 30px;">
            <el-tree :data="treeData" :props="defaultProps" ref="dTree" node-key="absolutePath" highlight-current lazy
                :load="loadNode" @node-expand="handleNodeExpand" @node-click="handleNodeClick">
            </el-tree>
        </div>
        <template #footer>
            <span style="margin-right: 30px;">当前选中路径：{{ selectedItem.absolutePath }}</span>
            <el-button-icon-fa type="primary" icon="el-icon-check" @click="onCheckedClick">选好了</el-button-icon-fa>
            <el-button-icon-fa _icon="fa fa-close-1" @click="d_showDir_v = false">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import { cdburnGetSubFile } from '$supersetApi/projects/apricot/case/report.js'
export default {
    emits: ['changeDirectory'],
    data () {
        return {
            d_showDir_v: false,
            treeLoading: false,
            treeData: [],
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            selectedItem: {},
            nodeLoading: false
        }
    },
    methods: {
        openDialog() {
            this.selectedItem = {};
            this.d_showDir_v = true;
            !this.treeData.length && this.GetSubFile();
        },
        // 获取根目录
        GetSubFile () {
            let params = {
                absolutePath: '/'
            }
            this.treeLoading = true;
            cdburnGetSubFile(params).then(res => {
                this.treeLoading = false;
                if (!res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.treeData = res.data || [];
            }).catch(err => {
                this.treeLoading = false;
                console.log(err);
            })
        },
        // 选中目录
        onCheckedClick () {
            this.$emit('changeDirectory', this.selectedItem.absolutePath)

            this.d_showDir_v = false;
        },
        // 展开节点
        handleNodeExpand (data, node) {
            if (!this.nodeLoading) {
                node.loaded = false;
                node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
            }
            this.$refs.dTree.setCurrentKey(node.data.absolutePath);
            this.selectedItem = data;
        },
        // 点击节点
        handleNodeClick (data, node) {
            this.selectedItem = data;
            if (node.expanded) {
                node.loaded = false;
                !data.isClick && node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
            }
        },
        // 加载节点
        loadNode (node, resolve) {
            if (node.level === 0) {
                return
            }
            let params = {
                absolutePath: node.data.absolutePath
            }
            this.nodeLoading = true;
            cdburnGetSubFile(params).then(res => {
                this.nodeLoading = false;
                if (!res.success) {
                    this.$message.error(res.msg);
                    resolve([])
                    return
                }
                node.data.isClick = true;
                let arr = res.data || [];
                resolve(arr)
            }).catch(err => {
                console.log(err);
                this.nodeLoading = false;
                resolve([])
            })
        },
    }
}
</script>