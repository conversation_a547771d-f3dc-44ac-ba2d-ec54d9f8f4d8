<template>
  <span></span>
  <el-dialog append-to-body title="收藏标签管理" v-model="visible" :close-on-click-modal="false" width="800px" class="t-default my-dialog"
    @close="closeDialog">
    <div class="g-content m-flexLaout-ty">
      <!-- <div style="padding-bottom:16px;">
                <span>标签名：</span>
                <el-input 
                    v-model="form.sTagName"
                    @input="onTagNameInput"
                    style="width:300px;margin-right:15px;"></el-input>
                <el-button-icon-fa :loading="saveLoading"
                    type="primary"
                    icon="fa fa-save"
                    @click="onSaveClick">保存</el-button-icon-fa>
                <el-button-icon-fa 
                    icon="el-icon-delete"
                    @click="onResetClick">清空</el-button-icon-fa>
                <el-button-icon-fa :loading="loading"
                    
                    icon="el-icon-refresh"
                    @click="getCollectionTagData">刷新</el-button-icon-fa>
            </div> -->
      <div class="g-flexChild">
        <el-table v-loading="loading" :data="tableData" border highlight-current-row height="100%">
          <el-table-column min-width="200" label="标签名" show-overflow-tooltip>
            <template v-slot="scope">
              <div>
                <span v-if="!scope.row.isEdit">{{ scope.row.sTagName }}</span>
                <el-input size="small" v-if="scope.row.isEdit" v-model="scope.row.sTagName" @input="onTagNameInput(scope.row)" />
              </div>
            </template>
          </el-table-column>
          <el-table-column width="100" label="启用" align="center">
            <template v-slot="{ row, $index }">
              <el-switch v-model="row.iIsEnable" :active-value="1" :inactive-value="0"
                @click.stop.native="onEnableChange(row, $index)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column width="200" label="操作" align="center">
            <template v-slot:header="scope">
              <el-button-icon-fa icon="el-icon-plus" type="primary" link @click="handleAdd">新增</el-button-icon-fa>
            </template>
            <template v-slot="scope">
              <el-button v-if="!scope.row.isEdit" link title="编辑" type="primary" size="small"
                @click="handleEditTable(scope.row, scope.$index)">
                编辑
                <template #icon>
                  <Icon name="el-icon-edit" color="">
                  </Icon>
                </template>
              </el-button>
              <el-button-icon-fa v-if="scope.row.isEdit" :loading="saveLoading" link title="保存" _icon="fa fa-save"
                size="small" type="primary" @click="handleSaveTable(scope.row, scope.$index)">保存</el-button-icon-fa>

              <el-button-icon-fa v-if="scope.row.isEdit && scope.row.sId" link icon="el-icon-close" @click="onCancelClick(scope.row)">
                <span>取消</span>
              </el-button-icon-fa>
              <el-button-icon-fa v-else link icon="el-icon-delete" size="small" type="primary"
                @click="onDeleteClick(scope.row, scope.$index)">删除</el-button-icon-fa>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template #footer>
      <el-button-icon-fa plain _icon="fa fa-close-1" @click="$emit('update:dialogVisible', false)">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>
<script>
import { deepClone } from '$supersetUtils/function'

import { getCollectionTagData, addCollectionTag, editCollectionTag, delCollectionTag } from '$supersetApi/projects/apricot/common/collectTag.js'
export default {
  name: 'CollectTag',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
  },
  emits: ['update:dialogVisible'],
  data() {
    return {
      visible: false,
      tableData: [],
      form: {},
      loading: false,
      saveLoading: false,
      originItem: {},
    }
  },
  watch: {
    dialogVisible(val) {
      this.visible = this.dialogVisible;
      if (val) {
        this.getCollectionTagData()
      }
    },
  },
  methods: {
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    onTagNameInput(row) {
      row.sTagName = String(row.sTagName).replace(/,/g, '');
    },
    // 重置
    onResetClick() {
      this.form = {};
    },

    // 新增
    handleAdd() {
      let object = {
        isEdit: 1,
        iIsEnable: 1,
        sTagName: ''
      }
      object.iIsEnable = 1
      object.isEdit = 1
      this.tableData.push(object)
    },
    // 编辑某一行
    handleEditTable(row, index) {
      this.originItem = { ...row };
      this.tableData[index]['isEdit'] = 1;
    },
    onCancelClick(row) {
        Object.keys(this.originItem).map(key => {
            row[key] = this.originItem[key]
        })
        row.isEdit = 0;
        this.originItem = {};
    },
    // 保存某一行
    handleSaveTable(row, index) {
      if (!row.sTagName) {
        this.$message.warning('请填写标签名！');
        return
      }

      if (row.sId) {
        this.saveLoading = true;
        editCollectionTag(row).then(res => {
          this.saveLoading = false;
          if (res.success) {
            this.$message.success(res.msg);
            this.getCollectionTagData()
            return
          }
          this.$message.error(res.msg);
        }).catch(err => {
          console.log(err);
          this.saveLoading = false
        })
        return
      }
      row.iIsEnable = 1;
      this.saveLoading = true;
      addCollectionTag(row).then(res => {
        this.saveLoading = false;
        if (res.success) {
          this.$message.success(res.msg);
          this.getCollectionTagData()
          return
        }
        this.$message.error(res.msg);
      }).catch(err => {
        console.log(err);
        this.saveLoading = false
      })
    },
    // 启用禁用
    onEnableChange(row, index) {
      editCollectionTag(row).then(res => {
        if (res.success) {
          this.$message.success(res.msg); 
          this.getCollectionTagData()
          return
        }
        this.$message.error(res.msg);
      }).catch(err => {
        console.log(err);
      })
    },
    // 删除
    onDeleteClick(row, idx) {
      if(!row.sId) {
        this.tableData.splice(idx, 1);
        return
      }
      this.$confirm('确定删除该条数据，是否继续？', '提示', { type: 'warning' }).then(() => {
        let jsonData = {
          sId: row.sId
        }
        delCollectionTag(jsonData).then(res => {
          if (res.success) {
            this.$message.success(res.msg);
            this.form = {}
            this.tableData.splice(idx, 1);
            return
          }
          this.$message.error(res.msg);
        }).catch(err => {
          console.log(err);
        })
      }).catch(err => err);
    },
    // 获取表格数据
    getCollectionTagData() {
      this.loading = true;
      getCollectionTagData().then(res => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data || []
          return
        }
        this.$message.error(res.msg);
      }).catch(err => {
        this.loading = false;
        console.log(err);
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.g-content {
  height: 60vh;
}
</style>
