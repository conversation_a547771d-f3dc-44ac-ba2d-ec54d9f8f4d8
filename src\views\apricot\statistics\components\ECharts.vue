<template>
    <div class="c-template m-flexLaout-ty">
        <div class="c-radio">
            <el-radio-group v-model="chartType">
                <el-radio :label="1">直方图</el-radio>
                <el-radio :label="2">折线图</el-radio>
                <el-radio :label="3">饼图</el-radio>
            </el-radio-group>
        </div>
        <div class="g-flexChild m-flexLaout-ty">
            <div v-if="chartReady"
                class="i-echart">
                <div v-if="chartType == 1"
                    class="c-chart-content">
                    <!-- 直方图 -->
                    <!-- @ready="chartReady" -->
                    <ChartBar :value="eChartsData"
                        :text="pageBaseInfo.headline"
                        :rotate="0"
                        :grid="{ left: '110px',right: '100px',containLabel: false }"></ChartBar>
                </div>
                <div v-if="chartType == 2"
                    class="c-chart-content">
                    <!-- 曲线图 -->
                    <ChartLine :value="eChartsData"
                        :text="pageBaseInfo.headline"
                        :rotate="0"
                        :grid="{ left: '110px',right: '100px',containLabel: false }"></ChartLine>
                </div>
                <div v-if="chartType == 3"
                    class="c-chart-content">
                    <!-- 饼图 -->
                    <ChartPie :value="eChartsData"
                        :text="pageBaseInfo.headline"
                        :grid="{ left: '110px',right: '100px',containLabel: false }"></ChartPie>
                </div>
            </div>
            <p v-else
                class="i-tips"
                style="padding-top:50px;text-align:center">暂无数据</p>
        </div>
    </div>
</template>
<script>
import ChartLine from '../chart/Line.vue'
import ChartBar from '../chart/Bar.vue'
import ChartPie from '../chart/Pie.vue'
export default {
    name: 'ECharts',
    props: {
        title: String,
        componentType: String,
        eChartsData: {
            type: Array,
            default: () => []
        },
        pageBaseInfo: {
            type: Object,
            default: () => ({})
        }
    },
    components: {
        ChartLine,
        ChartBar,
        ChartPie
    },
    data () {
        return {
            chartType: null,
            chartReady: false,
            // eChartsData: []
        }
    },
    watch: {
        'componentType': {
            handler (val) {
                // console.log(this.chartType)
                if (val === "echarts") {
                    this.setChartType(1);
                }
            },
        },
    },
    methods: {
      setChartType(type) {
        this.chartReady = false
        this.$nextTick(() => {
          this.chartType = type
          this.chartReady = true
        })
      }
    },
    created () {
    },
    mounted() { 
    }
}
</script>
<style lang="scss" scoped>
.c-template {
    padding: 0 15px;
}
.c-radio {
    padding: 10px 0;
}
.i-echart {
    flex: 1;
    .c-chart-content {
        width: 100%;
        height: 100%;
        margin: 0 auto;
    }
}
</style>
