<template>
    <el-dialog append-to-body
        title="我的语音"
        width="60%"
        v-model="visible"
        @close="closeDialog"
        class="my-dialog t-default">
        <div class="g-content m-flexLaout-ty" style="height: 50vh;">
            <div class="g-flexChild">
                <el-table :data="tableData"
                    id="itemTable"
                    ref="mainTable"
                    border
                    height="100%">
                    <el-table-column min-width="300"
                        property="sNuclideName"
                        label="语音播报内容"
                        show-overflow-tooltip>
                        <template v-slot="{ row }">
                            <span v-if="row.sId && !row.isEdit">{{row.sCaptions}}</span>
                            <el-input v-else
                                v-model="row.sCaptions"
                                size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column width="170"
                        property="sTracerName"
                        label="操作"
                        align="center"
                        show-overflow-tooltip>
                        <template 
                            v-slot:header="{ row }">
                            <span style="display:inline-block;margin-right:15px;">操作</span>
                            <el-button type="primary" 
                                link
                                style="position: relative;top:-2px;"
                                @click="addClick">
                                <i class="el-icon-plus" style="margin-right:5px;"></i>新增
                            </el-button>
                                <!-- < link
                                    size="small"
                                    @click="autoSort">
                                    <i class="el-icon-rank"
                                        title="首次或无法排序时，点击初始化排序"></i> 排序
                                </el-button> -->
                        </template>
                        <template v-slot="{ row, $index }">
                            <el-button-icon-fa v-if="row.sId && !row.isEdit"
                                type="primary"
                                link
                                icon="el-icon-edit"
                                @click="editClick(row, $index)">编辑</el-button-icon-fa>
                            <el-button-icon-fa v-else
                                type="primary"
                                link
                                icon="el-icon-check"
                                @click="saveClick(row, $index)">保存</el-button-icon-fa>
                            <el-button-icon-fa v-if="row.sId && row.isEdit"
                                link
                                icon="el-icon-close"
                                @click="onCancelClick(row)">取消</el-button-icon-fa>
                            <el-button-icon-fa v-else
                                link
                                icon="el-icon-delete"
                                @click="deleteClick(row, $index)">删除</el-button-icon-fa>
                            <!-- <el-button link
                                size="small"
                                :disabled="!row.sId"
                                @click="sortClick(row, $index)"><i class="el-icon-rank i-sort"></i> 排序</el-button> -->
                        </template>
                    </el-table-column>
                    <el-table-column width="170"
                        property="sItemPositionName"
                        label="播放"
                        align="center"
                        show-overflow-tooltip>
                        <template v-slot="{ row, $index }">
                            <el-button-icon-fa
                                link
                                icon="el-icon-refresh"
                                :disabled="!row.sId"
                                @click="word2VoiceClick(row, $index)">转语音</el-button-icon-fa>
                            <el-button-icon-fa
                                link
                                icon="el-icon-video-play"
                                :disabled="!row.sId"
                                @click="playClick(row, $index)">播放</el-button-icon-fa>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa icon="fa fa-close-1" @click="$emit('update:dialogVisible', false)">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/common/callSet.js'
export default {
    name: 'MyVoice',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        }
    },
    emits: [ 'update:dialogVisible' ],
    data () {
        return {
            visible: false,
            tableData: [],
            originItem: {}
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
        workStation () {
            let temp = this.$store.getters['user/workStation'];
            return temp
        },
    },
    watch: {
        dialogVisible (val) {
            this.visible = this.dialogVisible;
            if (val) {
                this.getCallCaptionsData();
                this.$nextTick(() => {
                    this.rowDrop()
                });
            }
        },
    },
    methods: {
        closeDialog () {
            this.$emit('update:dialogVisible', false);
        },
        // 添加 
        addClick () {
            this.tableData.push({
                sStationId: this.userInfo.sId,
                iCaptionsType: 5,
                sTitel: '个人语音',
                iIsEnable: 1
            });
        },
        editClick (row, index) {
            this.originItem = { ...row };
            this.tableData[index]['isEdit'] = true;
        },
        onCancelClick(row) {
            Object.keys(this.originItem).map(key => {
                row[key] = this.originItem[key]
            })
            row.isEdit = 0;
            this.originItem = {};
        },
        saveClick (row, index) {
            // console.log(this.tableData[index])
            if (row.sId) {
                Api.editCallCaptions(row).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        // this.tableData[index]['isEdit'] = false;
                        this.tableData[index] = res.data;
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(() => { })
                return
            }
            Api.addCallCaptions(row).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    this.tableData[index] = res.data;
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => { })
        },
        deleteClick (row, index) {
            if (row.sId) {
                this.$confirm('确定删除该条数据，是否继续？', '提示', { type: 'warning' }).then(() => {
                    Api.editCallCaptions(row).then(res => {
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.tableData.splice(index, 1);
                            return
                        }
                        this.$message.error(res.msg);
                    }).catch(() => { })
                }).catch(err => err);
                return
            }
            this.tableData.splice(index, 1);
        },
        // 自动排序
        autoSort () {
            if (!this.tableData.length) {
                return;
            }
            Api.autoSortCallCaptions({}).then(res => {
                if (res.success) {
                    this.getCallCaptionsData();
                    this.$message.success(res.msg);
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => { })
        },
        //行拖拽
        rowDrop () {
            let dom = document.getElementById("itemTable");
            if (!dom) return;
            const tbody = dom.querySelector('.el-table__body-wrapper tbody');
            const _this = this;
            let updateFnc = ({ newIndex, oldIndex }) => {
                if (newIndex === oldIndex) return;
                Api.sortCallCaptions({
                    iIndexOld: _this.tableData[oldIndex].iIndex,
                    iIndexNew: _this.tableData[newIndex].iIndex,
                    sId: _this.tableData[oldIndex].sId,
                }).then(res => {
                    // _this.$nextTick(() => {
                    //     new Sortable(dom.querySelector('.el-table__body-wrapper tbody'), {
                    //         animation: 200,
                    //         onEnd: updateFnc
                    //     })
                    // })
                    if (res.success) {
                        return
                    }
                    this.$message.error(res.msg);
                    this.getCallCaptionsData();
                })
            }
            new Sortable.create(tbody, {
                handle: ".i-sort",
                animation: 200,
                onEnd: updateFnc
            })
        },
        word2VoiceClick (row, index) {
            let jsonData = {
                sId: row.sId,
                // rate: 10,
                // volume: 10,
            };
            Api.tovoiceCallCaptions(jsonData).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => { })
        },
        playClick (row, index) {
            // console.log(row);
            // let jsonData = {
            //     sId: row.sId,
            //     moduleId: this.userInfo.sId
            // };
            // var src = require('$supersetViews/apricot/components/f41123d3034b47889827c0968dbdcaa6.mp3');
            var src = `${window.configs.urls.apricot}/call/captions/play?sId=${row.sId}&moduleId=${this.userInfo.sId}`
            var mp3 = new Audio(src);
            mp3.play();
        },
        // 获取
        getCallCaptionsData () {
            let jsonData = {
                stationId: this.workStation.stationId,
                // iCaptionsType: 5,
            };
            Api.getCallCaptionsData(jsonData).then(res => {
                if (res.success) {
                    this.tableData = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },

    },
    mounted () {
    },
}
</script>
<style lang="scss" scoped>
// :deep .my-full-dialog.el-dialog.is-fullscreen {
//     width: 60%;
//     height: 70%;
//     margin-top: 8%;
//     .el-dialog__body {
//         height: calc(100% - 45px);
//     }
//     .my-dialog {
//         overflow: hidden;
//         .el-dialog__body {
//             overflow: auto;
//             padding: 10px 0px;
//         }
//     }
// }
</style>
