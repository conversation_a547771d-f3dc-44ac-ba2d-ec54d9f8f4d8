export default {
    appointmentInputList0: [{
        sProp: 'sDistrictId',
        sLabel: '院区',
        iRequired: 1,
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sMachineryRoomId',
        sLabel: '机房',
        iRequired: 1,
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sProjectId',
        sLabel: '项目',
        iRequired: 1,
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sConsultRoomId',
        sLabel: '问诊室',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sInjectionRoomId',
        sLabel: '注射室',
        iCustom: 1,
        iLayourValue: 4.8
    }],
    appointmentInputList1: [{
        sProp: 'sName',
        sLabel: '患者姓名',
        iRequired: 1,
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sNameSpell',
        sLabel: '姓名拼音',
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sSex',
        sLabel: '性别',
        sOptionProp: 'iSexList',
        sInputType: 'option',
        iLayourValue: 4.8
    }, {
        sProp: 'dBirthday',
        sLabel: '出生日期',
        sInputType: 'date-picker',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'iAge',
        sLabel: '年龄',
        iRequired: 1,
        sInputType: 'number',
        iLayourValue: 4.8,
        iCustom: 1,
    }, {
        sProp: 'sRegister',
        sLabel: '登记方式',
        sOptionProp: 'sRegisterOptions',
        sInputType: 'option',
        iLayourValue: 4.8
    }, {
        sProp: 'sIdNum',
        sLabel: '身份证号',
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'fHeight',
        sLabel: '身高(cm)',
        sInputType: 'number',
        iLayourValue: 4.8
    }, {
        sProp: 'fWeight',
        sLabel: '体重(kg)',
        sInputType: 'number',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sPhone',
        sLabel: '联系电话',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sEthnicGroupCode',
        sLabel: '民族',
        sInputType: 'option',
        iLayourValue: 4.8,
        sOptionProp: 'ethnicGroupCodeOption', 
        sPropName: 'sEthnicGroupName'
    }, {
        sProp: 'sAddress',
        sLabel: '患者地址',
        sInputType: 'text',
        iLayourValue: 9.6
    }],
    appointmentInputList2: [{
        sProp: 'sSource',
        sLabel: '就诊类型',
        sOptionProp: 'sSourceOptions',
        sInputType: 'option',
        iLayourValue: 4.8
    }, {
        sProp: 'sPatientIndex',
        sLabel: '患者索引',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sMedicalRecordNO',
        sLabel: '病历号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sMedicalCaseNO',
        sLabel: '病案号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sImageNo',
        sLabel: '影像号',
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sCardNum',
        sLabel: '社保卡号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sEncounter',
        sLabel: '就诊次数',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sInHospitalNO',
        sLabel: '住院号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sInHospitalSerial',
        sLabel: '住院流水号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sInpatientAreaText',
        sLabel: '病区',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sBedNum',
        sLabel: '床号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sVitisNo',
        sLabel: '就诊流水号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sVisitCard',
        sLabel: '就诊卡号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOutpatientNO',
        sLabel: '门诊号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOutpatientSerial',
        sLabel: '门诊流水号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sHealthCardNO',
        sLabel: '健康卡号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sPhysicalExamNo',
        sLabel: '体检号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sPhysicalExamSerial',
        sLabel: '体检流水号',
        sInputType: 'text',
        iLayourValue: 4.8
    }],
    appointmentInputList3: [{
        sProp: 'sNuclearNum',
        sLabel: '核医学号',
        sInputType: 'text',
        iRequired: 1,
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'sNuclideText',
        sLabel: '核素',
        iReadonly: 1,
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8,
    }, {
        sProp: 'sTracerText',
        sLabel: '示踪剂',
        iReadonly: 1,
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8,
    }, {
        sProp: 'sPositionText',
        sLabel: '检查部位',
        iReadonly: 1,
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8,
    }, {
        sProp: 'sTestModeText',
        sLabel: '检查方式',
        iReadonly: 1,
        sInputType: 'text',
        iCustom: 1,
        iLayourValue: 4.8,
    }, {
        sProp: 'fRecipeDose',
        sLabel: '处方剂量',
        sInputType: 'number',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'fBloodSugar',
        sLabel: '检查空腹血糖',
        sUnit: 'mmol/L',
        sInputType: 'number',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'fBloodSugarHis',
        sLabel: '开单空腹血糖',
        sUnit: 'mmol/L',
        sInputType: 'number',
        iCustom: 1,
        iLayourValue: 4.8
    }, {
        sProp: 'fFees',
        sLabel: '总费用(元)',
        sInputType: 'number',
        iLayourValue: 4.8
    }, {
        sProp: 'sFeeType',
        sLabel: '收费类型',
        sOptionProp: 'ApricotReportFeeType',
        sInputType: 'option',
        iLayourValue: 4.8
    }, {
        sProp: 'sChargeState',
        sLabel: '收费状态',
        sOptionProp: 'ChargeStateOptions',
        sInputType: 'option',
        iLayourValue: 4.8
    }, {
        sProp: 'sInvoiceNum',
        sLabel: '发票号',
        sInputType: 'text',
        iLayourValue: 4.8
    }],
    appointmentInputList4: [{
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sInputType: 'text',
            iLayourValue: 4.8
        }, {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sInputType: 'text',
            iLayourValue: 4.8
        }, {
            sProp: 'dApplyDate',
            sLabel: '开单日期',
            sInputType: 'date-picker',
            iLayourValue: 4.8
        }, {
            sProp: 'sApplyDistrictName',
            sLabel: '申请院区',
            sInputType: 'text',
            iLayourValue: 4.8
        }, {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sInputType: 'text',
            iLayourValue: 4.8
        }, {
            sProp: 'sOrderItemName',
            sLabel: '申请项目',
            sInputType: 'text',
            iLayourValue: 4.8
        }, {
            sProp: 'sTargetSiteName',
            sLabel: '检查部位',
            sInputType: 'text',
            iLayourValue: 4.8
        }, {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sInputType: 'text',
            iLayourValue: 9.6
        }, {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            sInputType: 'text',
            iLayourValue: 9.6
        },
        {
            sProp: 'sMemo',
            sLabel: '申请备注',
            sInputType: 'text',
            iLayourValue: 9.6
        }
    ],
    appointmentInputList5: [{
        sProp: 'sEName',
        sLabel: '英文名',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sFullName',
        sLabel: '全名',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sMaritalStatusName',
        sLabel: '婚姻状况',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOccupationName',
        sLabel: '职业',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sNation',
        sLabel: '籍贯',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sHomeZip',
        sLabel: '邮箱',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sCustomerLevel',
        sLabel: '客户级别',
        sOptionProp: 'sCustomerLevelOptions',
        sInputType: 'option',
        iLayourValue: 4.8
    }],
    appointmentInputList6: [{
        sProp: 'sApplyHospitalText',
        sLabel: '申请医院',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOrderNO',
        sLabel: '医嘱号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOrderGroupNO',
        sLabel: '医嘱组号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOrderTypeName',
        sLabel: '医嘱类型',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sAppointDay',
        sLabel: '推送预约日期',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sAppointTime',
        sLabel: '预约时间段',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sMethodName',
        sLabel: '医嘱检查方法',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sChiefPhysicianName',
        sLabel: '主治医生',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sChiefPhysicianPhone',
        sLabel: '医生电话',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'fExamineFee',
        sLabel: '检查费用',
        sUnit: '元',
        iCustom: 1,
        sInputType: 'number',
        iLayourValue: 4.8
    }, {
        sProp: 'fMedicineFee',
        sLabel: '药物费用',
        sUnit: '元',
        iCustom: 1,
        sInputType: 'number',
        iLayourValue: 4.8
    }, {
        sProp: 'iIsBedsideExamine',
        sLabel: '床旁检查',
        sOptionProp: 'iIsBedsideExamineOptions',
        sInputType: 'option',
        iLayourValue: 4.8
    }, {
        sProp: 'sMedicalHistory',
        sLabel: '简要病史',
        sInputType: 'text',
        iLayourValue: 9.6
    }, {
        sProp: 'sPresentHistory',
        sLabel: '现病史',
        sInputType: 'text',
        iLayourValue: 9.6
    }, {
        sProp: 'sClinicalSymptoms',
        sLabel: '临床症状',
        sInputType: 'text',
        iLayourValue: 9.6
    }, {
        sProp: 'sPastHistory',
        sLabel: '既往病史',
        sInputType: 'text',
        iLayourValue: 9.6
    }],
    appointmentInputList9: [{
        sProp: 'sProvince',
        sLabel: '省',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sCity',
        sLabel: '市',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sCounty',
        sLabel: '县',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sTownship',
        sLabel: '乡镇',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sStreet',
        sLabel: '街道',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sHouseNumber',
        sLabel: '门牌号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sMaritalStatusCode',
        sLabel: '婚姻状况编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOccupationCode',
        sLabel: '职业编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'fOneHours',
        sLabel: '1小时血糖值',
        sUnit: 'mmol/L',
        iCustom: 1,
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'fTwoHours',
        sLabel: '2小时血糖值',
        sUnit: 'mmol/L',
        iCustom: 1,
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'fThreeHours',
        sLabel: '3小时血糖值',
        sUnit: 'mmol/L',
        iCustom: 1,
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sResponsibleName',
        sLabel: '责任医生',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sExecDeptName',
        sLabel: '执行科室',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sResponsibleId',
        sLabel: '责任医生编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sExecDeptIndex',
        sLabel: '执行科室索引',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sExecDeptCode',
        sLabel: '执行科室编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sApplyDeptIndex',
        sLabel: '申请科室索引',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sApplyDepartId',
        sLabel: '申请科室编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOrderTypeCode',
        sLabel: '医嘱类型编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sOrderItemCode',
        sLabel: '医嘱项目编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sTargetSiteCode',
        sLabel: '医嘱检查部位编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sMethodCode',
        sLabel: '医嘱检查方法编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sApplyHospital',
        sLabel: '申请医院编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sApplyDrIndex',
        sLabel: '申请医生索引',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sApplyPersonId',
        sLabel: '申请人编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sAttendingDrIndex',
        sLabel: '主治医生索引',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sChiefPhysicianId',
        sLabel: '主治医生编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sInpatientAreaId',
        sLabel: '病区编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sInpatientWardText',
        sLabel: '病房',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sInpatientWardId',
        sLabel: '病房编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sDiagnosisCode',
        sLabel: '诊断代码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sEntrustPersonName',
        sLabel: '委托人姓名',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sEntrustPersonId',
        sLabel: '委托人编号',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sFeeType',
        sLabel: '费用类型编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sChargeState',
        sLabel: '收费状态编码',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'iPriority',
        sLabel: '优先级',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sPatientNo',
        sLabel: '来源系统数据标识',
        sInputType: 'text',
        iLayourValue: 4.8
    }, {
        sProp: 'sSystemId',
        sLabel: '来源系统',
        sInputType: 'text',
        iLayourValue: 4.8
    }],

}