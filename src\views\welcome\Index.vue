<template>
  <div class="container">
    <div class="top-part">
      <div class="banner">
        <div class="i-head-left"></div>
        <div class="i-head-right"></div>
        <img class="bg" src="/img/welcome/hospital_bg1.png" />
        <img class="logo" src="/img/welcome/hospital_logo.png" />
      </div>
    </div>


    <ul class="c-content">
      <li v-for="(item, index) in projectList" :key="index" @click="moduleMenuClick(item.routerName, item.name)">
        <svg class="fa" aria-hidden="true">
          <use :xlink:href="'#' + item.icon"></use>
        </svg>
        <p>
          <label v-text="item.name"></label>
          <span>{{ item.subtitle }}</span>
        </p>
      </li>

    </ul>
    <div class="c-footer">
      <el-carousel :interval="5000" height="136px" arrow="never" indicator-position="none"
        style="width: 1020px; margin: 0 auto; bottom: 21px;">
        <el-carousel-item class="bottom-bannar-item" v-for="(item, index) in ivenaList" :key="index">
          <img class="i-pic-01" :src="(item.pic1)" />
          <img class="i-pic-02" :src="(item.pic2)" />
        </el-carousel-item>
      </el-carousel>
      <a href="http://www.ivena.cn" target="_blank" rel="noopener noreferrer" class="linktext">http://www.ivena.cn</a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'welcome',
  data() {
    return {
      topBannerImgPath: [],
      // projectList: [],
      ivenaList: [{
        pic1: 'ivena_pic1.png',
        pic2: 'ivena_pic2.png'
      }, {
        pic1: 'ivena_pic3.png',
        pic2: 'ivena_pic4.png'
      }, {
        pic1: 'ivena_pic5.png',
        pic2: 'ivena_pic6.png'
      }, {
        pic1: 'ivena_pic7.png',
        pic2: 'ivena_pic8.png'
      }, {
        pic1: 'ivena_pic9.png',
        pic2: 'ivena_pic10.png'
      }].map(item => {
        item.pic1 = window.location.origin + window.location.pathname + `/img/welcome/bottom/` + item.pic1
        item.pic2 = window.location.origin + window.location.pathname + `/img/welcome/bottom/` + item.pic2
        return item
      }),
      random: (new Date).getTime(),
      publicPath: '',
      // baseURL: baseURL.reporting
    }
  },
  computed: {
    menuList() {
      return this.$store.getters['module_router/menuList'] || []
    },
    projectList() {
      return this.menuList.filter((item) => {
        return (item.routerName != 'welcome_Index') && (item.routerName != 'documentaion_Guide')
      });
    },
    userInfo() {
      return this.$store.state.user.userSystemInfo
    }
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    moduleMenuClick(routerName, name) {
      if (!routerName) {
        this.$message.info('"' + name + '"' + '模块目前正在开发中，尚未开放！');
        return;
      }
      else {
        this.$store.commit({
          type: 'module_router/switchModuleRouter',
          activeRouterName: routerName
        })
        /*获取按钮携带的参数，并打开对应的路由，打开对应标签，为标签加上打开路由的事件*/
      }

    },
  },
  created() {
  }
}
</script>
<style scoped lang="scss">
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  background-color: white;
}

.top-part {
  position: relative;
  flex: 0;
  display: flex;
  flex-direction: column;
  height: 50%;
  min-width: 1020px;
  margin: 0 auto;
  align-items: center;
  justify-content: center;

  .banner {
    width: 1020px;
    position: relative;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    z-index: 2;

    .bg {
      width: 100%;
    }

    .logo {
      width: 96px;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      z-index: 2;
    }
  }

  .i-head-left {
    width: 100px;
    background: linear-gradient(to left, rgba(255, 255, 255, 0), white);
    position: absolute;
    left: 0px;
    height: 100%;
  }

  .i-head-right {
    width: 400px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), white);
    position: absolute;
    right: 0px;
    height: 100%;
  }





}


.c-content {
  flex: 1;
  padding: 24px 0px;
  width: 1020px;
  margin: 0 auto;

  >li {
    display: flex;
    align-items: center;
    width: calc(25% - 16px);
    height: 100px;
    box-sizing: border-box;
    padding: 24px 12px;
    margin: 0px 16px 16px 0px;
    list-style-type: none;
    border: 1px solid #eee;
    cursor: pointer;
    transition: all 0.1s;
    float: left;

    &:hover {
      border-color: #2084D2;
      box-shadow: 1px 1px 3px 0px #eee;
    }

    &:nth-child(4n + 0) {
      width: 25%;
      margin-right: 0px;
    }

    >svg {
      display: inline-block;
      width: 60px;
      padding: 2px;
      font-size: 60px;
      border: 1px solid #efefef;
      background: hsla(0, 0%, 100%, .5);
      border-radius: 30px;
    }

    >p {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding-left: 12px;
      margin: 0px;
      flex: 1;
      width: 0px;
      height: 100%;

      >label {
        cursor: pointer;
        display: block;
        font-size: 15px;
        color: #2084D2;
      }

      >span {
        font-size: 12px;
        color: #999;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.c-footer {
  position: relative;
  flex: 0;
  height: 115px;
  width: 100%;
  background-color: #F8F9FD;

  .bottom-bannar-item {
    .i-pic-01 {
      position: absolute;
      top: 0;
      left: 0;
      width: 27%;
      z-index: 2;
    }

    .i-pic-02 {
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
      width: 18%;
    }

    &:nth-of-type(2) {
      .i-pic-01 {
        width: 16%;
      }

      .i-pic-02 {
        width: 11%;
      }
    }

    &:nth-of-type(3) {
      .i-pic-01 {
        width: 33.5%;
      }

      .i-pic-02 {
        width: 11%;
      }
    }

    &:nth-of-type(4) {
      .i-pic-01 {
        width: 22%;
      }

      .i-pic-02 {
        width: 9%;
        top: 66%
      }
    }

    &:nth-of-type(5) {
      .i-pic-01 {
        width: 22%;
      }

      .i-pic-02 {
        width: 14%;
        right: 0;
      }
    }
  }

  >.i-text {
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    height: 115px;
    background-color: #F8F9FD;
    text-align: center;
  }

  .linktext {
    position: absolute;
    left: calc(50% - 60px);
    top: 42%;
    z-index: 2;
    color: #666;
  }
}
</style>
