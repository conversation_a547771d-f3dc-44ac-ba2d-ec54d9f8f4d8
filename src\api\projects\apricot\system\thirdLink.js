import request, { baseURL, stringify } from '$supersetUtils/request'
export default {
    // 第三方链接设置操作接口 : Third Link Controller
    getThirdLinkData,
    addThirdLink(data) {
        return request({
            url: baseURL.apricot + '/third/link/add',
            method: 'POST',
            data
        })
    },
    editThirdLink(data) {
        return request({
            url: baseURL.apricot + '/third/link/edit',
            method: 'POST',
            data
        })
    },
    delThirdLink(params) {
        return request({
            url: baseURL.apricot + '/third/link/del',
            method: 'POST',
            data: stringify(params)
        })
    },
    saveThirdLinkResources(data) {
        return request({
            url: baseURL.apricot + '/third/link/save/resources',
            method: 'POST',
            data
        })
    },

    // sortThirdLink(params) {
    //     return request({
    //         url: baseURL.apricot + '/third/link/set/sort',
    //         method: 'POST',
    //         params
    //     })
    // },
    // autoSortThirdLink(params) {
    //     return request({
    //         url: baseURL.apricot + '/third/link/set/autoSort',
    //         method: 'POST',
    //         params
    //     })
    // },
}

export function getThirdLinkData(data) {
    return request({
        url: baseURL.apricot + '/third/link/find/page',
        method: 'POST',
        data
    })
}