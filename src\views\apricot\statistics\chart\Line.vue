<template>
    <div ref="dom"
        class="charts chart-line">
    </div>
</template>

<script>
import * as echarts from "echarts"
import tdTheme from '../config/theme.json' // 图表主题
import { on, off } from '@/utils' // 引入方法
import { shallowRef } from 'vue';
echarts.registerTheme('tdTheme', tdTheme)
export default {
    name: 'ChartLine',
    props: {
        value: {
            type: Array,
            default () {
                return []
            }
        },
        visible: Boolean,
        text: String,
        xTitle: {
            type: String,
            default () {
                return 'left'
            }
        },
        titlePadding: {
            type: Array,
            default () {
                return []
            }
        },
        grid: {
            type: Object,
            default () {
                return { left: '50px', right: '30px', containLabel: false }
            }
        },
        subtext: String,
        rotate: {
            type: Number,
            default () {
                return 45
            }
        },
        animation: {
            type: <PERSON>olean,
            default () {
                return true
            }
        },
        isDataZoom: {
            type: <PERSON><PERSON><PERSON>,
            default () {
                return true
            }
        },
    },
    data () {
        return {
            dom: null
        }
    },
    methods: {
        resize () {
            this.dom.resize()
        },
        updateChart () {
            this.$nextTick(() => {
                let sName = this.value.map(_ => _?.sName || '未知')
                let sValue = this.value.map(_ => _.iCount)
                let option = {
                    title: {
                        text: this.text,
                        subtext: this.subtext,
                    },
                    animation: this.animation,
                    xAxis: {
                        type: 'category',
                        boundaryGap: true,
                        data: sName,
                        axisLabel: { interval: 0, rotate: this.rotate }
                    },
                    yAxis: {
                        type: 'value'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: { // 坐标轴指示器，坐标轴触发有效
                            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                        }
                    },
                    grid: this.grid,
                    series: [{
                        data: sValue,
                        type: 'line',
                        symbolSize: 8,
                        itemStyle: {
                            borderWidth: 1,
                            borderColor: this.color,
                            color: this.color
                        },
                        markPoint: {
                            data: [
                                { type: 'max', name: '最大值' },
                                { type: 'min', name: '最小值' }
                            ]
                        },
                    }],
                    dataZoom: this.isDataZoom ? [{
                        show: true,
                        realtime: true,
                        start: 0,
                        end: 100
                    }, {
                        type: 'inside',
                        realtime: true,
                        // start: 0,
                        // end: 100
                    }] : []
                }
                this.dom.hideLoading()
                this.dom.setOption(option)
                on(window, 'resize', this.resize)
            })
        }
    },
    mounted () {
        this.dom = shallowRef(echarts.init(this.$refs.dom, 'tdTheme'))
        this.dom.showLoading()
    },
    watch: {
        value: {
            handler () {
                this.$nextTick(() => {
                    this.dom.clear()
                    this.dom.showLoading()
                    this.updateChart()
                })

            },
            immediate: true,
            deep: true
        }
    },
    beforeUnmount () {
        off(window, 'resize', this.resize)
    }
}
</script>
<style lang="scss" scoped>
.charts {
    height: 100%;
}
</style>
