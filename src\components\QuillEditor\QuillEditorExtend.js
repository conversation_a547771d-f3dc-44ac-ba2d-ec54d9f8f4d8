import './quill'
import configData from './config'

const Inline = Quill.import('blots/inline');
const BlockEmbed = Quill.import('blots/block/embed');
const Embed = Quill.import('blots/embed');

class InlineNew extends Inline {
  optimize(context) {
    // 解决带颜色文字的圈选后输入时的bug 注释掉它自带的逻辑
    // super.optimize(context); 
    // if (this.parent instanceof Inline &&
    //     Inline.compare(this.statics.blotName, this.parent.statics.blotName) > 0) {
    //   let parent = this.parent.isolate(this.offset(), this.length());
    //   this.moveChildren(parent);
    //   parent.wrap(this);
    // }
  }
}

Quill.register({
  'blots/inline': InlineNew,
});

// 测试
class MentionBlot extends Embed {
  constructor(scroll , node ) {
    super(scroll, node);
    // this.contentNode.removeAttribute('contenteditable', 'true');
    this.domNode.removeChild(this.leftGuard);
    this.domNode.removeChild(this.rightGuard);
  }
  static create(value) {
    const node = super.create();
    node.innerText = `${value.text}`;
    node.setAttribute('pid', value.pid);
    // node.setAttribute('contenteditable', false)
    node.addEventListener('click', (e) => {
      eventBus.publish('triggerBlue', node, e)
    })
    return node;
  }
  static value(nodeOBJ) {
    return { text: String(nodeOBJ.innerText), pid: nodeOBJ.getAttribute('pid') };
  }
  length() {
    return 1;
  }
}
MentionBlot.blotName = 'mention';
MentionBlot.tagName = 'span';
MentionBlot.className = 'ql-tag-blot-mention';

Quill.register(MentionBlot);

// 分隔符 用于合并型编辑器
class DividerBlot extends BlockEmbed {
  constructor(node) {
    super(node);
  }
  static create(value) {
    let node = super.create();
    node.setAttribute('mark', value.mark);
    node.setAttribute('class', value.class);
    node.setAttribute('contenteditable', false)
    node.setAttribute('draggable', false)
    return node;
  }
  static value(node) {
    return {
      mark: node.getAttribute('mark'),
      class: node.getAttribute('class')
    };
  }
  static allowedChildren() {
    return []
  }
  static defaultChild() {
    return ''
  }
  deleteAt() {
    return false
  }
  index() {
    return 1
  }
  length() {
    return 1;
  }
  restore() { // 覆盖原有逻辑
  }
  update() { // 覆盖原有逻辑
  }
}
DividerBlot.blotName = 'divider';
DividerBlot.tagName = 'hr';
Quill.register(DividerBlot);

/**
 * @description   格式刷功能拓展 
 * 格式刷功能： 1 点击时编辑器内要有已框选内容，否则无效 
 *  2 获取已框选内容的样式，并将图标设为激活状态，鼠标设为特定样式，此时再点击则取消
 *  3 设置一个监听器，当监听到框选某一段内容时，触发
 *  4 将这段内容的样式覆盖，然后重置激活状态和监听器
 * 
 */
const QuillBrushWatch = {
  attributesObj: {},
  listener: function () { },
  emit: function () {  // 点击 
    const quill = this.quill

    const editorContain = quill.container

    const range = quill.getSelection();
    const toolbar = quill.getModule('toolbar')
    const toolbarDom = toolbar.container
    const btn = toolbarDom.querySelector('.ql-formats .ql-brush')

    if (quill.brushActive) { // 激活状态，重置
      this.attributesObj = {}
      this.brushActive = false
      btn.classList.remove('active')
    } else { // 未激活 进入激活状态
      if (range) { // 有已选内容
        if (range.length == 0) {
          return
        } else {
          const attributesObj = quill.getFormat();
          this.attributesObj = attributesObj
          this.brushActive = true
          btn.classList.add('active')

          this.listener = () => {
            editorContain.removeEventListener('mouseup', this.listener)
            const attributesObj = this.attributesObj

            if (range.length == 0) {
              return
            } else {
              const range = quill.getSelection();
              const formatData = Object.assign({
                bold: false,
                italic: false,
                strike: false,
                underline: false,
                script: false,
                font: false,
                size: false,
                color: false,
                background: false
              }, attributesObj)

              quill.formatText(range.index, range.length, formatData, 'user');
              this.attributesObj = {}
              this.brushActive = false
              btn.classList.remove('active')
            }
          }
          editorContain.addEventListener('mouseup', this.listener)
        }
      } else {
        // 无已选内容
      }
    }
  }
}

class brushExtend {
  constructor(quill) {
    this.quill = quill
    // this.quill.id = this.quill.id ? this.id : this.quill.id
    this.quill.brushActive = false

    // QuillBrushWatch.on(this)
  }
}

// 列表内支持多行
const Break = Quill.import('blots/break');

class NewBreak extends Break {
  length() {
    return 1;
  }
  value() {
    return "　";
  }
  insertInto(parent, ref) {
    Embed.prototype.insertInto.call(this, parent, ref);
  }
}
NewBreak.blotName = 'break';
NewBreak.tagName = 'br';

Quill.register(NewBreak);

const ListItem = Quill.import('formats/list/item');
const List = Quill.import('formats/list');

ListItem.allowedChildren.push(NewBreak)
List.allowedChildren.push(NewBreak)
Quill.register(ListItem);
Quill.register(List);

class SpanHighlight extends Inline {

  static create() {
    let node = super.create();
    node.setAttribute('class', 'ql-font-spanHighlight');
    return node;
  }
}

SpanHighlight.blotName = 'spanHighlight';
SpanHighlight.tagName = 'span';
Quill.register(SpanHighlight);


const BlockPrototype = Quill.import("blots/block");

class CustomBlock extends BlockPrototype {
  constructor(domNode, value) {
    super(domNode, value);
    this.customSetFormat("fontFamily", "ql-font-SimSun");
  }

  static tagName = "P";

  customSetFormat(name, value) {
    if (name === "fontFamily") {
      const clssStr = (this.domNode.getAttribute('class') || '')
      if (clssStr.length === 0) {
        this.domNode.setAttribute('class', clssStr)
      } else if (clssStr.indexOf('ql-font-') === -1) {
        this.domNode.setAttribute('class', clssStr + ' ' + value);
      }
    } else {
      super.customSetFormat(name, value);
    }
  }
}

Quill.register(CustomBlock, true);

Quill.register('modules/brushExtend', brushExtend)


let lineheights = ['1.5', '1.0', '2.0', '2.5', '16px', '24px', '32px', '48px', '56px']
let Parchment = Quill.import('parchment');
let lineHtStyle = new Parchment.Attributor.Style('lineheight', 'line-height', {
  scope: Parchment.Scope.BLOCK,
  whitelist: lineheights
});

Parchment.register(lineHtStyle);
Quill.register(lineHtStyle, true);

let Size = Quill.import('attributors/style/size');
Size.whitelist = ['56px', '48px', '34px', '32px', '29px', '24px', '21px', '20px', '18px', '16px', '14px', '12px', '10px'];
Quill.register(Size, true);

let fonts = ['SimSun', 'SimHei', 'MicrosoftYaHei', 'KaiTi', 'FangSong', 'Arial', 'Times-New-Roman'];
let Font = Quill.import('formats/font');
Font.whitelist = fonts.concat(['spanHighlight']); //将字体加入到白名单 
Quill.register(Font, true);

// 撤销 重做 格式刷的图标svg
let icons = Quill.import('ui/icons');

Object.keys(configData.toolIconSrcMap).forEach(key => {
  icons[key] = configData.toolIconSrcMap[key]
})

let Delta = Quill.import('delta');


//  混入 编辑器配置
export const globalOptions = {  // 富文本配置数据
  theme: 'snow',
  boundary: document.body,
  readonly: false,
  modules: {
    brushExtend: {},
    toolbar: {
      container: configData.defaultToolList.map(item => configData.toolConfigMap[item.prop]),
      handlers: {
        redo: function () {
          const quill = this.quill
          if (!quill.isEnabled()) return
          const history = quill.history
          quill && history && history.redo();
        },
        undo: function () {
          const quill = this.quill
          if (!quill.isEnabled()) return
          const history = quill.history
          quill && history && history.undo();
        },
        brush: function () {
          QuillBrushWatch.emit.call(this)
        },
        markspace: function () {
          const quill = this.quill
          if (!quill.isEnabled()) return
          const toolbar = quill.getModule('toolbar')
          const toolbarDom = toolbar.container
          const btn = toolbarDom.querySelector('.ql-formats .ql-markspace')
          const clipboard = quill.clipboard
          const innerContent = quill.innerContent || ''
          let transfered

          quill.history.cutoff();

          if (quill.isMarkingSpace) { // 激活状态，重置
            // transfered = innerContent.split('')
            // transfered = transfered.map(word => word === '■' ? ' ' : word).join('') 
            transfered = innerContent
            transfered = transfered.replace(/■/gi, '　')
            quill.setText('', 'user')
            clipboard.dangerouslyPasteHTML(0, transfered, 'user');
            quill.isMarkingSpace = false
            btn.classList.remove('active')
          } else { // 未激活 进入激活状态
            // 将非标签内的空格he &nbsp; 转成 &#9632;实心方块字符
            transfered = replaceTagInner(innerContent, /&nbsp;| /gi, '&#9632;')
            transfered = transfered.replace(/　/gi, '&#9632;')
            quill.setText('', 'user')
            clipboard.dangerouslyPasteHTML(0, transfered, 'user');
            quill.isMarkingSpace = true
            btn.classList.add('active')
          }

          quill.history.cutoff();
        },
        removespace: function () {
          const quill = this.quill
          if (!quill.isEnabled()) return
          quill.history.cutoff();
          const clipboard = quill.clipboard
          const innerContent = quill.innerContent || ''

          // 将非标签内的空格he &nbsp;  &#9632;实心方块字符 全部清除
          const transfered = replaceTagInner(innerContent, /&nbsp;| |　|&#9632;|■/gi, '')

          quill.setText('', 'user')
          clipboard.dangerouslyPasteHTML(0, transfered, 'user');
          // this.quill.pasteHTML(transfered)
          quill.history.cutoff();
        },
        addspace: function () {
          const quill = this.quill
          if (!quill.isEnabled()) return
          quill.history.cutoff();

          const blockList = quill.getLines(0, 99999999)
          blockList.forEach(block => {
            // console.log(block.domNode.tagName)

            if (['HR', 'LI'].includes(block.domNode.tagName)) {
              return
            }
            const index = quill.getIndex(block)
            let text = block.domNode.outerText || ''
            if (!/^　/.test(text)) {
              this.quill.insertText(index, '　　', 'user');
            } else if (!/^　{2}/.test(text)) {
              this.quill.insertText(index, '　', 'user');
            }
          })

          quill.history.cutoff();
        },
        help: function () {
          this.quill.triggerHelp()
        },
        symbol: function () {
          this.quill.triggerSymbol()
        },
        commonSentence: function () {
          const control = this.controls.find(arr => arr[0] == "commonSentence")
          if (control && control[1]) {
            this.quill.triggerCommonSentence(control[1])
          } else {
            alert('暂不可用')
          }
        },
        blueTrigger: function () {
          const control = this.controls.find(arr => arr[0] == "blueTrigger")
          if (control && control[1]) {
            this.quill.triggerBlueContextmenu(control[1])
          } else {
            alert('暂不可用')
          }
        },
        winTrigger: function () {
          const control = this.controls.find(arr => arr[0] == "winTrigger")
          if (control && control[1]) {
            this.quill.triggerWinClick();
          } else {
            alert('暂不可用')
          }
        },
      }
    }
    ,
    history: {
      delay: 750, // 记录间隔时间 750ms
      maxStack: 300,
      userOnly: true
    },
    clipboard: { // 由于vue-quill-editor使用pasteHtml进行内容修改，所以用clipboard的matcher做解析器
      matchVisual: false,
      matchers: [ // 解析br符号
        ['BR', function lineBreakMatcher() {
          let newDelta = new Delta();
          newDelta.insert({ 'break': '' });
          return newDelta;
        }]
      ]
    },
    keyboard: {
      bindings: {
        'list empty enter': { // 名字不能变，确保覆盖原有逻辑
          key: 'Enter',
          collapsed: true,
          format: ['list'],
          empty: true,
          handler() {
            // 覆盖原有的删除行操作
            this.quill.history.cutoff();
          },
        },
        'empty collapsed enter': {
          key: 'Enter',
          collapsed: true, // 仅限非圈选的情况下
          offset: 0, // 光标位于首位，为了解决在空行按回车的问题
          handler(range) {
            this.quill.history.cutoff();
            this.quill.insertText(range.index, '　\n', 'user');
            // this.quill.insertText(range.index, '\n', 'user');
            this.quill.setSelection(range.index + 2, 'user');
            this.quill.history.cutoff();
          },
        },
        'notempty list enter': {  // 列表内按回车 非圈选
          key: 'Enter',
          format: ['list'],
          collapsed: true,
          empty: false,
          handler(range, context) {
            this.quill.history.cutoff();
            this.quill.insertText(range.index, '\n', 'user');
            this.quill.setSelection(range.index + 1, 'user');
            this.quill.history.cutoff();
          },
        },
        'shift+Enter linebreak': {
          // key: 13,
          key: 'Enter',
          format: ['list'],
          shiftKey: true,
          handler(range) {
            this.quill.history.cutoff();
            let currentLeaf = this.quill.getLeaf(range.index)[0]
            let nextLeaf = this.quill.getLeaf(range.index + 1)[0]

            this.quill.insertEmbed(range.index, 'break', true, 'user');

            // 当遇到不同的p容器或者到达全文末尾时 再插入一个换行
            if (nextLeaf === null || (currentLeaf.parent !== nextLeaf.parent)) {
              this.quill.insertEmbed(range.index, 'break', true, 'user');
            }

            this.quill.setSelection(range.index + 1, 'user');
            this.quill.history.cutoff();
          },
        },
        'outdent backspace': {  // 列表或缩进内在首位时按退格的逻辑 名字不能变，确保覆盖原有逻辑
          key: 8,
          collapsed: true,
          shiftKey: null,
          metaKey: null,
          ctrlKey: null,
          altKey: null,
          format: ['indent', 'list'],
          offset: 0,
          handler(range, context) {
            // console.log(range, context)
            // console.log(this.quill.getLine(range.index + 1))

            this.quill.history.cutoff();
            // console.log(range.index)
            // 需求：当删除序号后，对齐上下行文本，而不是把下一行接到上一行
            // = list内第一位时按退格  把这一行变成不是列表的一行 相当于从上一行加了一个换行
            // 需要判断当前的上一行是否有indent 即该行是否二级列表，是的话要变成对应的indent
            // TODO：  多级列表（且列表之后有内容）时不能按退格来减indent级数，要按shift+tab
            if (context.format.list != null) {  // 在list中
              const nextLine = this.quill.getLeaf(range.index + 2)[0]
              const prevLine = this.quill.getLine(range.index - 1)[0]
              // console.log(nextLine)

              if (range.index === 0 || !prevLine || prevLine.domNode && ['HR', 'P'].includes(prevLine.domNode.tagName)) { // 列表开头 或 文章开头时

                this.quill.format('list', false, Quill.sources.USER); // 删掉当前li

              } else
                if (!nextLine
                  // ↓ 会引起无法跳出li
                  // || nextLine.domNode && ['HR', 'BREAK'].includes(nextLine.domNode.tagName)
                  // || nextLine.parent && nextLine.parent.domNode.tagName !== 'li'
                ) { // 是否在列表末尾 
                  if (context.format.indent != null) { // 有indent
                    this.quill.format('indent', '-1', 'user');
                  } else {
                    this.quill.format('list', false, Quill.sources.USER); // 删掉当前li
                  }
                }
                else {
                  const prevLi = this.quill.getFormat(range.index - 1)
                  if (context.format.indent != null && context.format.indent != prevLi.indent) { // 在下一级列表的第一行时
                    this.quill.format('indent', prevLi.indent, 'user');
                  }
                  this.quill.deleteText(range.index - 1, 1, 'user');  // 只删掉li前的换行符 不会把列表分割成两块 
                  //  插入列表内换行 - 移动光标 - 完成 

                  const ifLineTail = this.quill.getText(range.index - 1, 1) === '\n' // 该行为空时额外增加一行
                  if (ifLineTail) this.quill.insertEmbed(range.index - 1, 'break', true, 'user');

                  this.quill.insertEmbed(range.index - 1, 'break', true, 'user');

                  this.quill.setSelection(range.index, 'user');
                }
            } else {
              this.quill.format('indent', '-1', 'user');
            }

            this.quill.history.cutoff();

          }
        },
        'outdent backspace collapsed': {
          key: 8,
          collapsed: false,
          shiftKey: null,
          metaKey: null,
          ctrlKey: null,
          altKey: null,
          format: ['indent', 'list'],
          handler(range, context) {
            this.quill.history.cutoff();
            this.quill.deleteText(range, Quill.sources.USER);
            const ifLineTail = this.quill.getText(range.index - 1, 1) === '\n'

            if (context.offset === 0 && ifLineTail) {
              this.quill.insertText(range.index, '　', Quill.sources.SILENT);
              this.quill.insertEmbed(range.index + 1, 'break', true, Quill.sources.SILENT);
            }
            this.quill.setSelection(range.index, Quill.sources.USER);
            this.quill.history.cutoff();

          }
        },
        indent: {
          key: 'Tab',
          format: ['blockquote', 'indent', 'list'],
          handler(range, context) {
            // 取消使用二级序号
            //   if (context.collapsed && context.offset !== 0) return true;
            //   this.quill.format('indent', '+1', Quill.sources.USER);
            return true;
          },
        },
      }
    },
  },
  placeholder: "编辑内容..."
}

// 替换非标签范围内的html 
function replaceTagInner(html, regex = '', replaceText = '') {
  let input = html
  const convert = (t) => {
    const after = t.replace(regex, replaceText)
    input = input.replace(t, after)
  }
  const start = html.match(/^()</)
  const end = html.match(/>()$/)
  const between = html.matchAll(/>(.*?)</g)
  if (start && start[0]) convert(start[0], start.index)
  if (end && end[0]) convert(end[0], end.index)
  Array.from(between, (result) => {
    if (result && result[0]) convert(result[0], result.index)
  })
  return input
}


class EventBus {
  constructor() {
    this.eventObject = {};
    this.callbackId = 0;
  }
  publish(eventName, ...args) {
    const callbackObject = this.eventObject[eventName];

    if (!callbackObject) return console.warn(eventName + " not found!");

    for (let callbackId in callbackObject) {
      callbackObject[callbackId](...args);
    }
  }
  subscribe(eventName, callback) {
    if (!this.eventObject[eventName]) {
      this.eventObject[eventName] = {};
    }

    const callbackId = this.callbackId++;

    this.eventObject[eventName][callbackId] = callback;

    const unSubscribe = () => {
      delete this.eventObject[eventName][callbackId];

      if (Object.keys(this.eventObject[eventName]).length === 0) {
        delete this.eventObject[eventName];
      }
    };

    return unSubscribe;
  }
}

export const eventBus = new EventBus();
export const eventBusClass = EventBus
