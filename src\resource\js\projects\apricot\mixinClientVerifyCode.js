//  混入 枚举表格内部打勾字段 时间段处理
import { openWebReadImgOrRebuild } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/case/report.js';
const mixinClientVerifyCode = {
    data() {
        return {
            clientParams: {
                needVerifyLicence: null,//阅图是否需要授权
                verificationStatus: null, // 授权状态
                clientId: null,
            }
        };
    },
    methods: {
        // 打开web重建
        async openWebReBuildViewer (row, iIsRebuild = 1) {
            let load = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            await this.getClientConfig()
            const needVerifyLicence = this.clientParams.needVerifyLicence
            // 若接口链接超时，默认值为null,
            if(needVerifyLicence == null) {
                load.close()
                return
            }
            if (needVerifyLicence) {
                await this.getClientId()
                const clientId = this.clientParams.clientId
                // 没有获取到id，默认值为null,
                if(!clientId) {
                    load.close()
                    return
                }
                await this.getVerificationCode()
                if(!this.clientParams.verificationStatus) {
                    load.close()
                    return
                }              
            }
            load.close()
            let info = {
                accessNo: row.accessionNumber,
                studyDate: row.studyDate,
                patientId: row.patientId
            }
            openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info, true);
        },
        // 查询客户端配置
        async getClientConfig() {
            await Api.queryClientConfig()
                .then((res) => {
                    if (res.success) {
                        this.clientParams.needVerifyLicence = res.data.needVerifyLicence;
                        return;
                    }
                    this.$message.error(res.msg);
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        // 查询本机主板序列号
        async getClientId() {
            await Api.readClientId()
                .then((res) => {
                    if (res.success) {
                        this.clientParams.clientId = res.data;
                        return;
                    }
                    this.$message.error(res.msg);
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        // 获取授权状态
        async getVerificationCode() {
            let params = {
                clientId: this.clientParams.clientId,
            };
            await Api.verificationCode(params)
                .then((res) => {
                    if (res.success) {
                        this.clientParams.verificationStatus = res.success;
                        return;
                    }
                    this.$message.error(res.msg);
                })
                .catch((err) => {
                    console.log(err);
                });
        },
    },
};
export default mixinClientVerifyCode;
