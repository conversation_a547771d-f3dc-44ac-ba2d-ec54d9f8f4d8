<template>
    <el-dialog :title="title"
        top="38vh"
        @close="closeDialog"
        title="修改密码"
        draggable
        width="360px"
        :close-on-click-modal="false"
        append-to-body
        :destroy-on-close="true"
        v-model="DialogVisible"
        class="my-dialog ">
        <div class="block p-4 pt-1">
            <p>用户名： {{ userName }}</p>
            <el-input v-model="userPass"
                name="password"
                show-password
                placeholder="请输入登录密码"
                class="input-with-select"
                type="password"
                @keyup.enter.native="submitUnlock">
                <template v-slot:append>
                    <div class="w-6 text-center cursor-pointer unlock-btn"
                        @click="submitUnlock">
                        <i class="fa fa-unlock-lump"></i>
                    </div>
                </template>
            </el-input>
            <div class="c-tips">
                <p v-if="lockFormTips">
                    <i class="fa fa-remind"></i>
                    {{ lockFormTips }}
                </p>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { removeAccessToken } from '@/utils/accessToken';

import { Close, Unlock } from '@element-plus/icons-vue'
export default {
    name: 'ReloginModal',
    props: {
        modelValue: Boolean
    },
    components: {
        Close, Unlock
    },
    data () {
        return {
            userPass: '',
            lockFormTips: '',
            title: '已锁定'
        }
    },
    computed: {
        userName () {
            // return this.$store.getters['user/username']
            return this.$store.getters["user/userSystemInfo"]?.userName
        },
        DialogVisible: {
            get () {
                return this.modelValue
            },
            set (val) {
                this.$emit('update:modelValue', val)
            }
        },
    },
    mounted () {
        window.__reloginLock = this.lock
    },
    beforeUnmount () {
    },
    methods: {
        closeDialog () {
            const fromPath = this.$router.options.history.state.current
            this.$router.push({ path: `/login?redirect=${fromPath}` }).catch(() => { })

            // window.location.href = window.location.origin + window.location.pathname;
        },
        // 打开锁定弹窗
        lock (title = '已锁定') {
            //锁定页面需要删除token
            removeAccessToken()

            // this.modelValue = true
            this.$emit('update:modelValue', true)
            this.title = title
        },
        // 提交密码
        submitUnlock () {
            //拦截未填写情况
            if (!this.userPass) {
                this.lockFormTips = '密码不能为空'
                return
            }

            // 提交登录请求
            const userNo = this.$store.getters["user/userSystemInfo"]?.userNo;
            const newParams = {
                "loginType": "PC",
                subject: userNo || this.userName,
                password: this.userPass
            }
            let loading = this.loginLoading();
            this.$store.dispatch('user/login', newParams).then((res) => {
                loading.close()
                this.$store.commit({
                    type: "user/setSignatureAppName",
                    signatureAppName: this.appName
                });
                //登录成功设置touken
                this.afterUnlock(res);

                let timeout = setTimeout(() => {
                    this.lockFormTips = '';
                    this.userPass = '';
                    clearTimeout(timeout)
                }, 3000)
            }).catch((res) => {
                this.lockFormTips = res && res.msg || '';
                loading.close();
            });
        },
        // 解锁
        afterUnlock (res) {
            // //关闭弹窗
            this.$emit('update:modelValue', false)
            // //清空提示
            // this.lockFormTips = ''
            // // this.$loading()  
            // this.$message({
            //   message: '解锁成功',
            //   duration: 1800,
            //   type: 'success'
            // });
            // this.$loading()

            // 还是需要刷新
            //   window.location.reload() 
        },
        // 加载中效果
        loginLoading () {
            /*登录loadding*/
            return this.$loading({
                lock: true,
                text: '解锁请求已提交，请等待......',
                background: 'rgba(0, 0, 0, 0.35)'
            });
        },
    }
}
</script>

<style lang="scss" scoped>
.my-close {
    padding: 10px;
    position: absolute;
    top: 5px;
    right: 5px;
}

.c-tips {
    margin-top: 5px;
    color: #f56c6c;

    p {
        margin: 0;
    }
}
.unlock-btn {
    color: var(--el-color-primary);
    &:hover {
        filter: brightness(1.5);
    }
}
</style>
