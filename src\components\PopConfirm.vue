<template>
    <el-tooltip ref="tooltipRef" trigger="click" effect="light" v-bind="$attrs"
        :popper-class="`el-popover`" :popper-style="style" :teleported="teleported"
        :fallback-placements="['bottom', 'top', 'right', 'left']" :hide-after="hideAfter" :persistent="persistent">
        <template #content>
            <div :class="'el-popconfirm'">
                <div :class="'el-popconfirm__main'">
                    <el-icon v-if="!hideIcon && icon" :class="('el-icon el-popconfirm__icon')" :style="{ color: iconColor }">
                        <component :is="icon" />
                    </el-icon>
                    {{ title }}
                </div>
                <template v-if="$slots.customContent">
                    <slot name="customContent" />
                </template>
                <div :class=" ('el-popconfirm__action')">
                    <el-button size="small" :type="cancelButtonType === 'text' ? '' : cancelButtonType"
                        :text="cancelButtonType === 'text'" @click="cancel">
                        {{ finalCancelButtonText }}
                    </el-button>
                    <el-button size="small" :type="confirmButtonType === 'text' ? '' : confirmButtonType"
                        :text="confirmButtonType === 'text'" @click="confirm">
                        {{ finalConfirmButtonText }}
                    </el-button>
                </div>
            </div>
        </template>

        <template v-if="$slots.reference">
            <slot name="reference" />
        </template>

    </el-tooltip>
</template>
<script>
  export default {
    name: 'PopConfirm', 
  }
</script>
<script setup>
import { computed, ref } from 'vue'
// import ElButton from '@element-plus/components/button'
// import ElIcon from '@element-plus/components/icon'
// import ElTooltip from '@element-plus/components/tooltip'
// import { useLocale, useNamespace } from '@element-plus/hooks'
// import { addUnit } from '@element-plus/utils'
// import { popconfirmProps } from './popconfirm'
const emit = defineEmits(['change', 'delete'])
const props = defineProps({
    title: String,
    confirmButtonText: String,
    cancelButtonText: String,
    confirmButtonType: {
        type: String,
        // values: buttonTypes,
        default: 'primary',
    },
    cancelButtonType: {
        type: String,
        // values: buttonTypes,
        default: 'text',
    },
    icon: {
        // type: iconPropType,
        // default: () => QuestionFilled,
    },
    iconColor: {
        type: String,
        default: '#f90',
    },
    hideIcon: {
        type: Boolean,
        default: false,
    },
    hideAfter: {
        type: Number,
        default: 200,
    },
    onConfirm: {
        type: Function,
    },
    onCancel: {
        type: Function,
    },
    teleported: {
        type: Boolean,
        default: true,
    },
    persistent: {
        type: Boolean,
        default: false,
    },
    width: {
        type: [String, Number],
        default: 150,
    },
})
// const ns = useNamespace('popconfirm')
const tooltipRef = ref({})
const hidePopper = () => {
    tooltipRef.value?.onClose?.()
}
const style = computed(() => {
    return {
        width: String(props.width),
    }
})
const confirm = (e) => {
    props.onConfirm?.(e)
    hidePopper()
}
const cancel = (e) => {
    props.onCancel?.(e)
    hidePopper()
}
const finalConfirmButtonText = computed(
    () => props.confirmButtonText || '确认'
)
const finalCancelButtonText = computed(
    () => props.cancelButtonText || '取消'
)
</script>
