import request , { baseURL, stringify } from '$supersetUtils/request'
// 问诊模块
export default {
    // 简要病史    
    getBriefHistoryInit(data){
        return request({
            url: baseURL.apricot + '/briefly/illness/find/init',
            method: 'POST',
            transformRequest: stringify,
            data
        })
    },

    addBriefHistory: addBriefHistory,

    delBriefHistory(data){
        return request({
            url: baseURL.apricot + '/briefly/illness/del',
            method: 'POST',
            transformRequest: stringify,
            data
        }) 
    },

    editBriefHistory: editBriefHistory,

    // 既往病史
    getPostIllness(data){
        return request({
            url: baseURL.apricot + '/past/medical/and/illness/find/key',
            method: 'POST',
            data
        })
    },

    addPostIllness(data){
        return request({
            url: baseURL.apricot + '/past/medical/and/illness/add',
            method: 'POST',
            data
        })
    },

    editPostIllness(data){
        return request({
            url: baseURL.apricot + '/past/medical/and/illness/edit',
            method: 'POST',
            data
        })
    },

    delPostIllness(data){
        return request({
            url: baseURL.apricot + '/past/medical/and/illness/del',
            method: 'POST',
            transformRequest: stringify,
            data
        })
    },


    // 手术信息
    getOperationsData(data){
        return request({
            url: baseURL.apricot + '/operations/find/page',
            method: 'POST',
            data
        })
    },

    addOperations(data){
        return request({
            url: baseURL.apricot + '/operations/add',
            method: 'POST',
            data
        })
    },

    editOperations(data){
        return request({
            url: baseURL.apricot + '/operations/edit',
            method: 'POST',
            data
        })
    },

    delOperations(data){
        return request({
            url: baseURL.apricot + '/operations/del',
            method: 'POST',
            transformRequest: stringify,
            data
        })
    },


    // 病理信息
    getPathologysData(data){
        return request({
            url: baseURL.apricot + '/pathologys/find/page',
            method: 'POST',
            data
        })
    },

    addPathologys(data){
        return request({
            url: baseURL.apricot + '/pathologys/add',
            method: 'POST',
            data
        })
    },

    editPathologys(data){
        return request({
            url: baseURL.apricot + '/pathologys/edit',
            method: 'POST',
            data
        })
    },

    delPathologys(data){
        return request({
            url: baseURL.apricot + '/pathologys/del',
            method: 'POST',
            transformRequest: stringify,
            data
        })
    },

    // 查询历史患者（不包括当前患者）
    getPatientQueryPastPatients,
    
    // 获取患者结果
    getPatientResultById
}

// 获取患者结果
export function  getPatientResultById(data){
    return request({
        url: baseURL.apricot + '/past/patient/info/past/patient/result',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}

// 查询历史患者总数（不包括当前患者）
export function getPatientQueryPastPatientsCount(data) {
    return request({
        url: baseURL.apricot + '/past/patient/info/queryPastPatientsCount',
        method: 'POST',
        data
    })
}
// 查询历史患者（不包括当前患者）
export function getPatientQueryPastPatients(data) {
    return request({
        url: baseURL.apricot + '/past/patient/info/queryPastPatients',
        method: 'POST',
        data
    })
}
// 
export function editBriefHistory(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/edit',
        method: 'POST',
        data
    })
}
//
export function addBriefHistory(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/add',
        method: 'POST',
        data
    })
}

export function brieflyIllnessFindKey(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/find/key',
        method: 'POST',
        data,
    })
}

// 自定义问诊模板表接口
// 获取模板列表
export function getCustomTemplate(data) {
    return request({
        url: baseURL.apricot + '/customTemplate/findList',
        method: 'POST',
        data
    })
}
// 获取单个模板
// export function getOneCustomTemplate(data) {
//     return request({
//         url: baseURL.apricot + '/customTemplate/getOneById',
//         method: 'POST',
//         data
//     })
// }
// 添加
export function addCustomTemplate(data) {
    return request({
        url: baseURL.apricot + '/customTemplate/addOne',
        method: 'POST',
        data
    })
}
// 编辑
export function editCustomTemplate(data) {
    return request({
        url: baseURL.apricot + '/customTemplate/editOne',
        method: 'POST',
        data
    })
}

// 删除
export function delCustomTemplate(data) {
    return request({
        url: baseURL.apricot + '/customTemplate/deleteOne',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}

// 启禁
export function disableCustomTemplate(data) {
    return request({
        url: baseURL.apricot + '/customTemplate/enable/disable',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}

// 设备类型与模板关联接口
export function getDevicesRelationTemplate(data) {
    return request({
        url: baseURL.apricot + '/deviceTypeTemplateRelation/findList',
        method: 'POST',
        data
    })
}
// 获取关联模板
export function getDeviceRelationTemplate(data) {
    return request({
        url: baseURL.apricot + '/deviceTypeTemplateRelation/getOneById',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}
// 设置关联
export function setDeviceRelationTemplate(data) {
    return request({
        url: baseURL.apricot + '/deviceTypeTemplateRelation/relation',
        method: 'POST',
        data
    })
}
// 添加模板关联接口
export function addDeviceRelationTemplate(data) {
    return request({
        url: baseURL.apricot + '/deviceTypeTemplateRelation/addOne',
        method: 'POST',
        data
    })
}
// 编辑关联接口
export function editDeviceRelationTemplate(data) {
    return request({
        url: baseURL.apricot + '/deviceTypeTemplateRelation/editOne',
        method: 'POST',
        data
    })
}
// 删除模板关联接口
export function delDeviceRelationTemplate(data) {
    return request({
        url: baseURL.apricot + '/deviceTypeTemplateRelation/deleteOne',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}

// 获取问诊
export function getConsultTemplateData(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/getOneByPatientAndDeviceTypeId',
        method: 'post',
        transformRequest: stringify,
        data 
    })
}


// 患者问诊模板添加
export function addPatientConsult(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/addOne',
        method: 'POST',
        data
    })
}
// 患者问诊模板编辑
export function editPatientConsult(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/editOne',
        method: 'POST',
        data
    })
}

// 患者问诊模板删除
export function delPatientConsult(data) {
    return request({
        url: baseURL.apricot + '/briefly/illness/deleteOne',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}

export function getCustomTemplateField(data) {
    return request({
        url: baseURL.apricot + '/customTemplate/commonly/field',
        method: 'POST',
        transformRequest: stringify,
        data
    })
}