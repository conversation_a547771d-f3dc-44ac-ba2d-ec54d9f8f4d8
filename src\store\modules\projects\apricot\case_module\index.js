import report from './report'
import consult from './consult'

const state = {
    businessMenus: [],
    patientInfo: {},
    currentModule: '',
    IsUpdatePatientInfo: false,
}

const getters = {

}

const mutations = {
    setBusinessMenus(state, payload) {
        state.businessMenus = payload.businessMenus
    },
    setPatientInfo(state, payload) {
        state.patientInfo = payload.patientInfo
    },
    setCurrentModule(state, payload) {
        state.currentModule = payload.name
    },
    setIsUpdatePatientInfo(state, payload) {
        state.IsUpdatePatientInfo = payload.IsUpdatePatientInfo
    },
}

const actions = {
    actions1() {

    }
}

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
    modules: {
        report,
        consult,
    }
}