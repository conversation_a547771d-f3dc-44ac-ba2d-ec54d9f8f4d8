<template>
    <!-- 项目设置 示踪剂 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary"  
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa> 
            </div>
            
            <div class="c-form-search">
                <div>
                    <el-input v-model="condition.sTracerName"
                        placeholder="示踪剂名称"
                        clearable
                        @keyup.enter.native="mxDoSearch">
                    </el-input>
                </div>
                <div>
                    <el-input 
                        placeholder="上下标名称"
                        v-model="condition.sTracerSupName"
                        clearable
                        @keyup.enter.native="mxDoSearch"></el-input>
                </div>
                <!-- <div>
                    <el-select v-model="condition.iIsEnable"
                        style="width: 100%;"
                        placeholder=""
                        clearable
                        @change="mxDoSearch">
                        <el-option v-for="item in optionsLoc.isEnable"
                            :key="item.sValue"
                            :label="item.sName"
                            :value="item.sValue"></el-option>
                    </el-select>
                </div> -->
                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch"></el-button-icon-fa>
                </div>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content" v-loading="loading">
                <el-table :data="tableData"
                    v-drag:[config]="tableData"
                    ref="mainTable"
                    size="small"
                    id="tracerTable"
                    @row-click="onClickRow"
                    v-if="reRender"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort" >
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button size="small"
                                    link
                                    type="primary"
                                    @click="handleEdit(scope.row)"
                                    >编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class @click="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class="i-sort">排序
                                    <template #icon>
                                        <Icon name="el-icon-rank" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                <span v-else> 否 </span>
                            </template>
                            <template v-else-if="item.sProp === 'sTracerSupName'">
                                <span v-html="scope.row[`${item.sProp}`]"></span>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                        <!-- <template v-slot:header="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex flex-col">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="120px"
                    >
                   <el-col :span="24">
                        <el-form-item prop="sTracerName" label="名称：">
                            <el-input placeholder="示踪剂名称"
                                v-model="editLayer.form.sTracerName"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sTracerSupName" label="上下标名称：">
                            <el-input
                                placeholder="示踪剂上下标名称"
                                v-model="editLayer.form.sTracerSupName"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sNuclideSupName" label="显示效果：">
                            <div class="i-show">
                                <span v-html="editLayer.form.sTracerSupName"></span>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sMedicineSource" label="默认药物来源：">
                            <el-select v-model="editLayer.form.sMedicineSource"
                                placeholder=" "
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.ApricotReportMedicineSource"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="启  用：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-form>

                 <div class="c-tip">
                    <p><span>上标请添加:</span>&lt;sup&gt;&lt;/sup&gt;标签,<span>如：</span>&lt;sup&gt;99m&lt;/sup&gt;Tc-SC<span style="padding-left: 20px;">;效果:</span><sup>99m</sup>Tc-SC</p>
                    <p><span>下标请添加:</span>&lt;sub&gt;&lt;/sub&gt;标签,<span>如：</span>NaCHO&lt;sub&gt;99m&lt;/sub&gt;<span>;效果:</span>NaCHO<sub>99m</sub></p>
                </div>
            </div>
            <template #footer>
                <div  class="dialog-footer">
                    <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保存</el-button-icon-fa>
                    <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SetTracer',
    mixins: [mixinTable, mixinTableDrag],
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            configTable: [
                {
                    sProp: 'sTracerName', sLabel: '示踪剂名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'sTracerSupName', sLabel: '示踪剂上下标名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'sMedicineSourceText', sLabel: '默认药物来源',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '120px',
                },
                {
                    sProp: 'action', sLabel: '操作',
                    sAlign: 'center', sWidth: '220px',
                }
            ],
            rules: {
                sTracerName: [{ required: true, message: '请输入名称' }],
            },
            tableData: [],
            reRender: true,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                }
            },
            condition: {
                // iIsEnable: '1'
            },
            page: { pageCurrent: 1, pageSize: 9999 },
            optionsLoc: {
                isEnable: [
                    { sValue: '', sName: '全部' },
                    { sValue: '1', sName: '启用' },
                    { sValue: '0', sName: '禁用' },
                ],
                ApricotReportMedicineSource: this.$store.getters['dict/map'].ApricotReportMedicineSource || [],
            },
            sortApi: Api.sortTracer
        }
    },
    methods: {
           // 新增
        handleAdd() {
            let params = {
               iIsEnable: 1,
            }
            this.editLayer.form = Object.assign({},params)
            this.dialogTitle = '新增';
            this.dialogVisible = true
            let timeout = setTimeout(()=>{
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({},row)
            this.$nextTick(()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
            
        },
        handleSave() {
            this.editLayer.loading = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)  
                  return
                }
                this.editLayer.loading = false
            })
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledTracer({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sTracerName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delTracer({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        // 获取下拉框组件选中值的标签名
        getName (arr, val) {
            let item = arr.find(item => item.sValue == val);
            return item ? item.sName : null;
        },
        /**
         * 保存数据
         */
        saveData (params) {
            params.sMedicineSourceText = this.getName(this.optionsLoc.ApricotReportMedicineSource, params.sMedicineSource);
            if (!params.sId) {
                Api.addTracer(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            } else {
                Api.editTracer(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            const jsonData = params.condition;
            Api.getTracerData(jsonData).then((res) => {
                if (res.success) {
                    this.tableData = res?.data || [];
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    mounted () {
        this.mxOpenDialog(1, 'no-title')
    },
};
</script>
<style lang="scss" scoped>

.c-form .c-form-search {
    >div{
        width: 240px;
        margin:0 5px;
    }
}
.c-tip {
    border: 1px solid #f1f1f1;
    background-color: #fafafa;
    border-radius: 3px;
    user-select: text;
    color: #999;
    p > span {
        font-weight: bold;
        padding: 0px 5px;
    }
}
.i-show {
    width: 100%;
    height: 100%;
    background-color: #f4f8fb;
    text-indent: 10px;
}

</style>
