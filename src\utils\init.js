// import { MessageBox } from 'element-ui';
import { ElMessageBox as MessageBox, ElMessage as Message } from 'element-plus';
import router from '$supersetRouter'
import store from '$supersetStore'
import authApi from '@/api/auth/auth'
import { removeAccessToken } from '@/utils/accessToken';
import { getDefaultModuleRouterName } from '$supersetResource/js/common.js'



//初始化前端数据、路由
export default async function () {
  let origin = window.location.origin
  //获取最近的用户登录名

  // let sId = sessionStorage[origin + '_' + 'sId']
  // if (!sId) {
  //   if (!sessionStorage[origin + '_' + 'username']) {
  //     //没有拿到sId，即窗口关闭过，则需要重新登录
  //     jumpLogin()
  //     return;
  //   }
  const UserInfo = await store.dispatch('user/getUserInfo')
  if (!UserInfo) {
    // 这里是登录过期

    if (window.__reloginLock) { // 弹出登录窗口的全局函数
      window.__reloginLock('请输入密码重新登录')
    } else {
      jumpLogin()
    }
    return
  } else {
    // 已登录时刷新token并启动定时器，刷新完成前不能请求token
    await store.dispatch({ type: 'user/initTokenTimer'} )
    setSessionStorageUerserInfo(UserInfo.data);
    const robust = UserInfo.data.robust ?? 1;
    setIsRobustUserPwd(robust);
  }

  store.dispatch({
    type: 'dict/getDictionary'
  })

  //设置用户系统信息 
  store.commit({
    type: 'user/setUserSystemInfo',
    userSystemInfo: UserInfo.data
  })

  //根据用户系统信息，提取sId，获取用户权限
  const rights = await getUserRight(UserInfo.data.sId)
  if (!rights) {
    jumpLogin()
    return
  }
  //存储用户权限
  store.commit({
    type: 'user/setUserRight',
    userRight: rights
  })

  store.commit({
    type: 'module_router/updateMenuListByAccess',
    menuRight: store.state.user.menuRight
  })


  //设置路由加载完成标识
  await store.commit({
    type: 'user/setIsInited',
    isInited: true
  })



  
  // 进入默认页面
  store.commit({
    type: 'module_router/switchModuleRouter',
    activeRouterName: getDefaultModuleRouterName()
  })

  return true
}

function jumpLogin() {
  removeAccessToken()
  const fromPath = router.options.history.state.current;
  if (fromPath === '/' || fromPath === '/login') return;

  Message.error('登录信息丢失，请重新登录')

  if (window.__reloginLock) { // 弹出登录窗口的全局函数
    window.__reloginLock('请输入密码重新登录')
  } else {
    router.push({ path: `/login?redirect=${fromPath}` }).catch(() => { })
  }
}

function getUserRight(sUserId) {
  return new Promise(function (resolve, reject) {
    //调用接口
    authApi.queryUserRightTree({
      userId: sUserId
    }).then(function (res) {
      if (!res.success) {

        if (res.data == 5001) {
          MessageBox.alert('抱歉！您的权限访问失败，请联系管理员进行处理，是否返回到登录页？', '提示', {
            confirmButtonText: '确定',
            callback: action => {
              // debugger
              // window.location.href = rootPath
              window.location.href = window.location.origin + window.location.pathname;

              // if (window.__reloginLock) { // 弹出登录窗口的全局函数
              // 	window.__reloginLock('请输入密码重新登录')
              // } else {
              // 	window.location.href = window.location.origin + window.location.pathname;
              // }
              return
            }
          });
        }
      }
      let userRight = []
      if (res.success && res.data && res.data.menus) {
        //获取用户权限信息失败，请联系管理员
        userRight = res.data.menus
      }
      resolve(userRight);
    }).catch(function (err) {
      reject(err)
    })

  });
}

function setSessionStorageUerserInfo(data) {
  let origin = window.location.origin
  sessionStorage[origin + '_' + 'username'] = data.userNo || data.userName;
  sessionStorage[origin + '_' + 'sName'] = data.sName;
}

/**
 * 设置用户信用户密码强弱
 * @param {Boolean} isRobustUserPwd
 */
function setIsRobustUserPwd (bool) {
    store.commit({
        type: 'user/setIsRobustUserPwd',
        isRobustUserPwd: bool
    })
}
