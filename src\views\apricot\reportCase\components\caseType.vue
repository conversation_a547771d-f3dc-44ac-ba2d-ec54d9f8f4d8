<template>
     <el-dialog :close-on-click-modal="false"
        append-to-body
        fullscreen
        title="病例类型管理"
        :modelValue="dialogVisible"
        destroy-on-close
        @open="openDialog"
        @close="closeDialog"
        class="my-dialog t-default my-full-dialog my-padding-dialog">
            <div class="c-flex-context">
                <div style="style: padding-bottom:10px">
                        <el-button size="small" type="primary" @click="addFirstLevelNodes">
                            新增一级节点
                        </el-button>
                        <el-button-icon-fa
                            type="primary"
                            icon="el-icon-refresh"
                            :loading="loading"
                            plain
                            size="small"
                            @click="getData">刷新</el-button-icon-fa>
                </div>
                <div class="c-flex-auto">
                    <el-scrollbar>
                        <div class="c-content"
                            v-loading="loading">
                            <el-tree ref="tree"
                                highlight-current
                                :data="treeData"
                                :props="defaultProps"
                                node-key="sId"
                                default-expand-all
                                :expand-on-click-node="false"
                                draggable
                                :allow-drop="allowDrop"
                                @node-drop="handleDrop"
                                @node-click="onNodeClick"
                                @node-collapse="nodeCollapse"
                                :default-expanded-keys="treeExpandIndex">
                                <template v-slot="{node, data }">
                                <div class="custom-tree-node">
                                    <span v-if="!data.isEdit" :title="data.sName">{{data.sName}}</span>
                                    <span v-if="data.isEdit" ><el-input style="width:320px" placeholder="请输入病例类型名称" v-model="data.sName" size="small"/></span>
                                    <span class="action">
                                        <el-button-icon-fa
                                            type="primary"
                                            link 
                                            size="small"
                                            icon="el-icon-plus"
                                            @click="handleAddNode(data)">添加</el-button-icon-fa>
                                        
                                        <el-button-icon-fa v-if="!data.isEdit"
                                            type="primary"
                                            icon="el-icon-edit"
                                            link 
                                            size="small"
                                            @click="handleEditNode(data)">编辑</el-button-icon-fa>
                                        
                                        <el-button-icon-fa v-if="data.isEdit"
                                            type="primary"
                                            icon="fa fa-save"
                                            link 
                                            size="small"
                                            @click="saveData(node,data)">保存</el-button-icon-fa>

                                        <el-button-icon-fa 
                                            icon="el-icon-delete"
                                            link 
                                            size="small"
                                            @click="handleDelNode(node,data)">删除</el-button-icon-fa>
                                    </span>
                                </div>
                                </template>
                            </el-tree>
                        </div>
                    </el-scrollbar>
                </div>
            </div>
        <template #footer>
            <el-button-icon-fa _icon="fa fa-close-1" @click="closeDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import { deepClone } from '$supersetUtils/function'

import Api from '$supersetApi/projects/apricot/system/caseType.js'
export default {
    name: 'CaseType',
    components: {},
    emits: [ 'onCloseDialog' ],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            loading: false,
            defaultProps: {
                children: 'childs',
                label: 'sName'
            },
            flattArray: [],
            rules: {
                sName: [{ required: true, message: '不能为空' }],
            },
            treeData: [],
            treeExpandIndex: [],
        }
    },
    watch: {
        dialogVisible(val) {
            if(val) {
               this.getData() 
            }
        }
    },
    methods: {
        openDialog() {

        },
        closeDialog() {
            this.$emit('onCloseDialog')
        },
        addFirstLevelNodes() {
            const newChild = {isEdit:true, sName: '', childs: [] };
            this.treeData.unshift(newChild)
        },
        handleAddNode(data) {
            const newChild = {isEdit:true, sName: '', childs: [] };
            if (!data.childs) {
                data['childs'] = [];
            }
            data.childs.push(newChild);
            this.treeData = [ ...this.treeData ];
        },
        handleEditNode(data){
            data['isEdit'] =  true
        },
        // iState==1 新增；iState==2 编辑
        mxDoSaveData (data) {
            // this.actionState = iState;
            // this.editLayer.loading = true;

            // this.$refs[formName].validate((valid) => {
            //     if (!valid) {
            //         this.$message({
            //             message: '填写正确信息',
            //             type: 'warning',
            //             duration: 3000
            //         });
            //         this.editLayer.loading = false;
            //         return false;
            //     }
            //     // 验证成功
            //     this.saveData && this.saveData(this.editLayer.form) // axios 方法，在调用页面
            // })
            let jsonData = {
                sName: data.sName,
                sPid: data.sId ? data.sId : '0000',
                sMemo: data.sMemo
            }
            
        },
        /**
         * 保存数据
         */
        saveData (node,data) {
            if (!data.sId) {
                if([null,undefined,''].includes(data.sName)) {
                    this.$message.error('请输入病例类型名称！')
                    return
                }
                this.loading = true
                let jsonData = {
                    sName: data.sName,
                    sPid: node.parent.data.sId ? node.parent.data.sId : '0000',
                    sMemo: data.sMemo
                }
                Api.caseTypeAdd(jsonData).then((res) => {
                    this.loading = false
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // this.editLayer.form = {};
                        // this.$nextTick(() => {
                        //     this.$refs.refEditLayer.resetFields()
                        // })
                        this.getData();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.loading = false;
                })
                return
            }
            // if (!Object.keys(params).length) {
            //     this.$message.warning('请选择节点！');
            //     this.editLayer.loading = false;
            //     return
            // }
            // let jsonData = deepClone(params);
            // delete jsonData.sParentName
            Api.caseTypeEdit(data).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    // this.editLayer.form = {}
                    // this.$nextTick(() => {
                    //     this.$refs.refEditLayer.resetFields()
                    // })
                    this.getData();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.loading = false;
            })
        },
        // 删除
        handleDelNode (node,data) {
            if([null,undefined,''].includes(data.sName) || !data.sId) {
                this.$refs.tree.remove(node)
                return
            }
            let row = data;
            let msg = row.childs? '该节点存在子节点，确认删除该节点吗' : '确认删除该节点吗'
            this.$confirm(`${msg}？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.caseTypeDel({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.editLayer.form = {};
                        this.$nextTick(() => {
                            this.$refs.refEditLayer.resetFields()
                        })
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        // 点击树
        onNodeClick (data) {
            if(data.sId) {
                this.selectedNode = data
            }
            //let parentNode = this.getParentNode(this.treeData, row);
            // this.editLayer.form = deepClone(row);
            // this.editLayer.form['sParentName'] = parentNode ? parentNode.sName : ''
        },
        // 获取父级节点
        getParentNode (data = [], node) {
            if (node.sId === '0000') {
                return null
            }
            for (const item of data) {
                if (item.sId == node.sPid) {
                    return item
                }
                if (item.childs) {
                    let res = this.getParentNode(item.childs, node);
                    if (res) return res
                }
            }
            return null
        },
        handleDrop (draggingNode, dropNode) {
            this.sortTree(draggingNode.data, dropNode.data.iIndex)
        },
        allowDrop (draggingNode, dropNode, type) {
            if (draggingNode.level === dropNode.level) {
                if (draggingNode.parent.id === dropNode.parent.id) {
                    // 向上拖拽 || 向下拖拽
                    return type === "prev" || type === "next";
                }
            } else {
                // 不同级进行处理
                return false;
            }
        },
        nodeExpand (data) {
            this.treeExpandIndex.push(data.sId); // 在节点展开是添加到默认展开数组
        },
        nodeCollapse (data) {
            this.treeExpandIndex.splice(this.treeExpandIndex.indexOf(data.sId), 1); // 收起时删除数组里对应选项
        },
        sortTree (obj, iIndex) {
            const params = {
                iIndexOld: obj.iIndex,
                iIndexNew: iIndex,
                sId: obj.sId,
                sPid: obj.sPid,
            }
            Api.caseTypeSort(params).then((res) => {
                if (res.success) {
                    // console.log(res)
                    this.$message({
                        message: '排序成功！',
                        type: 'success',
                        duration: 3000
                    });
                    return;
                }
                this.$message({
                    message: '排序失败',
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 获取表格数据
        getData (params) {
            this.treeData = []
            this.loading = true
            Api.caseTypeTree().then((res) => {
                this.loading = false;
                if (res.success) {
                    this.treeData = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        }
    },
    mounted () {
    },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px 20px 10px 20px ;
    box-sizing: border-box;
    :deep(.el-tree-node__content ){
        height: 34px;
        line-height: 34px;
    }
    .c-flex-auto {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-content {
            flex: 1;
            height: 100%;
            padding: 10px 5px;
            box-sizing: border-box;
        }
    }
    
}
.custom-tree-node {
    border-bottom: 1px solid rgb(223, 223, 223);
    box-sizing: border-box;
    flex: 1;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    height: 33px;
    span.action {
        padding-left: 30px;
        display: none;
    }
}
.custom-tree-node:hover {
    span.action {
        display: block;
    }
}
.line {
    margin: 0 10px;
    display: inline-block;
    border-right: 1px solid #ddd;
    height: 16px;
    vertical-align: middle;
}
</style>
