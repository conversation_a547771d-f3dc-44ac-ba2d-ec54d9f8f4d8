<template>
    <el-dialog :close-on-click-modal="false"
        fullscreen
        append-to-body
        title="打印预览"
        v-model="visible"
        :destroy-on-close="true"
        @close="$emit('update:show', false)"
        @open="onOpen"
        :show="show"
        class="my-dialog t-default my-full-dialog">
        <div class="c-look-content c-inner-content"
            ref="print">
            <div class="c-title-parent">
                <p class="i-header"><strong>{{ pageBaseInfo.headline }}</strong><span v-if="printCheckDate">（{{pageBaseInfo.searchTimeStr}}）</span></p>
                <span v-if="printCheckDate">打印时间：<span style="color:#777">（{{pageBaseInfo.printTimeStr}}）</span></span>
            </div>
            <!-- <p v-if="!table.length"
                class="i-tips"
                ref="imageTips">暂无数据</p> -->
            <div v-show="printCheckTable"
                class="print-table"
                style="width: 100%;margin: 0px auto;">
                <el-table :data="tableData"
                    border
                    stripe> 
                    <el-table-column v-if="tableProps.length"  
                        type="index" 
                        label="序号" 
                        width="60px">
                    </el-table-column>
                    <el-table-column v-for="item in tableProps"
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :min-width="item.sMinWidth">
                        <template v-slot="scope">
                            <span>{{scope.row[item.sProp]}}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div v-show="printCheckBar"
                class="c-chart i-bar">
                <ChartBar ref="childBar"
                    :value="eChartsData"
                    text="直方图"
                    :rotate="45"
                    :animation="false"
                    :isDataZoom="false"
                    :grid="{ left: '60px',right: '60px', bottom: '100px',containLabel: false }"></ChartBar>
            </div>
            <div v-show="printCheckLine"
                class="c-chart i-line">
                <ChartLine ref="childLine"
                    :value="eChartsData"
                    text="折线图"
                    :rotate="45"
                    :animation="false"
                    :isDataZoom="false"
                    :grid="{ left: '60px',right: '60px', bottom: '100px',containLabel: false }"></ChartLine>
            </div>
            <div v-show="printCheckPie"
                class="c-chart i-pie">
                <ChartPie ref="childPie"
                    :value="eChartsData"
                    text="饼图"
                    :animation="false"
                    :grid="{ left: '20px',right: '20px',containLabel: false }"></ChartPie>
            </div>
        </div>
        <template #footer>
            <div class="g-page-footer">
                <div style="display: inline-block;margin-right: 20px;">
                    <el-checkbox v-model="printCheckDate">统计时间</el-checkbox>
                    <el-checkbox v-model="printCheckTable">报表</el-checkbox>
                    <el-checkbox v-model="printCheckBar">直方图</el-checkbox>
                    <el-checkbox v-model="printCheckLine">折线图</el-checkbox>
                    <el-checkbox v-model="printCheckPie">饼图</el-checkbox>
                </div>
                <el-button-icon-fa _icon="fa fa-print"
                    type="primary"
                    @click="onClickPrint">打 印</el-button-icon-fa>
                <el-button-icon-fa icon="fa fa-close-1"
                    @click="visible = false">关 闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
</template>
<script>

import ChartLine from '../chart/Line.vue'
import ChartBar from '../chart/Bar.vue'
import ChartPie from '../chart/Pie.vue'
import Print from '$supersetResource/js/print'
// import Vue from 'vue'
// Vue.use(Print) // 注册
export default {
    name: 'Print',
    components: {
        ChartBar,
        ChartPie,
        ChartLine
    },
    props: {
        show: {
            type: Boolean,
            default: false
        },
        tableProps: {
            type: Array,
            default: () => []
        },
        tableData: {
            type: Array,
            default: () => []
        },
        pageBaseInfo: {
            type: Object,
            default: () => ({})
        },
        eChartsData: {
            type: Array,
            default: () => []
        },
    },
    watch: {
        show () {
            this.visible = this.show;
        },
        printCheckPie (later) {
            if (later) {
                this.$nextTick(() => {
                    if (this.$refs.childPie != undefined) {
                        this.$refs.childPie.resize()
                    }
                })
            }
        },
        printCheckBar (later) {
            if (later) {
                this.$nextTick(() => {
                    if (this.$refs.childBar != undefined) {
                        this.$refs.childBar.resize()
                    }
                })
            }
        },
        printCheckLine (later) {
            if (later) {
                this.$nextTick(() => {
                    if (this.$refs.childLine != undefined) {
                        this.$refs.childLine.resize()
                    }
                })
            }
        }
    },
    data () {
        return {
            visible: this.show,
            printCheckDate: true,
            printCheckTable: true,
            printCheckPie: false,
            printCheckBar: false,
            printCheckLine: false
        }
    },
    methods: {
        onClickPrint () {
            this.$nextTick(()=> {
                // window.print()
                Print(this.$refs.print);
                // this.$print(this.$refs.print)
            })
           
        },
        onOpen () {
            this.printCheckDate = true;
            this.printCheckTable = true;
            this.printCheckPie = false;
            this.printCheckBar = false;
            this.printCheckLine = false
        },
    }
};
</script>
<style lang="scss" scoped>
:global(.my-full-dialog .el-dialog__body) {
    height: calc(100% - 122px);
    overflow-y: auto;
}

.c-chart {
    height: 420px;
}

.i-tips {
    font-size: 16px;
    color: #999;
    text-align: center;
    margin-top: 70px;
}
.c-chart {
    margin-top: 20px;
}
.c-title-parent {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    // .i-header {
    //     > strong {
    //         font-size: 16px;
    //     }
    // }
}
</style>
<style media="print" type="text/css">
@media print {
    html,
    body {
        height: 100%;
        /* padding: 10px; */
    }
    /* .print-table{
        border-bottom: 1px solid #DCDCDC;
        border-right: 2px solid #DCDCDC;
    } */
    .c-inner-content {
        padding: 10px;
    }
    .el-table--border,
    .el-table {
        border-bottom: 2px solid #eee;
        border-right: 2px solid #eee;
    }
    td,
    th {
        border: 1px solid #eee;
    }
    .el-table__footer-wrapper td {
        border-bottom: 2px solid #eee;
    }

    /* 打印效果时 */
    .el-table__header {
        width: 100% !important;
        table-layout: auto;
    }
    .el-table__body {
        width: 100% !important;
        table-layout: auto !important;
    }
    .el-table {
        width: 100% !important;
        font-size: 14px !important;
    }
    .el-table th, .el-table td{
        word-wrap: normal !important;
        width: auto !important;
        word-break: break-all !important;
    }
}
@page {
    /* size:  auto; */
    height: 100%;
    /* margin: 10px; */
}
</style>
