export default {
	name: 'input-number',
	mounted(el, binding) {
		// 获取传入的参数，如果没有传入或参数为 true，则允许输入小数
		const allowDecimal = binding.value || false;
		const decimalPlace = Number(binding.arg || 2) + 1

		// 定义输入框只能输入数字的事件处理函数
		const onInput = (event) => {
			const input = event.target.value;
			// 使用正则表达式保留合法字符（数字和小数点）
			let cleanedValue = input.replace(allowDecimal ? /[^\d.]/g : /\D/g, '').replace(/(\..*)\./g, '$1');

			if (allowDecimal) {
				const decimalIndex = cleanedValue.indexOf('.');
				if (decimalIndex !== -1) {
					cleanedValue = cleanedValue.substring(0, decimalIndex + decimalPlace); // 保留两位小数
				}
			}

			event.target.value = cleanedValue;
		};

		// 监听输入事件，并调用事件处理函数
		el.addEventListener("input", onInput);

		// 保存事件处理函数的引用，以便在解绑时使用
		el._decimalOnlyDirectiveCleanup = () => {
			el.removeEventListener("input", onInput);
		};
	},
	unmounted(el) {
		// 移除事件监听
		el._decimalOnlyDirectiveCleanup && el._decimalOnlyDirectiveCleanup();
	},
}
