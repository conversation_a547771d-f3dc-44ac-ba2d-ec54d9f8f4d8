<template>
    <el-dialog append-to-body
        title="+检验数据"
        v-model="visible"
        width="1000px"
        :close-on-click-modal="false"
        @close="closeDialog"
        class="my-dialog my-padding-dialog">
        <div class="g-content">
            <el-table v-loading="loading"
                :data="tableData"
                id="itemTable"
                ref="mainTable"
                highlight-current-row
                border
                stripe
                height="400"
                style="width: 100%">
                <!-- <el-table-column type="selection" width="55">
                </el-table-column> -->
                <template v-for="item in configTable.filter(_i => !_i.iIsHide)"
                    :key="item.index">
                    <el-table-column show-overflow-tooltip
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort">
                    </el-table-column>
                </template>
                <el-table-column min-width="80"
                    prop="dValue"
                    label="检验结果值">
                    <template v-slot="{ row, $index }">
                        <!-- @blur="saveData(row)" -->
                        <el-input v-model="row.dValue"
                            :min="0"
                            class="i-input text-left"
                            type="number"
                            :ref="`inputRef${$index}`"
                            size="small"
                            style="width: calc(100% - 10px)"
                            @change="changeValue"
                            @keyup.up.native="(event) => onKeyup(event, $index)"
                            @keyup.down.native="(event) => onKeyup(event, $index)">
                            <template #suffix>
                                {{ row.sUnit }}
                            </template>
                        </el-input>
                        <span class="pl-1 pr-1"
                            v-if="row.dValue && row.dValue > (+row.dMaxValue)"
                            style="position:absolute; right: 5px;color: red;font-weight:bold;">↑</span>
                        <span class="pl-1 pr-1"
                            v-if="row.dValue && row.dValue < (+row.dMinValue)"
                            style="position:absolute; right: 5px;color: red;font-weight:bold;">↓</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template #footer>
            <div class="p-3">
                <el-button-icon-fa class="float-left"
                    icon="el-icon-setting"
                    type="primary"
                    plain
                    @click="inspectionDataSet_Dialog=true">设置</el-button-icon-fa>
                <el-button-icon-fa icon="fa fa-save"
                    type="primary"
                    @click="onSaveClick">保存</el-button-icon-fa>
                <el-button-icon-fa icon="el-icon-close"
                    @click="closeDialog">关闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
    <InspectionDataSet v-model:dialogVisible="inspectionDataSet_Dialog"
        :sProjectId="paramsId.sProjectId"
        @updateInspectionData="updateInspectionData"></InspectionDataSet>
</template>
<script>
// 接口
import { getInspectionByPatientId, saveInspection } from '$supersetApi/projects/apricot/case/report.js'

export default {
    components: {
        InspectionDataSet: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/inspectionDataSet.vue'))
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        paramsId: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['addInspectData', 'closeDialog'],
    data () {
        return {
            visible: false,
            loading: false,
            firstLoad: true,
            page: { // 分页	
                pageCurrent: 1,
                pageSize: 9999,
            },
            reRender: true,
            tableData: [],
            selectedData: [],
            isChangeValue: false,
            configTable: [{
                sProp: 'iIndex',
                sLabel: '排序号',
                sAlign: 'center',
                sWidth: '76px'
            },
            {
                sProp: 'sCode',
                sLabel: '项目编码',
                sAlign: 'left',
                sMinWidth: '80px'
            },
            {
                sProp: 'sName',
                sLabel: '项目名称',
                sAlign: 'left',
                sMinWidth: '120px'
            },

            {
                sProp: 'dMinValue',
                sLabel: '最小值',
                sAlign: 'left',
                sWidth: '100px'
            },
            {
                sProp: 'dMaxValue',
                sLabel: '最大值',
                sAlign: 'left',
                sWidth: '100px'
            },
            {
                sProp: 'sUnit',
                sLabel: '单位',
                sAlign: 'left',
                sWidth: '100px'
            }],
            inspectionDataSet_Dialog: false,
        }
    },
    watch: {
        dialogVisible (val) {
            this.visible = val;
            if (val) {
                this.getData()
            }
        }
    },
    methods: {
        // 刷新
        updateInspectionData () {
            this.getData();
        },
        // 键盘事件
        onKeyup (event, index) {
            // 点击上下键切换输入框
            if (this.tableData.length === 0) return;
            var idx = index
            if (event.key === "ArrowDown") {
                if (idx < this.tableData.length - 1) {
                    idx++
                }
                this.$refs[`inputRef${idx}`].focus();
            }
            if (event.key === "ArrowUp") {
                if (idx > 0) {
                    idx--
                }
                this.$refs[`inputRef${idx}`].focus();
            }
        },
        changeValue (value) {
            console.log(value)
            this.isChangeValue = true;
        },
        saveData (row) {
            if (!this.isChangeValue || (row.dValue !== null && String(row.dValue).length === 0)) {
                return
            }
            row.dValue = !row.dValue ? '0' : row.dValue = this.auto2Fixed(row.dValue);

            let params = { ...row }
            params.sPatientId = this.paramsId.sPatientId
            params.sReportId = this.paramsId.sReportId
            saveInspection(params).then(res => {
                if (res.success) {
                    this.$message.success('保存成功！');
                    this.isChangeValue = false;
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        auto2Fixed (num) {
            if (!num) { return num }
            let suffix = num.toString().split('.')[1];
            if (!suffix && Number(num)) {
                return Number(num).toFixed(1)
            }
            if (suffix && suffix.length > 2) {
                return (Math.round(num * 1000) / 1000).toFixed(3);
            }
            return num
        },
        totalSelected (selection) {
            this.selectedData = selection
        },
        selectedRows (selection, row) {
            this.selectedData = selection
        },
        async onSaveClick () {
            if (!this.tableData.length) {
                this.$message.warning('未配置该项目下的检验值，请先配置！');
                return
            }
            const hasIdNoValueList = this.tableData.filter(item => item.sId && ['', undefined, null].includes(item.dValue));
            if (hasIdNoValueList.length) {
                this.$message.warning(`'${hasIdNoValueList[0].sName}'的检验结果值不能为空！`);
                return
            }
            const saveList = this.tableData.filter(item => !['', undefined, null].includes(item.dValue));
            let len = saveList.length;
            if (!len) return
            let count = 0;
            let isAllSuccess = true;
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            for (let i = 0; i < len; i++) {
                if (!isAllSuccess) {
                    break;
                }
                let row = saveList[i];
                row.dValue = ['', undefined, null].includes(row.dValue) ? row.dValue : (row.dValue = this.auto2Fixed(row.dValue));

                let params = { ...row }
                params.sPatientId = this.paramsId.sPatientId
                params.sReportId = this.paramsId.sReportId
                try {
                    const res = await saveInspection(params);
                    if (res?.success) {
                        count++;
                    } else {
                        isAllSuccess = false;
                    }
                } catch (err) {
                    console.log(err);
                    isAllSuccess = false;
                }
            }
            loading.close();
            if (isAllSuccess && count === len) {
                this.$message.success('保存成功！');
                await this.getData()
                return
            }
            if (!isAllSuccess) {
                await this.getData();
                let valueObject = {};
                let inspectionSetIdsList = [];
                saveList.slice(count).map(item => {
                    valueObject[item.sInspectionSetId] = item.dValue;
                    inspectionSetIdsList.push(item.sInspectionSetId);
                });
                this.tableData.map(item => {
                    if (inspectionSetIdsList.includes(item.sInspectionSetId)) {
                        item.dValue = valueObject[item.sInspectionSetId];
                    }
                })
            }
        },
        // addInspectionDataToReport () {
        //     if (!this.tableData.length) {
        //         this.$message.warning('未配置该项目下的检验值，请先配置！');
        //         return
        //     }
        //     if (!this.selectedData.length) {
        //         this.$message.warning('请选择需要插入的数据！');
        //         return
        //     }
        //     let paramsArr = []
        //     this.selectedData.forEach(ele => {
        //         let arrowDir = null
        //         let color = null
        //         if (ele.dValue === undefined || ele.dValue === null || Number(ele.dValue).toString() === 'NaN') {
        //             return
        //         }
        //         ele.dValue = Number(ele.dValue);
        //         ele.dMinValue = Number(ele.dMinValue);
        //         ele.dMaxValue = Number(ele.dMaxValue);
        //         if (ele.dValue >= ele.dMinValue && ele.dValue <= ele.dMaxValue) {
        //             arrowDir = ''
        //             color = '#333'
        //         }
        //         if (ele.dValue < ele.dMinValue) {
        //             arrowDir = '↓<i class="el-icon-bottom"></i>'
        //             color = '#f9bd00'
        //         }
        //         if (ele.dValue > ele.dMaxValue) {
        //             arrowDir = '↑'
        //             color = 'red'
        //         }
        //         let str = `<div>${ele.sName} ${ele.sCode} <span style="color:#707276">(正常范围${ele.dMinValue}${ele.sUnit} - ${ele.dMaxValue}${ele.sUnit})</span>， <b>${ele.dValue}</b>${ele.sUnit} <strong style="color: ${color}">${arrowDir}</strong>；</div>`
        //         paramsArr.push(str);
        //     })
        //     // console.log(paramsArr)
        //     if (!paramsArr.length) {
        //         this.$message.error('请先填写检验数据！')
        //         return
        //     }
        //     let paramsStr = paramsArr.join('')
        //     this.$emit('addInspectData', paramsStr)
        //     this.selectedData = []
        // },
        // mxDoRefresh 会回调这个方法--获取表格数据
        async getData () {
            this.loading = true
            let params = {
                sProjectId: this.paramsId.sProjectId,
                sPatientId: this.paramsId.sPatientId
            }
            this.selectedData = [];
            await getInspectionByPatientId(params).then(res => {
                this.loading = false
                if (res.success) {
                    let arr = res.data
                    if (arr.length) {
                        arr.forEach(element => {
                            if (Math.floor(element.dMaxValue) === element.dMaxValue) {
                                element.dMaxValue = this.auto2Fixed(element.dMaxValue)
                            }
                            if (Math.floor(element.dMinValue) === element.dMinValue) {
                                element.dMinValue = this.auto2Fixed(element.dMinValue)
                            }
                            if (Math.floor(element.dValue) === element.dValue) {
                                element.dValue = this.auto2Fixed(element.dValue)
                            }
                        });
                    }
                    this.tableData = arr
                    setTimeout(() => {
                        this.$refs.mainTable.$el.onkeydown = function () {
                            if (window.event.keyCode === 38 || window.event.keyCode === 40) {
                                window.event.returnValue = false;
                            }
                        }
                    }, 500);
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                this.loading = false
                console.log(err)
            })


        },
        closeDialog () {
            this.$emit('closeDialog', false);
        },
    },
    mounted () {
        this.firstLoad = false;
    },
}
</script>
<style lang="scss" scoped>
.my-dialog {
    min-height: 50%;
}

.g-content {
    padding: 15px 10px;

    header {
        padding: 20px 10px;
    }

    > section {
        padding: 0px 10px;
        margin-bottom: 20px;
    }
}

:deep(.my-dialog) {
    .el-dialog__footer {
        padding: 10px 10px 20px 10px;
    }
}
</style>
