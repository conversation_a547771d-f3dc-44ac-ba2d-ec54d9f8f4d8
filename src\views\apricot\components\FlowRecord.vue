<template>
  <el-dialog width="80%" :close-on-click-modal="false" append-to-body v-model="visible" 
    :destroy-on-close="true"  align-center @open="openDialog"
    @close="closeDialog" class="my-dialog t-default">
    <template #header>
        <span class="i-title el-dialog__title">流程记录</span>
        <span class="i-title el-dialog__title" v-if="patientInfo.sName">{{ patientInfo.sName }}</span>
        <span class="i-title el-dialog__title" v-if="patientInfo.sSexText">{{ patientInfo.sSexText }}</span>
        <span class="i-title el-dialog__title" v-if="patientInfo.sAge">{{ patientInfo.sAge }}</span>
        <span class="i-title el-dialog__title" v-if="patientInfo.dAppointmentTime">
          预约时间：{{ transformDate(patientInfo.dAppointmentTime, false, 'yyyy-MM-dd HH:mm') }}
        </span>
        <span class="i-title el-dialog__title" v-if="patientInfo.sProjectName">项目：{{ patientInfo.sProjectName }}</span>
    </template>
    <div class="m-flexLaout-ty" style="padding: 5px 5px 0; height: 70vh" >
        
          <div class="c-table g-flexChild m-flexLaout-ty">
            <div class="g-flexChild">
              <el-table :data="tableData" ref="mainTable" v-loading="loading" size="small"
               stripe border height="100%"
                highlight-current-row @row-click="onClickRow" style="width: 100%">
                <el-table-column align="center" label="序号" type="index" width="50">
                </el-table-column>
                <el-table-column v-for="(item, index) in tableHeaderProps" :key="index" :prop="item.sProp"
                  :label="item.sLabel" :width="item.sWidth" :min-width="item.sMinWidth" show-overflow-tooltip>
                  <template v-slot="scope">
                    <template v-if="item.sProp === 'dOperateTime'">
                      {{ transformDate(scope.row[`${item.sProp}`], true) }}
                    </template>
                    <template v-else>
                      {{ scope.row[`${item.sProp}`] }}
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="c-pagination">
              <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
                :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next" :total="page.total">
              </el-pagination>
            </div>
          </div>

    </div>
    
    <template #footer>
        <el-button-icon-fa _icon="fa fa-close-1" @click="closeDialog">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>
<script>

import { transformDate } from '$supersetUtils/function'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import { flowRecordPage } from '$supersetApi/projects/apricot/common'
export default {
  name: 'FlowRecord',
  mixins: [mixinTable],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    patientInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      loading1: false,
      tableHeaderProps: [
        {
          sProp: 'sOperationName',
          sLabel: '操作',
          sWidth: '150px',
        },
        {
          sProp: 'dOperateTime',
          sLabel: '系统时间',
          sWidth: '160px',
        },
        {
          sProp: 'sOperatorName',
          sLabel: '操作人',
          sWidth: '120px',
        },
        {
          sProp: 'sOperaIp',
          sLabel: 'IP',
          sWidth: '120px',
        },
        {
          sProp: 'sMemo',
          sLabel: '备注',
          sMinWidth: '120px',
        }
      ],
      tableData: [],
      flowData: [],
    }
  },
  watch: {
    dialogVisible(val) {
      this.visible = this.dialogVisible;
    },
  },
  methods: {
    transformDate: transformDate,
    closeDialog() {
      this.$emit('update:dialogVisible', false)
    },
    openDialog() {
      this.mxDoSearch();
    },
    getData(params) {
      if (!this.visible) {
        // 没有打开弹窗，结束弹窗
        return
      }
      params.condition = {};
      params.condition.sPatientId = this.patientInfo.sId;
      this.tableData = [];
      flowRecordPage(params).then(res => {
        this.loading = false;
        this.tableData = [];
        if (res.success) {
          this.tableData = res.data.recordList || [];
          this.page.total = res.data.countRow;
          return
        }
        this.$message.error(res.msg);
      }).catch(() => {
        this.loading = false;
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.i-title {
    &:not(:first-child){
        margin-left: 20px;
    }
}

.g-content {
  .c-table {
    margin-bottom: 10px;
  }
}
</style>
