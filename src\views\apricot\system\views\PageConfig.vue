<template>
    <div class="c-template">
        <DragAdjust :dragAdjustData="DA0">
            <template v-slot:c1>
                <div class="m-flexLaout-ty">
                    <div class="u-border-bottom u-btnArea"
                        style="text-align:right;">
                        <el-button-icon-fa link
                            _icon="fa fa-refresh"
                            size="small"
                            @click="handleRefreshClick">刷新</el-button-icon-fa>
                        <el-button-icon-fa link
                            _icon="fa fa-plus"
                            size="small"
                            @click="openModleDialog(1)">新增</el-button-icon-fa>
                        <el-button-icon-fa link
                            _icon="fa fa-pencil"
                            size="small"
                            @click="openModleDialog(2)">编辑</el-button-icon-fa>
                        <el-button-icon-fa link
                            _icon="fa fa-trash"
                            size="small"
                            @click="handleModleDeleteClick">删除</el-button-icon-fa>
                    </div>

                    <!-- 节点树 -->
                    <div class="g-flexChild"
                        v-loading="loadingTree">
                        <el-tree ref="treeRef"
                            node-key="sId"
                            :default-expanded-keys="expandedKeys"
                            :data="treeData"
                            :props="defaultProps"
                            highlight-current
                            @node-click="handleNodeClick"
                            @node-contextmenu="onContextmenu"></el-tree>
                    </div>
                    <!-- contextmenu -->
                    <v-contextmenu ref="contextmenu">
                        <v-contextmenu-item @click="addBrother()">添加兄弟节点</v-contextmenu-item>
                    </v-contextmenu>

                </div>
            </template>
            <template v-slot:c2>
                <ElementSetting :nodeData="seletedModleNode"
                    :refresh="refresh"></ElementSetting>
            </template>
        </DragAdjust>

        <!-- 模块弹窗 -->
        <el-dialog append-to-body
            title="模块编辑"
            v-model="d_modle_v"
            @close="handleModleCloseDialog"
            class="my-dialog"
            width="600px"
            :close-on-click-modal="false">
            <div class="c-look-content c-inner-content scope-input-look">
                <el-form ref="refEditLayer1"
                    :model="modleFormData"
                    :rules="rules">
                    <template v-for="(item, index) in treeEditFormInputs" :key="index">
                        <FormStyle v-if="!item.iCustom"
                            
                            :configData="item"
                            :formData="modleFormData"
                            :optionData="optionsLoc"></FormStyle>
                    </template>
                </el-form>
            </div>
            <template #footer><div 
                class="my-dialog-footer">
                <div class="g-page-footer">
                    <el-button-icon-fa _icon="fa fa-iconclose"
                        @click="handleModleCloseDialog"
                        size="small">关 闭</el-button-icon-fa>
                    <el-button-icon-fa type="primary"
                        _icon="fa fa-save"
                        size="small"
                        :loading="saveLoading"
                        @click="handleModleSaveClick">保 存</el-button-icon-fa>
                </div>
            </div></template>
        </el-dialog>

    </div>
</template>

<script>
import FormStyle from "$supersetViews/components/FormStyle.vue"
import ElementSetting from "./page_config/ElementSetting.vue"
import config from "../config/index"
import {
    adjustGetTree,
    adjustModelAdd,
    adjustModelEdit,
    adjustModelDel
} from "$supersetApi/projects/apricot/system/pageConfig"

export default {
    name: "Apricot_System_PageConfig",
    components: {
        FormStyle,
        ElementSetting
    },
    data () {
        return {
            d_modle_v: false,
            DA0: config.DA0,
            defaultProps: {
                children: "childs",
                label: "sNodeName"
            },
            TreeNodeConfigs: config.TreeNodeConfigs,
            optionsLoc: config.optionsLoc,
            rules: {},
            modleFormData: {},
            tableType: "form",
            treeEditType: null,
            editType: 1,
            seletedModleNode: {},
            treeData: [],
            loadingTree: false,
            saveLoading: false,
            cascader1Options: [],
            expandedKeys: [],
            selectedModleElement: {},
            refresh: Math.random()
        };
    },
    computed: {
        treeEditFormInputs () {
            //根据当前编辑模式返回对应的列表
            return this.TreeNodeConfigs[this.treeEditType];

        },
    },
    watch: {
        seletedModleNode: {
            handler: function (val) {
                if (val.iType != 3) {
                    return
                }
                this.refresh = Math.random();
            },
            deep: true
        }
    },
    methods: {
        // 右键事件
        onContextmenu (event, obj, node) {
            this.$refs.treeRef.setCurrentNode(obj)
            this.handleNodeClick(obj, node)
            this.contextmenuShow(event)
        },
        // 显示右键
        contextmenuShow (ev) {
            // const targetDimensions = this.$refs.cTree.getBoundingClientRect()
            const postition = {
                top: ev.clientY,
                left: ev.clientX
            }
            this.$refs.contextmenu.show(postition)
        },
        // 元素模块配置 添加兄弟节点
        addBrother () {
            this.openModleDialog(3)
        },
        // 刷新树节点
        handleRefreshClick () {
            this.modleFormData = {};
            this.seletedModleNode = {};
            //重新获取数据
            this.adjustGetTree();
        },
        // 树节点点击事件
        handleNodeClick (data, node) {
            //树节点数据
            this.seletedModleNode = data;
            this.selectedModleElement = node;
            this.expandedKeys = [data.sId];
            if (data.sModelType) {
                this.tableType = data.sModelType;
            }
            if (this.seletedModleNode.iType == 3) {
                // this.getElementTableData();
                // this.$nextTick(() => {
                //     this.rowDrop()
                // })
            }
        },
        // 打开模块编辑弹窗
        openModleDialog (type) {
            let obj = {
                '1': 'system',
                '2': 'module',
                '3': 'container'
            }
            this.editType = type;
            let iType = this.seletedModleNode.iType || 0;
            this.modleFormData = {};
            if (type == 1) {
                // 新增当前节点的子节点
                if (iType == 3 && type == 1) {
                    // 当前节点为表格或者表单节点的时候，不可以创建子节点
                    this.messageTipSelf('warning', '该节点不可新增子节点');
                    return
                }
                this.d_modle_v = true;
                let childrenType = iType + 1;
                this.treeEditType = obj[childrenType];
                this.modleFormData["iType"] = childrenType;
                this.modleFormData["iSetClass"] = 0;
                if (childrenType != 1) {
                    this.modleFormData["sSystemLabel"] = this.seletedModleNode.sSystemLabel;
                    this.modleFormData["sParentId"] = this.seletedModleNode.sId;
                }
                return;
            }
            if (type == 2) { //编辑当前节点
                if (!Object.keys(this.seletedModleNode).length) {
                    this.messageTipSelf('warning', '请选择节点标识')
                    return
                }
                this.d_modle_v = true;
                this.treeEditType = obj[iType];
                for (let item in this.seletedModleNode) {
                    this.modleFormData[item] = this.seletedModleNode[item];
                }
                return
            }
            //新增 当前节点的兄弟节点                   
            this.d_modle_v = true;
            this.treeEditType = obj[iType];
            this.modleFormData["iType"] = this.seletedModleNode.iType;
            this.modleFormData["iSetClass"] = '0';
            if (iType != 1) {
                this.modleFormData["sSystemLabel"] = this.seletedModleNode.sSystemLabel;
                this.modleFormData["sParentId"] = this.seletedModleNode.sParentId;
            }
        },
        // 关闭模块编辑弹窗
        handleModleCloseDialog () {
            // this.modleFormData = {};
            this.$refs.refEditLayer1.resetFields();
            this.d_modle_v = false;
        },
        // 删除模块节点
        handleModleDeleteClick () {
            if (!Object.keys(this.seletedModleNode).length) {
                this.messageTipSelf('warning', '请选择一条数据！')
                return;
            }
            let temp = Object.assign({}, this.seletedModleNode);
            this.$confirm(`此操作将永久删除‘${temp.sNodeName}’节点吗, 是否继续?`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                adjustModelDel({
                    iVersion: temp.iVersion,
                    sId: temp.sId
                }).then(res => {
                    if (res.success) {
                        // 刷新树
                        this.messageTipSelf('success', '删除成功');
                        const parent = this.selectedModleElement.parent;
                        const children = parent.data.childs || parent.data;
                        const index = children.findIndex(d => d.sId === this.seletedModleNode.sId);
                        children.splice(index, 1);
                        this.seletedModleNode = {};
                    } else {
                        this.messageTipSelf('error', res.msg)
                    }
                }).catch(err => {
                    console.log(err)
                });

            }).catch(() => {
                this.messageTipSelf('info', '已取消删除');
            });

        },
        // 保存模块编辑数据
        handleModleSaveClick () {
            // TODO
            this.$refs.refEditLayer1.validate(valid => {
                if (!valid) {
                    this.messageTipSelf('warning', '请正确填写必填项');
                    return;
                }
                let temp = Object.assign({}, this.modleFormData);
                // 如果是顶级节点的话 系统标识等于节点标识
                if (temp.iType == 1) {
                    temp.sSystemLabel = temp.sNodeLabel;
                }
                if (this.editType == 2) {
                    this.saveLoading = true;
                    adjustModelEdit(temp).then(res => {
                        this.saveLoading = false;
                        if (res.success) {
                            this.messageTipSelf('success', res.msg);
                            this.handleModleCloseDialog();
                            this.seletedModleNode = res.data;
                            ///刷新树
                            this.adjustGetTree();
                            return
                        }
                        this.messageTipSelf('warning', res.msg);
                    }).catch(() => {
                        this.saveLoading = false;
                    });
                } else if (this.editType == 1 || this.editType == 3) {
                    this.saveLoading = true;
                    adjustModelAdd(temp).then(res => {
                        this.saveLoading = false;
                        if (res.success) {
                            ///刷新树
                            this.messageTipSelf('success', res.msg);
                            this.handleModleCloseDialog();
                            this.adjustGetTree();
                            return
                        }
                        this.messageTipSelf('warning', res.msg);
                        this.saveLoading = false;
                    }).catch(err => {
                        console.log(err)
                    });
                }
            });
        },
        // 自定义message提示 offset， duration
        messageTipSelf (type, msg) {
            this.$message({
                message: msg,
                type: type,
                offset: 90,
                duration: 2500
            });
        },
        // 获取元素模块节点树
        adjustGetTree () {
            this.loadingTree = true;
            adjustGetTree().then(res => {
                this.loadingTree = false;
                if (res.success) {
                    this.treeData = res.data ? res.data : [];
                    // this.seletedModleNode = {}
                    this.$nextTick(() => {
                        if (Object.keys(this.seletedModleNode).length) {
                            this.$refs.treeRef.setCurrentNode(this.seletedModleNode)
                        }
                    })
                } else {
                    this.messageTipSelf('warning', res.msg)
                }
            }).catch(err => {
                this.loadingTree = false;
                console.log(err)
            });
        },

    },
    created () {
        this.adjustGetTree();
    },
    mounted () {
        // this.$nextTick(() => {
        //     this.rowDrop()
        // })
    },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style lang="scss" scoped>
:deep(.my-dialog .el-dialog__body ) {
    padding: 12px 15px 12px 5px;
}

.f-elementEditTableSearchFormSubmit {
    position: relative;
    top: 10px;
    right: 15px;
}
.c-form.t-1 {
    margin-left: 5px;
}
.el-form {
    overflow: hidden;
    padding-bottom: 5px;
    :deep(.el-form-item__error ) {
        bottom: -3px;
    }
    .el-select,
    .el-cascader {
        width: 100%;
    }
}
</style>
