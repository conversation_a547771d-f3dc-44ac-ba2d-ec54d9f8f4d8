import { ElMessageBox, ElMessage } from 'element-plus';

export default {
    methods: {
        callBackPageList(params) {
            this.ApiTable.pageList(params).then((res) =>{
                if(res.success) {
                    this.tableData = res.data.list || [];
                    this.page.total = res.data.total;
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected();
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        callBackSave(params) {
            if (this.actionState == 1){
                // 新增
            }else {
                // 编辑
                this.ApiTable.update(params).then((res) => {
                    this.editLayer.loading = false; 
                    if(res.success) {
                        ElMessage.success(res.msg);
                        this.editLayer.visible = false;
                        this.mxGetTableList();
                        return;
                    }
                    ElMessage.error(res.msg);
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        callBackDel(item) {
            this.ApiTable.del({id: item.id}).then((res) =>{
                if(res.success) {
                    ElMessage.success(res.msg);
                    this.mxGetTableList();
                    return;
                }
                ElMessage.error(res.msg);
            }).catch((e) => {
                console.log(e)
            })
        }
    }
};
