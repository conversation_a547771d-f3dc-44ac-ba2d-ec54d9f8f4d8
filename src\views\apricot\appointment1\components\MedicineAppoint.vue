<template>
    <!-- 药物预约 -->
    <LayerSlide title="用药统计" v-model="visible" @open="openLayer" fixed>
        <div class="c-dialog-body m-flexLaout-ty">
            <div class="c-form">
                <SearchList v-model="condition" :list="searchListConfig" :optionData="optionsLoc"
                storageKey="AppointmentMedicineAppointSearch">
                    <template v-slot:deviceItem>
                        <el-cascader v-model="condition.deviceItem"
                            :options="optionsLoc.deviceItemOptions"
                            :props="{ checkStrictly: true, expandTrigger: 'hover', value: 'sId', label: 'sName', children: 'childs'}"
                            ref="deviceItem"
                            clearable
                            placeholder=""
                            @change="onCascaderChange('deviceItem')"></el-cascader>
                    </template>
                    <template v-slot:dAppointmentTimeSt>
                        <el-date-picker v-model="condition.dAppointmentTimeSt"
                            @change="doSearch"
                            type="date">
                        </el-date-picker>
                    </template>
                    <template v-slot:dCalibrationDate>
                        <el-date-picker v-model="condition.dCalibrationDate"
                            @change="doSearch"
                            type="datetime"
                            :default-time="defaultTime">
                        </el-date-picker>
                    </template>
                </SearchList>
                <el-button-icon-fa type="primary"
                _icon="fa fa-search"
                @click="doSearch">查询</el-button-icon-fa>
            </div>
            <div class="c-flex-auto m-flexLaout-ty u-autoHeight">
                <div class="c-item m-flexLaout-ty">
                    <h5>
                        <span>预约用药统计</span>
                        <el-button-icon-fa class="float-right" type="primary" link icon="fa fa-file-excel-o" @click="exportStatisticsTable">导出</el-button-icon-fa>
                    </h5>
                    <div class="c-content">
                        <el-table-extend v-if="configTable" ref='mainTable' :iModuleId="iModuleId"
                        storageKey="appointment-MedicineAppoint-table1" v-loading="loading" :data="tableData" 
                        stripe border highlight-current-row height="100%" style="width: 100%">
                            <el-table-column type="index"
                                label="序号"
                                align="center"
                                width="60">
                            </el-table-column>
                            <template v-for="item in configTable.filter(_i=> !_i.iIsHide)">
                                <el-table-column show-overflow-tooltip
                                    :prop="item.sProp"
                                    :label="item.sLabel"
                                    :fixed="item.sFixed"
                                    :align="item.sAlign"
                                    :width="item.sWidth"
                                    :min-width="item.sMinWidth"
                                    :sortable="!!item.iSort"
                                    >
                                </el-table-column>
                            </template>
                        </el-table-extend>
                    </div>
                </div>
                <div class="c-item t-2 m-flexLaout-ty">
                    <h5>
                        <span>预约用药患者</span>
                        <el-button-icon-fa class="float-right" type="primary" link icon="fa fa-file-excel-o" @click="exportPatientTable">导出</el-button-icon-fa>
                    </h5>
                    <div class="c-content">
                        <el-table-extend v-if="configTable1" ref='mainTable' :iModuleId="iModuleId"
                        storageKey="appointment-MedicineAppoint-table2" v-loading="loading1" :data="patientsData" 
                        stripe border highlight-current-row height="100%" style="width: 100%">
                            <el-table-column type="index"
                                label="序号"
                                align="center"
                                width="60">
                            </el-table-column>
                            <template v-for="item in configTable1.filter(item => !item.iIsHide)">
                                <el-table-column show-overflow-tooltip
                                    :prop="item.sProp"
                                    :label="item.sLabel"
                                    :fixed="item.sFixed"
                                    :align="item.sAlign"
                                    :width="item.sWidth"
                                    :min-width="item.sMinWidth"
                                    :sortable="!!item.iSort"
                                    >
                                    <template v-slot="scope">
                                        <template v-if="item.sProp == 'dAppointmentTime'">
                                            {{ scope.row[`${item.sProp}`] | mxToDate() }}
                                        </template>
                                        <template v-else>
                                            {{scope.row[`${item.sProp}`]}}
                                        </template>
                                    </template>
                                </el-table-column>
                            </template>
                        </el-table-extend>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-item u-border-top">
            <el-button-icon-fa
                _icon="fa fa-close-1"
                @click="visible = false">关闭</el-button-icon-fa>
        </div>

    </LayerSlide>
</template>

<script>

import { transformDate, deepClone } from '$supersetUtils/function'
import { recentDayOptions } from '$supersetResource/js/projects/apricot/enum.js'
import { getToken } from "$supersetUtils/auth"
import Api from '$supersetApi/projects/apricot/appointment/medicineAppoint.js'
import { getItemTreeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinExportExcel } from '$supersetResource/js/projects/apricot/index.js'

export default {
    name: 'MedicineAppoint',
    mixins: [ mixinExportExcel],
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            iModuleId: 6,
            defaultTime: new Date(2023, 1, 1, 8, 0, 0),
            loading: false,
            loading1: false,
            configTable: [{
                sProp: 'sAppointmentTime',
                sLabel: ' 预约日期',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sDistrictName',
                sLabel: '院区 ',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sRoomText',
                sLabel: '检查设备',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sMachineryRoomText',
                sLabel: '机房',
                sAlign: 'center',
                sMinWidth: '100px'
            },
            {
                sProp: 'sProjectName',
                sLabel: '检查项目',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sNuclideText',
                sLabel: '核素',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sTracerText',
                sLabel: '示踪剂',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'iCount',
                sLabel: '人数',
                sAlign: 'left',
                sMinWidth: '80px'
            },
            {
                sProp: 'fDosage',
                sLabel: '总预订量（mCi）',
                sAlign: 'left',
                sMinWidth: '110px'
            }],
            configTable1: [{
                sProp: 'sAppointmentTime',
                sLabel: '预约日期',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sName',
                sLabel: '姓名',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sSexText',
                sLabel: '性别',
                sAlign: 'left',
                sMinWidth: '70px'
            },
            {
                sProp: 'sAge',
                sLabel: '年龄',
                sAlign: 'left',
                sMinWidth: '70px'
            },
            {
                sProp: 'sNuclearNum',
                sLabel: '核医学号',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sDistrictName',
                sLabel: '院区 ',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sRoomText',
                sLabel: '检查设备',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sMachineryRoomText',
                sLabel: '机房',
                sAlign: 'center',
                sMinWidth: '100px'
            },
            {
                sProp: 'sProjectName',
                sLabel: '检查项目',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sNuclideText',
                sLabel: '核素',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'sTracerText',
                sLabel: '示踪剂',
                sAlign: 'left',
                sMinWidth: '100px'
            },
            {
                sProp: 'dAppointmentTime',
                sLabel: '检查时间',
                sAlign: 'left',
                sMinWidth: '90px'
            },
            {
                sProp: 'fWeight',
                sLabel: '体重（kg）',
                sAlign: 'left',
                sMinWidth: '100px',
            },
            {
                sProp: 'fCoefficient',
                sLabel: '处方系数',
                sAlign: 'left',
                sMinWidth: '100px',
            },
            {
                sProp: 'fRecipeDose',
                sLabel: '处方剂量（mCi）',
                sAlign: 'left',
                sMinWidth: '150px',
            },
            {
                sProp: 'fDecayPeriod',
                sLabel: '衰变周期',
                sAlign: 'left',
                sMinWidth: '150px',
            },
            {
                sProp: 'fDosage',
                sLabel: '预订量（mCi）',
                sAlign: 'left',
                sMinWidth: '120px',
            }],
            searchListConfig: [
                { prop: 'deviceItem', label: '设备/项目', width: '20%', iCustom: 1 },
                { prop: 'dAppointmentTimeSt', label: '预约时间', width: '20%', iCustom: 1 },
                { prop: 'dCalibrationDate', label: '标定日期', width: '20%', iCustom: 1 },
            ],
            condition: {
                dAppointmentTimeSt: new Date(new Date().getTime() + 24 * 60 * 60 * 1000),
                dAppointmentTimeEd: new Date(new Date().getTime() + 24 * 60 * 60 * 1000)
            },
            pickerOptionsAppointDayStart: {
                disabledDate: time => {
                    if (this.condition.dAppointmentTimeEd) {
                        return time.getTime() > new Date(this.condition.dAppointmentTimeEd).getTime()
                    }
                }
            },
            pickerOptionsAppointDayEnd: {
                disabledDate: time => {
                    if (this.condition.dAppointmentTimeSt) {
                        return time.getTime() < new Date(this.condition.dAppointmentTimeSt).getTime()
                    }
                }
            },
            optionsLoc: {
                recentDayOptions: recentDayOptions,
                deviceItemOptions: []
            },
            tableData: [],
            patientsData: [],
            oneLoad: true
        }
    }, 
    computed: {
        visible: {
            get: function() {
                return this.modelValue
            },
            set: function(val) {
                this.$emit('update:modelValue', val)
            }
        }
    },
    methods: {
        openLayer() {
            this.tableData = [];
            this.patientsData = [];
            this.doSearch();
            if (this.oneLoad) {
                this.oneLoad = false
                this.getItemTreeData()

            }
        },
        mxToDate (val) {
            let timeText = '';
            if (!val) {
                return timeText
            }
            let date = new Date(val);
            let hh = date.getHours();
            hh = hh < 10 ? `0${hh}` : hh;
            let mm = date.getMinutes();
            mm = mm < 10 ? `0${mm}` : mm;
            timeText = `${hh}:${mm}`
            return timeText
        },
        exportStatisticsTable () {
            if (!this.tableData.length) {
                this.$message.warning('表格中不存在数据');
                return
            }
            this.exportTable('appointMedicine');
        },
        exportPatientTable () {
            if (!this.patientsData.length) {
                this.$message.warning('表格中不存在数据');
                return
            }
            this.exportTable('appointMedicinePatient');
        },
        exportTable (sModuleName) {
            let condition = deepClone(this.condition);
            condition.dAppointmentTimeSt = transformDate(condition.dAppointmentTimeSt, true);
            condition.dAppointmentTimeEd = transformDate(condition.dAppointmentTimeSt, true);
            condition.dCalibrationDate = transformDate(condition.dCalibrationDate, true);

            let deviceItem = condition.deviceItem
            if (deviceItem && deviceItem.length) {
                condition.sRoomId = deviceItem[0];
                condition.sProjectId = deviceItem[1];
            }
            delete condition.deviceItem;
            delete condition.sRecentDays;
            
             // 调用统一混入导出方法  
            this.mxDoExportTable(condition, sModuleName, []);
        },
        changeTimes (val) {
            let target = this.optionsLoc.recentDayOptions.find(item => item.keyWord == val);
            if(target) {
                this.condition.dAppointmentTimeSt = moment().add(target.dates[0], 'days').toDate();
                this.condition.dAppointmentTimeEd = moment().add(target.dates[1], 'days').toDate();
            } else {
                this.condition.dAppointmentTimeSt = '';
                this.condition.dAppointmentTimeEd = '';
            }
            this.doSearch()
        },
        doSearch () {
            this.getMedicineStatistics();
            this.getMedicinePatients()
        },
        // 格式化一天开始时间
        mxFormateOneDayStart (time) {
            if (!time) return '';
            return moment(time).startOf('day').toDate();
        },
        // 格式化一天结束时间
        mxFormateOneDayEnd (time) {
            if (!time) return '';
            return moment(time).endOf('day').toDate();
        },
        // 获取统计数据
        getMedicineStatistics () {
            // if(!this.condition.dCalibrationDate){
            //     this.$message.warning('请选择标定日期');
            //     return
            // }
            let condition = deepClone(this.condition);
            let dAppointmentTimeSt = condition.dAppointmentTimeSt;
            condition.dAppointmentTimeSt = this.mxFormateOneDayStart(dAppointmentTimeSt);
            condition.dAppointmentTimeEd =  this.mxFormateOneDayEnd(dAppointmentTimeSt);

            let deviceItem = condition.deviceItem
            if (deviceItem && deviceItem.length) {
                condition.sRoomId = deviceItem[0];
                condition.sProjectId = deviceItem[1];
            }
            delete condition.deviceItem;
            delete condition.sRecentDays;

            this.loading = true;
            Api.getMedicineStatistics(condition).then((res) => {
                if (res.success) {
                    this.tableData = res.data || []
                    this.loading = false;
                    return
                }
                this.loading = false;
                this.tableData = [];
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
                this.loading = false;
            })
        },
        // 获取患者数据
        getMedicinePatients () {
            // if(!this.condition.dCalibrationDate){
            //     this.$message.warning('请选择标定日期');
            //     return
            // }
            let condition = deepClone(this.condition);
            let dAppointmentTimeSt = condition.dAppointmentTimeSt;
            condition.dAppointmentTimeSt = this.mxFormateOneDayStart(dAppointmentTimeSt);
            condition.dAppointmentTimeEd =  this.mxFormateOneDayEnd(dAppointmentTimeSt);

            let deviceItem = condition.deviceItem
            if (deviceItem && deviceItem.length) {
                condition.sRoomId = deviceItem[0];
                condition.sProjectId = deviceItem[1];
            }
            delete condition.deviceItem;
            delete condition.sRecentDays;

            this.loading1 = true
            Api.getMedicinePatients(condition).then((res) => {
                if (res.success) {
                    this.patientsData = res.data || [];
                    this.loading1 = false;
                    return
                }
                this.loading1 = false;
                this.patientsData = [];
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
                this.loading1 = false;
            })
        },
        // 联级选择并失去焦点
        onCascaderChange (ref) {
            this.$refs[ref].dropDownVisible = false;
            this.doSearch();
        },
        getItemTreeData () {
            getItemTreeData().then(res => {
                if (res.success) {
                    this.optionsLoc.deviceItemOptions = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
    }
}
</script>

<style lang="scss" scoped>
.c-dialog-body {
    height: calc(100% - 50px);
}
.c-form {
    display: flex;
    align-items: center;
}
.c-flex-auto {
    flex: 1;
    overflow: hidden;
    .c-item {
        flex: 1;
        padding: 0 10px;
        overflow: hidden;
        &.t-2 {
            flex: 2;
            overflow: hidden;
        }
        h5 {
            margin: 20px 0 10px;
            padding-left: 6px;
            color: #3e3e3e;
            font-weight: bold;
            border-left: solid 4px var(--theme-header-bg);
            font-size: 14px;
        }
    }
    .c-content {
        flex: 1;
        overflow: hidden;
    }
}
.footer-item {
    margin: 10px;
    padding: 10px 0px;
    text-align: right;
}
.float-right {
    float: right;
}
.u-border-top {
    border-top-color: #eee;
}
.el-cascader {
    line-height: inherit;
}
</style>
