<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-button">
                <el-button-icon-fa
                    type="primary" 
                    plain 
                    icon="el-icon-refresh" 
                    :loading="loading" 
                    @click="mxDoRefresh">刷新</el-button-icon-fa>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content" v-loading="loading">
                <el-table 
                    :data="tableData" 
                    id="itemTable" 
                    ref="mainTable" 
                    size="small" 
                    border
                    stripe 
                    height="100%" 
                    style="width: 100%">
                    <!-- <el-table-column align="center"
                        type="selection"
                        width="55">
                    </el-table-column> -->
                    <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                        <el-table-column show-overflow-tooltip 
                             
                            :prop="item.sProp" 
                            :label="item.sLabel"
                            :fixed="item.sFixed" 
                            :align="item.sAlign" 
                            :width="item.sWidth" 
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort" 
                            >
                            <template v-slot="{row}">
                                <template
                                    v-if="['iAllowUpate','iForceUpate', 'iRegister', 'iUploadLog', 'iClientRunning'].includes(item.sProp)">
                                    {{row[`${item.sProp}`] == 1 ? '是' : '否'}}
                                </template>
                                <template v-else-if="['dAuthExpireDate', 'dLastHeatbeatTime'].includes(item.sProp)">
                                    {{setTime2yyyyMMDDHHmm(row[`${item.sProp}`])}}
                                </template>
                                <template v-else-if="item.sProp === 'action'">
                                    <el-button-icon-fa link 
                                        size="small"
                                        type="primary" 
                                        icon="el-icon-edit" 
                                        @click="openEditDiolog(row)">编辑</el-button-icon-fa>
                                    <el-divider direction="vertical"></el-divider>
                                    <!-- <el-button-icon-fa link 
                                        size="small" 
                                        _icon="fa fa-gear"  
                                        @click="openParamsDiolog(row)">配置</el-button-icon-fa> -->
                                    <!-- <el-divider direction="vertical"></el-divider> -->
                                    <el-button-icon-fa
                                        link 
                                        size="small" 
                                        _icon="fa fa-id-card"
                                        @click="openRegistedDiolog(row)">
                                        注册
                                    </el-button-icon-fa>
                                    <!-- <i title="注册" style="cursor: pointer; font-size: 12px;" class="fa fa-id-card"
                                        @click="openRegistedDiolog(row)">&nbsp;注册</i>
                                     -->
                                    <!-- <i title="参数配置" style="cursor: pointer; font-size: 14.8px;" class="fa fa-gear"
                                        @click="openParamsDiolog(row)"></i> -->
                                </template>
                                <template v-else>
                                    <span>{{row[`${item.sProp}`]}}</span>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>

        <el-dialog :close-on-click-modal="false" append-to-body title="客户端编辑" v-model="clientEditDiolog"
            :destroy-on-close="true" width="600px" class="t-default">
            <div class="c-inner-content" v-bind:class="{ 'scope-input-look': editLayer.look }">
                <el-form label-width="90px" :model="client" :rules="rules">
                    <el-form-item prop="clientName" label="客户端名称:">
                        <el-input v-model="client.sDesc" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer><div  class="my-dialog-footer">
                <div class="g-page-footer">
                    <el-button-icon-fa v-show="!editLayer.look" icon="el-icon-check"
                        :loading="editLayer.loading" type="primary" @click="onEdit()">保存</el-button-icon-fa>
                    <el-button-icon-fa icon="el-icon-close" @click="clientEditDiolog = false">取消</el-button-icon-fa>
                    
                </div>
            </div></template>
        </el-dialog>
        <el-dialog :close-on-click-modal="false" append-to-body title="客户端注册" v-model="clientRegistedDiolog"
            :destroy-on-close="true" width="600px" class="t-default">
            <div class="c-inner-content" v-bind:class="{ 'scope-input-look': editLayer.look }">
                <el-form label-width="90px" :model="clientForm" :rules="rules">
                    <el-form-item prop="" label="客户端名称:">
                        <span>{{clientForm.sDesc}}</span>
                    </el-form-item>
                    <el-form-item prop="" label="客户端IP:">
                        <span>{{clientForm.sIp}}</span>
                    </el-form-item>
                    <el-form-item prop="" label="主板序列号:">
                        <span>{{clientForm.sBaseboardSerialNoMD5}}</span>
                    </el-form-item>
                    <el-form-item prop="" label="授权码:">
                        <el-input type="textarea" :autosize="{ minRows: 3}" v-model="clientForm.sAuthCode" />
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <div class="my-dialog-footer">
                    <div class="g-page-footer">
                        <el-button-icon-fa v-show="!editLayer.look" icon="el-icon-check"
                            :loading="editLayer.loading" type="primary" @click="onRegisterClient()">保 存</el-button-icon-fa>
                        <el-button-icon-fa icon="el-icon-close" @click="clientRegistedDiolog = false">取 消
                        </el-button-icon-fa>
                    </div>
                </div>
            </template>
        </el-dialog>

        <el-dialog :close-on-click-modal="false" append-to-body title="参数设置" v-model="d_params_visiable"
            :destroy-on-close="true" width="80%" fullscreen @close="closeParamsDialog" class="my-dialog t-default my-full-dialog my-padding-dialog">
            <div class="c-inner-content m-flexLaout-ty">
                <div class="c-title">
                    <span>{{clientInfo.sDesc}}</span>
                    <div v-if="isDefaultClient" class="float-right">
                        <el-button-icon-fa type="primary" _icon="fa fa-plus" @click="onAddParamsRow">添加</el-button-icon-fa>
                    </div>
                </div>
                <div class="g-flexChild">
                    <el-table :data="paramsData" class="m-table" height="100%" border>
                        <el-table-column property="sKey" label="参数名" width="200" show-overflow-tooltip>
                            <template v-slot="{row}">
                                <el-input v-if="isDefaultClient" v-model="row.sKey"></el-input>
                                <span v-else>{{row.sKey}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column property="sDesc" label="参数描述" width="200" show-overflow-tooltip>
                            <template v-slot="{row}">
                                <el-input v-if="isDefaultClient" v-model="row.sDesc"></el-input>
                                <span v-else>{{row.sDesc}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column property="sValue" label="参数值" show-overflow-tooltip>
                            <template v-slot="{row}">
                                <el-input v-model="row.sValue"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="isDefaultClient" label="操作" width="150" align="center">
                            <template v-slot="{$index}">
                                <el-button-icon-fa link _icon="fa fa-plus" 
                                    @click="onInsertParamsRow($index)">添加</el-button-icon-fa>
                                <el-button-icon-fa link icon="el-icon-delete" 
                                    @click="onDelParamsRow($index)">删除</el-button-icon-fa>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <template #footer>
                <el-button-icon-fa  icon="el-icon-check" :loading="d_params_save" type="primary"
                    @click="onParamsSave">保 存</el-button-icon-fa>
                <el-button-icon-fa  icon="el-icon-close" @click="closeParamsDialog">取 消</el-button-icon-fa>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import { transformDate } from '$supersetUtils/function.js'
import {
    getClientData, editClient, registerClient, clientFindClientNo,
    clientSetSave
} from '$supersetApi/projects/apricot/system/clientMgmt.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'ExportTemplate',
    mixins: [mixinTable],
    props: {},
    data() {
        return {
            loading: false,
            clientEditDiolog: false,
            clientRegistedDiolog: false,
            client: {},
            clientForm: {},
            configTable: [
                {
                    sProp: 'sDesc',
                    sLabel: '客户端名称',
                    sAlign: 'left',
                    sMinWidth: '100px'
                },
                {
                    sProp: 'sIp',
                    sLabel: 'IP地址',
                    sAlign: 'left',
                    sMinWidth: '120px'
                },
                // {
                //     sProp: 'sMac',
                //     sLabel: '物理地址',
                //     sAlign: 'left',
                //     sMinWidth: '80px'
                // },
                // {
                //     sProp: 'sBaseboardSerialNoMD5',
                //     sLabel: '主板序列号',
                //     sAlign: 'left',
                //     sMinWidth: '100px'
                // },
                // {
                //     sProp: 'sProcessorId',
                //     sLabel: '处理器ID',
                //     sAlign: 'left',
                //     sMinWidth: '80px'
                // },
                {
                    sProp: 'iClientRunning',
                    sLabel: '是否运行中',
                    sAlign: 'center',
                    sWidth: '120px'
                },
                {
                    sProp: 'dAuthExpireDate',
                    sLabel: '授权码过期时间',
                    sAlign: 'left',
                    sMinWidth: '100px'
                },
                {
                    sProp: 'iRegister',
                    sLabel: '是否注册',
                    sAlign: 'center',
                    sMinWidth: '80px'
                },
                // {
                //     sProp: '',
                //     sLabel: '测试结果',
                //     sAlign: 'center',
                //     sMinWidth: '80px'
                // },
                
                // {
                //     sProp: 'iAllowUpate',
                //     sLabel: '是否运行更新',
                //     sAlign: 'center',
                //     sWidth: '106px'
                // },
                // {
                //     sProp: 'dLastHeatbeatTime',
                //     sLabel: '最后在线时间',
                //     sAlign: 'left',
                //     sMinWidth: '100px'
                // },
                // {
                //     sProp: 'iUploadLog',
                //     sLabel: '是否上传日志',
                //     sAlign: 'center',
                //     sWidth: '106px'
                // },
                // {
                //     sProp: 'iForceUpate',
                //     sLabel: '是否强制更新',
                //     sAlign: 'center',
                //     sWidth: '106px'
                // },
                // {
                //     sProp: 'sCurrentVersion',
                //     sLabel: '当前版本号',
                //     sAlign: 'center',
                //     sWidth: '94px'
                // },
                // {
                //     sProp: 'sTargetVersion',
                //     sLabel: '目标版本号',
                //     sAlign: 'center',
                //     sWidth: '94px'
                // },
                {
                    sProp: 'action',
                    sFixed:'right',
                    sLabel: '操作',
                    sAlign: 'center',
                    sWidth: '200px'
                }
            ],
            d_params_visiable: false,
            d_params_save: false,
            paramsData: [],
            clientInfo: {},
            isDefaultClient: false,
        }
    }, 
    methods: {
      setTime2yyyyMMDDHHmm(val) {
            return transformDate(val, false, 'yyyy-MM-dd HH:mm')
        },
        onAddParamsRow() {
            this.paramsData.unshift({ sClientNo: this.clientInfo.sBaseboardSerialNoMD5 });
        },
        onInsertParamsRow(index) {
            this.paramsData.splice(index + 1, 0, { sClientNo: this.clientInfo.sBaseboardSerialNoMD5 });
        },
        onDelParamsRow(index) {
            this.paramsData.splice(index, 1);
        },
        // 打开参数配置弹窗，获取配置参数
        openParamsDiolog(row) {
            this.clientInfo = row;
            this.isDefaultClient = false;
            if (row.sBaseboardSerialNoMD5 == 0) {
                this.isDefaultClient = true;
            }
            clientFindClientNo({ sClientNo: row.sBaseboardSerialNoMD5 }).then(res => {
                if (res.success) {
                    this.d_params_visiable = true;
                    this.paramsData = res.data || [];
                    this.paramsData.map(item => {
                        item.sClientNo = row.sBaseboardSerialNoMD5;
                    });
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
            })
        },
        // 关闭参数弹窗
        closeParamsDialog() {
            this.d_params_visiable = false;
            this.paramsData = [];
        },
        // 保存参数配置
        onParamsSave() {
            let len = this.paramsData.length;
            if (this.isDefaultClient) {
                for (let i = 0; i < len; i++) {
                    let temp = this.paramsData[i];
                    if ([undefined, null, ''].includes(temp.sKey)) {
                        this.$message.warning('参数名不能为空！')
                        return
                    }
                    if (!temp.sDesc) {
                        this.$message.warning('参数描述不能为空！')
                        return
                    }
                    this.paramsData[i].iIndex = i + 1;
                }
            }
            this.d_params_save = true;
            clientSetSave(this.paramsData).then(res => {
                this.d_params_save = false;
                if (res.success) {
                    this.$message.success(res.msg);
                    this.closeParamsDialog();
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.d_params_save = false;
            })
        },
        // 打开编辑弹框
        openEditDiolog(row) {
            this.clientEditDiolog = true
            this.client = Object.assign({}, row)
        },
        // 打开注册弹框
        openRegistedDiolog(row) {
            this.clientRegistedDiolog = true
            this.clientForm.sDesc = row.sDesc
            this.clientForm.sIp = row.sIp
            this.clientForm.sBaseboardSerialNoMD5 = row.sBaseboardSerialNoMD5
        },
        /**
        * 保存表单编辑数据
        */
        onEdit() {
            editClient(this.client).then(res => {
                if (res.success) {
                    this.clientEditDiolog = false
                    this.$message.success('修改成功！')
                    this.getData()
                    return
                }
                this.$message.error(res.msg)
            })
        },
        /**
         * 注册客户端
         */
        onRegisterClient() {
            let params = {
                sAuthCode: this.clientForm.sAuthCode,
                sBaseboardSerialNoMD5: this.clientForm.sBaseboardSerialNoMD5,
            }
            registerClient(params).then(res => {
                if (res.success) {
                    this.clientRegistedDiolog = false
                    this.$message.success('授权注册成功!')
                    this.getData()
                    return
                }
                this.$message.error(res.msg)
            })
        },
        /**
         * 获取表格数据
         */
        getData() {
            this.loading = true
            getClientData().then((res) => {
                if (res.success) {
                    this.tableData = res.data
                    this.loading = false;
                    // 赋选中状态
                    this.setSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    }
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px;

    :deep(.c-form) {
        display: flex;
        flex-direction: column;

        .c-form-button {
            padding: 0 10px 10px 0px;
        }
    }

    :deep(.c-flex-auto ){
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
       .c-content {
            flex: 1;
            height: 0px;
        }
    }
}

:deep(.drag-set) {
    &.i-show {
        .board-column-header {
            background: #4c9ddf;
        }
    }

    &.i-hide {
        .board-column-header {
            background: #ccc;
        }

        .board-column-content {
            flex-wrap: wrap;
        }
    }
}
.c-inner-content {
    padding: 10px;
    height: calc(100% - 20px) !important;
    .c-title {
        margin-bottom: 10px;
        > span {
            font-size: 16px;
            line-height: 30px
        }
    }
}
</style>
