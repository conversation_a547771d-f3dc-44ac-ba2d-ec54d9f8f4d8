<template>
    <!-- 打印按钮 -->
    <div :class="{ 
      'is-flex': propParams.isBatch || buttonMsg.icon,
      'print-btn-comp': true,
      'is-popup': isPopup && btnsOption.length > 1,
      'i-show': isShowPopup}"
      v-bind="$attrs"
      v-click-outside="handleOutsideClose">
      <el-button-icon-fa v-if="isPopup && btnsOption.length > 1" class="popup-hover-icon" icon="fa fa-print" 
        type="primary" plain 
        @mouseenter="togglePopover(true)" @mouseleave="togglePopover(false)"> 
        <!-- @click.stop="isShowPopup= !isShowPopup" -->
        打印
        <Icon name="el-icon-arrow-up"></Icon> 
      </el-button-icon-fa>
      <div class="popup-wrap"  @mouseenter="isPopup && togglePopover(true)" @mouseleave="isPopup && togglePopover(false)">
        <div class="child-print-btn" v-for="(item, index) in btnsOption" :key="index"> 
            <el-popover v-model:visible="isTemShows[index]"
                trigger="manual"
                placement="bottom"
                :width="450"
                :teleported="true"
                :ref="`print-popover${index}`"
                :popper-class="currentTemIdx == index && isTemLoading ? 'my-popover u-none' : 'my-popover'"
              >
                <p class="c-headline">
                    <strong>打印模板选择：</strong>  
                    <!-- <el-button link type="success" class="btn-record" v-if="!propParams.isBatch && Object.keys(propParams.patient).length" @click="onPrintRecordClick(index)"><i class="fa fa-log"></i>打印记录</el-button> -->
                </p>
                <div class="c-btns" v-loading="teploading">
                    <el-scrollbar class="scrollMenuBox"
                        :class="{'scroll-height': item.templateList.length >= 6}">
                        <template v-if="item.templateList.length">
                            <div v-for="(template, idx) in item.templateList" 
                                :key="idx"
                                class="c-btn-item">
                                <el-button class="i-btn"
                                    type="primary"
                                    plain
                                    size="large"
                                    :title="template.sTemplateName"
                                    :class="{'i-active': selectedTemItem.sTemplateId === template.sTemplateId}"
                                    @click="onTemplateClick(template, index)">{{template.sTemplateName}}</el-button>
                                <el-icon v-if="selectedTemItem.sTemplateId === template.sTemplateId" size="20" class="i-icon">
                                    <i class="el-icon-check"></i>
                                </el-icon>
                            </div>
                        </template>
                        <div v-else>
                            <el-empty :image-size="50" description="注：未配置模板" />
                        </div>
                    </el-scrollbar>
                    <template v-if="item.templateList.length && sPrintShow.showType == 2">
                        <div class="c-params" v-if="sPrintShow.isShowParams">
                            <el-select v-if="isTemOnline"
                                v-model="currentPrintParams.sPrinterName"
                                placeholder="请选择打印机"
                                :disabled="!selectedTemItem.sTemplateId"
                                :teleported="false"
                                style="flex: 2; margin-right: 10px;"
                                @change="onHandleChange()">
                                <el-option v-for="(name, index) in optionsTemLoc.sPrinterNameOptions"
                                    :key="index"
                                    :label="name"
                                    :value="name"></el-option>
                            </el-select>
                            <div v-else style="flex: 2; margin-right: 10px;color:red;line-height: 2;text-align: center;">客户端未启动</div>
                            <el-select v-model="currentPrintParams.sFileType"
                                placeholder="文件类型"
                                :disabled="!selectedTemItem.sTemplateId"
                                :teleported="false"
                                style="flex: 1; margin-right: 10px;"
                                @change="onHandleChange()">
                                <el-option v-for="fileType in optionsTemLoc.sFileTypeOptions"
                                    :key="fileType.sValue"
                                    :label="fileType.sValue"
                                    :value="fileType.sValue"></el-option>
                            </el-select>
                            <el-input v-model="currentPrintParams.iCopies"
                                type="number"
                                :disabled="!selectedTemItem.sTemplateId"
                                style="flex: 1;"
                                @change="onHandleChange()"></el-input>
                        </div>
                        <div class="i-action-btns">
                            <el-button v-if="sPrintShow.isShowPriviewBtn" type="primary" @click="onPrintClick(2, index)">预览打印</el-button>
                            <el-button v-if="sPrintShow.isShowPrintBtn" type="primary" @click="onPrintClick(1, index)">直接打印</el-button>
                            <el-button v-if="sPrintShow.isShowDownloadBtn" type="primary" @click="onPrintClick(3, index)">下载报告</el-button>
                        </div>
                    </template>
                </div>
                <template #reference>
                  <div class="inline-block" @click="handleShow(item, index)">
                    <slot>
                      <el-button 
                          :class="{
                              'm-vertical-btn t2': buttonMsg.icon,
                              'margin_l': !buttonMsg.icon,
                              'm-vertical-text': buttonMsg.isFold,
                              't-border': buttonMsg.isBorder
                          }"
                          :disabled="buttonMsg.isReadOnly"
                          :plain="buttonMsg.plain"
                          :link="buttonMsg.link"
                          :type="buttonMsg.type"
                          >
                          <svg v-if="buttonMsg.icon"
                              class="fa"
                              aria-hidden="true">
                              <use :xlink:href="'#' + buttonMsg.icon"></use>
                          </svg>
                          <template v-if="!buttonMsg.icon" #icon>
                            <el-icon><Printer/></el-icon>
                          </template>
                          <!-- {{propParams.isBatch ? '(批量)' : ''}} -->
                          <label>打印{{item.sClassify}}</label>
                      </el-button>
                    </slot>
                  </div>
                </template>
            </el-popover>
        </div>
      </div>

    </div>
    <!-- 打印记录 -->
    <!-- <PrintRecord v-model:dialogVisible="d_printRecord_v"
        :patientInfo="propParams.patient"></PrintRecord> -->
</template>

<script>
import { Printer } from '@element-plus/icons-vue';
import { deepClone } from '$supersetUtils/function'
import { saveWorkStationPrint } from '$supersetApi/projects/apricot/system/templateSet.js'

import { mixinPrintPreview } from '$supersetResource/js/projects/apricot/index.js'

export default {
    name: 'ReportPrintBtn',
    components: {
        Printer,
        // PrintRecord: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintRecord.vue'))
    },
    mixins: [mixinPrintPreview],
    props: {
        // 按钮信息
        buttonMsg: {
            type: Object,
            default: () => ({})
        },
        // 模板数据
        // propParams: {
        //      patient: 患者信息(object), 
        //      isBatch: 是否批量打印(boolean), 
        //      idKey: 患者Id的字段名(string), 
        //      deviceTypeIdKey: 设备类型Id的字段名(string)
        // }
        propParams: {
            type: Object,
            default: () => ({})
        },
        isCentralizedPrint: {
            type: Boolean,
            default: false
        },
        multipleSelection:{
            type: Array,
            default: () => []
        },
        isPopup: {
            type: Boolean,
            default: false
        },
        
    },
    data () {
        return {
            isTemShows: {},
            selectedTemItem: {},
            isTemLoading: false,
            currentTemIdx: null,
            patientId: undefined,
            d_printRecord_v: false,
            isViewerActivated: true,
            isShowPopup: false
        }
    },
    created () {
        if(this.isCentralizedPrint) {
            this.btnsOption = [
                {
                    iClassify: 1,
                    sClassify: '报告',
                    templateList: new Array()
                }
            ]
        } 
    },
    activated() {
        this.isViewerActivated = true;
    },
    deactivated() {
        this.isViewerActivated = false;
    },
    mounted () {
        // 打印设置关联模板修噶，通知刷新打印按钮类型
        this.$eventbus.on('onRefreshPrintButtonClassify', res => {
            res && this.isViewerActivated && this.mxGetPrintClassifyOfModule(this.propParams.iModuleId);
        });
        if(this.$parent.fixed) {
            return
        }
        if(!this.isCentralizedPrint) {
            this.mxGetPrintClassifyOfModule(this.propParams.iModuleId);
            return
        }
    },
    beforeUnmount() {
        this.$eventbus.off('onRefreshPrintButtonClassify')
    },
    methods: {
        async handleShow (item, index) {
            if(this.isTemShows[index]){
                this.isTemShows[index] = false;
                return
            }
            const workStation =  this.$store.getters['user/workStation'] || {};
            if(!workStation.stationId) {
                this.$message.warning('请选择工作站！');
                return
            }
            this.selectedTemItem = {};
            this.currentPrintParams = this.$options.data().currentPrintParams;
            this.currentTemIdx = index;
            this.isTemLoading = true;
            let deviceTypeIdKey = this.propParams.deviceTypeIdKey || 'sRoomId';
            let params = {
                iClassify: item.iClassify,
                iModuleId: this.propParams.iModuleId,
            }
            // 单个打印需要传递设备类型参数
            if(!this.propParams.isBatch) {
                params.sDeviceTypeId = this.propParams.patient[deviceTypeIdKey]
            }
            // this.propParams.isBatch && delete params.sDeviceTypeId;
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            await this.mxGetWorkStationPrintShow();
            await this.mxGetTemplateOfPrintClassify(params);
            if(item.templateList.length === 1 && this.sPrintShow.showType == 1) {
                this.onTemplateClick(item.templateList[0], index);
                this.isTemLoading = false;
                loading.close();
                return
            }
            this.mxGetPrinterNames();
            this.isTemShows[index] = true;
            this.isTemLoading = false;
            loading.close();
            // this.$nextTick(() => {
            //     this.$refs[`print-popover${index}`][0].updatePopper();
            // })
        },
        async onTemplateClick (item, index) {
            this.selectedTemItem = item;
            await this.mxWorkStationPrintOfTemplate(item);
            if(this.sPrintShow.showType == 2) {
                return
            }
            let params = deepClone(this.propParams);
            params.template = this.selectedTemItem;
            params.iOpenType = 2;
            params.iPrintType = this.sPrintShow.iPrintType || 2;  // 2 = 打开文件
            if(this.sPrintShow.isOneTemplate) {
                params.iPrintType = 2
            }
            this.mxHandlePrint(params);
            this.isTemShows[index] = false;
        },
        onPrintClick(iPrintType, index) {
            if(!this.selectedTemItem.sTemplateId) {
                this.$message.warning('请选择打印模板！')
                return
            }
            // if(!this.isTemOnline) {
            //     this.$message.error('客户端未启动！')
            //     return
            // }
            let params = deepClone(this.propParams);
            params.template = this.selectedTemItem;
            params.iOpenType = 2;
            params.iPrintType = iPrintType;
            this.mxHandlePrint(params);
            this.isTemShows[index] = false;
        },
        onHandleChange() {
            if(!this.selectedTemItem.sTemplateId) {
                this.$message.warning('请选择打印模板！')
                return
            }
            let jsonData = this.currentPrintParams;
            jsonData.iWorkStationId = this.workStation.stationId;
            saveWorkStationPrint(jsonData).then(res => {
                if (res.success) {
                    // this.$message.success(res.msg);
                    this.currentPrintParams.sId = res.data.sId;
                    this.selectedTemItem.iCopies = res.data.iCopies;
                    this.selectedTemItem.sFileType = res.data.sFileType;
                    this.selectedTemItem.sPrinterName = res.data.sPrinterName;
                    return
                }
                this.$message.error(res.msg);
                this.mxWorkStationPrintOfTemplate(jsonData);
            }).catch((err) => {
                console.log(err);
            })
        },
        // 打开打印记录
        onPrintRecordClick (index) {
            this.d_printRecord_v = true;
            this.isTemShows[index] = false;
        },
        handleOutsideClose() {
            this.isShowPopup = false;
        },
        togglePopover(value) {
            this.isShowPopup = value;
        }
    }
}
</script>

<style lang="scss" scoped>

.print-btn-comp {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  // background: black;
  .popup-wrap {
    position: relative;
    display: inline-block;
  }
  .child-print-btn {
    position: relative;
    display: inline-block;
  }
  .popup-hover-icon {
    position: relative;
    margin-left: 10px;
  }
  &.is-flex {
    .popup-wrap {
      display: flex;
    }
    display: flex;
  }
  &.is-popup {
    display: inline-flex!important;
    width: auto;
    height: auto;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    .popup-wrap {
      // display: none;
      position: absolute;
      display: block;
      width: 0;
      bottom: 100%;
      left: 0;
    }
    &.i-show {
      overflow: visible;
      .popup-wrap {
        left: -10px;
        width: auto;
        background: #fff;
        padding: 15px 30px 15px 10px;
        border: 1px solid #ddd;
        .child-print-btn {
            // width: 100%;
            width: 150px;
            margin: 5px 0;
        }
        .inline-block,.el-button {
            width: 100%;
        }
        .el-button {
            justify-content: start;
        }
      }
    }
  }
}
.c-headline {
    margin: 10px;
    font-size: 15px;
}
.btn-record {
    margin-top: -10px;
    float: right;
}
.c-btns {
    margin: 0;
    // border: 1px solid #f0f0f0;
    border-radius: 5px;
    // background-color: #eeeeee;

    .c-btn-item {
        text-align: center;
        position: relative;
        .i-icon {
            position: absolute;
            top: 40%;
            transform: translateY(-50%);
            right: 15px;
        }
    }
                        
    .el-button {
        margin: 0;
        margin-bottom: 10px;
        margin-right: 15px;
    }
    .i-btn {
        width: 100%;
        margin-right: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-color: var(--el-border-color);
    }
    .i-active:focus {
        background-color: var(--el-color-primary-light-9) !important;
        border-color: var(--el-border-color) !important;
        color: var(--el-color-primary);
    }
    .i-active:hover {
        background-color: var(--el-color-primary) !important;
        color: white;
    }
}
.c-params{
    display: flex;
    // width: 400px;
    margin: 20px 15px 10px;
}
.i-action-btns{
    padding: 10px 0;
    text-align: center;
    // .i-icon {
    //     color: var(--el-color-primary);
    //     font-size: 30px;
    //     position: relative;
    //     top: 10px;
    //     margin-left: 30px;
    //     cursor: pointer;
    //     &:hover {
    //         color: #4f9ddc;
    //     }
    // }
}
.scrollMenuBox {
    margin: 20px 15px;
    height: auto;
    // width: calc(100% - 15px);
}
:deep(.el-scrollbar__bar.is-horizontal){
    height: 0 !important;
}
.scroll-height {
    height: 300px;
}
/*隐藏水平滚动条*/
:deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
}
.margin_l {
    margin-left: 10px !important;
}

</style>


