<template>
    <!-- 预览按钮 -->
    <div :style="{'display': propParams.isBatch || buttonMsg.icon ? 'flex' : 'inline-block'}">
        <template v-for="(item, index) in btnsOption" :key="index">
            <!-- popper-class="my-popover" -->
            <el-popover v-model:visible="isTemShows[index]"
                placement="bottom"
                trigger="manual"
                :width="450"
                :ref="`preview-popover${index}`"
                :popper-class="currentTemIdx == index && isTemLoding ? 'my-popover u-none' : 'my-popover'">
                <p class="c-headline"><strong>模板选择：</strong></p>
                <div class="c-btns">
                    <el-scrollbar class="scrollMenuBox"
                        :class="{'srcoll-height': item.templateList.length >= 6}">
                        <template v-if="item.templateList.length">
                            <div v-for="(template, idx) in item.templateList" 
                                :key="idx"
                                style="text-align: center;">
                                <el-button class="i-btn"
                                    type="primary"
                                    plain
                                    size="large"
                                    :title="template.sTemplateName"
                                    @click="onTemplateClick(template, index)">{{template.sTemplateName}}</el-button>
                            </div>
                        </template>
                        <div v-else>
                            <el-empty :image-size="50" description="注：未配置模板" />
                        </div>
                    </el-scrollbar>
                </div>
                <template #reference>
                  <div class="inline-block" @click="handleShow(item, index)">
                    <slot>
                      <el-button
                          :class="{
                              'm-vertical-btn t2': buttonMsg.icon,
                              'margin_l': !buttonMsg.icon,
                              'm-vertical-text': buttonMsg.isFold,
                              't-border': buttonMsg.isBorder
                          }"
                          :disabled="buttonMsg.isReadOnly"
                          :plain="buttonMsg.plain"
                          :link="buttonMsg.link"
                          :type="buttonMsg.type"
                      >
                          <svg v-if="buttonMsg.icon"
                              class="fa"
                              aria-hidden="true">
                              <use :xlink:href="'#' + buttonMsg.icon"></use>
                          </svg>
                          <label>预览{{item.sClassify}}</label>
                      </el-button>
                    </slot>
                  </div>
                </template>
            </el-popover>
        </template>
    </div>
</template>

<script>
import { deepClone } from '$supersetUtils/function'
import { mixinPrintPreview } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'ReportPreviewBtn',
    mixins: [mixinPrintPreview],
    props: {
        buttonMsg: {
            type: Object,
            default: () => ({})
        },
        // propParams: {
        //      patient: 患者信息(object), 
        //      isBatch: 是否批量打印(boolean), 
        //      idKey: 患者Id的字段名(string), 
        //      deviceTypeIdKey: 设备类型Id的字段名(string)
        // }
        propParams: {
            type: Object,
            default: () => ({})
        },
    },
    data () {
        return {
            isTemShows: {},
            selectedTemItem: {},
            isTemLoding: false,
            currentTemIdx: null,
            btnsOption: [{
                iClassify: 1,
                sClassify: '报告',
                templateList: []
            }],
            currentPrintParams: {
                sPrinterName: '',
                sFileType: 'pdf',
                iCopies: 1
            }
        }
    },
    mounted () {
        // console.log('mounted: preview')
    },
    methods: {
        async handleShow (item, index) {
            if(this.isTemShows[index]){
                this.isTemShows[index] = false;
                return
            }
            this.currentTemIdx = index;
            this.isTemLoding = true;
            let deviceTypeIdKey = this.propParams.deviceTypeIdKey || 'sRoomId';
            let params = {
                iClassify: item.iClassify,
                iModuleId: this.propParams.iModuleId,
                sDeviceTypeId: this.propParams.patient[deviceTypeIdKey]
            }
            // 批量打印不需要传递设备类型参数
            this.propParams.isBatch && delete params.sDeviceTypeId;
            await this.mxGetTemplateOfPrintClassify(params);
            if(item.templateList.length === 1) {
                this.onTemplateClick(item.templateList[0], index);
                this.isTemLoding = false;
                return
            }
            this.selectedTemItem = {};
            this.isTemShows[index] = true;
            this.isTemLoding = false;
            // this.$nextTick(() => {
            //     this.$refs[`preview-popover${index}`][0].updatePopper();
            // })
        },
        async onTemplateClick (item, index) {
            this.selectedTemItem = item;
            let params = deepClone(this.propParams);
            params.template = item;
            params.iOpenType = 1;
            this.mxHandlePreview(params);
            this.isTemShows[index] = false;
        },
    }
}
</script>

<style lang="scss" scoped>
.c-headline {
    margin: 10px;
    font-size: 15px;
}
.c-btns {
    margin: 0;
    // border: 1px solid #f0f0f0;
    border-radius: 5px;
    // background-color: #eeeeee;
    .el-button {
        margin: 0;
        margin-bottom: 10px;
        margin-right: 15px;
    }
    .i-btn {
        width: 100%;
        margin-right: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        border-color: var(--el-border-color);
        // background-color: var(--el-color-primary-light-9);
    }
    .i-active {
        // background-color: var(--el-color-primary);
        // color: #Fff;
        // box-sizing: border-box;
    }
}
.scrollMenuBox {
    margin: 20px 15px;
    height: auto;
    box-sizing: border-box;
    // width: calc(100% - 15px);
}
.srcoll-height {
    height: 300px;
}
/*隐藏水平滚动条*/
:deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
}
.margin_l {
    margin-left: 10px;
}
</style>
