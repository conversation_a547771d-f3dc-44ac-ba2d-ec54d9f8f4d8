import { removeAccessToken } from '@/utils/accessToken';
import XTXSAB from '$supersetViews/login/XTXSAB.js'
import { guangxiqurenminUKeyGetPlain, guangxiqurenminUKeyVerify } from '$supersetApi/projects/apricot/case/report.js'
// 广西区人民医院
const mixinUKeySign = {
    data () {
        return {
            d_UKeyDialog_v: false,
            mxBusinessType: '',
        }
    },
    methods: {
        mxHandleUKeySign (certId, businessType) {
            this.mxBusinessType = businessType;
            this.initUKeyMode()
            XTXSAB.GetUserList((cb) => {
                // console.log('获取UKEY用户列表', cb)
                if (!cb.retVal) {
                    this.$message.error('请检查UKey是否被拔出！');
                    return
                }
                // console.log('cb',cb);
                const isUKeyUser = cb.retVal.includes(certId);
                if (!isUKeyUser) {
                    // this.$message.error('该UKey不是当前登录用户的UKey!');
                    this.showConfirm('该UKey不是当前登录用户的UKey');
                    return
                }
                var reportInfo = {
                    sPatientId: this.patientInfo.sId,
                    iBusinessType: businessType,
                    sUserName: this.userInfo.sName,
                };
                // 从后端获取报告原文
                guangxiqurenminUKeyGetPlain(reportInfo).then(res1 => {
                    if (!res1.success) {
                        this.$message.error(res.msg)
                        return
                    }
                    const plain = res1.data.plain;
                    const base64Plain = res1.data.base64Plain;
                    XTXSAB.SignedData(certId, base64Plain, (res2) => {
                        
                        // console.log('SignedData=', res2)
                        if (res2.retVal == "") {
                            // 情况：弹出alert窗口提示'not login',
                            // this.showConfirm('签名失败')
                            // 显示UKEY 密码弹窗
                            this.d_UKeyDialog_v = true;
                            return false;
                        }
                        const signedData = res2.retVal;
                        this.verifySign({ certId, plain, base64Plain, signedData, businessType })
                    });
                });
            });
        },
        // 获取到签名证书，进行服务器签名
        verifySign (params) {
            XTXSAB.GetSignCert(params.certId, (r) => {
                var userCert = r.retVal;
                if (userCert == "") {
                    this.$message.error("导出签名证书失败！");
                    return;
                }
                const { plain, base64Plain, signedData, businessType } = params;
                var verifyInfo = {
                    userCert: userCert,
                    plain: plain,
                    base64Plain: base64Plain,
                    signedData: signedData,
                    sPatientId: this.patientInfo.sId,
                    sReportId: this.form.reportId,
                    iBusinessType: businessType,
                    sUserId: this.userInfo.sId,
                    sUserCode: this.userInfo.sNo,
                    sUserName: this.userInfo.sName
                };
                // 验证服务器签名
                guangxiqurenminUKeyVerify(verifyInfo).then(res => {
                    if (res.success) {
                        // this.$message.success('签名成功！');
                        if (businessType == 1) {
                            this.signStatus.signSavedStatus = res.success
                        }
                        // 2审核
                        if (businessType == 2) {
                            this.signStatus.signAuditedStatus = res.success
                            this.requestauditReport(1)
                        }
                        if (businessType == 3) {
                            this.signStatus.signReAuditedStatus = res.success
                            this.requestFinalAuditReport(1)
                        }
                        if (businessType == 4) {
                            this.signStatus.signCommitedStatus = res.success
                            this.requesCommitReport()
                        }
                        return;
                    }
                    this.$message.error(res.msg);
                })
            });
        },
        initUKeyMode () {
            // 判断是否需要初始换UKEY（初始化则连接websocket）
            let isNeedInitUKeyXTXSAB = this.$store.getters['user/isNeedInitUKeyXTXSAB'];
            isNeedInitUKeyXTXSAB && XTXSAB.initXTXSAB();
            this.setIsNeedInitUKeyXTXSAB(false)
        },
        // 判断是否需要初始换UKEY
        setIsNeedInitUKeyXTXSAB (bool) {
            this.$store.commit({
                type: 'user/setIsNeedInitUKeyXTXSAB',
                isNeedInitUKeyXTXSAB: bool
            })
        },
        // 提示框
        showConfirm (text) {
            this.$confirm(`${text}, 是否重新登陆？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                removeAccessToken();
                const fromPath = this.$router.options.history.state.current;
                this.$router.push({ path: `/login?redirect=${fromPath}` }).catch(() => { })
            }).catch(err => {
                if (err === 'cancel') {
                    const errText = text === '签名失败' ? `${text}！` : `${text}，签名失败！`
                    this.$message.error(errText);
                }
            })
        }
    },
    mounted () { }
}
export default mixinUKeySign