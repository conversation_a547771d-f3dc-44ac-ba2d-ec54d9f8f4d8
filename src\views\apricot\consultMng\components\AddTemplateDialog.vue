<template>
    <el-dialog append-to-body
        :title="editRow.sTemplateName ? editRow.sTemplateName : '添加模板'"
        v-model="dialogVisible"
        fullscreen
        :modal="false"
        :close-on-click-modal="false"
        class="my-dialog my-full-dialog my-padding-dialog">
        <CustomGenerator style="overflow: hidden;" :editRow="editRow" @handleSave="handleSave"></CustomGenerator>
    </el-dialog>
</template>
<script>
import CustomGenerator from '$supersetViews/apricot/system/views/CustomGenerator.vue'
export default {
    components: {
        CustomGenerator
    }, 
    props: {
        modelValue: {
            type: Boolean,
            default: () => {return false }
        },
        editRow: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['update:modelValue', 'handleSave'],
    computed: {
        dialogVisible: {
            get: function() {
                return this.modelValue
            },
            set: function(val) {
                this.$emit('update:modelValue', val)
            }
        }
    },
    methods: {
        handleSave() {
            this.$emit('handleSave')
            this.dialogVisible = false;
        }
    }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog){
    &.is-fullscreen {
        width: 96% !important;
        height: 96% !important;
    }
    .el-dialog__header{
        background: #33383e;
        border-color: #727579;
        .el-dialog__title{
            color: white;
        }
        .el-dialog__close{
            color: #f9f9cb;
            &:hover{
                font-weight: bold;
            }
        }
    }
}
</style>
