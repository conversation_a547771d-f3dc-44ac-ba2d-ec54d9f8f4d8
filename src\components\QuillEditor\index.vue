<template>
  <div ref="editorContainer" class="quill-editor">
    <slot name="toolbar"></slot>
    <div ref="editor" vcontextmenu:contextmenu="{ trigger: ['contextmenu'] }"></div>
    <div v-if="isShowHelp" class="popup-window" ref="help">
      <div class="header">
        编辑器快捷键说明
        <i class="el-dialog__close el-icon el-icon-close" @click="(isShowHelp = false)"></i>
      </div>
      <div class="main">
        <div class="r">1. Ctrl + Z：撤销上一操作，Ctrl + Y：撤回上一次撤销操作。</div>
        <div class="r">2. 当已圈选一段文字时，按Ctrl + B 将文字加粗，按Ctrl + U 将文字加下划线，按Ctrl + I 将文字加斜体。</div>
        <div class="r">3. 在有序号列表中按回车键会换行并输入下一个序号，按Shift + 回车键在本序号内换行。</div>
        <!-- <div class="r">4. 在有序号列表中，未输入任何内容时按Tab键将一级序号变为二级序号（即将1 2 3 换为 a b c），按退格键将二级序号变为一级序号。</div>  -->
      </div>
    </div>
    <div v-if="isShowSymbol" class="popup-window" ref="symbol">
      <div class="header">
        插入特殊符号
        <i class="el-dialog__close el-icon el-icon-close" @click="(isShowSymbol = false)"></i>
      </div>
      <div class="main">
        <div v-for="(text, index) in symbolList" class="symbol-box" @click="insertSymbol(text)" :key="index">
          {{ text }}
        </div>
      </div>
    </div>



    <v-contextmenu ref="BlueContextmenu">
      <div v-if="!disabled" class="custom-contextmenu-inner">
        <v-contextmenu-item title=" " class="no-hover" style="">
          <div class="ediui-text " @click="BlueSentenceList_Dialog = true" style="display:  flex;  justify-content: end; min-width: 100px;">
            <i data-v-e117f028="" class="el-icon-setting" style="font-size: 14px;"></i>
            设置
          </div>
        </v-contextmenu-item>
        <v-contextmenu-group maxWidth="250px" class="menu-group">
          <v-contextmenu-item v-for="(item, index) in StructBookmarkList" :title="item.sBookmarkName"
            @click="insertBlue(item)" :key="index">
            <span class="context-text">{{ item.sBookmarkName }}</span>
          </v-contextmenu-item>
          <v-contextmenu-item v-if="!StructBookmarkList.length" class="no-hover">
            无数据
          </v-contextmenu-item>
        </v-contextmenu-group>
      </div>
    </v-contextmenu>

    <v-contextmenu ref="BlueContextmenu2">
      <div v-if="!disabled" class="custom-contextmenu-inner">
        <v-contextmenu-group maxWidth="250px" class="menu-group">
          <v-contextmenu-item v-for="(item, index) in StructBookmarkOptionList" :title="item.text"
            @click="insertBlue2(item.text)" :key="index">
            <span class="context-text">  {{ item.text }}</span>
          </v-contextmenu-item>
          <v-contextmenu-item v-if="!StructBookmarkOptionList.length" class="no-hover">
            无数据
          </v-contextmenu-item>
        </v-contextmenu-group>
      </div>
    </v-contextmenu>

    <v-contextmenu ref="CustomContextmenu">
      <!--
             <v-contextmenu-item title="粘贴" @click="handlePaste">
                粘贴
            </v-contextmenu-item> -->
      <div v-if="!disabled" class="custom-contextmenu-inner">
        <v-contextmenu-item title=" " class="no-hover" style="">
            <div class="ediui-text " @click="CommonSentence_Dialog = true" style="display:  flex;  justify-content: end; min-width: 100px;">
                <i data-v-e117f028="" class="el-icon-setting" style="font-size: 14px;"></i>
                设置
            </div>
        </v-contextmenu-item>
        <!-- <v-contextmenu-item title="复制" @click="() => handleCopyCut(0)">
          <div class="ediui-ico edui-for-copy  edui-icon "></div>
          <div class="ediui-text   ">复制</div>
        </v-contextmenu-item>
        <v-contextmenu-item title="剪切" @click="() => handleCopyCut(1)">
          <div class="ediui-ico edui-for-cut  edui-icon "></div>
          <div class="ediui-text   ">剪切</div>
        </v-contextmenu-item>
        <v-contextmenu-item divider class="no-hover">
        </v-contextmenu-item>
        <v-contextmenu-item title="插入常用语" class="no-hover">
          常用语
        </v-contextmenu-item>
        <v-contextmenu-item divider class="no-hover">
        </v-contextmenu-item> -->
        <v-contextmenu-group maxWidth="250px" mixWidth="150px" class="menu-group">
          <v-contextmenu-item v-for="(item, index) in CommonSentenceList" :title="item.sCommonWords"
            @click="insertCommonSentence(item.sCommonWords)" :key="index">
            <span class="context-text">{{ index + 1 }}: {{ item.sCommonWords }}</span>
          </v-contextmenu-item>

        </v-contextmenu-group>
      </div>
    </v-contextmenu>

    <el-popover v-if="storageKey" ref="popoverRef" trigger="click" placement="left" :width="'320'" :teleported="true" transition="none"
      :hide-after="0" @after-enter="onPopAfterEnter">
      <template #default>
        <!-- <div class="pop-header">
          <h3>{{ ('配置') }}</h3>
        </div>-->
        <el-table class="pop-table" :data="react.tableData" row-key="prop" ref="popTableRef" max-height="500">
          <el-table-column prop="label" :label="('编辑器按钮')" width="150" align="left">
            <template #default="scope">
              {{ scope.row.label }}
            </template>
          </el-table-column>
          <el-table-column prop="label" :label="('显示')" align="center" width="70">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isShow" @change="onChangeItemProperty(scope, 'isShow')"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="label" :label="('排序')" align="center" width="70">
            <el-icon class="drag-icon" :size="18">
              <Rank />
            </el-icon>
          </el-table-column>


        </el-table>
        <div class="flex items-center justify-between pt-2">
          <span class="flex">
            <el-button type="primary" @click="setStorage(1)">{{ ('保存到全局') }}</el-button>
            <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否恢复默认设置？')"
              :teleported="false" :persistent="false" @confirm="onClickResetConfig">
              <template #reference>
                <el-button type="primary" plain>{{ ('恢复默认设置') }}</el-button>
              </template>
            </el-popconfirm>
          </span>

          <span class="flex">
            <el-button type="default" @click="onClickClose">{{ ('关闭') }}</el-button>
          </span>

        </div>
      </template>
      <template #reference>
        <div class="config-btn">
          <el-icon class="setting-icon">
            <MoreFilled />
          </el-icon>
        </div>
      </template>
    </el-popover>
    <BlueSentenceList v-model:dialogVisible="BlueSentenceList_Dialog"></BlueSentenceList>
    
    <!-- 常用语设置 -->
    <CommonSentence v-model:dialogVisible="CommonSentence_Dialog"></CommonSentence>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import Sortable from 'sortablejs'
import { Rank, MoreFilled, ArrowDown } from '@element-plus/icons-vue'
import { cloneDeep, isArray } from 'lodash-es';
import { useStore } from 'vuex';

import { getOnlineConfig, saveOnlineConfig } from '@/utils'
import { globalOptions, eventBus } from './QuillEditorExtend'
import configData from './config'
const Quill = window.Quill

const defaultStorageData = {
  list: configData.defaultToolList
}




// export
export default {
  name: 'QuillEditor',
  props: {
    modelValue: String,
    disabled: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      required: false,
      default: () => ({})
    },
    storageKey: {
      type: String,
      default: "", // 不填不启用localstorage
    },
    iModuleId: {
      default: "", // 不填不启用数据上传
    },
    domParams: {
        type: Object,
        default: ({
            idx: undefined,
            superClass: undefined
        }),
    }
  },
  emits: ['ready', 'change', 'input', 'blur', 'focus', 'update:modelValue'],
  components: {
    Rank, MoreFilled,
    BlueSentenceList: defineAsyncComponent(() => import('./BlueSentenceList')),
    CommonSentence: defineAsyncComponent(() => import('./CommonSentence')),
  },

  setup(props) {
    const _store = useStore()
    const popoverRef = ref();
    const react = ref({
      tableData: [],
    });

    const userNo = computed(() => {
      let userInfo = _store.getters["user/userSystemInfo"] || {};
      return userInfo.sId
    })

    const CommonSentenceList = computed(() => {
      return _store.getters["apricot/report_module/CommonSentence"]
    })

    const StructBookmarkList = computed(() => {
      return _store.getters["apricot/report_module/StructBookmark"]
    }) 

    const quill = ref(null)
    const innerContent = ref('')
    const { modelValue, disabled } = toRefs(props)

    const showingToolList = computed(() => {
      return react.value.tableData.filter(item => item.isShow)
        .map(item => configData.toolConfigMap[item.prop])
    })


    // watch(innerContent, (newVal) => {
    //   quill.value.innerContent = newVal
    // })

    // const setWatch = () => {

    //   watch(modelValue, (newVal) => {
    //     if (quill.value) {
    //       if (newVal && newVal !== innerContent.value) {
    //         const newContent = (newVal)
    //         innerContent.value = newContent
    //         quill.value.pasteHTML(newContent)
    //       } else if (!newVal) {
    //         quill.value.setText('')
    //       }
    //     }
    //   })
    // }


    // watch(disabled, (newVal) => {
    //   if (quill.value) {
    //     quill.value.enable(!newVal)
    //   }
    // })

    return {
      id: Math.random() + '',
      react,
      innerContent,
      CommonSentenceList,
      StructBookmarkList,
      userNo,

      delayRender1: ref(false),


      popoverRef,
      getOnlineConfig,
      saveOnlineConfig,

      tableData: ref([]),
      globalOptions,
      innerOptions: ref({}),
      isShowHelp: ref(false),
      isShowSymbol: ref(false),
      showingToolList,
      symbolList: [...('ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩⅪⅫ'.split(''))],
      isShowCommonSentList: ref(false),
      isShowBlueList: ref(false),
      isShowBlueList2: ref(false),
      selectedBlue: null,
      BlueSentenceList_Dialog: ref(false),
      CommonSentence_Dialog: ref(false),
      StructBookmarkOptionList: ref([]),
    };
  },
  watch: {
    // 我也不知道为什么放到setup里面就会有bug
    modelValue(newVal) {
      if (this.quill) {
        if (newVal && newVal !== this.innerContent) {
          const newContent = replaceTagInner(newVal, / /g, '　')
          // debugger
          this.innerContent = newContent
          this.quill.pasteHTML(newContent)
        } else if (!newVal) {
          this.quill.setText('')
        }
      }
    },
    disabled(newVal) {
      if (this.quill) {
        this.quill.enable(!newVal)
      }
    },
    innerContent(newVal) {
      this.quill.innerContent = newVal
    },
  },
  created() {
    this.setDefaultData();
  },
  mounted() {
    // 很多时候会重载组件

    this.getStorage().then(() => {
      // debugger
      this.initialize()

    });
    this.$store.dispatch('apricot/report_module/loadCommonSentence')
    this.$store.dispatch('apricot/report_module/loadStructBookmark')

  },
  beforeUnmount() {
    this.quill = null
    delete this.quill
    this.unSubscribe && this.unSubscribe()
    this.unSubscribe2 && this.unSubscribe2()
  },
  methods: {

    initialize() {
      if (this.$el) {
        // debugger
        this.innerOptions = cloneDeep(this.globalOptions)
        Object.assign(this.innerOptions, this.options)

        if (this.storageKey) {
          this.innerOptions.modules.toolbar.container = this.showingToolList
        }

        // Instance
        this.quill = new Quill(this.$refs.editor, this.innerOptions)
        // if (!window.quill) window.quill = this.quill

        this.quill.enable(false)
        // Set editor content
        if (this.modelValue) {
          const newContent = replaceTagInner(this.modelValue, / /g, '　')
          this.quill.pasteHTML(newContent)
          this.innerContent = newContent
        }
        // Disabled editor
        if (!this.disabled) {
          this.quill.enable(true)
        }

        // 覆盖原有的复制、剪切事件捕捉
        this.quill.root.removeEventListener('copy', e => this.quill.clipboard.onCaptureCopy(e, false));
        this.quill.root.removeEventListener('cut', e => this.quill.clipboard.onCaptureCopy(e, true));

        this.quill.root.addEventListener('copy', e => this.onCaptureCopy(e, false));
        this.quill.root.addEventListener('cut', e => this.onCaptureCopy(e, true));

        // console.log(this.quill.root)
        // this.quill.container.addEventListener('drop', ev => {
        //     ev.stopPropagation();
        //     ev.preventDefault();
        //     this.quill.history.cutoff();
        //     const data = ev.dataTransfer.getData('text/html');
        //     console.log(ev )

        //     const newContent = spaceConvert(data).replace(/background-color\:.+?;/gi, '')
        //         .replace(/font-size\:.+?;/gi, '')
        //     const selection = this.quill.getSelection(true)
        //     //   this.quill.insertText(selection.index || 0, data, Quill.sources.USER);
        //     this.quill.clipboard.dangerouslyPasteHTML(selection.index || 0, newContent, Quill.sources.USER);
        //     this.quill.history.cutoff();
        // });

        // Mark model as touched if editor lost focus
        this.quill.on('selection-change', range => {
          if (!range) {
            this.$emit('blur', this.quill)
            setTimeout(() => {

              if (this.isShowBlueList2) {
                this.isShowBlueList2 = false
                this.$refs.BlueContextmenu2.hide()
              }
            }, 60);
          } else {
            this.$emit('focus', this.quill)
            this.checkCaret()
            if (this.isShowBlueList2) {
              this.isShowBlueList2 = false
              this.$refs.BlueContextmenu2.hide()
            }
          }

        })
        // Update model if text changes
        this.quill.on('text-change', (delta, oldDelta, source) => {
          // console.log(delta, source)
          let html = ''
          const quill = this.quill
          const text = this.quill.getText()

          // if (source == 'user' && Array.isArray(delta.ops)) {
          //   const insertEnter = delta.ops.find(item => item.insert === ' \n' || item.insert === '\n')
          //   if (insertEnter) {
          //     alert('Enter')
          //   }
          // }

          // if (source === 'user') {
          //   const mentionPattern = /test\[.+\]/g;
          //   const content = text
          //   const matches = content.match(mentionPattern);

          //   if (matches) {
          //     this.quill.history.cutoff();
          //       matches.forEach(match => {
          //           const index = content.indexOf(match);
          //           const length = match.length;
          //           this.quill.deleteText(index, length, Quill.sources.SILENT);
          //           this.quill.insertEmbed(index, 'mention', match.slice(0));
          //           this.quill.setSelection(index + length, Quill.sources.SILENT);
          //         });
          //         this.quill.history.cutoff();
          //       // console.log(this.$refs.CustomContextmenu)
          //       // this.$refs.CustomContextmenu.show({ top: 500, left: 500})
          //   }
          // }


          if (source === 'user') { // 确保是用户输入触发的事件
            const ops = delta.ops;

            if (ops.find(item => item.insert === ' ')) {
              const Selection = quill.getSelection(true);
              const newops = [
                { delete: 1 },
                { insert: '　' },
              ]
              const firstRetain = Selection && Selection.index > 1 ? { retain: Selection.index - 1 } : 0
              if (firstRetain) {
                newops.splice(0, 0, firstRetain)
              }
              quill.updateContents({ // 替换最后一个操作为插入 
                ops: newops
              });

            }

            if (ops.find(item => item.insert === ' \n')) {
              const Selection = quill.getSelection(true);
              const newops = [
                { delete: 1 },
                { insert: '\n　' },
              ]
              const firstRetain = Selection && Selection.index > 1 ? { retain: Selection.index } : 0
              if (firstRetain) {
                newops.splice(0, 0, firstRetain)
              }
              quill.updateContents({ // 替换最后一个操作为插入 
                ops: newops
              });

            }
          }

          html = this.$refs.editor.children[0].innerHTML

          if (html === '<p><br></p>') html = ''
          this.innerContent = replaceTagInner(html, / /g, '　')



          this.$emit('update:modelValue', this.innerContent)
          this.$emit('input', this.innerContent)
          this.$emit('change', { html, text, quill })

          const [range] = this.quill.selection.getRange();
          if (range) {
            this.checkCaret()
          };
        })

        configData.titleConfig.forEach((item) => {
          const tip = (this.$el || document).querySelector('.quill-editor ' + item.className)
          tip && tip.setAttribute('title', item.title)
        });

        this.quill.triggerHelp = this.triggerHelp
        this.quill.triggerSymbol = this.triggerSymbol
        this.quill.triggerCommonSentence = this.triggerCommonSentence
        this.quill.triggerBlueContextmenu = this.triggerBlueContextmenu
        this.quill.triggerWinClick = this.triggerWinClick

        const innerBody = this.quill.container
        let customCaretElement = innerBody.querySelector('.editor-caret')
        if (!customCaretElement) {
          customCaretElement = document.createElement('div')
          customCaretElement.setAttribute('class', 'editor-caret')
          customCaretElement.setAttribute('style', '')
          customCaretElement.setAttribute('title', '高亮光标')
          innerBody.appendChild(customCaretElement)
        }

        // bus同步更新
        this.unSubscribe = eventBus.subscribe("eventX", (storageKey, iModuleId, uid) => {
          if (this.storageKey == storageKey && this.iModuleId == iModuleId && uid != this._.uid) {
            this.getStorage().then(() => {
              this.refreshToolBar()
            });
          }
        });

        this.unSubscribe2 = eventBus.subscribe("triggerBlue", (node, event) => {
          var targetElement = this.$el;
          var rect = targetElement.getBoundingClientRect();
          var clickedX = event.clientX;
          var clickedY = event.clientY;

          if (clickedX >= rect.left && clickedX <= rect.right && clickedY >= rect.top && clickedY <= rect.bottom) {
            this.triggerBlueContextmenu2(node)
          }
        });

        this.$emit('ready', this.quill)
      }
    },
    // 放大，还原
    triggerWinClick() {
        const { idx, superClass } = this.domParams;
        if(!superClass) return
        let dialogHeaderEl = this.$el.querySelector(`.quill-trigger-win`);
        const s_data = dialogHeaderEl.getAttribute('s-data');
        let itemDom = document.querySelectorAll(`.${superClass} .g-adjust-item`);
        if(!itemDom) return
        if(s_data == 'max') {
            dialogHeaderEl.innerHTML = configData.restoreWinSvg;
            dialogHeaderEl.setAttribute('s-data', 'min');
            dialogHeaderEl.setAttribute('title', '还原');
            itemDom[idx].classList.add('i-full');
        } else {
            dialogHeaderEl.innerHTML = configData.maxWinSvg;
            dialogHeaderEl.setAttribute('s-data', 'max');
            dialogHeaderEl.setAttribute('title', '放大');
            itemDom[idx].classList.remove('i-full');
        }
    },
    onCaptureCopy(e, isCut = false) {
      if (e.defaultPrevented) return;
      e.preventDefault();
      const [range] = this.quill.selection.getRange();
      if (range == null) return;
      const text = this.quill.getText(range);
      e.clipboardData.setData('text/plain', text);
      e.clipboardData.setData('text/html', text);
      if (isCut) {
        const quill = this.quill;
        quill.deleteText(range, Quill.sources.USER);
        quill.setSelection(range.index, Quill.sources.SILENT);
      }
    },
    triggerHelp() {
      this.isShowSymbol = false

      this.isShowHelp = !this.isShowHelp
    },
    triggerSymbol() {
      this.isShowHelp = false

      this.isShowSymbol = !this.isShowSymbol
    },
    insertSymbol(text) {
      const range = this.quill.getSelection(true)
      const index = range ? range.index : 999999
      this.quill.history.cutoff();
      this.quill.insertText(index, text, 'user');
      this.quill.setSelection(index + 1, 'user');
      this.quill.history.cutoff();
    },
    checkCaret() {
      // TODO 多个编辑器互相影响 初始位置错误
      setTimeout(() => {

        const innerBody = this.quill.container
        const selection = window.getSelection()
        const editorElBound = innerBody.getBoundingClientRect()

        if (selection) {
          const pointer = innerBody.querySelector('.editor-caret')
          const range = selection.getRangeAt(0),
            rect = range && range.getClientRects()[0];
          if (pointer && rect) {
            pointer.style.top = rect.bottom - editorElBound.top + innerBody.scrollTop + 'px'
            pointer.style.left = rect.left - editorElBound.left + 'px'
            // console.log(rect.bottom - editorElBound.top)
            // console.log(rect.right - editorElBound.left)
          }
        }
      }, 33);

      // this.$refs.CustomContextmenu.hide()

    },
    insertCommonSentence(text) {
      const range = this.quill.getSelection(true)
      const index = range ? range.index : 999999
      this.quill.history.cutoff();
      this.quill.insertText(index, text, 'user');
      this.quill.setSelection(index + text.length, 'user');
      this.quill.history.cutoff();
    },
    triggerCommonSentence(el) {
      if (this.isShowCommonSentList) {
        return
      }
      const rect = el.getBoundingClientRect()
      const onblur = function (_el) {
        setTimeout(() => {
          this.isShowCommonSentList = false
          this.$refs.CustomContextmenu.hide()
          _el.removeEventListener('blur', this._commonsentencebuttonblur)
        }, 160);
      }

      this._commonsentencebuttonblur = onblur.bind(this, el)
      el.addEventListener('blur', this._commonsentencebuttonblur)
      this.isShowCommonSentList = true

      this.$refs.CustomContextmenu.show({ top: rect.y + 22, left: rect.x + 20 })

      el.focus()

    },

    triggerBlueContextmenu(el) {
      if (this.isShowBlueList) {
        return
      }
      const rect = el.getBoundingClientRect()
      const onblur = function (_el) {
        setTimeout(() => {
          this.isShowBlueList = false
          this.$refs.BlueContextmenu.hide()
          _el.removeEventListener('blur', this._bluebuttonblur)
        }, 160);
      }

      this._bluebuttonblur = onblur.bind(this, el)
      el.addEventListener('blur', this._bluebuttonblur)
      this.isShowBlueList = true

      this.$refs.BlueContextmenu.show({ top: rect.y + 22, left: rect.x + 20 })

      el.focus()

    },

    insertBlue(item) {
      const range = this.quill.getSelection(true)
      const index = range ? range.index : 999999
      this.quill.history.cutoff();
      // this.quill.deleteText(index, length, Quill.sources.SILENT);
      this.quill.insertEmbed(index, 'mention', {
        text: item.sBookmarkName, pid: item.sBookmarkId
      });
      this.quill.setSelection(index + 1, Quill.sources.SILENT);
      this.quill.history.cutoff();
    },

    triggerBlueContextmenu2(el) {
      if (this.isShowBlueList2) {
        return
      }
      const rect = el.getBoundingClientRect()
      this.isShowBlueList2 = true
      this.selectedBlue = el
      const pid = el.getAttribute('pid')
      const bo = this.StructBookmarkList.find(item => item.sBookmarkId == pid)
      try {  
        this.StructBookmarkOptionList = JSON.parse(bo.sOptionListJson)
      } catch (error) {
        this.StructBookmarkOptionList = []
      }
      this.$refs.BlueContextmenu2.show({ top: rect.y + 26, left: rect.x })
    },

    insertBlue2(text) {
      const range = this.quill.getSelection(true)
      const index = range ? range.index : 999999
      if (this.selectedBlue && this.selectedBlue.remove) {
        this.isShowBlueList2 = false
        this.$refs.BlueContextmenu2.hide()
        this.selectedBlue.querySelector('span').innerText = text 
        this.selectedBlue = null
        this.quill.setSelection(index, Quill.sources.SILENT);
      }
    },


    handleCopyCut(isCut) {
      const range = this.quill.getSelection(true)
      if (!range) return;
      // const index = range ? range.index : 999999
      this.quill.history.cutoff();
      const text = this.quill.getText(range);
      // console.log(text)

      document.execCommand('copy', false, text)
      if (isCut) {
        this.quill.deleteText(range, Quill.sources.USER);
        this.quill.setSelection(range.index, Quill.sources.SILENT);
      }
      this.quill.history.cutoff();
    },
    handlePaste() {
      this.quill.history.cutoff();
      navigator.clipboard
        .readText()
        .then((text) => {
          // console.log(text)
          const range = this.quill.getSelection(true)
          if (range) {
            this.quill.deleteText(range, Quill.sources.USER);
            this.quill.setSelection(range.index, Quill.sources.SILENT);
          }
          const index = range.index
          this.quill.insertText(index, text, 'user');
          this.quill.setSelection(index + text.length, 'user');
          this.quill.history.cutoff();
        },
          () => {
            this.$message.error('请开启浏览器的剪贴板权限')
          }
        );
    },
    // 点击重置
    onClickResetConfig() {
      this.setDefaultData();
      this.setStorage();
      this.refreshToolBar();
    },
    onClickClose() {
      this.$el.click()
    },

    onPopAfterEnter() {
      this.rowDrop()
    },
    //行拖拽
    rowDrop() {
      const popTable = (this.$refs.popTableRef.$el)
      const tbody = popTable.querySelector('tbody')
      Sortable.create(tbody, {
        disabled: false, // 是否开启拖拽
        handle: ".drag-icon",
        // ghostClass: 'sortable-ghost', //拖拽样式
        animation: 150, // 拖拽延时，效果更好看
        group: { // 是否开启跨表拖拽
          pull: false,
          put: false
        },
        onEnd: ({ newIndex, oldIndex }) => {
          const currRow = this.react.tableData.splice(oldIndex, 1)[0]
          this.react.tableData.splice(newIndex, 0, currRow)
          this.refreshToolBar()
          this.$nextTick(() => {
            this.setStorage();
          });
        }
      })
    },
    onChangeItemProperty(scope, key) {
      const index = scope.$index;
      const table = this.react.tableData[index];
      table[key] = scope.row[key];

      this.setStorage();
      this.refreshToolBar()
    },
    setDefaultData() {
      // this.react.tableData = this.react.defaultConfigContent.map(this.dataObjConvert);
      this.react.tableData = cloneDeep(defaultStorageData.list)
    },
    // 获取本地缓存
    async getStorage() {
      if (!this.storageKey) return;
      let storageStr = localStorage.getItem('QuillEditor-' + this.storageKey);
      let storageObj = {
        ...defaultStorageData
      }

      if (this.iModuleId && this.storageKey) {
        await this.getOnlineConfig()
        Object.assign(storageObj,
          this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])
      } else {
        try {
          const parsed = JSON.parse(storageStr)
          if (Array.isArray(parsed)) {
            storageObj.list = parsed
          } else if (parsed && parsed.list) {
            storageObj = parsed
          }
        } catch (error) {
          console.error(error)
        }
      }

      let list = storageObj.list;

      const propList = list.map((i) => i.prop);
      const inputList = cloneDeep(defaultStorageData.list);
      let newList = []

      if (propList.length !== inputList.length) {
        // 如果表格加了新的一列数据，恢复成默认顺序 TODO: diff and append
        // console.log('new data - 恢复成默认顺序', inputList,propList)
        newList = inputList
      } else {
        // newList // 按localstor排序后的slot列表
        propList.forEach(prop => {
          const index = inputList.findIndex(slot => slot.prop === prop)
          if (index > -1) {
            newList.push(inputList.splice(index, 1)[0])
          }
        })
        newList = [...inputList].concat(newList)
      }



      // 将store存贮的属性值还原到当前数据
      let index
      let newTable = cloneDeep(newList).map((item) => {
        index = list.findIndex(target => item.prop === target.prop)
        if (index > -1) {
          return ({ ...item, ...list[index] })
        }
        return (item)
      });

      this.react.tableData = newTable

      return newTable

    },
    // 设置缓存
    setStorage(isGlobal = 0) {
      if (!this.storageKey) {
        this.setDefaultData()
        return
      };

      const storageObj = {
        list: this.react.tableData,
      }

      const storageString = JSON.stringify(storageObj)

      localStorage.setItem(
        "QuillEditor-" + this.storageKey,
        storageString
      );



      if (this.iModuleId) {
        const prom = this.saveOnlineConfig(storageObj, isGlobal)
        if (prom) {
          prom.then(() => {
            // 触发bus同步更新
            eventBus.publish("eventX", this.storageKey, this.iModuleId, this._.uid);
          })
        }
      } else {
        eventBus.publish("eventX", this.storageKey, this.iModuleId, this._.uid);
      }
    },

    refreshToolBar() {
      // 重新初始化toolbar
      if (!this.quill) return
      const _this = this.quill.theme
      const toolbarClass = this.quill.theme.modules.toolbar
      var container = document.createElement('div');
      _this.modules.toolbar.addControls(container, this.showingToolList)
      _this.modules.toolbar.container.remove()
      this.quill.container.parentNode.insertBefore(container, this.quill.container);
      toolbarClass.container = container;
      // console.log(toolbarClass.container.options)
      toolbarClass.container.classList.add('ql-toolbar');
      toolbarClass.controls = [];
      toolbarClass.handlers = {};
      Object.keys(toolbarClass.options.handlers).forEach(function (format) {
        toolbarClass.addHandler(format, toolbarClass.options.handlers[format]);
      });
      [].forEach.call(toolbarClass.container.querySelectorAll('button, select'), function (input) {
        toolbarClass.attach(input);
      });

      // icon
      this.quill.theme.extendToolbar(this.quill.theme.modules.toolbar)
    }
  },
}
// 替换非标签范围内的html 
function replaceTagInner(html, regex = '', replaceText = '') {
  let input = html
  const convert = (t) => {
    const after = t.replace(regex, replaceText)
    input = input.replace(t, after)
  }
  const start = html.match(/^()</)
  const end = html.match(/>()$/)
  const between = html.matchAll(/>(.*?)</g)
  if (start && start[0]) convert(start[0], start.index)
  if (end && end[0]) convert(end[0], end.index)
  Array.from(between, (result) => {
    if (result && result[0]) convert(result[0], result.index)
  })
  return input
}
</script>

<style lang="scss" scoped>
.quill-editor {
  position: relative;
  overflow: hidden;

  :deep(.ql-container) {
    overflow-y: auto;
  }

  &:deep(.ql-editor) {
    height: auto;
    min-height: 100%;
    overflow-y: hidden;
    font-family: "SimSun";
  }

  &:hover {
    .config-btn {
      visibility: visible;
    }
  }

  .config-btn {
    position: absolute;
    display: flex;
    visibility: hidden;
    align-items: center;
    justify-content: center;
    top: 4px;
    right: 4px;
    width: 18px;
    height: 18px;
    border: none;
    border-radius: 4px;
    color: #888;
    // background: var(--el-color-primary);
    // border-radius: 50%;
    overflow: hidden;
    z-index: 3;
    cursor: pointer;

    &:hover {
      background: #ccc;
      // filter: contrast(1.5);
      // transition: all ease .4s;
    }

    .setting-icon {
      position: relative;
      display: inline-block;
      width: auto;
      top: 0px;
      right: 0;
      font-size: 16px;
      color: var(--el-button-bg-color);
    }
  }
}

.popup-window {
  position: absolute;
  width: 50%;
  height: 260px;
  max-width: 640px;
  left: 0;
  right: 0;
  top: 60px;
  margin: auto;
  background: rgb(252, 252, 252);
  border-radius: 4px;
  box-shadow: 0 0 12px #aaa;
  z-index: 2;

  .header {
    position: relative;
    height: 30px;
    color: #333;
    font-size: 18px;
    font-weight: bold;
    margin: 5px 10px 5px 10px;
    border-bottom: 1px solid #ddd;

    .el-icon-close {
      position: absolute;
      right: 3px;
      top: 3px;
      cursor: pointer;
    }
  }

  .main {
    position: relative;
    height: 220px;
    padding: 0 10px;

    overflow: auto;

    .r {
      font-size: 16px;
      line-height: 1.8;
    }

    .symbol-box {
      display: inline-block;
      padding: 5px;
      cursor: pointer;

      &:hover {
        background: #7DC8EE;
      }
    }
  }
}


.quill-editor :deep(.editor-caret) {
  position: absolute;
  width: 12px;
  height: 4px;
  background: #00ff00;
  top: -99999px;
  left: -99999px;
  transform: translateX(-6px) translateY(1px);
}



.drag-icon {
  position: relative;
  display: inline-flex;
  min-height: 25px;
  align-items: center;
  justify-content: center;
  cursor: move;
  vertical-align: middle;
}

.ediui-ico {
  position: relative;
  display: inline-block;
  height: 20px;
  width: 20px;
  top: 5px;
  background-image: url(/img/editor/editor-icons.gif);
}

.ediui-text {
  position: relative;
  display: inline-block;
  height: 20px;
  text-indent: 5px;
  font-weight: 900;
}
</style>
