<template>

	<el-form-item v-if="!item.iIsHide" :prop="item.sProp" :class="formItemClass" :style="{
	        width: item.sWidth.includes('%') ? item.sWidth : ''
	    }" class="textarea-group">
		<div class="m-labelInput"
		:style="{
	            width: item.sWidth ? (item.sWidth.includes('%') ? '100%' : item.sWidth) : defaultV.width,
	            background: item.sBgColor ? item.sBgColor : defaultV.bg,
	            fontSize: item.sFontSize ? item.sFontSize : defaultV.fontSize,
	            fontWeight: item.sFontWeight ? 'bold' : defaultV.fontWeight,
	            color: item.sFontColor ? item.sFontColor : defaultV.fontColor }">
			<h5 :style="{
	            background: item.sLabelBgColor ? item.sLabelBgColor : defaultV.labelBgColor,
	            fontWeight: item.sLabelFontWeight ? 'bold' : defaultV.labelFontWeight,
	            fontSize: item.sLabelFontSize ? item.sLabelFontSize : defaultV.labelFontSize,
	            color: item.sLabelFontColor ? item.sLabelFontColor : defaultV.labelFontColor}">{{item.sLabel}}</h5>

            <el-input
            type="textarea" 
            :rows="item.sHeight ? Math.round(parseInt(item.sHeight) / 34) : 2" 
            v-model="bindData[item.sProp]" 
            :readonly="!!item.iReadonly"></el-input>

		</div>
	</el-form-item>
</template>
<script>
	export default {
		name: 'TextareaStyle',
		props: {
			item: { // 后端返回的自定义对象
				type: Object,
				default: () => ({})
			},
			bindData: { // 绑定对象
				type: Object,
				default: () => ({})
			},
			optionList: { // 绑定对象
				type: Object,
				default: () => ({})
			},
			formItemClass: {
				type: String,
				default: ''
			},
			currentItem: { // 当前项
				type: String,
				default: ''
			},
			verifyObjName: { // 验证对象名称
				type: String,
				default: 'rules'
			},
			defaultValue: {
				type: String,
				default: '',
			}

		},
		data() {
			return {
				defaultV: {
					width: '145px',
					height: '30px',
					bg: 'white',
					fontSize: '14px',
					fontColor: '#606266',
					labelFontColor: '#2c3e50',
                    labelFontSize: '14px',
                    labelBgColor: '#F4F8FB',
					fontWeight: '400',
					labelFontWeight: '700'
				}
			}
		},
		methods: {
			// 如果没有必填，创建必填
			createRequired() {
				// 是否有验证对象

				if (this.$parent[`${this.verifyObjName}`]) {
					// 是否存在字段
					let rulesField = this.$parent[`${this.verifyObjName}`][`${this.currentItem}`]
					if (rulesField) {
						let isExist = false
						for (const iterator of rulesField) {
							if (iterator.required) {
								isExist = true
								break;
							}
						}
						// 没有必填,添加必填
						if (!isExist) {
							rulesField.push({
								required: true,
								message: '必填'
							})
						}
					} else {
						// 不存在字段，初始化
						this.$parent[`${this.verifyObjName}`][`${this.currentItem}`] = [{
							required: true,
							message: '必填'
						}]
					}
				} else {
					console.log('没有验证对象')
				}
			},
			// 如果没有长度验证，创建长度验证
			createLimit() {
				// 是否有验证对象
				if (this.$parent[`${this.verifyObjName}`]) {
					// 是否存在字段
					let rulesField = this.$parent[`${this.verifyObjName}`][`${this.currentItem}`]
					if (rulesField) {
						let isExist = false
						for (const iterator of rulesField) {
							if (iterator.max) {
								isExist = true
								break;
							}
						}
						// 没有最大长度限制
						if (!isExist) {
							rulesField.push({
								max: Number(this.item.iLimitLength),
								message: `超过最大长度${this.item.iLimitLength}`
							})
						}
					} else {
						// 不存在字段，初始化
						this.$parent[`${this.verifyObjName}`][`${this.currentItem}`] = [{
							max: Number(this.item.iLimitLength),
							message: `超过最大长度${this.item.iLimitLength}`
						}]
					}
				} else {
					console.log('没有验证对象')
				}
			}
		},
		created() {
			// 后端传递的值有必填字段
			if (this.item.iRequired) {
				this.createRequired();
			}
			// 后端传递的值有长度验证
			if (this.item.iLimitLength) {
				this.createLimit();
			}

			if (this.item.sValue) {
				this.bindData[this.item.sProp] = this.item.sValue
				// this.$emit('setDefualt', this.item.sValue)
			}
			if(this.item.sInputType==='option' && !this.optionList[this.item.sOptionProp]) {
				this.optionList[this.item.sOptionProp] = [];
			}
		}
	}
</script>
<style lang="scss" scoped>
	.m-labelInfo {
		label {
			user-select:none;
		}
	}
	.el-form-item {
		margin-bottom: 0px;
		margin-right: 0px;
	}
	.m-labelInput{
        margin: 0px;
        h5{
            padding: 5px;
            border-top: 1px solid #eee;
            margin: 0px;
            font-weight: bold;
        }
        :deep(.el-textarea) {
			height: inherit;
			font-size: inherit;

			.el-textarea__inner {
				height: inherit;
				background: inherit;
			}
		}
	}

</style>
