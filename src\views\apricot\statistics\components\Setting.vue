<template>
    <el-dialog append-to-body title="配置" :modelValue="dialogVisible" fullscreen :close-on-click-modal="false" @open="initPageData" @close="closeDialog(true)"
        class="my-dialog t-default my-full-dialog">
        <div class="g-dialog-body m-flexLaout-tx">
            <div class="c-item t-1 m-flexLaout-ty">
                <div class="i-item t-1 p-2">
                    <el-button-icon-fa type="primary" plain _icon="fa fa-plus-o" @click="onAddDirectory">新增</el-button-icon-fa>
                    <el-button-icon-fa plain _icon="fa fa-reset-1" @click="getTreeData">刷新</el-button-icon-fa>
                </div>
                <div class="i-item t-2 g-flexChild">
                    <!--draggable
                        :allow-drop="allowDrop"
                        @node-drop="handleNodeDrop" -->
                    <el-tree :data="treeData" :props="defaultProps" ref="treeRef" node-key="sId" default-expand-all highlight-current :expand-on-click-node="false"
                        @node-click="handleNodeClick" v-loading="loading">
                        <template v-slot="{ node, data }">
                            <span class="custom-tree-node" style="flex:1;">
                                <div v-if="data.sItemId" @mouseenter="() => onMouseEnter(data)" @mouseleave="() => onMouseLeave(data)">
                                    <span>{{ node.label }}</span>
                                    <!--  v-if="data.isShowBtn" -->
                                    <span style="margin-right:30px;">
                                        <el-button-icon-fa link _icon="fa fa-delete" @click.stop="() => onDeleteNode(data)">
                                        </el-button-icon-fa>
                                    </span>
                                </div>
                                <div v-else @mouseenter="() => onMouseEnter(data)" @mouseleave="() => onMouseLeave(data)">
                                    <span>{{ node.label }}</span>
                                    <!-- v-if="data.isShowBtn" -->
                                    <span style="margin-right:30px">
                                        <el-button-icon-fa link _icon="fa fa-plus-o" @click.stop="() => onAddNode(2, data)">
                                        </el-button-icon-fa>
                                        <el-button-icon-fa link _icon="fa fa-edit-3" @click.stop="() => onEditNode(data)">
                                        </el-button-icon-fa>
                                        <el-button-icon-fa link _icon="fa fa-delete" @click="() => onDeleteNode(data)">
                                        </el-button-icon-fa>
                                    </span>
                                </div>
                            </span>
                        </template>
                    </el-tree>
                </div>
            </div>
            <div class="c-item g-flexChild m-flexLaout-ty">
                <p class="c-title" v-html="title"></p>
                <div class="c-input">
                    <el-form ref="refEditLayer" :model="form" label-position="right" inline :disabled="nodeType === 1">
                        <el-form-item prop="sItemName" label="报表名称">
                            <el-input v-model="form.sItemName" clearable style="width: 200px;"></el-input>
                        </el-form-item>
                        <el-form-item prop="sItemType" label="报表类型">
                            <el-select v-model="form.sItemType" clearable style="width: 200px;">
                                <el-option v-for="(item, index) in optionsLoc.typeOptions" :key="index" :label="item.sName" :value="item.sValue">
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item v-show="this.form.sItemType === 1" prop="sNeedTotal" label=" ">
                            <el-checkbox v-model="form.sNeedTotal" :true-label="1" :false-label="0" :disabled="nodeType === 1">包含总计项</el-checkbox>
                        </el-form-item>
                        <!-- <el-form-item v-show="this.form.sItemType === 1"
                            prop="sNeedProportion"
                            label=" ">
                            <el-checkbox v-model="form.sNeedProportion"
                                :true-label="1"
                                :false-label="0"
                                :disabled="nodeType === 1">包含比例计算</el-checkbox>
                        </el-form-item> -->
                    </el-form>
                </div>
                <div class="c-set g-flexChild m-flexLaout-ty">
                    <div class="i-item m-flexLaout-ty u-autoHeight" v-if="form.sItemType != 2">
                        <div class="i-title">
                            <el-checkbox :indeterminate="isGroupIndeterminate" v-model="isCheckAllGroup" class="t-1" @change="handleCheckGroupAllChange"
                                :disabled="nodeType === 1">分组项</el-checkbox>
                        </div>
                        <div class="g-flexChild i">
                            <el-checkbox-group v-model="checkedGroupOptions" @change="handleCheckedGroupOptionsChange" :disabled="nodeType === 1">
                                <el-checkbox v-for="item in groupFields.sort((a,b) => a.sFieldDesc.localeCompare(b.sFieldDesc))" :label="item.sFieldName" :key="item.sFieldName">{{
                                    item.sFieldDesc }}</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>

                    <div class="i-item m-flexLaout-ty u-autoHeight" v-if="form.sItemType != 2">
                        <div class="i-title">
                            <el-checkbox :indeterminate="isTotalIndeterminate" v-model="isCheckAllTotal" class="t-1" @change="handleCheckTotalAllChange"
                                :disabled="nodeType === 1">求和项</el-checkbox>
                        </div>
                        <div class="g-flexChild u-autoHeight">
                            <el-checkbox-group v-model="checkedTotalOptions" @change="handleCheckedTotalOptionsChange" :disabled="nodeType === 1">
                                <el-checkbox v-for="item in totalFields.sort((a,b) => a.sFieldDesc.localeCompare(b.sFieldDesc))" :label="item.sFieldName" :key="item.sFieldName">{{
                                    item.sFieldDesc }}</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>

                    <div class="i-item m-flexLaout-ty u-autoHeight" v-if="form.sItemType == 2">
                        <div class="i-title">
                            <el-checkbox :indeterminate="isDisplayIndeterminate" v-model="isCheckAllDisplay" class="t-1" @change="handleCheckDisplayAllChange"
                                :disabled="nodeType === 1">显示项</el-checkbox>
                        </div>
                        <div class="g-flexChild u-autoHeight">
                            <el-checkbox-group v-model="checkedDisplayOptions" @change="handleCheckedDisplayOptionsChange" :disabled="nodeType === 1">
                                <el-checkbox v-for="item in displayFields.sort((a,b) => a.sFieldDesc.localeCompare(b.sFieldDesc))" :label="item.sFieldName"
                                    :key="item.sFieldName">{{ item.sFieldDesc }}</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa :disabled="nodeType === 1" type="primary" _icon="fa fa-save" @click="onSaveNode">保存</el-button-icon-fa>
            <el-button-icon-fa _icon="fa fa-close-1" @click="closeDialog(false)">关 闭</el-button-icon-fa>
        </template>
    </el-dialog>
    <el-dialog append-to-body v-model="dialog.visible" :title="directoryForm.sCategoryId?'修改目录':'添加目录'" :close-on-click-modal="false" class="my-dialog t-default"
        modal-class="my-modal">
        <el-form ref="refForm" :model="directoryForm" labelWidth="90px" label-suffix="：" style="margin: 50px 0">
            <el-form-item prop="publish" label="报表名称">
                <el-input v-model="directoryForm.sCategoryName" style="width: 100%;"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button-icon-fa type="primary" _icon="fa fa-save" @click="onSaveDirector">保存</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import Api from '$supersetApi/projects/apricot/statistics/index.js'
export default {
    name: 'Setting',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        FieldsObject: {
            type: Object,
            default: () => ({})
        },

    },
    emits: ['closeDialog'],
    data () {
        return {
            treeData: [],
            defaultProps: {
                children: 'items',
                label: 'sName'
            },
            form: {
                sItemType: 1,
                sNeedTotal: 0
            },
            optionsLoc: {
                typeOptions: [{
                    sName: '分组统计',
                    sValue: 1
                }, {
                    sName: '按列查询',
                    sValue: 2
                }]
            },
            selectedNode: {},
            nodeType: 1,

            isGroupIndeterminate: false,
            isCheckAllGroup: false,
            checkedGroupOptions: [],
            groupFields: [],

            isTotalIndeterminate: false,
            isCheckAllTotal: false,
            checkedTotalOptions: [],
            totalFields: [],

            isDisplayIndeterminate: false,
            isCheckAllDisplay: false,
            checkedDisplayOptions: [],
            displayFields: [],
            title: '--',
            loading: false,
            dialog: {
                visible: false
            },
            directoryForm: {}

        }
    },
    methods: {
        initPageData () {
            this.getTreeData();
            this.findAllStatsFields();
            this.selectedNode = {};
            this.title = '--';
            this.initFormData()
        },
        closeDialog (bool) {
            this.$emit('closeDialog', bool)
        },
        // 拖拽时判定目标节点能否被放置
        // 'prev'、'inner' 和 'next'，分前、插入、后
        allowDrop (draggingNode, dropNode, type) {
            // console.log(draggingNode, dropNode, type);
            if (draggingNode.data.level === dropNode.data.level) {
                if (draggingNode.data.sParentId === dropNode.data.sParentId) {
                    return type === "prev" || type === "next";
                }
                else {
                    return false;
                }
            } else {
                // 不同级进行处理
                return false;
            }
        },
        // tree拖拽成功完成时触发的事件
        handleNodeDrop (draggingNode, dropNode, dropType, ev) {
            const childNodes = dropNode.parent.childNodes;
            const isCategoryNode = dropNode.data.sItemId ? false : true;
            // console.log(draggingNode, dropNode, dropType, ev)
            var sortList = [];
            // console.log()
            if (isCategoryNode) {
                childNodes.map((item, index) => {
                    sortList.push({ iSort: index, sCategoryId: item.data.sCategoryId })
                })
            } else {
                childNodes.map((item, index) => {
                    sortList.push({ iSort: index, sItemId: item.data.sItemId })
                });
            }
            // console.log(sortList);
            let currenFn = isCategoryNode ? Api.updateSortStatsCategory : Api.updateSortStatsItem;
            currenFn(sortList).then(res => {
                if (res) {
                    this.getTreeData();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
                this.getTreeData();
            })
        },
        onAddDirectory () {
            this.directoryForm = {
                sCategoryName: ''
            };
            this.dialog.visible = true;
        },
        onEditNode (data) {
            this.directoryForm = {
                sCategoryId: data.sCategoryId,
                sCategoryName: data.sCategoryName,
                iVersion: data.ciVersion,
            };
            this.dialog.visible = true;
        },
        onSaveDirector () {
            // 修改一级节点
            if (this.directoryForm.sCategoryId) {
                let jsonData = { ...this.directoryForm };
                Api.editCategory(jsonData).then(res => {
                    if (res.success) {
                        // this.directoryForm.ciVersion += 1;
                        this.$message.success('修改成功');
                        this.getTreeData();
                        this.dialog.visible = false
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    console.log(err)
                });
                return
            }
            // 新增一级节点
            let jsonData = {
                sCategoryName: this.directoryForm.sCategoryName
            };
            Api.addCategory(jsonData).then(res => {
                if (res.success) {
                    this.$message.success('保存成功');
                    this.getTreeData();
                    this.dialog.visible = false
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            });
            return
        },
        handleNodeClick (node) {
            this.selectedNode = node;  // 节点赋值
            if (node.sItemId) {
                // 选中二级节点
                this.nodeType = 2;
                this.title = `编辑 <span style="color: var(--el-color-primary); font-weight:bold">${node.sItemName}</span> 节点`
                this.findStatsItemById(node);  // 获取节点数据
            } else {
                // // 选中一级节点
                this.nodeType = 1;
                // this.title = `编辑 <span style="color: var(--el-color-primary); font-weight:bold">${node.sCategoryName}</span> 节点`
                // this.initFormData();
                // this.form['sCategoryId'] = node.sCategoryId;
                // this.form['sItemName'] = node.sCategoryName;
                // this.form['ciVersion'] = node.ciVersion;
            }
        },
        findStatsItemById (data) {
            Api.findStatsItemById({ sItemId: data.sItemId }).then(res => {
                if (res.success) {
                    this.form = res.data;
                    this.form.sItemId = data.sItemId;
                    this.checkedGroupOptions = data.sGroupFields ? data.sGroupFields.split(',') : [];
                    this.checkedTotalOptions = data.sTotalFields ? data.sTotalFields.split(',') : [];
                    this.checkedDisplayOptions = data.sDisplayFields ? data.sDisplayFields.split(',') : [];
                    this.handleCheckedGroupOptionsChange(this.checkedGroupOptions);
                    this.handleCheckedTotalOptionsChange(this.checkedTotalOptions);
                    this.handleCheckedDisplayOptionsChange(this.checkedDisplayOptions);
                    return
                }
            }).catch(err => {
                console.log(err);
                this.form = Object.assign({}, data);
                this.checkedGroupOptions = data.sGroupFields ? data.sGroupFields.split(',') : [];
                this.checkedTotalOptions = data.sTotalFields ? data.sTotalFields.split(',') : [];
                this.checkedDisplayOptions = data.sDisplayFields ? data.sDisplayFields.split(',') : [];
                this.handleCheckedGroupOptionsChange(this.checkedGroupOptions);
                this.handleCheckedTotalOptionsChange(this.checkedTotalOptions);
                this.handleCheckedDisplayOptionsChange(this.checkedDisplayOptions);
            });
        },
        onMouseEnter (data) {
            data['isShowBtn'] = true;
        },
        onMouseLeave (data) {
            data['isShowBtn'] = false;
        },
        onAddNode (val, data) {
            this.nodeType = val;
            this.initFormData();
            this.title = `新增一级节点`
            if (val === 2) {
                this.title = `在 <span style="color: var(--el-color-primary); font-weight:bold">${data.sCategoryName}</span> 节点下新增子节点`
                this.selectedNode = data;
            }
        },
        initFormData () {
            this.form = {};
            if (this.nodeType === 2) {
                this.form['sItemType'] = 1;
                this.form['sNeedTotal'] = 0;
            }
            this.isGroupIndeterminate = false;
            this.isCheckAllGroup = false;
            this.checkedGroupOptions = [];
            this.isTotalIndeterminate = false;
            this.isCheckAllTotal = false;
            this.checkedTotalOptions = [];
            this.isDisplayIndeterminate = false;
            this.isCheckAllDisplay = false;
            this.checkedDisplayOptions = [];
        },
        // 分组项
        handleCheckGroupAllChange (val) {
            this.checkedGroupOptions = val ? this.groupFields.map(item => item.sFieldName) : [];
            this.isGroupIndeterminate = false;
        },
        handleCheckedGroupOptionsChange (value) {
            let checkedCount = value.length;
            this.isCheckAllGroup = checkedCount === this.groupFields.length;
            this.isGroupIndeterminate = checkedCount > 0 && checkedCount < this.groupFields.length;
        },
        // 统计项
        handleCheckTotalAllChange (val) {
            this.checkedTotalOptions = val ? this.totalFields.map(item => item.sFieldName) : [];
            this.isTotalIndeterminate = false;
        },
        handleCheckedTotalOptionsChange (value) {
            let checkedCount = value.length;
            this.isCheckAllTotal = checkedCount === this.totalFields.length;
            this.isTotalIndeterminate = checkedCount > 0 && checkedCount < this.totalFields.length;
        },
        // 显示项
        handleCheckDisplayAllChange (val) {
            this.checkedDisplayOptions = val ? this.displayFields.map(item => item.sFieldName) : [];
            this.isDisplayIndeterminate = false;
        },
        handleCheckedDisplayOptionsChange (value) {
            let checkedCount = value.length;
            this.isCheckAllDisplay = checkedCount === this.displayFields.length;
            this.isDisplayIndeterminate = checkedCount > 0 && checkedCount < this.displayFields.length;
        },
        onSaveNode () {
            if (!this.selectedNode.sCategoryId) {
                this.$message.warning('请选择左侧树节点！');
                return
            }
            if (!this.form.sItemName) {
                this.$message.warning('请填写报表名称！');
                return
            }
            if (this.nodeType == 2) {
                if (this.form.sItemType === 2 && this.checkedDisplayOptions.length === 0) {
                    this.$message.warning('至少选择一个显示项！');
                    return
                }
                if (this.form.sItemType === 1 && this.checkedGroupOptions.length === 0) {
                    this.$message.warning('至少选择一个分组项！');
                    return
                }
                if (this.form.sItemType === 1 && this.checkedTotalOptions.length === 0) {
                    this.$message.warning('至少选择一个求和项！');
                    return
                }
                // 修改二级节点
                if (this.selectedNode.sItemId) {
                    let jsonData = Object.assign({}, this.form);
                    jsonData.sGroupFields = this.checkedGroupOptions.join(',');
                    jsonData.sTotalFields = this.checkedTotalOptions.join(',');
                    jsonData.sDisplayFields = this.checkedDisplayOptions.join(',');
                    jsonData.sCategoryId = this.selectedNode.sCategoryId;
                    jsonData.sCategoryName = this.selectedNode.sCategoryName;
                    Api.editStatsItem(jsonData).then(res => {
                        if (res.success) {
                            this.form.iVersion += 1;
                            this.title = `编辑<span>${this.form.sItemName}</span>节点`
                            this.$message.success('修改成功');
                            this.getTreeData();
                            return
                        }
                        this.$message.error(res.msg)
                    }).catch(err => {
                        console.log(err)
                    });

                    return
                }
                // 新增二级节点
                let jsonData = Object.assign({}, this.form);
                jsonData.sDisplayFields = this.checkedDisplayOptions.join(',');
                jsonData.sGroupFields = this.checkedGroupOptions.join(',');
                jsonData.sTotalFields = this.checkedTotalOptions.join(',');
                jsonData.sCategoryId = this.selectedNode.sCategoryId;
                jsonData.sCategoryName = this.selectedNode.sCategoryName;
                Api.addStatsItem(jsonData).then(res => {
                    if (res.success) {
                        this.$message.success('保存成功');
                        this.getTreeData();
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    console.log(err)
                });
                return
            }
        },
        onDeleteNode (data) {
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                if (data.sItemId) {
                    let jsonData = {
                        sItemId: data.sItemId,
                        iVersion: data.iVersion
                    }
                    Api.delStatsItem(jsonData).then(res => {
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.getTreeData();
                            if (data.sItemId === this.selectedNode.sItemId) {
                                this.title = '--';
                                this.selectedNode = {};
                                this.form = this.$options.data().form;
                                this.initFormData()
                            }
                            return
                        }
                        this.$message.error(res.msg)
                    }).catch(err => {
                        console.log(err)
                    })
                    return
                }
                let jsonData = {
                    sCategoryId: data.sCategoryId,
                    iVersion: data.ciVersion
                }
                Api.delCategory(jsonData).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.getTreeData();
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    console.log(err)
                })
            })
        },
        // 获取所有字段
        findAllStatsFields () {
            if (Object.keys(this.FieldsObject).keys) {
                this.displayFields = this.FieldsObject.displayFields ? this.FieldsObject.displayFields : [];
                this.groupFields = this.FieldsObject.groupFields ? this.FieldsObject.groupFields : [];
                this.totalFields = this.FieldsObject.totalFields ? this.FieldsObject.totalFields : [];
                return
            }
            Api.findAllStatsFields().then(res => {
                if (res.success) {
                    this.displayFields = res.data && res.data.displayFields ? res.data.displayFields : [];
                    this.groupFields = res.data && res.data.groupFields ? res.data.groupFields : [];
                    this.totalFields = res.data && res.data.totalFields ? res.data.totalFields : [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 获取树数据
        getTreeData () {
            this.loading = true
            Api.getTreeData().then(res => {
                this.loading = false;
                if (res.success) {
                    this.treeData = res.data || [];
                    this.setTreeDataNodeKeys(this.treeData);
                    // console.log(this.treeData);
                    this.$nextTick(() => {
                        if (this.treeData.length && Object.keys(this.selectedNode).length) {
                            this.$refs.treeRef.setCurrentKey(this.selectedNode.sId);
                        }
                    })
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
                this.loading = false
            })
        },
        setTreeDataNodeKeys (data) {
            if (!data.length) {
                return
            }
            // 构建sId, sName属性
            data.forEach(item => {
                if (item.sItemId) {
                    item.sId = item.sItemId;
                    item.sName = item.sItemName;
                    item.sParentId = item.sCategoryName
                }
                if (!item.sId) {
                    item.sId = item.sCategoryId;
                    item.sName = item.sCategoryName;
                    item.sParentId = '-'
                }
                if (item.items) {
                    this.setTreeDataNodeKeys(item.items);
                }
            })
        },
    },
    mounted () {
    }
}
</script>
<style lang="scss" scoped>
:global(.m-set-dialog .el-dialog__body) {
    height: calc(100% - 100px);
}

.g-dialog-body {
    .custom-tree-node {
        flex: 1;

        > div {
            display: flex;
            align-items: center;
            justify-content: space-between;

            > span:first-child {
                display: inline-block;
                max-width: 170px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    :deep(.el-tree .el-tree-node__content) {
        height: 35px;
    }

    .c-item {
        &.t-1 {
            width: 320px;
            border-right: 1px solid #eee;

            .i-item.t-1 {
                margin-bottom: 5px;
            }

            .i-item.t-2 {
                overflow: auto;
            }
        }
        .c-title {
            margin-left: 15px;
            font-size: 16px;
            padding: 10px 0;
        }

        .c-input {
            margin-left: 15px;
        }

        .c-set {
            padding-top: 5px;
            padding-left: 15px;

            .i-item {
                width: 100%;
                margin-bottom: 15px;
                // margin-right: 15px;
                border: 1px solid #eee;
                box-sizing: border-box;
                :deep(.el-checkbox__input) {
                    top: 5px;
                }
            }
            .i-title {
                background-color: var(--el-color-primary-light-9);
            }
            .el-checkbox {
                // display: flex;
                display: inline-block;
                width: 100px;
                padding-left: 15px;
                margin-top: 5px;
                &.t-1 {
                    height: 30px;
                    line-height: 1.5;
                    padding-top: 5px;
                    padding-bottom: 5px;
                    margin-top: 0;
                }
            }

            .el-checkbox-group {
                margin: 15px 0;
            }
        }
    }
}
.i-item.t-2 {
    :deep(.el-button [class*='el-icon'] + span) {
        margin: 0;
    }
}
</style>
