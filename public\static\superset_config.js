(function () {
    window.configs = {};

    configs.urls = {

        system: window.location.origin + '/auth', //  系统（登陆类等）30:8004
        // 头像上传、显示地址
        // photoDownUrl: 'https://*************:9002/facecollect/https/img',      // 图片基础 url
        photoDownUrl: window.location.origin + '/report/facecollect/show', // 图片基础 url
        photoUpUrl: 'http://localhost:18153/app/index.html', // 点击拍摄按钮 url 地址

        // 调用本地服务
        apricotAssist: 'http://localhost:18153', // 报告通过接口方式调用本地服务

        // 2020-8-10 版报告系统
        // 新版报告系统
        // apricot: 'http://*************:19002/report',
        apricot: window.location.origin + '/report',
        image: window.location.origin + '/image',
        broken: window.location.origin + '/broken',
        webReadUrl: window.location.origin + '/app/webdicom/#/', // web阅图
        webReBuildUrl: window.location.origin + '/app/webdicom/#/', // web重建
        OCR: window.location.protocol + '//' + window.location.hostname + ':8151', // 图文识别
        downfileUrl: window.location.origin + '/report/download/downfile', // 下载的路径
    }

    configs.TokenInvalidTime = 3 * 60 * 1000; // token(令牌)失效时间 

    configs.names = {
        systemName: '医学影像与信息管理系统',
        systemNameEN: 'Medical Imaging and Information Management System'
    }

    // 业务参数
    configs.businessParams = {
        isAgeComputeBirthday: 1, // 按年龄计算生日 1：计算； 0：不计算
        enableTimingSave: false, //启用定时自动保存报告 1：启用；0：不启用
        timedSaveSenconds: 3, // 定时保存报告时长,单位分钟
    }


    configs.colors = {
            elementConfig: [
                "#f8ecec",
                "#edf8ec",
                "#ecf8f2",
                "#ecf8f7",
                "#ecf2f8",
                "#416cc3",
                "#41c38a",
                "#df9d29",
                "#df6060",
                "#44b1d9"
            ]
    },

    //业务数据
    configs.businessData = {}
    
    // 新版病理类型
    configs.businessData.pathologyNew = {
        type: "select",
        dataName: 'pathology',
        option: [{
                pid: '',
                id: 1,
                sName: '甲状腺左叶',
                sValue: false,
                type: 'checkbox',
                children: [{
                        pid: 1,
                        id: 11,
                        type: 'checkbox',
                        sValue: false,
                        sName: '乳头状癌'
                    },
                    {
                        pid: 1,
                        id: 12,
                        type: 'checkbox',
                        sValue: false,
                        sName: '滤泡状癌'
                    },
                    {
                        pid: 1,
                        id: 13,
                        type: 'checkbox',
                        sValue: false,
                        sName: '微小癌',
                        children: [{
                            pid: 13,
                            id: 131,
                            type: 'radio',
                            sValue: null,
                            option: [{
                                    sValue: 1311,
                                    sName: '单发',
                                },
                                {
                                    sValue: 1312,
                                    sName: '多发'
                                }
                            ]
                        }]
                    },
                    {
                        pid: 1,
                        id: 14,
                        type: 'checkbox',
                        sValue: false,
                        sName: '结节性甲状腺肿',
                    },
                    {
                        pid: 1,
                        id: 15,
                        type: 'checkbox',
                        sValue: false,
                        sName: '淋巴细胞性甲状腺炎',
                    },
                    {
                        pid: 1,
                        id: 16,
                        type: 'checkbox',
                        sValue: false,
                        sName: '其他',
                        child: {
                            type: 'text',
                            sName: '',
                            sValue: '',
                        }
                    }
                ]
            },
            {
                pid: '',
                id: 2,
                sName: '甲状腺右叶',
                sValue: false,
                type: 'checkbox',
                children: [{
                        pid: 2,
                        id: 21,
                        type: 'checkbox',
                        sValue: false,
                        sName: '乳头状癌'
                    },
                    {
                        pid: 2,
                        id: 22,
                        type: 'checkbox',
                        sValue: false,
                        sName: '滤泡状癌'
                    },
                    {
                        pid: 2,
                        id: 23,
                        type: 'checkbox',
                        sValue: false,
                        sName: '微小癌',
                        children: [{
                            pid: 23,
                            id: 231,
                            type: 'radio',
                            sValue: null,
                            option: [{
                                    sValue: 2311,
                                    sName: '单发',
                                },
                                {
                                    sValue: 2312,
                                    sName: '多发'
                                }
                            ]
                        }]
                    },
                    {
                        pid: 2,
                        id: 24,
                        type: 'checkbox',
                        sValue: false,
                        sName: '结节性甲状腺肿',
                    },
                    {
                        pid: 2,
                        id: 25,
                        type: 'checkbox',
                        sValue: false,
                        sName: '淋巴细胞性甲状腺炎',
                    },
                    {
                        pid: 2,
                        id: 26,
                        type: 'checkbox',
                        sValue: false,
                        sName: '其他',
                        child: {
                            type: 'text',
                            sName: '',
                            sValue: '',
                        }
                    }
                ]
            },
            {
                pid: '',
                id: 3,
                sName: '浸润或转移部位',
                sValue: false,
                type: 'checkbox',
                children: [{
                        pid: 3,
                        id: 31,
                        type: 'checkbox',
                        sValue: false,
                        sName: '乳头状癌',
                    },
                    {
                        pid: 3,
                        id: 32,
                        type: 'checkbox',
                        sValue: false,
                        sName: '滤泡状癌',
                    },
                    {
                        pid: 3,
                        id: 33,
                        type: 'checkbox',
                        sValue: false,
                        sName: '微小癌',
                        children: [{
                            pid: 33,
                            id: 331,
                            type: 'radio',
                            sValue: null,
                            option: [{
                                    sValue: 3311,
                                    sName: '单发',
                                },
                                {
                                    sValue: 3312,
                                    sName: '多发'
                                }
                            ]
                        }]
                    },
                    {
                        pid: 3,
                        id: 34,
                        type: 'checkbox',
                        sValue: false,
                        sName: '其他',
                        child: {
                            type: 'text',
                            sName: '',
                            sValue: '',
                        }
                    }
                ]
            },
            {
                pid: '',
                id: 4,
                sName: '转移淋巴结数',
                sValue: false,
                type: 'checkbox',
                children: [{
                        pid: 4,
                        id: 41,
                        type: 'radio',
                        sValue: null,
                        option: [{
                                sValue: 411,
                                sName: '左颈',
                            },
                            {
                                sValue: 412,
                                sName: '右颈'
                            },
                            {
                                sValue: 413,
                                sName: '其他部位淋巴结'
                            }
                        ]
                    },
                    {
                        pid: 4,
                        id: 42,
                        type: 'checkbox',
                        sValue: false,
                        sName: '其他',
                        child: {
                            type: 'text',
                            sName: '',
                            sValue: '',
                        }
                    }
                ]
            }
        ]
    }
})()
