import request, { stringify, baseURL } from '$supersetUtils/request'

/*登录---------------------------------*/
export function login(data) {
    return request({
        url: baseURL.system + '/login/in',
        method: 'post',
        transformRequest: stringify,
        data
    })
}
/*---------------------------------登录*/
/*退出登录------------------------------*/
export function logout(data) {
    return request({
        baseURL: baseURL.system,
        url: '/login/out',
        method: 'post',
        transformRequest: stringify,
        data
    })
}
/*------------------------------退出登录*/
/*获取用户信息---------------------------*/
export function getUserInfo(data) {
    return request({
        baseURL: baseURL.system,
        url: '/login/user/find/id', // '/login/user/find/key',
        method: 'post',
        transformRequest: stringify,
        data
    })
}

/*---------------------------获取用户信息*/
/*设置密码---------------------------*/
export function setPass(params) {
    return request({
        baseURL: baseURL.system,
        url: '/login/user/password/edit',
        method: 'post',
        transformRequest: stringify,
        params
    })
}

/*---------------------------设置密码*/

/*是否需要强制修改密码--------------------*/
export function getPwdRobustCheckParam (params) {
    return request({
        baseURL: baseURL.system,
        url: '/login/getPwdRobustCheckParam',
        method: 'post',
        data: stringify(params)
    })
}

/*--------------------是否需要强制修改密码*/

/*检查登录状态---------------------------*/
export function loginCheck(token) {
    return request({
        baseURL: baseURL.system,
        url: '/login/check',
        method: 'post',
        transformRequest: stringify,
        params: {
            token
        }
    })
}
/*---------------------------检查登录状态*/

/*获取用户权限---------------------------*/
export function getUserRight(data) {
    return request({
        baseURL: baseURL.system,
        url: '/user/and/right/find/urls', // '/right/manage/find/urls',
        method: 'post',
        transformRequest: stringify,
        data
    })
}
/*---------------------------获取用户权限*/

// 通过令牌获取登录信息
export function getCurrentInfo(params) {
    return request({
        url: baseURL.system + '/login/info/current',
        method: 'post',
        params
    })
}

// 维持登录的心跳操作
export function loginHeartbeat(params) {
    return request({
        url: baseURL.system + '/login/heartbeat',
        method: 'post',
        params
    })
}

// 字典 ( 下拉的数据 )
export function dictionaryGroup(params) {
    return request({
        url: baseURL.apricot + '/setting/dict/getDictById',
        method: 'POST',
        params
    })
}

// 测试连接
export function testLink() {
    return request({
        url: baseURL.apricotAssist + '/client/testLink',
        method: 'get'
    })
}
// 获取客户端IP
export function queryClientIp() {
    return request({
        url: baseURL.apricot + '/system/queryClientIp',
        method: 'post'
    })
}

// 获取登陆二维码// 获取二维码 
export function getLoginQRCode(data) {
    return request({
        url: baseURL.broken + '/sign/qrcode',
        method: 'post',
        data
    })
}

// 获取扫码结果
export function getScanResult(data) {
    return request({
        url: baseURL.broken + '/sign/qrcode/status',
        method: 'post',
        data
    })
}
// 二维码登陆接口
export function qrcodeLogin(params) {
    return request({
        url: baseURL.broken + '/login/qrcode',
        method: 'post',
        params 
    })
}

// 获取远程服务器上的证书信息
export function guangXiQuRenMinUKeyInit(data) {
    return request({
        url: baseURL.broken + '/guangxiqurenmin/ukey/init',
        method: 'post',
        data 
    })
}
// UKEY 登录系统
export function guangXiQuRenMinUKeyLogin(data) {
    return request({
        url: baseURL.broken + '/guangxiqurenmin/ukey/login',
        method: 'post',
        data 
    })
}



