export default {
    table: [{
            sProp: 'sName',
            sLabel: '姓名',
            sAlign: 'center',
            sMinWidth: '120px'
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sWidth: '120px'
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sWidth: '120px'
        },
        {
            sProp: 'sSourceText',
            sLabel: '来源',
            sAlign: 'center',
            sMinWidth: '120px'
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sAlign: 'center',
            sMinWidth: '120px'
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '检查日期',
            sAlign: 'center',
            sMinWidth: '120px'
        },
        {
            sProp: 'sRoomText',
            sLabel: '检查类型',
            sAlign: 'center',
            sMinWidth: '120px'
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sAlign: 'center',
            sMinWidth: '120px'
        },
        {
            sProp: 'sPositionText',
            sLabel: '部位',
            sAlign: 'center',
            sMinWidth: '120px'
        },
    ],
    DA1: {
        type: 't-x',
        localStorageKey: '201911261003',
        panelConfig: [{
                size: 0,
                minSize: 100,
                name: "c1",
                isFlexible: true
            },
            {
                size: 700,
                minSize: 300,
                maxSize: 1000,
                name: "c2",
                isFlexible: false
            }
        ]
    }
}