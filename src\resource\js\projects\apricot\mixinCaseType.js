
//  混入 病例类型
import {
    caseTypeTree
} from '$supersetApi/projects/apricot/system/caseType.js'
const mixinCaseType = {
    methods: {
        setConditionCaseTypeIds(val) {
            if (Object.prototype.toString.call(this.condition.recordTeach) == "[object Array]") {
                this.condition.sRecord = val.reduce((str, item) => str ? str + '/' + item : item, '');
                // this.condition.sTeach = val[val.length - 1];
                // let checkedNodes = this.$refs.myCascader[0].getCheckedNodes()[0];
                // this.condition.sRecordText = checkedNodes.pathLabels.reduce((str, item) => str ? str + '/' + item : item, '')
                // this.condition.sTeachText = checkedNodes.pathLabels[checkedNodes.pathLabels.length - 1];
            }
            // 关闭下拉选项
            this.$refs.myCascader1.togglePopperVisible()
            this.mxDoSearch()
        },
        setFormCaseTypeIds(val) {
            if (Object.prototype.toString.call(this.form.recordTeach) == "[object Array]") {
                let len = this.form.recordTeach.length;
                this.form.sRecord = len ? (val.reduce((str, item) => str ? str + '/' + item : item, '')) : '';
                this.form.sTeach = len ? val[val.length - 1] : '';
                let checkedNodes = this.$refs.myCascader[0].getCheckedNodes()[0];
                this.form.sRecordText = len ? (checkedNodes.pathLabels.reduce((str, item) => str ? str + '/' + item : item, '')) : '';
                this.form.sTeachText = len ? (checkedNodes.pathLabels[checkedNodes.pathLabels.length - 1]) : '';
            }
        },
        mxCaseTypeTree() {
            caseTypeTree().then(res => {
                if (res.success) {
                    this.optionsLoc.ApricotReportCaseType = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
    }
}
export default mixinCaseType;
