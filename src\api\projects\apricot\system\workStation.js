import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'
export default {
    // 工作站维护接口
    getWorkStationData,
    getMachineRoomData,
    addWorkStation,
    editWorkStation,
    delWorkStation,
    enableWorkStation,
    sortWorkStation,
    autoSortWorkStation,
    getWorkStationType
}
export function getWorkStationData(params) {
    return request({
        url: baseURL.apricot + '/work/station/list',
        method: 'POST',
        params
    })
}
export function getMachineRoomData(params) {
    return request({
        url: baseURL.apricot + '/machine/room/by/district',
        method: 'POST',
        params
    })
}
export function addWorkStation(data) {
    return request({
        url: baseURL.apricot + '/work/station/add',
        method: 'POST',
        data
    })
}
export function editWorkStation(data) {
    return request({
        url: baseURL.apricot + '/work/station/edit',
        method: 'POST',
        data
    })
}
export function delWorkStation(params) {
    return request({
        url: baseURL.apricot + '/work/station/del',
        method: 'POST',
        params
    })
}
export function enableWorkStation(params) {
    return request({
        url: baseURL.apricot + '/work/station/enable/disabled',
        method: 'POST',
        params
    })
}
export function sortWorkStation(params) {
    return request({
        url: baseURL.apricot + '/work/station/sort',
        method: 'POST',
        params
    })
}
export function autoSortWorkStation(params) {
    return request({
        url: baseURL.apricot + '/work/station/autoSort',
        method: 'POST',
        params
    })
}
// 获取工作站类型
export function getWorkStationType() {
    return request({
        url: baseURL.apricot + '/work/station/getWorkStationType',
        method: 'POST',
    })
}