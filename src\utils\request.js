import axios from 'axios';
import qs from 'qs';
import { ElMessage as Message, ElMessageBox as MessageBox } from 'element-plus';

import router from '@/router/index.js';
import { requestConfig, baseURL as baseUrl } from './base_config';

import { getAccessToken, removeAccessToken, setAccessToken } from '@/utils/accessToken';
import { tokenName } from '@/utils/base_config';

// import { login } from '$supersetApi/user'
import store from '$supersetStore';
// 基础路径
export const baseURL = baseUrl;
//数据格式转换，否则后端拿不到
export function stringify(data) {
    return qs.stringify(data);
}

let stopAllRequest = false;
let failureCount = 0;

const checkFailure = () => {
    if (failureCount > 10) {
        stopAllRequest = true;
        failureCount = 0;
    }
};

// create an axios instance
const service = axios.create({
    timeout: 120 * 1000, // request timeout
    timeoutErrorMessage: '很抱歉，由于网络问题，服务器未能响应您的请求。请稍后再试。',
});
// request 预处理器
service.interceptors.request.use(
    (config) => {
        //请求头token设置
        config.headers[tokenName] = getAccessToken();
        checkFailure();
        if (stopAllRequest && config.url.indexOf('/auth/login/pcLogin') === -1) {
            return false; // 请求中止
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// response 拦截器
service.interceptors.response.use(
    (response) => {
        const res = response.data;

        if (Array.isArray(response.data)) {
            return Promise.resolve({
                success: true,
                data: response.data,
            });
        }

        if (response.config.url.indexOf('/auth/user/getCurrentUser') > -1) {
            if (!res || !res.success) {
                return Promise.resolve(res);
            }
        }

        if (!res) {
            failureCount++;
            Message.error({
                message: '请求失败，返回数据为空！',
                grouping: true,
            });
            return Promise.reject(res);
        }

        if (!res.success) {
            // 401未登录
            if (res.code === 401) {
                failureCount++;

                Message.error({
                    message: '401-登录已过期，请重新登录',
                    grouping: true,
                });

                // stopRequest = true
                store.dispatch('user/resetAccessToken');

                if (window.__reloginLock) {
                    // 弹出登录窗口的全局函数
                    window.__reloginLock('请输入密码重新登录');
                } else {
                    const fromPath = router.options.history.state.current;
                    router.push({ path: `/login?redirect=${fromPath}` }).catch(() => {});
                    // window.location.href = window.location.origin + window.location.pathname;
                }
            } else {
                if (res.msg === null || res.msg === '') {
                    res.msg = '请求失败，返回错误信息为空！';
                }
                Message.error({
                    message: res.msg,
                    grouping: true,
                });
            }

            return Promise.reject(res);
        } else {
            stopAllRequest = false;
        }
        // // 心跳设置
        // store.dispatch('user/setHeartbeat');
        //传递res,让业务处理
        return Promise.resolve(res);
    },
    (error) => {
        if (stopAllRequest) return Promise.reject({ msg: '请求中止', stopAllRequest });

        if (error && error.response) {
            let status = error.response.status;
            let tipsObject = {
                400: '请求错误',
                401: '未授权，请登录',
                403: '拒绝访问',
                404: `请求地址出错: ${error.response.config.url}`,
                408: `请求超时: ${error.response.config.url}`,
                500: `服务器内部错误: ${error.response.config.url}`,
                501: `服务未实现: ${error.response.config.url}`,
                502: '网关错误',
                503: '服务不可用',
                504: '网关超时',
                505: 'HTTP版本不受支持',
            };
            if (Object.keys(tipsObject).includes(status + '')) {
                Message({
                    message: tipsObject[status + ''],
                    type: 'error',
                    duration: 5 * 1000,
                    showClose: true,
                    grouping: true,
                });
                return Promise.reject(error);
            }
        }
        Message.closeAll();
        const url = error.config.url;
        const index = url.indexOf(':', 5);
        // const configIp = url.slice(0,index)
        const configPort = url.slice(index + 1, index + 6);
        const testLink = url.indexOf('testLink'); // 为testLink请求不提示
        const printerNames = url.indexOf('/printer/get/names'); // 获取打印机名字接口
        const calibratorRead = url.indexOf('/calibrator/read'); // 活度仪数据采集接口
        // 不需要任何提示
        if ((testLink > 0 || printerNames > 0 || calibratorRead > 0) && configPort == '18153') {
            return Promise.reject(error);
        }
        // 本机ip和请求ip一样时为本地客户端服务调用，18153端口不可改，所以暂时改用端口判断
        if (configPort == '18153') {
            Message({
                message: '客户端未启动！',
                type: 'error',
                duration: 5 * 1000,
                offset: 150,
                showClose: true,
                grouping: true,
            });
            onActiveClient();
            return Promise.reject(error);
        }
        Message({
            message: error.message,
            type: 'error',
            duration: 5 * 1000,
            showClose: true,
            grouping: true,
        });
        return Promise.reject(error);
    }
);
// 激活客户端
function onActiveClient() {
    let a = document.createElement('a');
    a.href = 'XingXiang://';
    // a.setAttribute('target', '_blank');
    document.body.append(a);
    a.click();
    a.remove();
}

export default service;
