<template>
    <el-popover v-if="patient.iPriority !== 1"  v-model:visible="visible"
        trigger="click"
        placement="right"
        width="650"
        @show="onShowPop">
        <div style="padding:10px;">
            <div class="m-flexLaout-tx u-autoHeight"
                style="margin-bottom: 15px;">
                <span>紧急原因：</span>
                <div class="g-flexChild"
                    style="max-height:400px">
                    <el-input type="textarea"
                        :rows="4"
                        placeholder=""
                        v-model="sMemo"></el-input>
                </div>
            </div>
            <div style="text-align: right; margin: 0">
                <el-button type="primary"
                    @click="onClickSave">保存</el-button>
                <el-button @click="visible = false">关闭</el-button>
            </div>
        </div>
        <template #reference>
                <!--  'i-grade': patient.iPriority === 1 ? true: false -->
            <i class="fa fa-fire-fill c-icon" 
                :class="{'i-current': currentIndex === patient.index,}"
                @click.capture></i>
        </template>
    </el-popover>

    <i v-else class="fa fa-text-card c-icon i-grade" 
        :class="{'i-current': currentIndex === patient.index}"
        @click="onCancelClick"></i>
         
</template>
<script>

import { prioritySet } from '$supersetApi/projects/apricot/common/index.js'
export default {
    name: 'PriorityMark',
    props: {
        // buttonMsg: {
        //     type: Object,
        //     default: () => ({})
        // },
        patient: {
            type: Object,
            default: () => ({})
        },
        currentIndex: {
            default: undefined
        }
    },
    emits: ['callBack'],
    data () {
        return {
            visible: false,
            sCollectionTag: '',
            checkList: [],
            options: [],
            sMemo: '',
        }
    },
    watch: {
        visible (val) {
            this.sMemo = '';
            // if (val) {
            //     if (this.patient.sCollectionTagId) {
            //         let id = this.patient.sCollectionTagId
            //         // this.getsTagsContentById(id)
            //     } else {
            //         // 当sCollectionTagId为false时，重置表单数据
            //         this.sCollectionTag = '';
            //         this.checkList = [];
            //         this.sTagReason = '';
            //     }
            //     this.getCollectionTagEnable();
            // }
        },
    },
    methods: {
        onShowPop (val) {
            // console.log(this.visible)
        },
        onCancelClick() {
            this.$confirm(`确定要取消【紧急】设置吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                this.onClickSure()
            })
        },
        onClickSave () {
            this.onClickSure()
        },
        onClickSure () {
            const iOperateType =  this.patient.iPriority === 1 ? 0 : 1;
            let jsonData = {
                sPatientId: this.patient.sId,
                iOperateType: iOperateType,
                sMemo: this.sMemo
            }
            prioritySet(jsonData).then(res => {
                console.log(res)
                if (res.success) {
                    this.patient.iPriority = iOperateType;
                    this.visible = false;
                    this.$message.success('操作成功！');
                    return;
                }
                this.$message.error(res.msg)
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.c-icon {
    position: relative;
    top: 4px;
    font-size: 24px;
    font-weight: normal;
    cursor: pointer;
    &:hover {
        color:red;
    }
    &.i-current {
        top: 0;
    }
    &.i-grade {
        color:red; 
    }
}
</style>
