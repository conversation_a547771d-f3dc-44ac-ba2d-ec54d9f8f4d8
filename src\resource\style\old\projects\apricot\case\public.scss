/*患者列表行选中--------------------------------------------*/
.c-casePatientList {
    .el-table {
        .el-table__body-wrapper {
            overflow: auto;
        }
    }
}

.c-casePatientList .el-table__body tr {

    td {
        position: relative;

        .c-headPortrait {
            height: 24px;
            display: block;

            >svg.fa {
                font-size: 24px;
            }
        }

        .c-other {
            position: absolute;
            display: none;
        }
    }
}

.c-casePatientList .el-table__body tr.current-row {
    // height: 125px;

    
}

/*--------------------------------------------患者列表行选中*/
/*病例管理患者信息------------------------------------------------*/
.c-patientInfo.s-showAll {

    >div:not(.g-flexChild) {
        display: none;
    }
}

.My-dragAdiust.i-caseBusiness {

    >.g-adjust-item:first-of-type {

        >.c-content {
            overflow: visible;
        }
    }
}




/*------------------------------------------------病例管理患者信息*/


