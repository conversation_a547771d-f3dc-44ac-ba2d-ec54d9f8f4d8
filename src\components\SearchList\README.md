# SearchList 通用查询条件栏组件

示例：
```
  <SearchList v-model:modelValue="condition" :list="searchListConfig" :optionData="optionsLoc" :configBtn="buttonRef"
        storageKey="ReportIndexSearch" iModuleId="6" @clickSearch="mxDoSearch" @changeSearch="mxDoSearch" @reset="mxOnClickReset">
        <template v-slot:dAppointmentTimeSt>
          <el-date-picker v-model="condition.dAppointmentTimeSt" type="date"
            :picker-options="pickerOptionsAppointDayStart" @change="changeAppointTime">
          </el-date-picker>
        </template>
  </SearchList> 
  <el-button type="primary" :ref="(el) => buttonRef = el">替换按钮</el-button>
```

属性: 
| 属性名 | 说明 | 格式 | 默认值 | 
|----|--------------|---|---| 
| modelValue  | 包含了查询条件数据的对象  | object | {}  | 
| list  | 查询条件的配置数据，格式见示例   | array | []  | 
| storageKey  | 保存到localstorage时的唯一标识，不填则不保存   | string | 无  | 
| iModuleId  | 保存到服务器时的moduleId字段，与storageKey同时存在时启用数据上传   | string | 无  |  
| optionData  | 包含所有查询条件中下拉选择项的选项数据的对象   | object | {}  |  
| configBtn  | 配置按钮是否显示，或用于替换默认按钮的ref对象   | boolean,Ref | true  |  
| defaultCondition  |  查询条件的默认值，重置的时候，如果有默认值查询条件，使用默认值  | object | {}  | 

插槽:
|  插槽名称 | 说明  |
|-----------|-------------------------|
| “查询条件的prop字段” | 配置了iCustom: true的查询条件会使用插槽名字为其prop字段的插槽内容作为渲染组件  |


事件:
| 事件 | 参数 | 说明 |   
|----|-----|---------------|
'reset' | 无  |  点击重置按钮时触发   |
'clickSearch' | 无 |  点击查询按钮时触发  |
'changeSearch' | { value, sProp, sInputType } |  内置组件的值改变时触发  |


 配置数据示例：
 ``` 
 modelValue: {
    iIsImaging: 1
 },
 list: [
        {     
            sProp: 'iIsImaging',
            sLabel: '收图状态',
            iIsHide: false,  // 为true时排除掉该配置项
            prop: '',  // 与sProp任选一个
            label: '',  // 与sLabel任选一个
            isShow: true, // 不为false时默认显示该项（可设置）

            sInputType: 'text', // 组件类型， 目前只有date-picker，option，text三种
            iLayourValue: 3, // 没有传width时默认按 1/12 转化为宽度%
            iCustom: 1, // 是否使用插槽
              
            width: '25%',  
            labelWidth: '100px',  
            clearable: true,            // 是否有清除按钮
            placeholder: '',
            sOptionProp: 'iIsImagingOptions', // 使用下拉选择项时，在options中找到这个字段对应的数据作为可选项数据
            optionLabel: 'sName',            //  options中的label
            optionValue: 'sValue',            //  options中的value
            isMultiSelect: false,          //  下拉选择项是否多选
            dateType: 'date', //  element-plus datePicker组件中的Type属性 
            dateValueFormat: 'YYYY-MM-DD HH:mm:ss' //  element-plus datePicker组件中ValueFormat属性 
        },
        //  必填：
        {
            sProp: 'sAge',
            sLabel: '年龄', 
        },
 ],
optionData: {
  iIsImagingOptions: [
    {
            sName:'全部',
            sValue:null
        },
        {
            sName:'已收图',
            sValue:1
        },
        {
            sName:'未收图',
            sValue:0
        }
  ],
}
 ```




