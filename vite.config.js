import { defineConfig, splitVendorChunkPlugin } from 'vite';
const path = require('path');
import vue from '@vitejs/plugin-vue';
import legacy from '@vitejs/plugin-legacy';
import WindiCSS from 'vite-plugin-windicss';
// import { viteMockServe } from 'vite-plugin-mock';
// import { svgBuilder } from './src/components/plugins/svgBuilder';

// import vueI18n from '@intlify/vite-plugin-vue-i18n';

import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import IconsResolver from 'unplugin-icons/resolver';
import resolveExtensionVue from 'vite-plugin-resolve-extension-vue';

import { createHtmlPlugin } from 'vite-plugin-html';

//项目部署的基础路径
const base = './',
  // 静态资源服务的文件夹 类型 string | false
  publicDir = 'public',
  // 输出路径
  outDir = 'dist',
  // 生成静态资源的存放路径
  assetsDir = 'static/',
  // 构建后是否生成 source map 文件
  sourcemap = false,
  // chunk 大小警告的限制
  chunkSizeWarningLimit = 7000,
  // 启用/禁用 CSS 代码拆分
  // 压缩大型输出文件可能会很慢，因此禁用该功能可能会提高大型项目的构建性能。
  cssCodeSplit = false,
  // 启用/禁用 brotli 压缩大小报告
  brotliSize = false,
  // 指定服务器应该监听哪个 IP 地址
  host = '0.0.0.0',
  // 指定开发服务器端口
  port = '8089',
  // 设为 true 时若端口已被占用则会直接退出，而不是尝试下一个可用端口
  strictPort = false,
  // 服务器启动时自动在浏览器中打开应用程序 此值为字符串时，会被用作 URL 的路径名
  open = true,
  //token名称
  tokenName = 'aptSessionId',
  //token在localStorage、sessionStorage存储的key的名称
  tokenTableName = 'aptSessionId',
  // default language
  lang = 'zh-cn',
  // 导入时想要省略的扩展名列表
  extensions = ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'],
  // 调整控制台输出的级别 'info' | 'warn' | 'error' | 'silent'
  logLevel = 'info',
  // 设为 false 可以避免 Vite 清屏而错过在终端中打印某些关键信息
  clearScreen = false,
  // 是否删除生产环境console
  drop_console = true,
  // 是否删除生产环境debugger
  drop_debugger = true,
  // 配置文件版本号
  config_version = new Date().getTime();

const isDev = process.env.NODE_ENV === 'development';

// const loadI18n = isDev ? vueI18n({ include: path.resolve(__dirname, './src/locales/**') }) : '';
// https://vitejs.dev/config/
export default defineConfig({
  root: process.cwd(),
  base: isDev ? '/' : '/app/taxus/',
  esm: true,
  cache: true,
  publicDir,
  logLevel,
  clearScreen,
  plugins: [
    // stylelint({ files: '**/*.css', emitError: false, emitWarning: true, fix: false }),
    vue(),
    WindiCSS(),
    resolveExtensionVue(),

    // loadI18n,
    legacy({
    }),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等
      imports: ['vue', 'vue-router', 'vuex'],
      // resolvers: [
      //   ElementPlusResolver(), 
      // ],
    }),
    // Components({
    //   //   resolvers: [
    //   //     ElementPlusResolver({
    //   //       importStyle: 'sass',
    //   //       // directives: true,
    //   //       // version: "2.1.5",
    //   //     }), 
    //   //   ],
    // }),
    splitVendorChunkPlugin(),
    createHtmlPlugin({
      minify: true,
      inject: {
        data: {
          title: 'index',
          injectScript: `<script src="./static/superset_config.js?v=${config_version}"></script>`,
        },
      },
    }),
 ],

 server: {
    host,
    port,
    strictPort: false,
    open,
    fs: {
      strict: false,
    },
    proxy: {
      // '/api/bsuser': {
      //   target: 'http://*************:19006',
      //   changeOrigin: true,
      //   rewrite: (path) => path.replace(/^\/api/, ''),
      // }, 
      // }, 



    }
  },

  resolve: {
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    // 设置别名
    alias: {
      /*common---*/
      '@': resolve('src'),
      $plugins: resolve('src/components/plugins'), //公共插件(组件)


      /*superset--*/
      $supersetViews: resolve('src/views'), //报告系统页面组件
      $supersetResource: resolve('src/resource'), //报告系统本身的资源
      $supersetUtils: resolve('src/utils'),
      $supersetApi: resolve('src/api'),
      $supersetStore: resolve('src/store'),
      $supersetRouter: resolve('src/router'),
      $supersetIcon: resolve('src/resource/iconfonts'),


    },
  },

  css: {
    preprocessorOptions: {
      // 引入公用的样式
      scss: {
        additionalData: `@use "$supersetResource/style/common.scss" as *;`,
        charset: false,
      },
    },
    // postcss: {

    // plugins: [
    //     {
    //       postcssPlugin: 'internal:charset-removal',
    //       AtRule: {
    //         charset: (atRule) => {
    //           if (atRule.name === 'charset') {
    //             atRule.remove();
    //           }
    //         }
    //       }
    //     }
    // ],
    // }
  },

  build: {
    target: 'modules',
    outDir,
    assetsDir,
    sourcemap: isDev,
    cssCodeSplit,
    brotliSize,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    esbuild: {
      drop: isDev ? [] : ['console.log']
    },
    rollupOptions: {
    //   watch: {
    //     exclude: 'node_modules/**'
    //   },
    //   cache: true,
    //   output: {
    //     sourcemap: false,
    //     manualChunks: (id) => {
    //       if (id.includes('node_modules')) {
    //         return 'vendor';
    //       }
    //     },
    //   },
    // 静态资源分类打包
      output: {
        chunkFileNames: 'static/js/[name]-[hash].js',
        entryFileNames: 'static/js/[name]-[hash].js',
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
      },
    },
    chunkSizeWarningLimit,
    reportCompressedSize: false,
  },

  optimizeDeps: {
    // 检测需要预构建的依赖项 
    include: [
      "@element-plus/icons-vue",
      "axios",
      "clipboard",
      "core-js",
      "echarts",
      "element-plus",
      "element-plus/es",
      "element-plus/es/components/avatar/style/css",
      "element-plus/es/components/avatar/style/index",
      "element-plus/es/components/backtop/style/css",
      "element-plus/es/components/backtop/style/index",
      "element-plus/es/components/breadcrumb-item/style/css",
      "element-plus/es/components/breadcrumb-item/style/index",
      "element-plus/es/components/breadcrumb/style/css",
      "element-plus/es/components/breadcrumb/style/index",
      "element-plus/es/components/button-group/style/css",
      "element-plus/es/components/button-group/style/index",
      "element-plus/es/components/button/style/css",
      "element-plus/es/components/button/style/index",
      "element-plus/es/components/card/style/css",
      "element-plus/es/components/card/style/index",
      "element-plus/es/components/checkbox-button/style/css",
      "element-plus/es/components/checkbox-button/style/index",
      "element-plus/es/components/checkbox-group/style/css",
      "element-plus/es/components/checkbox-group/style/index",
      "element-plus/es/components/checkbox/style/css",
      "element-plus/es/components/checkbox/style/index",
      "element-plus/es/components/col/style/css",
      "element-plus/es/components/col/style/index",
      "element-plus/es/components/config-provider/style/css",
      "element-plus/es/components/config-provider/style/index",
      "element-plus/es/components/container/style/css",
      "element-plus/es/components/container/style/index",
      "element-plus/es/components/date-picker/style/css",
      "element-plus/es/components/date-picker/style/index",
      "element-plus/es/components/descriptions-item/style/css",
      "element-plus/es/components/descriptions-item/style/index",
      "element-plus/es/components/descriptions/style/css",
      "element-plus/es/components/descriptions/style/index",
      "element-plus/es/components/divider/style/css",
      "element-plus/es/components/divider/style/index",
      "element-plus/es/components/drawer/style/css",
      "element-plus/es/components/drawer/style/index",
      "element-plus/es/components/dropdown-item/style/css",
      "element-plus/es/components/dropdown-item/style/index",
      "element-plus/es/components/dropdown-menu/style/css",
      "element-plus/es/components/dropdown-menu/style/index",
      "element-plus/es/components/dropdown/style/css",
      "element-plus/es/components/dropdown/style/index",
      "element-plus/es/components/form-item/style/css",
      "element-plus/es/components/form-item/style/index",
      "element-plus/es/components/form/style/css",
      "element-plus/es/components/form/style/index",
      "element-plus/es/components/header/style/css",
      "element-plus/es/components/header/style/index",
      "element-plus/es/components/icon/style/css",
      "element-plus/es/components/icon/style/index",
      "element-plus/es/components/input/style/css",
      "element-plus/es/components/input/style/index",
      "element-plus/es/components/main/style/css",
      "element-plus/es/components/main/style/index",
      "element-plus/es/components/menu-item/style/css",
      "element-plus/es/components/menu-item/style/index",
      "element-plus/es/components/menu/style/css",
      "element-plus/es/components/menu/style/index",
      "element-plus/es/components/message/style/css",
      "element-plus/es/components/option/style/css",
      "element-plus/es/components/option/style/index",
      "element-plus/es/components/pagination/style/css",
      "element-plus/es/components/pagination/style/index",
      "element-plus/es/components/popover/style/css",
      "element-plus/es/components/popover/style/index",
      "element-plus/es/components/progress/style/css",
      "element-plus/es/components/progress/style/index",
      "element-plus/es/components/radio-button/style/css",
      "element-plus/es/components/radio-button/style/index",
      "element-plus/es/components/radio-group/style/css",
      "element-plus/es/components/radio-group/style/index",
      "element-plus/es/components/radio/style/css",
      "element-plus/es/components/radio/style/index",
      "element-plus/es/components/row/style/css",
      "element-plus/es/components/row/style/index",
      "element-plus/es/components/scrollbar/style/css",
      "element-plus/es/components/scrollbar/style/index",
      "element-plus/es/components/select/style/css",
      "element-plus/es/components/select/style/index",
      "element-plus/es/components/sub-menu/style/css",
      "element-plus/es/components/sub-menu/style/index",
      "element-plus/es/components/switch/style/css",
      "element-plus/es/components/switch/style/index",
      "element-plus/es/components/tab-pane/style/css",
      "element-plus/es/components/tab-pane/style/index",
      "element-plus/es/components/table-column/style/css",
      "element-plus/es/components/table-column/style/index",
      "element-plus/es/components/table/style/css",
      "element-plus/es/components/table/style/index",
      "element-plus/es/components/tabs/style/css",
      "element-plus/es/components/tabs/style/index",
      "element-plus/lib/locale/lang/en",
      "element-plus/lib/locale/lang/zh-cn",
      "file-saver",
      "jquery",
      "js-cookie",
      "lodash-es",
      "mitt",
      "moment",
      "nprogress",
      "qs",
      "screenfull",
      "sortablejs",
      "tiff.js",
      "v-contextmenu",
      "vue",
      "vue-router",
      "vuedraggable",
      "vuex",
      "xlsx"
    ]
  },

});

function resolve(sPath) {
  return path.resolve(__dirname, sPath)
}

function writeUpdateLog() {
  if (process.env.NODE_ENV === 'development') return
  const fs = require('fs')
  const txt = String(+new Date())
  fs.writeFile("public/static/checkUpdate.json", txt, (err) => {
    if (err) throw err;
  });
}

writeUpdateLog() // 检查代码是否更新
