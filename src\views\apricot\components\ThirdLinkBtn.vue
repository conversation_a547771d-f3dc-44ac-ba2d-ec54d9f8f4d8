<template>
    <el-dropdown>
        <el-button :disabled="buttonMsg.isReadOnly" class="m-vertical-btn t2" :class="{
            'm-vertical-btn t2': buttonMsg.icon,
            'margin_l': !buttonMsg.icon,
            'm-vertical-text': buttonMsg.isFold,
            't-border': buttonMsg.isBorder
        }">
            <svg class="fa" aria-hidden="true">
                <use :xlink:href="'#' + buttonMsg.icon"></use>
            </svg>
            <label>{{ buttonMsg.name }}<i class="el-icon-caret-bottom"></i></label>
        </el-button>
        <template #dropdown>
            <el-dropdown-menu v-if="linkList.length">
                <el-dropdown-item v-for="(item, index) in linkList" :key="index" @click.native="handleItemClick(item)">
                    <span> {{ item.sLinkTypeName }}</span>
                </el-dropdown-item>
            </el-dropdown-menu>
            <el-empty v-else
                :image-size="50"
                description="暂无资料"
                style="padding: 10px 50px" />
        </template>
    </el-dropdown>
</template>

<script>
import Api from '$supersetApi/projects/apricot/system/thirdLink.js'
import { getStoreNameByRoute } from '$supersetResource/js/projects/apricot'
import { useHandleThirdLinkItem } from '$supersetResource/js/projects/apricot/useHandlerThirdLink.js';
export default {
    name: 'ThirdLinkBtn',
    props: {
        buttonMsg: {
            type: Object,
            default: () => ({})
        },
    },
    data () {
        return {
            linkList: [],
            urlModule: ''
        }
    },
    computed: {
        patientInfo () {
            let iModule = this.$store.state.apricot[this.urlModule];
            if (iModule && iModule.patientInfo) {
                return iModule.patientInfo;
            }
            return {};
        },
        userInfo () {
            let loginUser = this.$store.state.user.userSystemInfo
            if (loginUser) {
                return loginUser
            }
            return {}
        }
    },
    methods: {
        handleItemClick (item) {
            const patientInfo_ = this.patientInfo;
            if (!patientInfo_.sId) {
                this.$message.warning('请选择患者数据!');
                return
            }
            const userInfo_ = this.userInfo;
            useHandleThirdLinkItem(item, patientInfo_, userInfo_)
        },
        getData () {
            let params = {
                condition: {
                    sLinkTypeCode: '1'
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 200,
                }
            };
            Api.getThirdLinkData(params).then(res => {
                if (res.success) {
                    this.linkList = res.data.recordList == null ? [] : res.data.recordList;
                }
            }).catch(err => {
                console.log(err);
            })
        },
    },
    mounted () {
        this.urlModule = getStoreNameByRoute(this.$route.name);
        this.getData();
    }
}
</script>

<style lang="scss" scoped>
.margin_l {
    margin-left: 10px;
}
</style>
