export default {
  ApricotReportForm: [{
    sProp: 'sProcessRecord',
    sLabel: '检查技术',
    iIsHide: false,
    iCustom: 1,
    name: 'c0'
  },
  {
    sProp: 'sInspectSee',
    sLabel: '检查所见',
    iIsHide: false,
    iCustom: 1,
    name: 'c1'
  },
  {
    sProp: 'sDiagnosticOpinion',
    sLabel: '诊断意见',
    iIsHide: false,
    iCustom: 1,
    name: 'c2'
  },
  ],
  ApricotReportFormEnd: [{
    sProp: 'sPracticeName',
    sLabel: '书写医生（进修）',
    sWidth: '100%',
    sInputType: 'text',
    iLayourValue: 3,
  }, {
    sProp: 'dPracticeTime',
    sLabel: '书写日期',
    sWidth: '100%',
    iReadonly: 1,
    sInputType: 'date-picker',
    iLayourValue: 3,
  }, {
    sProp: 'sReporterName',
    sLabel: '报告医生',
    sWidth: '100%',
    Readonly: 1,
    sInputType: 'text',
    iLayourValue: 3,
  }, {
    sProp: 'dReporterTime',
    sLabel: '报告日期',
    sWidth: '100%',
    iReadonly: 1,
    sInputType: 'date-picker',
    iLayourValue: 3,
  }, {
    sProp: 'sExamineName',
    sLabel: '审核医生',
    sWidth: '100%',
    Readonly: 1,
    sInputType: 'text',
    iLayourValue: 3,
  }, {
    sProp: 'dExamineDate',
    sLabel: '审核日期',
    sWidth: '100%',
    iReadonly: 1,
    sInputType: 'date-picker',
    iLayourValue: 3,
  }, {
    sProp: 'sFinalExamineName',
    sLabel: '复审医生',
    sWidth: '100%',
    Readonly: 1,
    sInputType: 'text',
    iLayourValue: 3,
  }, {
    sProp: 'dFinalExamineDate',
    sLabel: '复审日期',
    sWidth: '100%',
    iReadonly: 1,
    sInputType: 'date-picker',
    iLayourValue: 3,
  }],
  ApricotReportEditor1: [{
    sProp: 'sQualitative',
    sLabel: '阴阳性',
    sInputType: 'option',
    sOptionProp: 'qualitative',
    iLayourValue: 24,
    iCustom: 1,
  },
//   {
//     sProp: 'sImgQuality',
//     sLabel: '影像质量',
//     sInputType: 'option',
//     sOptionProp: 'imgQuality',
//     iLayourValue: 24,
//     iCustom: 1,
//     iRequired: 1,
//   }, {
//     sProp: 'sReportQuality',
//     sLabel: '报告质量',
//     sInputType: 'option',
//     sOptionProp: 'reportQuality',
//     iLayourValue: 24,
//     iCustom: 1,
//     // iRequired: 1,

//   },
//   {
//     sProp: 'sDiagnosticed',
//     sLabel: '诊断符合',
//     sInputType: 'option',
//     sOptionProp: 'diagnosticAccord',
//     iLayourValue: 24,
//     iCustom: 1,
//     // iRequired: 1,

//   },
  // {
  //   sProp: 'recordTeach',
  //   sLabel: '病例类型',
  //   // sInputType: 'option',
  //   sOptionProp: 'ApricotReportCaseType',
  //   iLayourValue: 24,
  //   iCustom: 1,
  //   sHeight: 'auto',
  // },

  ],

  businessMenus: [{
    name: '患者信息',
    fn: 'openPatientInfoEditDialog',
    icon: 'fa-modify-1',
    isFold: false,
    group: '操作组1',
    rights: 'report:report:patientInfo'
  },
  {
    name: '阅图',
    fn: 'onRebuildOpen0',
    icon: 'fa-poly-img-view',
    isFold: false,
    group: '操作组1',
    rights: 'report:report:csView'
  },
  {
    name: '重建',
    fn: 'onRebuildOpen1',
    icon: 'fa-poly-rebuildding-1',
    isFold: false,
    group: '操作组1',
    rights: 'report:report:csRebuild'
  },
  {
    name: 'web阅图',
    fn: 'openWebReadViewer',
    icon: 'fa-poly-img-view',
    isFold: false,
    group: '操作组1',
    rights: 'report:report:webView'
  },
  {
    name: 'web重建',
    fn: 'openWebReBuildViewer',
    icon: 'fa-poly-rebuildding-1',
    isFold: false,
    group: '操作组1',
    rights: 'report:report:webRebuild'
  },
  {
    name: '临床资料',
    // fn: 'hello',
    icon: 'fa-clinical-data',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:clinicMaterial',
    mark: 'isClinic'
  },
  {
    name: '电子申请单',
    fn: 'hello',
    icon: 'fa-electronic-medical-record',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:applyOrder',
    mark: 'isApplyOrder'
  },
  {
    name: '打印预览',
    fn: 'mxOnReportPreView',
    icon: 'fa-printing-format',
    isFold: false,
    mark: 'isPopover',
    group: '操作组2',
    rights: 'report:report:browseReport'
  },
  {
    name: '报告打印',
    fn: '',
    icon: 'fa-print-text',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:printReport',
    mark: 'isPrint',
  },
  {
    name: '报告下载',
    fn: '',
    icon: 'fa-download',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:downloadReport',
    mark: 'isPDF',
  },
  {
    name: '刻录',
    fn: '',
    icon: 'fa-recording-1',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:cdBurn',
    mark: 'isBurn',
  },
  {
    name: '图像导入',
    fn: 'onOpenImageImport',
    icon: 'fa-import-fill',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:importDicom'
  },
  {
    name: '图像导出',
    fn: '',
    icon: 'fa-export-fill ',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:exportDicom',
    mark: 'exportBtn',
  },

  {
    name: '关联图像',
    fn: 'showDialogMergeCase',
    icon: 'fa-note-link',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:relateDicom'
  },
  {
    name: '发送图像',
    fn: '',
    icon: 'fa-image-send',
    isFold: false,
    group: '操作组2',
    rights: 'report:report:batchPublish',
    mark: 'sendImage',
  },

  // {
  //   name: '收藏病例',
  //   fn: 'onClickEnshrine',
  //   icon: 'fa-collect-case',
  //   isFold: false,
  //   group: '操作组2',
  //   rights: 'report:report:patientInfoCollectCase',
  //   mark: 'isCollect',
  // },






  {
    name: '保存(S)',
    fn: 'onSave',
    icon: 'fa-sign',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'SAVE'
    key: '1'
  },
  {
    name: '提交(A)',
    fn: 'onSubmit',
    icon: 'fa-submit-report',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'COMMIT'
    key: '2'
  },
  {
    name: "撤销提交",
    fn: "onSubmit",
    icon: "fa-take-back",
    isFold: false,
    group: "操作组3",
    iIsHide: 1,
    // key: 'CANCEL_COMMIT'
    key: '3'
  },
  {
    name: '审核(E)',
    fn: 'onClickAudit',
    icon: 'fa-presentation-examine',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'AUDIT'
    key: '4'
  },
  {
    name: '审核退回',
    fn: 'onClickAuditBack',
    icon: 'fa-presentation-return',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'AUDIT_BACK'
    key: '5'
  },
  {
    name: '撤审',
    fn: 'onClickAudit',
    icon: 'fa-presentation-revoke-audit',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'CANCEL_AUDIT'
    key: '6'
  },
  {
    name: '复审(R)',
    fn: 'onClickDoubleAudit',
    icon: 'fa-examine-review-case',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'RECHECK'
    key: '7'
  },
  {
    name: '复审退回',
    fn: 'onClickDoubleAuditBack',
    icon: 'fa-presentation-return',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'RECHECK_BACK'
    key: '8'
  },
  {
    name: '撤销复审',
    fn: 'onClickDoubleAudit',
    icon: 'fa-revoke-review',
    isFold: false,
    group: '操作组3',
    iIsHide: 1,
    // key: 'CANCEL_RECHECK'
    key: '9'
  },
  // },
  {
    name: '修改痕迹',
    fn: 'showDialogModifyTrace',
    icon: 'fa-modify-compare',
    isFold: true,
    group: '流程记录',
    rights: 'report:report:modifyTrace'
  },

  {
    name: '打印记录',
    fn: 'onPrintRecordClick',
    icon: 'fa-log',
    isFold: true,
    group: '流程记录',
    rights: 'report:report:printLog'
  },

  {
    name: '流程记录',
    fn: 'openFlowRecordDialog',
    icon: 'fa-onduty-record',
    isFold: true,
    group: '流程记录',
    rights: 'report:report:flowLog'
  },
  {
    name: '自动备份记录',
    fn: 'openBackupsRecodeDialog',
    icon: 'fa-copy',
    isFold: true,
    group: '流程记录',
    rights: 'report:report:bakLog'
  },

  
  {
    name: '修改实习医生',
    fn: '',
    icon: 'fa-doctors',
    isFold: true,
    group: '信息修改',
    rights: 'report:report:modifyTrainee',
    mark: 'isChangePractice',
  },
  {
    name: '修改报告医生',
    fn: '',
    icon: 'fa-doctors',
    isFold: true,
    group: '信息修改',
    rights: 'report:report:modifyReporter',
    mark: 'isChangeWrite',
  },
  {
    name: '修改审核医生',
    fn: '',
    icon: 'fa-doctors',
    isFold: true,
    group: '信息修改',
    rights: 'report:report:modifyAuditor',
    mark: 'isChangeAuditor',
  },

  {
    name: '报告发布',
    fn: 'handlerPublish',
    icon: 'fa-report-publish',
    isFold: true,
    group: '报告发布',
    rights: 'report:report:publishReport'
  },
  {
    name: '状态回传',
    fn: '',
    icon: 'fa-send',
    isFold: true,
    group: '报告发布',
    rights: 'report:report:sendMessage',
    mark: 'sendMessage',
  },

  {
    name: '发送会诊',
    fn: 'onSendConsultation',
    icon: 'fa-ploy-consultation',
    isFold: true,
    group: '报告发布',
    rights: 'report:report:sendConsultationEditor',
    iIsHide: 1,
  },
  {
    name: '取消会诊',
    fn: 'onCancelConsultation',
    icon: 'fa-ploy-consultation',
    isFold: true,
    group: '报告发布',
    rights: 'report:report:cancelConsultation',
    iIsHide: 1,
  },

  {
    name: '页面设置',
    fn: 'openReportSettingDialog',
    icon: 'fa-page-setting',
    isFold: true,
    group: '其他设置',
    rights: 'report:report:pageConfig',
  },

  {
    name: '打印设置',
    fn: 'onOpenPrintSet',
    icon: 'fa-print-set',
    isFold: true,
    group: '其他设置',
    rights: 'report:report:printConfig',
  },

  {
    name: '签章验证',
    fn: 'onSignVerifyClick',
    icon: 'fa-material-fill',
    isFold: true,
    group: '其他设置',
    rights: 'report:report:signVerify',
  },
  
  ]
}
