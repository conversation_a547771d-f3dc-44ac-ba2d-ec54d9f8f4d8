import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'

export function addFollowupRecord(data) {
    return request({
        url: baseURL.apricot + '/follow/up/add',
        method: 'POST',
        data
    })
}

export function editFollowupRecord(data) {
    return request({
        url: baseURL.apricot + '/follow/up/edit',
        method: 'POST',
        data
    })
}

export function delFollowupRecord(params) {
    return request({
        url: baseURL.apricot + '/follow/up/del',
        method: 'POST',
        params
    })
}

export function getDayProjectTime(params) {
    return request({
        url: baseURL.apricot + '/plan/and/project/project/time',
        method: 'POST',
        params
    })
}
 

export function getFollowList(data) {
    return request({
        url: baseURL.apricot + '/follow/up/find/page',
        method: 'POST',
        data
    })
}

//
export function getReportInfo(params) {
    return request({
        url: baseURL.apricot + '/process/queryCaseReport',
        method: 'POST',
        params
    })
}
