<template>
  <!-- 患者信息修改 -->
  <el-dialog title="打印模板" :close-on-click-modal="false" append-to-body align-center :modelValue="dialogVisible"
    class="t-default my-dialog" width="1150px" @close="handleCloseDialog">
    <div class="c-dialog-body" style="height: 70vh;">
      <div class="m-flexLaout-ty">
        <div class="g-flexChild">
          <TemplateSet></TemplateSet>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button-icon-fa _icon="fa fa-close-1" @click="handleCloseDialog">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>

<script>
import TemplateSet from '$supersetViews/apricot/system/views/components/TemplateSet.vue'
export default {
  name: 'PrintTemplate',
  emits: ['closeDialog'],

  components: {
    TemplateSet
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {

    }
  },
  watch: {
    // dialogVisible(newValue) {
    //     if (newValue) {
    //         this.getPatientInfo()
    //     }
    // },
  },
  methods: {
    handleCloseDialog() {
      this.$emit('closeDialog');
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
