<template>
<!-- v-if="isVisible" v-loading="loading" -->
    <el-scrollbar class="my-scrollbar" >
        <el-empty
            v-if="!tableData.length"
            :image-size="80"
            description="暂无记录"
            style="height: 100%"
        />
        <el-card
            v-else
            class="box-card"
            shadow="never"
            v-for="(item, index) in tableData"
            :key="index"
            :class="{ 'c-active': selectedId === item.sId }"
        >
            <div class="text-right p-2 i-title">
                <span class="float-left i-left" style="hei">{{ `第 ${index + 1} 条` }}</span>
                <el-button-icon-fa v-if="$auth['report:machine:edit']"
                    type="primary"
                    size="small"
                    icon="el-icon-edit"
                    @click="onEdit(item)"
                    >编辑</el-button-icon-fa
                >
                <el-button-icon-fa v-if="$auth['report:machine:delete']"
                    size="small"
                    icon="el-icon-delete"
                    @click="onDelete(item)"
                    >删除</el-button-icon-fa
                >
            </div>
            <TextList
                :list="machineInfoFormConfig"
                :data="item"
                :configBtn="false"
                :iModuleId="iModuleId"
                storageKey="MachineMngInfoForm"
                labelWidth="120px"
            >
                <template #dOperateEnd="{ row, style }">
                    <span :style="style" :title="transformDate(item[row.prop])">
                        {{ transformDate(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                <template #iIntake="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + 'ml'
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #fPickingRate="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + '分钟/床'
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #iNumber="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + '分钟'
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #sImgTypeCode="{ style }">
                    <span :style="style" :title="item['sImgTypeName']">
                        {{ item['sImgTypeName'] || '（空）' }}
                    </span>
                </template>
                <template #sTargetSiteCode="{ style }">
                    <span :style="style" :title="item['sTargetSiteName']">
                        {{ item['sTargetSiteName'] || '（空）' }}
                    </span>
                </template>
                <template #sMachineryRoomId="{ style }">
                    <span :style="style" :title="item['sMachineryRoomText']">
                        {{ item['sMachineryRoomText'] || '（空）' }}
                    </span>
                </template>
                <template #sDrinkTypeCode="{ style }">
                    <span :style="style" :title="item['sDrinkTypeText']">
                        {{ item['sDrinkTypeText'] || '（空）' }}
                    </span>
                </template>
                <template #sOperatorId="{ style }">
                    <span :style="style" :title="item['sOperator']">
                        {{ item['sOperator'] || '（空）' }}
                    </span>
                </template>
            </TextList>
        </el-card>
    </el-scrollbar>
    <!-- <div class="m-flexLaout-ty" style="overflow: auto;">
        <el-table-extend :data="tableData" ref="mainTable" stripe highlight-current-row height="100%" style="width: 100%"
            @row-dblclick="onRowDblclick" :configBtn="isShowConfigBtn" :iModuleId="iModuleId" storageKey="MachineMngRecodeTable">
            <el-table-column v-for="item in machineInfoTableConfig.filter(i => !i.iIsHide)"
                :show-overflow-tooltip="item.sProp !== 'img'" :key="item.index" :prop="item.sProp" :label="item.sLabel"
                :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth" :min-width="item.sMinWidth"
                :sortable="(!!item.iSort) ? 'custom' : false" :column-key="item.sSortField ? item.sSortField : null">
                <template v-slot="scope">
                    <template v-if="item.sProp.slice(0, 1) === 'd'">
                        {{ transformDate(scope.row[`${item.sProp}`]) }}
                    </template>
                    <template v-if="item.sProp === 'Actions'">
                        <el-button-icon-fa v-if="$auth['report:machine:edit']"
                            type="primary"
                            link 
                            plain
                            icon="el-icon-edit"
                            @click="onEdit(scope.row)" >编辑</el-button-icon-fa>
                        <el-button-icon-fa v-if="$auth['report:machine:delete']"
                            link 
                            plain
                            icon="el-icon-delete"
                            @click="onDelete(scope.row)">删除</el-button-icon-fa>
                    </template>
                </template>
            </el-table-column>
        </el-table-extend>
    </div> -->
</template>

<script>
import { deepClone, transformDate } from '$supersetUtils/function'

import Configs from "../config"
import Api from '$supersetApi/projects/apricot/case/machine.js'
export default {
    props: {
        patientInfo: {
            type: Object,
            default: () => ({})
        },
        iModuleId: {
            type: [String, Number],
            default: ''
        },
        isShowConfigBtn: {
            type: Boolean,
            default: true
        }
    },
    inject: {
        updateTableInfo: {
            from: 'updateTableInfo',
        },
    },
    emits: ['toRecordTab'],
    data () {
        return {
            // machineInfoTableConfig: [...Configs.collectionTable],
            machineInfoFormConfig: [...Configs.machineInfoFormConfig],
            tableData: [],
            selectedId: ''
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
    },
    watch: {
        patientInfo: {
            handler (val, oldVal) {
                if(val && oldVal && val.sId === oldVal.sId) return;
                this.selectedId = '';
                if (val.sId) {
                    this.getTableList();
                    return
                }
                this.tableData = [];
            },
        },
    },
    methods: {
        transformDate (time) {
            return transformDate(time, false, 'yyyy-MM-dd HH:mm')
        },
        onRowDblclick(row) {
            this.onEdit(row);
        },
        onEdit(row) {
            this.selectedId = row.sId;
            // 兄弟组件 发布上机信息修改
            this.$eventbus.emit('machineInfoEdit', row);
        },
        // 删除数据
        onDelete (row) {
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                Api.delData({ sId: row.sId, iVersion: row.iVersion, sPatientId: this.patientInfo.sId }).then((res) => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.getTableList();
                        this.$eventbus.emit('machineFormInit', row);
                        return;
                    }
                    this.$message.error(res.msg);
                })
            })
        },
        // 获取表格信息
        getTableList () {
            let params = {
                condition: {
                    sPatientId: this.patientInfo.sId
                },
                orders: {
                    orderInfoList: [
                        {
                            iIndex: 0,
                            sDirection: 'asc',
                            sOrderDbName: '',
                            sOrderField: 'dOperateEnd',
                            sOrderTable: '',
                        }
                    ]
                },
                page: {
                    pageSize: 50,
                    pageCurrent: 1
                }
            }
            Api.getData(params).then(res => {
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList;
                    for (let i in this.tableData) {
                        this.setNull2Undefined(this.tableData[i])
                    }
                    if(!this.tableData.length){
                        this.updateTableInfo()
                    }
                    if(!this.selectedId) {
                        this.selectedId = this.tableData[0].sId;
                    }
                    return
                }
                this.$message.error(res.msg);
                this.tableData = [];
            }).catch(() => {
                this.tableData = [];
            })
        },
        // element组件 数字输入框 当数据返回为null 界面显示为0，设置为undefined 则不显示
        setNull2Undefined (obj) {
            if (Object.prototype.toString.call(obj) !== '[object Object]') return
            if (!Object.keys(obj).length) return
            for (let i in obj) {
                if (obj[i] === null) {
                    obj[i] = undefined
                }
            }
        },

    },
    mounted () {
        this.$eventbus.on('onRefreshMachineRecordTable',  ({res, isEdit}) => {
            if(res) {
                this.selectedId = res.sId;
                this.getTableList();
            }
            if(isEdit) {
                this.$emit('toRecordTab')
            }
        });
    },
    destroyed() {
        this.$eventbus.off('onRefreshMachineRecordTable')
    }
}
</script>
<style lang="scss" scoped>

    .el-card {
        margin-top: 15px;
        &:first-child {
            margin-top: 10px;
        }
        &.c-active {
            border: 1px solid var(--el-color-primary);
        }
        :deep(.container .grid-box) {
            border: none;
        }
        :deep(.el-card__body) {
            padding: 0;
        }
        .i-title {
            padding-left: 15px;
            padding-right: 15px;
            border-bottom: 1px solid #eee;
        }
        .i-left {
            line-height: 24px;
        }
    }
    :deep(.container .grid-box .cell-box) {
        align-items: center;
    }
// .pull-left {
//     float: left;
// }

// .delay-scan {
//     padding: 0 10px 20px;
// }

// .c-item {
//     &.t-1 {
//         padding: 15px 15px 15px;
//         font-size: 16px;
//         background-color: #e8e9ff;
//         margin-bottom: 8px;

//         .i-text {
//             font-size: 18px;
//             margin-right: 15px;
//         }
//     }

//     &.t-2 {
//         padding: 10px 15px 0 5px;
//         border-bottom: 1px solid #eee;

//         :deep(.el-col) {
//             .m-labelInput .el-input .el-input__inner {
//                 font-size: 16px;
//                 color: #333;
//                 font-weight: bold;
//             }
//         }

//         :deep(.el-col) {
//             .m-labelInput .el-input.c-input .el-input__inner {
//                 color: red !important;
//             }
//         }
//     }

//     &.t-3 {
//         padding: 10px 15px 10px 5px;

//         // border-bottom: 1px solid #eee;
//         .i-lineHead {
//             margin: 0 15px 10px;
//             font-size: 16px;
//         }

//         .i-form {
//             overflow: hidden;
//         }

//         .i-btns {
//             // padding: 0 15px;
//             // text-align: right;
//         }
//     }

//     &.t-4 {
//         padding: 0 15px 5px;
//     }
// }

// .configBtn {
//     display: inline-block;
//     width: 20px;
//     height: 20px;
// }

// .my-multipSelect {
//     flex: 1;

//     :deep(.el-input__prefix) {
//         right: 0;
//         left: auto;
//         z-index: 999;
//     }

//     .my-prefix {
//         height: calc(100% - 2px);
//         position: relative;
//         top: 1px;
//         right: 1px;
//         background-color: white;
//     }

//     .el-button {
//         margin-right: 5px;
//         line-height: 1;
//     }
// }
</style>
