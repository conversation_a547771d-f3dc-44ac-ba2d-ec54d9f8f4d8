<template>
  <el-dialog
      title="保密病例"
      :close-on-click-modal="false"
      v-model="visible"
      width="65%"
      class="t-default my-dialog"
      top="10vh"
      @close="handleClose">
          <el-tabs v-model="activeName" @tab-click="handleClick" style="height:65vh">
              <el-tab-pane label="保密病例" name="secretPatient">
                  <div class="m-table">
                      <!-- <el-row :gutter="10" class="search">
                          <el-col :span="8">
                              <el-input v-model="condition.sName" size="small" placeholder="姓名" @keyup.enter.native="getData"/>
                          </el-col>
                          <el-col :span="8">
                              <el-button size="small" plain type="primary" icon="el-icon-search" @click="getData">搜索</el-button>
                          </el-col>
                      </el-row> -->
                      <el-table ref="mainTable"
                          :loaing="tableLoading"
                          :data="tableData"
                          highlight-current-row
                          border
                          height="100%"
                          style="width: 100%;">
                          <el-table-column type="index"
                              label="序号"
                              align="center"
                              width="60">
                              <template v-slot="scope">
                                  {{scope.$index + 1}}
                              </template>
                          </el-table-column>
                          <el-table-column prop="sName" label="姓名" width="90" show-overflow-tooltip>
                              <template v-slot="scope">
                                  {{scope.row.sName}}
                              </template>
                          </el-table-column>
                          <el-table-column prop="sSexText" label="性别" width="70">
                              <template v-slot="scope">
                                  {{scope.row.sSexText}}
                              </template>
                          </el-table-column> 
                          <el-table-column prop="sAge" label="年龄" width="90" show-overflow-tooltip>
                              <template v-slot="scope">
                                  {{scope.row.sAge}}
                              </template>
                          </el-table-column>    
                          <el-table-column prop="dAppointmentTime" label="检查日期" show-overflow-tooltip>
                              <template v-slot="scope">
                                  {{transformDate(scope.row.dAppointmentTime)}}
                              </template>
                          </el-table-column>  
                          <el-table-column prop="sProjectName" label="检查项目" show-overflow-tooltip>
                              <template v-slot="scope">
                                  {{scope.row.sProjectName}}
                              </template>
                          </el-table-column> 
                          <el-table-column prop="" label="操作" width="110px" align="center">
                              <template v-slot="scope">
                                  <el-button-icon-fa type="primary" link icon="fa fa-cancle"
                                      @click="removeSecretPatient(scope.row)">取消保密</el-button-icon-fa>
                              </template>
                          </el-table-column>     
                      </el-table>

                  </div>
              </el-tab-pane>
              <el-tab-pane label="权限设置" name="setSecretRights">
                <el-scrollbar>
                    <div class="rights-box" v-loading="userLoading">
                        <h5>请勾选可查看保密病例的医生：</h5>
                        <div class="user-list">
                            <span v-for="(item,index) in usersArr"
                                :key="index">
                                <el-checkbox v-model="item.isCheck" @change="saveSecretRight(item,index)" >{{item.userName}}</el-checkbox>
                            </span>
                        </div> 
                    </div>
                </el-scrollbar>
               
              </el-tab-pane>
          </el-tabs>
          
        <template #footer>
            <el-button-icon-fa icon="fa fa-close-1" @click="$emit('update:dialogVisible', false)">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
  
</template>
<script>

import { getReportDoctors, secretPatientsList,saveUserSecretRights,removeSecretPatients,cancelUserSecretRights } from '$supersetApi/projects/apricot/appointment/patientInfo.js'
import { getReportAboveDrData  } from '$supersetResource/js/projects/apricot/useHandlerSelect.js' // 获取报告医生
// 混入
import { transformDate } from '$supersetResource/js/tools'

export default {
    name: 'secretPatients',
    mixins:[],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            condition:{
                sName:'',
                specialFlag:1
            },
            activeName: 'secretPatient',
            tableLoading : false,
            tableData:[],
            rePage: { // 分页	
                pageCurrent: 1,
                pageSize: 30,
                total: 0
            },
            doDeled: false,
            userLoading: false,
            usersArr:[],
            uerRights:{}
        }
    },
    computed: {
        visible() {
            
            return this.dialogVisible
        }
    },
    watch: {
        dialogVisible(val) {
            if(val) {
                this.getData()
            }
        },
        async activeName(val) {
            if(val == 'setSecretRights') {
                this.userLoading = true
                const Doctors = await getReportAboveDrData()
                this.userLoading = false
                this.usersArr = Doctors.reportDoctors
                this.getReportDoctors()
            }
            if(val == 'secretPatient') {
                this.getData()
            }
        }
    },
    methods: {
        transformDate:transformDate,
        handleClick(data) {
        },
        handleClose() {
            // this.visible = false
            this.$emit('onCloseDialog', this.doDeled)
            this.activeName = 'secretPatient'
            this.doDeled = false
        },
        removeSecretPatient(row) {
            let patientInfoIds = []
            patientInfoIds.push(row.sId)
            removeSecretPatients(patientInfoIds).then( res=>{
                if(res.success) {
                    this.doDeled = true
                    this.$message.success(res.msg)
                    this.getData()
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{

            })
        },
        getData() {
            this.tableLoading = true
            secretPatientsList().then( res=>{
                this.tableLoading = false
                if(res.success) {
                    this.tableData = res.data?res.data:[]
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{
                this.tableLoading = false
            })
        },
        getReportDoctors() {
            // this.userLoading = true
            getReportDoctors().then( res =>{
                // this.userLoading = false
                if(res.success) {
                    let data = res.data || [];
                    let oRights = {}
                    data.map(item => {
                        oRights[item.userId] = item.isHasSecretRight
                    })
                    this.usersArr.map(item => {
                        item.isCheck = !!oRights[item.userId];
                    })
                    // console.log(res.data)
                    return
                }
                this.$message.error(res.msg)
            }).catch( err=>{
                // this.userLoading = false
                console.log(err)
            })
        },
        saveSecretRight(item,index) {
            let userNo = {
                    userNo: item.userNo
                }
            this.userLoading = true
            if(item.isCheck) {
                saveUserSecretRights(userNo).then(res=>{
                    this.userLoading = false
                    if(res.success) {
                        this.$message.success(res.msg)
                        return
                    }
                    this.$message.error(res.msg)
                    this.getReportDoctors()
                }).catch(err=>{
                    console.log(err)
                })
                return
            }
            cancelUserSecretRights(userNo).then( res=>{
                this.userLoading = false
                if(res.success) {
                    this.$message.success(res.msg)
                    return
                }
                this.$message.error(res.msg)
                this.getReportDoctors()
            }).catch(err=>{
                console.log(err)
            })  
        }
    },
    mounted () {
        
    }

}
</script>
<style lang="scss" scoped>
 .el-tabs {
    :deep(.el-tabs__content) {
        padding: 0 10px;
    }
 }
.m-table{
    height: 100%;
    padding-left: 5px;
    padding-top: 10px;
    box-sizing: border-box;
    .search {
        padding: 10px 0;
    }
}
.rights-box {
    padding: 10px;
    h5{
        font-weight: 600;
        margin: 10px 0;
    }
    .user-list {
        span {
            display: inline-block;
            width: 170px;
            margin: 10px 10px;
        }
    }
}
</style> 
