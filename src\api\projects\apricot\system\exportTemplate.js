import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'
export default {
    // 导出模板操作接口 : 
    getData(data) {
        return request({
            url: baseURL.apricot + '/export/template/search',
            method: 'POST',
            data
        })
    },
    getModules(data) {
        return request({
            url: baseURL.apricot + '/export/template/get/modules',
            method: 'POST',
            data
        })
    },
    saveData(data) {
        return request({
            url: baseURL.apricot + '/export/template/add',
            method: 'POST',
            data
        })
    },
    editData(data) {
        return request({
            url: baseURL.apricot + '/export/template/edit',
            method: 'POST',
            data
        })
    },
    delData(data) {
        return request({
            url: baseURL.apricot + '/export/template/del',
            method: 'POST',
            data
        })
    },
    findDataById(params) {
        return request({
            url: baseURL.apricot + '/export/template/find/id',
            method: 'POST',
            params
        })
    }
}