$borderColor: #cdcecf;
$borderColor2: #eee;

/*字体图标基础设置--------------------------------------*/
.fa {
    width: 1em;
    height: 1em;
    vertical-align: 0.07em;
    fill: currentColor;
    overflow: hidden;
    &.fa-auto {
      width: auto;
      height: auto;
      vertical-align: middle;
    }
}

/*--------------------------------------字体图标基础设置*/

/*样式组件-------------------------------------------*/
$labelbgColor: var(--labelbgColor, #fff);

.m-labelInput {
    position: relative;
    float: left;
    margin-left: 10px;
    margin-top: 11px;
    margin-bottom: 11px;
    // -moz-user-select: none;
    // -khtml-user-select: none;
    // user-select: none;

    >label {
        position: absolute;
        top: 0;
        left: 0;
        line-height: 20px;
        transform: translateY(-50%);
        margin-left: 10px;
        padding: 0 2px;
        z-index: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
        color: #606266;
        &:before {
            content: '';
            position: absolute;
            height: 4px;
            width: 100%;
            left: 0;
            top: 50%;
            background-color: #fff;
            background-color: $labelbgColor;
            z-index: -1;
        }
    }

    &.t-inlineBlock {
        float: none;
        display: inline-block;
    }
}

.el-radio-group+.m-labelInput {
    margin-left: 20px;
}

/*图标、文字竖直排列的按钮-------------------------*/
// .m-vertical-btn-nogrey{
//     .el-button.is-disabled, .i-reportFoldMenuPanel .el-button.is-disabled{
//         color: #606266;
//     }
// }
.m-vertical-btn.el-button {
  height: auto;
  padding: 8px;
}

@keyframes my-animation {
  0% { height: 70px; overflow: hidden; }
  100% {  height: auto; overflow: auto; }
}

@media screen and (max-width: 1280px) {
    .m-vertical-btn.el-button {
        padding: 8px 2px;
    }
    .m-businessMenu {
        animation: my-animation 1s ease-in-out;
    }
}
@media screen and (max-width: 1440px) {
    .m-vertical-btn.el-button {
        padding: 8px 4px;
    }
    .m-businessMenu {
        animation: my-animation 1s ease-in-out;
    }
}
.m-vertical-btn {
    position: relative;
    padding: 8px;
    height: auto;

    >span {
        display: block!important;

        svg {
            font-size: 24px !important;
        }

        >label {
            display: block;
            font-size: 12px;
            margin-top: 4px;

            &:hover {
                cursor: inherit;
            }
        }
    }

    &.m-vertical-text {
        // width: 66px;
        white-space: initial;
        height: auto;
        label {
            line-height: 18px;
        }
    }

    &.t-module,
    &.t2 {
        border: none;
        // padding: 5px;
        // margin: 0px 5px !important;
    }

    &.t-module {
        margin-left: 0;
        margin-right: 4px;
    }

    &.t2 {
        // &.t-border {
        //     margin-right: 20px !important;

        //     &:before {
        //         content: "";
        //         position: absolute;
        //         top: 15%;
        //         right: 0;
        //         margin-right: -12px;
        //         bottom: 15%;
        //         width: 1px;
        //         background-color: #ccc;
        //         z-index: 1;
        //     }
        // }
    }

    &.is-disabled {
        svg {
            opacity: 0.5;
        }
    }
}

.el-button+.el-button.m-vertical-btn {
    &.t2 {
        margin-left: 0;
        min-width: 4em;
    }
}

/*-------------------------图标、文字竖直排列的按钮*/
/*弹性布局Y---------------------------------*/
.m-flexLaout-ty,
.m-flexLaout-tx {
    display: flex;
    height: 100%;

    >div:not(.g-flexChild) {
        flex-shrink: 0;
    }

    >.g-flexChild {
        flex: 1;
        overflow: auto;
    }
}

.m-flexLaout-ty {
    flex-direction: column;
}

.m-flexLaout-tx {

    flex-direction: row;
}

/*---------------------------------弹性布局Y*/
.e-btn.t-default,
button.t-default {

    //鼠标经过
    &:hover {
        opacity: .85;
    }

    //鼠标按下
    &:active {
        opacity: 1;
    }
}

/* 表单元素布局容器--------------------------------- */
.c-formItem {
    float: left;
    min-height: 30px;
    margin: 13px 0 0 10px;

    >.el-radio-group {
        margin-top: 8px;
    }
}

/* ---------------------------------表单元素布局容器 */
/*-------------------------------------------样式组件*/
/*border------------------------------------------*/
.u-border {
    border: solid 1px $borderColor;

    &.t-2 {
        border-color: $borderColor2;
    }
}

.u-border-top {
    border: solid 1px transparent;
    border-top-color: $borderColor;

    &.t-2 {
        border-top-color: $borderColor2;
    }
}

.u-border-right {
    border: solid 1px transparent;
    border-right-color: $borderColor;

    &.t-2 {
        border-right-color: $borderColor2;
    }
}

.u-border-bottom {
    border: solid 1px transparent;
    border-bottom-color: $borderColor;

    &.t-2 {
        border-bottom-color: $borderColor2;
    }
}

.u-border-left {
    border: solid 1px transparent;
    border-left-color: $borderColor;

    &.t-2 {
        border-left-color: $borderColor2;
    }
}

/*------------------------------------------border*/

/*按钮区域------------------------------------------*/
.u-btnArea {
    padding: 10px;

    &.t-right {
        text-align: right;
    }
}

.u-btnArea-narrow {
    padding: 5px;

    &.t-right {
        text-align: right;
    }
}

/*------------------------------------------按钮区域*/

/*高度充满-------------------------------------------*/
.u-fullHeight {
    height: 100%;
}

/*-------------------------------------------高度充满*/
/*高度自动-------------------------------------------*/
.u-autoHeight {
    height: auto;
}

/*-------------------------------------------高度自动*/
/*自适应尺寸------------------------------------------*/
.u-selfAdaption {
    width: 100%;
    height: 100%;
}

/*------------------------------------------自适应尺寸*/

/*单行超出省略-----------------------------------------*/
.u-text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    /*超出显示省略号*/
    white-space: nowrap;
    /*强制单行显示*/
}

/*-----------------------------------------单行超出省略*/
.u-indent {
    padding-left: 20px;
}


/* autocomplete-------------------------------------------------- */
.i-autocomplete1 {
    width: 603px !important;
}

/* --------------------------------------------------autocomplete */

/*一组输入框的间距-----------------------------------------*/
.m-input-padding {
    padding: 15px 15px 10px 10px;
    border-top: 10px solid #eee;
}

// 禁止用鼠标选择功能
.no-user-select {
    -moz-user-select: none;
    -khtml-user-select: none;
    user-select: none
}

@keyframes fade {
    from {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    to {
        opacity: 1;
    }
}
