<template>
    <!-- 项目设置 项目 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div>
                <el-button-icon-fa type="primary" 
                icon="el-icon-plus"
                @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    icon="el-icon-refresh"
                    @click="mxGetTableList">刷新</el-button-icon-fa> 
            </div>
            <div class="search">
                <div>
                    <el-select 
                        style="width:100%"
                        clearable=""
                        v-model="condition.sDeviceTypeId"
                        placeholder="设备类型"
                        @change="mxDoSearch">
                        <el-option v-for="(item, index) in optionsLoc.deviceTypeOptions"
                            :key="index"
                            :value="item.sId"
                            :label="item.sDeviceTypeName"></el-option>
                    </el-select>
                </div>
                <div>
                    <el-input 
                        inline
                        @keyup.enter.native="mxDoSearch"
                        v-model="condition.sItemName" 
                        placeholder="项目名称"></el-input>
                </div>
                <div>
                    <el-select v-model="condition.iIsEnable"
                        style="width: 100%;"
                        clearable
                        placeholder="启用"
                        @change="mxDoSearch">
                        <el-option v-for="item in optionsLoc.isEnable"
                            :key="item.sValue"
                            :label="item.sName"
                            :value="item.sValue"></el-option>
                    </el-select>
                </div>
                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch"></el-button-icon-fa>
                </div>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content">
                <el-table :data="tableData"
                    v-drag:[config]="tableData"
                    :row-class-name="tableRowClassName"
                    ref="mainTable"
                    size="small"
                    id="itemTable"
                    @row-click="onClickRow"
                    v-if="reRender"
                    v-loading="loading"
                    :default-sort="{prop: 'sDeviceTypeName', order: 'ascending'}"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                        <el-table-column show-overflow-tooltip
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort"
                            >
                            <template v-slot="scope">
                                <template v-if="item.sProp === 'action'">
                                    <el-button size="small"
                                        link
                                        type="primary"
                                        @click="handleEdit(scope.row)"
                                        >编辑
                                        <template #icon>
                                            <Icon name="el-icon-edit" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button size="small" link class @click="onClickDel(scope.row)">
                                        删除
                                        <template #icon>
                                            <Icon name="el-icon-delete" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button size="small" link class="i-sort">排序
                                        <template #icon>
                                            <Icon name="el-icon-rank" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                </template>
                                <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                    {{ scope.row[`${item.sProp}`] | mxToDate() }}
                                </template>
                                <template v-else-if="item.sProp === 'iIsEnable'">
                                    <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                        v-model="scope.row.iIsEnable"
                                        :active-value="1"
                                        :inactive-value="0"></el-switch> -->
                                    <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                    <span v-else> 否 </span>
                                </template>
                                <template v-else>
                                    {{scope.row[`${item.sProp}`]}}
                                </template>
                            </template>
                            <!-- <template v-slot:header>
                                <span>{{item.sLabel}}</span>
                                <i v-if="item.sProp === 'action'"
                                    class="el-icon-rank i-sort"
                                    style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                    title="首次或无法排序时，点击初始化排序"
                                    @click="autoSort"></i>
                            </template> -->
                        </el-table-column>
                    </template>
                </el-table>
            </div>
            
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog">
            <div class="flex">
                <el-form :model="editLayer.form"
                    ref="refEditLayer"
                    :rules="rules"
                    label-width="100px"
                    label-position="right">
                    <el-col :span="24">
                        <el-form-item prop="sDeviceTypeId" label="设备类型：">
                            <el-select 
                                style="width:100%"
                                clearable=""
                                v-model="editLayer.form.sDeviceTypeId"
                                placeholder="设备类型"
                                @change="mxDoSearch">
                                <el-option v-for="(item, index) in optionsLoc.deviceTypeOptions"
                                    :key="index"
                                    :value="item.sId"
                                    :label="item.sDeviceTypeName"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sItemName" label="项目名称：">
                            <el-input 
                                v-model="editLayer.form.sItemName"
                                placeholder="项目名称"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                  
                    <el-col :span="24">
                        <el-form-item label="启  用：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>

                </el-form>
            </div>
            <template #footer><div  class="dialog-footer">
                <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="mxDoSaveData('refEditLayer')">保存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SetItem',
    mixins: [mixinTable, mixinTableDrag],
    props: {
    },
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            defaultProps: {
                children: 'children',
                label: 'sDeviceTypeName'
            },
            selectedNode: {},
            configTable: [
                
                {
                    sProp: 'sDeviceTypeName', sLabel: '设备类型',
                    sAlign: 'left', sMinWidth: '40px',
                    // iSort: 1
                },
                {
                    sProp: 'sItemName', sLabel: '项目名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                // {
                //     sProp: 'sItemCode', sLabel: '项目编号',
                //     sAlign: 'left', sMinWidth: '100px',
                // },
                // {
                //     sProp: 'sItemPrefix', sLabel: '项目前缀',
                //     sAlign: 'left', sMinWidth: '100px',
                // },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '120px',
                },
                {
                    sProp: 'action', sLabel: '操作',
                    sAlign: 'center', sWidth: '220px',
                }
            ],
            rules: {
                sDeviceTypeId:[{required: true, message: '请选择设备类型'}],
                sItemName: [{ required: true, message: '请输入名称' }],
                sItemCode: [{ required: true, message: '请输入编码' }]
            },
            tableData: [],
            reRender: true,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                    sDeviceTypeId:''
                }
            },
            condition: {
                iIsEnable: ''
            },
            optionsLoc: {
                isEnable: [
                    { sValue: '', sName: '全部' },
                    { sValue: 1, sName: '启用' },
                    { sValue: 0, sName: '禁用' },
                ],
                deviceTypeOptions: []
            },
            sortApi: Api.sortItem,
        }
    },
    methods: {
        tableRowClassName({ row, rowIndex }) {
            if (this.dragRow.sDeviceTypeId && row.sDeviceTypeId !== this.dragRow.sDeviceTypeId) {
                return "row-filtered";
            }
            return "";
        },
        handleAdd() {
            if(this.optionsLoc.deviceTypeOptions.length) {
                this.defualtVal.editLayer.sDeviceTypeId = this.optionsLoc.deviceTypeOptions[0].sId
            }
            this.mxOpenDialog(1)
            this.dialogTitle = '新增'
            this.dialogVisible = true
            let timeout = setTimeout(()=>{
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({},row)
            this.$nextTick(()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledItem({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sItemName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delItem({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            let target= this.optionsLoc.deviceTypeOptions.find(item => item.sId === params.sDeviceTypeId)
            if(target) {
                params.sDeviceTypeCode = target.sDeviceTypeCode
                params.sDeviceTypeName = target.sDeviceTypeName
                params.sDeviceTypeId = target.sId
            }
            if (!params.sId) {
                Api.addItem(params).then((res) => {
                    this.editLayer.loading = false;   
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.dialogVisible = false
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            } else {
                Api.editItem(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.dialogVisible = false
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            const jsonData  = params.condition
            Api.getItemData(jsonData).then((res) => {
                if (res.success) {
                    this.tableData = res?.data || [];
                    // this.$nextTick( ()=>{
                    //     this.$refs.mainTable.sort('sDeviceTypeName', 'ascending')
                    // })
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        // 获取设备下拉
        getDeviceOptions () {
            Api.getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.optionsLoc.deviceTypeOptions = res?.data || [];
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => {
            })
        },
    },
    mounted () {
        this.getDeviceOptions()
        this.mxOpenDialog(1, 'no-title')
    },
};
</script>
<style lang="scss" scoped>

.c-form .search {
    width: 50%;
    min-width: 600px;
    display: flex;
    >div{
        margin:0 5px;
        width: 33.3%;
    }
}


:deep(.el-button--text .el-icon-delete) {
    color: #f56c6c;
}
:deep(.el-button--text .el-icon-rank) {
    color: #333;
}
.i-sort {
    font-size: 16px;
}
</style>
