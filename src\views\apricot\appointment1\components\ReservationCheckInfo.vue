<template>
    <FormList
    storageKey="reservation-patient"
    iModuleId="2"
    :list="formList"
    :formData="props.formData"
    :optionData="props.optionData">
        <!-- 院区 -->
        <template v-slot:sDistrictId="{ row, style }">
            <el-select v-model="props.formData[row.sProp]" :style="style" v-select-name="{ formData: props.formData, fields: row }"
                placeholder=""
                @change="changeMachineHospital">
                <el-option v-for="item in props.districtData"
                    :key="item.sHospitalDistrictId"
                    :label="item.sDistrictPrefix"
                    :value="item.sHospitalDistrictId"></el-option>
            </el-select>
        </template>
        <!-- 机房 -->
        <template v-slot:sMachineryRoomId="{ row, style }">
            <el-select v-model="props.formData[row.sProp]" :style="style" v-select-name="{ formData: props.formData, fields: row }"
                placeholder="">
                <el-option v-for="item in props.optionData.machineRoomOptions"
                    :key="item.sRoomId"
                    :label="item.sRoomName"
                    :value="item.sRoomId"></el-option>
            </el-select>
        </template>
        <!-- 检查项目 -->
        <template v-slot:sProjectId="{ row, style }">
            <el-select v-model="props.formData[row.sProp]" v-select-name="{ formData: props.formData, fields: row }"
                :style="style"
                placeholder="">
                <el-option v-for="item in props.optionData.projectOptions"
                    :key="item.sValue"
                    :label="item.sName"
                    :value="item.sValue"></el-option>
            </el-select>
        </template>
        <!-- 核医学号 -->
        <template v-slot:sNuclearNum="{ row, style }">
            <el-input class="i-age"
                v-model="props.formData[row.sProp]"
                :readonly="!!row.iReadonly"
                :style="style">
                <template #append>
                    <el-button
                        size="small"
                        link
                        style="padding:0 5px"
                        :loading="loadingNuclearNum"
                        @click="getNuclearNum()">
                        <template #icon>
                            <el-icon><Refresh /></el-icon>
                        </template>
                        </el-button>
                </template>
            </el-input>
        </template>
        <!-- 核素 -->
        <template v-slot:sNuclideText="{ row, style }">
            <el-select v-if="configData?.medicineChooseType === 2"  
                v-model="props.formData.sNuclide"
                filterable
                clearable
                placeholder=" "
                :style="style"
                @change="onMedicineChange">
                <el-option v-for="item in nuclideOptions"
                    :key="item.sId"
                    :label="item.sNuclideName"
                    :value="item.sId" />
            </el-select>  
            <el-input v-else
                v-model="props.formData[row.sProp]" 
                readonly
                :style="style"
                @click="(e) => showLogin(e, row.sProp)"></el-input>
        </template>
        <!-- 示踪剂 -->
        <template v-slot:sTracerText="{ row, style }">
            <el-select v-if="configData?.medicineChooseType === 2"  
                v-model="props.formData.sTracer"
                filterable
                clearable
                placeholder=" "
                :style="style"
                @change="onMedicineChange">
                <el-option v-for="item in tracerOptions"
                    :key="item.sId"
                    :label="item.sTracerName"
                    :value="item.sId" />
            </el-select>  
            
            <el-input v-else
                v-model="props.formData[row.sProp]" 
                readonly
                :style="style"
                @click="(e) => showLogin(e, row.sProp)"></el-input>
        </template>
        <!-- 检查部位 -->
        <template v-slot:sPosition="{ row, style }">
            <el-select v-model="props.formData[row.sProp]"
                v-select-name="{ formData: props.formData, fields: row }"
                filterable
                clearable
                placeholder=" "
                :style="style"
                @change="onMedicineChange">
                <template v-if="configData?.medicineChooseType === 2">
                    <el-option v-for="item in itemPositionOptions"
                        :key="item.sId"
                        :label="item.sItemPositionName"
                        :value="item.sId" />
                </template>
                <template v-else >
                    <el-option-group v-for="(group, index) in itemPositionList.filter(o => o.options.length)"
                        :key="index"
                        :label="group.label">
                        <el-option v-for="item in group.options"
                            :key="item.sId"
                            :label="item.sItemPositionName"
                            :value="item.sId" />
                    </el-option-group>
                </template>
            </el-select>
        </template>
        <!-- 检查方式 -->
        <template v-slot:sTestMode="{ row, style }">
            <el-select v-model="props.formData[row.sProp]"
                v-select-name="{ formData: props.formData, fields: row }"
                filterable
                clearable
                placeholder=" "
                :style="style"
                @change="onMedicineChange">
                <template v-if="configData?.medicineChooseType === 2">
                    <el-option v-for="item in testModeOptions"
                            :key="item.sId"
                            :label="item.sTestModeName"
                            :value="item.sId" />
                </template>
                <template v-else>
                    <el-option-group v-for="(group, index) in testModeList.filter(o => o.options.length)"
                        :key="index"
                        :label="group.label">
                        <el-option v-for="item in group.options"
                            :key="item.sId"
                            :label="item.sTestModeName"
                            :value="item.sId" />
                    </el-option-group>
                </template>
            </el-select>
        </template>
        <!-- 处方剂量 -->
        <template v-slot:fRecipeDose="{ row, style }">
            <el-input class="i-top"
                v-model="props.formData[row.sProp]"
                :readonly="!!row.iReadonly"
                :style="style">
                <template #append>
                    <el-select
                        v-model="props.formData.sRecipeDoseUnit"
                        placeholder="选择" 
                        :style="style"
                        style="width:80px;height:100%">
                        <el-option v-for="item in props.optionData.ApricotReportDoseUnit"
                            :key="item.sValue"
                            :label="item.sName"
                            :value="item.sName"></el-option>
                    </el-select>
                </template>
            </el-input>
        </template>
    </FormList>


    <el-popover
        v-if="popVisible"
        placement="bottom"
        :width="670"
        trigger="click"
        v-model:visible="popVisible"
        virtual-triggering
        :virtual-ref="tempRef"
        popper-class="pop-class"
        @show="onShowItemSetPop">
            <div style="margin-bottom: 10px;">
                <el-input v-model="medicationCondition.nuclearName" 
                    placeholder="核素" 
                    clearable
                    style="width: 148px;margin-right: 4px"></el-input>
                <el-input v-model="medicationCondition.tracerName" 
                    placeholder="示踪剂" 
                    clearable
                    style="width: 148px"></el-input>
                <el-input ref="sTextInput"
                    style="width: 0;opacity: 0;"></el-input>
                 <i class="el-icon-s-tools float-right"
                    @click="handlerSetCheckMedicine"
                    style="float: right;font-size: 22px;position: relative;top: 6px;cursor: pointer;"></i>
            </div>
            <el-table :data="nuclideParamOptions.filter(item => item.sNuclideName.toLowerCase().includes(medicationCondition.nuclearName.toLowerCase())).filter(item =>item.sTracerName.toLowerCase().includes(medicationCondition.tracerName.toLowerCase()))"
                ref="medicineRef"
                highlight-current-row
                row-key="sId"
                size="small"
                border
                max-height="350"
                @row-click="(item) => nuclearTableRowClick(item)">
                <el-table-column width="100"
                    property="sNuclideName"
                    label="核素"
                    show-overflow-tooltip></el-table-column>
                <el-table-column min-width="100"
                    property="sTracerName"
                    label="示踪剂"
                    show-overflow-tooltip></el-table-column>
                <el-table-column width="100"
                    property="sItemPositionName"
                    label="检查部位"
                    show-overflow-tooltip></el-table-column>
                <el-table-column width="120"
                    property="sTestModeName"
                    label="检查方式"
                    show-overflow-tooltip>
                </el-table-column>
                <el-table-column width="60"
                    property="iIsInvariable"
                    label="定量"
                    show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ row.iIsInvariable ? '是' : '否'}}
                    </template>
                    </el-table-column>
                <el-table-column width="80"
                    property="fCoefficient"
                    label="处方系数"
                    show-overflow-tooltip></el-table-column>
                <el-table-column width="80"
                    property="fDosage"
                    label="定量剂量"
                    show-overflow-tooltip></el-table-column>
            </el-table>
            <div style="margin-top: 10px;text-align: right;">
                <el-button-icon-fa
                    icon="fa fa-close-1"
                    @click="popVisible = false">关闭</el-button-icon-fa>
            </div>
    </el-popover>

</template>
<script setup>
    import {nextTick} from 'vue'
    /**
     * 预约单-检查信息
     */
    import { Close, Refresh } from '@element-plus/icons-vue'
    import { ElMessage } from 'element-plus'
    import { useRouter } from 'vue-router'
    import { useStore } from 'vuex'

    import { transformDate } from '$supersetResource/js/tools'
    import { getNuclearNum as apiGetNuclearNum} from '$supersetApi/projects/apricot/appointment/index.js'
    import { getItemData, getItemSetData as apiGetItemSetData, getItemPositionData, getTestModeData, 
        getNuclideData, getTracerData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
    import ConfigsItems from '../configs/configsItems.js'
    import useUserConfig from '../useUserConfig.js'

    const { configData } = useUserConfig();

    const props = defineProps({
        visible: false,   // 打开弹窗
        formData: {
            type: Object,
            default: () => ({})
        },
        configBtn: {},
        rules: {},
        refEditLayer: null,
        districtData: Array, // 院区、机房,
        defaultCheckInfo: Object,  // 外边日历选中的院区、机房
        optionData: {},   // 下拉数据
        
    })

    const proxy = getCurrentInstance();

    const formList = ref(ConfigsItems.appointmentInputList2);
    

    const popVisible= ref(false);
    const tempRef = ref();
    const keyWordProp = ref(null);
    const medicationCondition = ref({nuclearName: '', tracerName: ''})
    const showLogin = (e, prop)=> {
        const evt = e || window.e || window.event;
        if(keyWordProp.value === prop && popVisible.value) {
            popVisible.value = false;
            return
        }
        if (tempRef) popVisible.value = false;
        medicationCondition.value.nuclearName = '';
        medicationCondition.value.tracerName = '';
        nextTick(() => {
            tempRef.value = evt.currentTarget
            popVisible.value = true;
            keyWordProp.value = prop;
            // onShowItemSetPop()
        })
    }

    // 选中院区
    function changeMachineHospital(id) {
        const item = props.districtData.find(item => item.sHospitalDistrictId == id)
        // console.log('选择院区')
        if (item) {
            // console.log('清除机房，获取机房列表')
            props.formData.sMachineryRoomId = ''
            props.optionData.machineRoomOptions = item.rooms
            

            props.formData.sConsultRoomId = ''
            props.formData.sInjectionRoomId = ''
            
            const consultRoom   = item.workstationMap[3] || []
            const injectionRoom = item.workstationMap[4] || []

            if (consultRoom) {
                props.optionData.consultRoomOption = consultRoom.map(room =>{
                    room.sValue = room.sStationId
                    room.sName = room.sStationName
                    return room
                }).filter(room => room.enable)
            }
            if (injectionRoom) {
                props.optionData.injectionRoomOption = injectionRoom.map(room =>{
                    room.sValue = room.sStationId
                    room.sName = room.sStationName
                    return room
                }).filter(room => room.enable)
            }
        }
    }
    
    // 选择机房时
    const deviceTypeId = ref(null)
    /**
     * value 机房id
     * firstLoad false 不是第一次加载
     */
    function changeMachineRoom(value, firstLoad = false) {
        if (value) {
            // console.log('选择机房')
            const item = props.optionData.machineRoomOptions?.find(item => item.sRoomId == value )
            if (item) {

                if (!(firstLoad && props.formData.sId)) {
                    // 第一次加载，已经预约的患者，不执行任何操作
                    // 赋值问诊室，注射室
                    props.formData.sConsultRoomId = item.sConsultRoomId;
                    props.formData.sInjectionRoomId = item.sInjectionRoomId;
                }
                // console.log('获取项目列表')
                deviceTypeId.value = item.sDeviceTypeId
                handleGetItemData(item.sDeviceTypeId)
            }
        }else {
            props.formData.sProjectId = ''
        }
    }

    watch(() => props.formData.sMachineryRoomId, (value, oldValue) => {
        // 在弹窗后在监听，如果之前的值是未定义不获取（未预约）
        if (props.visible && oldValue !== undefined) {
            changeMachineRoom(value);
            if(!props.formData.sId && value !== oldValue) {
                nuclideParamOptions.value = [];
                nuclearTableRowClick({});
            }
        }
    })
    
    // 获取检查项目
    async function handleGetItemData (val) {
        let params = {
            sDeviceTypeId: val,
            iIsEnable: 1
        }
        await getItemData(params).then((res) => {
            if (res.success) {
                props.optionData.projectOptions = res?.data || [];
                props.optionData.projectOptions.map(item => {
                    item['sValue'] = item.sId
                    item['sName'] = item.sItemName
                })

                // 不存该项目-清除
                const findObj = props.optionData.projectOptions?.find(item => item.sValue == props.formData.sProjectId)
                // console.log(props.optionData.projectOptions, props.formData.sProjectId)
                if (!findObj) {
                    props.formData.sProjectId = ''
                }

            } else {
                ElMessage.error(res.msg)
            }
        }).catch((err) => {
            console.log(err)
        })
    }

    // 获取核医学号
    const loadingNuclearNum = ref(false)

    watch(() => props.formData.sProjectId, (value, later) => {
        // console.log('props.formData.sProjectId=', props.formData.sProjectId)
        nextTick(async () => {
            if(!value) {
                nuclideParamOptions.value = [];
                nuclearParams.value = {};
                emits('getNuclearParams', nuclearParams.value)
                return
            }
            if (!props.defaultCheckInfo.isHistory && !isApplyAppoint.value && props.visible) {
                // 不是历史患者且不是带核医学号的申请单患者
                getNuclearNum()
            }
            const medicineCache =  getMedicineCache();
            const medicineChooseType = configData?.value?.medicineChooseType ?? 1;
            if(medicineChooseType == 1) {
                // console.group('关联模式读取')
                await getItemSetData();
            } 
            if(medicineChooseType == 2) {
                // console.group('随机模式读取')
                await handleGetNuclideData();
                await handleGetTracerData();
                !medicineCache[value] && !props.formData.sId && nuclearTableRowClick({});
                !medicineCache[value] && !props.formData.sId && await getItemSetData();
                const len = Object.keys(nuclearParams.value).length;
                !medicineCache[value] && !props.formData.sId && len && onMedicineChange();
            }
            await handleGetItemPositionData();
            await handleGetTestModeData();
            if(medicineChooseType == 2 && medicineCache[value] && !props.formData.sId){
                // console.group('从缓存赋值给表单')
                Object.assign(props.formData, medicineCache[value]);  
                const { sNuclide, sTracer } = medicineCache[value];
                setParamsOfNuclideToForm(sNuclide);
                setParamsOfTracerToForm(sTracer);
            }
        })
    }, { immediate: true })

    watch(() => props.formData.numSourceId, (value) => {
        nextTick(() => {
            if(props.visible && value && props.formData.isCheckNumberSource)  {
                if (!props.formData.sProjectId || props.formData.sId || props.defaultCheckInfo['isHistory'] || props.formData.sNuclearNum) return
                // console.log('props.formData.numSourceId')
                getNuclearNum()
            }
        })
    })

    function getNuclearNum () {
        if (!props.formData.sProjectId) {
            ElMessage.warning('请选择检查项目！')
            return
        }
        loadingNuclearNum.value = true;
        const sDate = props.formData.sAppointmentTime ? moment(props.formData.sAppointmentTime).format('YYYY-MM-DD') : '';
        const sTime = props.formData.sTimeText ||  '';
        const appointmentTime = sDate && sTime ? moment(sDate + " " +  sTime).format('YYYY-MM-DD HH:mm:ss') : '' ;
        let jsonData = {
            sDeviceTypeId: deviceTypeId.value,
            sItemId: props.formData.sProjectId,
        }
        if(appointmentTime) {
            jsonData.appointmentTime = appointmentTime;
        }
        apiGetNuclearNum(jsonData).then(res => {
            if (res.success) {
                props.formData.sNuclearNum = res.data || ''
                return
            }
            ElMessage.closeAll();
            // ElMessage.error(res.msg)
        }).catch(err => {
            props.formData.sNuclearNum = ''
        }).finally(err => {
            loadingNuclearNum.value = false;
            ElMessage.closeAll();
        })
    }

    const sTextInput = ref(null);
    // 查询检查用药信息
    async function onShowItemSetPop() {
        props.formData.sProjectId && await getItemSetData();
        medicineRef.value && medicineRef.value.setCurrentRow(nuclearParams.value || {});
        sTextInput.value && sTextInput.value.focus();
    }

    // 获取核素用药相关信息
    const nuclideParamOptions = ref([])
    async function getItemSetData () {
        await apiGetItemSetData({
           sItemId: props.formData.sProjectId
        }).then(res => {
            if (res.success) {
                nuclideParamOptions.value = res?.data || [];
                if(props.formData.sId) {
                    findNuclearRow(nuclideParamOptions.value);
                    return;
                }
                if(nuclideParamOptions.value.length && props.formData.sProjectId !== nuclearParams.value.sItemId) {
                    nuclearTableRowClick(nuclideParamOptions.value[0] || {});
                }
                return
            }
            nuclideParamOptions.value = [];
            ElMessage.error(res.msg);
        }).catch(err => { 
            nuclideParamOptions.value = [];
            console.log(err);
        })
    }
    const nuclearParams = ref({}) // 用药参数,
    const emits = defineEmits(['getNuclearParams'])
    const medicineRef = ref(null)
    function findNuclearRow(arr) {
        if(!arr.length) {
            return
        }
        let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode'];
        let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sItemPositionName', 'sItemPositionId', 'sTestModeName', 'sTestModeId'];
        let finalItem = arr.find(item => {
            let isEq = true;
            oKeys.map((key, index) => {
                if(props.formData[formKeys[index]] && item[key] !== props.formData[formKeys[index]]){
                    isEq = false;
                }
            })
            let targetItem  = null;
            if(isEq) {
                targetItem = item;
            }
            return targetItem;
        })
        if(finalItem) {
            nuclearParams.value = finalItem
            emits('getNuclearParams', nuclearParams.value);
        }
    }
    // 核素相关表格行点击事件
    function nuclearTableRowClick (row, sProp) {
        nuclearParams.value = row
        emits('getNuclearParams', nuclearParams.value)
        // 核素、示踪剂、检查部位、检查方式相关字段的赋值
        let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode'];
        let originKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sItemPositionName', 'sItemPositionId', 'sTestModeName', 'sTestModeId'];
        formKeys.map((item, index) => {
            props.formData[item] = row[originKeys[index]];
        })
        sProp && proxy.refs[sProp].hide();
        popVisible.value = false;
        if (!Object.keys(row).length) {
            // 没有数据时
            props.formData['fRecipeDose'] = undefined;
            return
        }
        if (row.iIsInvariable) {
            props.formData['fRecipeDose'] = row.fDosage;
            return
        }
        let fWeight = props.formData.fWeight;
        props.formData['fRecipeDose'] = (fWeight >= 0 && row.fCoefficient) ? (fWeight * row.fCoefficient).toFixed(2) : undefined;
    }

    // 核素
    const nuclideOptions = ref([]);
    async function handleGetNuclideData () {
        await getNuclideData({}).then(res => {
            if (res.success) {
                nuclideOptions.value = res?.data || [];
                return
            }
            nuclideOptions.value = []
        }).catch(err => {
            nuclideOptions.value = []
        })
    }

    // 示踪剂
    const tracerOptions = ref([]);
    async function handleGetTracerData () {
        await getTracerData({}).then(res => {
            if (res.success) {
                tracerOptions.value = res?.data || [];
                return
            }
            tracerOptions.value = []
        }).catch(err => {
            tracerOptions.value = []
        })
    }

    // 检查部位
    const itemPositionOptions = ref([]);
    async function handleGetItemPositionData () {
        await getItemPositionData({}).then(res => {
            if (res.success) {
                itemPositionOptions.value = res?.data || [];
                return
            }
            itemPositionOptions.value = []
        }).catch(err => {
            itemPositionOptions.value = []
        })

    }
    // 检查方式
    const testModeOptions = ref([]);
    async function handleGetTestModeData () {
        await getTestModeData({}).then(res => {
            if (res.success) {
                testModeOptions.value = res?.data || [];
                return
            }
            testModeOptions.value = []
        }).catch(err => {
            testModeOptions.value = []
        })

    }

    const itemPositionList = computed(() => {
    const list = [
            {
                label: '已配置项',
                options: getReorganizeOptions(itemPositionOptions.value, 'sItemPositionId' , true)
            },
            {
                label: '未配置项',
                options: getReorganizeOptions(itemPositionOptions.value, 'sItemPositionId', false)
            },
        ];
        return list
    })

    const testModeList = computed(() => {
        const list = [
            {
                label: '已配置项',
                options: getReorganizeOptions(testModeOptions.value, 'sTestModeId' , true)
            },
            {
                label: '未配置项',
                options: getReorganizeOptions(testModeOptions.value, 'sTestModeId', false)
            },
        ];
        return list
    })

    function getReorganizeOptions (origin, key, isSetList) {
        var arr = [];
        var ids = [];
        if (key === 'sNuclideId') {
            ids = [... new Set(nuclideParamOptions.value.map(o => o[key]))];
        } else {
            ids = [... new Set(nuclideParamOptions.value.filter(o => o.sNuclideId === props.formData.sNuclide).map(o => o[key]))]
        }
        if (isSetList) {
            ids.map(id => {
                let temp = origin.find(o => o.sId === id);
                if (temp) {
                    arr.push(temp)
                }
            })
            return arr
        } else {
            return origin.filter(item => !ids.includes(item.sId))
        }
    }
    // 选中核素相关参数赋值给表单对象
    function setParamsOfNuclideToForm(id) {
        const tempItem = nuclideOptions.value.find(item => item.sId === id);
        if(!tempItem) return
        props.formData.sNuclideText = tempItem.sNuclideName;
        props.formData.sNuclideSupName = tempItem.sNuclideSupName;
    }
    // 选中示踪剂相关参数赋值给表单对象
    function setParamsOfTracerToForm(id) {
        const tempItem = tracerOptions.value.find(item => item.sId === id);
        if(!tempItem) return
        props.formData.sTracerText = tempItem.sTracerName;
        props.formData.sTracerSupName = tempItem.sTracerSupName;
    }

    function onMedicineChange() {
        if(configData?.value?.medicineChooseType == 1) return;
        const { sNuclide, sTracer, sPosition, sTestMode, sProjectId } = props.formData;
        setParamsOfNuclideToForm(sNuclide);
        setParamsOfTracerToForm(sTracer);
        const chooseMedicine = {};
        chooseMedicine[sProjectId] = { sNuclide, sTracer, sPosition, sTestMode };
        let len = Object.keys(chooseMedicine[sProjectId]).length;
        if(!len) return
        let targetCache = getMedicineCache();
        Object.assign(targetCache, chooseMedicine);
        localStorage.setItem('appointMedicineCache', JSON.stringify(targetCache));
    }

    function getMedicineCache() {
        let result = localStorage.getItem('appointMedicineCache');
        return result ? JSON.parse(result) : {}; 
    }

    const router = useRouter()
    const store = useStore()
    // 打开药物设置页面
    function handlerSetCheckMedicine () {
        // 打开系统设置里的 ‘设备机房、检查项目’ 页面
        store.commit({
            type: 'module_router/switchModuleRouter',
            activeRouterName: 'apricot_System'
        })
        setTimeout(() => {
            router.push({ path: 'apricot_System', query: { activeIndex: 'ProjectSet', activePanel: 'CheckMedicine' } });
        }, 100)
    }
    // 是否从申请单预约
    var isApplyAppoint = ref(false);
    // 打开弹窗监听，获取下拉数据
    watch(() => props.visible, (later) => {
        if (later) {
            isApplyAppoint.value = false;
            // 赋初值院区
            const sConsultRoomId = props.formData.sConsultRoomId
            const sInjectionRoomId = props.formData.sInjectionRoomId

            if (props.defaultCheckInfo.hospitalId) {
                props.formData.sDistrictId = props.defaultCheckInfo.hospitalId
                // 触发选中院区、获取机房
                changeMachineHospital(props.formData.sDistrictId)
            }
            // changeMachineHospital 会清除选中问诊室、注射室
            props.formData.sConsultRoomId = sConsultRoomId
            props.formData.sInjectionRoomId = sInjectionRoomId
            
            // 赋初值机房
            if (props.defaultCheckInfo.roomId) {
                props.formData.sMachineryRoomId = props.defaultCheckInfo.roomId
                // 触发选中机房，获取项目列表等
                changeMachineRoom(props.formData.sMachineryRoomId, true)
                // 初始获取核医学号
                if (!props.defaultCheckInfo.isHistory && props.formData.sProjectId) {
                    // console.log('watch:visible::props.formData.sNuclearNum', props.formData.sNuclearNum);
                    // 申请单信息的核医学号没有值的时候，根据查询新的核医学号
                    if(props.formData.sNuclearNum) {
                        isApplyAppoint.value = true;
                    }
                    // else  {
                    //     getNuclearNum()
                    // }
                }
            }
        }
    }, { immediate: true, deep: true })
</script>