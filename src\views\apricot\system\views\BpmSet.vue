<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa> 
            </div>
            
            <div class="c-form-search">
                <div>
                    <el-input v-model="condition.sKey"
                        clearable=""
                        placeholder="参数名称"
                        @keyup.enter.native="mxDoSearch()"
                        >
                    </el-input>
                </div>
                <div>
                    <el-input v-model="condition.sName"
                        clearable=""
                        placeholder="参数含义"
                        @keyup.enter.native="mxDoSearch()"
                        >
                    </el-input>
                </div>
                <div>
                    <el-input v-model="condition.sValue"
                        clearable=""
                        placeholder="参数值"
                        @keyup.enter.native="mxDoSearch()"
                        >
                    </el-input>
                </div>
                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch" :loading="loading"></el-button-icon-fa>
                </div>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content" v-loading="loading">
                <!-- @row-click="handleRowClick"  -->
                <el-table :data="tableData" id="itemTable" ref="mainTable" size="small"
                @row-dblclick="handleRowClick"
                 border stripe height="100%" style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)" 
                        show-overflow-tooltip 
                        :key="item.index" 
                        :prop="item.sProp" 
                        :label="item.sLabel" 
                        :fixed="item.sFixed" 
                        :align="item.sAlign" 
                        :width="item.sWidth"
                        :min-width="item.sMinWidth" 
                        :sortable="!!item.iSort" 
                        >
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button size="small" type="primary" link @click="handleEdit(scope.row)">编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class @click="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class="i-sort">排序
                                    <template #icon>
                                        <Icon name="el-icon-rank" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                        <!-- <template v-slot:header>
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            v-model="dialogVisible"
            append-to-body
            class="t-default"
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex">
                <el-form :model="editLayer.form"
                    ref="refEditLayer"
                    label-width="100px"
                    :rules="rules">
                    <el-col :span="24">
                        <el-form-item label="参数名称：" prop="sKey" >
                            <el-input v-model="editLayer.form.sKey"
                                placeholder="参数名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="参 数  值：" prop="sValue">
                            <el-input v-model="editLayer.form.sValue"
                                placeholder="参数值"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="参数含义：" show-messageprop="sName">
                            <el-input v-model="editLayer.form.sName"
                                placeholder="参数含义"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="备    注：" prop="sMemo">
                            <el-input v-model="editLayer.form.sMemo" type="textarea" :rows="2" placeholder="备注"></el-input>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer><div  class="dialog-footer">
                <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保 存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取 消</el-button-icon-fa>
                
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/system/bpmSet.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'BpmSet',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            dialogVisible:false,
            dialogTitle:'',
            loading: false,
            configTable: [{
                sProp: 'sKey',
                sLabel: ' 参数名称',
                sAlign: 'left',
                sMinWidth: '60px',
                
            },
            {
                sProp: 'sValue',
                sLabel: '参数值 ',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'sName',
                sLabel: '参数含义',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'sMemo',
                sLabel: '备注',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '220px',
                
                // sFixed: "right"
            }],
            rules: {
                sKey: [{ required: true, message: '参数名称不能为空' }],
                sValue: [{ required: true, message: '参数值不能为空' }],
                // sKey: [{ required: true, message: '不能为空' }],
            },
            condition: {},
            actionState: null,
            editLayer: {
                form:{
                }
            }
        }
    },
    methods: {
        // 新增
        handleAdd() {
            this.actionState = 1
            this.dialogVisible = true
            this.dialogTitle = '新增'
            let params = {
                sKey:'',
                sValue: '',
                sName:'',
                sMemo:'',
            }     
            this.editLayer.form = Object.assign({},params)
            let timeout = setTimeout(() => {
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.selectedItem = row
            this.editLayer.form = Object.assign({},row)
            this.$nextTick( ()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
        },
        handleSave() {
            this.editLayer.loading = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)
                  return
                }
                this.editLayer.loading = false
            })
        },
        onChangeConditionKey(val) {
            this.selectKey = val
        },
        /**
         * 保存数据
         */
        saveData (params) {
            if (this.actionState == 1) {
                Api.addBPMSet(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.dialogVisible = false
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            } else {
                Api.editBPMSet(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.dialogVisible = false
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【${row.sKey}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delBPMSet({ sKey: row.sKey, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        // if (this.editLayer.form.sKey && this.editLayer.form.sKey === row.sKey) {
                        //     this.mxOpenDialog(1, 'no-title')
                        // }
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        // 自动排序
        autoSort () {
            if (!this.tableData.length) {
                return;
            }
            Api.autoSortBPMSet({}).then(res => {
                if (res.success) {
                    this.mxGetTableList();
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        //行拖拽
        rowDrop () {
            let dom = document.getElementById("itemTable");
            if (!dom) return;
            const tbody = dom.querySelector('.el-table__body-wrapper tbody');
            const _this = this;
            let updateFnc = ({ newIndex, oldIndex }) => {
                if (newIndex === oldIndex) return;
                Api.sortBPMSet({
                    iIndexOld: _this.tableData[oldIndex].iIndex,
                    iIndexNew: _this.tableData[newIndex].iIndex,
                    sId: _this.tableData[oldIndex].sId,
                    iVersion: _this.tableData[oldIndex].iVersion,
                }).then(res => {
                    if (res.success) {
                        _this.tableData[oldIndex].iVersion += 1
                        _this.tableData[newIndex].iVersion += 1
                        const currRow = _this.tableData.splice(oldIndex, 1)[0]
                        _this.tableData.splice(newIndex, 0, currRow)

                        _this.reRender = false
                        _this.$nextTick(() => {
                            _this.reRender = true
                            this.mxGetTableList(); // 由于其他的数据项item也发送了变化，需要那最新的
                            if (_this.editLayer.form.iVersion) _this.editLayer.form.iVersion += 1
                            _this.$nextTick(() => {
                                new Sortable(dom.querySelector('.el-table__body-wrapper tbody'), {
                                    animation: 200,
                                    onEnd: updateFnc
                                })
                                // 赋选中状态
                                this.mxSetSelected()
                            })
                        })
                    }
                })
            }
            new Sortable.create(tbody, {
                handle: ".i-sort",
                animation: 200,
                onEnd: updateFnc
            })
        },
        handleRowClick(row, isCancel = false, id = 'sId') {
            this.onClickRow(row, isCancel, id);
            this.mxOpenDialog(4, '111')
        },
        // 获取表格数据
        getData (params) {
            const p = Object.assign({}, params, {page: {pageSize: 500}})
            delete p.orders
            Api.getBPMSetData(p).then((res) => {
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.rowDrop()
        });
    },
};
</script>
<style lang="scss" scoped>

.delete-color {
    // color: #f56c6c;
}

.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px 15px 15px;
    :deep(.c-form) {
        display: flex;
        padding-bottom: 10px;
        justify-content: space-between;

        .c-form-search {
            min-width: 600px;
            display: flex;
            width: 52%;
            >div{
                width: 33.33%;
                margin:0 5px;
            }
        }
        .el-textarea__inner {
            border-color: #dcdfe6;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
}
</style>
