<template>
    <el-dialog append-to-body
        title="创建病例"
        v-model="visible"
        :close-on-click-modal="false"
        @open="openDialog"
        @close="closeDialog"
        top="10vh"
        width="70%"
        class="t-default my-dialog">
        <div class="m-flexLaout-ty" style="height: 66vh;">
            <div class="g-flexChild">
                <el-scrollbar class="my-scrollbar">
                    <el-form ref="refEditLayer1"
                        :model="formData"
                        :rules="rules"
                        inline>
                        <FormList :list="inputConfig" :formData="formData" :optionData="optionsLoc"
                            :configBtn="false" storageKey="CreatePatientForm">
                            <template v-slot:sDistrictId="{ row,style }">
                                    <el-select v-model="formData.sDistrictId" v-select-name="{formData, fields: row}"
                                        placeholder=" " clearable :style="style"
                                        @change="onChangeHospital"> 
                                        <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                                            :key="index"
                                            :label="item.sHospitalDistrictName"
                                            :value="item.sId">
                                        </el-option>
                                    </el-select>
                            </template>
                            <template v-slot:sMachineryRoomId="{ row ,style }">
                                <el-select v-model="formData.sMachineryRoomId"  v-select-name="{formData, fields: row}"
                                    placeholder=" " clearable :style="style"
                                    @change="onChangeMachineRoom"
                                    @clear="nuclearTableRowClick({})">
                                    <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                                        :key="index"
                                        :label="item.sRoomName"
                                        :value="item.sId">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:sProjectId="{ row, style }">
                                <el-select v-model="formData.sProjectId"  v-select-name="{formData, fields: row}"
                                    placeholder=" " clearable  :style="style"
                                    @change="onChangeProject"
                                    @clear="nuclearTableRowClick({})">
                                    <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                                        :key="index"
                                        :label="item.sItemName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sItemName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:patientName="{ style }">
                                <el-input class="i-name"
                                    v-model="formData.patientName"
                                    :style="style"
                                    clearable
                                    @blur="setPinYin"
                                    @clear="isSpellShow = false"></el-input>
                            </template>
                            <template v-slot:patientNameSpell="{ style }">
                                <el-popover placement="bottom"
                                    :width="250"
                                    trigger="click"
                                    :visible="isSpellShow"
                                    @show="isSpellShow = !!optionsLoc.pinyinOptions.length">
                                    <el-radio-group v-model="formData.patientNameSpell"
                                        @change="isSpellShow = false">
                                        <el-radio v-for="(item, index) in optionsLoc.pinyinOptions"
                                            :key="index"
                                            :label="item"
                                            style="margin-top: 15px;">
                                            <span style="font-size: 16px;">{{item}}</span>
                                        </el-radio>
                                    </el-radio-group>
                                    <template #reference>
                                        <el-input v-model="formData.patientNameSpell"
                                            clearable
                                            :style="style"
                                            @focus="isSpellShow = true"
                                            @blur="() => {if(formData.patientNameSpell){isSpellShow = false}}"
                                            @clear="isSpellShow = !!optionsLoc.pinyinOptions.length"></el-input>
                                        </template>
                                </el-popover>
                            </template>
                            <template v-slot:age="{ style }">
                                <el-input v-model="formData.age"
                                    clearable
                                    type="number"
                                    :style="style">
                                    <template #append>
                                        <el-select v-model="formData.ageUnit"
                                            placeholder="单位"
                                            style="width:80px;height:100%">
                                            <el-option v-for="item in optionsLoc.sAgeUnitOptions"
                                                :key="item.sValue"
                                                :label="item.sName"
                                                :value="item.sValue"></el-option>
                                        </el-select>
                                    </template>
                                </el-input>
                            </template>
                            <template v-slot:patientWeight="{ style }">
                                <el-input-number class="i-weight-3"
                                    v-model="formData.patientWeight"
                                    :min="0"
                                    :controls="false"
                                    :style="style"
                                    @input.native="(val)=>handleInputWeight(item, val)"
                                    @change="(val)=>handleInputWeight(item, val)">
                                </el-input-number>
                            </template>
                            <template v-slot:sImageNo="{ style }">
                                <el-input v-model="formData.sImageNo"
                                    clearable
                                    :style="style">
                                    <template #append>
                                        <el-button 
                                            style="width: 50px;"
                                            @click="getImageNo">get</el-button>
                                    </template>
                                </el-input>
                            </template>
                            <template v-slot:sNuclearNum="{ style }">
                                <el-input v-model="formData.sNuclearNum"
                                    clearable
                                    :style="style">
                                    <template  #append>
                                        <el-button
                                            link
                                            style="width: 50px;"
                                            @click="getNuclearNum">new</el-button>
                                    </template>    
                                </el-input>
                            </template>
                            <template v-slot:fBloodSugar="{ style }">
                                <el-input v-model="formData.fBloodSugar"
                                    type="number"
                                    :style="style">
                                    <template #suffix>
                                        <span>mmol/L</span>
                                    </template>
                                </el-input>
                            </template>
                            <template v-slot:fRecipeDose="{ style, row }">
                                <el-input v-model="formData[row.sProp]"
                                    type="number"
                                    :style="style">
                                    <template #append>
                                        <el-select v-model="formData.sRecipeDoseUnit"
                                            placeholder="单位"
                                            style="width:80px;height:100%">
                                            <el-option v-for="item in optionsLoc.sRecipeDoseUnitOptions"
                                                :key="item.sValue"
                                                :label="item.sName"
                                                :value="item.sName"></el-option>
                                        </el-select>
                                    </template>
                                </el-input>
                            </template>

                            <!-- 核素特殊情况 ['sNuclideText','sTracerText','sPositionText','sTestModeText'] -->
                            <!-- 核素特殊情况 ['核素','示踪剂','检查部位','用药'] -->
                            <template v-for="(item) in inputConfig.filter(i => ['sNuclideText','sTracerText','sPositionText','sTestModeText'].includes(i.sProp))"
                                v-slot:[item.sProp]="{ style, row }">
                                <el-popover :ref="item.sProp"
                                    width="670"
                                    trigger="click"
                                    @show="getItemSetData"
                                    @hide="onPopoverHide">
                                    <div style="margin-bottom: 10px;">
                                        <el-input v-model="medicationCondition.nuclearName" 
                                            placeholder="核素" 
                                            clearable
                                            style="width: 148px;margin-right: 4px"></el-input>
                                        <el-input v-model="medicationCondition.tracerName" 
                                            placeholder="示踪剂" 
                                            clearable
                                            style="width: 148px"></el-input>
                                        <!-- <i class="el-icon-s-tools float-right"
                                            @click="handlerSetCheckMedicine"
                                            style="float: right;font-size: 22px;position: relative;top: 6px;cursor: pointer;"></i> -->
                                    </div>
                                    <el-table :data="nuclideParamOptions.filter(item => item.sNuclideName.toLowerCase().includes(medicationCondition.nuclearName.toLowerCase())).filter(item =>item.sTracerName.toLowerCase().includes(medicationCondition.tracerName.toLowerCase()))"
                                        highlight-current-row
                                        size="small"
                                        border
                                        max-height="300"
                                        @row-click="(o) => nuclearTableRowClick(o, item.sProp)">
                                        <el-table-column width="100"
                                            property="sNuclideName"
                                            label="核素"
                                            show-overflow-tooltip></el-table-column>
                                        <el-table-column min-width="100"
                                            property="sTracerName"
                                            label="示踪剂"
                                            show-overflow-tooltip></el-table-column>
                                        <el-table-column width="100"
                                            property="sItemPositionName"
                                            label="检查部位"
                                            show-overflow-tooltip></el-table-column>
                                        <el-table-column width="120"
                                            property="sTestModeName"
                                            label="检查方式"
                                            show-overflow-tooltip>
                                        </el-table-column>
                                        <el-table-column width="60"
                                            property="iIsInvariable"
                                            label="定量"
                                            show-overflow-tooltip>
                                            <template #default="{ row }">
                                                {{ row.iIsInvariable ? '是' : '否'}}
                                            </template>
                                            </el-table-column>
                                        <el-table-column width="80"
                                            property="fCoefficient"
                                            label="处方系数"
                                            show-overflow-tooltip></el-table-column>
                                        <el-table-column width="80"
                                            property="fDosage"
                                            label="定量剂量"
                                            show-overflow-tooltip></el-table-column>
                                    </el-table>
                                    <div style="margin-top: 10px;text-align: right;">
                                        <el-button-icon-fa
                                            icon="fa fa-close-1"
                                            @click="popVisible = false">关闭</el-button-icon-fa>
                                    </div>
                                    <template #reference>
                                        <el-input v-model="formData[row.sProp]"
                                            readonly
                                            :style="style"></el-input>
                                    </template>
                                </el-popover>
                            </template>
                        </FormList>
                    </el-form>
                </el-scrollbar>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa
                type="primary"
                plain
                _icon="fa fa-save"
                @click="onSaveClick">保存</el-button-icon-fa>
            <el-button-icon-fa 
                plain
                _icon="fa fa-close-1"
                @click="closeDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>

import { getOptionName } from '$supersetResource/js/tools'
import { deepClone } from '$supersetUtils/function'
import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'

import { getNuclearNum, brokenImgno, chineseToPinyin } from '$supersetApi/projects/apricot/appointment/index.js'
import { getItemSetData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { imgPatientCreateRegisterInfo } from '$supersetApi/projects/apricot/common'

import { useGetHospitalData, useGetMachineRoomData, useGetItemData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'

export default {
    name: 'CreatePatient',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['isUpdateRowData', 'update:dialogVisible'],
    data () {
        return {
            visible: false,
            formData: {},
            inputConfig: [{
                sProp: 'patientName',
                sLabel: '姓名',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 6,
                iCustom: 1,
            }, {
                sProp: 'patientNameSpell',
                sLabel: '姓名拼音',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 6,
                iCustom: 1,
            }, {
                sProp: 'patientSex',
                sLabel: '性别',
                sOptionProp: 'iSexList',
                sInputType: 'option',
                iLayourValue: 6
            }, {
                sProp: 'age',
                sLabel: '年龄',
                iRequired: 1,
                iLayourValue: 6,
                iCustom: 1,
            }, {
                sProp: 'sIdNum',
                sLabel: '身份证号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'patientBirthDate',
                sLabel: '出生日期',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sPhone',
                sLabel: '联系电话',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sRegister',
                sLabel: '登记方式',
                sOptionProp: 'sRegisterOptions',
                sInputType: 'option',
                iLayourValue: 6
            }, {
                sProp: 'patientSize',
                sLabel: '身高(cm)',
                sInputType: 'number',
                iLayourValue: 6
            }, {
                sProp: 'patientWeight',
                sLabel: '体重(kg)',
                sInputType: 'number',
                iLayourValue: 6,
                iCustom: 1
            }, {
                sProp: 'sAddress',
                sLabel: '地址',
                sInputType: 'text',
                iLayourValue: 12
            }, {
                sProp: 'sDistrictId',
                sPropName: 'sDistrictName',
                sLabel: '院区',
                iRequired: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sMachineryRoomId',
                sPropName: 'sMachineryRoomText',
                sLabel: '机房',
                iRequired: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sProjectId',
                sPropName: 'sProjectName',
                sLabel: '项目',
                iRequired: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sNuclearNum',
                sLabel: '核医学号',
                iRequired: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'fBloodSugar',
                sLabel: '空腹血糖',
                iCustom: 1,
                sInputType: 'text-unit',
                iLayourValue: 6
            }, {
                sProp: 'fRecipeDose',
                sLabel: '处方剂量',
                iCustom: 1,
                sInputType: 'number',
                iLayourValue: 6
            }, {
                sProp: 'sChargeState',
                sLabel: '收费状态',
                sOptionProp: 'ChargeStateOptions',
                sInputType: 'option',
                iLayourValue: 6
            }, {
                sProp: 'sNuclideText',
                sLabel: '核素',
                iReadonly: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sTracerText',
                sLabel: '示踪剂',
                iReadonly: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sPositionText',
                sLabel: '检查部位',
                iReadonly: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sTestModeText',
                sLabel: '检查方式',
                iReadonly: 1,
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sApplyDepartText',
                sLabel: '申请科室',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sApplyPersonName',
                sLabel: '申请医生',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'dApplyDate',
                sLabel: '开单日期',
                sInputType: 'date-picker',
                iLayourValue: 6
            }, {
                sProp: 'sApplyDistrictName',
                sLabel: '申请院区',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sSource',
                sLabel: '就诊类型',
                sOptionProp: 'sSourceOptions',
                sInputType: 'option',
                iLayourValue: 6
            }, {
                sProp: 'sPatientIndex',
                sLabel: '患者索引',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sMedicalRecordNO',
                sLabel: '病历号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sMedicalCaseNO',
                sLabel: '病案号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sEncounter',
                sLabel: '就诊次数',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sApplyNO',
                sLabel: '申请单号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sOrderNO',
                sLabel: '医嘱号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sImageNo',
                sLabel: '影像号',
                iCustom: 1,
                iLayourValue: 6
            }, {
                sProp: 'sInHospitalNO',
                sLabel: '住院号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sInHospitalSerial',
                sLabel: '住院流水号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sInpatientAreaText',
                sLabel: '病区',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sBedNum',
                sLabel: '床号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sVisitCard',
                sLabel: '就诊卡号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sOutpatientNO',
                sLabel: '门诊号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sOutpatientSerial',
                sLabel: '门诊流水号',
                sInputType: 'text',
                iLayourValue: 6
            }, {
                sProp: 'sVitisNo',
                sLabel: '就诊流水号',
                sInputType: 'text',
                iLayourValue: 6
            }],
            optionsLoc: {
                iSexList: appointmentEnum.sexOptions,
                sSourceOptions: appointmentEnum.visitTypeOptions,
                sAgeUnitOptions: appointmentEnum.ageUnitOptions,
                iIsBedsideExamineOptions: [{ sName: '是', sValue: '1' }, { sName: '否', sValue: '0' }],
                ChargeStateOptions: [{ sName: '已收费', sValue: '1' }, { sName: '未收费', sValue: '0' }],
                pinyinOptions: [],
                // 登记方式
                sRegisterOptions: this.$store.getters['dict/map'].ApricotReportRegister || [],
                // 客户级别
                sCustomerLevelOptions: this.$store.getters['dict/map'].ApricotReportCustomerLevel || [],
                // 处方剂量单位
                sRecipeDoseUnitOptions: this.$store.getters['dict/map'].ApricotReportDoseUnit || [],
                // 收费类型
                ApricotReportFeeType: this.$store.getters['dict/map'].ApricotReportFeeType || [],
            },
            rules: { },
            loading: false,
            isSpellShow: false,
            nuclearParams: {}, // 用药参数,
            nuclideParamOptions: [],
            medicationCondition: {
                nuclearName: '',
                tracerName: ''
            }
        }
    },
    watch: {
        dialogVisible () {
            this.visible = this.dialogVisible;
            this.$nextTick(() => {
                if (!this.visible) {
                    this.$refs['refEditLayer1'] && this.$refs['refEditLayer1'].resetFields();
                    let dom = document.querySelector('.i-name');
                    dom && dom.removeAttribute('sName')
                    return
                }
                this.$refs['refEditLayer1'] && this.$refs['refEditLayer1'].resetFields();
                this.formData = deepClone(this.patientInfo);
                let formData = this.formData;
                // 姓名字段没有值的时候赋拼音字段的值
                formData['patientName'] = formData.patientName ? formData.patientName : formData.patientInfoName;
                formData['patientNameSpell'] = formData.patientName;
                // this.$refs.refEditLayer1.clearValidate('patientNameSpell')

                // 年龄字段处理成年龄值和年龄单位值
                formData['age'] = formData.patientAge ? parseInt(formData.patientAge) : '';
                let ageUnit = formData.patientAge ? formData.patientAge.substring(formData.patientAge.length - 1) : '';
                let isLetter = /^[a-zA-Z]+$/.test(ageUnit);
                formData['ageUnit'] = isLetter ? ageUnit : '';
                // 身高处理换算成厘米
                if (formData.patientSize !== null || formData.patientSize !== undefined) {
                    formData['patientSize'] = formData.patientSize > 10 ? formData.patientSize : Number(formData.patientSize * 100);
                }
                // patientSize 身高 体重
                formData['patientSize'] = formData.patientSize ? Number(formData.patientSize) : undefined;
                formData['patientWeight'] = formData.patientWeight ? Number(formData.patientWeight) : undefined;
                formData['sNuclearNum'] = formData.patientId;
                formData['sMedicalRecordNO'] = formData.patientId;
                formData['sRecipeDoseUnit'] = this.optionsLoc.sRecipeDoseUnitOptions[0]?.sName;

                // this.setPinYin() // 获取拼音
            })
        },
        nuclearParams (val) {
            if (!Object.keys(val).length) return;
            if (val.iIsInvariable) {
                this.formData['fRecipeDose'] = val.fDosage;
                return
            }
            if (!this.formData.patientWeight) {
                this.formData['fRecipeDose'] = undefined;
                return
            }
            this.formData['fRecipeDose'] = Number((this.formData.patientWeight * val.fCoefficient).toFixed(2));
        },
    },
    methods: {
        async openDialog () {
            await this.useGetHospitalData();
            let workStation = this.$store.getters['user/workStation'];
            if (Object.keys(workStation).length){
                this.formData['sDistrictId'] = workStation.districtId;
                await this.useGetMachineRoomData(workStation.districtId);
            } 
        },
        closeDialog () {
            this.$emit('update:dialogVisible', false);
            this.isSpellShow = false;
        },
        async onChangeHospital (val) {
            this.formData.sMachineryRoomId = '';
            this.formData.sProjectId = '';
            await this.useGetMachineRoomData(this.formData.sDistrictId);
            this.optionsLoc.itemsArrOption = []
        },
        async onChangeMachineRoom (val) {
            var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === val);
            this.formData['sRoomId'] = target.sDeviceTypeId;
            this.formData['sRoomText'] = target.sDeviceTypeName;
            if(target || !val) {
                await this.useGetItemData(target?.sDeviceTypeId);
                var filterLen = this.optionsLoc.itemsArrOption.filter(item => this.formData.sProjectId === item.sId).length
                if(!this.optionsLoc.itemsArrOption.length || !filterLen) {
                    this.formData.sProjectId = ''
                }
            }
            this.nuclearTableRowClick({});
        },
        onChangeProject () {
            this.getItemSetData();
        },
        handleInputWeight (item, value) {
            let val = value;
            if (!Object.keys(this.nuclearParams).length) return;
            if (this.nuclearParams.iIsInvariable) {
                this.formData['fRecipeDose'] = this.nuclearParams.fDosage;
                return
            }
            if (!val) {
                this.formData['fRecipeDose'] = undefined;
                return
            }
            this.formData['fRecipeDose'] = Number((val * this.nuclearParams.fCoefficient).toFixed(2));
        },
        onSaveClick () {
            this.$refs['refEditLayer1'].validate(valid => {
                if (!valid) {
                    this.$message.closeAll();
                    this.$message({
                        message: '填写正确信息',
                        type: 'info',
                        duration: 3000
                    });
                    return false;
                }
                // 验证成功
                if (this.isSpellShow) {
                    this.$message.closeAll();
                    this.$message({
                        message: '请完成选择姓名拼音！',
                        type: 'warning',
                    });
                    return
                }
                let jsonData = deepClone(this.formData);
                let optionsLoc = this.optionsLoc;
                let temp = {
                    sSexText: getOptionName(this.formData.sSex, optionsLoc.iSexList),  // 性别
                    sRegisterText: getOptionName(this.formData.sRegister, optionsLoc.sRegisterOptions),  //登记方式
                    ageUnitText: getOptionName(this.formData.sAgeUnit, optionsLoc.sAgeUnitOptions), // 年龄单位
                    sSourceText: getOptionName(this.formData.sSource, optionsLoc.sSourceOptions),  // 就诊类型
                    sChargeStateText: getOptionName(this.formData.sChargeState, optionsLoc.ApricotReportFeeType), // 收费状态
                    sFeeTypeText: getOptionName(this.formData.sFeeType, optionsLoc.ChargeStateOptions), // 收费类型
                    // sDistrictName: getOptionName(this.formData.sDistrictId, this.optionsLoc.districtArrOption),   // 院区
                    // sMachineryRoomText: getOptionName(this.formData.sMachineryRoomId, this.optionsLoc.machineRoomArrOption),  // 机房
                    // sProjectName: getOptionName(this.formData.sProjectId, this.optionsLoc.itemsArrOption)   // 项目
                }
                Object.assign(jsonData, temp);

                let loading = this.$loading({
                    lock: true,
                    text: '正在保存中，请稍等',
                    background: 'rgba(0, 0, 0, 0.1)'
                });
                imgPatientCreateRegisterInfo(jsonData).then(res => {
                    loading.close()
                    if (!res.success) {
                        // 创建失败
                        this.$message.error(res.msg);
                        return
                    }
                    // 创建成功
                    this.$emit('isUpdateRowData');
                    this.closeDialog()
                    this.$message.success(res.msg);
                }).catch(err => {
                    loading.close()
                    console.log(err)
                })
            })
        },
        // 关闭核素药物popover组件
        onPopoverHide() {
            this.medicationCondition.nuclearName = '';
            this.medicationCondition.tracerName = '';
        },
        popoverClickOutside(sProp) {
            this.$refs[sProp] && this.$refs[sProp].hide();
        },
        // 核素相关表格行点击事件
        nuclearTableRowClick (row, sProp) {
            this.nuclearParams = row;
            this.popoverClickOutside(sProp);
            // 核素、示踪剂、检查部位、检查方式相关字段的赋值
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode'];
            let originKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sItemPositionName', 'sItemPositionId', 'sTestModeName', 'sTestModeId'];
            formKeys.map((item, index) => {
                this.formData[item] = row[originKeys[index]];
            })

            if (!Object.keys(row).length) {
                this.formData['fRecipeDose'] = undefined;
                return;
            } 
            if (row.iIsInvariable) {
                this.formData['fRecipeDose'] = row.fDosage;
                return
            }
            let patientSize = this.formData.patientSize;
            this.formData['fRecipeDose'] = (patientSize >= 0 && row.fCoefficient) ? Number((patientSize * row.fCoefficient).toFixed(2)) : undefined;
        },
        // 清除核素相关属性数据
        resetFormNuclearData () {
            let temp = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode', 'fRecipeDose'];
            for (let item in temp) {
                if (this.formData[temp[item]]) {
                    this.formData[temp[item]] = null;
                }
            }
        },
        getItemSetData () {
            if(!this.formData.sProjectId) {
                this.nuclearTableRowClick({});
                return
            }
            getItemSetData({
                sItemId: this.formData.sProjectId,
                iIsEnable: 1
            }).then(res => {
                if (res.success) {
                    this.nuclideParamOptions = res?.data || [];
                    if(this.formData.sProjectId !== this.nuclearParams.sItemId && this.nuclideParamOptions.length) {
                        this.nuclearTableRowClick(this.nuclideParamOptions[0] || {})
                    }
                    return;
                }
                this.nuclideParamOptions = [];
            }).catch(err => {
                this.nuclideParamOptions = [];
                console.log(err)
            })
        },
        // 获取影像号
        getImageNo () {
            let orignData = deepClone(this.formData)
            let jsonData = {
                sName: orignData.patientName,
                sSexCode: orignData.patientSex,
                dBirthday: orignData.patientBirthDate,
                sIdNum: orignData.sIdNum,
                sVisitTypeCode: orignData.sSource,
                sInHospitalNO: orignData.sInHospitalNO,
                sOutpatientNO: orignData.sOutpatientNO
            };
            let tipmsg = {
                sName: '请填写患者姓名',
                sSexCode: '请填选患者性别',
                dBirthday: '请填选患者出生日期',
                sIdNum: '请填写患者身份证号',
                sVisitTypeCode: '请填选患者就诊类型',
                sInHospitalNO: '住院号和门诊号至少填写一项'
            }
            for (let key in jsonData) {
                if (key == 'sInHospitalNO' || key == 'sOutpatientNO') {
                    continue
                }
                if (!jsonData[key]) {
                    this.$message.warning(tipmsg[key]);
                    return
                }
            }
            if (!jsonData.sInHospitalNO && !jsonData.sOutpatientNO) {
                this.$message.warning(tipmsg.sInHospitalNO);
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            brokenImgno(jsonData).then(res => {
                loading.close()
                if (res.success) {
                    this.formData['sImageNo'] = res.data;
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close();
                console.log(err);
            })
        },
        // 获取核医学号
        getNuclearNum () {
            let data = this.formData;
            if (!data.sProjectId) {
                this.$message.warning('请选择检查项目');
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            getNuclearNum({
                sDeviceTypeId: data.sRoomId,
                sItemId: data.sProjectId
            }).then(res => {
                loading.close()
                if (res.success) {
                    this.formData['sNuclearNum'] = res.data || ''
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close()
                console.log(err);
            })
        },
        querySearch (queryString, cb) {
            var targetOptions = this.optionsLoc.pinyinOptions;
            // var results = queryString ? targetOptions.filter(this.createFilter(queryString)) : targetOptions;
            var results = targetOptions;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        handlePinYinSelect (item) {
            this.formData['patientNameSpell'] = item;
        },
        onNameSpellfocus () {
            if (!this.formData.patientName) {
                this.optionsLoc.pinyinOptions = [];
                return
            }
            if (this.formData.patientNameSpell.length) {
                return
            }
            if (this.optionsLoc.pinyinOptions.length) {
                this.isSpellShow = true
            }
        },
        // 创建拼音
        setPinYin () {
            let sName = this.formData.patientName;
            let dom = document.querySelector('.i-name');
            if(!dom) return
            if (!sName) {
                this.optionsLoc['pinyinOptions'] = [];
                this.formData['patientNameSpell'] = undefined;
                this.$refs.refEditLayer1.clearValidate('patientNameSpell');
                dom && dom.removeAttribute('sname');
                this.isSpellShow = false;
                return
            }
            if (sName === dom.getAttribute('sname')) { return }
            dom.setAttribute('sname', this.formData.patientName)
            chineseToPinyin({ sChineseText: this.formData.patientName }).then(res => {
                if (res.success) {
                    let data = res.data || [];
                    this.optionsLoc['pinyinOptions'] = data;
                    if (data.length === 1) {
                        this.formData['patientNameSpell'] = data[0];
                    } else if (data.length > 1) {
                        if (!data.includes(this.formData.patientNameSpell)) {
                            this.formData['patientNameSpell'] = '';
                        } else {
                            this.$nextTick(() => {
                                this.isSpellShow = false;
                            })
                        }
                    } else {
                        this.formData['patientNameSpell'] = ''
                    }
                    this.$refs.refEditLayer1.clearValidate('patientNameSpell');
                    if (data.length > 1) {
                        this.isSpellShow = true
                    }
                    return
                }
                this.$message.error(res.msg)
            })
        },
        closePinYinDialog () {
            if (!this.formData.patientNameSpell) {
                this.$message.warning('请选择姓名拼音！')
                return
            }
            this.isSpellShow = false;
        },
        useGetHospitalData, 
        useGetMachineRoomData, 
        useGetItemData
    }
    
}
</script>
<style lang="scss" scoped>
:deep(.el-select .el-input) {
    width: 100%;
}
</style>
