const state = {
    patientInfo: {},
    currentModule: '',
    IsUpdatePatientInfo: false
}

const getters = {

}

const mutations = {
    setPatientInfo(state, payload) {
        state.patientInfo = payload.patientInfo
    },
    setCurrentModule(state, payload) {
        state.currentModule = payload.name
    },
    setIsUpdatePatientInfo(state, payload) {
        state.IsUpdatePatientInfo = payload.IsUpdatePatientInfo
    },
}

const actions = {
    actions1() {

    }
}

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
    modules: {}
}