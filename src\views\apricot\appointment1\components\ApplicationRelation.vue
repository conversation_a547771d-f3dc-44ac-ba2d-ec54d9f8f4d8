<template>
    <el-dialog
        :title="'申请单'+ (props.selectedItem.sOrderItemName ? ` - ${props.selectedItem.sOrderItemName}` : '')"
        v-model="isShowItemTree"
        :close-on-click-modal="false"
        class="t-default my-dialog"
        draggable
        width="600px">
        <div style="height: 620px;overflow: auto;">
            <span class="c-head" style="padding-bottom: 4px;display: block;">请选择系统对应的检查项目：</span>
            <div class="c-center">
                <el-tree :data="treeData" 
                    :props="defaultProps" 
                    default-expand-all 
                    highlight-current
                    :expand-on-click-node="false"
                    @node-click="handleNodeClick"></el-tree>
            </div>
        </div>
        <template #footer>
            <div slot="footer">
                <el-button v-if="false" type="primary" @click="onAddCheckItem" :loading="loading">
                    <template #icon>
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </template>
                    新增项目
                </el-button>
                <el-button type="primary" @click="onMatchSystemItem" :loading="loading">
                    <template #icon>
                        <el-icon>
                            <Connection />
                        </el-icon>
                    </template>
                    关联
                </el-button>
                <el-button-icon-fa
                    _icon="fa fa-close-1"
                    @click="isShowItemTree = false">关闭</el-button-icon-fa>
            </div>
        </template>

    </el-dialog>
</template>
<script setup>
    import { ElMessage } from 'element-plus'
    import { Plus, Connection } from '@element-plus/icons-vue'
    import { getItemTreeData, addAutoItemSet, addItemAndAutoItem } from '$supersetApi/projects/apricot/appointment/projectSet.js'

    const emits = defineEmits(['update:modelValue', 'onQueryApply'])
    const props = defineProps({
        modelValue: Boolean,
        selectedItem: {}
    })

    const isShowItemTree = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emits('update:modelValue', val)
        }
    })

    watch(() => isShowItemTree.value, (later) => {
        if (later) {
            getTreeData()
        }
    })

    const treeData = ref([])
    const selectedNode = ref({})
    const defaultProps = {
        children: 'childs',
        label: 'sName'
    }
    const loading = ref(false)
    const treeLoading = ref(false)

    // 树节点点击事件
    function handleNodeClick (obj) {
        selectedNode.value = obj
    }
    // 获取树数据
    function getTreeData() {
        selectedNode.value = {}
        treeLoading.value = true
        getItemTreeData().then(res => {
            
            if (res.success) {
                treeData.value = res.data || []
                return
            }
            treeData.value = []
            ElMessage.error(res.msg)
        }).finally(() => {
            treeLoading.value = false
        })
    }
    function onAddCheckItem() {
        if(!Object.keys(selectedNode.value).length) {
            ElMessage.warning('请选择设备类型！');
            return
        }
        let sDeviceTypeName = ''
        let sDeviceTypeCode = ''
        if(selectedNode.value.sItemId) {
            treeData.value.find(item => {
                if(item.sDeviceTypeId === selectedNode.value.sDeviceTypeId) {
                    sDeviceTypeName = item.sName
                    sDeviceTypeCode = item.sCode
                }
            })
        } else {
            sDeviceTypeName = selectedNode.value.sName
            sDeviceTypeCode = selectedNode.value.sCode
        }
        let params = {
            sDeviceTypeCode: sDeviceTypeCode,
            sDeviceTypeName: sDeviceTypeName,
            sDeviceTypeId: selectedNode.value.sDeviceTypeId,
            sItemCode: props.selectedItem.sOrderItemCode,
            sItemName: props.selectedItem.sOrderItemName,
            iIsEnable: 1
        }
        loading.value = true
        addItemAndAutoItem(params).then((res) => {
            if (res.success) {
                ElMessage.success(res.msg)
                emits('onQueryApply')
                isShowItemTree.value = false
                return
            }
            ElMessage.error(res.msg)
        }).finally(() => {
            loading.value = false
        })
    }
    // 关联本地项目
    function onMatchSystemItem () {
        if(!Object.keys(selectedNode.value).length || !selectedNode.value.sItemId) {
            ElMessage.warning('请选择系统项目！')
            return
        }
        // 获取设备类型名称
        let sDeviceTypeName = ''
        treeData.value.find(item => {
            if(item.sDeviceTypeId === selectedNode.value.sDeviceTypeId) {
                sDeviceTypeName = item.sName
            }
        })
        // 入参
        let params = {
            sDeviceTypeId: selectedNode.value.sDeviceTypeId,
            sDeviceTypeName: sDeviceTypeName,
            sItemId: selectedNode.value.sItemId,
            sItemName: selectedNode.value.sName,
            sOrderItemCode: props.selectedItem.sOrderItemCode,
            sOrderItemName: props.selectedItem.sOrderItemName,
            iIsEnable: 1
        }
        loading.value = true
        // 创建项目匹配
        addAutoItemSet(params).then((res) => {
            if (res.success) {
                ElMessage.success(res.msg)
                emits('onQueryApply')
                isShowItemTree.value = false
                return
            }
            ElMessage.error(res.msg)
        }).finally(() => {
            loading.value = false
        })
    }
</script>
<style lang="scss" scoped>
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
    background-color: var(--table-highlight-row-bg);
}
</style>