<!-- 号源管理 -->
<template>
    <LayerSlide title="号源模板维护" destroy-on-close v-model="visible" @open="openLayer" fixed>
        <div class="g-content g-flexChild m-flexLaout-tx">
            <!-- 左边内容 -->
            <DragAdjustFlex v-model="dragA1" dragBgColor="#eee">
                <template v-slot:c1>
                    <div class="c-left m-flexLaout-ty">
                        <!-- 按钮组 -->
                        <div class="c-btns flex justify-end">
                            <!-- <el-button type="primary"
                                size="small"
                                icon="el-icon-plus">新增</el-button> -->
                            <el-button type="primary"
                                :loading="loading1===2"
                                @click="onRefreshClick">
                                <template #icon>
                                    <el-icon>
                                        <Refresh />
                                    </el-icon>
                                </template>
                                刷新</el-button>
                            <el-button-icon-fa
                                icon="el-icon-plus"
                                :disabled="saveLoading"
                                @click="onResetClick">新增</el-button-icon-fa>
                        </div>

                        <div class="g-flexChild">
                            <!-- 表格内容 -->
                            <el-table-extend
                                v-if="configTable1"
                                :data="tableData1"
                                v-loading="loading1===1"
                                @row-click="onRowClick1"
                                :iModuleId="iModuleId"
                                ref="mainTable1"
                                highlight-current-row
                                border
                                height="100%"
                                storageKey="appointment1-NumberSourceMng">
                                <el-table-column 
                                    v-for="item in configTable1"
                                    :key="item.sProp"
                                    :prop="item.sProp" 
                                    :sortable="(!!item.iSort) ? 'custom' : defaultItem.iSort"
                                    :column-key="item.sSortField ? item.sSortField : null"
                                    :width="item.sWidth || defaultItem.sWidth"
                                    :min-width="item.sMinWidth || defaultItem.sMinWidth"
                                    :show-overflow-tooltip="item.showOverflowTooltip != undefined ? item.showOverflowTooltip : defaultItem.showOverflowTooltip"  
                                    :label="item.sLabel"
                                    :align="item.sAlign || defaultItem.sAlign"
                                    :fixed="item.sFixed"
                                    :formatter="item.formatter || defaultItem.formatter">
                                        <template v-slot="{ row }">
                                            <template v-if="item.sProp === 'action'">
                                                <el-button link 
                                                size="small" 
                                                @click.stop="onClickDel1(row)">
                                                <template #icon>
                                                    <el-icon><Delete /></el-icon>
                                                </template>
                                                删除
                                                </el-button>
                                            </template>
                                            <template v-else-if="item.sProp === 'sPlanName'">
                                                <el-input v-model="row.sPlanName"
                                                    size="small"
                                                    v-if="row.edit"></el-input>
                                                <span v-else>{{row.sPlanName}}</span>
                                            </template>
                                            <template v-else-if="item.sProp === 'iEnabled'">
                                                <span>{{row[item.sProp] ? '是' : '否'}}</span>
                                            </template>
                                            <template v-else>
                                                {{ row[`${item.sProp}`] }}
                                            </template>
                                        </template>
                                </el-table-column>
                            </el-table-extend>
                        </div>
                    </div>
                </template>
                <template v-slot:c2>
                    <!-- 右边内容 -->
                    <div class="c-right m-flexLaout-ty">
                        <!-- 表单内容 -->
                        <div class="c-form">
                            <el-form :model="form"
                                :rules="rules"
                                ref="refEditLayer">
                                <FormList
                                    storageKey="NumberSourceMng-form"
                                    :iModuleId="iModuleId"
                                    :list="formConfig"
                                    :formData="form"
                                    :optionData="optionsLoc">
                                </FormList>
                            </el-form>
                        </div>
                        <!-- 表格 -->
                        <div class="c-table g-flexChild">
                            <el-table :data="tableData2"
                                ref="mainTable"
                                id="schemePlanTimeTable"
                                size="small"
                                border
                                height="100%">
                                <el-table-column type="index"
                                    align="center"
                                    width="50">
                                </el-table-column>
                                <el-table-column v-for="item in configTable2"
                                    show-overflow-tooltip
                                    :key="item.index"
                                    :prop="item.sProp"
                                    :label="item.sLabel"
                                    :fixed="item.sFixed"
                                    :align="item.sAlign"
                                    :width="item.sWidth"
                                    :min-width="item.sMinWidth"
                                    :sortable="!!item.iSort">
                                    <template v-slot="{row, $index}"
                                        v-if="!item.iIsHide">
                                        <template v-if="item.sProp === 'action'">
                                            <el-button 
                                                link 
                                                size="small" 
                                                @click.stop="onClickAdd2($index)">
                                                <template #icon>
                                                    <el-icon><Plus /></el-icon>
                                                </template>
                                                添加
                                                </el-button>
                                            <el-button 
                                                link 
                                                size="small" 
                                                @click.stop="onClickDel2($index)">
                                                <template #icon>
                                                    <el-icon><Delete /></el-icon>
                                                </template>
                                                删除
                                            </el-button>
                                        </template>
                                        <template v-else-if="item.sProp === 'sTime'">
                                            <el-autocomplete
                                                v-model="row.sTime"
                                                :fetch-suggestions="querySearch"
                                                clearable
                                                class="i-table-input"
                                                placeholder=""/>
                                        </template>
                                        <template v-else-if="item.sProp === 'iPlanCount'">
                                            <el-autocomplete
                                                v-model="row.iPlanCount"
                                                :fetch-suggestions="querySearchPlanCount"
                                                clearable
                                                class="i-table-input"
                                                placeholder=""/>
                                        </template>
                                        <template v-else>
                                            {{row[`${item.sProp}`]}}
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                        <!-- 按钮组 -->
                        <div class="c-btns">
                            <el-button-icon-fa type="primary"
                                _icon="fa fa-save"
                                :loading="saveLoading"
                                @click="onSaveClick">{{this.form.sId ? '保存' : '保存'}} </el-button-icon-fa>
                            <el-button-icon-fa
                            _icon="fa fa-close-1"
                            @click="visible = false">关闭</el-button-icon-fa>
                        </div>
                    </div>
                </template>
        </DragAdjustFlex>
        </div>
    </LayerSlide>
</template>
<script>
import FormStyle from '$supersetViews/components/FormStyle.vue'

import { deepClone } from '$supersetUtils/function'

import {
    getHospitalAndRoomData, addRegisterTemplate, editRegisterTemplate, delRegisterTemplate,
    queryAllRegisterTemplate
} from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { Refresh, Delete, Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'

export default {
    name: "NumberSourceMng",
    emits: ['closeDialog', 'update:modelValue'],

    components: {
        FormStyle,
        Refresh,
        Delete,
        Plus
    },
    props: {
        modelValue: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        visible: {
            get: function() {
                return this.modelValue
            },
            set: function(val) {
                this.$emit('update:modelValue', val)
            }
        }
    },
    data () {
        return {
            configTable1: [
                { sProp: 'sHospitalDistrictName', sLabel: '院区', sAlign: 'left', sMinWidth: '120px', isSortable: true },
                { sProp: 'sTemplateName', sLabel: '模板名称', sAlign: 'left', sMinWidth: '100px' },
                { sProp: 'sRoomName', sLabel: '机房', sAlign: 'left', sMinWidth: '80px' },
                { sProp: 'iEnabled', sLabel: '启用', sAlign: 'center', sWidth: '60px' },
                { sProp: 'action', sLabel: '操作', sAlign: 'center', sWidth: '80px', sFixed: 'right' }
            ],
            tableData1: [],
            selectItem1: {},
            loading1: false,
            loading2: false,
            formConfig: [
                { sProp: 'sTemplateName', sLabel: '模板名称', sInputType: 'text', iLayourValue: 12, iRequired: 1 },
                { sProp: 'sHospitalDistrictId', sLabel: '院区', sPropName: 'sDistrictPrefix', sInputType: 'option', sOptionProp: 'districtOptions', iLayourValue: 12 },
                { sProp: 'sRoomId', sLabel: '机房', sPropName: 'sRoomName', sInputType: 'option', sOptionProp: 'machineRoomOptions', optionLabel: 'sRoomName', optionValue: 'sRoomId', iLayourValue: 12 },
                { sProp: 'iEnabled', sLabel: '启用状态', sInputType: 'option', sOptionProp: 'stateOptions', iLayourValue: 12 },
                { sProp: 'iAutoGenDay', sLabel: '提前生成号源', sInputType: 'option', sOptionProp: 'daysOptions', iLayourValue: 12 }
            ],
            defaultItem: { 
                iSort: false,
                sWidth: null,
                showOverflowTooltip: true,
                sAlign: 'center',
                sMinWidth: 100,
                formatter: null
            },
            optionsLoc: {
                districtOptions: [],
                machineRoomOptions: [],
                daysOptions: [
                    { sName: '7天内', sValue: 7 },
                    { sName: '14天内', sValue: 14 },
                    { sName: '20天内', sValue: 20 },
                    { sName: '30天内', sValue: 30 },
                    { sName: '60天内', sValue: 60 }
                ],
                stateOptions: [
                    { sName: '是', sValue: 1 },
                    { sName: '否', sValue: 0 },
                ]
            },
            defaultForm: {
                iAutoGenDay: 7,
                sDeviceTypeId: undefined,
                sDeviceTypeName: undefined,
                sHospitalDistrictId: undefined,
                sDistrictPrefix: undefined,
                sTemplateName: undefined,
                iEnabled: 1,
                times: [],
            },
            form: {},
            rules: {},
            configTable2: [
                { sProp: 'sTime', sLabel: '时间段（格式HH:mm）', sAlign: 'center', sMinWidth: '150px' },
                { sProp: 'iPlanCount', sLabel: '方案限数', sAlign: 'center', sMinWidth: '150px' },
                { sProp: 'action', sLabel: '操作', sAlign: 'center', sWidth: '130px' }
            ],
            tableData2: [{}],
            saveLoading: false,
            iModuleId: 2,
            dragA1: {
                type: 'column',
                localStorageKey: '202308171710',
                panelConfig: [
                    {
                        slot: 'c1',
                        size: 416,
                        minSize: 316,
                        maxSize: 1010
                    },
                    {
                        slot: 'c2',
                        flex: 1
                    }
                ]
            }
        }
    },
    watch: {
        'form.sHospitalDistrictId': {
            handler() {
                // 选择院区
                const item = this.optionsLoc.districtOptions.find(item => item.sHospitalDistrictId == this.form.sHospitalDistrictId)
                if (item) {
                    // console.log('清除机房，获取机房列表')
                    // props.formData.sMachineryRoomId = ''
                    this.optionsLoc.machineRoomOptions = item.rooms
                }
            }
        }
    },
    methods: {
        querySearch(queryString, cb) {
            cb([
                { value: '07:00' }, { value: '07:30' },
                { value: '08:00' }, { value: '08:30' },
                { value: '09:00' }, { value: '09:30' },
                { value: '10:00' }, { value: '10:30' },
                { value: '11:00' }, { value: '11:30' },
                { value: '12:00' }, { value: '12:30' },
                { value: '13:00' }, { value: '13:30' },
                { value: '14:00' }, { value: '14:30' },
                { value: '15:00' }, { value: '15:30' },
                { value: '16:00' }, { value: '16:30' },
                { value: '17:00' }, { value: '17:30' },
                { value: '18:00' }, { value: '18:30' },
                { value: '19:00' }, { value: '19:30' },
                { value: '20:00' }, { value: '20:30' },
                { value: '21:00' }, { value: '21:30' },
                { value: '22:00' }, { value: '22:30' },
                { value: '23:00' }, { value: '22:30' },
            ])
        },
        querySearchPlanCount(queryString, cb) {
            cb([
                { value: '1' }, { value: '2' },
                { value: '3' }, { value: '4' },
                { value: '5' }, { value: '6' },
                { value: '7' }, { value: '8' },
                { value: '9' }, { value: '10' },
            ])
        },
        openLayer() {
            // this.getHospitalData();
            // this.getDeviceTypeData();
            this.getHospital()

            this.tableData1 = [];
            this.queryAllRegisterTemplate();
            // this.onResetClick();
        },
        onRefreshClick () {
            this.queryAllRegisterTemplate(2);
        },
        // 删除方案
        onClickDel1 (row) {
            this.$confirm('确定删除选中数据吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                let jsonData = {
                    sId: row.sId,
                };
                delRegisterTemplate(jsonData).then(res => {
                    if (res.success) {
                        this.$message.success('删除成功！');
                        this.queryAllRegisterTemplate();
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch()
            }).catch(err => err);
        },
        // 表格一行点击事件
        onRowClick1 (row) {
            this.selectItem1 = row;
            this.form = deepClone(row);
            this.tableData2 = this.form.times && this.form.times.length ? this.form.times : [{}];
        },
        // 时间段表格增行点击事件
        onAddTimeRow () {
            this.tableData2.push({});
        },
        onClickAdd2 (index) {
            this.tableData2.splice(index + 1, 0, {})
        },
        // 时间段表格删除行数据
        onClickDel2 (index) {
            if (this.tableData2.length === 1) {
                this.$message.warning('仅剩一行数据，不允许删除！');
                return
            }
            this.tableData2.splice(index, 1);
        },
        // 清空
        onResetClick (status) {
            if (status === true) {
                this.$nextTick(() => {
                    this.$refs['refEditLayer'] && this.$refs['refEditLayer'].resetFields();
                    this.selectItem1 = {};
                    this.$refs.mainTable1.setCurrentRow()
                    this.tableData2 = [];
                    for (let index = 8; index <= 1; index++) {
                        this.tableData2.push({
                            sTime: `${index < 10 ? '0' : ''}${index}:00`,
                            iPlanCount: 5
                        })
                    }
                    this.form = deepClone(this.defaultForm);
                })
                return;
            }
            ElMessageBox.prompt('', '模板名称', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(({ value }) => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].resetFields();
                this.selectItem1 = {};
                this.$refs.mainTable1.setCurrentRow()
                this.tableData2 = [];
                for (let index = 8; index <= 18; index++) {
                    this.tableData2.push({
                        sTime: `${index < 10 ? '0' : ''}${index}:00`,
                        iPlanCount: 5
                    })
                }
                this.form = deepClone(this.defaultForm);
                this.form.sTemplateName = value
                ElMessage.info('补录其它信息，并保存！')
            })
            
        },
        // 保存
        onSaveClick () {
            this.$refs['refEditLayer'].validate((valid) => {
                if (!valid) {
                    this.$message.closeAll();
                    this.$message({
                        message: '请完成必填信息录入！',
                        type: 'warning',
                    });
                    return;
                }
                let jsonData = deepClone(this.form);
                // jsonData.sRoomName = getOptionName(jsonData.sRoomId, this.optionsLoc.machineRoomOptions, { find: 'sRoomId', get: 'sRoomName' });
                // jsonData.sDistrictPrefix = getOptionName(jsonData.sHospitalDistrictId, this.optionsLoc.districtOptions)
                let times = this.tableData2.filter(item => Object.keys(item).length !== 0);
                let isTrueFormat = false;
                let reg = /^((20|21|22|23|[0-1]\d)\:[0-5][0-9])(\:[0-5][0-9])?$/;  // (00:00~23:59)
                times.map(item => {
                    item.sTime = item.sTime.trim();
                    if (!reg.test(item.sTime)) {
                        isTrueFormat = true
                    }
                })
                if (isTrueFormat) {
                    this.$confirm('时间段格式错误，正确格式 HH:mm', '提示',
                        {
                            type: 'warning',
                            showCancelButton: false,
                            confirmButtonText: '知道了'
                        }).then(() => {

                        })
                    return
                }
                // console.log(times)
                jsonData.times = times;
                this.saveLoading = true;
                if (this.form.sId) {
                    editRegisterTemplate(jsonData).then(res => {
                        this.saveLoading = false;
                        if (res.success) {
                            this.$message.success('保存成功！');
                            this.queryAllRegisterTemplate();
                            return;
                        }
                        this.$message.error(res.msg);
                    }).catch(err => {
                        this.saveLoading = false;
                        console.log(err);
                    })
                    return
                }
                addRegisterTemplate(jsonData).then(res => {
                    this.saveLoading = false;
                    if (res.success) {
                        this.$message.success('保存成功！');
                        this.queryAllRegisterTemplate();
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    this.saveLoading = false;
                    console.log(err);
                })
            })
        },
        // 获取所有号源模板  loadingValue 加载状态值
        queryAllRegisterTemplate (loadingValue = 1) {
            this.loading1 = loadingValue;
            queryAllRegisterTemplate().then(res => {
                this.loading1 = null;
                if (res.success) {
                    this.tableData1 = res.data || [];
                    if (Object.keys(this.selectItem1).length) {
                        let temp = this.tableData1.find(item => item.sId === this.selectItem1.sId);
                        if (temp) {
                            this.$nextTick(() => {
                                this.$refs.mainTable1.setCurrentRow(temp);
                                this.onRowClick1(temp);
                            })
                            return
                        }
                        this.onResetClick(true);
                    }
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading1 = null;
                console.log(err);
            })
        },
        // 获取院区，机房
        getHospital() {
            getHospitalAndRoomData().then((res) => {
                if (res.success) {
                    let data = res.data || []
                    data.map(item => {
                        item.sName = item.sDistrictPrefix;
                        item.sValue = item.sHospitalDistrictId;
                    })
                    data = data.filter(district => {
                        if (district.enable) {
                            const rooms = district.rooms.filter(room => room.enable)
                            district.rooms = rooms || []
                            return true
                        }
                        return false
                    })
                    this.optionsLoc.districtOptions = data
                    // districtData.value = data
                    // if (districtData.value[0]) {
                    //     hospitalId.value = districtData.value[0].sHospitalDistrictId
                    // }
                }
            })
        },
    }

}
</script>
<style lang="scss" scoped>
.g-content {
    .c-left {
        flex: 3;
        padding: 15px;
        // border-right: 10px solid #eee;
        overflow: hidden;
        box-sizing: border-box;
        .c-btns {
            margin-bottom: 15px;
        }
    }
    .c-right {
        flex: 7;
        padding: 0 15px 5px 5px;
        overflow: hidden;
        box-sizing: border-box;
        .c-form {
            overflow: hidden;
        }
        .c-table {
            padding-top: 15px;
            padding-left: 10px;
        }
        .c-btns {
            padding-top: 15px;
            text-align: right;
        }
    }
}
.i-table-input {
    width: 100%;
    // height: 30px;
    :deep(.el-input__inner ){
        text-align: center;
    }
}
</style>
