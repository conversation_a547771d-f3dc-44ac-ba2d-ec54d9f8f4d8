<template>
    <div class="m-flexLaout-ty pos-rea">
        <el-tabs ref="tabsRef" 
            v-model="activeName" 
            class="g-flexChild demo-tabs" 
            :style="styleVar"
            @tab-click="handleTabClick">
            <el-tab-pane label="注射操作" name="InjectForm" 
                v-if="configValue.tabInjectForm">
                <!-- v-if="activeName === 'InjectForm'" -->
                <InjectForm ref="injectForm"
                    v-if="patientInfo.sId"
                    :doctorOptions="optionsLoc.DoctorOptions"
                    @upDateLoading="upDateLoading"
                    @onChangeActiveTab="onToFormTab"
                    ></InjectForm>
                <el-empty v-else :image-size="80" description=" " style="height: 100%;" />
            </el-tab-pane>
            <el-tab-pane name="InjectRecord" 
                v-if="configValue.tabInjectRecord">
                <template #label>
                    <span>注射记录</span>
                    <span class="i-count">({{ recordLength }})</span>
                </template>
                <InjectRecord ref="InjectRecord" :lastFormTabName="lastFormTabName" @onToRecordTab="onToRecordTab"></InjectRecord>
            </el-tab-pane>
            <el-tab-pane label="活度仪采集" name="ActivityMeter" 
                v-if="configValue.tabActivityMeter">
                <!-- v-if="activeName === 'ActivityMeter'" -->
                <ActivityMeter ref="activityMeter"
                    v-if="patientInfo.sId"
                    :isActivePane="activeName === 'ActivityMeter'"
                    :doctorOptions="optionsLoc.DoctorOptions"
                    @upDateLoading="upDateLoading"
                    @onChangeActiveTab="onToFormTab"
                    ></ActivityMeter>
            </el-tab-pane>
            <el-tab-pane label="血糖及给药" name="BloodAndDose" 
                v-if="configValue.tabBloodAndDose">
                <BloodAndDose ref="bloodAndDose"
                    v-if="activeName === 'BloodAndDose'"
                    :doctorOptions="optionsLoc.DoctorOptions"
                    @upDateLoading="upDateLoading"
                    class="c-flex-auto"></BloodAndDose>
            </el-tab-pane>
            
        </el-tabs>
        <div class="c-bottom">
            <div>
                <span class="buttonItem" v-auth="'report:injection:applyOrder'">
                    <ApplyListInfo :patientInfo="patientInfo" buttonName="电子申请单" type="primary" plain></ApplyListInfo>
                </span>
                
                <!-- trigger="click" @visible-change="eldropDownShow -->
                
                <el-dropdown style="margin-left:10px" v-auth="'report:injection:call'" @visible-change="eldropDownShow">
                    <el-button-icon-fa type="primary" 
                        plain
                        icon="fa-call-fill fa">
                        呼叫<Icon name="el-icon-arrow-up"></Icon>
                    </el-button-icon-fa>
                    <template #dropdown>
                        <el-dropdown-menu v-if="callBtnArray?.length">
                            <el-dropdown-item v-for="(item, index) in callBtnArray" :key="index"
                                plain
                                :loading="item.loading"
                                _icon="fa fa-volume-up"
                                size="small"
                                @click="handleQuickCall(item, index)">
                                    <span :class="item.isCalled?'isCalled':''"><i class="fa fa-call-fill"></i>{{item.buttonName}}</span>
                                    <span style="font-size: 18px"><i v-if="item.isCalled" class="el-icon-check isCalled"></i></span>
                                </el-dropdown-item>
                        </el-dropdown-menu>
                        <div v-else style="width: 100px;">
                            <el-empty :image-size="80" description="未配置按钮" />
                        </div>
                    </template>
                </el-dropdown>
                <span style="margin-left:10px" v-auth="'report:injection:bloodSugarMeasure'">
                    <el-button-icon-fa type="primary" plain
                        icon="fa-thermometer- fa"
                        @click="handleBloodCheckClick">
                        血糖测量
                    </el-button-icon-fa>
                </span>
               
                <span v-auth="'report:injection:print'">
                    <ReportPrintBtn 
                        :propParams="{ patient: patientInfo,  isBatch: false, idKey:'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"
                        :buttonMsg="{plain:true,type: 'primary'}"
                        :isPopup="true">
                    </ReportPrintBtn>
                </span>
            </div>
            <div>
                <span v-auth="'report:injection:save'">
                    <el-button-icon-fa
                        style="margin-left: 10px"
                        type="primary"
                        _icon="fa fa-save"
                        :loading="saveLoading"
                        @click="clickSave"
                        >保存</el-button-icon-fa>
                </span>
                
            </div>
        </div>
    </div>
   
     <!-- 血糖测值 -->
    <BloodCheck v-model:show="dialog_BloodCheck_v"
        ></BloodCheck>
    <Call v-model:dialogVisible="call_DialogVisible" 
        :patientInfo="patientInfo" 
        :iModule="{iModuleId:4, iModuleName:'注射管理'}"
        @closeDialog="closeCallDialog"></Call>
</template>

<script>
    import { ArrowDown,Setting, ArrowUp,Delete, Mic } from '@element-plus/icons-vue';
    import InjectForm from '$supersetViews/apricot/injectMng/components/InjectForm.vue'
    import ReportPrintBtn from '$supersetViews/apricot/components/ReportPrintBtn.vue'
    import ApplyListInfo from '$supersetViews/apricot/components/ApplyListInfo.vue'    // 申请单
    import ActivityMeter from '$supersetViews/apricot/injectMng/components/ActivityMeter.vue'    // 活度仪
    import BloodAndDose from '$supersetViews/apricot/injectMng/components/BloodAndDose.vue'    // 血糖及给药-模块
    import BloodCheck from '$supersetViews/apricot/injectMng/components/BloodCheck.vue'
    import Call from '$supersetViews/apricot/components/Call.vue' // 呼叫设置
    import InjectRecord from '$supersetViews/apricot/injectMng/components/InjectRecord.vue'
    

    import { getCalledBtn, handleCallAction } from '$supersetResource/js/projects/apricot/call.js'
    
    import { queryUserListByType } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
    export default {
        
        components:{
            InjectForm,
            ArrowDown,
            Setting,
            ArrowUp,
            Delete,
            Mic,
            ReportPrintBtn,
            ApplyListInfo,
            BloodAndDose,
            ActivityMeter,
            BloodCheck,
            Call,
            InjectRecord
        },
        props:{
            callBtnArray:{
                type: Array,
                default: ()=>{
                    return []
                }
            },
            isFixedWostation: {
                type: Boolean,
                default: ()=>{
                    return false
                }
            }
        },
        emits:['updateTable', 'getInitInJectData', 'updateRowData'],
        setup(props) {
            const iModuleId = 4  // 注射管理标识 ，eName: 'INJECTION'， 在mixinPrintPreview混合模块中调用
            // 患者信息
            const patientInfo = inject('patientInfo', {});
            // 配置项
            const configValue = inject('configValue', {});
            // 当前激活tab
            const activeName = ref('InjectForm');
            // tabs点击事件
            var saveLoading = ref(false)
            var delLoading = ref(false)
            var call_DialogVisible = ref(false)
            var showCalledBtn = ref([])

            var btnsOption = [{}]
            // 血糖测量
            var dialog_BloodCheck_v = ref(false);

            const lastFormTabName = ref('InjectForm');
            
            const count = ref(0)

            return {
                iModuleId,
                patientInfo,
                configValue,
                activeName,
                saveLoading,
                
                btnsOption,
                delLoading,
                optionsLoc: {
                    DoctorOptions:[]
                },
                showCalledBtn,
                dialog_BloodCheck_v,
                call_DialogVisible,
                lastFormTabName,
                count,
            }

        },
        computed:{
            userInfo () {
                let temp = this.$store.getters["user/userSystemInfo"];
                if (temp.__proto__.constructor === Object) {
                    return temp;
                } else {
                    return {};
                }
            },
            styleVar() {
                return {
                "--ativeTabColor": this.configValue.tabBgColor 
                }
            },
            recordLength() {
                var count = this.$refs.InjectRecord?.dataList?.length ||  0;
                this.count = count;
                return count;
            }
        },
        watch: {
            // 'configValue.rightTabPanels': {
            //     handler(val,oldValue) {
            //         if(val.length && oldValue.length > val.length) {
            //             let target = [];
            //             target = oldValue.filter(item=> val.indexOf(item) !== -1);
            //             if(target) {
            //                 this.activeName = target[0]
            //             }
            //         }
            //     }
            // },
            'patientInfo.sId': {
                handler(val, oldValue) {
                    if(val && val !== oldValue) {
                        this.count = undefined
                    }
                }
            },
            'count': {
                handler(val) {
                    if(this.count === undefined) return
                    val ? this.onToRecordTab() : this.onToFormTab();
                },
                immediate:true
            }
        },
        methods:{
            // getPrintClassifyOfModule: exGetPrintClassifyOfModule(),
            clickSave() {
                if(this.activeName === 'InjectForm') {
                    this.saveLoading = true;
                    this.handleSaveInjectData()
                    return
                    
                }
                if(this.activeName === 'ActivityMeter'){
                    this.saveLoading = true;
                    this.$refs.activityMeter.saveData()
                    return
                }
                if(this.activeName === 'BloodAndDose') {
                    // 保存血糖
                    this.saveLoading = true;
                    this.$refs.bloodAndDose.handleSaveDrugClick();
                    return
                }
            },
            // 保存注射信息
            handleSaveInjectData() {
                this.$refs.injectForm.handleSaveInjectClick();
            },
            isUpdateRow() {
                this.$emit('updateRowData', this.patientInfo.sId, this.patientInfo.index)
            },
            onToFormTab() {
                this.activeName = this.lastFormTabName || 'InjectForm';
            },
            onToRecordTab(){
                if(['InjectForm', 'ActivityMeter'].includes(this.activeName)) {
                    this.lastFormTabName = this.activeName;
                }
                this.$nextTick(()=> {
                    this.activeName = 'InjectRecord';
                })
            },
            handleTabClick (tab, event)  {
                const name = tab.paneName;
                if(['InjectForm', 'ActivityMeter'].includes(name)) {
                    this.lastFormTabName = name;
                }
            },
            upDateLoading(val) {
                if(val == 'saveLoading') {
                    this.saveLoading = false
                    return
                }
                this.delLoading = false;
            },
            // 删除注射信息
            handleDelInjectClick() {
                this.delLoading = true;
                this.$refs.injectForm.handleDelInjectClick()
            },

            eldropDownShow(val) {
                if(val) {
                    this.getRowCalled()
                }
            },
            async getRowCalled() {
                if(!this.isFixedWostation) {
                    this.$message.warning('请切换到注射工作站！')
                }
                const id = this.patientInfo.sId
                const calledBtnData = await getCalledBtn(id)
                this.callBtnArray.forEach(item =>{
                    item.isCalled = false
                    if (calledBtnData.includes(item.buttonCode)) {
                        item.isCalled = true
                    }
                })
            },
            // 呼叫
            async handleQuickCall(data, index) {
                if(data.buttonCode == 0) {
                    this.onOpenCall()
                    return
                }
                const row = this.patientInfo
                data.loading = true
                // this.callBtnArray[index] = data 
                let jsonData = {
                    callBtnCode: data.buttonCode,
                    captionsId: "",
                    patientInfoId: row.sId,
                    stationId: data.stationId,
                    sysModuleCode: data.moduleId,
                }
                const isCalled = await handleCallAction(jsonData)
                data.loading = false
                if(isCalled.isCalled) {
                    data.isCalled = true
                }
                this.$emit('updateRowData',row.sId, row.index)
            },
            
            // 血糖测量
            handleBloodCheckClick () {
                if (!this.patientInfo.sId) {
                    this.$message.warning('请选择患者数据！')
                    return
                }
                this.dialog_BloodCheck_v = true;
            },
            hasChangeBlood() {

            },
            // 打开呼叫
            onOpenCall(){
                this.call_DialogVisible = true
            },
             // 关闭呼叫  
            closeCallDialog() {
                this.call_DialogVisible= false
            },
        },
        async mounted() {
            this.optionsLoc.DoctorOptions = await queryUserListByType(4)
        }
    }
</script>
<style lang="scss" scoped >
.pos-rea {
    position: relative;
    box-sizing: border-box;
}
.c-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 10px 5px 10px;
    border-top: 2px solid #e5e8ec;
    box-sizing: border-box;
}

.isCalled {
    color: #67C23A;
}
:deep(.el-tabs) {
    
    >.el-tabs__header {
        margin: 0 0 0 0px
    }
    >.el-tabs__content {
        padding: 10px;
        height: 100%;
        background-color: var(--ativeTabColor);
        // padding: 10px;
    }
}
.setting {
    position: absolute;
    top: 4px;
    right: 10px;
    color: var(--el-color-primary);
    cursor: pointer;
}
.c-contain {
    padding: 10px 15px;
}
.c-item {
    h4 {
        margin: 0 0 8px 0;
        
    }
    h4.print-title {
        margin: 0;
    }
    .c-padding {
        padding: 5px 15px;    
    }
    .padding-lr {
        padding: 0 15px; 
    }
    .c-setting {
        font-weight: bold;
        span {
            color: #868080;
            font-size: 13px;
        }
        .sub-title {
            padding: 15px 0px;
            font-weight: bold;
        }
    }
}
.c-item.t-2 {
    padding-bottom: 20px;

}
</style>
