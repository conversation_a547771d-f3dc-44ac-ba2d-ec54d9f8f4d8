<template>
    <div class="m-flexLaout-ty">
        <el-tabs v-model="activeName" class="g-flexChild demo-tabs" :style="styleVar">
            <el-tab-pane v-if="configValue.tabMachineInfo" name="first">
                <template #label>
                    <span>上机操作</span>
                </template>
                <Machine v-if="patientInfo.sId" :patientInfo="patientInfo" :iModuleId="iModuleId" :formValueConfig="{fPickingRate: configValue.fPickingRate}"
                    :isShowConfigBtn="configValue.isShowConfigBtn" :isSaveAndAllowLeave="configValue.isSaveAndAllowLeave" 
                    :configValue="configValue" @onChangeActiveTab="onChangeActiveTab"></Machine>
            </el-tab-pane>
            <el-tab-pane v-if="configValue.tabMachineRecord" name="second">
                <template #label>
                    <span>上机记录</span>
                    <span class="i-count">({{ machineRecordRef?.tableData.length }})</span>
                </template>
                <MachineRecord ref="machineRecordRef" :patientInfo="patientInfo" :iModuleId="iModuleId" :isShowConfigBtn="configValue.isShowConfigBtn" @toRecordTab="toRecordTab"></MachineRecord>
            </el-tab-pane>
            <el-tab-pane v-if="configValue.tabScannerData" name="third">
                <template #label>
                    <span>扫描资料</span>
                    <span class="i-count">({{ scanImageRef?.serverImages.length }})</span>
                </template>
                <ScanImage :isVisible="activeName === 'third'" ref="scanImageRef" :rights="rights" :isOnlyShowUpload="!configValue.isShowAltimeterPanel"></ScanImage>
            </el-tab-pane>
            <el-tab-pane v-if="configValue.tabPatientImage" name="fourth">
                <template #label>
                    <span>图像</span>
                    <span class="i-count">({{patientImageRef?.imageSum}})</span>
                </template>
                <PatientImage ref="patientImageRef" :patientInfo="patientInfo"></PatientImage>
            </el-tab-pane>
            <el-tab-pane v-if="configValue.tabInjectRecord" name="five">
                <template #label>
                    <span>注射信息</span>
                    <span class="i-count">({{injectRecordRef?.dataList?.length}})</span>
                </template>
                <InjectRecord ref="injectRecordRef" :patientInfo="patientInfo"></InjectRecord>
            </el-tab-pane>
        </el-tabs>

        <div class="c-bottom">
            <div>
                <!-- 呼叫 -->
                <el-dropdown v-if="$auth['report:machine:call']"
                    ref="callDropdownRef" 
                    @visible-change="onCallDropdownClick"
                    :style="{'margin-right':$auth['report:machine:print']? '0':'10px'}">
                    <el-button-icon-fa type="primary" plain icon="fa fa-call-fill">
                        <span>呼叫 </span>
                        <Icon name="el-icon-arrow-up"></Icon>
                    </el-button-icon-fa>
                    <template #dropdown>
                        <el-dropdown-menu v-if="showCalledBtn?.length">
                            <template v-for="(item, index) in showCalledBtn" :key="index">
                                <el-dropdown-item :type="item.isCalled ? 'success' : 'primary'" plain :loading="item.loading"
                                        :class="item.isCalled ? 'isCalled' : ''" _icon="fa fa-volume-up" size="small"
                                        @click="handleQuickCall(item, index)">
                                        <span :class="item.isCalled?'icon-green':''"><i class="fa fa-call-fill"></i>
                                        {{ item.buttonName }}</span>
                                        <i v-if="item.isCalled" class="el-icon-check icon-green" style="margin-left: 5px;"></i>
                                </el-dropdown-item>
                            </template>
                        </el-dropdown-menu>
                        <div v-else style="width: 100px;">
                            <el-empty :image-size="80" description="未配置按钮" />
                        </div>
                    </template>
                </el-dropdown>
                <!-- 打印按钮集合 -->
                <ReportPrintBtn v-if="$auth['report:machine:print']"
                    :propParams="{ patient: patientInfo, isBatch: false, idKey: 'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"
                    :buttonMsg="{ plain:true, type: 'primary' }"
                    :isPopup="true"
                    style="margin-right: 10px"></ReportPrintBtn>

                <!-- 延迟信息保存 -->
                <span class="my-machine-delay"></span>
                <!-- 允许离开 -->
                <span class="my-machine-leave"></span>
            </div>
            <div>
                <!-- 上机信息保存 -->
                <span class="my-machine-save"></span>
            </div>
        </div>

        <Call v-model:dialogVisible="isDialogVisible" :patientInfo="patientInfo"
            :iModule="{ iModuleId: iModuleId, iModuleName: '上机管理' }" @closeDialog="closeCallDialog"></Call>
    </div>
</template>

<script setup>
// 组件
import { ElMessage } from 'element-plus';
import ScanImage from '$supersetViews/apricot/components/ScanImage.vue';
import Machine from './Machine.vue';
import MachineRecord from './MachineRecord.vue';
import PatientImage from './PatientImage.vue';
import ReportPrintBtn from '$supersetViews/apricot/components/ReportPrintBtn.vue';
import Call from '$supersetViews/apricot/components/Call.vue' // 呼叫设置
import InjectRecord from './InjectRecord.vue';
    
import { getCalledBtn, handleCallAction } from '$supersetResource/js/projects/apricot/call.js'

const props = defineProps({
    callBtnArray: {
        type: Object,
        default: new Object()
    },
    callConfigBtnShow: {
        type: Boolean,
        default: false
    }
})
const emits = defineEmits(['updateTableInfo'])
// 扫描资料权限对象 传值
const {appContext: {config: {globalProperties: { $auth }}}} = getCurrentInstance();
const rights = ref({
    deleteFile: !!$auth['report:machine:deleteFile'],
    uploadFile: !!$auth['report:machine:uploadFile'],
    manualUpload: !!$auth['report:machine:manualUpload'],
})


const showCalledBtn = computed(() => props.callBtnArray)


const iModuleId = ref(5); // 上机管理标识 ，eName: 'MACHINE'， 在mixinPrintPreview混合模块中调用

// 患者信息
const patientInfo = inject('patientInfo', {});

// 配置
const configValue = inject('configValue', {});

// 是否显示呼叫dialog
const isDialogVisible = ref(false);

// 当前激活tab
const activeName = ref('first');

// 获取组件实例
const machineRecordRef = ref(null);
const scanImageRef = ref(null);
const patientImageRef = ref(null);
const callDropdownRef = ref(null);
const injectRecordRef = ref(null);


const styleVar = computed(()=> {
    return {
        "--ativeTabColor": configValue.value.tabBgColor
    }
})

// const recordCount = computed(() => machineRecordRef.value?.tableData?.length || 0);
// 若有记录则跳转到上机记录tab
// watch(recordCount, (val) => {
//     if(val) {
//         activeName.value = 'second';
//     } else {
//         activeName.value = 'first';
//     }
// })

const toRecordTab = () => {
    activeName.value = 'second'
}

const onCallDropdownClick = (val) => {
    if(val && !props.callConfigBtnShow) {
        ElMessage.warning('请切换到上机工作站！');
        callDropdownRef.value.handleClose();
        return
    }
    if(val && !patientInfo.value.sId) {
        ElMessage.warning('请选择患者数据');
        callDropdownRef.value.handleClose();
        return
    }
    if(val) {
        getRowCalled()
    }
}
async function getRowCalled() {
    showCalledBtn.value.map( item=>{
        item.isCalled = false
    })
    const calledBtnData = await getCalledBtn(patientInfo.value.sId)
    showCalledBtn.value.forEach(item =>{
        if (calledBtnData.includes(item.buttonCode)) {
            item.isCalled = true
        }
    })
    
}
const closeCallDialog = () => {
    isDialogVisible.value = false
}
// 打开呼叫弹窗
const onOpenCall = () => {
    isDialogVisible.value = true
}

const onChangeActiveTab = () => {
    activeName.value = 'first'
}

// 点击呼叫按钮
async function handleQuickCall(data, index) {
    if(!patientInfo.value.sId){
        ElMessage.warning('请选择患者数据！');
        return
    }
    if (data.buttonCode == 0) {
        onOpenCall()
        return
    }
    const row = patientInfo.value
    data.loading = true
    showCalledBtn.value[index] = data
    let jsonData = {
        callBtnCode: data.buttonCode,
        captionsId: "",
        patientInfoId: row.sId,
        stationId: data.stationId,
        sysModuleCode: data.moduleId,
    }
    const isCalled = await handleCallAction(jsonData)
    data.loading = false
    if(isCalled.isCalled) {
        data.isCalled = true
    }
    emits('updateTableInfo',row.sId, row.index)
    showCalledBtn.value[index] = data
}


</script>

<style lang="scss" scoped >
.c-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 10px 5px 10px;
    border-top: 2px solid #e5e8ec;
    box-sizing: border-box;
}


:deep(.el-tabs) {
    >.el-tabs__header {
        margin: 0 0 0 0px
    }
}
:deep(.demo-tabs>.el-tabs__content) {
    padding: 10px;
    background-color: var(--ativeTabColor);
    // background-color: var(--el-color-primary-light-9);
}

.i-count {
    display: inline-block;
    width: 18px;
}
</style>
