<template>
    <div style="width:100%;min-width: 850px; height:100%;overflow: auto;">
        <ApplyDetail :info="props.applyForm" :isAppointmentModule="true"></ApplyDetail>
    </div>
</template>
<script setup>
    import ApplyDetail from '$supersetViews/apricot/components/ApplyDetail.vue';
    const props = defineProps({
        applyForm: Object
    })
</script>

<!-- <template>
    <TextList :list="textList" :data="props.applyForm" storageKey="ApplyFormInfo" class="none-border">
        <template #dBirthday="{ style }">
            <span :style="style" :title="transformDate(props.applyForm.dBirthday)">
                {{ transformDate(props.applyForm.dBirthday) || '（空）' }}
            </span>
        </template>
        <template #dApplyDate="{ style }">
            <span :style="style" :title="transformDate(props.applyForm.dApplyDate)">
                {{ transformDate(props.applyForm.dApplyDate) || '（空）' }}
            </span>
        </template>
        <template #iExecState="{ style }">
            <span :style="style" :title="execStateTxts[props.applyForm.iExecState + '']"> 
                {{ execStateTxts[props.applyForm.iExecState + ''] || '（空）' }} 
            </span>
        </template>
    </TextList>
</template>
<script setup>
    import { transformDate } from '$supersetResource/js/tools'

    import ConfigsItems from '../configs/configsItems.js'


    const props = defineProps({
        applyForm: Object
    })

    const textList = ref(ConfigsItems.textListApplyFormInfo)
    
    const execStateTxts = {
        '-1': '已撤销申请',
        '0': '未登记',
        '1': '已登记',
        '-': '-'
    }
</script> -->