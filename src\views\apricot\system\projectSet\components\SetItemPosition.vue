<template>
    <!-- 项目设置 项目部位 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa> 
            </div>
            
            <div class="c-form-search">
                <div>
                    <el-input v-model="condition.sItemPositionName"
                        placeholder="检查部位名称"
                        clearable
                        @keyup.enter.native="mxDoSearch">
                    </el-input>
                </div>
                <div>
                    <el-select v-model="condition.iIsEnable"
                        style="width: 100%;"
                        placeholder=""
                        clearable
                        @change="mxDoSearch">
                        <el-option v-for="item in optionsLoc.isEnable"
                            :key="item.sValue"
                            :label="item.sName"
                            :value="item.sValue"></el-option>
                    </el-select>
                </div>
                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch"></el-button-icon-fa>
                </div>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table :data="tableData"
                    v-drag:[config]="tableData"
                    ref="mainTable"
                    size="small"
                    @row-click="onClickRow"
                    border
                    stripe
                    height="100%"
                    id="itemPositionTable"
                    v-if="reRender"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort" >
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button size="small"
                                    link
                                    type="primary"
                                    @click="handleEdit(scope.row)"
                                    >编辑
                                     <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class @click="onClickDel(scope.row)">
                                     删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class="i-sort">排序
                                    <template #icon>
                                        <Icon name="el-icon-rank" color="">
                                        </Icon>
                                    </template>
                                </el-button>

                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                <span v-else> 否 </span>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                        <!-- <template
                            v-slot:header="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="120px"
                    >
                    <el-col :span="24">
                        <el-form-item prop="sItemPositionName" label="检查部位">
                            <el-input 
                                placeholder="检查部位名称"
                                clearable
                                v-model="editLayer.form.sItemPositionName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="启  用：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer><div>
                <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SetItemPosition',
    mixins: [mixinTable, mixinTableDrag],
    props: {  },
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            configTable: [
                {
                    sProp: 'sItemPositionName', sLabel: '检查部位名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '120px',
                },
                {
                    sProp: 'action', sLabel: '操作',
                    sAlign: 'center', sWidth: '210px',
                }
            ],
            rules: {
                sItemPositionName: [{ required: true, message: '请输入名称' }],
            },
            tableData: [],
            reRender: true,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                }
            },
            condition: {
                iIsEnable: ''
            },
            page: { pageCurrent: 1, pageSize: 9999 },
            optionsLoc: {
                isEnable: [
                    { sValue: '', sName: '全部' },
                    { sValue: '1', sName: '启用' },
                    { sValue: '0', sName: '禁用' },
                ]
            },
            sortApi: Api.sortItemPosition
        }
    },
    methods: {
        // 新增
        handleAdd() {
            let params = {
               iIsEnable: 1,
            }
            this.editLayer.form = Object.assign({},params)
            this.dialogTitle = '新增';
            this.dialogVisible = true
            let timeout = setTimeout(()=>{
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({},row)
            this.$nextTick(()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
            
        },
        handleSave() {
            this.editLayer.loading = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)  
                  return
                }
                this.editLayer.loading = false
            })
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledItemPosition({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sItemPositionName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delItemPosition({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            if (!params.sId) {
                Api.addItemPosition(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            } else {
                Api.editItemPosition(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            const jsonData = params.condition;
            Api.getItemPositionData(jsonData).then((res) => {
                if (res.success) {
                    this.tableData = res?.data || [];
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    mounted () {
        this.mxOpenDialog(1, 'no-title')
    },
};
</script>
<style lang="scss" scoped>

.delete-color {
    color: #f56c6c;
}

.c-form .c-form-search {
    >div{
        width: 240px;
        margin:0 5px;
    }
}

</style>
