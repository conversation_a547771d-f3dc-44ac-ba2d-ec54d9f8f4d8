import { getOptionName } from '$supersetResource/js/tools'

// 时间转换为  yyyy-mm-dd HH:MM:SS 或者 yyyy-mm-dd
export function mxToDate(t, status) {
    let dd = new Date(t);
    let y = dd.getFullYear();
    if (isNaN(y) || t == null) {
        return;
    }
    let m = dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1;
    let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
    if (status) {
        let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
        let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
        let s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
        return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
    }
    return y + '-' + m + '-' + d;
}

// 时间转换为  yyyy-mm-dd HH:MM
export function mxFormatterDate(t) {
    let dd = new Date(t);
    let y = dd.getFullYear();
    if (isNaN(y) || t == null) {
        return;
    }
    let m = dd.getMonth() + 1 < 10 ? '0' + (dd.getMonth() + 1) : dd.getMonth() + 1;
    let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
    let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
    let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
    return y + '-' + m + '-' + d + ' ' + h + ':' + mm;
}

// 时间转换为  HH:MM
export function mxFormatterTime(t) {
    let dd = new Date(t);
    let y = dd.getFullYear();
    if (isNaN(y) || t == null) {
        return;
    }
    let m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
    let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
    let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
    let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
    return h + ':' + mm;
}



// 表格行内处理    key-> name 怀孕字段转文字
export function setPregnantText(val) {
    return getOptionName(val, this.iIsPregnantOptions);
}

// 表格行内处理 处方剂量值与其单位拼接
export function setRecipeDose(val, sUnit) {
    val = (val ?? '') + '';
    sUnit = sUnit ?? '';
    return val.length ? val + sUnit: ''
}
// 表格行内处理 血糖单位值与其单位
export function setBloodSugar(val) {
    val = (val ?? '') + '';
    return val.length ? val + 'mmol/L': ''
}
