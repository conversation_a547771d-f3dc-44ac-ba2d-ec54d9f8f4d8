<template>
    <!-- 手动上传 -->
    <el-dialog append-to-body
        :modelValue="dialogVisible"
        class="t-default my-dialog"
        width="900px"
        :close-on-click-modal="false"
        @close="handleCloseDialog">
        <template #header><div 
            class="header-title el-dialog__title">
            <strong>手动上传：</strong>
            <span v-if="patientInfo.sName">{{patientInfo.sName}}</span>
            <span v-if="patientInfo.sSexText">{{patientInfo.sSexText}}</span>
            <span v-if="patientInfo.sAge">{{patientInfo.sAge}}</span>
        </div></template>
        <div class="c-dialog-body">
            <!-- 
                    :action="`${apricotUrl}/attachments/upload`" 
                    :headers="{sCookie: sCookie}"
                    :on-success="handleSuccess"
                    :on-progress="handleProgress"
                    :data="{sInnerIndex: patientInfo.sInnerIndex, sPatientId: patientInfo.sId, sNuclearNum: patientInfo.sNuclearNum, iUploadMode: 1}"
                    :on-error="handleUploadError"
                     -->
            <!-- 
                    action="void"
                    :http-request="customUpload" -->
            <div style="margin-bottom: 10px;display: flex;align-items: center;">
                <el-upload class="upload-demo"
                    style="display: inline-block; margin-right: 10px"
                    action="void"
                    :http-request="customUpload"
                    :on-progress="handleProgress"
                    :data="{sInnerIndex: patientInfo.sInnerIndex, sPatientId: patientInfo.sId, sNuclearNum: patientInfo.sNuclearNum, iUploadMode: 1}"
                    multiple
                    accept="*"
                    :show-file-list="false">
                    <el-button-icon-fa 
                        type="primary"
                        _icon="fa fa-cloud-upload">选择上传文件</el-button-icon-fa>
                </el-upload>

                <el-button-icon-fa 
                    plain
                    _icon="fa fa-repeat"
                    @click="getFiles">刷新</el-button-icon-fa>
            </div>

            <div class="c-item t-1">
                <!-- 表格 -->
                <div class="i-table"
                    v-loading="loading">
                    <el-table size='mini'
                        border
                        :data="tableData"
                        height="500"
                        style="width: 100%;">
                        <el-table-column label="原文件名"
                            prop="sUploadName"
                            show-overflow-tooltip>
                            <template v-slot="{row}">
                                {{ row.sUploadName || row.name }}
                            </template>
                        </el-table-column>
                        <el-table-column v-if="imageInfo.upload"
                            label="上传进度"
                            show-overflow-tooltip>
                            <template v-slot="{row}">
                                <div style="display: flex; align-items:center">
                                    <span v-if="row.isError">失败</span>
                                    <i v-else-if="!row.sId"
                                        class="el-icon-loading"></i>
                                    <span v-else>成功</span>
                                    <el-progress :percentage="row.sId ? 100 : row.percent"
                                        style="width: 200px; margin-left:15px"></el-progress>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="文件说明"
                            show-overflow-tooltip>
                            <template v-slot="scope">
                                <el-input v-if="scope.row.isEdit"
                                    
                                    v-model="scope.row.sMemo"></el-input>
                                <span v-else>{{scope.row.sMemo}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作"
                            align="center"
                            width="150">
                            <template v-slot="scope">
                                <el-button-icon-fa v-if="!scope.row.isEdit"
                                    :disabled="!scope.row.sId"
                                    plain
                                    _icon="fa fa-pencil"
                                    @click="handleEdit(scope.row)"></el-button-icon-fa>
                                <el-button-icon-fa type="primary"
                                    v-else
                                    plain
                                    _icon="fa fa-save"
                                    @click="handleSave(scope.row, scope.$index)"></el-button-icon-fa>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa
                _icon="fa fa-close-1"
                @click="handleCloseDialog">关闭</el-button-icon-fa>
        </template>

    </el-dialog>
</template>

<script>
import { getFiles, attachmentsEdit } from '$supersetApi/projects/apricot/common/files.js'
import { getToken } from "$supersetUtils/auth"
import axios from 'axios'
export default {
    name: 'SelfUpload',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {},
        sFilePath: {
            type: String,
            default: ''
        },
    },
    data () {
        return {
            tableData: [],
            loading: false,
            sCookie: getToken(),
            apricotUrl: window.configs.urls.apricot,
            doEdit: false,
            imageInfo: {
                len: 0,
                currentIdx: 0,
                upload: false,
            },
            fileList: []
        }
    },
    watch: {
        dialogVisible (val) {
            if (val) {
                this.visible = this.dialogVisible;
                this.tableData = []
                if (this.sFilePath) {
                    this.getFiles();
                }
            }
        }
    },
    methods: {
        // 修改附件
        handleEdit (row) {
            row['isEdit'] = 1;
        },
        // 保存修改
        handleSave (row, index) {
            attachmentsEdit(row).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    this.tableData[index] = res.data;
                    this.doEdit = true
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err)
            })
        },
        async customUpload (file) {
            let FormDatas = new FormData();
            FormDatas.append('file', file.file);
            try {
                let res = await axios({
                    url: `${this.apricotUrl}/attachments/upload`,
                    method: 'post',
                    data: FormDatas,
                    params: {
                        sInnerIndex: this.patientInfo.sInnerIndex,
                        sPatientId: this.patientInfo.sId,
                        sNuclearNum: this.patientInfo.sNuclearNum,
                        iUploadMode: 1
                    },
                    headers: {
                        sCookie: this.sCookie,
                        'Content-Type': 'multipart/form-data'
                    },
                    //上传进度
                    onUploadProgress: (progressEvent) => {
                        let num = progressEvent.loaded / progressEvent.total * 100 | 0;  //百分比
                        file.onProgress({ percent: num })     //进度条
                    }
                })
                let { success, data } = res.data;
                if (success) {
                    file.onSuccess(); //上传成功(打钩的小图标)
                    data = data || {};
                    this.tableData.find((item, index) => {
                        if (file.file.uid === item.uid) {
                            this.tableData[index] = Object.assign(item, data)
                        }
                    });
                    this.doEdit = true;
                    return
                }
                this.tableData.find((item, index) => {
                    if (file.file.uid === item.uid) {
                        this.tableData[index]['isError'] = true;
                    }
                });
            } catch (err) {
                console.log(err);
                this.tableData.find((item, index) => {
                    if (file.file.uid === item.uid) {
                        this.tableData[index]['isError'] = true;
                    }
                });
            }

        },
        handleProgress (event, file, fileList) {
            this.fileList = fileList;

            let targetOne = this.tableData.find(item => file.uid === item.uid);
            if (!targetOne) {
                this.tableData.push(file);
            }
            this.imageInfo.upload = true;

            let target = this.tableData.find(item => file.uid === item.uid);


            if(target)(target['percent'] = Math.round(event.percent));

            this.imageInfo.len = fileList.length;
            if (event.percent == 100) {
                this.imageInfo.currentIdx++;
                if (this.imageInfo.currentIdx == this.imageInfo.len) {
                    setTimeout(() => {
                        this.imageInfo.len = 0;
                    }, 500)
                }
            }
        },
        // 获取图片列表
        getFiles () {
            let jsonData = {
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex,
                sNuclearNum: this.patientInfo.sNuclearNum,
                sFilePath: this.sFilePath
            }
            this.loading = true;
            getFiles(jsonData).then(res => {
                this.loading = false;
                if (res.success) {
                    this.imageInfo.upload = false;
                    this.tableData = res.data || [];
                    return
                }
                this.$message.error(res.msg);
                return
            }).catch(err => {
                this.loading = false;
                console.log(err)
            })
        },
        // 关闭弹窗 
        handleCloseDialog () {
            this.$emit('closeDialog', this.doEdit);
        },
        // 上传成功
        handleSuccess (res) {
            if (res.success) {
                this.tableData.unshift(res.data);
                this.doEdit = true
            }
        },
        //上传失败
        handleUploadError (err, file, fileList) {
            // console.log(err, file, fileList)
        },
    },
    created () {
    }
}
</script>

<style lang="scss" scoped>
.header-title {
    font-size: 16px;
    > strong {
        color: #2384d3;
    }
    > span:not(:first-child) {
        margin-right: 15px;
    }
}
.c-dialog-body {
    .c-item.t-1 {
        display: flex;
        padding-bottom: 10px;
        .i-table {
            flex: 1;
        }
        .i-upload {
            width: 180px;
            padding-left: 20px;
        }
    }

    .c-item.t-2 {
        padding: 10px 0;
    }

    .c-item.t-3 {
        margin-bottom: 10px;
        .i-item {
            margin: 0;
            background-color: #2384d3;
            text-indent: 10px;
            color: #fff;
            line-height: 2;
            font-size: 16px;
        }
    }
}

:deep(.el-input--mini .el-input__inner ) {
    color: inherit;
}
</style>
