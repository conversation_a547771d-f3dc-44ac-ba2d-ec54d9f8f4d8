import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'

    // 用户字典
export function getUserDictionaryData(data) {
    return request({
        url: baseURL.broken + '/wholehospitaluser/list',
        method: 'POST',
        data
    })
}
// 科室字典
export function  getDepartDictionaryData(data) {
    return request({
        url: baseURL.broken + '/wholehospitaldept/list/list',
        method: 'POST',
        data
    })
}

export function createDict(data) {
  return request({
      url: baseURL.apricot + '/setting/dict/createDict',
      method: 'POST',
      data
  })
}
export function createItem(data) {
  return request({
      url: baseURL.apricot + '/setting/dict/createItem',
      method: 'POST',
      data
  })
}
export function deleteDict(params) {
  return request({
      url: baseURL.apricot + '/setting/dict/deleteDict',
      method: 'POST',
      params
  })
}
export function deleteItem(params) {
  return request({
      url: baseURL.apricot + '/setting/dict/deleteItem',
      method: 'POST',
      params
  })
}
export function editDict(data) {
  return request({
      url: baseURL.apricot + '/setting/dict/editDict',
      method: 'POST',
      data
  })
}
export function editItem(data) {
  return request({
      url: baseURL.apricot + '/setting/dict/editItem',
      method: 'POST',
      data
  })
}
export function getDictById(params) {
  return request({
      url: baseURL.apricot + '/setting/dict/getDictById',
      method: 'POST',
      params
  })
}
export function getItemByDict(params) {
  return request({
      url: baseURL.apricot + '/setting/dict/getItemByDict',
      method: 'POST',
      params
  })
}
export function getItemById(params) {
  return request({
      url: baseURL.apricot + '/setting/dict/getItemById',
      method: 'POST',
      params
  })
}
export function queryAllDictAndItem(params) {
  return request({
      url: baseURL.apricot + '/setting/dict/queryAllDictAndItem',
      method: 'POST',
      params
  })
}
export function queryDictPage(data) {
  return request({
      url: baseURL.apricot + '/setting/dict/queryDictPage',
      method: 'POST',
      data
  })
}
export function settingDictSort (data) {
  return request({
      url: baseURL.apricot + '/setting/dict/sort',
      method: 'POST',
      data
  })
}

export default { 
  getUserDictionaryData,
  getDepartDictionaryData,
  createDict,
  createItem,
  deleteDict,
  deleteItem,
  editDict,
  editItem,
  getDictById,
  getItemByDict,
  getItemById,
  queryAllDictAndItem,
  queryDictPage,
  settingDictSort
}
