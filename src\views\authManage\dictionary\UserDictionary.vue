<template>
    <!-- <LayoutTable>
        <template #action>
            <el-button-icon-fa type="primary" plain icon="el-icon-refresh" :loading="loading" @click="mxDoRefresh">刷新
            </el-button-icon-fa>
        </template>
        <template v-slot:content>
            
            <el-table :data="tableData" style="width: 100%" height="100%" v-loading="loading"> 
                <xxTableColumn v-model="showColumn" storageKey="" :isLang="false">
                </xxTableColumn>
            </el-table>
        </template>
    </LayoutTable> -->
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-button">
                <el-row :gutter="10">
                    <el-col :span="24">
                        <div>
                            <el-button-icon-fa type="primary" plain icon="el-icon-refresh" :loading="loading" @click="mxDoRefresh">刷新
                            </el-button-icon-fa>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content" v-loading="loading">
                <el-table :data="tableData" id="itemTable" ref="mainTable" size="small"
                    stripe height="100%" style="width: 100%">
                    <!-- <el-table-column align="center"
                        type="selection"
                        width="55">
                    </el-table-column> -->
                    <el-table-column type="expand">
                        <template v-slot="{row,$index}">
                                <div style="background-color: #eee; border-top: 1px solid var(--el-border-color-light)">
                                    <div class="info-item">
                                        <p class="title">登陆信息：</p>
                                        <el-row :gutter="10">
                                            <el-col :span="6">
                                                <span class="label"> 用户名：</span>
                                                <span class="value">{{ row.propsdbUser }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">建立日期：</span>
                                                <span class="value">{{ row.createDate  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">用户密码：</span>
                                                <span class="value">{{ row.userPwd }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">输入拼音：</span>
                                                <span class="value">{{ row.inputCode  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">拼音码：</span>
                                                <span class="value">{{ row.pym  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">五笔码 ：</span>
                                                <span class="value">{{ row.wbm  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">域用户名：</span>
                                                <span class="value">{{ row.domainuser  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 保密等级：</span>
                                                <span class="value">{{ row.secretLevel }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 登陆名：</span>
                                                <span class="value">{{ row.userLoginName }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 组编号：</span>
                                                <span class="value">{{ row.groupCode }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">数字证书：</span>
                                                <span class="value">{{ row.caNo }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">医院编码：</span>
                                                <span class="value">{{ row.hospitalId }}</span>    
                                            </el-col>
                                        </el-row>
                                    </div>
                                     <div class="info-item">
                                        <p class="title">个人信息：</p> 
                                        <el-row :gutter="10">                
                                            <el-col :span="6">
                                                <span class="label">出生日期：</span>
                                                <span class="value">{{row.dateOfBirth}}</span>    
                                            </el-col>
                                            
                                            <el-col :span="6">
                                                <span class="label">身份证号：</span>
                                                <span class="value">{{row.idNumber}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">婚姻状况：</span>
                                                <span class="value">{{row.maritalStatus}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">籍贯：</span>
                                                <span class="value">{{row.birthplace}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">国籍：</span>
                                                <span class="value">{{row.countryOfCitizens}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">民族：</span>
                                                <span class="value">{{row.nationality}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">住宅电话：</span>
                                                <span class="value">{{row.residencePhone}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">办公电话：</span>
                                                <span class="value">{{row.officePhone}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">邮政编码</span>
                                                <span class="value">{{row.postalCode}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">住  址：</span>
                                                <span class="value" :title="row.addressProvince + row.addressStreet">{{row.addressProvince + row.addressStreet}}</span>    
                                            </el-col>
                                            
                                            <el-col :span="6">
                                                <span class="label">奖金银行卡号：</span>
                                                <span class="value" :title="row.bonusBankCardNumber">{{row.bonusBankCardNumber }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">奖金银行类型：</span>
                                                <span class="value">{{row.bonusBankType }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">工资银行账号：</span>
                                                <span class="value" :title="row.payBankAccount">{{row.payBankAccount}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">工资银行类型：</span>
                                                <span class="value">{{row.payBankType }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 入党时间：</span>
                                                <span class="value">{{ row.joiningPartyTime }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">入共产党介绍人：</span>
                                                <span class="value">{{ row.introducingTheCommunistParty}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 入团时间：</span>
                                                <span class="value">{{row.joinTime }}</span>    
                                            </el-col>
                                        </el-row>
                                    </div>
                                    <div class="info-item">
                                        <p class="title">入职信息：</p>
                                        <el-row :gutter="10">
                                            <el-col :span="6">
                                                <span class="label">职位：</span>
                                                <span class="value">{{row.position }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">入职类型：</span>
                                                <span class="value">{{row.entryType }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">职称编码：</span>
                                                <span class="value" :title="row.educationCode">{{ row.educationCode }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">职称评定时间：</span>
                                                <span class="value">{{ row.titleEvaluationTime}}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">资 质：</span>
                                                <span class="value">{{ row.educationFlag == 1?'有':'无' }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 转正日期：</span>
                                                <span class="value">{{ row.positiveDate }}</span>    
                                            </el-col>
                                            
                                            <el-col :span="6">
                                                <span class="label">试用结束日期：</span>
                                                <span class="value">{{row.trialEndDate }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">岗位分类：</span>
                                                <span class="value">{{row.jobClassification }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">岗位名称：</span>
                                                <span class="value">{{row.positionTitle }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">聘任上岗时间：</span>
                                                <span class="value">{{row.appointmentTime }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">聘任截止时间：</span>
                                                <span class="value">{{row.appointmentDeadline }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">参加工作时间：</span>
                                                <span class="value">{{row.timeOfParticipationInWork }}</span>    
                                            </el-col>
                                        </el-row>
                                    </div>
                     
                                    <div class="info-item">
                                        <p class="title">学历/执业：</p>
                                        <el-row :gutter="10">
                                            <el-col :span="6">
                                                <span class="label">学  历：</span>
                                                <span class="value">{{ row.education }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">最高学历：</span>
                                                <span class="value">{{ row.highestEducation }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">学  位：</span>
                                                <span class="value">{{ row.bachelorOfScience }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 毕业学校：</span>
                                                <span class="value" :title="row.graduatedSchool">{{ row.graduatedSchool }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">入学时间：</span>
                                                <span class="value">{{ row.admissionTime  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">毕业时间：</span>
                                                <span class="value">{{ row.graduationTime  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 所学专业：</span>
                                                <span class="value" :title="row.major">{{ row.major }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">学  制：</span>
                                                <span class="value">{{ row.schoolSystem  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">学习形式：</span>
                                                <span class="value">{{ row.waysOfLearning  }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 研究方向：</span>
                                                <span class="value" :title="row.researchDirection">{{ row.researchDirection }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 毕业证书号：</span>
                                                <span class="value" :title="row.graduationCertificateNumber">{{ row.graduationCertificateNumber }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 学位证书号：</span>
                                                <span class="value" :title="row.degreeCertificateNumber">{{ row.degreeCertificateNumber }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label"> 资格证书名称：</span>
                                                <span class="value" :title="row.qualificationCertificateName">{{ row.qualificationCertificateName }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">资格证书编号：</span>
                                                <span class="value" :title="row.qualificationCertificateNumber">{{ row.qualificationCertificateNumber }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">执业证书名：</span>
                                                <span class="value" :title="row.licenseName">{{ row.licenseName }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">执业证书编号：</span>
                                                <span class="value" :title="row.practiceCertificateNumber ">{{ row.practiceCertificateNumber }}</span>    
                                            </el-col>
                                            <el-col :span="6">
                                                <span class="label">执业资格获取时间：</span>
                                                <span class="value">{{ row.licensingQualificationTime }}</span>    
                                            </el-col>
                                        </el-row>
                                    </div>
                                   
                          
                                </div>
                                
                        </template>
                    </el-table-column>
                    <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                        <el-table-column show-overflow-tooltip  :prop="item.sProp" :label="item.sLabel"
                            :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth" :min-width="item.sMinWidth"
                            :sortable="!!item.iSort" >
                            <template v-slot="{row}">
                                <template
                                    v-if="['isDel'].includes(item.sProp)">
                                    {{row[`${item.sProp}`] == 1 ? '是' : '否'}}
                                </template>
                                <!-- <template v-else-if="['dAuthExpireDate', 'dLastHeatbeatTime'].includes(item.sProp)">
                                    {{setTime2yyyyMMDDHHmm(row[`${item.sProp}`])}}
                                </template> -->
                                <template v-else>
                                    <span>{{row[`${item.sProp}`]}}</span>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                   
                </el-table>
            </div>
        </div>
    </div>
</template>
<script>
import { transformDate } from '$supersetUtils/function.js'
import { getUserDictionaryData } from '$supersetApi/projects/apricot/system/dictionary.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'ExportTemplate',
    mixins: [mixinTable],
    props: {},
    data() {
        return {
            loading: false,
            clientEditDiolog: false,
            clientRegistedDiolog: false,
            client: {},
            clientForm: {},








// isDel 删除标识：0:未删除;1:已删除
            configTable: [
                {
                    sProp: 'userNo',
                    sLabel: '工号',
                    sAlign: 'left',
                    sMinWidth: '80px'
                },
                {
                    sProp: 'userName',
                    sLabel: '姓名',
                    sAlign: 'left',
                    sMinWidth: '80px'
                },
                {
                    sProp: 'sex',
                    sLabel: '性别',
                    sAlign: 'center',
                    sWidth: '60px'
                },
                {
                    sProp: 'age',
                    sLabel: '年龄',
                    sAlign: 'center',
                    sWidth: '60px'
                },
               
                
                {
                    sProp: 'userDept',
                    sLabel: '科室',
                    sAlign: 'left',
                    sMinWidth: '80px'
                },
                {
                    sProp: 'accountStatus',
                    sLabel: '状态',
                    sAlign: 'center',
                    sWidth: '60'
                },
                {
                    // 0 为医生,1 为护士，2 为医技，9 为其他；
                    sProp: 'userType',
                    sLabel: '用户类别',
                    sAlign: 'left',
                    sMinWidth: '80px'
                },
                {
                    //职称 医师、主治医师、副主任医师、主任医师等其名称
                    sProp: 'educationTitle',
                    sLabel: '职称',
                    sAlign: 'center',
                    sWidth: '94px'
                },
                
                {
                    sProp: 'birthplace HIP',
                    sLabel: '籍 贯',
                    sAlign: 'center',
                    sMinWidth: '80px'
                },
                
                
                {
                    sProp: 'dateOfEntry',
                    sLabel: '入职日期',
                    sAlign: 'left',
                    sMinWidth: '100px'
                },
                
                {
                    sProp: 'politicalStatus',
                    sLabel: '政治面貌',
                    sAlign: 'center',
                    sWidth: '94px'
                },
               
                {
                    sProp: 'eMail',
                    sLabel: '电子邮箱 ',
                    sAlign: 'center',
                    sWidth: '130px'
                },
                {
                    sProp: 'mobilePhone',
                    sLabel: '移动电话',
                    sAlign: 'center',
                    sWidth: '130px'
                },
                {
                    sProp: 'isDel',
                    sLabel: '删除',
                    sAlign: 'center',
                    sWidth: '130px'
                }
            ],
            d_params_visiable: false,
            d_params_save: false,
            paramsData: [],
            clientInfo: {},
            isDefaultClient: false,
        }
    }, 
    methods: {
      setTime2yyyyMMDDHHmm(val) {
            return transformDate(val, false, 'yyyy-MM-dd HH:mm')
        },
        /**
         * 获取表格数据
         */
        getData() {
            this.loading = true
            getUserDictionaryData().then((res) => {
                if (res.success) {
                    this.tableData = res.data
                    this.loading = false;
                    // 赋选中状态
                    this.setSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    mounted() {

    }
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px 0px 0px;
    background-color: #fff;
    :deep(.c-form) {
        display: flex;
        flex-direction: column;

        .c-form-button {
            padding: 0 10px 10px 10px;
            border-bottom: var(--el-border);
        }

       
    }

    :deep(.c-flex-auto ){
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        padding-bottom: 10px;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;

            >button {
                margin-top: 13px;
            }
        }

        .c-content {
            flex: 1;
            height: 0px;
        }
    }
}
.info-item {
    padding:10px 10px;
    .title {
        padding: 10px 0 4px 0;
        color: #222;
        font-weight: 600;
        margin-bottom: 0;
        margin: 0;
       
    }
    span {
        vertical-align: middle;
        

    }
    span.value {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            font-weight: 500;
        }
    span.label {
        min-width: 120px;
        width: 120px;
        text-align: right;
        color: #7c7d7f;
    }
    .el-row {
        margin-left: 5px !important;
        .el-col {
            height: 22px;
            line-height: 22px;
            display: flex;
        }
    }
}
</style>
