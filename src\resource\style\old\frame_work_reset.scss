/*element-ui reset---------------------------------*/

.el-popper[x-placement^=bottom] {
    margin-top: 7px;
}


/*受reset影响--------------------------*/

.el-date-editor .el-range-separator {
    box-sizing: content-box;
}


/*--------------------------受reset影响*/

$defaultHeight1: 30px;

.m-labelInput {
    .el-input {
        font-size: 13px;
    }

    .el-cascader {
        height: $defaultHeight1;
        width: 100%;
    }

    .el-input--mini {
        font-size: 13px;
    }

    .el-input__icon {
        line-height: $defaultHeight1;
    }

    .el-input .el-input__inner {
        height: $defaultHeight1;
        line-height: $defaultHeight1;
    }

    .el-input__inner {
        padding-top: 1px;
        height: $defaultHeight1;
        line-height: $defaultHeight1;
        border-radius: 2px;
    }

    // .el-input__prefix .el-input__icon.el-icon-date{
    //     padding-top: 8px;
    // }
    // .el-input__prefix .el-input__icon.el-icon-time{
    //     padding-top: 8px;
    // }
    .el-textarea__inner {
        padding-top: 6px;
        border-radius: 2px;
    }

    .el-date-editor .el-range__icon {
        line-height: 14px;
    }

    .el-date-editor .el-range__close-icon {
        line-height: 14px;
    }

    .el-date-editor .el-range-separator {
        line-height: 14px;
    }

    .el-date-editor {
        width: 100%;

        .el-icon-date {
            line-height: 34px;
        }
    }

    &.inputHeight40 {
        .el-input .el-input__inner {
            height: 40px;
            line-height: 40px;
        }
    }

    &.input-border-none {
        .el-input .el-input__inner {
            border: 1px solid transparent;
            padding-top: 0;
        }
    }

    &.el-form-item__content {
        line-height: 36px;
    }

    .el-textarea {
        line-height: 0;
    }
}

.t-defaultHeight1 {
    :deep(.el-input__inner) {
        height: $defaultHeight1;
        line-height: $defaultHeight1;
    }

    :deep(.el-input__icon) {
        line-height: $defaultHeight1;
    }
}

.el-button--default {
    border-color: #D0E1EA;
    // color: #505254;
}

.el-button.t-defult {
    height: $defaultHeight1;
    line-height: 28px;
    padding: 0 10px;
    font-size: 14px;
    border-radius: 3px;

    >.fa {
        font-size: 14px;
        margin-right: .3rem;
    }
}

/*---------------------------------element-ui reset*/


/* my-dialog-------------------------------------- */
/*
* 加前缀 class 覆盖 element ui 弹窗
* class="my-dialog my-full-dialog">
* my-dialog 必须
* my-full-dialog 全屏时使用
* my-dialog-footer 页脚
*/

.my-dialog .el-dialog__header {
    padding: 10px 20px;
    border-bottom: var(--el-border);
    background: var(--el-color-primary-light-9);
}

.my-dialog .el-dialog__title {
    font-size: 16px;
    color: var(--theme-header-bg);
}

.my-dialog .el-dialog__body {
    padding: 10px 20px;
}

.my-dialog .my-dialog-footer {
    height: 48px;
    line-height: 48px;
    margin: 0px 10px;
}

.my-dialog .el-dialog__footer {
    padding: 0px;
    padding-top: 1px;
    border-top: 1px solid var(--el-border);
    background: #f8f8f8;
}

// 半全屏
.my-full-dialog .el-dialog__body {
    height: calc(100% - 95px);
    overflow: hidden;
}

.my-full-dialog.el-dialog.is-fullscreen {
    width: 96%;
    height: 96%;
    margin-top: 1%;
    display: flex;
    flex-direction: column;
}

.my-full-dialog.my-padding-dialog .el-dialog__body {
    // height: calc(100% - 45px);
    flex: 1;
    overflow: auto;

    >div {
        overflow: auto;
        height: 100%;
    }
}

.my-dialog.my-padding-dialog .el-dialog__body {
    padding: 0px;
}

/* --------------------------------------my-dialog */


.el-tooltip__popper {
    max-width: 50%;
    font-size: 14px;
}



.el-tooltip__popper.is-dark {
    line-height: 24px;
    background: #fff;
    min-width: 150px;
    border-radius: 4px;
    border: 1px solid #909399;
    color: #606266;
    text-align: justify;
    font-size: 14px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    word-break: break-all;
}

.popper__arrow::after {
    border-top-color: white !important;
}

.el-tooltip__popper[x-placement^=bottom] .popper__arrow::after {
    border-bottom-color: white !important;
}

/*element-ui tabs-------------------------------------*/

.el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__header {
        margin: 0 0 0 5px;
    }

    .el-tabs__content {
        flex: 1;

        .el-tab-pane {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: auto;
        }
    }
}

.m-checkInputItem {
    display: inline-block;
    float: left;

    .m-checkbox {
        display: inline-block;
        width: 30px;
        height: 30px;
        float: left;
        margin-left: 10px;
        margin-top: 13px;
        background-color: #F4F8FB;
    }

    .el-checkbox__inner {
        position: relative;
        top: 5px;
        left: 5px;
    }

    >span {
        display: inline-block;
        margin-top: 10px;
        line-height: 40px;
        padding-left: 10px;
    }
}
 


/*---------------------------------element-ui 带文字或者复选框的input*/



/*element-ui table---------------------------------*/

.el-table {
    width: 100%;
    color: #505254;

}

/*---------------------------------element-ui table*/

/*element-ui tree---------------------------------*/
.el-tree {
    color: #4c4e50;
    color: #505254;
}

/*element-ui tree---------------------------------*/

/*element-ui dialog--------------------------------*/
.el-dialog.t-default {
    >.el-dialog__header {
        padding: 10px 20px;
        border-bottom: var(--el-border);
        background: var(--el-color-primary-light-9);


        .el-dialog__title {
            font-size: 16px; 
        } 
    }

    .el-dialog__footer {
        padding: 10px 20px 10px;
        border-top: var(--el-border);

        .el-button {
            padding: 7px 20px;
        }
    }
}

/*--------------------------------element-ui dialog*/

/*--------------------------------element-ui 自定义图标大小*/
.el-button--mini,
.el-button--small {
    .fa {
        font-size: inherit;
        vertical-align: 0rem;
    }
}

.el-button [class*=fa-]+span {
    margin-left: 5px;
}

.el-table--mini,
.el-table--small,
.el-table__expand-icon {
    font-size: 13px;
}


.el-tag--mini {
    i.fa {
        font-size: 12px;
        vertical-align: 0;
        margin-right: 2px;
    }
}


/* el-carousel--------------------------- */

.el-carousel {

    &.t-fullHight {
        height: 100%;
    }
}

/* ---------------------------el-carousel */
/* table--------------------------------- */
.el-table.t-noHoverHighlight tbody tr:hover>td {
    background-color: transparent !important
}

/*--------------------------------- table */





// 顶部进度条

#nprogress .bar {
    background: #2384D3 !important;
}
