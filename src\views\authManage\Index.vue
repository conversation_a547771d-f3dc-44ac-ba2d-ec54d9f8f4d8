<template>
  <div class="h-full">
    <DrawerAside v-if="activeKey" :asideWidth="220" :spillage="22">
      <template v-slot:aside>
        <Menu :default-active="menus.findIndex(i => i.key === activeKey)" class="el-menu-vertical" :menuList="menus"
          @select="handleSelect" />
      </template>
      <template v-slot:content>
        <component :is="activeKey"></component>
      </template>

    </DrawerAside>

    <div class="flex w-full h-full items-center justify-center">
      <div class="text-xl">
        无访问权限，请检查您的用户权限
      </div>
    </div>
  </div>
</template>

<script>
// 模块辅助样式
import { defineAsyncComponent } from 'vue'

export default {
  name: 'apricot_userManage',
  components: {
    'AuthManageUsersIndex': defineAsyncComponent(() => import('@/views/authManage/users/index.vue')),
    'AuthManageRolesIndex': defineAsyncComponent(() => import('@/views/authManage/roles/index.vue')),
    'AuthManageFuncIndex': defineAsyncComponent(() => import('@/views/authManage/func/index.vue')),
    'AuthManageDeptADTIndex': defineAsyncComponent(() => import('@/views/authManage/dictionary/DeptDictionary.vue')),
    'AuthManageUserADTIndex': defineAsyncComponent(() => import('@/views/authManage/dictionary/UserDictionary.vue')),
  },
  data() {
    const dataList = [
      {
        name: 'AuthManageUsersIndex',
        // component: () => import('@/views/authManage/users/index.vue'),
        meta: {
          icon: 'fa fa-user',
          title: '用户管理',
          buttonCode: 'report:user:userMng'
        },
      },
      {
        name: 'AuthManageRolesIndex',
        // component: () => import('@/views/authManage/roles/index.vue'),
        meta: {
          icon: 'fa fa-user_1',
          title: '角色管理',
          buttonCode: 'report:user:roleMng'

        },
      },
      {
        name: 'AuthManageFuncIndex',
        // component: () => import('@/views/authManage/func/index.vue'),
        meta: {
          icon: 'fa fa-lock-fill',
          title: '权限管理',
          buttonCode: 'report:user:rightMng'

        },
      },
      {
        name: 'AuthManageDeptADTIndex',
        // component: () => import('@/views/authManage/func/index.vue'),
        meta: {
          icon: 'fa fa-poly-data-dictionary_bak',
          title: '科室字典',
          buttonCode: 'report:user:departmentDict'

        },
      },
      {
        name: 'AuthManageUserADTIndex',
        // component: () => import('@/views/authManage/func/index.vue'),
        meta: {
          icon: 'fa fa-rolesIcon',
          title: '用户字典',
          buttonCode: 'report:user:userDict'

        },
      },
    ].map(item => {
      return {
        key: item.name,
        title: item.meta.title,
        icon: item.meta.icon,
        buttonCode: item.meta.buttonCode,

      }
    })
    return {
      menus: dataList,
      activeKey: '',
      rights: {},

    }
  },
  methods: {
    handleSelect(item) {
      this.activeKey = item.key
    }
  },
  mounted() {
    setTimeout(() => {
      const target = this.menus.filter(item => this.$auth[item.buttonCode]);
      if (target[0]) {
        this.activeKey = target[0].key
      }
    }, 0);

  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style lang="scss" scoped>
.el-menu-vertical {
  background: transparent;
}

.My-drawerAside :deep(.g-content) {
  background: transparent;
}
</style>
