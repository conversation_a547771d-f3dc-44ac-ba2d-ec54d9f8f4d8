<template>
    <el-dialog title="报告编辑页面设置"
        v-model="isDialogVisible"
        width="950"
        append-to-body
        align-center
        :close-on-click-modal="false"
        class="my-dialog t-default">
        <el-tabs v-model="activeName"
            v-loading="loading"
            style="height:660px">
            <el-tab-pane label="编辑页配置"
                name="first">
                <div class="m-flexLaout-tx">
                    <div style="width: 350px;">
                        <div class="c-title">备份定时设置</div>
                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline"
                                style="width:80px;">时长(秒)：</div>
                            <el-input-number v-model="react.settingDataObj.iAutoBackupTime"
                                :precision="0"
                                :min="10"
                                :controls="false"></el-input-number>
                        </div>
                        <div class="c-title">审核打印设置</div>
                        <div class="mt-4 flex items-center mt-s">
                            <el-radio-group v-model="react.settingDataObj.reportModuleSetData.printTypeForSave"
                                style="margin-left: 15px;">
                                <el-radio :label="2">打印</el-radio>
                                <!-- <el-radio :label="1">首次打印</el-radio> -->
                                <el-radio :label="0">不打印</el-radio>
                            </el-radio-group>
                        </div>

                        <div class="mt-4 flex items-center mt-s">
                            <p style="padding-left: 15px;">打印模板类型：</p>
                            <el-checkbox-group v-model="react.settingDataObj.reportModuleSetData.printClassifys"
                                :disabled="react.settingDataObj.reportModuleSetData.printTypeForSave == 0">
                                <el-checkbox v-for="item in react.btnsOption"
                                    :key="item.iClassify"
                                    :label="item.iClassify">{{ item.sClassify }}</el-checkbox>
                            </el-checkbox-group>
                        </div>

                        <div class="c-title">报告提交校验</div>
                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">临床诊断：</div>
                            <el-radio-group v-model="react.settingDataObj.isRequiredConsult"
                                class="m-0">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">注射信息：</div>
                            <el-radio-group v-model="react.settingDataObj.isRequiredInject"
                                class="m-0">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </div>

                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">阴阳性：</div>
                            <el-radio-group v-model="react.settingDataObj.isRequiredQualitative"
                                class="m-0">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </div>

                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">影像质量：</div>
                            <el-radio-group v-model="react.settingDataObj.isRequiredQuality"
                                class="m-0">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">报告质量：</div>
                            <el-radio-group v-model="react.settingDataObj.isRequiredImageQuality"
                                class="m-0">
                                <el-radio :label="1">开启</el-radio>
                                <el-radio :label="0">关闭</el-radio>
                            </el-radio-group>
                        </div>

                        <div class="c-title">病例资料大图查看方式</div>
                        <div class="mt-4 flex items-center mt-s">
                            <el-radio-group v-model="react.settingDataObj.openScannerImageInNewPage"
                                class="m-0">
                                <el-radio :label="1">新页面打开</el-radio>
                                <el-radio :label="0">本页面打开</el-radio>
                            </el-radio-group>
                        </div>

                        <div class="c-title">显示右侧报告列表</div>
                        <div class="mt-4 flex items-center">
                            <el-radio-group v-model="react.settingDataObj.isShowInnerList"
                                class="m-0">
                                <el-radio :label="1">显示</el-radio>
                                <el-radio :label="0">隐藏</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="g-flexChild"
                        style="padding-left: 30px;border-left: 1px solid #e4e7ed;">
                        <div class="c-title">检查技术配置</div>
                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">检查技术：</div>
                            <el-radio-group v-model="react.settingDataObj.isShowProcessRecord"
                                class="m-0">
                                <el-radio :label="1">显示</el-radio>
                                <el-radio :label="0">隐藏</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="mt-4 flex items-center mt-s">
                            <div class="inline">自动填充：</div>
                            <el-radio-group v-model="react.settingDataObj.isFillProcessRecord"
                                class="m-0">
                                <el-radio :label="1">是</el-radio>
                                <el-radio :label="0">否</el-radio>
                            </el-radio-group>
                        </div>
                        <div class="mt-4 flex flex-col mt-s">
                            <div class="mt-2">
                                <span>填充内容配置：</span>
                                <span class="float-right" style="color: #aaa">*注意：请不要粘贴带格式的字段名</span>
                            </div>
                            <div class="w-full mt-2"
                                style="height: 300px;border:1px solid #eee;box-sizing:border-box"
                                :style="{ backgroundColor: themeColor }">
                                <quill-editor v-model="react.settingDataObj.structuralProcessRecord"
                                    :options="{ modules: { toolbar: { container: [] } } }"
                                    class="u-none-toolbar">
                                </quill-editor>
                                <!-- <el-input v-model="react.settingDataObj.structuralProcessRecord" type="textarea" :rows="6"></el-input> -->
                            </div>
                            <div class="mt-2"  style="color: #999">示例：血糖{fBloodSugar}mmol/L</div>
                        </div>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane
                label="列表配置"
                name="second">
                <div class="c-title">列表行内显示流程图</div>
                <div class="mt-4 flex items-center mt-s">
                    <el-radio-group v-model="react.settingDataObj.isShowListFlowRecord"
                        class="m-0">
                        <el-radio :label="1">显示</el-radio>
                        <el-radio :label="0">隐藏</el-radio>
                    </el-radio-group>
                </div>
                <div class="c-title">自动打开图像设置</div>
                <div class="mt-4 flex items-center">
                    <div class="inline inline-1">web重建：</div>
                    <el-radio-group v-model="react.settingDataObj.isOpenImgTab">
                        <el-radio-button :label="1">新窗口打开</el-radio-button>
                        <el-radio-button :label="0">覆盖</el-radio-button>
                    </el-radio-group>
                </div>
                <div class="mt-4 flex items-center">
                    <div class="inline inline-1">关闭时：</div>
                    <el-radio-group v-model="react.settingDataObj.closeImgSwitch">
                        <el-radio-button :label="1">关闭图像</el-radio-button>
                        <el-radio-button :label="0">不触发</el-radio-button>
                    </el-radio-group>
                </div>

                <div class="mt-4 flex items-center"
                    v-for="(item, index) in deviceTypeData"
                    :key="index">
                    <div class="inline inline-1">{{ item.sDeviceTypeName }}：</div>
                    <el-radio-group v-model="react.settingDataObj[item.sId]">
                        <el-radio-button :label="2">web阅图</el-radio-button>
                        <el-radio-button :label="1">web重建</el-radio-button>
                        <el-radio-button :label="0">不触发</el-radio-button>
                    </el-radio-group>
                </div>
            </el-tab-pane>
        </el-tabs>
        <template #footer>
            <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否读取全局设置？')"
                :teleported="false" :persistent="false" @confirm="onClickGlobalStorage">
                <template #reference>
                    <el-button class="float-left" type="primary" plain>{{ ('恢复全局设置') }}</el-button>
                </template>
            </el-popconfirm>
            
            <el-button-icon-fa type="primary"
                plain
                icon="fa fa-save"
                @click="handleSave(1)">保存至全局</el-button-icon-fa>
            <el-button-icon-fa type="primary"
                icon="fa fa-save"
                @click="handleSave(0)">保存</el-button-icon-fa>
            <el-button-icon-fa type="default"
                icon="fa fa-close-1"
                @click="isDialogVisible = false;">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import { exGetPrintClassifyOfModule } from '$supersetResource/js/projects/apricot/mixinPrintPreview.js'
import { getDeviceTypeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { getOnlineConfig, saveOnlineConfig, getGlobalOnlineConfig } from '@/utils'

const defaultStorageData = {
    iAutoBackupTime: 20,
    isRequiredConsult: 0,
    isRequiredInject: 0,
    isRequiredQualitative: 1,
    isRequiredQuality: 0,
    isRequiredImageQuality: 0,
    isOpenImgTab: 0,
    isShowProcessRecord: 0,
    isShowListFlowRecord: 1,
    openScannerImageInNewPage: 1,
    isShowInnerList: 0,
    closeImgSwitch: 1,
    reportModuleSetData: {
        printTypeForSave: 0,
        printClassifys: []
    },
    isFillProcessRecord: 0,
    structuralProcessRecord: ''
}
export default {
    name: 'ReportSetting',
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
    },
    setup () {

        const _store = useStore()

        const userNo = computed(() => {
            let userInfo = _store.getters["user/userSystemInfo"] || {};
            return userInfo.sId
        })

        const react = ref({
            settingDataObj: {
                ...defaultStorageData,
            },
            btnsOption: []

        })
        const activeName = ref('first')
        const themeColor = ref(localStorage.getItem('reportTheme')??'#cee7d1')  // 主题颜色
        return {
            userNo,
            react,
            getOnlineConfig,
            saveOnlineConfig,
            getGlobalOnlineConfig,
            deviceTypeData: [],
            iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
            loading: ref(true),
            storageKey: 'ReportCaseIndexReportSetting',
            activeName,
            themeColor
        }

    },
    computed: {
        isDialogVisible: {
            get () {
                return this.modelValue
            },
            set (val) {
                this.$emit('update:modelValue', val)
            }
        },
        btnsOption: {
            get () {
                return this.react.btnsOption
            },
            set (val) {
                this.react.btnsOption = val
            }
        }
    },
    watch: {
        modelValue (val) {
            if (val) {
                this.initData()
            }
        }
    },
    methods: {
        getPrintClassifyOfModule: exGetPrintClassifyOfModule(),

        // 读取配置
        async getStorage () {
            if (!this.storageKey) return;
            let storageObj = {
                ...defaultStorageData
            }

            await this.getOnlineConfig()
            Object.assign(storageObj,
                this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])

            this.react.settingDataObj = storageObj

            this.saveLocalStorage()

        },
        // 设置缓存
        setStorage (isGlobal = 0) {
            if (!this.storageKey) return;

            const storageObj = this.react.settingDataObj

            this.saveOnlineConfig(storageObj, isGlobal)

            if (!isGlobal) {
                this.$message.success('配置保存成功！');
            }

            this.saveLocalStorage()
        },
        // 读取全局设置
        async onClickGlobalStorage() {
            if (!this.storageKey) return;
            let storageObj = {
                ...defaultStorageData
            }

            await this.getGlobalOnlineConfig()
            Object.assign(storageObj,
                this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])

            this.react.settingDataObj = storageObj;
            this.setStorage();
        },
        setDefaultData () {
            this.react.settingDataObj = {
                ...defaultStorageData
            }

            let settingDataObj = this.react.settingDataObj;
            settingDataObj.iAutoBackupTime = 20;
        },

        saveLocalStorage () {
            //  存local 提供给别的地方使用
            window.localStorage.setItem('reportModuleSetData', JSON.stringify(this.react.settingDataObj.reportModuleSetData));

            window.localStorage.setItem('oAutoSaveTime', JSON.stringify(this.react.settingDataObj));
        },
        // 设置定时保存时长
        handleSave (isGlobal) {
            this.setStorage(isGlobal)
            this.isDialogVisible = false
        },
        initData () {
            this.getPrintClassifyOfModule(this.iModuleId);  // 赋值到this.btnsOption

            this.setDefaultData();
            getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.deviceTypeData = res?.data || [];
                    this.deviceTypeData.forEach(item => {
                        const obj = this.react.settingDataObj
                        obj[item.sId] = [null, undefined].includes(obj[item.sId]) ? 2 : obj[item.sId];
                    })
                    return
                }
                this.$message.error(res.msg)
            }).finally(() => {
                this.getStorage();
                this.loading = false

            })



        }

    },
    mounted () {

    }
}
</script>

<style lang="scss" scoped>
.inline {
    display: inline-block;
    width: 70px;
    text-align: right;
    &.inline-1 {
        width: 100px;
    }
}
.mt-4 {
    margin: 10px 0px 0px 12px;
    &.mt-s {
        margin-top: 0;
    }
}
.c-title {
    position: relative;
    display: block;
    border-left: 2px solid var(--el-color-primary);
    padding-left: 10px;
    margin-top: 30px;
    font-weight: bold;
    margin-bottom: 10px;
}

.el-col {
    line-height: 2;
    text-align: right;
    margin-right: 10px;
    background-color: #f5f5f5;
}

.margin_l {
    margin-left: 10px;
}

.i-checkbox {
    margin-left: 30px;
}
:deep(.u-none-toolbar .ql-toolbar) {
    display: none !important;
}
</style>
