<template>
    <!-- 患者详情 -->
    <el-dialog title="患者详情"
        :modelValue="dialogVisible"
        :close-on-click-modal="false"
        destroy-on-close
        append-to-body
        fullscreen
        class="t-default my-dialog dialog-height90"
        width="1150px"
        @close="handleCloseDialog">
        <div class="c-dialog-body">
            <el-scrollbar class="my-scrollbar">
                <el-form>
                    <div class="i-patientInfoGroup clearfix">
                        <label>患者信息</label>
                        <div>
                            <TextList :list="patientInfoInputList1" :data="appointmentForm" labelWidth="100" :iModuleId="iModuleId"
                                storageKey="PatientInfoReadText1">
                            </TextList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>检查信息</label>
                        <div>
                            <TextList :list="patientInfoInputList2" :data="appointmentForm" labelWidth="100" :iModuleId="iModuleId"
                                storageKey="PatientInfoReadText2">
                            </TextList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>就诊信息</label>
                        <div>
                            <TextList :list="patientInfoInputList3" :data="appointmentForm" labelWidth="100" :iModuleId="iModuleId"
                                storageKey="PatientInfoReadText3">
                            </TextList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>申请信息</label>
                        <div>
                            <TextList :list="patientInfoInputList4" :data="appointmentForm" labelWidth="100" :iModuleId="iModuleId"
                                storageKey="PatientInfoReadText4">
                            </TextList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>其它信息</label>
                        <div>
                            <TextList :list="patientInfoInputList9" :data="appointmentForm" labelWidth="118" :iModuleId="iModuleId"
                                storageKey="PatientInfoReadText9">
                            </TextList>
                        </div>
                    </div>
                </el-form> 
            </el-scrollbar>
        </div>
        <template #footer>
            <el-button-icon-fa
                _icon="fa fa-close-1"
                @click="handleCloseDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import config from './config/index'
import textList from './config/textList'
import { transformDate} from '$supersetResource/js/tools'

import { getPatientInfo } from '$supersetApi/projects/apricot/common'

export default {
    name: 'PatientInfoRead',
    emits: ['closeDialog'],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientSid: {
            type: String,
            default: ''
        },
        iModuleId: {
            default: ''
        }
    },
    data () {
        return {
            appointmentForm: {},
            patientInfoInputList1: [...textList.appointmentInputList1],
            patientInfoInputList2: [...textList.appointmentInputList2],
            patientInfoInputList3: [...textList.appointmentInputList3],
            patientInfoInputList4: [...config.appointmentInputList4, ...config.appointmentInputList6],
            patientInfoInputList9: [...textList.appointmentInputList5, ...config.appointmentInputList9],
        }
    },
    watch: {
        dialogVisible: {
            async handler (newValue) {
                if (newValue) {
                    await this.getPatientInfo();
                    return
                }
            }
        },
    },
    methods: {
        transformDate: transformDate,
        // 获取患者基本信息
        async getPatientInfo () {
            let sId = this.patientSid;
            if (!sId) return;
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            await getPatientInfo({ sId: sId }).then(res => {
                loading.close()
                if (res.success) {
                    this.setAppointmentForm(res.data);
                    this.appointmentForm.dBirthday = this.transformDate(this.appointmentForm.dBirthday);
                    const fRecipeDose = this.appointmentForm.fRecipeDose;
                    this.appointmentForm.fRecipeDose = ![undefined, null].includes(fRecipeDose) ? fRecipeDose + this.appointmentForm.sRecipeDoseUnit : '';
                }
            }).catch(err => {
                loading.close()
                console.log(err)
            })
        },
        setAppointmentForm (data) {
            for (let key in data) {
                this.appointmentForm[key] = data[key] || undefined;
            }
        },
        // 关闭弹窗
        handleCloseDialog () {
            this.$emit('closeDialog');
        },
    }
}
</script>

<style lang="scss" scoped>
:global(.dialog-height90.is-fullscreen) {
    height: 90%;
    width: 80%;
    top: 5vh;
}
:global(.dialog-height90 .el-dialog__body) {
    height: calc(100% - 118px);
}
.c-dialog-body {
    height: 100%;
    overflow: auto;
}

.i-patientInfoGroup {
    padding: 10px 0 0;
    > label {
        display: block;
        margin: 5px 0;
        padding: 0 10px;
        border-left: 3px solid var(--el-color-primary-light-3);
        font-size: 16px;
    }
    :deep(.grid-box) {
        border-top: none;
    }
}
</style>
