<template>
    <el-dialog :close-on-click-modal="false"
        append-to-body
        fullscreen
        title="检验值管理"
        :modelValue="visible"
        destroy-on-close
        @close="closeDialog"
        top="2vh"
        class="my-dialog t-default my-full-dialog height-96">
        <div class="c-container">
            <div class="c-left m-flexLaout-ty"
                v-loading="treeLoading">
                <h4>项目</h4>
                <div class="g-flexChild">
                    <el-scrollbar style="height: 100%">
                        <el-tree :data="treeData"
                            node-key="sId"
                            highlight-current
                            :expand-on-click-node="false"
                            ref="tree"
                            @node-click="onNodeClick"
                            :props="defaultProps"
                            default-expand-all
                            :default-expanded-keys="treeExpandIndex">
                            <template v-slot="{ data }">
                            <span :title="data.sName">{{data.sName}}</span>
                            </template>
                        </el-tree>
                    </el-scrollbar>
                </div>
            </div>

            <div class="c-right">
                <div class="c-flex-context ">
                    <div class="c-form">
                        <div class="c-form-button">
                            <el-button-icon-fa 
                                icon="el-icon-plus"
                                type="primary"
                                @click="addInspectionDataItem()">新增</el-button-icon-fa>
                            
                            <el-button-icon-fa 
                                icon="el-icon-edit"
                                type="primary"
                                plain
                                @click="editData">编辑</el-button-icon-fa>
                        </div>
                        <div>
                            
                        </div>
                    </div>
                    <div class="c-flex-auto">
                        <div class="c-content">
                            <el-table v-loading="loading"
                                :data="tableData"
                                id="itemTable"
                                ref="mainTable"
                                highlight-current-row
                                border
                                stripe
                                height="100%"
                                style="width: 100%">
                                <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                                    <el-table-column show-overflow-tooltip
                                        :prop="item.sProp"
                                        :label="item.sLabel"
                                        :fixed="item.sFixed"
                                        :align="item.sAlign"
                                        :width="item.sWidth"
                                        :min-width="item.sMinWidth"
                                        :sortable="!!item.iSort" >
                                        <template v-slot="{row, $index}">
                                            <template v-if="item.sProp === 'action'">
                                                <el-button-icon-fa v-if="row.sId" type="danger" link icon="el-icon-delete" @click="delData(row, $index)">
                                                    删除
                                                </el-button-icon-fa>
                                            </template>
                                            <template v-else>
                                                <span v-if="!row.isEdit">{{row[`${item.sProp}`]}}</span>
                                                <el-input v-if="row.isEdit" v-model="row[`${item.sProp}`]" size="small" style="width:100%" />
                                            </template>
                                        </template>
                                    
                                    </el-table-column>
                                </template>
                            </el-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa 
                _icon="fa fa-save"
                type="primary"
                @click="saveData">保存</el-button-icon-fa>
            <el-button-icon-fa _icon="fa fa-close-1" @click="$emit('update:dialogVisible', false)">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>

import { getItemTreeData} from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { getInspectionData, saveInspectionData, delInspectionData } from '$supersetApi/projects/apricot/system/inspectionDataSet.js'
export default {
    name:'inspectionDataSet',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        sProjectId: {
            type: String,
            default: ''
        }
    },
    emits: ['updateInspectionData'],
    components: {},
    data () {
        return {
            loading: false,
            treeLoading: false,
            defaultProps: {
                children: 'childs',
                label: 'sName',
                class: (data)=> `nid_${data.sId}`
            },
            treeData: [],
            tableData: [],
            selectedNode: {},
            treeExpandIndex: [],
            defualtVal: {
                editLayer: {
                    isEnable: 1,
                }
            },
            condition: {},
            configTable: [{
                sProp: 'iIndex',
                sLabel: '排序号',
                sAlign: 'center',
                sMinWidth: '80px'
            },
            {
                sProp: 'sCode',
                sLabel: '项目编码',
                sAlign: 'center',
                sMinWidth: '80px'
            },
            {
                sProp: 'sName',
                sLabel: '项目名称',
                sAlign: 'left',
                sMinWidth: '120px'
            },
            {
                sProp: 'sUnit',
                sLabel: '单位',
                sAlign: 'center',
                sMinWidth: '80px'
            },
            {
                sProp: 'dMinValue',
                sLabel: '最小值',
                sAlign: 'center',
                sMinWidth: '80px'
            },
            {
                sProp: 'dMaxValue',
                sLabel: '最大值',
                sAlign: 'center',
                sMinWidth: '80px'
            },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '100px'
            }],
            isEnableRequest: false
        }
    },
    computed: {
        visible() {
            return this.dialogVisible
        }
    },
    watch: {
        dialogVisible(val) {
            if(val) {
                this.selectedNode = [];
                this.getTreeData();
            }
        }
    },
    methods: {
        closeDialog() {
            this.$emit('update:dialogVisible', false);
            this.$emit('updateInspectionData');
        },
        mxOnClickReset () {
            this.condition = {};
        },
        addInspectionDataItem(){
            this.tableData.push({
                sId:null,
                sCode: '',
                sName: '',
                sUnit:'',
                dMaxValue:'',
                dMinValue:'',
                iIndex:'',
                sProjectId:this.selectedNode.sId,
                isEdit: true
            })
        },
        // 保存新增
        saveData(){
            // if(!this.editTable) {
            //     this.$message.error('未编辑无需保存！')
            //     return
            // }
            let data = this.tableData;
            let loading = this.$loading({
                lock: true,
                text: '正在处理，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            saveInspectionData(data).then(res =>{
                loading.close();
                if(res.success){
                    this.editTable = false
                    this.$message.success(res.msg) 
                    this.getData(this.selectedNode.sId)
                    return
                }
                this.$message.error(res.msg)
            }).catch(err =>{
                loading.close();
                console.log(err)
            })
        },
        // 删除数据
        delData(row, index) {
            if (!row.sId) {
                this.tableData.splice(index,1)
                return
            }
            let params = {
                sId : row.sId
            }
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                let loading = this.$loading({
                    lock: true,
                    text: '正在处理，请稍等',
                    background: 'rgba(0, 0, 0, 0.2)'
                });
                delInspectionData(params).then(res => {
                    loading.close();
                    if(res.success) {
                        this.$message.success(res.msg) 
                        this.getData(this.selectedNode.sId)
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    loading.close();
                    console.log(err)
                })
            })
        },
        editData(){
            if(!this.tableData.length) {
                this.$message.error('没有要编辑的数据！')
                return
            }
            this.tableData.map(  (item,index)=>{
                item.isEdit = true
                this.tableData[index] =  item
            })
            // this.tableData[index]['isEdit'] =  true
        },
        // 点击树
        onNodeClick (data) {
            this.editTable = false
            this.selectedNode = data;
            this.getData(this.selectedNode.sId)
        },
        // 获取表格数据
        getData(projectId){
            this.loading = true
            let params = {
                condition:{
                    sProjectId:projectId
                },
                orders: {orderInfoList: []},
                page: {pageCurrent: 1, pageSize: 9999}
            }
            getInspectionData(params).then(res =>{
                this.loading = false
                if(res.success) {
                    let arr=[]
                    arr = res.data.recordList? res.data.recordList:[]
                    if(arr.length) {
                        arr.forEach(element => {
                            if(Math.floor(element.dMaxValue)===element.dMaxValue) {
                                element.dMaxValue = this.toFixed(element.dMaxValue)
                            }
                            if(Math.floor(element.dMinValue)===element.dMinValue) {
                                element.dMinValue = this.toFixed(element.dMinValue)
                            }
                        });
                    }
                    this.tableData = arr
                    return
                }
                this.$message.error(res.msg)
            }).catch(err =>{
                this.loading = false
                console.log(err)
            })
        },
        // 通过项目id查找树节点id
        getNodeIdByProjectId() {
            let targetList = [ ...this.treeData ];
            let tileList = [];
            targetList.map(item => {
                if(item?.childs.length) {
                    tileList = [...tileList, ...item.childs]
                }
            })
            const nodeItem = tileList.find(item => this.sProjectId === item.sItemId);
            return nodeItem || {}
        },
        // 获取树数据
        getTreeData () {
            this.treeLoading = true
            getItemTreeData({}).then(res => {
                this.treeLoading = false
                if (res.success) {
                    this.treeData = res.data ? res.data : [];
                    if(this.sProjectId && !this.selectedNode.sId) {
                        const nodeItem = this.getNodeIdByProjectId();
                        if(!nodeItem.sId) return
                        this.treeExpandIndex = [nodeItem.sId];
                        this.selectedNode = nodeItem;

                        this.$nextTick(() => {
                            this.$refs.tree.setCurrentKey(this.selectedNode.sId);
                            this.onNodeClick(nodeItem);
                            let dom = document.querySelector(`.nid_${this.selectedNode.sId}`)
                            dom && dom.scrollIntoView(true)
                        });
                        return
                    }
                    // 存在树，默认选中第一个节点的子节点
                    if (!this.treeExpandIndex.length && this.$refs.tree) {
                        // 有选中
                        this.treeExpandIndex = [this.treeData[0].childs[0].sId];
                        this.selectedNode = this.treeData[0].childs[0];

                        this.$nextTick(() => {
                            this.$refs.tree.setCurrentKey(this.selectedNode.sId);
                            this.onNodeClick(this.selectedNode)
                        });
                    }
                    //如果选中节点，刷新树时，保持选中
                    if (this.treeExpandIndex.length && Object.keys(this.selectedNode).length && this.$refs.tree) {
                        this.$nextTick(() => {
                            this.$refs.tree.setCurrentKey(this.selectedNode.sId);
                            this.onNodeClick(this.selectedNode)
                        });
                    }
                    return;
                }
                this.treeData = [];
            }).catch(e => {
                this.treeLoading = false;
                console.log(e);
            })
        },
        toFixed(num) { 
            return (Math.round(num*100)/100).toFixed(2); 
        }
       
    },
    mounted () {
        
        // this.getTableData();
        // this.$nextTick(() => {
        //     this.rowDrop()
        // });
    },
}
</script>
<style lang="scss" scoped>
:global(.height-96 .el-dialog__body) {
    height: calc(100% - 120px);
}
.c-container {
    height: 100%;
    display: flex;
    padding: 0 5px;
    .c-left {
        width: 340px;
        border-right: 1px solid #eee;
        overflow: hidden;
        overflow: auto;
        :deep(.el-tree-node__content ){
            height: 28px;
        }
    }
    h4 {
        margin: 0;
        padding:10px; 
        border-bottom: 1px solid #eee;
    }
    .c-right {
        flex: 1;
        overflow: hidden;
        .c-flex-context {
            height: 100%;
            display: flex;
            flex-direction: column;
            :deep(.c-form) {
                display: flex;
                justify-content: space-between;
                padding: 10px 0px 10px 15px;
                .c-form-input {
                    padding: 15px 15px 0 5px;
                }
                .c-form-button {
                    
                    // margin-left: 10px;
                    border-bottom: 1px solid #eee;
                }
                .el-form-item--mini.el-form-item {
                    margin-bottom: 0;
                }
                .el-input-number.el-input-number--mini {
                    width: 100%;
                }
                .el-select {
                    width: 100%;
                }
            }
            :deep(.c-flex-auto) {
                flex: 1;
                display: flex;
                flex-direction: column;
                padding-left: 10px;
                .c-search {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    padding: 10px 15px 10px 5px;
                }
                .c-content {
                    flex: 1;
                    height: 0px;
                }
            }
            .m-labelInput {
                width: calc(100% - 10px);
            }
        }
    }
}
.action {
    cursor: pointer;
    font-size: 16px;
}
:deep(.el-table td) {
    padding: 2px 0;
}
</style>
