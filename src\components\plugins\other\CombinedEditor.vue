<template>
    <quill-editor
        :modelValue="combinedContent"
        :options="options"
        ref="myQuillEditor"
        storageKey="ReportIndexEditor" :iModuleId="6"
        @ready="onEditorReady"
        :disabled="disabled"
    ></quill-editor>
</template>

<script>


export default {
    name: "CombinedEditor",
    components: {
    },
    props: {
        options: {
            type: Object,
            required: true,
        },
        sProcessRecord: {
            type: String,
        },
        sInspectSee: {
            type: String,
        },
        sDiagnosticOpinion: {
            type: String,
        },
        disabled: {
            type: Boolean,
            default: false
        },
        dataArray: {
            type: Array,
        }
        // highlightArray: {
        //     type: Array,
        // }
    },
    emits: ['input','update:sProcessRecord',
    'update:sInspectSee',
    'update:sDiagnosticOpinion'],
    data() {
        return {
          editor: null,
            combinedContent: '',
            head1: '<hr mark="head" class="ql-head-type1" contenteditable="false" draggable="false">',
            head23: '<hr mark="head" class="ql-head-type23" contenteditable="false" draggable="false">',
            mark3: '<hr mark="divider" class="ql-divider-type3" contenteditable="false" draggable="false">',
            mark12: '<hr mark="divider" class="ql-divider-type12" contenteditable="false" draggable="false">',
            inputPositionIndex: 0
        };
    },
    computed: {
        
        ProcessRecordInput() {
            return this.sProcessRecord || '<p><br></p>'
        },
        InspectSeeInput() {
            return this.sInspectSee || '<p><br></p>'
        },
        DiagnosticOpinionInput() {
            return this.sDiagnosticOpinion || '<p><br></p>'
        },
        hasProcessRecord() {
            return (this.dataArray || []).findIndex(item => item.sProp === 'sProcessRecord') > -1 // 检查技术
        },
        hasInspectSee() {
            return (this.dataArray || []).findIndex(item => item.sProp === 'sInspectSee') > -1 // 检查所见
        },
        displayType() {
            if (this.hasInspectSee && this.hasProcessRecord) {
                return 3 // 检查技术 检查所见 诊断意见
            } else if (this.hasProcessRecord) {
                return 2 // 检查技术 诊断意见
            } else {
                return 1 // 检查所见 诊断意见
            }
        }
    },
    watch: {
        ProcessRecordInput: function () {
            this.outerChange()
        },
        InspectSeeInput: function () {
            this.outerChange()
        },
        DiagnosticOpinionInput: function () {
            this.outerChange()
        },
    },
    methods: {
        onEditorReady(quill) { 
          this.editor = quill
            const container = this.editor.container
            const editorDom = container.querySelector('.ql-editor')

            quill.on('selection-change', (range) => {
                if (range) {
                    this.inputPositionIndex = range.index
                }
            });

            quill.on('text-change', (delta, oldDelta, source) => {
                if (source == 'user') {
                    let contentHtml = editorDom.innerHTML
                    const head = this.displayType === 1 ? this.head1 : this.head23
                    contentHtml = contentHtml.replace(head, '')
                    contentHtml = this.HtmlContentFilter(contentHtml)

                    let index = this.editor.selection.savedRange.index
                    const ops = delta.ops
                    const [line] = this.editor.getLine(index + 1)

                    // 列表内按回车换行的处理
                    if (line.domNode && line.domNode.tagName === 'LI') {
                        const brOps = ops.filter(item => item.insert && (item.insert === '\n'))
                        if (brOps.length === 1) {
                            index += 1
                        } else if (brOps.length === 2) {
                            index += 2
                        }
                    } else {
                        // 按回车换行的处理 （避免在分隔符前和在末尾换行会被吞掉的问题）
                        const br = ops.filter(item => item.insert && (item.insert === '\n\n'))
                        if (br.length === 1 && ops.length === 2) {
                            const ifLineTail = this.editor.getText(index + 1, 1) === '\n' // 当输入光标在一段的末尾时
                            const ifArticleEnd = !line.next //  在全文末尾时
                            const ifDividerBlot = line.next && line.next.domNode.tagName === 'HR' // 当分隔符前时
                            if (ifLineTail && (ifArticleEnd || ifDividerBlot)) {
                                // console.log('文末换行特殊处理')  
                                return  // 跳过该次触发的后续步骤即可解决
                            } else {
                                index += 1
                            }
                        }
                    }

                    // 按空格键的处理 （避免多个空格合并问题） deprecated
                    // const space = ops.filter(item => item.insert && (item.insert === ' '))
                    // if (space.length === 1 && ops.length === 2) {
                    //     return
                    // }

                    // 复制粘贴处理 todo
                    // 文本内容特别多时 快速键盘输入会有卡顿 todo

                    this.inputPositionIndex = index

                    const displayType = this.displayType
                    const mark = this.displayType === 3 ? this.mark3 : this.mark12
                    const splitArr = contentHtml.split(mark)
                    let sProcessRecord = '<p><br></p>'
                    let sInspectSee = '<p><br></p>'
                    let sDiagnosticOpinion = '<p><br></p>'

                    switch (displayType) {
                        case 1:
                            sInspectSee = splitArr[0]
                            sDiagnosticOpinion = splitArr[1]
                            break
                        case 2:
                            sProcessRecord = splitArr[0]
                            sDiagnosticOpinion = splitArr[1]
                            break
                        case 3:
                            sProcessRecord = splitArr[0]
                            sInspectSee = splitArr[1]
                            sDiagnosticOpinion = splitArr[2]
                            break
                    }

                    let changedDataIndex = []
                    if (this.sProcessRecord !== sProcessRecord) {
                        this.$emit('update:sProcessRecord', sProcessRecord)
                        changedDataIndex.push(0)
                    }
                    if (this.sInspectSee !== sInspectSee) {
                        this.$emit('update:sInspectSee', sInspectSee)
                        changedDataIndex.push(1)
                    }
                    if (this.sDiagnosticOpinion !== sDiagnosticOpinion) {
                        this.$emit('update:sDiagnosticOpinion', sDiagnosticOpinion)
                        changedDataIndex.push(2)
                    }

                    this.$emit('input', splitArr, changedDataIndex)
                }
            });

        },

        outerChange() {
            const displayType = this.displayType
            // head      mark    mark
            //     检查所见 诊断意见 
            //     检查技术 诊断意见
            //     检查技术 检查所见 诊断意见

            let combinedContent = ''
            switch (displayType) {
                case 1:
                    combinedContent = `${this.head1}${this.InspectSeeInput}${this.mark12}${this.DiagnosticOpinionInput}`
                    break
                case 2:
                    combinedContent = `${this.head23}${this.ProcessRecordInput}${this.mark12}${this.DiagnosticOpinionInput}`
                    break
                case 3:
                    combinedContent = `${this.head23}${this.ProcessRecordInput}${this.mark3}${this.InspectSeeInput}${this.mark3}${this.DiagnosticOpinionInput}`
                    break
            }
            this.combinedContent = this.HtmlContentFilter(combinedContent)

            if (this.editor && this.editor.hasFocus()) {
                this.$nextTick(() => {
                    this.editor.setSelection(this.inputPositionIndex || 2, 0, 'user')
                })
            }
        },
        // 插入模板内容函数， 外部调用
        outerInsertContent({ sModelEssayText }) { // 插入的时候光标可能不在要插入的那个区块,总之会直接插入到光标位置
            const index = this.editor.selection.savedRange.index
            this.editor.clipboard.dangerouslyPasteHTML(index, sModelEssayText, 'user');
            this.$nextTick(() => {
                this.editor.setSelection(this.inputPositionIndex, 0, 'user')
            })
        },
        outerSetContent({ sModelEssayText, mark }, ifAppend = false) {

            let elementArray = {
                sProcessRecord: 1,        // 检查技术
                sInspectSee: 2,       // 检查所见
                sDiagnosticOpinion: 3, // 诊断意见
            }

            const container = this.editor.container
            const editorDom = container.querySelector('.ql-editor')
            const hrArr = editorDom.querySelectorAll('hr')
            const hrIndexArr = []
            hrArr.forEach(dom => {
                const blot = Quill.find(dom)
                hrIndexArr.push(this.editor.getIndex(blot)) // 获取mark的index 区分模块
            })

            const id = elementArray[mark]
            let startIndex = -1
            let endIndex = 1e5
            const ifShowPart3 = this.displayType === 3
            const flag1 = ifShowPart3 ? 1 : 0
            const flag2 = ifShowPart3 ? 2 : 1
            switch (id) {
                case 1: // 检查技术
                    if (hrIndexArr[0] > -1 && hrIndexArr[1] > -1) {
                        startIndex = hrIndexArr[0] + 1
                        endIndex = hrIndexArr[1] - 1
                    }
                    break
                case 2: // 检查所见
                    if (hrIndexArr[flag1] > -1 && hrIndexArr[flag2] > -1) {
                        startIndex = hrIndexArr[flag1] + 1
                        endIndex = hrIndexArr[flag2] - 1
                    }
                    break
                case 3: // 诊断意见
                    if (hrIndexArr[flag1] > -1 && hrIndexArr[flag2] > -1) {
                        startIndex = hrIndexArr[flag2] + 1
                        endIndex = 1e5
                    }
                    break
                default:
                    break
            }

            if (ifAppend) {
                this.editor.history.cutoff();
                this.editor.clipboard.dangerouslyPasteHTML(endIndex, sModelEssayText, 'user');

            } else {
                this.editor.history.cutoff();
                this.editor.setSelection(startIndex, 0, 'api')
                this.editor.deleteText(startIndex, endIndex, 'user')
                this.editor.history.cutoff();
                this.editor.clipboard.dangerouslyPasteHTML(startIndex, sModelEssayText, 'user');
                this.editor.history.cutoff();
            }


        },
        HtmlContentFilter(html) {
            let result = (html)
            result = this.markFilter(result)
            return result
        },
        // 过滤mark head
        markFilter(html) {
            let input = html

            const mark = this.displayType === 3 ? this.mark3 : this.mark12
            const limitNum = this.displayType === 3 ? 3 : 2
            const markMatches = html.matchAll(new RegExp(mark, 'g'))
            Array.from(markMatches, (result, index) => {
                if (index < limitNum - 1) return
                if (result && result[0]) {
                    input = input.slice(0, result.index) + input.slice(result.index + mark.length)
                }
            })

            const head = this.displayType === 1 ? this.head1 : this.head23
            const headMatches = html.matchAll(new RegExp(head, 'g'))
            Array.from(headMatches, (result, index) => {
                if (index < 1) return
                if (result && result[0]) {
                    input = input.slice(0, result.index) + input.slice(result.index + mark.length)
                }
            })
            return input
        },
    },
    created() {
    },
    mounted() {
        this.outerChange() 
    }
};
</script>

<style lang="scss">
</style>
