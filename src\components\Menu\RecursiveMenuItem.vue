<template>
  <template v-for="(item, index) in menuList">
    <el-sub-menu v-if="Array.isArray(item.children)" :index="`${index}`">
      <template v-slot:title>
        <i v-if="item.icon" :class="item.icon"></i>
        <span>{{ item.title }}</span>
      </template>
      <recursive-menu-item :menu-list="item.children" :key="`${index}-${childIndex}`"
        @click="$emit('click', item, `${index}-${childIndex}`)" v-slot="{ expand }">
        <i :class="expand ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
      </recursive-menu-item>
    </el-sub-menu>
    <el-menu-item v-else v-auth="item.buttonCode" :index="`${index}`" :key="`${index}`"
      @click="$emit('click', item, `${index}`)">
      <template v-slot:title>
        <i v-if="item.icon" :class="item.icon" class=" leading-none pr-2"></i>
        <span>{{ item.title }}</span>
      </template>
    </el-menu-item>
  </template>
</template>

<script>
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'RecursiveMenuItem',
  props: {
    // 菜单列表数据
    menuList: {
      type: Array,
      required: true
    }
  },
  emits: ['click'],

  setup(props, context) {
    const expand = ref(false)
    return {
      expand
    }
  }
})
</script>
