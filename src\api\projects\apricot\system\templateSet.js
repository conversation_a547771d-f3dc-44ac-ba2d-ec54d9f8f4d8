import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'
export default {
    // 模板维护操作接口 : Template Controller
    getTemplateData(data) {
        return request({
            url: baseURL.apricot + '/template/findAllTemplate',
            method: 'POST',
            data
        })
    },
    addTemplate(data) {
        return request({
            url: baseURL.apricot + '/template/addTemplate',
            method: 'POST',
            data
        })
    },
    editTemplate(data) {
        return request({
            url: baseURL.apricot + '/template/editTemplate',
            method: 'POST',
            data
        })
    },
    delTemplate(params) {
        return request({
            url: baseURL.apricot + '/template/delTemplate',
            method: 'POST',
            data: stringify(params)
        })
    },
    // sortTemplate(params) {
    //     return request({
    //         url: baseURL.apricot + '/template/sort',
    //         method: 'POST',
    //         data: stringify(params)
    //     })
    // },
    // autoSortTemplate(params) {
    //     return request({
    //         url: baseURL.apricot + '/template/autoSort',
    //         method: 'POST',
    //         data: stringify(params)
    //     })
    // },
    // uploadTemplate(data) {
    //     return request({
    //         url: baseURL.apricot + '/template/upload',
    //         method: 'POST',
    //         data
    //     })
    // },
    // setDefaultTemplate(params) {
    //     return request({
    //         url: baseURL.apricot + '/template/set/default/template',
    //         method: 'POST',
    //         data: stringify(params)
    //     })
    // },
    queryTagByTemplateId(params) {
        return request({
            url: baseURL.apricot + '/template/queryTagByTemplateId',
            method: 'POST',
            data: stringify(params)
        })
    },
    editImageTag(params) {
        return request({
            url: baseURL.apricot + '/template/editImageTag',
            method: 'POST',
            data: stringify(params)
        })
    },
}

// 取站点打印列表数据
export function workStationPrintList(params) {
    return request({
        url: baseURL.apricot + '/printing/workStationPrint/list',
        method: 'POST',
        data: stringify(params)
    })
}
// 保存打印配置
export function saveWorkStationPrint(data) {
    return request({
        url: baseURL.apricot + '/printing/workStationPrint/save',
        method: 'POST',
        data
    })
}
// 保存工作站打印配置
export function workStationPrintShowSave(data) {
    return request({
        url: baseURL.apricot + '/print/workStationPrintShow/save',
        method: 'POST',
        data
    })
}
// 保存工作站打印配置
export function getWorkStationPrintShow(params) {
    return request({
        url: baseURL.apricot + '/print/workStationPrintShow/listOfWorkStation',
        method: 'POST',
        data: stringify(params)
    })
}


// 生成打印文件接口
export function createPrintFile(data) {
    return request({
        url: baseURL.apricot + '/printFile/print',
        method: 'POST',
        data
    })
}

// 获取电脑已连接的所有打印机名称列表数据
export function getPrinterNames() {
    return request({
        url: baseURL.apricotAssist + '/printer/get/names',
        method: 'POST',
    })
}
// 测试打印
export function printerTest(params) {
    return request({
        url: baseURL.apricotAssist + '/printer/test',
        method: 'POST',
        data: stringify(params)
    })
}

// 客户端打印机调用
export function downloadFileAndPrint(params) {
    return request({
        url: baseURL.apricotAssist + '/download/file/print',
        method: 'POST',
        data: stringify(params)
    })
}
// 客户端下载并打开文件
export function downloadFileAndPreview(params) {
    return request({
        url: baseURL.apricotAssist + '/download/file/preview',
        method: 'POST',
        data: stringify(params)
    })
}

// 打印记录
export function printFileList(params) {
    return request({
        url: baseURL.apricot + '/printFile/list',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取模块可打印模板类型
export function getPrintClassifyOfModule(params) {
    return request({
        url: baseURL.apricot + '/modulePrint/getPrintClassifyOfModule',
        method: 'POST',
        data: stringify(params)
    })
}
// 获取模块某个打印类型的打印模板
export function getTemplateOfPrintClassify(data) {
    return request({
        url: baseURL.apricot + '/modulePrint/getTemplateOfPrintClassify',
        method: 'POST',
        data
    })
}

// 按工作站ID和模板ID查询打印机配置
export function workStationPrintOfTemplate(params) {
    return request({
        url: baseURL.apricot + '/printing/workStationPrint/ofTemplate',
        method: 'POST',
        data: stringify(params)
    })
}
