<template>
    <div ref="dom" class="charts chart-line">
    </div>
</template>

<script>
import * as echarts from "echarts"
import tdTheme from '../config/theme.json' // 图表主题
import { on, off } from '@/utils' // 引入方法
import { shallowRef } from 'vue';
echarts.registerTheme('tdTheme', tdTheme)
export default {
    name: 'Chart<PERSON>ie',
    props: {
        value: {
            type: Array,
            default () {
                return []
            }
        },
        visible: Boolean,
        text: String,
        xTitle: {
            type: String,
            default () {
                return 'left'
            }
        },
        titlePadding: {
            type: Array,
            default () {
                return []
            }
        },
        grid: {
            type: Object,
            default () {
                return {
                    left: '50px',
                    right: '30px',
                    containLabel: false
                }
            }
        },
        subtext: String,
        animation: {
            type: Boolean,
            default () {
                return true
            }
        },
    },
    data() {
        return {
            dom: null
        }
    },
    methods: {
        resize() {
            this.dom.resize()
        },
        updateChart() {
            this.$nextTick(() => {
                let sName = this.value.map(_ => _?.sName || '未知');
                let tempData = [];
                this.value.forEach(_ => {
                    let obj = {}
                    obj.name = _?.sName || '未知';
                    obj.value = _.iCount
                    tempData.push(obj)
                });
                let option = {
                    title: {
                        text: this.text,
                        subtext: this.subtext,
                    },
                    animation: this.animation,
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        right: '10',
                        top: '50',
                        type: "scroll",
                        data: sName
                    },
                    grid: this.grid,
                    series: [{
                        name: '数量(百分比)',
                        data: tempData,
                        type: 'pie',
                        label: {
                            formatter: "{b}: {d}%",
                            // position: 'outside'
                        },
                    }]
                }
                this.dom.hideLoading()
                this.dom.setOption(option)
                on(window, 'resize', this.resize)
            })
        }
    },
    mounted() {
        this.dom = shallowRef(echarts.init(this.$refs.dom, 'tdTheme'))
        this.dom.showLoading()
    },
    watch: {
        value: {
            handler() {
                this.$nextTick(() => {
                    this.dom.clear()
                    this.dom.showLoading()
                    this.updateChart()
                })

            },
            immediate: true,
            deep: true
        }
    },
    beforeUnmount() {
        off(window, 'resize', this.resize)
    }
}
</script>
<style lang="scss" scoped>
.charts {
    height: 100%;
}
</style>
