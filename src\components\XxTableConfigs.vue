<template>
    <el-popover
    :width="420"
    placement="left-start"
    popper-class="m-popover"
    trigger="click">
        <div class="container">
            <div class="header">
                <h4>{{ ('tableConfigsTitle') }}</h4>
                <el-popconfirm
                    :confirm-button-text="('configsPopconfirmConfirm')"
                    :cancel-button-text="('configsPopconfirmCancel')"
                    :title="('configsPopconfirmTitle')"
                    :teleported="false"
                    @confirm="onClickResetConfig">
                    <template #reference>
                        <el-button size="small" type="primary" plain>{{ ('configsPopconfirmText') }}</el-button>
                    </template>
                </el-popconfirm>
            </div>

            <ul class="table-container">
                <li class="table-header">
                    <span>{{ ('sortText') }}</span>
                    <span>{{ ('showText') }}</span>
                    <span>{{ ('columnText') }}</span>
                    <span>{{ ('alignText') }}</span>
                    <span>{{ ('widthText') }}</span>
                </li>
                <el-scrollbar style="height: 100%" height="100%">
                    <draggable 
                        v-model="tableData" 
                        :move="onMove" 
                        @sort="onChangeStorage" 
                        class="table-content"
                        item-key="prop" 
                        animation="300" 
                        filter=".disabled"
                        handle=".move">
                        <template #item="{element}">
                            <li class="table-item" :class="{'disabled': element.prop === tableConfigField}">
                                <span class="move">
                                    <!-- <el-icon :size="18"><Rank /></el-icon> -->
                                </span>
                                <span>
                                    <el-checkbox v-model="element.show" :disabled="element.prop === tableConfigField" @change="onChangeStorage(element)"></el-checkbox>
                                </span>
                                <span :title="isLang ? (element.label) : element.label">{{ isLang ? (element.label) : element.label }}</span>
                                <span>
                                    <el-radio-group v-model="element.align" :disabled="element.prop === tableConfigField" @change="onChangeStorage" class="radio-group" size="small">
                                        <el-radio-button label="left">左</el-radio-button>
                                        <el-radio-button label="center">中</el-radio-button>
                                        <el-radio-button label="right">右</el-radio-button>
                                    </el-radio-group>
                                </span>
                                <span>
                                    <el-input v-model="element.width" :disabled="element.prop === tableConfigField" @blur="onChangeStorage" v-input-number :placeholder="('widthPlaceholder')" size="small"/>
                                </span>
                            </li>
                        </template>
                    </draggable>
                </el-scrollbar>
            </ul>
        </div>
        <template #reference>
            <span></span>
            <!-- <el-icon class="popover-button"><Setting /></el-icon> -->
        </template>
    </el-popover>
</template>
<script>
import draggable from 'vuedraggable';
import { cloneDeep } from "lodash-es";
import { Rank, Setting } from '@element-plus/icons-vue';
export default {
    name: 'XxTableConfigs',

    components: {
        draggable,Rank, Setting
    },
    props: {
        modelValue: {
            type: Array,
            default: () => {
                return []
            }
        },
        defaultItem: { 
            type: Object,
            default: () => {
                return {}
            }
        },
        tableConfigField: {
            type: String,
            default: 'action'
        },
        isLang: true,
        storageKey: String
    },
    computed: {
        tableData: {
            get: function() {
                return this.modelValue;
            },
            set: function(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    data() {
        return {
            defaultTable: []
        }
    },
    methods: {
        onMove(e) {
            // 禁止停靠
            if (e.relatedContext.element.prop == this.tableConfigField) return false;
            return true;
        },
        onChangeStorage() {
            this.$nextTick(() => {
                this.setStorage(this.tableData);
            })
        },
        // 获取本地缓存
        getStorage() {
            let list = localStorage.getItem('table-col-'+this.storageKey);
            if (list) {
                return JSON.parse(list);
            }else {
                return [];
            }
        },
        // 设置缓存
        setStorage(list) {
            return localStorage.setItem('table-col-'+this.storageKey, JSON.stringify(list))
        },
        setDefaultFieldValue(item, col, field) {
            // 赋值列属性值
            if (item[field] == undefined && col[field] === undefined) {
                // 赋默认值
                col[field] = this.defaultItem[field]
            }else {
                // 赋缓存值
                col[field] = item[field]
            }
        },
        // 点击重置
        onClickResetConfig() {
            for (let index = 0; index < this.defaultTable.length; index++) {
                const col = this.defaultTable[index];
                // 列与缓存列相同
                this.setDefaultFieldValue(col, col, 'show')
                this.setDefaultFieldValue(col, col, 'align')
                this.setDefaultFieldValue(col, col, 'width')
            }
            const newTable = cloneDeep(this.defaultTable)

            this.tableData.splice(0, this.tableData.length, ...newTable)

            this.onChangeStorage()
        }
    },
    created() {
        // 获取本地缓存
        const list = this.getStorage()
        this.defaultTable = cloneDeep(this.tableData)

        if (list.length) {
            list.forEach(item => {
                // 遍历所有列
                for (let index = 0; index < this.tableData.length; index++) {
                    const col = this.tableData[index];
                    // 列与缓存列相同
                    if (col.prop === item.prop) {
                        this.setDefaultFieldValue(item, col, 'show')
                        this.setDefaultFieldValue(item, col, 'align')
                        this.setDefaultFieldValue(item, col, 'width')
                        break;
                    }
                }
            });
        }else {
            // 没有缓存的时候，赋初值。遍历所有列
            for (let index = 0; index < this.tableData.length; index++) {
                const col = this.tableData[index];
                // 列与缓存列相同
                this.setDefaultFieldValue(col, col, 'show')
                this.setDefaultFieldValue(col, col, 'align')
                this.setDefaultFieldValue(col, col, 'width')
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.container{
    min-height: 200px;
    .header{
        display: flex;
        align-items: center;
        padding: 6px 0 10px;
        justify-content: space-between;
        h4 {
            margin: 0;
        }
    }
    .radio-group{
        height: 30px;
        :deep(.el-radio-button__inner){
        }
    }
    .table-container{
        .table-header{
            font-size: 13px;
            height: 36px;
            line-height: 36px;
            background-color: #fafafa;
            color: #333333;
            font-weight: bold;
            display: flex;
            border-bottom: 1px solid #EBEEF5;
            > span{
                width: 135px;
                text-align: center;
                &:nth-child(1) {
                    width: 70px;
                }
                &:nth-child(2) {
                    width: 50px;
                    text-align: left;
                }
                &:nth-child(3) {
                    text-align: left;
                }
                &:nth-child(4) {
                    width: 170px;
                }
                &:last-child{
                    width: 80px;
                    text-align: center;
                }
            }
        }
        .table-content{
            max-height: 500px;
        }
        .table-item{
            display: flex;
            align-items: center;
            font-size: 13px;
            height: 35px;
            padding: 10px 0px;
            border-bottom: 1px solid #EBEEF5;
            .move { 
                cursor: move;
            }
            &.disabled{
                position: relative;
                cursor: no-drop;
                color: #888;
                &:hover {
                    &::after{
                        content: "禁止操作";
                        position: absolute;
                        left: 0;
                        top: 0;
                        z-index: 3;
                        width: 100%;
                        height: 100%;
                        text-align: center;
                        line-height: 35px;
                        color: white;
                        background: rgb(0 0 0 / 26%);
                    }
                }
            }
            > span{
                width: 135px;
                text-align: center;
                overflow:hidden; 
                text-overflow:ellipsis;
                display:-webkit-box; 
                -webkit-box-orient:vertical;
                -webkit-line-clamp:2; 
                &:nth-child(1) {
                    width: 70px;
                }
                &:nth-child(2) {
                    width: 50px;
                    text-align: left;
                }
                &:nth-child(3) {
                    text-align: left;
                }
                &:nth-child(4) {
                    width: 170px;
                }
                &:last-child{
                    font-size: 13px;
                    width: 70px;
                    text-align: center;
                }
            }
        }
    }
}
.popover-button{
    position: absolute;
    right: 12px;
    top: 0px;
    font-size: 16px;
    color: #767d88;
    cursor: pointer;
    height: 100%;
}
</style>
