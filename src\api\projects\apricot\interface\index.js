import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'
// 接口管理
export default {

    // 医院列表 
    getHospitalList(data) {
        return request({
            url: baseURL.broken + '/hospitalsys/hospital/list',
            method: 'POST',
            data
        })
    },
    // 医院系统列表 
    getHospitalSysPage(data) {
        return request({
            url: baseURL.broken + '/hospitalsys/find/page',
            method: 'POST',
            data,
        })
    },
    // 新增医院系统
    addHospitalSys(data) {
        return request({
            url: baseURL.broken + '/hospitalsys/add',
            method: 'POST',
            data,
        })
    },
    // 修改医院系统
    editHospitalSys(data) {
        return request({
            url: baseURL.broken + '/hospitalsys/edit',
            method: 'POST',
            data,
        })
    },
    // 删除医院系统
    delHospitalSys(data) {
        return request({
            url: baseURL.broken + '/hospitalsys/del',
            method: 'POST',
            data: stringify(data),
        })
    },
    // 医院系统参数列表 
    getHospitalSysParamPage(data) {
        return request({
            url: baseURL.broken + '/hospitalsysparam/find/page',
            method: 'POST',
            data,
        })
    },
    // 新增医院系统参数
    addHospitalSysParam(data) {
        return request({
            url: baseURL.broken + '/hospitalsysparam/add',
            method: 'POST',
            data,
        })
    },
    // 修改医院系统参数
    editHospitalSysParam(data) {
        return request({
            url: baseURL.broken + '/hospitalsysparam/edit',
            method: 'POST',
            data,
        })
    },
    // 删除医院系统参数
    delHospitalSysParam(data) {
        return request({
            url: baseURL.broken + '/hospitalsysparam/del',
            method: 'POST',
            data: stringify(data),
        })
    },

}