<template>
    <el-dialog title="签章验证"
        :close-on-click-modal="false"
        v-model="visible"
        destroy-on-close
        append-to-body
        align-center
        class="my-dialog t-default"
        width="1000px"
        @open="openDialog"
        @close="closeDialog">
        <div class="m-flexLaout-ty"
            v-loading="loading"
            style="height:70vh; padding: 5px 5px 0;">
            <div class="c-table">
                <div class="g-flexChild c-box">
                    <el-table :data="tableData"
                        ref="mainTable"
                        v-loading="loading"
                        stripe
                        border
                        height="180px"
                        highlight-current-row
                        @row-click="onClickRow"
                        style="width: 100%">
                        <el-table-column align="center"
                            label="序号"
                            type="index"
                            width="70">
                        </el-table-column>
                        <el-table-column prop="sSignerName"
                            align="center"
                            label="医生签名"
                            min-width="120">
                        </el-table-column>
                        <el-table-column prop="sOperationTime"
                            align="center"
                            label="签名时间"
                            min-width="120">
                            <template v-slot="{row}">
                                {{ transformDate(row.sOperationTime) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="sBusinessType"
                            align="center"
                            label="操作类型"
                            min-width="120">
                            <template v-slot="{row}">
                                {{ businessType[row.sBusinessType] }}
                            </template>
                        </el-table-column>
                        <!-- <el-table-column prop="sOperationType" align="center" label="操作类型" min-width="120">
                            <template v-slot="{row}">
                                {{ row.sOperationType == 1 ? '签章' : '撤销' }}
                            </template>
                        </el-table-column> -->
                        <el-table-column prop="sMemo"
                            align="center"
                            label="备注"
                            min-width="120">
                        </el-table-column>
                        <el-table-column v-if="!readonly" 
                            align="center"
                            label="操作"
                            width="120">
                            <template v-slot="{$index, row}">
                                <el-button-icon-fa type="primary"
                                    link
                                    icon="el-icon-thumb"
                                    @click="elecSignatureValidate(row, $index)">验证</el-button-icon-fa>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="g-flexChild"
                style="margin-top: 10px;">
                <el-scrollbar class="my-scrollbar">
                    <el-form ref="form"
                        :model="form"
                        label-width="110px">
                        <el-col :span="8">
                            <el-form-item label="证书序列号:">
                                <el-input v-model="form.sSerialNumber"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="签名医生:">
                                <el-input v-model="form.sSignerName"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item label="签名时间:">
                                <el-input :value="transformDate(form.sOperationTime)"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="签名原文:">
                                <el-input v-model="form.sSourceText"
                                    type="textarea"
                                    :rows="6"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="签名摘要:">
                                <el-input v-model="form.name"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="证书公钥:">
                                <el-input v-model="form.sSignCert"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="时间戳:">
                                <el-input v-model="form.sTimestamp"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="MD5签名数据:">
                                <el-input v-model="form.sSignatureData"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="验证结果:">
                                <el-input v-model="form.sResponse"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                    </el-form>
                </el-scrollbar>
            </div>

        </div>
        <template #footer>
            <el-button-icon-fa _icon="fa fa-close-1"
                @click="closeDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>

import { transformDate } from '$supersetUtils/function'
import { elecSignatureRecordList, elecSignatureValidate, signVerifySignData } from '$supersetApi/projects/apricot/case/report.js'
export default {
    name: 'SignatureVerification',
    emits: ['update:dialogVisible'],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: new Object()
        },
        sReportId: {
            type: String,
            default: ''
        },
        readonly: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            visible: false,
            loading: false,
            form: {},
            tableData: [],
            businessType: {
                1: '报告',
                2: '审核',
                3: '复审',
                4: '提交'
            }
        }
    },
    watch: {
        dialogVisible (val) {
            this.visible = this.dialogVisible;
            if (val) {
                this.form = {}
                this.elecSignatureRecordList();
            }
        },
    },
    methods: {
        transformDate (time) {
            return transformDate(time, false, 'yyyy-MM-dd HH:mm')
        },
        closeDialog () {
            this.$emit('update:dialogVisible', false)
        },
        openDialog () {
        },
        onClickRow (row) {
            this.form = row;
        },
        elecSignatureRecordList () {
            this.tableData = [];
            let jsonData = {
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex,
                // sReportId: this.sReportId 
            }
            this.loading = true;
            elecSignatureRecordList(jsonData).then(res => {
                this.loading = false;
                if (!res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.tableData = res.data || [];
            }).catch(() => {
                this.loading = false;
            })
        },
        elecSignatureValidate (row, index) {
            // this.handleElecSignatureValidate(row, index)
            this.handleSignVerifySignData(row, index)
        },
        handleElecSignatureValidate (row, index) {
            // 目前只有太和用
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            elecSignatureValidate({ signatureRecordId: row.sId }).then(res => {
                loading.close()
                this.tableData[index]['sResponse'] = res.msg;
                if (!res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.$message.success(res.msg);
            }).catch(() => {
                loading.close()
            })
        },
        // 签明验证统一接口处理
        handleSignVerifySignData (row, index) {
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            let userInfo = this.$store.getters['user/userSystemInfo'] || {}
            let params = {
                sElecSignatureRecordId: row.sId,
                sUserCode: userInfo.userNo,
                sUserId: userInfo.userId,
                sUserName: userInfo.userName,
            }
            signVerifySignData(params).then(res => {
                loading.close()
                this.tableData[index]['sResponse'] = res.data?.verifyResult || res.msg;
                if (!res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.$message.success(res.data?.verifyResult || res.msg);
            }).catch(() => {
                loading.close()
            })
        }
    }
}
</script>
<style lang="scss" scoped>
</style>
