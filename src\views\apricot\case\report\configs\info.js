export default {
    // 文本组件配置
    textBaseConfig: [
        { prop: 'sPhone', label: '电话号码', width: '100%', },
        { prop: 'sIdNum', label: '身份证号', width: '100%' },
        { prop: 'sDistrictName', label: '检查院区', width: '100%' },
        { prop: 'sApplyDepartText', label: '申请科室', width: '100%' },
        { prop: 'sApplyPersonName', label: '申请医生', width: '100%' },
        { prop: 'sMedicalRecordNO', label: '病历号', width: '100%' },
        { prop: 'sInHospitalNO', label: '住院号', width: '100%' },
        { prop: 'sOutpatientNO', label: '门诊号', width: '100%' },
        { prop: 'sVisitCard', label: '就诊卡号', width: '100%' },
        { prop: 'sHealthCardNO', label: '健康卡号', width: '100%', isShow: false },
        { prop: 'sChargeStateText', label: '费用状态', width: '100%' },
        { prop: 'iConsultation', label: '会诊状态', width: '100%' },
    ],
    textCheckConfig: [
        { prop: 'fBloodSugar', label: '空腹血糖', width: '100%', },
        { prop: 'fHeight', label: '身高(cm)', width: '100%' },
        { prop: 'fWeight', label: '体重(kg)', width: '100%' },
        { prop: 'sRecipeDose', label: '处方剂量', width: '100%' },
        { prop: 'sFactDose', label: '实际注射量', width: '100%' },
        { prop: 'sRoomText', label: '检查类型', width: '100%' },
        { prop: 'sProjectName', label: '检查项目', width: '100%' },
        { prop: 'sCheckIntent', label: '检查目的', width: '100%' },
        { prop: 'sPositionText', label: '检查部位', width: '100%' },
        { prop: 'sTestModeText', label: '检查方式', width: '100%' },
        { prop: 'sNuclideSupName', label: '药物核素', width: '100%' },
        { prop: 'sApplyMemo', label: '申请备注', width: '100%' },
        { prop: 'dAppointmentTime', label: '预约时间', width: '100%' },
        { prop: 'dConsultTime', label: '问诊时间', width: '100%' },
        { prop: 'dInjectTime', label: '注射时间', width: '100%' },
        { prop: 'dFactCheckTime', label: '上机时间', width: '100%' },
    ],

}