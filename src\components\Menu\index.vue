<template>
  <el-menu class="recursive-menu" :unique-opened="true" :default-active="String(defaultActive)"
    v-bind="$attrs">
    <!-- 通过递归组件方式渲染菜单 -->
    <recursive-menu-item :menu-list="menuList" @click="handleClick"></recursive-menu-item>
  </el-menu>
</template>

<script>
import { defineComponent } from 'vue'
import RecursiveMenuItem from './RecursiveMenuItem.vue'

export default defineComponent({
  name: 'Menu',
  components: {
    RecursiveMenuItem
  },
  emits: ['select'],
  props: {
    // 菜单列表数据
    menuList: {
      type: Array,
      required: true
    },
    defaultActive: {
      default: ''
    },
  },
  data() {
    return {
      activeIndex: this.defaultActive
    }
  },
  methods: {
    handleClick(item, index) { // 1 , 1-1 , 1-1-1
      this.$emit('select', item, index)
      this.activeIndex = item.key || index
    }
  }
})
</script>
