<template>
    <el-dialog append-to-body v-model="visible" fullscreen @close="closeDialog" :close-on-click-modal="false"
        class="my-dialog t-default my-full-dialog"
        modal-class="my-modal">
        <template #header>
            <div class="header-title el-dialog__title">
                <span>扫描件预览</span>
                <span v-if="patientInfo.sName">{{ patientInfo.sName }}</span>
                <span v-if="patientInfo.sSexText">{{ patientInfo.sSexText }}</span>
                <span v-if="patientInfo.sAge">{{ patientInfo.sAge }}</span>
            </div>
        </template>
        <div class="m-flexLaout-ty">
            <ScanImage :isVisible="true" :isOnlyShowUpload="isOnlyShowUpload"></ScanImage>
        </div>
        <template #footer>
            <el-button-icon-fa icon="fa fa-close-1" @click="closeDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import ScanImage from '$supersetViews/apricot/components/ScanImage.vue';
export default {
    name: 'LookImage',
    components: {
        ScanImage
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {},
        isOnlyShowUpload: {
            type: Boolean,
            default: false
        }
    },
    emits:['update:dialogVisible'],
    data () {
        return {
            visible: false,
            delayPatientInfo: {}
        }
    },
    provide () {
        return {
            patientInfo: computed(() => this.delayPatientInfo)
        }
    },
    watch: {
        async dialogVisible(val) {
            this.visible = val;
            this.delayPatientInfo = {};
            if(val) {
                setTimeout(() => {
                    this.delayPatientInfo = this.patientInfo
                }, 300);
            }
        }

    },
    methods: {
        // 关闭扫描文件弹窗
        closeDialog () {
            this.$emit('update:dialogVisible', false);
        },
    }
}
</script>
<style lang="scss" scoped>
.header-title {
    >span:not(:first-child) {
        margin-right: 15px;
    }
}
:global(.my-modal .el-dialog__body) {
    overflow: hidden;
}
</style>
