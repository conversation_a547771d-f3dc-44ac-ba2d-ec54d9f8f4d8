<template>
    <el-dialog v-model="visible" 
        title="备份记录"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        fullscreen
        @open="openDialog"
        @close="closeDialog"
        class="my-dialog t-default my-full-dialog">
        <div class="g-content" v-loading="loading">
            <div class="c-left" style="width: 250px;">
                <el-table :data="dataList"
                    ref="mainTable"
                    stripe
                    border
                    highlight-selection-row
                    highlight-current-row
                    height="100%"
                    style="width: 100%"
                    @row-click="onRowClick">
                    <el-table-column prop="dBakTime"
                        align="center"
                        show-overflow-tooltip
                        label="备份时间"
                        min-width="200">
                        <template v-slot="{ row }">
                            <span style="cursor:pointer;">{{ row.dBakTime }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="c-box c-right">
                <div class="c-title">
                    <span>名称</span>
                    <span> 备份报告&nbsp;&nbsp;  {{ info.dBakTime  }} </span>
                    <!-- <span>
                        当前报告&nbsp;&nbsp;
                        {{ currentSaveReport.dModifiedDate ? transformDate(info.dReportModifyTime) : '' }}
                    </span> -->
                </div>
                <div class="c-content">
                    <div class="c-row">
                        <h4>检查所见</h4>
                        <div class="c-item c-before">
                            <el-button-icon-fa class="copy-btn" icon="fa fa-copy" type="primary" link
                                data-clipboard-action="copy"
                                data-clipboard-target="#text-info-sInspectSee"
                                @click="onCopyClick">复制</el-button-icon-fa>
                            <div class="i-text" v-html="info.sInspectSee" id="text-info-sInspectSee"></div>
                        </div>
                        <!-- <div class="c-item c-after">
                            <div class="i-text" v-html="info.currentMarkedInspectSee" ></div>
                        </div> -->
                    </div>
                    <div class="c-row">
                        <h4>诊断意见</h4>
                        <div class="c-item c-before">
                            <el-button-icon-fa class="copy-btn" icon="fa fa-copy" type="primary" link
                                data-clipboard-action="copy"
                                data-clipboard-target="#text-info-sDiagnosticOpinion"
                                @click="onCopyClick">复制</el-button-icon-fa>
                            <div class="i-text" v-html="info.sDiagnosticOpinion" id="text-info-sDiagnosticOpinion"></div>
                        </div>
                        <!-- <div class="c-item c-after">
                            <div class="i-text" v-html="info.currentMarkedDiagnosticOpinion"></div>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="g-page-footer">
                <!-- <div>
                    <span class="i-color1"></span>
                    <span>增加</span>
                </div>
                <div>
                    <span class="i-color2"></span>
                    <span>删除</span>
                </div> -->
                <el-button-icon-fa type="primary" plain  icon="fa fa-restore-fill"
                    :disabled="!info.dBakTime" @click="onRestoreClick">还原</el-button-icon-fa>
                <el-button-icon-fa icon="fa fa-close-1"
                    @click="visible = false">关闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
</template>
<script>
import ClipboardJs from 'clipboard';
// 接口
import { getReportBak } from '$supersetApi/projects/apricot/case/report.js'
export default {
    name: 'BackupsRecord',
    emits: ['restoreReport', 'update:dialogVisible'],
    props:{
        dialogVisible: {
            type: Boolean,
            default: false
        },
        currentSaveReport: {
            type: Object,
            default: () => ({})
        }
    },
    data () {
        return {
            loading: false,
            visible: false,
            info: {},
            clipboardInstance: null,
            dataList: []
        }
    }, 
    watch: {
        dialogVisible () {
            this.visible = this.dialogVisible;
        }
    },
    methods: {
        transformDate (val) {
            return  moment(val).format("YYYY-MM-DD HH:mm:ss");
        },
        onRowClick(row) {
            this.info = row;
        },
        // 还原
        onRestoreClick() {
            this.$confirm('确认还原报告吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                const params = {
                    sInspectSee: this.info.sInspectSee,
                    sDiagnosticOpinion: this.info.sDiagnosticOpinion
                }
                this.$emit('restoreReport', params);
                this.closeDialog();
            }).catch(err => {
                console.log(err);
            })
        },
        // 复制
        onCopyClick() {
            if (this.clipboardInstance) {
                this.clipboardInstance.destroy();
            }

            this.clipboardInstance = new ClipboardJs(".copy-btn", {
                // text: (trigger) => {
                //     return html;
                // }
            });
            this.clipboardInstance.on('success', () => {
                this.$message({
                    showClose: true,
                    message: '复制成功！',
                    type: 'success',
                });
            });
            this.clipboardInstance.on('error', (err) => {
                this.$message({
                    showClose: true,
                    message: '复制失败！',
                    type: 'error',
                });
            });
        },
        // 获取备份记录
        getReportBak () {
            const params = {
                sReportId: this.currentSaveReport.reportId
            }
            this.info = {};
            this.loading = true
            getReportBak(params).then(res => {
                this.loading = false;
                if (res.success) {
                    this.dataList = res.data || [];
                    if(this.dataList.length) {
                        this.info = res.data[0]  || {};
                        this.$refs.mainTable.setCurrentRow(this.info);
                    }
                    return
                } 
                this.dataList = [];
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
                this.dataList = [];
            })
        },
        // 打开弹窗
        openDialog () {
            this.getReportBak();
        },
        // 关闭弹窗
        closeDialog () {
            this.info = {};
            this.$emit('update:dialogVisible', false);
        },
    },
    created () {
    },
}
</script>
<style lang="scss" scoped>
.g-content {
    height: 100%;
    display: flex;
    .c-right {
        flex: 1;
        margin-left: 10px;
        .c-title {
            z-index: 1;
            position: relative;
            height: 27px;
            background: #f5f9fc;
            border: 1px solid #eee;
            box-sizing: border-box;
            span {
                display: block;
                float: left;
                line-height: 27px;
                text-align: center;
                font-weight: bold;
                // width: calc((100% - 45px) / 2);
                width: calc(100% - 45px);
                height: 100%;
            }
            > span:nth-child(1) {
                width: 45px;
            }
            > span:nth-child(2) {
                // border-right: 1px solid #eee;
                border-left: 1px solid #eee;
                box-sizing: border-box;
            }
        }
        .c-content {
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100% - 27px);
            .c-row {
                height: 100%;
                min-height: 200px;
                display: flex;
                border: 1px solid #eee;
                border-top: none;
                box-sizing: border-box;
                &:nth-child(2) {
                    background: #f7f7f9;
                }
                .c-item {
                    // width: calc((100% - 45px) / 2);
                    width: calc(100% - 45px);
                    height: 100%;
                    padding: 20px 10px;
                    box-sizing: border-box;
                    overflow: hidden;
                }
                .c-item:nth-child(2) {
                    // border-right: 1px solid #eee;
                    box-sizing: border-box;
                }
                .i-text{
                    width: 100%;
                    height: 100%;
                    overflow: auto;
                    :deep(p) {
                        margin: 5px;
                    }
                }
                h4 {
                    float: left;
                    display: flex;
                    align-items: center;
                    margin: 0px;
                    width: 45px;
                    height: 100%;
                    border-right: 1px solid #eee;
                    padding: 0px 10px;
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;
                    box-sizing: border-box;
                }
            }
        }
    }
}
.g-page-footer {
    overflow: hidden;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    > div {
        display: flex;
        align-items: center;
        width: 70px;
        margin-right: 10px;
        > span {
            float: left;
            display: block;
            &:first-child {
                width: 18px;
                height: 18px;
                margin-right: 10px;
            }
        }
        .i-color1 {
            background-color: #d2e7a6;
        }
        .i-color2 {
            background-color: #ffd0c0;
        }
    }
}
.c-before {
    position: relative;
    :deep(.c-datamodify-diff ){
        background-color: #ffd0c0;
        text-decoration: line-through;
    }
}
.c-after {
    position: relative;
    :deep(.c-datamodify-diff ){
        background-color: #d2e7a6;
    }
}
.copy-btn {
    position: absolute; 
    top: 2px; 
    right: 10px;
}
</style>
