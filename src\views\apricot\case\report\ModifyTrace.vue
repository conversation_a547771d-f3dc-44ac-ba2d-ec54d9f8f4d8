<template>
    <el-dialog append-to-body
        title="修改痕迹"
        v-model="visible"
        destroy-on-close
        fullscreen
        @open="openDialog"
        @close="closeDialog"
        class="my-dialog t-default my-full-dialog">
        <div class="g-content">
            <div class="c-box c-left"
                v-loading="loading">
                <el-table :data="tableData"
                    ref="mainTable"
                    stripe
                    border
                    height="100%"
                    @selection-change="handleSelectionChange"
                    style="width: 100%">
                    <el-table-column type="selection"
                        align="center"
                        width="45">
                    </el-table-column>
                    <el-table-column prop="sModifieName"
                        align="center"
                        show-overflow-tooltip
                        label="操作人"
                        min-width="100">
                    </el-table-column>
                    <el-table-column prop="dModifiedDate"
                        align="center"
                        show-overflow-tooltip
                        label="时间"
                        min-width="150">
                        <template #default="scope">
                            {{transformDate(scope.row.dModifiedDate)}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="sModifyEntity"
                        align="center"
                        show-overflow-tooltip
                        min-width="70"
                        label="模块">
                    </el-table-column>
                </el-table>
            </div>
            <div class="c-box c-right"
                v-loading="loadingContent">
                <div class="c-title">
                    <span>名称</span>
                    <span>
                        修改前&nbsp;&nbsp;
                        <template v-if="multipleSelection[0]">{{multipleSelection[0].sModifieName}}
                         {{transformDate(multipleSelection[0].dModifiedDate)  }}</template>
                    </span>
                    <span>修改后&nbsp;&nbsp;
                        <template v-if="multipleSelection[1]">{{multipleSelection[1].sModifieName}} 
                        {{transformDate(multipleSelection[1].dModifiedDate) }}</template>
                    </span>
                </div>
                <div class="c-content">
                    <div class="c-row">
                        <h4>检查所见</h4>
                        <div class="c-item c-before"
                            v-html="info.sInspectSeeBefore"></div>
                        <div class="c-item c-after"
                            v-html="info.sInspectSeeAfter"></div>
                    </div>
                    <div class="c-row">
                        <h4>诊断意见</h4>
                        <div class="c-item c-before"
                            v-html="info.sDiagnosticOpinionBefore"></div>
                        <div class="c-item c-after"
                            v-html="info.sDiagnosticOpinionAfter"></div>
                    </div>
                </div>
            </div>
        </div>
        <template #footer>
            <div class="g-page-footer">
                <div>
                    <span class="i-color1"></span>
                    <span>增加</span>
                </div>
                <div>
                    <span class="i-color2"></span>
                    <span>删除</span>
                </div>
                <el-button-icon-fa icon="fa fa-close-1"
                    @click="visible = false">关闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
</template>
<script>
import { getStoreNameByRoute } from '$supersetResource/js/projects/apricot'
import { transformDate } from "$supersetUtils/function";
// 接口
import Api from '$supersetApi/projects/apricot/case/report.js'
export default {
    name: 'ModifyTrace',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            loading: false,
            loadingContent: false,
            visible: false,
            tableData: [],
            page: {},
            multipleSelection: [],
            info: {
                newDiagnosticOpinion: '',
                newInspectSee: '',
                oldDiagnosticOpinion: '',
                oldInspectSee: ''
            },
            oBefore: {},
            oAfter: {}
        }
    }, 
    computed: {
        patientInfo () {
            const module = getStoreNameByRoute(this.$route.name);
            if (this.$store.state.apricot[module]) {
                return this.$store.state.apricot[module].patientInfo || {};
            }
            return {};
        }
    },
    watch: {
        dialogVisible () {
            this.visible = this.dialogVisible;
        }
    },
    methods: {
      transformDate (val) {
            return transformDate(val, false, 'yyyy-MM-dd HH:mm')
        },
        onClickRow (row) {
            this.$message.closeAll()
            // 请求
            Api.getModifyTraceCompare(
                {
                    sTableKey: row.sTableKey,
                    sId: row.sId
                }
            ).then(res => {
                if (res.success) {
                    this.info = res.data
                    this.$message.success(res.msg)
                    return;
                }
                this.$message.error(res.msg)
            })
        },
        // 点击对比
        onClickContrast () {
            this.$message.closeAll()
            if (this.multipleSelection.length != 2) {
                this.$message.info('请勾选二组对比')
                return;
            }
            this.loadingContent = true;
            Api.getModifyTraceContrast(
                {
                    sIdBefore: this.multipleSelection[0].sId,
                    sIdAfter: this.multipleSelection[1].sId
                }
            ).then(res => {
                this.loadingContent = false;
                if (res.success) {
                    this.info = res.data || {}
                    this.$message.success(res.msg)
                    return;
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loadingContent = false;
            })
        },
        getData () {
            this.loading = true
            const p = {
                condition: { sPatientId: this.patientInfo.sId, sModifyEntity: 'Report' },
                page: { pageCurrent: 1, pageSize: 9999 }
            }
            Api.getModifyTraceData(p).then(res => {
                this.loading = false
                if (res.success) {
                    this.tableData = res.data.recordList ? res.data.recordList : [];
                    this.page.countRow = res.data.countRow;
                }
            }).catch(() => {
                this.loading = false
            })
        },
        onContextmenuRow (row, column, event) {
            this.selectedMenu = row
            this.contextmenuShow(event)
        },
        contextmenuShow (ev) {
            const position = {
                top: ev.clientY,
                left: ev.clientX
            }
            this.$refs.contextmenuRef.show(position)
        },
        handleSelectionChange (val) {
            let aTemp = val
            // this.multipleSelection = val;
            if (val.length > 2) {
                this.$refs.mainTable.toggleRowSelection(val[0], false)
                aTemp = val.slice(val.length - 2, val.length);
            }
            if (val.length == 2) {
                this.multipleSelection = aTemp.sort((a, b) => new Date(a.dModifiedDate).getTime() - new Date(b.dModifiedDate).getTime())
                this.onClickContrast();
            }
        },
        openDialog () {
            this.getData()
        },
        closeDialog () {
            this.tableData = [];
            this.info = {};
            this.multipleSelection = [];
            this.$emit('update:dialogVisible', false);
        },
    },
    created () {
        this.info = {}
    },
}
</script>
<style lang="scss" scoped>
.g-content {
    height: 100%;
    display: flex;
    .c-left {
        width: 371px;
    }
    .c-right {
        flex: 1;
        margin-left: 10px;
        .c-title {
            z-index: 1;
            position: relative;
            height: 27px;
            background: #f5f9fc;
            border: 1px solid #eee;
            box-sizing: border-box;
            span {
                display: block;
                float: left;
                line-height: 27px;
                text-align: center;
                font-weight: bold;
                width: calc((100% - 45px) / 2);
                height: 100%;
            }
            > span:nth-child(1) {
                width: 45px;
            }
            > span:nth-child(2) {
                border-right: 1px solid #eee;
                border-left: 1px solid #eee;
                box-sizing: border-box;
            }
        }
        .c-content {
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100% - 27px);
            .c-row {
                height: 100%;
                min-height: 200px;
                display: flex;
                border: 1px solid #eee;
                border-top: none;
                box-sizing: border-box;
                &:nth-child(2) {
                    background: #f7f7f9;
                }
                .c-item {
                    width: calc((100% - 45px) / 2);
                    height: 100%;
                    overflow: auto;
                    padding: 10px;
                    box-sizing: border-box;
                }
                .c-item:nth-child(2) {
                    border-right: 1px solid #eee;
                    box-sizing: border-box;
                }
                h4 {
                    float: left;
                    display: flex;
                    align-items: center;
                    margin: 0px;
                    width: 45px;
                    height: 100%;
                    border-right: 1px solid #eee;
                    padding: 0px 10px;
                    text-align: center;
                    font-size: 14px;
                    font-weight: bold;
                    box-sizing: border-box;
                }
            }
        }
    }
}
.g-page-footer {
    overflow: hidden;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    > div {
        display: flex;
        align-items: center;
        width: 70px;
        margin-right: 10px;
        > span {
            float: left;
            display: block;
            &:first-child {
                width: 18px;
                height: 18px;
                margin-right: 10px;
            }
        }
        .i-color1 {
            background: #d2e7a6;
        }
        .i-color2 {
            background: #ffd0c0;
        }
    }
}
.c-before {
    :deep(.c-datamodify-diff ){
        background: #ffd0c0;
        text-decoration: line-through;
    }
}
.c-after {
    :deep(.c-datamodify-diff ){
        background: #d2e7a6;
    }
}
</style>
