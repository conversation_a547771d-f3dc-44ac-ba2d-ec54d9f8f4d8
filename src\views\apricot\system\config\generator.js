export const defaultAttribute = {
    span: 24,
    required: false,
    titleShow: true,
    labelWidth: 100,
    labelStyle: {
        fontSize: '14px',
        color: '#6a6c71',
        fontWeight: '400',
    },
    style: {
        height: '30px',
        fontSize: '14px',
        color: '#606266',
        fontWeight: '400',
        background: '#FFFFFF',
    }
};

export const fromComponents = [
    {
        layout: 'formItem',
        label: '输入框',
        tag: 'input',
        tagIcon: 'fa-drag-input',
        value: undefined,
        placeholder: '',
        span: 24,
        clearable: true,
        readonly: false,
        disabled: false,
        required: true,
        checkStyle: false,
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '文本域',
        tag: 'input-textarea',
        tagIcon: 'fa-drag-input-textarea',
        value: undefined,
        type: 'textarea',
        placeholder: '',
        span: 24,
        rows: 4,
        maxlength: 9999,
        'showWordLimit': false,
        readonly: false,
        disabled: false,
        required: true,
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '计数器',
        tag: 'input-number',
        tagIcon: 'fa-drag-input-number',
        value: undefined,
        placeholder: '',
        span: 24,
        min: 0,
        max: 999,
        step: 1,
        'stepStrictly': false,
        disabled: false,
        required: true,
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '下拉选择',
        tag: 'select',
        tagIcon: 'fa-drag-select',
        value: undefined,
        placeholder: '请选择',
        span: 24,
        clearable: true,
        disabled: false,
        required: true,
        filterable: false,
        allowCreate: false,
        multiple: false,
        options: [{
            value: '选项一',
        }, {
            value: '选项二',
        }],
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '时间选择',
        tag: 'time-picker',
        tagIcon: 'fa-drag-time-picker',
        placeholder: '',
        value: undefined,
        span: 24,
        disabled: false,
        clearable: true,
        required: true,
        'pickerOptions': {
          selectableRange: '00:00:00-23:59:59'
        },
        format: 'HH:mm:ss',
        valueFormat: 'HH:mm:ss',
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '日期选择',
        tag: 'date-picker',
        value: undefined,
        tagIcon: 'fa-drag-date-picker',
        placeholder: '',
        type: 'date',
        span: 24,
        disabled: false,
        clearable: true,
        required: true,
        readonly: false,
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '单选框',
        tag: 'radio-group',
        tagIcon: 'fa-drag-radio-group',
        value: undefined,
        span: 24,
        optionType: 'default',
        disabled: false,
        required: true,
        options: [{
          value: '选项一'
        }, {
          value: '选项二'
        }],
        regList: [],
        labelStyle: {},
        style: {}
    },
    {
        layout: 'formItem',
        label: '多选框',
        tag: 'checkbox-group',
        tagIcon: 'fa-drag-checkbox-group',
        value: undefined,
        span: 24,
        optionType: 'default',
        disabled: false,
        required: true,
        options: [{
            value: '选项一',
        }, {
            value: '选项二',
        }],
        regList: [],
        labelStyle: {},
        style: {}
    },
];

export const layoutComponents = [
    {
        layout: 'layoutItem',
        tagIcon: 'fa-drag-plane-box',
        tag: 'plane-box',
        label: '容器',
        layoutTree: true,
        children: [],
        span: 24,
        navigation: false,
    },
    {
        layout: 'layoutItem',
        tagIcon: 'fa-drag-plane-box-title',
        tag: 'plane-box-title',
        label: '标题容器',
        titlePosition: 'left',
        layoutTree: true,
        children: [],
        span: 24,
        navigation: false,
    },
];

export const modifierComponents = [
    {
        layout: 'modifierItem',
        label: '分割线',
        tagIcon: 'fa-drag-divider',
        tag: 'divider',
        span: 24,
        backgroundColor: '#dcdfe6',
        height: '1px',
        marginTop: 24,
        marginBottom: 24,
        navigation: false,
    },
    {
        layout: 'modifierItem',
        label: '标题头',
        tagIcon: 'fa-drag-title',
        tag: 'title',
        span: 24,
        color: '#333',
        marginTop: 24,
        marginBottom: 24,
        fontWeight: '600',
        fontSize: 24,
        textAlign: 'center',
        navigation: false,
    }
];