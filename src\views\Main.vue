<template>
  <div class="admin-container">
    <el-header class="header" height="45px">
      <NavBar />
    </el-header>

    <div class="container">
      <div class="main">
        <MainContent />
      </div>


    </div>
  </div>
</template>

<script>

import MainContent from './MainContent.vue'

export default {
  name: 'Main',
  components: {
    MainContent
  },
  computed: {
    moduleRouterDataList() {
      return this.$store.state.module_router.moduleRouterDataList
    }
  },
  watch: {
  },
  methods: {
  },
  mounted() {
    // 权限判断
    const routeName = (this.$router.currentRoute.value.name)
    const hasAuth = (this.moduleRouterDataList.find(item => item.routerName === routeName))
    if (!hasAuth) {
      this.$router.push({ name: 'Error_401', query: { name: this.$router.currentRoute.value.name } })
    }
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
$footerHeight: 24px;

.admin-container {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--theme-menu-background-color);

  .container {
    display: flex;
    flex-direction: column;
    position: absolute;
    right: 0;
    left: 0;
    max-width: 100%;
    top: $base-nav-bar-height;
    // width: calc(100% - $base-menu-width);
    height: calc(100% - $base-nav-bar-height);
    transition: all $base-transition-time-4;
  }

  .header {
    position: fixed;
    padding: 0;
    top: 0;
    left: 0;
    right: 0;
    transition: all $base-transition-time-4;
    z-index: $base-z-index-999;
  }

  .main {
    flex: 1;
    position: relative;
    // top: $base-main-vertical-top;
    // height: calc(100% - $base-nav-bar-height - $base-tabs-bar-height);
    // padding: 15px 15px;
    padding: 0;
    overflow-y: hidden;

    &.fixed {
      // top: $base-main-fixed-top;
    }

    &[class='el-main main fixed notag'] {
      // top: $base-main-vertical-fixed-notag-top;
    }

    &[class='el-main main notag'] {
      // top: $base-main-notag-top;
    }
  }

  .footer {
    flex: 0;
    position: relative;
    padding: 0 15px;
    border-top: 1px solid #f1f1f1;
    background: #fff;
    font-size: 13px;
    width: 100%;
    height: $footerHeight;
    line-height: $footerHeight;
    text-align: right;
  }
}
</style>
