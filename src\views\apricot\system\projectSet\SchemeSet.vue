<template>
    <div class="scope-checkProjectTree c-container">
        <ul class="c-left">
           <li>
               <SchemeOrderPlan @onSelectItem="onSelectItem"></SchemeOrderPlan>
           </li>
           <li>
               <SchemeTree @onClickTree="onClickTree"></SchemeTree>
           </li>
        </ul>
        <div class="c-right">
            <SchemePlanTime :scheme="selectOrderPlan" :selectTree="selectTree"></SchemePlanTime>
        </div>
    </div>
</template>
<script>
import { SchemeOrderPlan, SchemePlanTime, SchemeTree } from './components/index.js'
/** 方案设置 */
export default {
    name: 'SchemeSet',
    components: {
        SchemeOrderPlan,
        SchemePlanTime,
        SchemeTree
    },
    props: {
    },
    data() {
        return {
            selectOrderPlan: {},
            selectTree: {}
        }
    },
    methods: {
        onSelectItem(obj){
            if (!obj.sId) return;
            this.selectOrderPlan = obj
        },
        onClickTree(obj){
            this.selectTree = obj
        }
    }
};
</script>
<style lang="scss" scoped>
.scope-checkProjectTree{
    display: flex;
    height: 100%;
    .c-left{
        padding: 0px;
        margin-right: 15px;
        width: 320px;
        border-right: 1px solid #eee;
        overflow: hidden;
        padding-right: 15px;
        display: flex;
        flex-direction: column;
        > li:first-child{
            height: 240px;
        }
        > li:last-child{
            flex: 1;
            padding-top: 10px;
            overflow: auto;
        }
    }
    .c-right{
        flex: 1;
        overflow: hidden;
    }
}
</style>