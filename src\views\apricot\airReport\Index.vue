<template>
    <DragAdjust :dragAdjustData="DA1">
        <template v-slot:c1>
            <LayoutTable v-bind="$attrs">
                <template v-slot:header>
                    <SearchList v-model:modelValue="condition"
                        :list="searchListConfig"
                        :optionData="optionsLoc"
                        :configBtn="true"
                        storageKey="AirReportIndexSearch"
                        @changeSearch="mxDoSearch">
                        <template v-slot:dAppointmentTimeSt>
                            <el-date-picker v-model="condition.dAppointmentTimeSt"
                                type="date"
                                :disabled-date="pickerOptionsAppointDayStart"
                                @change="changeAppointTime" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:dAppointmentTimeEd>
                            <el-date-picker v-model="condition.dAppointmentTimeEd"
                                :disabled-date="pickerOptionsAppointDayEnd"
                                type="date"
                                @change="changeAppointTime" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:sExamDocName>
                            <el-autocomplete v-model="condition.sExamDocName"
                                value-key="userName"
                                :fetch-suggestions=" (queryString, cb) => querySearch(queryString, cb, 'sExamDocName')"
                                placeholder=" "
                                clearable
                                @change="mxDoSearch"
                                style="width: 100%">
                                <template #default="{ item }">
                                    {{ item.userName }}
                                </template>
                            </el-autocomplete>
                        </template>
                        <template v-slot:sReportDocName>
                            <el-autocomplete v-model="condition.sReportDocName"
                                value-key="userName"
                                :fetch-suggestions=" (queryString, cb) => querySearch(queryString, cb, 'sReportDocName')"
                                placeholder=" "
                                clearable
                                @change="mxDoSearch"
                                style="width: 100%">
                                <template #default="{ item }">
                                    {{ item.userName }}
                                </template>
                            </el-autocomplete>
                        </template>
                    </SearchList>
                </template>
                <template v-slot:action>
                    <div class="c-action">
                        <div>
                            <el-button v-auth="'report:airReport:sendHis'"
                                type="primary"
                                plain
                                @click="onClickSendHis">
                                <template #icon>
                                    <Icon name="el-icon-position"> </Icon>
                                </template>
                                发送HIS
                            </el-button>
                        </div>
                        <div style="display: flex; align-items: center">
                            <el-button @click="onClickReset">
                                <template #icon>
                                    <Icon name="el-icon-refresh-left"> </Icon>
                                </template>
                                重置
                            </el-button>

                            <el-button v-auth="'report:airReport:query'"
                                type="primary"
                                :loading="loading"
                                @click="mxDoSearch">
                                <template #icon>
                                    <Icon name="el-icon-search"
                                        color="white"></Icon>
                                </template>
                                查询
                            </el-button>
                        </div>
                    </div>
                </template>
                <template v-slot:content>
                    <el-table-extend v-loading="loading"
                        :data="tableData"
                        ref="mainTable"
                        stripe
                        :row-class-name="mxRowClassName"
                        @row-click="handleRowClick"
                        @sort-change="mxOnSort"
                        @selection-change="handleSelectionChange"
                        highlight-current-row
                        height="100%"
                        style="width: 100%"
                        storageKey="AirReportIndexTable">
                        <el-table-column fixed
                            type="selection"
                            label="选择"
                            prop="_select"
                            align="center"
                            width="50"
                            :selectable="selectEnable">
                        </el-table-column>
                        <el-table-column type="index"
                            label="序号"
                            prop="_index"
                            align="center"
                            width="60"
                            class-name="tableColOverflowVisible"></el-table-column>
                        <el-table-column v-for="item in patientTableConfig.filter((i) => !i.iIsHide)"
                            :show-overflow-tooltip="item.sProp !== 'img'"
                            :key="item.index"
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort ? 'custom' : false"
                            :isSortable="item.isSortable"
                            :sOrder="item.sOrder"
                            :column-key="item.sSortField ? item.sSortField : null">
                            <template v-slot="{ row }">
                                <template v-if="item.sProp === 'iReportStatus'">
                                    {{ optionsLoc.reportStatusOptions.find((option) => option.sValue == row[item.sProp])?.sName }}
                                </template>
                                <template v-else-if="item.sProp === 'iSendStatus'">
                                    {{ optionsLoc.sendStatusOptions.find((option) => option.sValue == row[item.sProp])?.sName }}
                                </template>
                                <template v-else-if="item.sProp.slice(0, 1) === 'd'">
                                    {{ mxFormatterDate(row[item.sProp]) }}
                                </template>
                                <template v-else>
                                    {{ row[item.sProp] }}
                                </template>
                            </template>
                        </el-table-column>
                    </el-table-extend>
                </template>
                <template v-slot:footer>
                    <el-pagination background
                        @size-change="onSizeChange"
                        @current-change="onCurrentChange"
                        :current-page="page.pageCurrent"
                        :page-sizes="mxPageSizes"
                        :pager-count="5"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next"
                        :total="page.total">
                    </el-pagination>
                </template>
            </LayoutTable>
        </template>
        <template v-slot:c2>
            <div v-loading="pdfLoading"
                class="flex"
                style="position: relative; height: 100%"
                ref="PdfParentDom">
                <div v-if="userAgent && pdfUrl"
                    class="line-cover"></div>
                <iframe v-if="pdfUrl"
                    :src="pdfUrl"
                    class="flex-grow"
                    frameborder="0"></iframe>
                <!-- <embed v-if="pdfUrl" :src="pdfUrl" type="application/pdf" class="flex-grow" width="100%" /> -->
                <!-- <VueOfficePdf v-if="pdfUrl"
                    :src="pdfUrl"
                    class="flex-grow"></VueOfficePdf> -->
                <el-empty v-else
                    class="flex-grow"
                    :image-size="100"
                    description="暂无报告"
                    style="box-shadow: 0 0 15px rgba(255, 255, 255, 1) inset" />
            </div>
        </template>
    </DragAdjust>
</template>

<script>
//引入VueOfficePdf组件
// import VueOfficePdf from '@vue-office/pdf';

import Configs from './config';
// 接口、外部函数、混入
import { mixinTable, mixinTableInner } from '$supersetResource/js/projects/apricot/index.js';
import { caseEnum } from '$supersetResource/js/projects/apricot/enum.js';
import {
    getAirReportPage,
    airReportSendHis,
} from '$supersetApi/projects/apricot/airReport/index.js';
import { getReportAboveDrData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'; // 获取报告医生
import { baseURL } from '$supersetUtils/request';
import axios from 'axios';

export default {
    name: 'apricot_AirReport',
    mixins: [mixinTable, mixinTableInner],
    // components: {
    //     VueOfficePdf
    // },
    data () {
        return {
            isMixinDynamicGetTableHead: true, // 是否动态获取表头
            DA1: Configs.DA1,
            searchListConfig: Configs.searchListConfig,
            pickerOptionsAppointDayStart: (time) => {
                // if (this.condition.dAppointmentTimeEd) {
                //     return (
                //         time.getTime() >
                //         new Date(this.condition.dAppointmentTimeEd).getTime() ||
                //         time.getTime() > new Date().getTime()
                //     );
                // }
                // return time.getTime() > new Date().getTime();
            },
            pickerOptionsAppointDayEnd: (time) => {
                // if (this.condition.dAppointmentTimeSt) {
                //     return (
                //         time.getTime() <
                //         new Date(this.condition.dAppointmentTimeSt).getTime() ||
                //         time.getTime() > new Date().getTime()
                //     );
                // }
                // return time.getTime() > new Date().getTime();
            },
            condition: {
                dAppointmentTimeEd: new Date(),
                dAppointmentTimeSt: new Date(),
            },
            optionsLoc: {
                qualitativeOptions: caseEnum.qualitative,
                reportStatusOptions: [
                    {
                        sName: '未出报告',
                        sValue: 0,
                    },
                    {
                        sName: '已出报告',
                        sValue: 1,
                    },
                ],
                sendStatusOptions: [
                    {
                        sName: '未发送',
                        sValue: 0,
                    },
                    {
                        sName: '发送成功',
                        sValue: 1,
                    },
                    {
                        sName: '发送失败',
                        sValue: 2,
                    },
                ],
                sExamDocNameOptions: [],
                sReportDocNameOptions: [],
            },
            patientTableConfig: Configs.tableConfig,
            multipleSelection: [],
            isSandHisRefreshTable: false,
            userAgent: navigator.userAgent.toLowerCase().indexOf('chrome') !== -1,
            pdfLoading: false,
            // pdfUrl: 'https://501351981.github.io/vue-office/examples/dist/static/test-files/test.pdf'
            pdfUrl: '',
            page: {  // 分页	
                pageCurrent: 1,
                pageSize: localStorage.airReportIndexPageSize ? JSON.parse(localStorage.airReportIndexPageSize) : 30,
                total: 0
            },
        };
    },
    watch: {
        'page.pageSize'(val) {
            localStorage.setItem('airReportIndexPageSize', val);
        },
    },
    methods: {
        querySearch (queryString, cb, sProp) {
            var target = this.optionsLoc[`${sProp}Options`];
            var results = queryString ? target.filter(this.createFilter(queryString)) : target;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        createFilter (queryString) {
            return (option) => {
                return option.userName.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
            };
        },
        changeAppointTime () {
            this.mxDoSearch();
        },
        // 重置
        onClickReset () {
            this.condition = this.$options.data().condition;
            this.mxDoSearch();
        },
        // 表格多选
        handleSelectionChange (rows) {
            this.multipleSelection = rows;
        },
        // 加载状态
        loadFindTip (text) {
            return this.$loading({
                lock: true,
                text: text || '文件生成中...',
                background: 'rgba(255, 255, 255, 0.5)',
                customClass: 'my-loading',
            });
        },
        // 发送his
        async onClickSendHis () {
            const len = this.multipleSelection.length;
            if (!len) {
                this.$message.warning('请至少勾选一个患者数据！');
                return;
            }
            let loading = this.loadFindTip('发送中...');
            var tipMsg = '发送成功！';
            let isStopSend = false;
            for (let i = 0; i < len; i++) {
                if (isStopSend) {
                    // 停止循环；
                    loading.close();
                    return;
                }
                let jsonData = {
                    sPatientId: this.multipleSelection[i].sPatientId,
                };
                await airReportSendHis(jsonData)
                    .then((res) => {
                        if (res.success) {
                            tipMsg = res.msg;
                            return;
                        }
                        this.$message({
                            message: `患者：${this.multipleSelection[i].sName}；${res.msg}，停止发送`,
                            type: 'error',
                            showClose: true,
                            duration: 5000,
                        });
                        isStopSend = true;
                    })
                    .catch((err) => {
                        isStopSend = true;
                        console.log(err);
                    });
            }
            loading.close();
            this.$message.success(tipMsg);
            this.isSandHisRefreshTable = true;
            this.mxGetTableList();
        },
        // 多选禁用未发出报告部分选择框
        selectEnable (row) {
            return !!row.iReportStatus;
        },
        // 行点击
        handleRowClick (row) {
            if (this.editLayer.selectedItem.sPatientId === row.sPatientId) return;
            this.getPdfOrigin(row.sAirReportId);
            this.onClickRow(row);
        },
        // 获取pdf文件流，转化成blob url
        getPdfOrigin (sAirReportId) {
            this.pdfUrl = '';
            if (!sAirReportId) return;
            const jsonData = {
                method: 'get',
                responseType: 'blob',
                // url : 'http://*************:18109/report/airreport/show/pdf?sAirReportId=d0a01ced836541fe927a8ed85623f7f6',// 测试地址
                url: `${baseURL.apricot}/airreport/show/pdf?sAirReportId=${sAirReportId}`, // 后端接口地址
            };
            this.pdfLoading = true;
            axios(jsonData)
                .then((res) => {
                    let result = res.data;
                    var binaryData = [];
                    binaryData.push(result);
                    //解析文件类型为pdf类型的文件流
                    let Blobs = new Blob(binaryData, {
                        type: 'application/pdf;chartset=UTF-8',
                    });
                    //生成一个blob链接
                    this.pdfUrl = URL.createObjectURL(Blobs);
                    this.pdfUrl += '#statusbar=0&messages=0&navpanes=0';
                    // this.pdfUrl += '#view=FitH&scrollbar=0&toolbar=1&statusbar=0&messages=0&navpanes=0';
                    this.pdfLoading = false;
                    // console.log(this.pdfUrl);
                })
                .catch((err) => {
                    this.pdfLoading = false;
                });
        },
        // 查询数据
        getData (obj) {
            const params = { ...obj };
            let dAppointmentTimeSt = params.condition.dAppointmentTimeSt;
            params.condition.dAppointmentTimeSt = this.mxFormateOneDayStart(dAppointmentTimeSt);

            // 当选择了结束时间，转换成23：59：59
            let dAppointmentTimeEd = params.condition.dAppointmentTimeEd;
            params.condition.dAppointmentTimeEd = this.mxFormateOneDayEnd(dAppointmentTimeEd);
            getAirReportPage(params)
                .then((res) => {
                    this.tableData = [];
                    if (res.success) {
                        this.tableData = res?.data?.recordList || [];
                        this.page.total = res.data.countRow;
                        this.loading = false;
                        if (!this.tableData.length) {
                            this.pdfUrl = '';
                            return;
                        }
                        // 赋选中状态
                        if (this.tableData.length && !this.editLayer?.selectedItem?.sPatientId) {
                            this.mxSetSelected();
                            this.onClickRow(this.tableData[0]);
                            this.getPdfOrigin(this.tableData[0].sAirReportId);
                            return;
                        }
                        if (this.isSandHisRefreshTable) {
                            const target = this.tableData.find(
                                (item) =>
                                    item.sPatientId === this.editLayer.selectedItem.sPatientId
                            );
                            target && this.$refs.mainTable.setCurrentRow(target);
                            this.isSandHisRefreshTable = false;
                            return;
                        }
                        return;
                    }
                    this.loading = false;
                    this.$message.error(res.msg);
                })
                .catch(() => {
                    this.loading = false;
                });
        },
    },
    async mounted () {
        this.onClickReset();
        const Doctors = await getReportAboveDrData();
        this.optionsLoc['sExamDocNameOptions'] = Doctors.reportDoctors; // 获取报告医生
        this.optionsLoc['sReportDocNameOptions'] = [...Doctors.auditDoctors]; //获取审核医生
    },
};
</script>

<style lang="scss" scoped>
.c-action {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .c-checkbox {
        margin-right: 10px;

        &:last-child {
            margin-right: none;
        }
    }
}

.line-cover {
    position: absolute;
    top: 15px;
    left: 50px;
    width: calc((100% - 310px) / 2 - 60px);
    height: 30px;
    background-color: #323639;
    z-index: 1;
    animation: changeBackground 2s;
}

@keyframes changeBackground {
    0% {
        width: 100%;
    }
    100% {
        width: calc((100% - 310px) / 2 - 60px);
    }
}
// :deep(.vue-office-pdf-wrapper){
//     background-color: #f1f1f1 !important;
//     padding: 10px 0 !important;
// }
</style>
