<template>
    <div class="m-flexLaout-ty" v-loading="loading">
                <!-- <div class="g-flexChild"
                    v-loading="loading"> -->
        <div style="padding: 10px 0 15px;">
            <span>当前工作站：</span> <strong>{{workStation.stationName}}</strong>
        </div>
        <div class="g-flexChild">
            <el-table :data="tableData"
                border
                height="100%">
                <el-table-column type="index"
                    label="序号"
                    width="60"></el-table-column>
                <el-table-column property="sName"
                    label="模板名称"
                    min-width="150"
                    show-overflow-tooltip>
                </el-table-column>
                <el-table-column property="sPrinterName"
                    label="打印机"
                    width="250"
                    show-overflow-tooltip>
                    <template v-slot="{row, $index}">
                        <span v-if="!row.isEdit">{{ row.sPrinterName }}</span>
                        <el-select
                            v-if="row.isEdit"
                            v-model="row.sPrinterName"
                            size="small"
                            clearable
                            >
                            <el-option v-for="(item, index) in optionsLoc.sPrinterNameOptions"
                                :key="index"
                                :label="item"
                                :value="item"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column property="sFileType"
                    label="文件类型"
                    width="150"
                    show-overflow-tooltip>
                    <template v-slot="{row, $index}">
                        <span v-if="!row.isEdit">{{ row.sFileType }}</span>
                        <el-select v-if="row.isEdit" v-model="row.sFileType"
                            size="small"
                            clearable>
                            <el-option v-for="item in optionsLoc.sFileTypeOptions"
                                :key="item.sValue"
                                :label="item.sValue"
                                :value="item.sValue"></el-option>
                        </el-select>
                    </template>
                </el-table-column>
                <el-table-column property="iCopies"
                    label="打印份数"
                    width="150"
                    show-overflow-tooltip>
                    <template v-slot="{row, $index}">
                        <span v-if="!row.isEdit">{{ row.iCopies }}</span>
                        <el-select v-if="row.isEdit" size="small" clearable
                            v-model="row.iCopies">
                            <el-option v-for="item in 10" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                        <!-- <el-input v-model="row.iCopies"
                            type="number"
                            size="small"
                            style="width:100%;"
                            @change="onHandleChange(row, $index)"></el-input> -->
                    </template>
                </el-table-column>
                <el-table-column property="actions"
                    label="操作"
                    align="center"
                    fixed="right"
                    width="160">
                    <template v-slot="{row, $index}">
                        <el-button-icon-fa v-if="!row.isEdit" title="编辑" type="primary"  icon="el-icon-edit-outline" link 
                            @click="onEdit(row, $index)">
                            编辑
                        </el-button-icon-fa>
                        <el-button-icon-fa v-if="row.isEdit" :loading="saveLoading" type="primary" link 
                            title="保存" 
                            icon="fa fa-save" 
                            @click="handleSaveRow(row, $index)">保存</el-button-icon-fa>
                        <el-button-icon-fa v-if="row.isEdit" link icon="el-icon-close" @click="onCancelClick(row)">
                            <span>取消</span>
                        </el-button-icon-fa>
                        <el-button-icon-fa v-else link type="primary" icon="fa fa-print"
                            @click="onTestClick(row, $index)">测试</el-button-icon-fa>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="c-item">
            <div class="c-item-child t-2" :class="{'c-active': sPrintShow.showType == 1}">
                <div class="c-check-item">
                    <el-radio v-model="sPrintShow.showType" 
                        :label="1" 
                        @change="onChangeSetting">简约模式</el-radio>
                </div>
                <div class="c-check-item t-2">
                    <span>点击模板后：</span>
                    <el-radio-group v-model="sPrintShow.iPrintType" 
                        :disabled="sPrintShow.showType == 2" 
                        @change="onChangeSetting">
                        <el-radio :label="2">预览打印</el-radio>
                        <el-radio :label="1">直接打印</el-radio>
                        <el-radio :label="3">下载打印</el-radio>
                    </el-radio-group>
                </div>
                <div class="c-check-item t-2">
                    <el-checkbox v-model="sPrintShow.isOneTemplate" 
                        :disabled="sPrintShow.showType == 2"
                        @change="onChangeSetting">只有一个模板时，直接执行报告的预览打印</el-checkbox>
                </div>
            </div>
            <div class="c-item-child" :class="{'c-active': sPrintShow.showType == 2}">
                <div class="c-check-item">
                    <el-radio v-model="sPrintShow.showType" 
                        :label="2" 
                        @change="onChangeSetting">详细模式</el-radio>
                </div>
                <div class="c-check-item t-2">
                    <el-checkbox v-model="sPrintShow.isShowParams" 
                        :disabled="sPrintShow.showType == 1"
                        @change="onChangeSetting">显示打印机、文件格式、打印份数</el-checkbox>
                </div>
                <div class="c-check-item t-2">
                    <el-checkbox v-model="sPrintShow.isShowPriviewBtn"
                        :disabled="sPrintShow.showType == 1"
                        @change="onChangeSetting">显示预览打印按钮</el-checkbox>
                </div>
                <div class="c-check-item t-2">
                    <el-checkbox v-model="sPrintShow.isShowPrintBtn" 
                        :disabled="sPrintShow.showType == 1"
                        @change="onChangeSetting">显示直接打印按钮</el-checkbox>
                </div>
                <div class="c-check-item t-2">
                    <el-checkbox v-model="sPrintShow.isShowDownloadBtn" 
                        :disabled="sPrintShow.showType == 1"
                        @change="onChangeSetting">显示下载图标</el-checkbox>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
// import { getOptionName } from '$supersetResource/js/tools.js'
import { systemModuleOption } from '$supersetResource/js/projects/apricot/enum.js'
import {
    workStationPrintList, saveWorkStationPrint, getPrinterNames,
    printerTest, workStationPrintShowSave, getWorkStationPrintShow
} from '$supersetApi/projects/apricot/system/templateSet.js'
export default {
    name: 'PrintSet',
    props: {
        iModuleId: {
            type: Number,
            default: undefined
        },
    },
    data () {
        return {
            loading: false,
            activeName: '',
            tableData: [],
            saveLoading: false,
            optionsLoc: {
                iPrintModeOptions: [
                    {
                        sName: '静默打印',
                        sValue: 1
                    }, {
                        sName: '打开文件',
                        sValue: 2
                    }, {
                        sName: '下载文件',
                        sValue: 3
                    }
                ],
                sFileTypeOptions: [
                    {
                        sValue: 'pdf'
                    },
                    {
                        sValue: 'docx'
                    }
                ],
                sPrinterNameOptions: [],
            },
            sPrintShow: {
                showType: 1,
                isShowParams: false,
                isShowPriviewBtn: false,
                isShowPrintBtn: false,
                isShowDownloadBtn: false,
                isOneTemplate: false,
                iPrintType: 2,
            },
            originItem: {}
        }
    },
    computed: {
        workStation () {
            let temp = this.$store.getters['user/workStation'];
            return temp
        },
    },
 
    methods: {
        getModuleName(val) {
            let target = systemModuleOption.find(item => val === item.sValue);
            return target ? target.sName : null;
        },
        onEdit(row, index) {
            this.originItem = { ...row };
            row.isEdit = true
            if(!row.sPrinterName) {
                row.sPrinterName = this.optionsLoc.sPrinterNameOptions[0]
            }
            if(!row.sFileType) {
                row.sFileType = 'pdf'
            }
            if(!row.iCopies) {
                row.iCopies = 1
            }
        },
        onCancelClick(row) {
            Object.keys(this.originItem).map(key => {
                row[key] = this.originItem[key]
            })
            row.isEdit = false;
            this.originItem = {};
        },
        // 保存打印显示配置
        onChangeSetting(){
            if(this.sPrintShow.showType == 2) {
                const includeTrue = [this.sPrintShow.isShowParams, this.sPrintShow.isShowPriviewBtn, this.sPrintShow.isShowPrintBtn, this.sPrintShow.isShowDownloadBtn].includes(true);
                if(!includeTrue) {
                    this.sPrintShow.isShowParams = true;
                    this.sPrintShow.isShowPriviewBtn = true;
                    this.sPrintShow.isShowPrintBtn = true;
                    this.sPrintShow.isShowDownloadBtn = true;
                }
            }
            let jsonData = {
                iModuleId: this.iModuleId,
                sPrintShow: JSON.stringify(this.sPrintShow),
                sWorkStationId: this.workStation.stationId
            };
            delete jsonData.iHandleType;
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            workStationPrintShowSave(jsonData).then(res => {
                loading.close();
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch((err) => {
                console.log(err)
                loading.close();
            })
        },
        handleSaveRow(row, index) {
            let jsonData = row;
            this.saveLoading = true
            jsonData.iWorkStationId = this.workStation.stationId;
            saveWorkStationPrint(jsonData).then(res => {
                this.saveLoading = false
                if (res.success) {
                    this.$message.success(res.msg);
                    row.isEdit = false
                    this.tableData[index]['sId'] = res.data.sId;
                    return
                }
                this.$message.error(res.msg);
            }).catch((err) => {
                this.saveLoading = false
                console.log(err)
            })
        },
        // 保存
        onSaveClick (row, index) {
            let jsonData = row;
            saveWorkStationPrint(jsonData).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    this.tableData[index]['sId'] = res.data.sId;
                    return
                }
                this.$message.error(res.msg);
            }).catch((err) => {
                console.log(err)
            })
        },
        // 测试打印机
        onTestClick (row) {
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            printerTest({ printerName: row.sPrinterName }).then(res => {
                loading.close();
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                loading.close();
            })
        },
        // 获取配置数据
        getWorkStationPrintList () {
            this.tableData = [];
            let sId = this.workStation.stationId;
            this.loading = true
            workStationPrintList({ iWorkStationId: sId, iModuleId: this.iModuleId }).then(res => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data || [];
                    this.tableData.map(item => {
                        item['iCopies'] = item.iCopies === null ? undefined : item.iCopies;
                    })
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        },
        // 获取本地电脑打印机名称
        getPrinterNames () {
            getPrinterNames().then(res => {
                if (res.success) {
                    this.optionsLoc.sPrinterNameOptions = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            })
        },
        async getWorkStationPrintShow() {
            let jsonData = {
                sWorkStationId: this.workStation.stationId,
                iModuleId: this.iModuleId
            }
            this.sPrintShow = {};
            await getWorkStationPrintShow(jsonData).then(res => {
                if(res.success) {
                    if(res.data) {
                        res.data.map(item => {
                            if(item.iModuleId == jsonData.iModuleId) {
                                this.sPrintShow = JSON.parse(item.sPrintShow);
                            }
                        })
                        if(!Object.keys(this.sPrintShow).length) {
                            this.sPrintShow = this.$options.data().sPrintShow;
                        }
                    }
                    return
                }
                this.$message.error(res.msg);
            }).catch(err=> {
                console.log(err)
            })
        },
    },
    mounted() {
        this.getWorkStationPrintList();
        this.getPrinterNames();
        this.getWorkStationPrintShow()
    }
}
</script>

<style lang="scss" scoped>
.m-flexLaout-ty {
    padding: 15px 10px;
    overflow: hidden;
}
.c-check-item {
    margin-top: 10px;
    // margin-left: 30px;
    &.t-2 {
        margin-left: 40px;
    }
}
.c-item {
    display: flex; 
    margin-top: 15px;
    .c-item-child {
        flex: 1;padding: 15px; 
        border: 1px solid #eee;
        &.t-2 {
            margin-right: 15px
        }
        &.c-active {
            background-color: #DEEDF8;
            border-color: var(--el-color-primary);
        }
    }
}
</style>
