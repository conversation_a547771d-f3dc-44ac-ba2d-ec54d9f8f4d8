import {
    createPrintFile, downloadFileAndPrint, downloadFileAndPreview, getPrintClassifyOfModule,
    getTemplateOfPrintClassify, getWorkStationPrintShow, getPrinterNames, workStationPrintOfTemplate
} from '$supersetApi/projects/apricot/system/templateSet.js'
import { getToken } from '$supersetUtils/auth'

//  混入更新一条table患者信息
const mixinPrintPreview = {
    data() {
        return {
            btnsOption: [],
            optionsTemLoc: {
                sFileTypeOptions: [
                    { sValue: 'pdf' },
                    { sValue: 'docx' }
                ],
                sPrinterNameOptions: [],
            },
            sPrintShow: {
                showType: 1, // 1 = 打印方式一;  2=显示方式二
                isShowParams: false,
                isShowPriviewBtn: false,
                isShowPrintBtn: false,
                isShowDownloadBtn: false,
                isOneTemplate: false,
                iPrintType: 2,
            },
            teploading: false,
            isTemOnline: true,
            currentPrintParams: {
                sPrinterName: '',
                sFileType: 'pdf',
                iCopies: 1
            },
            sFileType:'pdf',
            iCopies: 1,
            printTemList: [],
            printDialogList: [],
            visibleTemplate: false,
        }
    },
    computed: {
        workStation() {
            let temp = this.$store.getters['user/workStation'];
            return temp
        },
    },
    methods: {
        // 加载提示
        loadFindTip(text) {
            return this.$loading({
                lock: true,
                text: text || '文件生成中...',
                // 
                background: 'rgba(255, 255, 255, 0.5)',
                customClass: 'my-loading'
            });
        },
        // 获取打印设置
        async mxWorkStationPrintOfTemplate(item) {
            let jsonData = {
                iWorkStationId: this.workStation.stationId,
                sTemplateId: item.sTemplateId
            }
            await workStationPrintOfTemplate(jsonData).then(res => {
                if(res.success) {
                    if(res.data) {
                        this.currentPrintParams = res.data;
                        this.currentPrintParams.iCopies = res.data.iCopies > 0 ? res.data.iCopies: this.iCopies;
                        this.currentPrintParams.sFileType = res.data.sFileType ? res.data.sFileType: this.sFileType;
                    } else {
                        this.currentPrintParams = this.$options.data().currentPrintParams;
                    }
                    this.currentPrintParams.sTemplateId = item.sTemplateId;
                    return
                }
                this.currentPrintParams = this.$options.data().currentPrintParams;
                this.currentPrintParams.sTemplateId = item.sTemplateId;
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
            })
        },
        // 预览按钮
        mxHandlePreview(params) {
            this.createFileAndPrint(params);
        },
        // 打印按钮
        mxHandlePrint(params) {
            if (!params.isBatch) {
                // 单个打印
                this.createFileAndPrint(params);
                return;
            }
            // 批量打印
            this.batchCreatedFileAndPrint(params);
        },
        // 批量处理
        async batchCreatedFileAndPrint(params) {
            const multipleSelection = [ ...this.multipleSelection ]
            if (!multipleSelection.length) {
                // 未选中患者
                this.$message.warning('请选择患者数据！')
                return
            }
            let loading = this.loadFindTip('加载中...');
            let len = multipleSelection.length;
            params.isStopPrint = false;
            for (let i = 0; i < len; i++) {
                if (params.isStopPrint) {
                    // 当一个患者打印出现问题，停止循环
                    loading.close();
                    return
                }
                params.patient = multipleSelection[i];
                await this.createFileAndPrint(params);
            }
            loading.close();
        },
        // 生成打印文件
        async createFileAndPrint(params) {
            if (!params.patient[params.idKey]) {
                this.$message.warning('请选择患者数据');
                return
            }
            let jsonData = {
                sPatientId: params.patient[params.idKey],
                sTemplateId: params.template.sTemplateId,
                iOpenType: params.iOpenType === 1 ? params.iOpenType : 2, // iOpenType = 1为预览，2 为打印；3为下载
                sWorkStationId: this.workStation.stationId,
                sFileType: params.template.sFileType || this.currentPrintParams.sFileType || this.sFileType  // 默认打印pdf类型，见currentTemplateSet配置
            }
            let isBatch = params.isBatch;
            let loading = {};
            if(!isBatch) (loading = this.loadFindTip('文件生成中，大约需45秒时间'));
            // 生成文件
            await createPrintFile(jsonData).then(async res => {
                !isBatch && loading.close();
                if (res.success) {
                    let iPrintType = params.iPrintType;  // iPrintType没有值的话，见sPrintShow属性配置
                    if (params.iOpenType == 2 && params.template.iClassify == 1 && (!params.patient.iIsPrintText || (params.template.iType == 3 && !params.patient.iIsPrintImg))) {
                        // 报告类型打印标记打印状态
                        this.updateTableInfo && this.updateTableInfo()
                    }
                    if (iPrintType == 1) {
                        // todo iPrintType = 1 打印模式=‘打印’
                        await this.downloadFileAndPrint(res.data, params);
                        return
                    }
                    if (iPrintType == 3) {
                        // todo  iPrintType = 3  打印模式=‘下载’
                        this.downloadFileByTarget(res.data);
                        return
                    }
                    // todo  iPrintType = 2  打印模式=‘预览’ 
                    if (jsonData.sFileType === 'pdf') {
                        // 文件类型 = pdf
                        let apricot = window.configs.urls.apricot;
                        apricot = apricot.includes('http') || apricot.includes('https') ? apricot : window.location.origin + apricot;
                        window.open(apricot + `/printFile/webPreviewFile?sFileId=` + res.data.sFileId, '_blank', 'noopener');
                        return
                    }
                    // 文件类型 =d ocx
                    await this.downloadFileAndPreview(res.data, params);
                    return
                }
                if (isBatch) {
                    // 批量打印错误提示信息处理
                    this.$message({
                        message: `患者：${params.patient.sName}；${res.msg}`,
                        type: 'error',
                        showClose: true,
                        duration: 5000
                    })
                    params.isStopPrint = true;
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                !params.isBatch && loading.close()
            })
        },
        // 打印机调用
        async downloadFileAndPrint(data, params) {
            let jsonData = {
                sFileId: data.sFileId,
                printerName: params.template.sPrinterName || this.currentPrintParams.sPrinterName,
                copies: params.template.iCopies || this.currentPrintParams.iCopies || this.iCopies
            }
            let loading = this.loadFindTip('文件加载中...');
            await downloadFileAndPrint(jsonData).then(res => {
                loading.close();
                if (res && !res.success) {
                    this.$message.error(res.msg);
                    return
                }
                if (res !== undefined) {
                    this.$message.success('正在打印文件！');
                }
                // if (params.template.iClassify == 1 && (!params.patient.iIsPrintText || (params.template.iType == 3 && !params.patient.iIsPrintImg))) {
                //     // 报告类型打印标记打印状态
                //     this.updateTableInfo && this.updateTableInfo()
                // }
            }).catch(err => {
                console.log(err);
                loading.close()
            });
        },
        // 下载并执行预览
        async downloadFileAndPreview(data) {
            let jsonData = {
                sFileId: data.sFileId
            }
            let loading = this.loadFindTip('文件加载中...');
            await downloadFileAndPreview(jsonData).then(res => {
                loading.close()
                if (res && !res.success) {
                    this.$message.error(res.msg);
                    return
                }
                if (res !== undefined) {
                    this.$message.success('正在打开文件！');
                }
            }).catch(() => {
                loading.close()
            })
        },
        // a标签下载
        downloadFileByTarget(params) {
            let a = document.createElement('a');
            let downURL = `${window.configs.urls.apricot}/printFile/webDownloadFile?sFileId=${params.sFileId}`
            a.href = downURL;
            a.setAttribute('target', '_blank');
            document.body.append(a);
            a.click();
            a.remove();
        },

        // 报告下载
        async mxReportDownload(params) {
            if (!params.patient[params.idKey]) {
                this.$message.warning('请选择患者数据');
                return
            }
            let jsonData = {
                sPatientId: params.patient[params.idKey],
                sTemplateId: params.template.sTemplateId,
                iOpenType: params.iOpenType, // iOpenType: 1=预览; 2=打印; 3=下载;
                sWorkStationId: this.workStation.stationId,
                sFileType: params.template.sFileType || this.currentPrintParams.sFileType || this.sFileType  // 默认打印pdf类型，见currentTemplateSet配置
            }
            let loading = this.loadFindTip('文件生成中，大约需45秒时间');
            // 生成文件
            await createPrintFile(jsonData).then(async res => {
                loading.close();
                if (res.success) {
                    this.downloadFileByTarget(res.data);
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                loading.close()
            })
        },
        // 获取打印按钮
        mxGetPrintClassifyOfModule: exGetPrintClassifyOfModule(),
        // 获取打印模板
        async mxGetTemplateOfPrintClassify(params) {
            // console.log(params)
            let jsonData = {
                iClassify: params.iClassify,
                iModuleId: params.iModuleId,
                sDeviceTypeId: params.sDeviceTypeId,
                sWorkStationId: this.workStation.stationId
            }
            this.teploading = true;
            await getTemplateOfPrintClassify(jsonData).then(res => {
                this.teploading = false;
                if (res.success) {
                    this.btnsOption.map(item => {
                        if (item.iClassify == params.iClassify) {
                            item.templateList = res.data || [];
                        }
                    })
                    return
                }
                this.btnsOption.map(item => {
                    if (item.iClassify == params.iClassify) {
                        item.templateList = [];
                    }
                })
                this.$message.error(res.msg)
            }).catch(err => {
                this.teploading = false;
                this.btnsOption.map(item => {
                    if (item.iClassify == params.iClassify) {
                        item.templateList = [];
                    }
                })
            })
        },
        // 工作站打印设置
        async mxGetWorkStationPrintShow(iModuleId) {
            let jsonData = {
                sWorkStationId: this.workStation.stationId,
                iModuleId: iModuleId || this.propParams.iModuleId
            }
            // this.sPrintShow = this.$options.data().sPrintShow;
            await getWorkStationPrintShow(jsonData).then(res => {
                if (res.success) {
                    if (res.data) {
                        res.data.map(item => {
                            if (item.iModuleId == jsonData.iModuleId) {
                                this.sPrintShow = JSON.parse(item.sPrintShow);
                            }
                        })
                    }
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
        // 获取本地电脑打印机名称
        async mxGetPrinterNames() {
            this.isTemOnline = true;
            await getPrinterNames().then(res => {
                if (res.success) {
                    this.optionsTemLoc.sPrinterNameOptions = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
                this.isTemOnline = false;
            })
        },

        exTemplateListByClassify: exTemplateListByClassify(),
        // configValue:Object 配置对象   isOncePrint=true 一次打印；false  每次打印
        async mxOnPrintByCache (input, isOncePrint = true) {
            
            // 读取缓存设置
            let configValue
            if (typeof input === 'string') {
              let moduleSetData = window.localStorage.getItem(input);
              if(!moduleSetData) return;
              try {
                configValue = JSON.parse(moduleSetData); 
                // 旧字段名兼容
                configValue.autoPrintType = configValue.autoPrintType || configValue.printTypeForSave
                configValue.autoPrintClassifys = configValue.autoPrintClassifys || configValue.printClassifys 
              } catch (error) {
                console.warn(error)
              }
            } else {
              configValue = input
            }

            if(!configValue) return;
            // autoPrintType = 0 不打印  1=首次打印，2=每次打印
            if(!configValue.autoPrintType) return;
            // 修改时
            if(!isOncePrint && configValue.autoPrintType != 2) return;
            // 获取配置打印的类型；
            let setPrintArr = configValue.autoPrintClassifys || [];
            // 设置打印的类型
            let len = setPrintArr.length;
            if(!len) return;
            let iModuleId = this.iModuleId;
            // 需要弹出手动选择打印的弹窗
            this.printDialogList = [];
            // 获取打印方式
            await this.mxGetWorkStationPrintShow(iModuleId);
            // 遍历模板
            for(var i = 0; i < len; i++) {
                let iClassify = setPrintArr[i]
                let jsonData = {
                    iClassify: iClassify,
                    iModuleId: iModuleId,
                    sDeviceTypeId: this.patientInfo.sRoomId,
                }
                // 获取模板list
                await this.exTemplateListByClassify(jsonData);
                if(this.printTemList.length === 1) {
                    // 当仅有一个模板时，执行打印
                    let params = {
                        patient: this.patientInfo,
                        isBatch: false,
                        idKey: 'sId',
                        // deviceTypeIdKey: 'sDeviceTypeId',
                        template: this.printTemList[0],
                        iPrintType: this.sPrintShow.iPrintType || 2,  // 2 = 打开文件
                    }
                    this.mxHandlePrint(params);
                }
                else if(this.printTemList.length > 1) {
                    // 当多个模板时， 放入数组中
                    this.printDialogList.push({iClassify, templateList: this.printTemList})
                }
            }
            if(this.printDialogList.length) {
                // 存在多个模板，显示弹出窗手动选择模板打印
                this.visibleTemplate = true;
            }
        },
        mxDialogPrintClick(item) {
            let targetObj = this.printDialogList[0].templateList.find(child => child.sTemplateId === item.sTemplateId);
            if (targetObj) {
                let params = {
                    patient: this.patientInfo,
                    isBatch: false,
                    idKey: 'sId',
                    // deviceTypeIdKey: 'sDeviceTypeId',
                    template: targetObj,
                    iPrintType: this.sPrintShow.iPrintType || 2,  // 2 = 打开文件
                }
                this.mxHandlePrint(params);
            }
            this.visibleTemplate = false;
            this.$nextTick(() => {
                if(this.printDialogList.length){
                    this.visibleTemplate = true;
                }
            })
        },
    }
}
export default mixinPrintPreview;

export function exGetPrintClassifyOfModule() {
    return async function (iModuleId) {
        let jsonData = {
            iModuleId: iModuleId
        }
        await getPrintClassifyOfModule(jsonData).then(res => {
            if (res.success) {
                this.btnsOption = res.data || [];
                if (this.btnsOption.length) {
                    this.btnsOption.map(item => {
                        item['templateList'] = [];
                    })
                }
                return
            }
            this.$message.error(res.msg)
        }).catch(err => {
            console.log(err);
        })
    }
}
export function exTemplateListByClassify() {
    return async function(params) {
        let jsonData = {
            iClassify: params.iClassify,
            iModuleId: params.iModuleId,
            sDeviceTypeId: params.sDeviceTypeId,
            sWorkStationId: this.workStation.stationId
        }
        this.printTemList = [];
        await getTemplateOfPrintClassify(jsonData).then(res => {
            if (res.success) {
                this.printTemList = res.data || [];
            } else {
                this.$message.error(res.msg);
            }
            
        }).catch(err => {
            console.log(err);
        })
    }
}
