import jquery from 'jquery'
import store from '$supersetStore/index.js'

let userRight = '';

/*无操作定时锁定------------------------------------------------------*/
+function(){
	var maxTime = 1000 * 60 * 5;
	var time;
	resetTime();
	
	document.addEventListener("keydown", resetTime);
  document.addEventListener("mousedown", resetTime);
  document.addEventListener("mousemove", resetTime);
  
  var timer = setInterval(function(){
  	
  	time--
  	
  	if(time == 0) {
			clearInterval(timer);
			// do something
		}
  	
  }, 1000);
  
  /**
   * 重置定时时间
   */
  function resetTime(){
  	//console.log('resetTime')
  	
  	time = maxTime;
  }
  
  
}();
/*-----------------------------------------------------无操作定时锁定*/

/*localStorage key------------------------------------------------*/
+function(){
	window.localStorageRootKey = {
		dragAdjustData: 'dragAdjustData',
		defaultRouterName: 'defaultRouterName'
	}
}();
/*------------------------------------------------localStorage key*/


/*defaultModuleRouterName-----------------------------------------*/
export function setDefaultModuleRouterName(value){
	//加后缀，以区分登录用户
	let suffix = '_' + store.getters['user/userId']
	
	localStorage[localStorageRootKey.defaultRouterName + suffix] = value
}

export function getDefaultModuleRouterName(){
	let defaultName ='welcome_Index' 
  //加后缀，以区分登录用户
	let suffix = '_' + store.getters['user/userId']
	//暂时存在
	// console.log( localStorageRootKey.defaultRouterName + suffix  )
	return localStorage[localStorageRootKey.defaultRouterName + suffix] || defaultName
}
/*-----------------------------------------defaultModuleRouterName*/

/*权限检测------------------------------------------------*/
export function checkRight(right){
	//检验是否包含权限
	if(!userRight){
		userRight = store.getters['user/userRight']
	}
	let uRight = userRight
	// return uRight.includes(right)
	return true
}
/*------------------------------------------------权限检测*/

export function setRightsData(data, vueInstance, rKey){
	//设置权限容器
	let rightKey = rKey || 'rightData'

	for(let key in data){
		// vueInstance[rightKey][key] = checkRight(data[key])
		vueInstance[rightKey][key] = true
	}
}

/*全局函数设置---------------------------------------------*/
window.winFn = window.winFn || {}
window.$ = jquery

winFn.checkRight = checkRight
winFn.setRightsData = setRightsData
/*---------------------------------------------全局函数设置*/
