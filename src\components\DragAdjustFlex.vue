<template>
    <div class="dragAdiust" ref="dragAdiust" :class="[djustData.type === 'column' ? 'row' : 'column']">
        <template v-for="(item, index) in djustData.panelConfig">
            <div
                :data-index="index"
                :data-minSize="item.minSize || 100"
                :data-maxSize="item.maxSize || 10000"
                :data-flex="item.flex"
                :style="styles[index]">
                <slot :name="item.slot"></slot>
            </div>
            <button ref="drag" class="drag-el" :style="{backgroundColor: dragBgColor}" :data-index="index" v-if="index != djustData.panelConfig.length - 1">
                <span></span>
                <span></span>
                <!-- <span></span>
                <span></span>
                <span></span>
                <span></span> -->
            </button>
        </template>
    </div>
</template>
<script>
export default {
    name: 'DragAdjustFlex',
    props: {
        modelValue: {
            type: Object,
            default: () => {
                return {
                    type: 'row',              // row 行排  column 列排
                    localStorageKey: '',      // 缓存 key 以开发时间命名
                    panelConfig: [            // 区块
                        {
                            slot: 'c1',       // 插槽名
                            size: 300,        // 当前 宽或高  300, '300px', '30%'  // 支持百分比、像素px
                            minSize: 200,     // 最小 宽或高  300, '300px', '30%'  // 支持百分比、像素px
                            maxSize: 500      // 最大 宽或高  300, '300px', '30%'  // 支持百分比、像素px
                        },
                        {
                            slot: 'c2',       // 插槽名
                            flex: 1           // 固定撑满
                        }
                    ]
                }
            }
        },
        dragBgColor: {
            default: String,
            default: 'transparent'
        }
    },
    data() {
        return {
            prefix: 'dragAdjustData_'
        }
    },
    computed: {
        djustData: {
            get: function () {
                return this.modelValue
            },
            set: function (val) {
                this.$emit('update:modelValue', val)
            }
        },
        styles () {
            let arr = []
            let adjustAttr = this.djustData.type === 'column' ? 'width:' : 'height:'
            let fixedAttr  = adjustAttr === 'width:' ? 'height: 100%;' : 'width: 100%;'
            
            const panel = this.djustData.panelConfig
            for (let index = 0; index < panel.length; index++) {
                const item = panel[index];
                if (item.flex) {
                    arr[index] = 'flex: 1;overflow: hidden;'
                }else {
                    arr[index] = `${adjustAttr}${item.size}${typeof item.size == 'number' ? 'px' : ''};${fixedAttr}`
                }
            }
            return arr
        }
    },
    methods: {
        init() {
            // 获取缓存
            this.getStore()
            // 拖拽控制事件
            this.dragController()
        },
        getStore() {
            if (this.djustData.localStorageKey) {
                const store = localStorage.getItem(this.prefix + this.djustData.localStorageKey)
                if (store) {
                    const obj = JSON.parse(store)
                    this.djustData.type = obj.type
                    this.djustData.panelConfig = obj.panelConfig
                }
            }
        },
        setStore() {
            // 缓存
            if (this.djustData.localStorageKey) {
                localStorage.setItem(this.prefix + this.djustData.localStorageKey, JSON.stringify(this.djustData))
            }
        },
        dragController() {
            this.$nextTick(() => {
                // 获取拖拽元素
                const dragEvent = this.$refs['drag']
                const dragAdiustDom = this.$refs['dragAdiust']

                // 元素添加事件
                for (let index = 0; index < dragEvent.length; index++) {
                    const element = dragEvent[index];
                    const idx = Number(element.getAttribute('data-index'))
                    element.onmousedown = (e) => {
                        // 记录按下时的值
                        const startX = e.clientX
                        const startY = e.clientY

                        // 拖拽目标修改区域
                        const curPanel = this.djustData.panelConfig[idx]
                        const nextPanel = this.djustData.panelConfig[idx + 1]

                        // 拖拽源
                        let moveSrouce = null
                        if (curPanel && !curPanel.flex) {
                            moveSrouce = curPanel
                        }else if (nextPanel && !curPanel.flex) {
                            moveSrouce = nextPanel
                        }
                        // 是否百分比
                        let suffix = 0
                        if (typeof moveSrouce.size === 'string') {
                            suffix = moveSrouce.size.includes('%') ? '%' : 'px'
                        }
                        // 按下时，原始偏移
                        const offsetValue = parseInt(moveSrouce.size)
                        // 拖拽
                        document.onmousemove = (e) => {
                            const endX = e.clientX
                            const endY = e.clientY
                            // 移动距离
                            const moveOffset = this.djustData.type === 'column' ? endX - startX : endY - startY
                            let moveValue = 0
                            if (suffix == '%') {
                                const max = this.djustData.type === 'column' ? dragAdiustDom.clientWidth - 6 : dragAdiustDom.clientHeight - 6

                                moveValue = offsetValue + (moveOffset / max * 100)
                            }else {
                                moveValue = offsetValue + moveOffset
                            }

                            // 最大，最小值限制
                            if (moveValue > parseInt(moveSrouce.maxSize)) {
                                moveValue = parseInt(moveSrouce.maxSize)
                            }else if (moveValue < parseInt(moveSrouce.minSize)) {
                                moveValue = parseInt(moveSrouce.minSize)
                            }
                            moveSrouce.size = moveValue + suffix
                        }
                        document.onmouseup = () => {
                            // 存储
                            this.setStore()
                            // 清除事件
                            document.onmousemove = null;
                            document.onmouseup = null;
                        }
                    }
                    
                }
            })
        }
    },
    mounted() {
        this.init()
    }
}
</script>
<style lang="scss" scoped>
.dragAdiust{
    display: flex;
    width: 100%;
    height: 100%;
    .drag-el{
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0;
        margin: 0;
        border: none;
        background-color: transparent;
        span {
            background: #aaa;
        }
    }
    &.row {
        flex-direction: row;
        > .drag-el{
            flex-direction: row;
            width: 10px;
            // border-left: 1px solid #e6e6e6;
            // border-right: 1px solid #e6e6e6;
            cursor: col-resize;
            span {
                height: 30px;
                width: 1px;
                &:last-child {
                    margin-left: 2px;
                }
            }
        }
    }
    &.column {
        flex-direction: column;
        > .drag-el{
            flex-direction: column;
            height: 10px;
            // border-top: 1px solid #e6e6e6;
            // border-bottom: 1px solid #e6e6e6;
            cursor: row-resize;
            span {
                height: 1px;
                width: 30px;
                &:last-child {
                    margin-top: 2px;
                }
            }
        }
    }
}
</style>