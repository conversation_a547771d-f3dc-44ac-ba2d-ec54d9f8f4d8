import request from '$supersetUtils/request'
import {
    baseURL,stringify
} from '$supersetUtils/request'

//获取患者信息（用于修改）
export function getPatientInfo(params) {
    return request({
        url: baseURL.apricot + '/patient/info/find/id',
        method: 'POST',
        params
    })
}

//患者信息修改
export function patientInfoModifi(data) {
    return request({
        url: baseURL.apricot + '/patient/info/edit',
        method: 'POST',
        data
    })
}

// 流程记录表操作接口: Flow Record Controller
export function flowRecordPage(data) {
    return request({
        url: baseURL.apricot + '/flow/record/find/page',
        method: 'POST',
        data
    })
}

// 流程记录表操作接口: Flow Record Controller
export function flowRecordInfo(data) {
    return request({
        url: baseURL.apricot + '/flow/record/find/flowinfo',
        method: 'POST',
        data: stringify(data)
    })
}

// 图像关联操作接口 : Img Patient Controller
export function imgPatientCreateRegisterInfo(data) {
    return request({
        url: baseURL.image + '/ris/create/register/info',
        method: 'POST',
        data
    })
}

// 设置紧急标志
export function prioritySet(data) {
    return request({
        url: baseURL.apricot + '/priority/set',
        method: 'POST',
        data: stringify(data)
    })
}


