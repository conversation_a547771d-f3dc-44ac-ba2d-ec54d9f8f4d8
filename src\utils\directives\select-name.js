/**
 * formData: { id: 2, name: '中文' }           // 表单数据
 * fields: { sProp: 'id', sPropName: 'name' }  // 属性字段
 * 
 * 方式
 * <el-select v-model="formData.id" v-select-name="{ formData, fields }"></el-select>
 */
export default {
    name: 'select-name',
    updated(el, { value }) {
        nextTick(() => {
            const dom = el.querySelector('input')
            const { formData, fields } = value
            
            // 没有参数，不处理
            if (!formData || !fields) {
                return
            }
            // id 跟 name 一样
            if (formData[fields.sProp] && formData[fields.sProp] == dom.value) {
                // 赋值显示 name
                dom.value = formData[fields.sPropName] || ''
            }else {
                // 没有 name 属性不赋值
                if (!fields.sPropName) {
                    return
                }
                // 一样不赋值
                if (dom.value == formData[fields.sPropName]) {
                    return
                }

                formData[fields.sPropName] = dom.value
            }
        })
    }
}