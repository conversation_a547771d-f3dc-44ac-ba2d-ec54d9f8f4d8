<template>
    <div class="flex flex-col h-full">
        <!-- <div class="g-title">
			<el-button-icon-fa
				type="warning"
        icon="fa fa-angle-double-left"
				:class="'f-closeProcess'" 
				@click="$emit('closeClick', '')">
        <span class=" text-sm">关闭</span>
			</el-button-icon-fa>
			<h3>{{headline}}</h3>
		</div> -->
        <slot></slot>
        <div class="flex-grow-0 m-2.5 " style="margin-bottom: 0px;">
            <slot name="businessMenu"></slot>
        </div>
        <div class=" flex-auto m-2.5 mt-0">
            <!--  hasDragBorder -->
            <DragAdjust class="i-caseBusiness" :dragAdjustData="isOpenEssayLayout ? computedDA2 : computedDA1">
                <template v-slot:c1>
                    <PatientInfo ref="PatientInfo" />
                    <slot name="essayTemplate"></slot>
                </template>
                <template v-slot:c2>
                    <slot name="businessEditor"></slot>
                </template>
                <template v-slot:c3>
                    <slot name="patientList"></slot>
                </template>
            </DragAdjust>
        </div>

    </div>
</template>

<script>
import PatientInfo from './PatientInfo.vue'
export default {
    name: 'ReportProcessLayout',
    props: {
        isShowInnerList: {
            default: true
        },
    },
    components: {
        PatientInfo
    },
    data () {
        return {
            DA1: {
                type: 't-x',
                localStorageKey: '202308061524',
                panelConfig: [
                    {
                        size: 450,
                        minSize: 0,
                        maxSize: 0,
                        name: "c1",
                        isFlexible: false
                    }, {
                        size: 0,
                        minSize: 0,
                        name: "c2",
                        isFlexible: true
                    }, {
                        size: 500,
                        minSize: 0,
                        name: "c3",
                        isFlexible: false
                    }
                ]
            },
            DA2: {
                type: 't-x',
                localStorageKey: '202308241517',
            },
            panelCacheKey: '',
            panelCacheKey2: '',
            infoPenalSize: 400,
        }
    },
    computed: {
        isOpenEssayLayout () {
            return this.$store.getters['apricot/report_module/isOpenEssayLayout'];
        },
        isHideEssayContent () {
            return this.$store.getters['apricot/report_module/isHideEssayContent'];
        },
        isCollapse () {
            return this.$store.getters['apricot/report_module/infoLayerMenuCollapse'];
        },
        computedDA1 () {
            const showC3 = this.isShowInnerList
            const showC1 = !this.isCollapse
            let panelConfig = this.DA1.panelConfig;
            panelConfig = panelConfig.map(item => {
                const obj = {
                    ...item,
                    _hidden: false
                }
                if (item.name == 'c3' && !showC3) obj._hidden = true;
                if (item.name == 'c1' && !showC1) obj._hidden = true;
                return obj
            })
            return { ...this.DA1, panelConfig };
        },
        computedDA2() {
            const showC3 = this.isShowInnerList
            let getConfigData1 = JSON.parse(localStorage.getItem(this.panelCacheKey1));
            let getConfigData2 = JSON.parse(localStorage.getItem(this.panelCacheKey2));
            // TODO 有待改善
            let oData2C0 = !this.isCollapse || this.isHideEssayContent ? getConfigData1[0]: (getConfigData2 && getConfigData2[0] || this.DA1.panelConfig[0]);
            delete oData2C0._hidden;
            if(this.isOpenEssayLayout && !this.isHideEssayContent) {
                const midWidth = window.innerWidth * (1 / 2)
                oData2C0.size = getConfigData1[0].size >= midWidth ? getConfigData1[0].size : midWidth;
            }
            const panelConfig = [ oData2C0,  
              ...getConfigData1.filter(item => item.name !== 'c1').filter(item => showC3 ? true : item.name !== 'c3') ]
            localStorage.setItem(this.panelCacheKey2, JSON.stringify(panelConfig));
            return { ...this.DA2, panelConfig };
        }
    },
    methods: {
        // 键盘 ESC 退出
        keyEsc (e) {
            if (e.keyCode == 27) {
                // 没有打开的弹窗，才进行关闭退出
                if (!$('.el-dialog__wrapper:modelValue').length && !$('.el-message-box__wrapper:modelValue').length) {
                    this.$emit('closeClick', '')
                }
            }
        },
    },
    created () {
        this.panelCacheKey1 = window.localStorageRootKey["dragAdjustData"] + '_' + this.DA1.localStorageKey;
        this.panelCacheKey2 = window.localStorageRootKey["dragAdjustData"] + '_' + this.DA2.localStorageKey;
    },
    beforeUnmount () {
        // window.removeEventListener('keyup', this.keyEsc) // 销毁监听
        this.isOpenEssayLayout && this.$store.commit({
            type: `apricot/report_module/setIsOpenEssayLayout`,
            isOpenEssayLayout: false
        });
    },
}
</script>

<style lang="scss" scoped>
.i-caseBusiness {
    background-color: var(--theme-menu-background-color);
}
</style>
