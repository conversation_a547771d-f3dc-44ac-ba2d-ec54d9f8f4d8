<template>
    <el-dialog title="质控评级模板设置"
        v-model="mainDialogVisible"
        append-to-body
        align-center
        width="1000"
        class="t-default my-dialog"
        :close-on-click-modal="false">
        <div class="c-flex-context c-container">
            <div class="c-form">
                <div class="c-form-btn text-right mb-2.5">
                    <el-button-icon-fa type="primary"
                        icon="el-icon-circle-plus-outline"
                        @click="handleAddTemplate">新增</el-button-icon-fa>
                </div>
                <el-dialog :title="dialogTitle"
                    v-model="dialogVisible"
                    append-to-body
                    class="t-default my-dialog"
                    width="1000"
                    :close-on-click-modal="false"
                    @close="closeTemplateDialog">
                    <div>
                        <el-form ref="refEditLayer"
                            :model="editLayer.form"
                            :rules="rules"
                            :show-message="false"
                            label-width="120px"
                            style="overflow: hidden;">
                            <el-col :span="13">
                                <el-form-item label="模板名称："
                                    prop="sTemplateName">
                                    <el-input v-model="editLayer.form.sTemplateName"
                                        clearable></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="13">
                                <el-form-item label="类型："
                                    prop="iTemplateType">
                                    <!-- <el-input v-model="editLayer.form.iTemplateType"></el-input> -->
                                    <el-select v-model="editLayer.form.iTemplateType"
                                        placeholder=" "
                                        clearable
                                        style="width:100%">
                                        <el-option v-for="item in optionsLoc.typeOptions"
                                            :key="item.sValue"
                                            :label="item.sName"
                                            :value="item.sValue">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="13">
                                <el-form-item label="状态："
                                    prop="iEnable">
                                    <!-- <el-input v-model="editLayer.form.iEnable"></el-input> -->
                                    <el-select v-model="editLayer.form.iEnable"
                                        placeholder=" "
                                        clearable
                                        style="width:100%">
                                        <el-option v-for="item in optionsLoc.stateOptions"
                                            :key="item.sValue"
                                            :label="item.sName"
                                            :value="item.sValue">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-form>
                        <div class="c-form-btn text-right mb-2.5">
                            <el-button-icon-fa type="primary"
                                plain
                                size="small"
                                icon="el-icon-circle-plus-outline"
                                @click="handleAddItemClick">添加</el-button-icon-fa>
                        </div>
                        <el-table class="pop-table"
                            :data="templateItems"
                            size="small"
                            border
                            height="350">
                            <el-table-column type="index"
                                label="序号"
                                prop="_index"
                                align="center"
                                width="60">
                            </el-table-column>
                            <el-table-column prop="sItemName"
                                label="评级项目"
                                min-width="120">
                                <template #default="{row, $index}">
                                    <el-input v-model="row.sItemName"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="sItemLevelJson"
                                label="质量等级"
                                min-width="100"
                                show-overflow-tooltip>
                                <template #default="{row, $index}">
                                    <div @click="(e) => showPopover(e, row, $index)"
                                        style="cursor: pointer;text-decoration:underline;">
                                        <template v-if="row?.sItemLevelJson"> {{setGradeString(row?.sItemLevelJson)}} </template>
                                        <el-icon v-else
                                            class="drag-icon"
                                            :size="18">
                                            <i class="el-icon-circle-plus-outline"></i>
                                        </el-icon>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column prop="label"
                                label="评级说明"
                                min-width="120">
                                <template #default="{row, $index}">
                                    <el-input v-model="row.sItemDesc"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="icon"
                                label="操作"
                                width="100"
                                align="center">
                                <template #default="{row, $index}">
                                    <el-button-icon-fa icon="el-icon-delete"
                                        link
                                        @click="handleDelItemClick(row, $index)">删除</el-button-icon-fa>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                    <template #footer>
                        <div class="dialog-footer">
                            <el-button-icon-fa :loading="editLayer.loading"
                                icon="el-icon-check"
                                type="primary"
                                @click="handleSaveTemplate">保存</el-button-icon-fa>
                            <el-button-icon-fa @click="closeDialog"
                                icon="el-icon-close">取消</el-button-icon-fa>
                        </div>
                    </template>
                </el-dialog>

                <el-popover v-if="popVisible"
                    placement="bottom"
                    :width="450"
                    trigger="click"
                    v-model:visible="popVisible"
                    virtual-triggering
                    :virtual-ref="tempRef"
                    popper-class="pop-class">
                    <div class="c-form-btn text-right mb-2.5">
                        <el-button-icon-fa type="primary"
                            plain
                            size="small"
                            icon="el-icon-circle-plus-outline"
                            @click="handleAddGradeClick">添加</el-button-icon-fa>
                    </div>
                    <!-- highlight-current-row -->
                    <el-table :data="gradeList"
                        ref="medicineRef"
                        row-key="sTemplateId"
                        size="small"
                        border
                        max-height="300">
                        <el-table-column min-width="100"
                            property="sItemLevelName "
                            label="等级名称"
                            show-overflow-tooltip>
                            <template #default="{row, $index}">
                                <el-input v-model="row.sItemLevelName"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column min-width="100"
                            property="iItemLevelGrade"
                            label="分值"
                            show-overflow-tooltip>
                            <template #default="{row, $index}">
                                <el-input type="number"
                                    v-model="row.iItemLevelGrade"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column prop="icon"
                            label="操作"
                            width="100"
                            align="center">
                            <template #default="{row, $index}">
                                <el-button-icon-fa icon="el-icon-delete"
                                    link
                                    @click="handleDelGradeClick(row, $index)">删除</el-button-icon-fa>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="margin-top: 10px;text-align: right;">
                        <el-button-icon-fa icon="el-icon-circle-check"
                            type="primary"
                            @click="handleSaveGradeClick">确定</el-button-icon-fa>
                        <el-button-icon-fa icon="fa fa-close-1"
                            @click="popVisible = false">关闭</el-button-icon-fa>
                    </div>
                </el-popover>
            </div>
            <div class="c-flex-auto">
                <div class="c-content"
                    v-loading="loading">
                    <el-table :data="tableData"
                        id="itemTable"
                        ref="mainTable"
                        size="small"
                        row-key="sTemplateId"
                        @row-click="handleRowClick"
                        highlight-current-row
                        border
                        stripe
                        height="500"
                        style="width: 100%">
                        <el-table-column v-for="item in configTable.filter(_i => !_i.iIsHide)"
                            show-overflow-tooltip
                            :key="item.index"
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort">
                            <template v-slot="{row, $index}">
                                <template v-if="item.sProp === 'iTemplateType'">
                                    {{ row.iTemplateType ? optionsLoc.typeOptions.find(o => row.iTemplateType == o.sValue)?.sName : undefined }}
                                </template>
                                <template v-else-if="item.sProp === 'iEnable'">
                                    {{ row.iEnable ? optionsLoc.stateOptions.find(o => row.iEnable == o.sValue)?.sName : undefined }}
                                </template>
                                <template v-else-if="item.sProp === 'action'">
                                    <el-button-icon-fa icon="el-icon-edit"
                                        link
                                        type="primary"
                                        @click="handleEditTemplate(row)">编辑</el-button-icon-fa>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button-icon-fa icon="el-icon-delete"
                                        link
                                        @click="handleDelTemplateClick(row)">删除</el-button-icon-fa>
                                </template>
                                <template v-else>
                                    {{ row[`${item.sProp}`] }}
                                </template>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa type="default"
                icon="fa fa-close-1"
                @click="mainDialogVisible = false;">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import {
    qualityTemplateQueryAll, addQualityTemplate, editQualityTemplate, delQualityTemplate,
    qualityTemplateItemFindById, delQualityTemplateItem, saveQualityTemplateItem
} from '$supersetApi/projects/apricot/case/report.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import { deepClone } from '$supersetUtils/function'
export default {
    name: 'QualityTemplateSet',
    mixins: [mixinTable],
    components: {},
    props: {
        modelValue: {
            default: false
        }
    },
    data () {
        return {
            isMixinDynamicGetTableHead: true,
            dialogVisible: false,
            dialogTitle: '',
            configTable: [{
                sProp: 'sTemplateName',
                sLabel: '模板名称',
                sMinWidth: '150px',
            },
            {
                sProp: 'iTemplateType',
                sLabel: '类型',
                sMinWidth: '80px',
            },
            {
                sProp: 'iEnable',
                sLabel: '状态',
                sMinWidth: '80px',
            },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '180px',
            }],
            optionsLoc: {
                typeOptions: [
                    { sName: '影像质量', sValue: 1 },
                    { sName: '报告质量', sValue: 2 },
                    { sName: '上机质量', sValue: 3 },
                ],
                stateOptions: [
                    { sName: '启用', sValue: 1 },
                    { sName: '禁用', sValue: 0 },
                ]
            },
            tableData: [],
            templateItems: [],
            rules: {
                sTemplateName: [{ required: true, message: '' }],
                iTemplateType: [{ required: true, message: '' }],
                iEnable: [{ required: true, message: '' }],
            },
            gradeList: [],
            popVisible: false,
            keyWordProp: undefined
        }
    },
    computed: {
        mainDialogVisible: {
            get () {
                return this.modelValue
            },
            set (val) {
                this.$emit('update:modelValue')
            }
        },

    },
    watch: {
        mainDialogVisible (val) {
            if (!val) return
            this.mxDoSearch()
        }
    },
    methods: {
        closeTemplateDialog () {
            this.mxDoSearch();
            this.templateItems = [];
        },
        setGradeString (jsonString) {
            if (!jsonString) {
                return ''
            }
            let newString = JSON.parse(jsonString);
            if (Object.prototype.toString.call(newString) !== '[object Array]') {
                return ''
            }
            let str = '';
            const len = newString.length;
            newString.map((item, index) => {
                str += item.sItemLevelName + '--' + item.iItemLevelGrade + ' ; ';
                // if(index < len - 1) {
                //     str += ';'
                // }
            })
            return str
        },
        closeDialog () {
            this.dialogVisible = false;
        },
        // 打开评分等级
        showPopover (e, row, index) {
            const evt = e || window.e || window.event;
            if (this.keyWordProp === index && this.popVisible) {
                this.popVisible = false;
                return
            }
            if (this.tempRef) this.popVisible = false;
            this.$nextTick(() => {
                this.tempRef = evt.currentTarget;
                this.popVisible = true;
                this.keyWordProp = index;
                if (row.sTemplateId && row.sItemLevelJson.length) {
                    this.gradeList = JSON.parse(row.sItemLevelJson);
                } else {
                    this.gradeList = [{}, {}, {}, {}];
                }
            })
        },
        // 添加评分等级
        handleAddGradeClick () {
            this.gradeList.push({
                sItemLevelName: '',
                iItemLevelGrade: ''
            });
        },
        // 删除评分等级
        handleDelGradeClick (row, index) {
            this.gradeList.splice(index, 1);
        },
        // 确定评分等级
        handleSaveGradeClick () {
            const filterList = this.gradeList.filter(item => (item.sItemLevelName ?? false) && (item.iItemLevelGrade ?? false));
            this.templateItems[this.keyWordProp].sItemLevelJson = filterList.length ? JSON.stringify(filterList) : '';
            this.popVisible = false;
            this.keyWordProp = undefined;
        },
        // 添加评级项目
        handleAddItemClick () {
            this.templateItems.push({
                sItemLevelJson: '',
                sItemName: '',
                sItemDesc: ''
            });
        },
        // 删除评级项目
        handleDelItemClick (row, index) {
            if (!row.sTemplateId) {
                this.templateItems.splice(index, 1);
                return
            }
            this.$confirm(`确定要删除【${row.sItemName}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                delQualityTemplateItem({ sItemId: row.sItemId }).then((res) => {
                    if (res.success) {
                        this.$message.success('删除成功！');
                        this.templateItems.splice(index, 1);
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err);
                });
            })
        },
        // 新增模版
        handleAddTemplate () {
            this.actionState = 1
            this.dialogVisible = true
            this.dialogTitle = '新增'
            let defaultForm = {
                sTemplateName: '',
                iTemplateType: '',
                iEnable: 1
            }
            this.editLayer.form = Object.assign({}, defaultForm);
            let timeout = setTimeout(() => {
                this.handleAddItemClick();
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout);
            }, 50)
        },
        // 编辑模版
        handleEditTemplate (row) {
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({}, row)
            this.$nextTick(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            })
            this.qualityTemplateItemFindById(row.sTemplateId);
        },
        handleSaveTemplate () {
            let params = Object.assign({}, this.editLayer.form)
            this.$refs['refEditLayer'].validate((valid) => {
                if (!valid) return

                const templateItems = Object.assign([], this.templateItems.filter(item => item.sItemId || item.sItemName));
                if (!templateItems.length) {
                    this.$message.warning('请添加评级项目！');
                    return
                } 
                const findItem = templateItems.find(item => !item.sItemName.length || !item.sItemLevelJson.length || !item.sItemDesc.length);
                if (findItem && !findItem.sItemName.length) {
                    this.$message.warning('请填写评级项目名称！');
                    this.editLayer.loading = false;
                    return
                }
                if (findItem && !findItem.sItemLevelJson.length) {
                    this.$message.warning('请填写评级项目质量等级！');
                    return
                }
                if (findItem && !findItem.sItemDesc.length) {
                    this.$message.warning('请填写评级项目说明！');
                    return
                }
                this.saveData(params)
            })
        },
        onClearForm () {
            this.editLayer.form = {};
            this.$nextTick(() => {
                this.$refs.refEditLayer.clearValidate();
            })
        },
        /**
         * 保存模版数据
         */
        async saveData (data) {
            let params = deepClone(data);
            let isSuccess = false;
            if (params.sTemplateId) {
                const selectedItem = this.editLayer.selectedItem;
                const keys = ['sTemplateName', 'iTemplateType', 'iEnable'];
                let isDiff = false;
                keys.map(item => {
                    if (selectedItem[item] !== params[item]) {
                        isDiff = true
                    }
                })
                if (!isDiff) {
                    // 若模版没有修改； 直接保存内容项
                    this.editLayer.loading = true
                    await this.saveQualityTemplateItem(params);
                    this.editLayer.loading = false;
                    return
                }
                // 保存模版和内容项
                this.editLayer.loading = true
                await editQualityTemplate(params).then(res => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        isSuccess = true;
                    } else {
                        this.$message.error(res.msg);
                    }
                }).catch(err => {
                    console.log(err)
                    this.editLayer.loading = false;
                });
                if (isSuccess) {
                    await this.saveQualityTemplateItem(params);
                }
                return
            }
            // 新增模版，并保存内容项
            this.editLayer.loading = true
            await addQualityTemplate(params).then(res => {
                this.editLayer.loading = false;
                if (res.success) {
                    isSuccess = true;
                    this.editLayer.form = params = res.data;
                } else {
                    this.$message.error(res.msg);
                }
            }).catch(err => {
                console.log(err)
                this.editLayer.loading = false;
            })

            if (isSuccess) {
                await this.saveQualityTemplateItem(params);
            }
        },
        async saveQualityTemplateItem (data) {
            let { sTemplateId } = data;
            const params = Object.assign([], this.templateItems.filter(item => item.sItemId || item.sItemName));
            params.map(item => {
                item.sTemplateId = sTemplateId;
            })
            if (!params.length) return
            await saveQualityTemplateItem(params).then(res => {
                if (res.success) {
                    let data = res.data;
                    if (Object.prototype.toString.call(data) === '[object Object]') {
                        this.templateItems = data;
                    }
                    this.$message.success('保存成功！');
                    return
                }
                this.$message.error(res.msg);
            });
        },
        // 删除模版
        handleDelTemplateClick (item) {
            this.$confirm(`确定要删除【${item.sTemplateName}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                delQualityTemplate({ sTemplateId: item.sTemplateId }).then((res) => {
                    if (res.success) {
                        this.$message.success('删除成功！');
                        this.mxGetTableList();
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err);
                });
            })
        },
        handleRowClick (row, isCancel = false, id = 'sTemplateId') {
            this.onClickRow(row, isCancel, id);
        },
        // 获取表格数据
        getData () {
            qualityTemplateQueryAll().then((res) => {
                if (res.success) {
                    this.tableData = res.data || [];
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                    return
                }
                this.loading = false;
                this.tableData = [];
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        },
        // 获取表格数据
        qualityTemplateItemFindById (sTemplateId) {
            this.templateItems = [];
            qualityTemplateItemFindById({ sTemplateId }).then((res) => {
                if (res.success) {
                    this.templateItems = res.data || [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => { })
        },
    },
};
</script>
<style lang="scss" scoped>
</style>
