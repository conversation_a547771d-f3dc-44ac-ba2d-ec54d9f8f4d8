import request, { baseURL, stringify } from '$supersetUtils/request'

// KIP短信模板接口  列表
export function kipSmsTemplateList (data) {
    return request({
        url: baseURL.apricot + '/kipSmsTemplate/list',
        method: 'POST',
        data
    })
}

// KIP短信模板接口  保存
export function kipSmsTemplateSave (data) {
    return request({
        url: baseURL.apricot + '/kipSmsTemplate/save',
        method: 'POST',
        data
    })
}

// KIP短信模板接口  删除
export function kipSmsTemplateDel (params) {
    return request({
        url: baseURL.apricot + '/kipSmsTemplate/del',
        method: 'POST',
        data: stringify(params)
    })
}


// KIP日志接口  列表
export function kipLogSaveSearch (data) {
    return request({
        url: baseURL.apricot + '/kipLog/search',
        method: 'POST',
        data
    })
}

// KIP日志接口  发送短信
export function kipLogSendSms (data) {
    return request({
        url: baseURL.apricot + '/kipLog/send/sms',
        method: 'POST',
        data
    })
}

// KIP关键字接口  读取
export function kipReportKeyList (data) {
    return request({
        url: baseURL.apricot + '/kipReportKey/list',
        method: 'POST',
        data
    })
}
// KIP关键字接口  新增
export function kipReportKeyAdd (params) {
    return request({
        url: baseURL.apricot + '/kipReportKey/add',
        method: 'POST',
        data: stringify(params)
    })
}

// KIP关键字接口  删除
export function kipReportKeyDel (params) {
    return request({
        url: baseURL.apricot + '/kipReportKey/del',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取KIP信息
export function getKip (params) {
    return request({
        url: baseURL.apricot + '/kip/getKip',
        method: 'POST',
        data: stringify(params)
    })
}

// 保存KIP信息
export function kipSave (data) {
    return request({
        url: baseURL.apricot + '/kip/save',
        method: 'POST',
        data
    })
}
