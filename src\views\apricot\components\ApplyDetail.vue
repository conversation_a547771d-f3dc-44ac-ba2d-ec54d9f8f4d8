<template>
    <div class="g-flexChild c-content">
        <el-col :span="iSpan">
            <label-info label="姓名"
                :title="info.sName">
                {{ info.sName }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="性别">
                {{ info.sSexText }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="年龄">
                {{ isAppointmentModule && !/[\u4e00-\u9fff]/.test(info.sAge) ?  (info.sAge ?? '') + (info.sAgeUnit ?? '') : info.sAge }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="出生日期">
                {{ toDate(info.dBirthday) }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="身高(cm)">
                {{ info.fHeight }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="体重(kg)">
                {{ info.fWeight }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="身份证">
                {{ info.sIdNum }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="患者电话">
                {{ info.sPhone }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="执行状态">
                {{ execStateTxt[info.iExecState] }}
            </label-info>
        </el-col>
        <el-col :span="isAppointmentModule ? 12 : 24">
            <label-info label="患者地址"
                :title="info.sAddress">
                {{ info.sAddress }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="预约日期">
                {{ isAppointmentModule ? toDate(info.dAppointDay) : toDate(info.dAppointmentTime) }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="就诊类型">
                {{ info.sSourceText }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="设备">
                {{ isAppointmentModule ? info.sDeviceTypeName : info.sRoomText }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="检查方法">
                {{ info.sMethodName }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="病历号"
                :title="info.sMedicalRecordNO">
                {{ info.sMedicalRecordNO }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="申请单号"
                :title="info.sApplyNO">
                {{ info.sApplyNO }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="医嘱号">
                {{ info.sOrderNO }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="医保卡号">
                {{ info.sCardNum }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="门诊号"
                :title="info.sOutpatientNO">
                {{ info.sOutpatientNO }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="住院号"
                :title="info.sInHospitalNO">
                {{ info.sInHospitalNO }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="就诊卡号"
                :title="info.sVisitCard">
                {{ info.sVisitCard }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="就诊流水号"
                :title="info.sVitisNo">
                {{ info.sVitisNo }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="就诊次数">
                {{ info.sEncounter }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="费用(元)">
                {{ info.fFees }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="申请医院">
                {{ info.sApplyHospitalText }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="收费状态">
                {{ info.sChargeStateText }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="病区">
                {{ info.sInpatientAreaText }}
            </label-info>
        </el-col>
        <el-col :span="iSpan">
            <label-info label="床号">
                {{ info.sBedNum }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="既往病史">
                {{ info.sPastHistory }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="简要病史">
                {{ info.sMedicalHistory }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="临床诊断">
                {{ info.sClinicalDiagnosis }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="申请单项目">
                {{ info.sOrderItemName }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="申请单部位">
                {{ info.sTargetSiteName }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="检查目的">
                {{ info.sCheckIntent }}
            </label-info>
        </el-col>
        <el-col :span="24">
            <label-info label="开单备注">
                {{ info.sMemo }}
            </label-info>
        </el-col>
    </div>
    <div class="my-footer">
        <el-col :span="6">
            <label-info label="申请科室">
                {{ info.sApplyDepartText }}
            </label-info>
        </el-col>
        <el-col :span="6">
            <label-info label="申请医生">
                {{ info.sApplyPersonName }}
            </label-info>
        </el-col>
        <el-col :span="6">
            <label-info label="申请日期">
                {{ toDate(info.dApplyDate) }}
            </label-info>
        </el-col>
        <el-col :span="6">
            <label-info label="医生电话">
                {{ info.sChiefPhysicianPhone }}
            </label-info>
        </el-col>
    </div>
</template>
<script>
export default {
    name: 'ApplyDetail',
    props: {
        info: {
            type: Object,
            default: () => ({})
        },
        isAppointmentModule: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            execStateTxt: {
                '-1': '已撤销申请',
                '0': '未登记',
                '1': '已登记',
                '-': '-'
            }
        }
    },
    computed: {
        iSpan() {
            return this.isAppointmentModule ? 4 :   8;
        }
    },
    methods: {
        toDate(time) {
            return time ? moment(time).format('YYYY-MM-DD') : null
        }
    }
}
</script>
<style lang="scss" scoped>
.c-content {
    padding: 15px 5px 10px 15px;

    .el-col {
        float: none;
        display: inline-block;
    }

    .el-col-24 {
        :deep(.m-labelInfo > span) {
            overflow: visible;
            text-overflow: inherit;
            white-space: inherit;
            line-height: 1.5;
        }
    }

    .m-labelInfo {
        width: calc(100% - 10px);
        margin: 0 10px 20px 0;
    }
}
.my-footer {
    padding: 10px 0 0 10px;

    .m-labelInfo {
        width: calc(100% - 10px);
        // font-size: 16px;
        margin: 0 10px 10px 0;
        line-height: 1.5;

        span {
            border: none;
        }
    }
}
</style>
