<template>
    <!-- 打印设置 -->
    <el-dialog 
        title="打印设置"
        :close-on-click-modal="false"
        append-to-body
        align-center
        :modelValue="dialogVisible"
        class="t-default my-dialog"
        width="1150px"
        @close="handleCloseDialog">
        <template #header>
            <span class="titles">打印设置</span><span>【{{getModuleName(iModuleId)}}】</span>
        </template>
        <div v-if="dialogVisible"
            class="c-dialog-body" style="height: 70vh;">
            <el-tabs v-model="activeName">
                <el-tab-pane label="打印设置" name="printSet">
                    <PrintSetMng :iModuleId="iModuleId" v-if="activeName == 'printSet'"></PrintSetMng>
                </el-tab-pane>
                <el-tab-pane label="模板关联" name="modulePrint">
                    <ModulePrintConfig v-if="activeName==='modulePrint'" ref="modulePrint" :iModuleId="iModuleId" 
                        @updateRelatedTemplate="updateRelatedTemplate" ></ModulePrintConfig>
                </el-tab-pane>
                <el-tab-pane label="文件设置" name="templateSet">
                    <TemplateSet v-if="activeName==='templateSet'" :iModuleId="iModuleId"></TemplateSet>
                </el-tab-pane>
            </el-tabs>
            
        </div>
        <template #footer>
            <el-button-icon-fa _icon="fa fa-close-1"
                 @click="$emit('closeDialog')">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import { systemModuleOption } from '$supersetResource/js/projects/apricot/enum.js'
import PrintSetMng from './Print/PrintSetMng.vue'
import ModulePrintConfig from './Print/ModulePrintConfig.vue'
import TemplateSet from './Print/TemplateSet.vue'
export default {
    name: 'PrintSet',
    components:{
        PrintSetMng,
        ModulePrintConfig,
        TemplateSet
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        iModuleId: {
            type: Number,
            default: undefined
        },
        
    },
    emits: ['closeDialog'],
    data () {
        return {
            loading: false,
            activeName: 'printSet',
            isChangeRelatedTemplate: false
        }
    },
    // computed: {
    //     workStation () {
    //         let temp = this.$store.getters['user/workStation'];
    //         return temp
    //     },
    // },
    methods: {
        // 模块名称
        getModuleName(val) {
            let target = systemModuleOption.find(item => val === item.sValue);
            return target ? target.sName : null;
        },
        // 关闭弹窗
        handleCloseDialog () {
            if(this.isChangeRelatedTemplate) {
                // 通知打印按钮组件
                this.$eventbus.emit('onRefreshPrintButtonClassify', true);
                this.isChangeRelatedTemplate = false;
            }
            this.$emit('closeDialog');
        },
        updateRelatedTemplate(bool) {
            this.isChangeRelatedTemplate = bool;
        }
     }
}
</script>

<style lang="scss" scoped>
    .titles {
        color: var(--theme-header-bg);
        margin-right: 10px;
    }
</style>
