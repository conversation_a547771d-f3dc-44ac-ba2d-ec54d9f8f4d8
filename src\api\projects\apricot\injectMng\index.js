import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'


export default {
    getInjectionData (data) {
        // 注射模块患者列表数据
        return request({
            url: baseURL.apricot + '/injection/info/findInjectionPage',
            method: 'POST',
            data
        })
    },

    getInjectionRow (params) {
        // 注射模块患者列表数据
        return request({
            url: baseURL.apricot + '/injection/info/find/patients/row',
            method: 'POST',
            params
        })
    },

    
}
