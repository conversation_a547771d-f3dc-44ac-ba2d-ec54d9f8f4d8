import { setDefaultModuleRouterName } from '$supersetResource/js/common.js'
import { constantRoutes } from '@/router'
import router from '$supersetRouter/index';

const state = {
  activeRouterName: null,
  menuList: [],
  moduleRouterDataList: [],
  RecentOpens: [],
  firstVisit: true, // 首次加载页面 
}

const getters = {
  menuList(state) {
    return state.menuList
  },
  moduleRouterDataList(state) {
    return state.moduleRouterDataList
  },
  RecentOpens(state) {
    return state.RecentOpens
  },
  activeRouterName(state) {
    return state.activeRouterName
  }
}

const actions = {
}

const mutations = {
  updateMenuListByAccess(state, { menuRight }) {
    // console.log(menuRight)
    const menuCodeList = menuRight
    let moduleRouters = []

    moduleRouters = constantRoutes.find(function (obj) {
      return obj.name == 'Main'
    }).children


    moduleRouters = moduleRouters.filter(item => {
      if (item.meta && item.meta.menuCode) {
        return menuCodeList.includes(item.meta.menuCode)
      }
      return true
    })


    let moduleRouterDataList = moduleRouters.map(item => {

      return {
        routerName: item.name,
        componentName: item.meta.componentName,            //组件名称要与路由名称对应
        meta: item.meta,
        children: item.children,
        hidden: item.hidden
      }
    })
    /*debugger*/
    // console.log(222, moduleRouterDataList)

    state.moduleRouterDataList = moduleRouterDataList

    var menuList = moduleRouterDataList
      .filter(item => !item.hidden)
      .map(item => {
        return {
          name: item.meta.name,
          routerName: item.routerName,
          icon: item.meta.icon,
          subtitle: item.meta.subtitle
        }
      });

    // console.log(444, menuList)

    state.menuList = menuList
  },
  //设置菜单列表
  setMenuList(state, payload) {
    state.menuList = payload.menuList
  },
  setModuleRouterDataList(state, payload) {
    /*debugger*/
    state.moduleRouterDataList = payload.moduleRouterDataList
  },

  //更改显示的模块路由
  switchModuleRouter(state, payload) {
    // debugger
    let routerName = payload.activeRouterName  //将要打开的路由名
    let RecentOpens = state.RecentOpens        //当前打开的路由名
    let targetTab = null
    //尝试匹配对应菜单
    let targetMenu = state.menuList.find(function (menu) {
      return menu.routerName == routerName
    })
    //如果不存在对应的菜单（重新部署可能不包含原有的某些模块）
    if (!targetMenu) {
      //重新设置默认的路由名称
      // this.commit({
      // 	type: 'module_router/switchModuleRouter',
      // 	activeRouterName: 'welcome_Index'
      // })

      //不继续往下执行
      return
    }

    //设置当前打开的模块，当MainContent.vue组件监听到时，就切换路由
    setTimeout(() => { // 后置执行，触发mainContent的watch
      state.activeRouterName = routerName  
    }, 0);

    if(state.firstVisit) {
        // 判断url地址与localStorage缓存的路由名称是否一致，若不一致，则跳转至缓存路由；
        !window.location.href.includes(routerName.toLowerCase()) && router.push({name: routerName});
        state.firstVisit = false;
    }

    /***************************************/
    //每次打开菜单，就设置为默认菜单，下一次打开浏览器默认打开此菜单
    setDefaultModuleRouterName(routerName)  /***************************************/

    //根据targetTab更新打开的模块的标签栏的状态（设置当前打开）
    for (let i = 0; i < RecentOpens.length; i++) {
      let tab = RecentOpens[i]
      if (tab.routerName == routerName) {
        targetTab = tab
        tab.isActive = true
      } else {
        tab.isActive = false
      }
    }

    //数组中不存在，且存在对应的路由，则增加
    if (!targetTab) {
      RecentOpens.push({
        routerName: routerName,
        name: targetMenu.name,
        isActive: true
      })
    }

    // 大于等于 6 个打开模块的时候，删除第二个模块的
    // if (RecentOpens.length > 6) {
    //   this.commit({
    //     type: 'module_router/removeRecentOpen',
    //     activeRouterName: RecentOpens[1].routerName
    //   })
    // }
  },
  //删除“打开过的 模块标签”列表项
  removeRecentOpen(state, payload) {

    let index = 0
    let RecentOpens = state.RecentOpens
    let targetTabIndex = RecentOpens.findIndex(function (tab, i) {
      return tab.routerName == payload.activeRouterName
    })

    if (targetTabIndex > -1) {
      index = targetTabIndex - 1
    }
    if (targetTabIndex === 0) {
      index = targetTabIndex + 1
    }

    //如果删除的是目前打开的模块，删除tab之前完成切换
    if (RecentOpens[targetTabIndex].routerName == state.activeRouterName) {
      this.commit({
        type: 'module_router/switchModuleRouter',
        activeRouterName: RecentOpens[index].routerName
      })
    }

    RecentOpens.splice(targetTabIndex, 1)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
