<template>
    <!-- 患者信息修改 -->
    <el-dialog title="编辑患者信息"
        :modelValue="dialogVisible"
        :close-on-click-modal="false"
        append-to-body
        fullscreen
        class="t-default my-dialog dialog-height90"
        width="1150px"
        @close="handleCloseDialog">
        <div class="c-dialog-body">
                <el-form ref="refEditLayer1"
                    :model="appointmentForm"
                    :rules="rules"
                    label-position="right">
                    <div class="i-patientInfoGroup clearfix">
                        <label>患者信息</label>
                        <div>
                            <FormList :list="patientInfoInputList0" :formData="appointmentForm" :optionData="optionsLoc"
                                :iModuleId="iModuleId" storageKey="PatientInfoModifiedForm0">
                                <template v-slot:sName="{ style }">
                                    <el-input class="i-history-1"
                                        v-model="appointmentForm.sName"
                                        :style="style"
                                        @input="() => {if(!appointmentForm.sName.length){isSpellShow = false}}"
                                        @blur="setPinYin">
                                        <template v-slot:append="scope"> 
                                            <el-popover v-model:visible="isHistoryShow"
                                                slot="append"
                                                class="i-sNameHistory"
                                                placement="right-start"
                                                :width="800"
                                                trigger="click"
                                                @show="getPatientNameHistoryData">
                                                <div style="min-height: 200px;">
                                                    <el-table :data="patientNameHistoryData"
                                                        @row-click="historyTableRowClick"
                                                        border
                                                        highlight-current-row
                                                        max-height="50vh">
                                                        <el-table-column width="60"
                                                            property="index"
                                                            label="序号"></el-table-column>
                                                        <el-table-column width="110"
                                                            property="sName"
                                                            label="姓名"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="100"
                                                            property="sNuclearNum"
                                                            label="核医学号"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="80"
                                                            property="sSexText"
                                                            label="性别"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="80"
                                                            property="sAge"
                                                            label="年龄"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="100"
                                                            property="dBirthday"
                                                            label="出生日期"
                                                            show-overflow-tooltip>
                                                            <template v-slot="scope">
                                                                {{transformDate(scope.row.dBirthday)}}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column width="150"
                                                            property="sProjectName"
                                                            label="检查项目"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="100"
                                                            property="dAppointmentTime"
                                                            label="预约日期"
                                                            show-overflow-tooltip>
                                                            <template v-slot="scope">
                                                                {{transformDate(scope.row.dAppointmentTime)}}
                                                            </template>
                                                        </el-table-column>
                                                        <el-table-column width="150"
                                                            property="sPhone"
                                                            label="电话"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="150"
                                                            property="sIdNum"
                                                            label="身份证"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="150"
                                                            property="sCardNum"
                                                            label="社保号"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="150"
                                                            property="sInHospitalNO"
                                                            label="住院号"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="150"
                                                            property="sOutpatientNO"
                                                            label="门诊号"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="150"
                                                            property="sApplyDepartText"
                                                            label="申请科室"
                                                            show-overflow-tooltip></el-table-column>
                                                        <el-table-column width="200"
                                                            property="sClinicalDiagnosis"
                                                            label="临床诊断"
                                                            show-overflow-tooltip></el-table-column>
                                                    </el-table>
                                                </div>
                                                <template #reference>
                                                <el-button slot="reference"
                                                    link
                                                    style="width: 50px;">历史</el-button>
                                                </template>
                                            </el-popover>
                                        </template>
                                    </el-input>
                                </template>
                                <template v-slot:sNameSpell="{ style }">
                                    <el-popover placement="bottom"
                                        :width="250"
                                        :visible="isSpellShow"
                                        @show="isSpellShow = !!optionsLoc.pinyinOptions.length">
                                        <el-radio-group v-model="appointmentForm.sNameSpell"
                                            @change="isSpellShow = false">
                                            <el-radio v-for="(item, index) in optionsLoc.pinyinOptions"
                                                :key="index"
                                                :label="item"
                                                style="margin-top: 15px;">
                                                <span style="font-size: 16px;">{{item}}</span>
                                            </el-radio>
                                        </el-radio-group>
                                        <template #reference>
                                        <el-input slot="reference"
                                            v-model="appointmentForm.sNameSpell"
                                            clearable
                                            :style="style"
                                            @focus="isSpellShow = true"
                                            @blur="() => {if(appointmentForm.sNameSpell){isSpellShow = false}}"
                                            @clear="isSpellShow = !!optionsLoc.pinyinOptions.length"></el-input>
                                        </template>
                                    </el-popover> 
                                </template>
                                <template v-slot:sIdNum="{ style }">
                                    <el-input v-model="appointmentForm.sIdNum"
                                        clearable
                                        :style="style"
                                        @blur="computedAgeByIdNums"></el-input>
                                </template>
                                <template v-slot:dBirthday="{ style }">
                                    <el-date-picker v-model="appointmentForm.dBirthday"
                                        type="date"
                                        :picker-options="pickerOptions"
                                        :style="style"
                                        @blur="computedAgeByBirthDay">
                                    </el-date-picker>
                                </template>
                                <template v-slot:iAge="{ style }">
                                    <el-input v-model="appointmentForm.iAge"
                                        :style="style"
                                        onKeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                                        @blur="computedBirthDayByAge">
                                        <template #append>
                                            <el-select v-model="appointmentForm.sAgeUnit"
                                                @change="computedBirthDayByAge"
                                                placeholder="单位"
                                                style="width:70px;height:100%">
                                                <el-option v-for="item in optionsLoc.sAgeUnitOptions"
                                                    :key="item.sValue"
                                                    :label="item.sName"
                                                    :value="item.sValue"></el-option>
                                            </el-select>
                                        </template>
                                    </el-input>
                                </template>
                                <template v-slot:fWeight="{ style, row }">
                                    <el-input-number
                                        v-model="appointmentForm.fWeight"
                                        :min="0"
                                        :controls="false"
                                        :style="style"
                                        @input.native="(val)=>handleInputWeight(row, val)"
                                        @change="(val)=>handleInputWeight(row, val)">
                                    </el-input-number>
                                </template>
                            </FormList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>检查信息</label>
                        <div>
                            <FormList :list="patientInfoInputList1" :formData="appointmentForm" :optionData="optionsLoc"
                                :iModuleId="iModuleId" storageKey="PatientInfoModifiedForm1">
                                <template v-slot:sDistrictId="{ style }">
                                    <el-select v-model="appointmentForm.sDistrictId" 
                                        :disabled="isDisabled" placeholder=" " clearable :style="style"
                                        @change="onChangeHospital"> 
                                        <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                                            :key="index"
                                            :label="item.sHospitalDistrictName"
                                            :value="item.sId">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:sMachineryRoomId="{ style }">
                                    <el-select v-model="appointmentForm.sMachineryRoomId" 
                                        :disabled="isDisabled" placeholder=" " clearable :style="style"
                                        @change="onChangeMachineRoom">
                                        <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                                            :key="index"
                                            :label="item.sRoomName"
                                            :value="item.sId">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:sProjectId="{ style }">
                                    <el-select v-model="appointmentForm.sProjectId" 
                                        :disabled="isDisabled" placeholder=" " clearable :style="style"
                                        @change="onChangeProject">
                                        <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                                            :key="index"
                                            :label="item.sItemName"
                                            :value="item.sId">
                                            <span style="float: left">{{ item.sItemName }}</span>
                                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                                        </el-option>
                                    </el-select>
                                </template>
                                <!-- 问诊室 -->
                                <template v-slot:sConsultRoomId="{ style }">
                                    <el-select v-model="appointmentForm.sConsultRoomId"
                                        :disabled="isDisabled"
                                        clearable placeholder=" "
                                        :style="style">
                                        <el-option v-for="item in optionsLoc.consultRoomOption"
                                            :key="item.sValue"
                                            :label="item.sName"
                                            :value="item.sValue"></el-option>
                                    </el-select>
                                </template>
                                <template v-slot:sInjectionRoomId="{ style }">
                                    <el-select v-model="appointmentForm.sInjectionRoomId"
                                        :disabled="isDisabled"
                                        clearable placeholder=" "
                                        :style="style">
                                        <el-option v-for="item in optionsLoc.injectionRoomOption"
                                            :key="item.sValue"
                                            :label="item.sName"
                                            :value="item.sValue"></el-option>
                                    </el-select>
                                </template>
                                <template v-slot:sNuclearNum="{ style }">
                                    <el-input v-model="appointmentForm.sNuclearNum"
                                        :style="style">
                                        <template #append>
                                            <el-button link
                                                style="width: 50px;"
                                                @click="getNuclearNum">new</el-button>
                                        </template>
                                    </el-input>
                                </template>
                                <!-- 核素特殊情况 ['sNuclideText','sTracerText','sPositionText','sTestModeText'] -->
                                <!-- 核素特殊情况 ['核素','示踪剂','检查部位','用药'] -->
                                <template v-for="(item, index) in patientInfoInputList1.filter(i => ['sNuclideText','sTracerText','sPositionText','sTestModeText'].includes(i.sProp))"
                                    v-slot:[item.sProp]="{ style, row }" :key="index">
                                    <el-select v-model="appointmentForm[medicineConfigParams[row.sProp].prop]"
                                        filterable
                                        clearable
                                        placeholder=" "
                                        :style="style"
                                        @change="onMedicineChange(row.sProp)">
                                        <el-option v-for="item in optionsLoc[medicineConfigParams[row.sProp].optionListName]"
                                            :key="item[medicineConfigParams[row.sProp].optionValue]"
                                            :label="item[medicineConfigParams[row.sProp].optionLabel]"
                                            :value="item[medicineConfigParams[row.sProp].optionValue]" />
                                    </el-select>
                                    <!-- <el-popover :ref="item.sProp"
                                        width="670"
                                        trigger="click"
                                        @show="getItemSetData"
                                        @hide="onPopoverHide">
                                        <div style="margin-bottom: 10px;">
                                            <el-input v-model="medicationCondition.nuclearName" 
                                                placeholder="核素" 
                                                clearable
                                                style="width: 148px;margin-right: 4px"></el-input>
                                            <el-input v-model="medicationCondition.tracerName" 
                                                placeholder="示踪剂" 
                                                clearable
                                                style="width: 148px"></el-input>
                                        </div>
                                        <el-table :data="nuclideParamOptions.filter(item => item.sNuclideName.toLowerCase().includes(medicationCondition.nuclearName.toLowerCase())).filter(item =>item.sTracerName.toLowerCase().includes(medicationCondition.tracerName.toLowerCase()))"
                                            :ref="item.sProp + 'Table'"
                                            highlight-current-row
                                            size="small"
                                            border
                                            max-height="300"
                                            @row-click="(o) => nuclearTableRowClick(o, item.sProp)">
                                            <el-table-column width="100"
                                                property="sNuclideName"
                                                label="核素"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column min-width="100"
                                                property="sTracerName"
                                                label="示踪剂"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column width="100"
                                                property="sItemPositionName"
                                                label="检查部位"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column width="120"
                                                property="sTestModeName"
                                                label="检查方式"
                                                show-overflow-tooltip>
                                            </el-table-column>
                                            <el-table-column width="60"
                                                property="iIsInvariable"
                                                label="定量"
                                                show-overflow-tooltip>
                                                <template #default="{ row }">
                                                    {{ row.iIsInvariable ? '是' : '否'}}
                                                </template>
                                                </el-table-column>
                                            <el-table-column width="80"
                                                property="fCoefficient"
                                                label="处方系数"
                                                show-overflow-tooltip></el-table-column>
                                            <el-table-column width="80"
                                                property="fDosage"
                                                label="定量剂量"
                                                show-overflow-tooltip></el-table-column>
                                        </el-table>
                                        <div style="margin-top: 10px;text-align: right;">
                                            <el-button-icon-fa
                                                icon="fa fa-close-1"
                                                @click="popVisible = false">关闭</el-button-icon-fa>
                                        </div>
                                        <template #reference>
                                            <el-input v-model="appointmentForm[row.sProp]"
                                                readonly
                                                :style="style"></el-input>
                                            </template>
                                    </el-popover> -->
                                </template>
                                
                                <template v-slot:fRecipeDose="{ style, row }">
                                    <el-input v-model="appointmentForm[row.sProp]"
                                        class="input-select-append"
                                        type="number"
                                        :style="style">
                                        <template #append>
                                            <el-select v-model="appointmentForm.sRecipeDoseUnit"
                                                placeholder="单位"
                                                style="width:70px;height:100%">
                                                <el-option v-for="item in optionsLoc.sRecipeDoseUnitOptions"
                                                    :key="item.sValue"
                                                    :label="item.sName"
                                                    :value="item.sName"></el-option>
                                            </el-select>
                                        </template>
                                    </el-input>
                                </template>
                                <template v-slot:fBloodSugar="{ style, row }">
                                    <el-input v-model="appointmentForm[row.sProp]"
                                        type="number"
                                        :style="style">
                                        <template #suffix>
                                            <span>mmol/L</span>
                                        </template>
                                    </el-input>
                                </template>
                                <template v-slot:fBloodSugarHis="{ style, row }">
                                    <el-input v-model="appointmentForm[row.sProp]"
                                        type="number"
                                        :style="style">
                                        <template #suffix>
                                            <span>mmol/L</span>
                                        </template>
                                    </el-input>
                                </template>
                            </FormList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>就诊信息</label>
                        <div>
                            <FormList :list="patientInfoInputList2" :formData="appointmentForm" :optionData="optionsLoc"
                                :iModuleId="iModuleId" storageKey="PatientInfoModifiedForm2">
                                <template v-slot:sImageNo="{ style, row }">
                                    <el-input v-model="appointmentForm[row.sProp]"
                                        :style="style">
                                        <el-button slot="append"
                                            link
                                            style="width: 50px;"
                                            @click="getImageNo">get</el-button>
                                    </el-input>
                                </template>
                            </FormList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>申请信息</label>
                        <div>
                            <FormList :list="patientInfoInputList4" :formData="appointmentForm" :optionData="optionsLoc"
                                :iModuleId="iModuleId" storageKey="PatientInfoModifiedForm4">
                                <template v-for="(item) in patientInfoInputList4.filter(i => ['fMedicineFee','fExamineFee'].includes(i.sProp))"
                                    v-slot:[item.sProp]="{ style, row }">
                                    <el-input v-model="appointmentForm[row.sProp]"
                                        type="number"
                                        :style="style">
                                        <template #suffix>
                                            <span>元</span>
                                        </template>
                                    </el-input>
                                </template>
                            </FormList>
                        </div>
                    </div>
                    <div class="i-patientInfoGroup clearfix">
                        <label>其它信息</label>
                        <div>
                            <FormList :list="patientInfoInputList9" :formData="appointmentForm" :optionData="optionsLoc"
                                :iModuleId="iModuleId" storageKey="PatientInfoModifiedForm9">
                                <template v-for="(item) in patientInfoInputList9.filter(i => ['fOneHours','fTwoHours','fThreeHours'].includes(i.sProp))"
                                    v-slot:[item.sProp]="{ style, row }">
                                    <el-input v-model="appointmentForm[row.sProp]"
                                        type="number"
                                        :style="style">
                                        <template #suffix>
                                            <span>mmol/L</span>
                                        </template>
                                    </el-input>
                                </template>
                            </FormList>
                        </div>
                    </div>
                </el-form> 
        </div>
        <template #footer>
            <el-button-icon-fa
                type="primary"
                _icon="fa fa-save"
                @click="patientInfoModifi"
                :loading="loading">保存</el-button-icon-fa>
            <el-button-icon-fa
                _icon="fa fa-close-1"
                @click="handleCloseDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import config from './config/index'

import { transformDate, getOptionName } from '$supersetResource/js/tools'
import { deepClone } from '$supersetUtils/function'
import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { getStoreNameByRoute } from '$supersetResource/js/projects/apricot'
import {
    getPatientNameHistoryData, getNuclearNum,
    chineseToPinyin, brokenImgno, getHistoryNuclearNum
} from '$supersetApi/projects/apricot/appointment/index.js'
import { getPatientInfo, patientInfoModifi } from '$supersetApi/projects/apricot/common'
import { getMachineRoomById, getItemSetData, getItemPositionData, getTestModeData, 
        getNuclideData, getTracerData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { getWorkStationData } from '$supersetApi/projects/apricot/system/workStation.js'
import { useGetHospitalData, useGetMachineRoomData, useGetItemData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'

import { createNamespacedHelpers } from 'vuex'

const { mapState } = createNamespacedHelpers('apricot/appointment')

export default {
    name: 'PatientInfoModifi',
    emits: ['closeDialog', 'updatePatientOfTable'],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        isUpdateRowData: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: new Object()
        },
        iModuleId: {
            default: ''
        }
    },
    data () {
        return {
            appointmentForm: {},
            optionsLoc: {
                iSexList: appointmentEnum.sexOptions,
                sSourceOptions: appointmentEnum.visitTypeOptions,
                // 登记方式
                sRegisterOptions: this.$store.getters['dict/map'].ApricotReportRegister || [],
                // 收费类型
                ApricotReportFeeType: this.$store.getters['dict/map'].ApricotReportFeeType || [],
                // 处方剂量单位
                sRecipeDoseUnitOptions: this.$store.getters['dict/map'].ApricotReportDoseUnit || [],
                // 客户级别
                sCustomerLevelOptions: this.$store.getters['dict/map'].ApricotReportCustomerLevel || [],
                sAgeUnitOptions: appointmentEnum.ageUnitOptions,
                iIsBedsideExamineOptions: [{ sName: '是',  sValue: '1' }, { sName: '否', sValue: '0' }],
                pinyinOptions: [],
                ChargeStateOptions: [{ sName: '已收费', sValue: '1' }, { sName: '未收费', sValue: '0' }],
                consultRoomOption: [] ,
                injectionRoomOption: [],
                
                districtArrOption: [], //院区
                machineRoomArrOption: [], // 机房
                itemsArrOption: [],    // 项目

                nuclideOptions: [],
                tracerOptions: [],
                itemPositionOptions: [],
                testModeOptions: [],
                ethnicGroupCodeOption: this.$store.getters['dict/map'].ethnic_group || []
            },
            rules: {},
            nuclideParamOptions: [],
            patientNameHistoryData: [],
            checkupOptions: [],
            loading: false,
            isHistoryShow: false,
            nuclearParams: {},  // 用药参数
            pickerOptions: {
                disabledDate: time => {
                    return time.getTime() > new Date().getTime()
                }
            },
            isSpellShow: false,
            patientInfoInputList0: [...config.appointmentInputList1],
            patientInfoInputList1: [...config.appointmentInputList0, ...config.appointmentInputList3],
            patientInfoInputList2: [...config.appointmentInputList2],
            patientInfoInputList4: [...config.appointmentInputList4, ...config.appointmentInputList6],
            patientInfoInputList9: [...config.appointmentInputList5, ...config.appointmentInputList9],
            isDisabled: false, // 检查项目信息是否可编辑
            medicationCondition: {
                nuclearName: '',
                tracerName: ''
            },
            medicineConfigParams: {
                sNuclideText: {
                    prop: 'sNuclide',
                    optionListName: 'nuclideOptions',
                    optionLabel: 'sNuclideName',
                    optionValue: 'sId'
                },
                sTracerText: {
                    prop: 'sTracer',
                    optionListName: 'tracerOptions',
                    optionLabel: 'sTracerName',
                    optionValue: 'sId',
                },
                sPositionText: {
                    prop: 'sPosition',
                    optionListName: 'itemPositionOptions',
                    optionLabel: 'sItemPositionName',
                    optionValue: 'sId',
                },
                sTestModeText: {
                    prop: 'sTestMode',
                    optionListName: 'testModeOptions',
                    optionLabel: 'sTestModeName',
                    optionValue: 'sId',
                },
            }
        }
    },
    computed: {
        ...mapState({
            'sNuclearNum': state => state.sNuclearNum,
        }),
    },
    watch: {
        sNuclearNum (newData) {
            this.appointmentForm['sNuclearNum'] = newData
        },
        dialogVisible: {
            async handler (newValue) {
                this.$refs.refEditLayer1 && this.$refs.refEditLayer1.resetFields();
                if (newValue) {
                    // 获取院区、机房、项目数据
                    await this.useGetHospitalData(), 
                    await this.useGetMachineRoomData(this.patientInfo.sDistrictId);
                    await this.useGetItemData(this.patientInfo.sRoomId);
                    await this.handleGetNuclideData();
                    await this.handleGetTracerData();
                    await this.handleGetItemPositionData();
                    await this.handleGetTestModeData();
                    // 获取患者信息
                    await this.getPatientInfo();
                    this.isDisabled = this.appointmentForm?.iFlowState >= 3;
                    // 获取注射室、问诊室数据
                    this.getWorkStationByType(3)
                    this.getWorkStationByType(4)
                    return
                }
            }
        },
    },
    methods: {
        transformDate: transformDate,
        // 选中核素相关参数赋值给表单对象
        setParamsOfNuclideToForm() {
            let id = this.appointmentForm.sNuclide;
            const tempItem = this.optionsLoc.nuclideOptions.find(item => item.sId === id);
            if(!tempItem) return
            this.appointmentForm.sNuclideText = tempItem.sNuclideName;
            this.appointmentForm.sNuclideSupName = tempItem.sNuclideSupName;
        },
        // 选中示踪剂相关参数赋值给表单对象
        setParamsOfTracerToForm() {
            let id = this.appointmentForm.sTracer;
            const tempItem = this.optionsLoc.tracerOptions.find(item => item.sId === id);
            if(!tempItem) return
            this.appointmentForm.sTracerText = tempItem.sTracerName;
            this.appointmentForm.sTracerSupName = tempItem.sTracerSupName;
        },
        // 选中检查部位相关参数赋值给表单对象
        setParamsOfPositionToForm() {
            let id = this.appointmentForm.sPosition;
            const tempItem = this.optionsLoc.itemPositionOptions.find(item => item.sId === id);
            if(!tempItem) return
            this.appointmentForm.sPositionText = tempItem.sItemPositionName;
        },
        // 选中检查部位相关参数赋值给表单对象
        setParamsOfTestModeToForm() {
            let id = this.appointmentForm.sTestMode;
            const tempItem = this.optionsLoc.testModeOptions.find(item => item.sId === id);
            if(!tempItem) return
            this.appointmentForm.sTestModeText = tempItem.sTestModeName;
        },
        onMedicineChange (propName) {
            const changeFn = {
                sNuclideText: this.setParamsOfNuclideToForm,
                sTracerText: this.setParamsOfTracerToForm,
                sPositionText: this.setParamsOfPositionToForm,
                sTestModeText: this.setParamsOfTestModeToForm,
            }
            changeFn[propName]();

            this.nuclearParams = {};
            this.findNuclearRow(this.nuclideParamOptions);
            this.doComputeDosageBySetting();
        },
        // 核素
        async handleGetNuclideData () {
            await getNuclideData({}).then(res => {
                if (res.success) {
                    this.optionsLoc.nuclideOptions = res?.data || [];
                    return
                }
                this.optionsLoc.nuclideOptions = []
            }).catch(err => {
                this.optionsLoc.nuclideOptions = []
            })
        },
        // 示踪剂
        async handleGetTracerData () {
            await getTracerData({}).then(res => {
                if (res.success) {
                   this.optionsLoc.tracerOptions = res?.data || [];
                    return
                }
               this.optionsLoc.tracerOptions = []
            }).catch(err => {
               this.optionsLoc.tracerOptions = []
            })
        },
        // 检查部位
        async handleGetItemPositionData () {
            await getItemPositionData({}).then(res => {
                if (res.success) {
                    this.optionsLoc.itemPositionOptions = res?.data || [];
                    return
                }
                this.optionsLoc.itemPositionOptions = []
            }).catch(err => {
                this.optionsLoc.itemPositionOptions = []
            })

        },
        // 检查方式
        async handleGetTestModeData () {
            await getTestModeData({}).then(res => {
                if (res.success) {
                    this.optionsLoc.testModeOptions = res?.data || [];
                    return
                }
                this.optionsLoc.testModeOptions = []
            }).catch(err => {
                this.optionsLoc.testModeOptions = []
            })

        },
        async onChangeHospital (val) {
            this.appointmentForm.sMachineryRoomId = '';
            this.appointmentForm.sProjectId = '';
            await this.useGetMachineRoomData(this.appointmentForm.sDistrictId);
            this.optionsLoc.itemsArrOption = []
        },
        async onChangeMachineRoom (val) {
            var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === val);
            this.appointmentForm['sRoomId'] = target.sDeviceTypeId;
            this.appointmentForm['sRoomText'] = target.sDeviceTypeName;
            if(target || !val) {
                await this.useGetItemData(target?.sDeviceTypeId);
                var filterLen = this.optionsLoc.itemsArrOption.filter(item => this.appointmentForm.sProjectId === item.sId).length
                if(!this.optionsLoc.itemsArrOption.length || !filterLen) {
                    this.appointmentForm.sProjectId = ''
                }
            }
        },
        onChangeProject () {
            this.getItemSetData();
        },
        // 处理体重输入
        handleInputWeight (item, val) {
            let value = val;
            let nuclearParams = this.nuclearParams;
            if (!Object.keys(nuclearParams).length) {
                this.appointmentForm['fRecipeDose'] = value > 0 && this.appointmentForm.sNuclide ? (value * 0.1).toFixed(2) : undefined;
                return
            }
            if(nuclearParams.iIsInvariable) return;
            let regx = item.sRegEx ? new RegExp(item.sRegEx) : null;
            let bool = (value && regx && regx.test(value)) || (value && !regx);
            this.appointmentForm['fRecipeDose'] = bool ? Number((value * nuclearParams.fCoefficient).toFixed(2)) : undefined;
        },
        // 根据身份证号计算年龄
        computedAgeByIdNums () {
            let identityCard = this.appointmentForm.sIdNum
            let len = (identityCard + '').length;
            let regx = new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/);
            // 身份证号码只能为15位或18位其它不合法
            if (len == 0 || (len != 15 && len != 18) || regx.test(identityCard) == false) {
                return
            }
            // // 性别  sSex
            // if (parseInt(identityCard.slice(-2, -1)) % 2 == 1) {
            //     this.appointmentForm['sSex'] = '1';
            // } else {
            //     this.appointmentForm['sSex'] = '2';
            // }
            var birthDate = this.getComputeStringBirthday();
            this.appointmentForm['dBirthday'] = birthDate.toISOString();

            var nowDateTime = new Date();
            // let age =  nowDateTime.getFullYear() > birthDate.getFullYear() ?  nowDateTime.getFullYear() - birthDate.getFullYear() : '';
            // //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
            // if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
            //     age--;
            // }
            // this.appointmentForm['sAgeUnit'] = 'Y';
            // this.appointmentForm['iAge'] = age;

            if (birthDate.getTime() >= nowDateTime.getTime()) {
                this.appointmentForm['sAgeUnit'] = 'Y';
                this.appointmentForm['iAge'] = '';
                return
            }
            if (nowDateTime.getFullYear() > birthDate.getFullYear()) {
                let age = nowDateTime.getFullYear() - birthDate.getFullYear();
                //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
                if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
                    age--;
                }
                this.appointmentForm['sAgeUnit'] = 'Y';
                this.appointmentForm['iAge'] = age;
                return
            }
            if (nowDateTime.getMonth() > birthDate.getMonth()) {
                let age = nowDateTime.getMonth() - birthDate.getMonth();
                //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
                if (nowDateTime.getDate() < birthDate.getDate()) {
                    age--;
                }
                this.appointmentForm['sAgeUnit'] = 'M';
                this.appointmentForm['iAge'] = age;
                return
            }
            if (nowDateTime.getDate() > birthDate.getDate() >= 7) {
                let age = nowDateTime.getDate() - birthDate.getDate();
                this.appointmentForm['sAgeUnit'] = 'W';
                this.appointmentForm['iAge'] = Math.floor(age / 7);
                return
            }
            let age = nowDateTime.getDate() - birthDate.getDate();
            this.appointmentForm['sAgeUnit'] = 'D';
            this.appointmentForm['iAge'] = age;
            return
        },
        getComputeStringBirthday() {
            let identityCard = this.appointmentForm.sIdNum
            let len = (identityCard + '').length;
            let regx = new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/);
            // 身份证号码只能为15位或18位其它不合法
            if (len == 0 || (len != 15 && len != 18) || regx.test(identityCard) == false) {
                return ''
            }
            var strBirthday = "";
            //处理18位的身份证号码从号码中得到生日
            //时间字符串里，必须是“/”
            if (len == 18) {
                strBirthday = identityCard.substr(6, 4) + "/" + identityCard.substr(10, 2) + "/" + identityCard.substr(12, 2);
            }
            if (len == 15) {
                strBirthday = "19" + identityCard.substr(6, 2) + "/" + identityCard.substr(8, 2) + "/" + identityCard.substr(10, 2);
            }
            return  new Date(strBirthday);
        },
        // 根据生日计算年龄 规则（小于一个月单位为天，一个月到三个月单位为周，三到十二个月单位为月，大于等于十二个月单位岁）
        computedAgeByBirthDay () {
            // let reg = new RegExp( /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/);
            // let identityCard = this.appointmentForm.sIdNum;
            // if(reg.test(identityCard)) return;
            if (!this.appointmentForm.dBirthday) {
                this.appointmentForm['iAge'] = '';
                this.appointmentForm['sAgeUnit'] = 'Y';
                return;
            }
            var birthDate = new Date(this.appointmentForm.dBirthday);
            var nowDateTime = new Date();

            if (birthDate.getTime() >= nowDateTime.getTime()) {
                this.appointmentForm['iAge'] = '';
                this.appointmentForm['sAgeUnit'] = 'Y';
                return;
            }
            var disMonth = nowDateTime.getFullYear() * 12 + nowDateTime.getMonth() - birthDate.getFullYear() * 12 - birthDate.getMonth()

            if (disMonth > 12) {
                let age = nowDateTime.getFullYear() - birthDate.getFullYear();
                //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
                if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
                    age--;
                }
                this.appointmentForm['sAgeUnit'] = 'Y';
                this.appointmentForm['iAge'] = age;
                return
            }
            if (disMonth == 12 && nowDateTime.getDate() >= birthDate.getDate()) {
                this.appointmentForm['sAgeUnit'] = 'Y';
                this.appointmentForm['iAge'] = 1;
                return
            }
            if ((disMonth > 3 && disMonth <= 12) || (disMonth == 3 && nowDateTime.getDate() >= birthDate.getDate())) {
                let age = disMonth;
                //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
                if (nowDateTime.getDate() < birthDate.getDate()) {
                    age--;
                }
                this.appointmentForm['sAgeUnit'] = 'M';
                this.appointmentForm['iAge'] = age;
                return
            }
            if ((disMonth > 1 && disMonth <= 3) || disMonth == 1 && nowDateTime.getDate() >= birthDate.getDate()) {
                let age = (nowDateTime.getTime() - birthDate.getTime()) / (7 * 24 * 60 * 60 * 1000);
                this.appointmentForm['sAgeUnit'] = 'W';
                this.appointmentForm['iAge'] = Math.round(age);
                return
            }
            let age = (nowDateTime.getTime() - birthDate.getTime()) / (24 * 60 * 60 * 1000);
            this.appointmentForm['sAgeUnit'] = 'D';
            this.appointmentForm['iAge'] = Math.floor(age);
            return
        },
        // 根据年龄计算生日（岁，月，周，日）
        computedBirthDayByAge () {
            // let reg = new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/);
            // let identityCard = this.appointmentForm.sIdNum;
            // if (reg.test(identityCard) || !this.rules.hasOwnProperty('dBirthday')) return;
            let appointmentForm = this.appointmentForm;
            let iAge = appointmentForm.iAge;
            // 如果配置数据 isAgeComputeBirthday 为 false，则不进行计算
            // if (!window.configs.businessParams.isAgeComputeBirthday) return
            if (!iAge) return
            let date = new Date();
            let birdthDay = '';
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            // 分别计算当前年龄单位为（岁，月，周，日）
            if (appointmentForm.sAgeUnit === "Y") {
                birdthDay = (year - iAge) + '-' + month + '-' + day
                birdthDay = (year - iAge) + `-${month}-${day}`
            }
            if (appointmentForm.sAgeUnit === "M") {
                let month2Year = Math.floor(iAge / 12);
                let remainder = iAge % 12;
                birdthDay = (year - month2Year - 1) + '-' + (remainder > month ? 12 - remainder + month : month - remainder) + '-' + day;
            }

            if (appointmentForm.sAgeUnit === "W") {
                birdthDay = date.getTime() - iAge * 7 * 60 * 60 * 24 * 1000;
            }
            if (appointmentForm.sAgeUnit === "D") {
                birdthDay = date.getTime() - iAge * 60 * 60 * 24 * 1000;
            }
            this.appointmentForm['dBirthday'] = new Date(birdthDay);
        },
        //患者历史表格 行点击事件
        historyTableRowClick (row) {
            this.isHistoryShow = false;
            this.appointmentForm['sName'] = row.sName;
            this.appointmentForm['sSex'] = row.sSex;
            this.appointmentForm['sSexText'] = row.sSexText;
            this.appointmentForm['dBirthday'] = row.dBirthday ? new Date(row.dBirthday) : row.dBirthday;
            this.appointmentForm['iAge'] = row.iAge;
            this.appointmentForm['sPhone'] = row.sPhone;
            this.appointmentForm['sIdNum'] = row.sIdNum;
            this.appointmentForm['sCardNum'] = row.sCardNum;
            this.getHistoryNuclearNum(row);
            if (!this.appointmentForm.sNameSpell) {
                let dom = document.querySelector('.i-history-1');
                dom && dom.removeAttribute('sname');
                this.setPinYin();
            }
            if (this.appointmentForm.sIdNum) {
                this.computedAgeByIdNums();
                return
            }
            if (this.appointmentForm.dBirthday) {
                this.computedAgeByBirthDay();
                return
            }
            if (this.appointmentForm.iAge) {
                this.computedBirthDayByAge();
                return
            }
        },
        getHistoryNuclearNum (row) {
            getHistoryNuclearNum({
                sName: row.sName,
                sNuclearNum: row.sNuclearNum,
                sInHospitalNO: row.sInHospitalNO,
                sMedicalRecordNO: row.sMedicalRecordNO,
                sOutpatientNO: row.sOutpatientNO,
                sPatientIndex: row.sPatientIndex,
                sRoomId: this.appointmentForm.sRoomId
            }).then(res => {
                if (res.success) {
                    this.$store.commit('apricot/appointment/setNuclearNum', {
                        sNuclearNum: res.data
                    });
                    return
                }
                this.$message.error(res.msg);
            })
        },
        // 关闭核素药物popover组件
        onPopoverHide() {
            this.medicationCondition.nuclearName = '';
            this.medicationCondition.tracerName = '';
        },
        popoverClickOutside(sProp) {
            this.$refs[sProp] && this.$refs[sProp].hide();
        },
        // 核素相关表格行点击事件
        nuclearTableRowClick (row, sProp) {
            this.nuclearParams = row;
            this.popoverClickOutside(sProp);
            // 核素、示踪剂、检查部位、检查方式相关字段的赋值
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode'];
            let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sItemPositionName', 'sItemPositionId', 'sTestModeName', 'sTestModeId'];
            formKeys.map((item, index) => {
                this.appointmentForm[item] = row[oKeys[index]];
            })
            this.doComputeDosageBySetting();
        },
        findNuclearRow(arr) {
            if(!arr.length) {
                return
            }
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode'];
            let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sItemPositionName', 'sItemPositionId', 'sTestModeName', 'sTestModeId'];
            let finalItem = arr.find(item => {
                let isEq = true;
                oKeys.map((key, index) => {
                    if(this.appointmentForm[formKeys[index]] && item[key] !== this.appointmentForm[formKeys[index]]){
                        isEq = false;
                    }
                })
                let targetItem  = null;
                if(isEq) {
                    targetItem = item;
                }
                return targetItem;
            })
            if(finalItem) {
                this.nuclearParams = finalItem;
                ['sNuclideText','sTracerText','sPositionText','sTestModeText'].map(item => {
                    const tableRef = this.$refs[item + 'Table'];
                    tableRef && tableRef.setCurrentRow(finalItem)
                })
            }
            // console.log('findNuclearRow',this.nuclearParams);
        },
        // 根据核素设置计算处方剂量
        doComputeDosageBySetting() {
            let val = this.nuclearParams;
            let fWeight = this.appointmentForm.fWeight;
            
            if (!Object.keys(val).length) {
                fWeight > 0 && (this.appointmentForm['fRecipeDose'] = (fWeight * 0.1).toFixed(2))
                return
            }
            if (val.iIsInvariable) {
                this.appointmentForm['fRecipeDose'] = Number(val.fDosage);
                return
            }
            this.appointmentForm['fRecipeDose'] = (fWeight && val.fCoefficient) ? Number((fWeight * val.fCoefficient).toFixed(2)) : undefined;
        },
        // 清除核素相关属性数据
        resetFormNuclearData () {
            let temp = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode', 'fRecipeDose'];
            for (let item in temp) {
                if (this.appointmentForm[temp[item]]) {
                    this.appointmentForm[temp[item]] = null;
                }
            }
        },
        // 关闭弹窗
        handleCloseDialog (isEditInfo) {
            this.$emit('closeDialog', isEditInfo);
            this.isSpellShow = false; 
            this.patientNameHistoryData = [];
        },
        // 获取患者历史数据
        getPatientNameHistoryData () {
            this.patientNameHistoryData = [];
            let sName = this.appointmentForm.sName;
            let sRoomId = this.appointmentForm.sRoomId;
            if (!sName) {
                return
            }
            if(!this.appointmentForm.sRoomId) {
                this.$message.warning('请选择设备类型！');
                return
            }
            getPatientNameHistoryData({
                sName,
                sRoomId
            }).then(res => {
                if (res.success) {
                    this.patientNameHistoryData = res.data || [];
                    let sexs = {}
                    this.optionsLoc.iSexList.map(item => {
                        sexs[item.sValue] = item.sName
                    });
                    let data = this.patientNameHistoryData;
                    for (let i = 0; i < data.length; i++) {
                        let item = data[i];
                        item.index = i + 1;
                        item.sSexText = item.sSexText || sexs[item.sSex];
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch((err) => {
                console.log(err)
            })
        },
        // 获取患者基本信息
        async getPatientInfo () {
            let sId = this.patientInfo.sId;
            if (!sId) return;
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            await getPatientInfo({ sId: sId }).then(res => {
                loading.close()
                if (res.success) {
                    this.setAppointmentForm(res.data);
                    this.getItemSetData(true);
                }
            }).catch(err => {
                loading.close()
                console.log(err)
            })
        },
        setAppointmentForm (data) {
            for (let key in data) {
                this.appointmentForm[key] = data[key] || undefined;
            }
        },
        patientInfoModifi () {
            this.$refs['refEditLayer1'].validate(valid => {
                if (!valid) {
                    this.$message.closeAll();
                    this.$message({
                        message: '请完成必填信息的录入！',
                        type: 'info',
                        duration: 3000
                    });
                    return false;
                }
                if (this.isSpellShow) {
                    this.$message.closeAll();
                    this.$message({
                        message: '请完成选择姓名拼音！',
                        type: 'warning',
                    });
                    return
                }
                // 验证成功
                let temp = {
                    sSexText: getOptionName(this.appointmentForm.sSex, this.optionsLoc.iSexList),  // 性别
                    sRegisterText: getOptionName(this.appointmentForm.sRegister, this.optionsLoc.sRegisterOptions),  //登记方式
                    sAgeUnitText: getOptionName(this.appointmentForm.sAgeUnit, this.optionsLoc.sAgeUnitOptions), // 年龄单位
                    sSourceText: getOptionName(this.appointmentForm.sSource, this.optionsLoc.sSourceOptions),  // 就诊类型
                    sChargeStateText: getOptionName(this.appointmentForm.sChargeState, this.optionsLoc.ApricotReportFeeType), // 收费状态
                    sFeeTypeText: getOptionName(this.appointmentForm.sFeeType, this.optionsLoc.ChargeStateOptions), // 收费类型
                    sConsultRoomName: getOptionName(this.appointmentForm.sConsultRoomId, this.optionsLoc.consultRoomOption),  // 问诊室
                    sInjectionRoomName: getOptionName(this.appointmentForm.sInjectionRoomId, this.optionsLoc.injectionRoomOption),   //注射室
                    sDistrictName: getOptionName(this.appointmentForm.sDistrictId, this.optionsLoc.districtArrOption),   // 院区
                    sMachineryRoomText: getOptionName(this.appointmentForm.sMachineRoomId, this.optionsLoc.machineRoomArrOption),  // 机房
                    sProjectName: getOptionName(this.appointmentForm.sProjectId, this.optionsLoc.itemsArrOption)   // 项目
                }
                let combine = { ...this.appointmentForm, ...temp };
                let data = deepClone(combine);
                this.loading = true;
                patientInfoModifi(data).then(res => {
                    this.loading = false;
                    if (!res.success) {
                        this.$message.error(res.msg);
                        return
                    }
                    this.$message.success(res.msg);
                    this.updatedPatientInfo(res.data)
                    this.setAppointmentForm(res.data);
                    this.isUpdateRowData && this.$emit('updatePatientOfTable');
                    let urlModule = getStoreNameByRoute(this.$route.name)
                    this.$store.commit({
                        type: `apricot/${urlModule}/setIsUpdatePatientInfo`,
                        IsUpdatePatientInfo: true
                    });
                    this.handleCloseDialog(true);
                }).catch(err => {
                    this.loading = false;
                    console.log(err)
                })
            })
        },
        updatedPatientInfo(data) {
            Object.assign(this.patientInfo, data);
        },
        getOptions (data) {
            this.checkupOptions = data;
        },
        // 过滤选项
        filterOptions (valueList, options, newArr) {
            if (valueList instanceof Array) {
                let sid = valueList.shift()
                let item = options.filter(function (item) {
                    return item.sId == sid
                })[0]
                newArr.push(item)
                if (item.childs) {
                    this.filterOptions(valueList, item.childs, newArr)
                }

            }
        },
        // 获取检查用药数据
        async getItemSetData (isSetSelectedRow) {
            this.nuclearParams = {};
            let form = this.appointmentForm;
            await getItemSetData({
                sItemId: form.sProjectId,
                iIsEnable: 1
            }).then(res => {
                if (res.success) {
                    this.nuclideParamOptions = res?.data || [];
                    if(!isSetSelectedRow) {
                        return
                    }
                    this.findNuclearRow(this.nuclideParamOptions);
                    return
                }
                this.nuclideParamOptions = []
            }).catch(err => {
                console.log(err);
                this.nuclideParamOptions = []
            })
        },
        // 获取核医学号
        getNuclearNum () {
            let data = this.appointmentForm;
            if (!data.sRoomId) {
                this.$message.warning('请选择检查项目！');
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            getNuclearNum({
                sDistrictId: data.sDistrictId,
                sDeviceTypeId: data.sRoomId,
                sRoomId: data.sMachineryRoomId,
                sItemId: data.sProjectId
            }).then(res => {
                loading.close();
                if (res.success) {
                    this.$store.commit('apricot/appointment/setNuclearNum', {
                        sNuclearNum: res.data
                    });
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close();
                console.log(err);
            })
        },
        // 获取影像号
        getImageNo () {
            let orignData = deepClone(this.appointmentForm)
            let jsonData = {
                sName: orignData.sName,
                sSexCode: orignData.sSex,
                dBirthday: orignData.dBirthday,
                sIdNum: orignData.sIdNum,
                sVisitTypeCode: orignData.sSource,
                sInHospitalNO: orignData.sInHospitalNO,
                sOutpatientNO: orignData.sOutpatientNO
            };
            let tipmsg = {
                sName: '请填写患者姓名',
                sSexCode: '请填选患者性别',
                dBirthday: '请填选患者出生日期',
                sIdNum: '请填写患者身份证号',
                sVisitTypeCode: '请填选患者就诊类型',
                sInHospitalNO: '住院号和门诊号至少填写一项'
            }
            for (let key in jsonData) {
                if (key == 'sInHospitalNO' || key == 'sOutpatientNO') {
                    continue
                }
                if (!jsonData[key]) {
                    this.$message.warning(tipmsg[key]);
                    return
                }
            }
            if (!jsonData.sInHospitalNO && !jsonData.sOutpatientNO) {
                this.$message.warning(tipmsg.sInHospitalNO);
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            brokenImgno(jsonData).then(res => {
                loading.close();
                if (res.success) {
                    this.appointmentForm['sImageNo'] = res.data;
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close();
                console.log(err);
            })
        },
        // 检索
        querySearch (queryString, cb) {
            var targetOptions = this.optionsLoc.pinyinOptions;
            // var results = queryString ? targetOptions.filter(this.createFilter(queryString)) : targetOptions;
            var results = targetOptions;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        // 创建拼音
        setPinYin () {
            let sName = this.appointmentForm.sName;
            let dom = document.querySelector('.i-history-1');
            if(!dom) return
            if (!sName) {
                this.optionsLoc['pinyinOptions'] = [];
                this.appointmentForm['sNameSpell'] = undefined;
                this.$refs.refEditLayer1.clearValidate('sNameSpell');
                dom && dom.removeAttribute('sname');
                this.isSpellShow = false;
                return
            }
            if (sName === dom.getAttribute('sname')) { return }
            dom.setAttribute('sName', this.appointmentForm.sName);
            chineseToPinyin({ sChineseText: this.appointmentForm.sName }).then(res => {
                if (res.success) {
                    let data = res.data || [];
                    this.optionsLoc.pinyinOptions = data;
                    this.appointmentForm['sNameSpell'] = '';
                    this.$refs.refEditLayer1.clearValidate('sNameSpell');
                    if(this.isHistoryShow) return;
                    if(data.length > 1) {
                        this.isSpellShow = true
                    } else if(data.length === 1) {
                        this.appointmentForm['sNameSpell'] = data[0];
                    }
                    return
                }
                this.$message.error(res.msg)
            })
        },
        // 获取问诊室，注射室工作站'ApricotReportConsult'  ApricotReportInject
        getWorkStationByType (typeCode) {
            getWorkStationData({
                districtId: this.patientInfo.sDistrictId,
                stationTypeCode: typeCode,
            }).then((res) => {
                if (res.success) {
                    if(typeCode == 3) {
                        this.optionsLoc.consultRoomOption = res.data || [];
                        this.optionsLoc.consultRoomOption.map(item =>{
                            item.sValue = item.stationId
                            item.sName = item.stationName
                        })
                        return
                    }
                    this.optionsLoc.injectionRoomOption = res.data || [];
                    this.optionsLoc.injectionRoomOption.map(item =>{
                        item.sValue = item.stationId
                        item.sName = item.stationName
                    })
                }
            }).catch(() => {
            })
        },
        // 根据机房id查找机房
        getMachineRoom(val){
            let params = {
               sMachineRoomId: val
            }
            getMachineRoomById(params).then( res=>{
                if(res.success) {
                    let data = res.data
                    this.appointmentForm['sConsultRoomId'] = data.sConsultRoomId
                    this.appointmentForm['sInjectionRoomId'] = data.sInjectionRoomId
                }
            }).catch( ()=>{
                console.log(err)
            })
        },
        useGetHospitalData, 
        useGetMachineRoomData, 
        useGetItemData
    }
}
</script>

<style lang="scss" scoped>
:global(.dialog-height90.is-fullscreen) {
    height: 90%;
    width: 80%;
    top: 5vh;
}
:global(.dialog-height90 .el-dialog__body) {
    height: calc(100% - 118px);
}
.c-dialog-body {
    height: 100%;
    overflow: auto;
}

.i-patientInfoGroup {
    padding: 10px 0 0;

    &:not(:last-of-type) {
        border-bottom: solid 1px #eee;
    }

    > label {
        display: block;
        margin: 5px 0;
        padding: 0 10px;
        border-left: 3px solid var(--el-color-primary-light-3);
        font-size: 16px;
    }

    > div {
        padding-bottom: 10px;
    }
    :deep(.el-input__suffix) {
        height: 0;
    }
}

:deep(.input-select-append) {
    .el-input-group__append .select-trigger, .el-input-group__append .el-input {
        height: 100%;
    }
}

:deep(.i-history-1) {
    position: relative;
    top: 2px;

    & + .i-sNameHistory {
        display: inline-block;
        height: 30px;
        width: 40px;
        position: relative;
        padding: 0px;
        top: -4px;
        line-height: 0;

        & > .el-popover__reference-wrapper {
            display: inline-block;
            width: 100%;
            height: 100%;

            .el-popover__reference {
                padding: 8px;
            }
        }
    }
}
</style>
