<template>
    <div class="mx-content" :style="{paddingTop:!stationName? 4+'px':15+'px'}">
        <div class="c-form">
            <h4 class="modulesName">
                <span v-if="stationName">{{seletedNode.stationName }}</span>
            </h4>
            <div class="pull-right">
                <el-popover
                    placement="left"
                    width="300"
                    :visible="visible"
                    @show="getWorkStationData()">
                    <div class="my-popover">
                        <div class="tip">*此操作会覆盖目标工作站原有配置</div>
                        <el-select v-model="targetWorkStationId"
                            placeholder="请选择目标工作站"
                            clearable
                            style="width: 100%"
                            >
                            <el-option v-for="(item,index) in workStationData"
                                :key="index"
                                :label="item.stationName"
                                :value="item.stationId">
                                    <span style="float: left; margin-left: 15px">{{ item.stationName}}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px; padding:0px 15px">{{ item.districtName }}</span>
                            </el-option>
                        </el-select>
                        <div class="btns">
                            <el-button plain type="primary" size="small" @click="visible = false">取消</el-button>
                            <el-button type="primary" size="small" @click="handleCopySetData">确定</el-button>
                        </div>
                    </div>
                    <template #reference>
                        <el-button-icon-fa 
                            type="primary" 
                            _icon="fa fa-copy" 
                            @click="visible = !visible"
                            >同步设置</el-button-icon-fa>
                    </template>
                </el-popover>
                <el-button-icon-fa 
                    class="add-btn"
                    icon="el-icon-plus"
                    type="primary" 
                    @click="handleAdd">新增</el-button-icon-fa>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table :data="tableData"
                    row-key="id"
                    ref="mainTable"
                    id="callSetTable"
                    size="small"
                    
                    border
                    stripe
                    height="100%"
                    style="width: 100%"
                    @row-click="handleRowClick"
                    :row-class-name="mxRowClassName"
                    >
                    <el-table-column label="序号" type="index" width="60px" align="center">
                        <template v-slot="{row, $index}">
                            <span>{{$index + 1}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="语音分类" align="center" width="130px">
                        <template v-slot="scope">
                            <div >
                                <span v-if="!scope.row.isEdit">{{ scope.row.sCaptionsTypeName }}</span>
                                <el-select v-if="scope.row.isEdit" 
                                    v-model="scope.row.iCaptionsType" 
                                    size="small">
                                    <el-option v-for="item in callCaptionsTypeOption"
                                        :key="item.captionsTypeCode" 
                                        :value="item.captionsTypeCode"
                                        :label="item.captionsTypeName">{{item.captionsTypeName}}</el-option>
                                </el-select>
                            </div>
                        </template>
                    </el-table-column>
                     <el-table-column label="弹框提示" min-width="30px">
                        <template v-slot="scope">
                            <div>
                                <span v-if="!scope.row.isEdit">{{scope.row.sTitel}}</span>
                                <el-input size="small" v-if="scope.row.isEdit" v-model="scope.row.sTitel" />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="语音内容">
                        <template v-slot="scope">
                            <div>
                                <span v-if="!scope.row.isEdit">{{scope.row.sCaptions}}</span>
                                <el-input size="small" v-if="scope.row.isEdit" v-model="scope.row.sCaptions" />
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="默认语音" width="80" align="center">
                        <template v-slot="scope">
                            <div>
                                <!-- <span v-if="!scope.row.isEdit">{{scope.row.iIsDefaulted}}</span> -->
                                <el-switch 
                                    v-model="scope.row.iIsDefaulted"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @click.stop="handleSaveSwitch(scope.row, scope.$index,'iIsDefaulted')">
                                </el-switch>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="显示" width="80" align="center">
                        <template v-slot="scope">
                                <!-- <span v-if="!scope.row.isEdit">{{scope.row.iIsEnable == 1?"显示":"隐藏"}}</span> -->
                                <el-switch 
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"
                                    @click.stop="handleSaveSwitch(scope.row, scope.$index, 'iIsEnable')">
                                </el-switch>
                            </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" width="140">
                        <template v-slot="scope">
                            <span class="">  
                                <el-button v-if="!scope.row.isEdit"
                                    link 
                                    title="编辑" 
                                    type="primary"
                                    size="small"
                                    @click="handleEditTable(scope.row, scope.$index)">
                                        编辑
                                        <template #icon>
                                            <Icon name="el-icon-edit" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                <el-button-icon-fa v-if="scope.row.isEdit"
                                    :loading="isSaveLoading"
                                    link 
                                    title="保存" 
                                    _icon="fa fa-save" 
                                    size="small"
                                    type="primary"
                                    @click="handleSaveTable(scope.row, scope.$index)">保存</el-button-icon-fa>
                                <el-divider direction="vertical"></el-divider>
                                <el-button-icon-fa v-if="scope.row.isEdit && scope.row.sId" link icon="el-icon-close" @click="scope.row.isEdit=false">
                                    <span>取消</span>
                                </el-button-icon-fa>
                                <el-button v-else link size="small" @click="delData(scope.row, scope.$index)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>

</template>
<script>
import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/common/callSet.js'
import { getWorkStationData } from '$supersetApi/projects/apricot/system/workStation.js'
// import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'

export default {
    name: 'VoiceSet',
    //mixins: [mixinTable],
    components: {},
    props: {
        seletedNode: {
            type:Object,
            default: () => ({})
        },
        stationName: {
            type: Boolean,
            default: true
        }
    },

    data () {
        return {
            loading: false,
            tableData:[],
            isEdit: true,
            reRender: true,
            copyData:{},
            currentRow: null,
            callCaptionsTypeOption: [],
            isSaveLoading: false,
            workStationData:[],
            targetWorkStationId:null,
            visible: false,
        }
    },
    watch: {
        seletedNode: {
            handler(val) {
                if(val.stationId) {
                    this.getTableData()  
                }
            },
            deep: true,
            immediate: true
        },    
    },
     
    methods: {
      setsCaptionsTypeText(val) {
            //console.log(this)
            let target = captionsTypeOption.find(item => item.sValue == val)
            return target ? target.sName : ''
   
        },
        // 编辑某一行
        handleEditTable(row, index) {
            this.tableData[index]['isEdit'] = 1;
        },

        // 同步设置
        handleCopySetData() {
            if(this.targetWorkStationId == this.seletedNode.stationId) {
                this.$message.error('不能选择同一个工作站做同步设置！')
                return
            }
            if(!this.targetWorkStationId) {
                this.$message.error('请选择同步工作站！')
                return
            }
            let params = {
                sourceStationId: this.seletedNode.stationId,
                targetStationId: this.targetWorkStationId
            }
            Api.workStationSyncSet(params).then(res =>{
                this.targetWorkStationId =  null
                this.visible = false
                if(res.success) {
                    this.$message.success(res.msg)
                    return
                }
                this.$message.error(res.msg)
            }).catch(()=>{
                this.visible = false
            })
        },
        // 新增
        handleAdd(){
            let object = {}
            object.iIsDefaulted = 1
            object.iIsEnable = 1
            object.isEdit = true
            this.tableData.push(JSON.parse(JSON.stringify(object)))
        },
        // 保存某一行
        handleSaveTable(row,  index) {
            
            if(['', undefined, null].includes(row.iCaptionsType)) {
                this.$message.warning('请选择语音分类！')
                return
            }
            if(['', undefined, null].includes(row.sCaptions)) {
                this.$message.warning('请填写语音内容！')
                return
            }
            this.isSaveLoading = true
            row.sCaptionsTypeName = this.getOptionName(row.iCaptionsType)
            if(row.sId) {
                Api.editCallCaptions(row).then(res =>{
                    this.isSaveLoading = false
                    if(res.success) {
                        this.tableData[index]['isEdit'] = 0;
                        this.$message.success(res.msg)
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(()=>{
                    this.isSaveLoading = false
                    console.log(err)
                })
                return
            }
            row.sStationId = this.seletedNode.stationId
            Api.addCallCaptions(row).then( res =>{
                this.isSaveLoading = false
                if( res.success) {
                    this.tableData[index]['isEdit'] = 0;
                    this.$message.success(res.msg)
                    this.getTableData()
                    return 
                }
                this.$message.error(res.msg)
            }).catch(()=>{
                this.isSaveLoading = false
                console.log(err)
            })
            
        },
        getOptionName(val) {
            if(this.callCaptionsTypeOption.length == 0 || ['', undefined].includes(val)) {
                return null 
            }
            let target = this.callCaptionsTypeOption.find(item => item.captionsTypeCode === val)
            return target?target.captionsTypeName : null
        },
        // 保存修改显示
        handleSaveSwitch(row, index, key) {
            if(!row.sId) {
                return
            }
            Api.editCallCaptions(row).then(res =>{
                if(res.success) {
                    this.$message.success(res.msg)
                    this.getTableData()
                    return
                }
                if(key=="iIsDefaulted") {
                    this.tableData[index]['iIsDefaulted'] = !iIsDefaulted;
                }
                if(key=="iIsEnable") {
                    this.tableData[index]['iIsEnable'] = !iIsDefaulted;
                }
                this.$message.error(res.msg)
                
            }).catch(()=>{
                if(key=="iIsDefaulted") {
                    this.tableData[index]['iIsDefaulted'] = !iIsDefaulted;
                }
                if(key=="iIsEnable") {
                    this.tableData[index]['iIsEnable'] = !iIsDefaulted;
                }
                console.log(err)
            })
        },
        // 删除
        delData(row, index) {
            var title =  row.sTitel ? row.sTitel : ''
            if(!row.sId) {
                this.tableData.pop()
                return
            }
            this.$confirm(`确定要删除【${title}】吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                Api.delCallCaptions(row).then(res =>{
                    if(res.success) {
                        this.$message.success(res.msg)
                        this.getTableData()
                        return
                    }
                    this.$message.error(res.msg)
                }).catch((err) => {
                    console.log(err)
                })
            })
            
        },
        setCurrent(row) {
            this.$refs["table"].setCurrentRow(row)
        },
        mxRowClassName({
            row,
            rowIndex
        }) {
            row.index = rowIndex;
        },
        handleRowClick(row) {
            let obj = {
                iCaptionsType:row.iCaptionsType,
                sCaptions: row.sCaptions,
                isVoice: row.isVoice,
                isEdit: 1
            }
            this.copyData = obj
        },
        // 获取播放列表
        getTableData() {
            this.loading = true
            let params={
                stationId: this.seletedNode.stationId,
            }
            Api.getCallCaptionsData(params).then( res=> {
                this.loading = false
                if(res.success) {
                    let data = res.data
                    this.tableData = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{
                this.loading = false;
                console.log(err)
                
            })
        },
        // 获取语音分类
        async getCallCaptionsType() {
            await Api.getCallCaptionsType().then( res=> {
                if(res.success) {
                    this.callCaptionsTypeOption = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{
                console.log(err)
                
            })
        },
        // 获取工作站数据
        getWorkStationData () {
            getWorkStationData({}).then((res) => {
                if (res.success) {
                    this.workStationData = res.data || [];
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
            })
        },
    },
    mounted () {    
        this.getCallCaptionsType()

    },
    created () {

    }

};
</script>
<style lang="scss" scoped>

.mx-content {
    height: 100%;
    padding-top:15px;
   .c-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 7px 0px 10px 0px;
        .modulesName {
            padding-left: 15px;
            font-size: 16px;
            height: 18px;
            margin:0;
            span {
                font-weight: bold;
            }
        }
        .pull-right {
            margin-right: 5px;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0px 5px 5px 15px;
        overflow: auto;
        height: calc(100% - 70px);
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;
            > button {
                margin-top: 13px;
            }
        }
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
    // .m-labelInput {
    //     width: 100%;
    //     margin-left: 0;
    // }
}
.el-table .cell {
    .el-button--small {
    padding: 0px 0px;
}
}
.danger {
    color: rgb(245, 108, 108)
}
.action {
    font-size: 12px;
    cursor: pointer;
    //color:#f56c6c;
}
.my-popover {
    padding: 10px 10px 20px 10px;
    .tip{
        padding: 0px 0 10px 0;
        color: rgb(202, 85, 85);
    }
    .btns {
       text-align: right; 
       margin-top: 15px;
    }
}
.add-btn {
    margin-left: 12px;
}
</style>
