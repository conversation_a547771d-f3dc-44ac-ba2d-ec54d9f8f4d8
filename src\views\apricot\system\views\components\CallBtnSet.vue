<template>
    <div class="mx-content">
        <div class="c-form">
            <h4 class="modulesName" v-if="stationName">
                <span >{{seletedNode.stationName }} </span>
            </h4>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                >
                <el-table v-loading="loading"
                    :data="tableData"
                    row-key="id"
                    ref="table"
                    id="callBtnTable"
                    size="small"
                    
                    border
                    stripe
                    height="100%"
                    style="width: 100%"
                    :row-class-name="mxRowClassName"
                    >
                    <el-table-column label="序号" type="index" width="60px" align="center">
                        <template v-slot="{row, $index}">
                            <span>{{$index + 1}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="按钮名称" align="center" width="160px">
                        <template v-slot="{row, $index}">
                            <div >
                                <span v-if="!row.isEdit">{{row.buttonName }}</span>
                                <el-select v-if="row.isEdit" 
                                    v-model="row.buttonCode" 
                                    size="small">
                                    <el-option v-for="item in callCaptionsTypeOption"
                                        :key="item.captionsTypeCode" 
                                        :value="item.captionsTypeCode"
                                        :label="item.captionsTypeName">{{item.captionsTypeName}}</el-option>
                                </el-select>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="显示" >
                        <template v-slot="{row, $index}">
                            <div>
                                <!-- // <span v-if="!scope.row.isEdit">{{scope.row.isEnable?"显示":"关闭"}}</span> -->
                                <el-switch
                                    v-model="row.isEnable"
                                    :active-value="1" 
                                    :inactive-value="0"
                                    @click.stop.native="handleSaveBtn(row, $index)">
                                </el-switch>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>

</template>
<script>
import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/common/callSet.js'
// import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'

export default {
    name: 'VoiceSet',
    //mixins: [mixinTable],
    components: {},
    props: {
        seletedNode: {
            type:Object,
            default: () => ({})
        },
        stationName: {
            type: Boolean,
            default: true
        },
        // 跨模块设置呼叫按钮才需要传入模块id，系统管理》呼叫管理不需要传入
        iModuleId: {
            type: Number,
            default: null
        }
    },

    data () {
        return {
            loading: false,
            callCaptionsTypeOption:[],
            tableData:[],
            isEdit: true,
            reRender: true,
            copyData:{},
            currentRow: null,
            isSaveLoading: false,
            // 呼叫类型
            callTypeOptions : [{
                sName: '预约',
                sValue: 'ApricotReportAppoint'
            }, {
                sName: '问诊',
                sValue: 'ApricotReportConsult'
            }, {
                sName: '注射',
                sValue: 'ApricotReportInject'
            }, {
                sName: '上机',
                sValue: 'ApricotReportMachine'
            }, {
                sName: '报告',
                sValue: 'ApricotReportReport'
            }],
            moduleId:'',
        }
    },
    watch: {
        seletedNode: {
            handler(val) {
                if(val.stationId) {
                    this.moduleId = val.stationTypeCode
                    this.getModuleCallBtn()
                }
            },
            deep: true,
            immediate: true
        },
    },
    methods: {
       // 编辑某一行
        handleEditTable(row, index) {
            this.tableData[index]['isEdit'] = 1;
        },
        // 复制
        handleCopy() {
            if(!Object.keys(this.copyData).length)  {
                this.$message.error('您未选择复制的数据！')
                return
            }
            let obj = JSON.parse(JSON.stringify(this.copyData))
            this.tableData.push(obj); 
        },
        // 新增
        handleAdd(){
            let object = {}
            object.isEdit = true
            this.tableData.push(JSON.parse(JSON.stringify(object)))
        },
        mxRowClassName({
            row,
            rowIndex
        }) {
            row.index = rowIndex;
        },
        // 保存saveModuleCallBtn
        handleSaveBtn(row, index) {
            row.stationName = this.seletedNode.stationName
            row.moduleName = this.seletedNode.stationTypeName
            Api.saveModuleCallBtn(row).then( res=>{
                if(res.success) {
                    this.$message.success(res.msg)
                    // 通知模块按钮重新设置了
                    if( !this.stationName ) {
                        this.$emit('isEditBtn', true)
                    }
                    // this.getModuleCallBtn(this.moduleId)
                    return
                }
                this.btnTableData['isEnable'] = !row.isEnable
                this.$message.error(res.msg)
            }).catch( ()=>{
                console.log(err)
            })
        },
        // getoptionName
        getOptionName(val) {
            let target =  this.callCaptionsTypeOption.find(item =>item.captionsTypeCode == val)
            return target? target.captionsTypeName : ''
        },
        // 获取呼叫按钮
        getModuleCallBtn() {
            // 跨模块设置呼叫按钮
            if(this.iModuleId) {
                this.moduleId = this.iModuleId
            }
            let params = {
                // iModuleId:'',
                moduleId: this.moduleId,
                stationId:this.seletedNode.stationId
            }
            this.loading = true
            Api.getModuleCallBtn(params).then( res=> {
                this.loading = false
                if(res.success) {
                    this.tableData = res.data? res.data:[]
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{
                this.loading = false;
                console.log(err)
                
            })
        },
         // 获取语音分类
        getCallCaptionsType() {
            Api.getCallCaptionsType().then( res=> {
                if(res.success) {
                    this.callCaptionsTypeOption = res.data
                    
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{
                console.log(err)
                
            })
        }
    },
    mounted () {   

    },
    created () {

    }

};
</script>
<style lang="scss" scoped>

.mx-content {
    height: 100%;
    padding-top:15px;
    box-sizing: border-box;
    .modulesName {
        font-size: 16px;
        height: 18px;
        margin: 0;
        padding: 14px 0px 17px 15px;
        span {
            font-weight: bold;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0px 15px 0;
        height: calc(100% - 64px);
        overflow: auto;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;
            > button {
                margin-top: 13px;
            }
        }
        .c-content {
            flex: 1;
            height: 100%;
            //height: 0px;
        }
    }
}
.danger {
    color: rgb(245, 108, 108)
}
.action {
    margin-left: 10px;
    font-size: 15px;
    cursor: pointer;
}
</style>
