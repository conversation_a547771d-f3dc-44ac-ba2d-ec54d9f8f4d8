<template>
    <div class="c-print">
        <el-tabs v-model="activeName">
            <el-tab-pane label="模板管理" name="TemplateSet">
                <component v-if="activeName == 'TemplateSet'" :is="activeName"></component>
            </el-tab-pane>
             <el-tab-pane label="打印配置" name="modulePrintConfig">
                <component v-if="activeName == 'modulePrintConfig'" :is="activeName"></component>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
    import TemplateSet from './components/TemplateSet.vue'
    import modulePrintConfig from './components/modulePrintConfig.vue'
    export default {
        name: 'printManagement',
        components: {
            TemplateSet,
            modulePrintConfig
        },
        props: {},
        data () {
            return {
                activeName: 'TemplateSet'
            }
        },
        methods: {

        }
    }
</script>
<style scoped>
.c-print{
    width: 100%;
    height: 100%;
    display: flex;
    padding: 15px;
    box-sizing: border-box;
}
.el-tabs {
    width: 100%;
}
:deep(.el-tabs .el-tabs__header) {
    margin:0
}
</style>