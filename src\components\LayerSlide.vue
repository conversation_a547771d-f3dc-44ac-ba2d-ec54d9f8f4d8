<template>
	<transition name="slide-fade">
		<div v-show="modelValue" class="slide-container" :class="{ 'fixed': fixed }" v-bind="$attrs">
			<header>
				<slot name="header">
					<h4 name="">{{ title }}</h4>
				</slot>
				<Icon name="el-icon-close" size="20" @click="close" class="mr-4 cursor-pointer">
				</Icon>
			</header>
			<section v-if="isDestroyOnClose">
				<slot></slot>
			</section>
			<!-- <footer></footer> -->
		</div>
	</transition>
</template>
<script>
export default {
	name: 'LayerSlide',
	emits: ['close', 'open', 'update:modelValue'],
	props: {
		modelValue: {
			type: Boolean,
			default: () => {
				return false;
			}
		},
		title: {
			type: String,
			default: ''
		},
		fixed: {
			type: Boolean,
			default: false
		},
		destroyOnClose: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		isDestroyOnClose() {
			if (this.destroyOnClose) {
				return this.modelValue
			}
			return true
		},
	},
	watch: {
		modelValue(later) {
			if (later) {
				this.$emit('open')
			}else {
				this.$emit('close')
			}
		}
	},
	methods: {
		close() {
			this.$emit('update:modelValue', false);
		}
	}
}
</script>
<style lang="scss" scoped>
.slide-container {
	position: absolute;
	top: 0;
	width: 100%;
	bottom: 0;
	left: 0;
	// border: solid 1px $borderColor;
	background-color: white;
	z-index: 11;
	border-right: none;
	display: flex;
	flex-direction: column;

	&.fixed {
		position: fixed;
		top: 55px;
		left: 10px;
		width: calc(100% - 20px);
		height: calc(100% - 65px);
	}

	>header {
		height: 45px;
		background-color: var(--el-color-primary-light-9);
		color: var(--theme-header-bg);
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1px solid #eee;

		h4 {
			margin: 0px;
			padding-left: 20px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.el-button--text {
			font-size: 18px;
		}
	}

	>section {
		text-align: left;
		padding: 10px;
		flex: 1;
		overflow: hidden;
	}
}

.slide-fade-enter-active,
.slide-fade-leave-active {
	transition: all .4s ease;
}

.slide-fade-enter-from {
	opacity: 0;
	transform: translateY(-30px);
}

.slide-fade-leave-to {
	opacity: 0;
	transform: translateY(-10px) !important;
}
</style>
