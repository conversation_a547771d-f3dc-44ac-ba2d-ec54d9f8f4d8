import {
  typeOf,
  deepClone
} from '$supersetUtils/function'
const mixinTable = {
  data() {
    return {
      loading: false,
      optionsLoc: {},
      tableData: [], // 表格数据
      actionState: 1, // 1 新增 2编辑 3 查看
      page: { // 分页	
        pageCurrent: 1,
        pageSize: 30,
        total: 0
      },
      mxPageSizes: [10, 20, 30, 50, 100, 200],

      sLikeStr: '',
      condition: {}, // 查询参数-调用页面可覆盖

      editLayer: { // 新增-编辑-查看
        loading: false, // 加载
        visible: false, // 是否显示弹窗
        playerText: '', // 弹窗标题文字
        form: {},
        selectedItem: {}, // 选中表格行数据
        look: false,
      },
      orders: {
        orderInfoList: [{
          iIndex: 1,
          sDirection: "",
          sOrderDbName: "",
          sOrderTable: "",
          sOrderField: "",
        }]
      },
      mxPickerOptions: {
        shortcuts: [{
          text: '最近三天',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近二周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 14);
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end])
          }
        }
        ]
      },
      rules: {},
      rights: {},
      isMixinDynamicGetTableHead: false, // 是否动态获取表头
    }
  },
  methods: {
    // 格式化一天开始时间
    mxFormateOneDayStart (time) {
        if (!time) return '';
        return moment(time).startOf('day').toDate();
    },
    // 格式化一天结束时间
    mxFormateOneDayEnd (time) {
        if (!time) return '';
        return moment(time).endOf('day').toDate();
    },
    // 时间转换为  yyyy-mm-dd HH:MM:SS 或者 yyyy-mm-dd
    mxToDate: function (t, status) {
      let dd = new Date(t);
      let y = dd.getFullYear();
      if (isNaN(y) || t == null) {
        return;
      }
      let m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
      let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
      if (status) {
        let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
        let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
        let s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
        return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
      }
      return y + '-' + m + '-' + d;
    },
    // 时间转换为  yyyy-mm-dd HH:MM
    mxFormatterDate: function (t) {
      let dd = new Date(t);
      let y = dd.getFullYear();
      if (isNaN(y) || t == null) {
        return;
      }
      let m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
      let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
      let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
      let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
      return y + '-' + m + '-' + d + ' ' + h + ':' + mm;
    },
    // 在表格 el-table-extend 中用作点击排序按钮后的回调 @sort-change="mxOnSort" （只有参数启用后端查询时才会调用）
    mxOnSort(column, prop, order, isDefaultSort = 0) {
     // console.log(column, prop, order, isDefaultSort)
      if (column.order === null) {
        // this.$set(this.orders.orderInfoList[0], 'sOrderField', !isDefaultSort ? 'dCreateDate' : null);
        this.$set(this.orders.orderInfoList[0], 'sOrderTable', undefined);
        this.$set(this.orders.orderInfoList[0], 'sOrderField', undefined);
        this.mxDoSearch();
        return
      }
      // this.$set(this.orders.orderInfoList[0], 'sOrderField', column.prop);
      let key = column.column.columnKey;
      let keyArray = key && key.split('.');
      if (key) {
        this.$set(this.orders.orderInfoList[0], 'sOrderField', keyArray.length > 1 ? keyArray[1]: keyArray[0]);
        keyArray.length > 1 && this.$set(this.orders.orderInfoList[0], 'sOrderTable', key.split('.')[0]);
      }
      if (column.order == 'ascending') {
        this.$set(this.orders.orderInfoList[0], 'sDirection', 'asc')
      } else {
        this.$set(this.orders.orderInfoList[0], 'sDirection', 'desc')
      }
      this.mxDoSearch();
    },
    // 新的结构下的排序-未经测试 2024-02-23
    // mxOnSort(column, prop, order, isDefaultSort = 0) {
    //   console.log(column, prop, order, isDefaultSort);
    //   if (column.order === null) {
    //     this.orders.orderInfoList[0]['sOrderTable'] = undefined;
    //     this.orders.orderInfoList[0]['sOrderField'] = undefined;
    //     this.mxDoSearch();
    //     return
    //   }
    //   if(column['column-key'] || column.column?.columnKey) {
    //     let key = column['column-key'] || column.column?.columnKey;
    //     let keyArray = key && key.split('.');
    //     if (key) {
    //       this.orders.orderInfoList[0]['sOrderField'] = keyArray.length > 1 ? keyArray[1]: keyArray[0];
    //       keyArray.length > 1 && (this.orders.orderInfoList[0]['sOrderTable'] = key.split('.')[0]);
    //     }
    //     if (column.order == 'ascending') {
    //       this.orders.orderInfoList[0]['sDirection'] = 'asc';
    //     } else {
    //       this.orders.orderInfoList[0]['sDirection'] = 'desc';
    //     }
    //     this.mxDoSearch();
    //     return
    //   }
    //   if(prop) {
    //     this.orders.orderInfoList[0]['sOrderField'] = prop;
    //     this.orders.orderInfoList[0]['sDirection'] = order;
    //   }
    //   this.mxDoSearch();
    // },
    // 转换 结束 开始 特殊日期格式。
    mxTransformDate(t) {
      let ddd = t;
      if (t.__proto__.constructor !== Date) {
        ddd = new Date(ddd);
      }
      let dd = new Date(ddd.getTime());
      let y = dd.getFullYear();
      let m = dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
      let d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate();
      return y + "-" + m + "-" + d;
    },
    // 判断表格头部是否显示img 头像列；  显示头像列为true，反之，为false；
    mxhandleHasImage(options) {
      if (!options) { return }
      let target = options.find(item => item.sProp === 'img');
      return target && target.iIsHide == 0 ? true : false
    },
    //查询
    mxDoSearch() {
      this.editLayer.selectedItem = {};
      this.page.pageCurrent = 1;
      this.mxGetTableList();
    },
    // 刷新
    mxDoRefresh() {
      this.mxGetTableList();
    },
    /**
     * 重置
     */
    mxOnClickReset(callback, isRefresh = true) {
      for (const key in this.condition) {
        if (this.condition.hasOwnProperty(key)) {
          this.condition[key] = ''
        }
      }
      this.sLikeStr = ''
      if (isRefresh) {
        this.mxDoSearch()
      }

      // 回调
      if (callback && toString.call(callback).slice(8, -1) === 'Function') {
        callback()
      }

    },
    // 翻页
    onSizeChange(val) {
      this.page.pageSize = val
      this.mxGetTableList();
      this.editLayer.selectedItem = {};
    },
    // 切页
    onCurrentChange(val) {
      this.page.pageCurrent = val
      this.mxGetTableList();
      this.editLayer.selectedItem = {};
    },
    // 点击行
    onClickRow(row, isCancel = false, id = 'sId') {
      this.editLayer.selectedItem = row;
      if (isCancel === true && this.editLayer.selectedItem[id] === row[id]) {
        this.$refs.mainTable.setCurrentRow()
        this.editLayer.selectedItem = {}
      } else {
        this.$refs.mainTable.setCurrentRow(row)
      }
    },
    mxRowClassName({
      row,
      rowIndex
    }) {
      if (row.index === undefined) row.index = rowIndex;
    },
    mxOnClickDel(title, callBack) {
      if (!Object.keys(this.editLayer.selectedItem).length) {
        this.$message.error('请选择某一行');
        return;
      }

      this.$confirm(title, '提示', {
        // confirmButtonClass: 'i-device-primary',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
        customClass: 'my-message-box'
      }).then(() => {
        this.setCancel(this.editLayer.selectedItem) // axios 方法，在调用页面
      }).catch((e) => {
        console.log(e)
      });
      callBack && callBack(this.editLayer.form)
    },
    /**
     * 打开弹窗
     */
    mxOpenDialog(status, title, editCallBack) {
      let formName = 'refEditLayer'
      this.editLayer.look = false
      this.editLayer.visible = false
      this.actionState = status == 2 ? 2 : 1
      if (status == 1) {
        // 新增
        this.editLayer.playerText = title

        // 清空值
        for (const key in this.editLayer.form) {
          if (this.editLayer.form.hasOwnProperty(key)) {
            switch (typeOf(this.editLayer.form[key])) {
              case 'Array':
                this.editLayer.form[key] = []
                break;
              case 'Object':
                this.editLayer.form[key] = {}
                break;
              default:
                this.editLayer.form[key] = '';
                break;
            }

          }
        }
        // 子类是否需要添加其它值
        if (this.defualtVal != undefined) {

          for (const key in this.defualtVal.editLayer) {
            if (this.defualtVal.editLayer.hasOwnProperty(key)) {
              this.editLayer.form[key] = typeOf(this.defualtVal.editLayer[key]) == 'Array' ||
                typeOf(this.defualtVal.editLayer[key]) == 'Object' ?
                deepClone(this.defualtVal.editLayer[key]) : this.defualtVal.editLayer[key]

            }
          }
        }
        for (const key in this.editLayer.form) {
          if (this.editLayer.form.hasOwnProperty(key) && this.editLayer.form[key] === '') {
            delete this.editLayer.form[key]
          }
        }
        this.editLayer.visible = true;
      } else if (status == 2) {
        // 编辑
        if (!Object.keys(this.editLayer.selectedItem).length) {
          this.$message.error('请选择某一行');
          return;
        }
        this.editLayer.playerText = title
        // this.editLayer.form = Object.assign({}, this.editLayer.selectedItem) 
        this.editLayer.form = deepClone(this.editLayer.selectedItem) // 深拷贝
        this.editLayer.visible = true;
      } else if (status == 3) {
        // 复制
        if (!Object.keys(this.editLayer.selectedItem).length) {
          this.$message.error('请选择某一行');
          return;
        }
        this.editLayer.playerText = title
        // this.editLayer.form = Object.assign({}, this.editLayer.selectedItem) 
        this.editLayer.form = deepClone(this.editLayer.selectedItem) // 深拷贝
        delete this.editLayer.form.sId;
        delete this.editLayer.form.iVersion;
        this.editLayer.visible = true;
      } else {
        this.actionState = 3 // todo ??
        // 查看
        if (!Object.keys(this.editLayer.selectedItem).length) {
          this.$message.error('请选择某一行');
          return;
        }
        this.editLayer.playerText = title
        // this.editLayer.form = Object.assign({}, this.editLayer.selectedItem) 
        this.editLayer.form = deepClone(this.editLayer.selectedItem) // 深拷贝
        this.editLayer.look = true;
        this.editLayer.visible = true;
      }
      editCallBack && editCallBack(this.editLayer.form, status) // 回调 用于不同操作
      this.$nextTick(() => {
        if (formName != undefined && this.$refs[formName] != undefined) {
          this.$refs[formName].clearValidate();
        }
      })
    },
    /**
     * 验证数据
     */
    mxDoSaveData(formName = 'refEditLayer') {
      this.editLayer.loading = true;
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          this.$message({
            message: '填写正确信息',
            type: 'warning',
            duration: 3000
          });
          this.editLayer.loading = false;
          return false;
        }
        // 验证成功
        this.saveData && this.saveData(this.editLayer.form) // axios 方法，在调用页面
      })
    },

    /**
     * 获取表格数据
     */
    mxGetTableList(callBack) {
      this.loading = true;
      let params = {
        orders: {
          orderInfoList: []
        },
        page: {
          pageCurrent: this.page.pageCurrent,
          pageSize: this.page.pageSize,
        }
      };

      if (this.sLikeStr) {
        params.sLikeStr = this.sLikeStr
      }

      if (this.orders && this.orders.orderInfoList[0].sOrderTable) {
        // if (this.orders && this.orders.orderInfoList[0]) {
        params.orders = this.orders
      }
      // 去除空格 和null
      const condition = Object.assign({}, this.condition)
      for (const key in condition) {
        if (condition.hasOwnProperty(key)) {
          const element = condition[key];
          if (typeof element === 'string') {
            condition[key] = element.trim()
          }
          else if (element === null) {
            condition[key] = ''
          }
        }
      }

      params = Object.assign({}, params, { condition });

      this.getData && this.getData(params, callBack); // axios 方法，在调用页面
      let maintableRef = this.$refs.mainTable
      let wrapper = maintableRef && maintableRef.$el.querySelector('.el-table__body-wrapper')
      if (maintableRef && wrapper) wrapper.scrollTop = 0
    },
    // 赋值选中状态，还原之前选中状态
    mxSetSelected() {
      this.$nextTick(() => {
        const mainTableRef = this.$refs.mainTable

        // if (mainTableRef) {
        //   // 有些主键是 sId,iId
        //   let idx = 0; // 通过id找到某行数据
        //   let selected = this.editLayer.selectedItem || {}
        //   let key = selected.sId || selected.iId || selected.sKey
        //   if (key != undefined) {
        //     this.tableData.find((item, index) => {
        //       if (item.sId == key || item.iId == key || item.sKey == key) {
        //         idx = index
        //       }
        //     })
        //   }
        //   // 赋值选中
        //   // console.log(this.tableData[idx])
        //   if (this.tableData[idx]) {
        //     this.editLayer.selectedItem = Object.assign({}, this.tableData[idx]);
        //     if (mainTableRef.setCurrentRow) {
        //       mainTableRef.setCurrentRow(this.tableData[idx]);
        //     } else if (mainTableRef.tableRef) {
        //       mainTableRef.tableRef.setCurrentRow(this.tableData[idx]);
        //     }

        //   }
        // }

        try {
          const target = (mainTableRef.tableRef.$refs.tableHeaderRef.store.states.data.value[0] ) // 获取排序后的表格数组
          if (target) { 
            // 选中排序后的第一条
            this.editLayer.selectedItem = target
            mainTableRef.setCurrentRow(target)
          }
        } catch (error) {
          
        }
      })
    },
  },
  mounted() {
    // 非动态请求‘表头’数据时，请求加载表格数据
    !this.isMixinDynamicGetTableHead && this.mxGetTableList(); // 获取表格数据
  },
}
export default mixinTable;
