import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'

// import Mock from 'mockjs'

// Mock.XHR.prototype.send = (() => {
//     const _send = Mock.XHR.prototype.send
//     return function () {
//         if (!this.match) {
//             this.custom.xhr.responseType = this.responseType || ''
//             this.custom.xhr.timeout = this.timeout || 0
//             this.custom.xhr.withCredentials = this.withCredentials || false
//             this.custom.xhr.onabort = this.onabort || null
//             this.custom.xhr.onerror = this.onerror || null
//             this.custom.xhr.onload = this.onload || null
//             this.custom.xhr.onloadend = this.onloadend || null
//             this.custom.xhr.onloadstart = this.onloadstart || null
//             this.custom.xhr.onprogress = this.onprogress || null
//             this.custom.xhr.onreadystatechange = this.onreadystatechange || null
//             this.custom.xhr.ontimeout = this.ontimeout || null
//         }
//         return _send.apply(this, arguments)
//     }
// })

export function getFiles(data) {
    return request({
        url: baseURL.apricot + '/attachments/find/key',
        method: 'POST',
        data
    })
}

export function attachmentsPreview(params) {
    return request({
        url: baseURL.apricot + '/attachments/preview',
        method: 'GET',
        params
    })
}

export function attachmentsEdit(data) {
    return request({
        url: baseURL.apricot + '/attachments/edit',
        method: 'POST',
        data
    })
}

export function attachmentsDel(params) {
    return request({
        url: baseURL.apricot + '/attachments/del',
        method: 'POST',
        params
    })
}

export function attachmentsDownload(params) {
    return request({
        url: baseURL.apricot + '/attachments/download',
        method: 'POST',
        params
    })
}
