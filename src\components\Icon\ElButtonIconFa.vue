<template>
  <el-button v-bind="$attrs" :loading="loading">
    <template #default>
      <slot name="default"></slot>
    </template>
    <template v-if="!loading" #loading>
      <slot name="loading"></slot>
    </template>

    <template v-if="_icon || icon" #icon> 
      <i :class="[_icon || icon]"></i>
      <!-- <i :class="[_icon, 'el-button--text']"></i> -->
      <!-- 带颜色 -->
    </template>
    <template v-else #icon>
      <slot name="icon"></slot>
    </template>
  </el-button>
</template>
<script>
export default {
  name: 'ElButtonIconFa'
}
</script>
<script setup>
defineProps({
  _icon: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: '',
  },
  loading: {
    default: false,
  },
});


</script>
<style lang="scss" scoped></style>
