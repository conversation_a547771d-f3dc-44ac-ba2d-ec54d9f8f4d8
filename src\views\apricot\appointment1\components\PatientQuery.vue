<template>
    <!-- 预约查询 -->
    <LayerSlide title="预约查询"
        destroy-on-close
        v-model="visible"
        @open="openLayer"
        fixed>
        <div class="window-container">
            <LayoutTable>
                <template v-slot:header>
                    <div class="flex flex-col">
                        <SearchList v-model="condition"
                            :list="searchListConfig"
                            storageKey="AppointmentPatientQuery"
                            :optionData="optionsLoc">
                            <template v-slot:dAppointmentTimeSt>
                                <el-date-picker v-model="condition.dAppointmentTimeSt"
                                    type="date"
                                    :picker-options="pickerOptionsAppointDayStart"
                                    @change="changeAppointTime"
                                    style="height:100%;">
                                </el-date-picker>
                            </template>
                            <template v-slot:dAppointmentTimeEd>
                                <el-date-picker v-model="condition.dAppointmentTimeEd"
                                    type="date"
                                    @change="changeAppointTime"
                                    style="height:100%;">
                                    <!-- :picker-options="pickerOptionsAppointDayEnd"> -->
                                </el-date-picker>
                            </template>
                            <template v-slot:sRecentDays>
                                <el-select v-model="condition.sRecentDays"
                                    @change="changeTimes"
                                    placeholder=""
                                    clearable>
                                    <el-option v-for="(item, index) in optionsLoc.recentDayOptions"
                                        :key="index"
                                        :label="item.sName"
                                        :value="item.keyWord">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:sSource>
                                <el-select v-model="condition.sSource"
                                    placeholder=""
                                    clearable
                                    @change="mxDoSearch">
                                    <el-option v-for="(item, index) in optionsLoc.visitTypeOptions"
                                        :key="index"
                                        :label="item.sName"
                                        :value="item.sValue">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:sApplyNO>
                                <el-input v-model="condition.sApplyNO"
                                    clearable></el-input>
                            </template>
                            <template v-slot:sOrderNO>
                                <el-input v-model="condition.sOrderNO"
                                    clearable></el-input>
                            </template>
                            <template v-slot:sInHospitalNO>
                                <el-input v-model="condition.sInHospitalNO"
                                    clearable></el-input>
                            </template>
                            <template v-slot:sName>
                                <el-input v-model="condition.sName"
                                    clearable></el-input>
                            </template>
                            <template v-slot:sNuclearNum>
                                <el-input v-model="condition.sNuclearNum"
                                    clearable></el-input>
                            </template>
                            <template v-slot:sDistrictId>
                                <el-select v-model="condition.sDistrictId"
                                    placeholder=" "
                                    clearable
                                    @change="useChangeHospital">
                                    <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                                        :key="index"
                                        :label="item.sDistrictPrefix"
                                        :value="item.sId">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:sMachineryRoomId>
                                <el-select v-model="condition.sMachineryRoomId"
                                    placeholder=" "
                                    clearable
                                    @change="useChangeMachineRoom">
                                    <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                                        :key="index"
                                        :label="item.sRoomName"
                                        :value="item.sId">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:sProjectId>
                                <el-select v-model="condition.sProjectId"
                                    placeholder=" "
                                    filterable
                                    clearable
                                    @change="mxDoSearch">
                                    <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                                        :key="index"
                                        :label="item.sItemName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sItemName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                                    </el-option>
                                </el-select>
                            </template>

                            <template v-slot:iIsRegister>
                                <el-select v-model="condition.iIsRegister"
                                    placeholder=""
                                    clearable
                                    @change="mxDoSearch">
                                    <el-option v-for="item in optionsLoc.iIsRegisterOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:iIsCancel>

                                <el-select v-model="condition.iIsCancel"
                                    placeholder=""
                                    clearable
                                    @change="mxDoSearch">
                                    <el-option v-for="item in optionsLoc.cancelOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue">
                                    </el-option>
                                </el-select>
                            </template>
                        </SearchList>
                    </div>
                </template>
                <template v-slot:action>
                    <div class="flex justify-between" style="margin: 0 -10px;">
                        <div class="flex flex-grow-0 justify-start items-center">
                            <span v-auth="'report:appoint:batchPrint'" style="margin-left: -10px;">
                                <!-- 批量打印 -->
                                <ReportPrintBtn class="float-left"
                                    :propParams="{
                                        isBatch: true,
                                        idKey: 'sId',
                                        deviceTypeIdKey: 'sRoomId',
                                        iModuleId: iModuleId,
                                    }"
                                    :multipleSelection="multipleSelection">
                                </ReportPrintBtn>
                            </span>
                            <!-- <el-button-icon-fa
                                v-if="callConfigBtnShow"
                                class="btn-margin-l float-left"
                                type="primary"
                                plain
                                icon="fa fa-call-setup"
                                @click="onOpenCallSet"
                                >呼叫设置</el-button-icon-fa> -->

                        </div>
                        <div class="flex justify-end items-center">
                            <el-button-icon-fa icon="fa fa-rotate-right"
                                @click="onClickReset">重置</el-button-icon-fa>
                            <el-button-icon-fa type="primary"
                                icon="fa fa-search"
                                @click="mxDoSearch"
                                :loading="loading">查询</el-button-icon-fa>
                        </div>
                    </div>
                </template>
                <template v-slot:content>
                    <el-table-extend v-if="appointmentQueryTable"
                        :iModuleId="iModuleId"
                        storageKey="appointment-patientQuery-table"
                        v-loading="loading"
                        :data="tableData"
                        stripe
                        ref="mainTable"
                        :row-class-name="mxRowClassName"
                        @row-dblclick="handleRowClick"
                        @sort-change="mxOnSort"
                        @selection-change="handleSelectionChange"
                        highlight-current-row
                        height="100%"
                        style="width: 100%">
                        <el-table-column fixed
                            prop="_selection"
                            type="selection"
                            align="center"
                            width="50">
                        </el-table-column>
                        <el-table-column type="index"
                            prop="_index"
                            label="序号"
                            align="center"
                            width="60">
                        </el-table-column>
                        <el-table-column v-for="item in appointmentQueryTable"
                            :show-overflow-tooltip="item.sProp !== 'img'"
                            :key="item.index"
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort ? 'custom' : false"
                            :column-key="item.sSortField ? item.sSortField : null"
                            :iIsHide="item.iIsHide">
                            <template v-slot="scope">
                                <template v-if="FlowStateEnum.includes(item.sProp)">
                                    <!-- 0:未上机;1:上机准备;2:上机中;3:延迟中;4:上机完成 -->
                                    <span v-if="item.sProp === 'iIsMachine' && scope.row[`${item.sProp}`] == 1"
                                        class="icon-blue">准备</span>
                                    <span v-else-if="item.sProp==='iIsMachine'&& scope.row[`${item.sProp}`]==3"
                                        class="icon-blue">延迟</span>
                                    <i v-else-if="scope.row[`${item.sProp}`] == 2"
                                        class="icon-blue"
                                        :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                    <i v-else-if="scope.row[`${item.sProp}`]"
                                        class="icon-green"
                                        :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                </template>
                                <template v-else-if="item.sProp.slice(0, 1) === 'd'">
                                    {{ mxFormatterDate(scope.row[`${item.sProp}`]) }}
                                </template>
                                <template v-else-if="['sNuclideSupName', 'sTracerSupName'].includes(item.sProp)">
                                    <span v-if="scope.row[item.sProp]" v-html="scope.row[item.sProp]"></span>
                                </template>
                                <template v-else-if="['fRecipeDose'].includes(item.sProp)">
                                    {{ setRecipeDose(scope.row[item.sProp], scope.row.sRecipeDoseUnit) }}
                                </template>
                                <template v-else-if="['fBloodSugar'].includes(item.sProp)">
                                    {{ setBloodSugar(scope.row[item.sProp]) }}
                                </template>
                                <template v-else-if="['iIsPregnant'].includes(item.sProp)">
                                    {{ setPregnantText(scope.row[item.sProp]) }}
                                </template>
                                <template v-else>
                                    {{ scope.row[`${item.sProp}`] }}
                                </template>
                            </template>
                        </el-table-column>
                    </el-table-extend>
                </template>
                <template v-slot:footer>
                    <el-pagination background
                        @size-change="onSizeChange"
                        @current-change="onCurrentChange"
                        :current-page="page.pageCurrent"
                        :page-sizes="mxPageSizes"
                        :pager-count="5"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next"
                        :total="page.total">
                    </el-pagination>
                </template>
            </LayoutTable>
        </div>
    </LayerSlide>
    <!-- 采集头像 -->
    <CollectAvatar :dialogVisible="d_CollectAvatar_v"
        :patientInfo="editLayer.selectedItem"
        :index="editLayer.selectedItem.index"
        @refreshImg="refreshImg"
        @closeDialog="closeCollectAvatarDialog">
    </CollectAvatar>

    <!-- 上机呼叫 呼叫-->
    <Call v-model:dialogVisible="call_DialogVisible"
        :patientInfo="editLayer.selectedItem"
        :iModule="{ iModuleId: iModuleId, iModuleName: '预约登记' }"
        @closeDialog="closeCallDialog"></Call>
    <!-- 呼叫设置-->
    <CallSet :dialogVisible="callSet_DialogVisible"
        :iModule="{ iModuleId: iModuleId, iModuleName: '预约登记' }"
        @closeDialog="onCloseCallSet"></CallSet>
</template>

<script>
// 模块辅助样式
import { useGetHospitalData, useGetMachineRoomData, useGetItemData, useChangeHospital, useChangeMachineRoom } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
// 组件
import PanelTable from '$supersetViews/components/PanelTable.vue';
import ScrollPane from '$supersetViews/components/ScrollPane.vue';
import Call from '$supersetViews/apricot/components/Call.vue'; // 上机呼叫
import CallSet from '$supersetViews/apricot/components/CallSet.vue'; // 呼叫设置

import {
    mixinAvatar,
    mixinElementConfigs,
    mixinUpdatePatient,
    mixinTableInner,
    mixinTable
} from '$supersetResource/js/projects/apricot/index.js';

// 接口、工具函数
import ConfigsItems from '../configs/configsItems.js';
import { transformDate } from '$supersetResource/js/tools';
import { deepClone } from '$supersetUtils/function';
import {
    appointmentEnum,
    recentDayOptions,
} from '$supersetResource/js/projects/apricot/enum.js';
import Api from '$supersetApi/projects/apricot/appointment/patientInfo.js';
import { getCallButtonSetOfModule } from '$supersetApi/projects/apricot/common/callSet.js'; // 获取列表呼叫按钮
import { callAction, isCalledBtns } from '$supersetApi/projects/apricot/common/call.js'; // 呼叫接口
import { defineAsyncComponent } from 'vue';

const searchListConfig = [
    { prop: 'dAppointmentTimeSt', label: '预约开始', width: '20%', iCustom: 1 },
    { prop: 'dAppointmentTimeEd', label: '预约结束', width: '20%', iCustom: 1 },
    { prop: 'sRecentDays', label: '最近天数', width: '20%', iCustom: 1 },
    { prop: 'sSource', label: '就诊类型', width: '20%', iCustom: 1 },
    { prop: 'sApplyNO', label: '申请单号', width: '20%', iCustom: 1 },
    { prop: 'sOrderNO', label: '医嘱号', width: '20%', iCustom: 1 },
    { prop: 'sInHospitalNO', label: '住院号', width: '20%', iCustom: 1 },
    { prop: 'sName', label: '姓名', width: '20%', iCustom: 1 },
    { prop: 'sNuclearNum', label: '核医学号', width: '20%', iCustom: 1 },
    { prop: 'sVisitCard', label: '就诊卡号', width: '20%', isShow: false},
    { prop: 'sMedicalRecordNO', label: '病历号', width: '20%', isShow: false},
    { prop: 'sMedicalCaseNO', label: '病案号', width: '20%', isShow: false},
    { prop: 'sImageNo', label: '影像号', width: '20%', isShow: false},
    { prop: 'sOutpatientNO', label: '门诊号', width: '20%', isShow: false},
    { prop: 'sPhysicalExamNo', label: '体检号', width: '20%', isShow: false},
    { prop: 'sVitisNo', label: '就诊号', width: '20%', isShow: false},
    { prop: 'sCardNum', label: '社保卡号', width: '20%', isShow: false},
    { prop: 'sHealthCardNO', label: '健康卡号', width: '20%', isShow: false},
    { prop: 'sDistrictId', label: '院区', width: '20%', iCustom: 1 },
    { prop: 'sMachineryRoomId', label: '机房', width: '20%', iCustom: 1 },
    { prop: 'sProjectId', label: '项目', width: '20%', iCustom: 1 },
    { prop: 'iIsRegister', label: '签到状态', width: '20%', iCustom: 1 },
    { prop: 'iIsCancel', label: '取消检查', width: '20%', iCustom: 1 },
];

export default {
    name: 'dialog_PatientQuery',
    mixins: [mixinElementConfigs, mixinAvatar, mixinUpdatePatient, mixinTableInner, mixinTable],
    emits: ['update:modelValue', 'clickRecord'],

    components: {
        PanelTable,
        ScrollPane,
        Call,
        CallSet,
        TableAvatar: defineAsyncComponent(() =>
            import('$supersetViews/apricot/components/TableAvatar.vue')
        ),
        CollectAvatar: defineAsyncComponent(() =>
            import('$supersetViews/apricot/components/CollectAvatar.vue')
        ),
        PatientInfoModifi: defineAsyncComponent(() =>
            import('$supersetViews/apricot/common/PatientInfoModifi.vue')
        ),
        ReportPrintBtn: defineAsyncComponent(() =>
            import('$supersetViews/apricot/components/ReportPrintBtn.vue')
        ), // 打印按钮
        PrintSet: defineAsyncComponent(() =>
            import('$supersetViews/apricot/components/PrintSet.vue')
        ),
    },

    props: {
        modelValue: {
            type: Boolean,
            default: false,
        },
    },
    data () {
        return {
            isMixinDynamicGetTableHead: true,
            iModuleId: 2, // 集中预约标识 ，eName: 'REGISTER'， 在mixinPrintPreview混合模块中调用
            searchListConfig,
            condition: {
                dAppointmentTimeSt: new Date(),
                dAppointmentTimeEd: new Date(),
                iIsCancel: 0,
                iIsRegister: '',
            },
            isOPen: false,
            loading: false,
            optionsLoc: {
                visitTypeOptions: appointmentEnum.visitTypeOptions,
                iIsRegisterOptions: appointmentEnum.iIsRegisterOptions,
                recentDayOptions: recentDayOptions,
                cancelOptions: [
                    { sValue: 1, sName: '是' },
                    { sValue: 0, sName: '否' }
                ]

            },
            pickerOptionsAppointDayStart: {
                // disabledDate: (time) => {
                //     if (this.condition.dAppointmentTimeEd) {
                //         return (
                //             time.getTime() >
                //             new Date(this.condition.dAppointmentTimeEd).getTime()
                //         );
                //     }
                // },
            },
            pickerOptionsAppointDayEnd: {
                // disabledDate: (time) => {
                //     if (this.condition.dAppointmentTimeSt) {
                //         return (
                //             time.getTime() <
                //             new Date(this.condition.dAppointmentTimeSt).getTime()
                //         );
                //     }
                // },
            },
            multipleSelection: [],
            isUpdateData: false,
            isFirst: true,
            call_DialogVisible: false,
            callSet_DialogVisible: false,
            callBtnArray: [], //  呼叫按钮数组
            appointmentQueryTable: ConfigsItems.patientQueryTableConfig
        };
    },
    computed: {
        visible: {
            get: function () {
                return this.modelValue;
            },
            set: function (val) {
                this.$emit('update:modelValue', val);
            },
        },
        workStation () {
            let temp = this.$store.getters['user/workStation'];
            return temp;
        },
        callConfigBtnShow () {
            if (this.workStation.stationTypeCode === this.iModuleId.toString()) {
                return true;
            } else {
                return false;
            }
        },
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    watch: {
        workStation: {
            async handler (val, oldVal) {
                if (val) {
                    // 赋值院区Id到查询条件
                    this.condition.sDistrictId = val.districtId;
                    // 清空机房、项目查询条件
                    this.condition.sMachineryRoomId = '';
                    this.condition.sProjectId = '';
                    // 获取患者表格数据
                    oldVal && this.mxDoSearch()
                    // 获取按钮类型
                    // if(oldVal && this.callConfigBtnShow)  {
                    //     this.rightClickCallBtnArr = await getCallButtonSetOfModules(this.iModuleId, val.stationId)
                    //     this.callBtnArray = deepClone(this.rightClickCallBtnArr)
                    // }
                    // 根据院区查询相应的机房数据
                    await this.useGetMachineRoomData(val.districtId);
                    if (this.optionsLoc.machineRoomArrOption.length) {
                        // 院区匹配到机房，查询检查项目
                        this.useGetItemData();
                    } else {
                        // 匹配不到机房，清空项目
                        this.optionsLoc.itemsArrOption = []
                    }
                }
            },
        },
    },
    created () {
        // this.setDefaultSearchTimeSt()
    },
    async mounted () {
        // 查询院区、与院区匹配的机房数据
        // await this.useGetHospitalData();
        // await this.useGetMachineRoomData(this.workStation.districtId);
        // // 根据机房数据长度，获取检查项目数据
        // this.optionsLoc.machineRoomArrOption.length && this.useGetItemData();
    },
    methods: {
        useGetHospitalData,
        useGetMachineRoomData,
        useGetItemData,
        useChangeHospital,
        useChangeMachineRoom,
        transformDate: transformDate,
        onClickRecord (item) {
            this.$emit('clickRecord', item)
        },
        // 设置 isUpdateData为true时，关闭窗口刷新数据；
        updateDataState () {
            this.isUpdateData = true;
            this.updateTableInfo();
        },
        // 默认两个工作日
        setDefaultSearchTimeSt () {
            let today = new Date();
            let weekDay = today.getDay();
            let oneday_milliseconds = 1000 * 60 * 60 * 24;
            if (weekDay == 1 || weekDay == 0) {
                this.condition.dAppointmentTimeSt = new Date(
                    today.getTime() - oneday_milliseconds * 3
                );
            } else if (weekDay == 6) {
                this.condition.dAppointmentTimeSt = new Date(
                    today.getTime() - oneday_milliseconds * 2
                );
            } else {
                this.condition.dAppointmentTimeSt = new Date(
                    today.getTime() - oneday_milliseconds
                );
            }
        },
        async onClickReset () {
            this.condition = this.$options.data().condition;
            this.condition.sDistrictId = this.workStation.districtId;
            this.condition.sMachineryRoomId = '';
            this.condition.sProjectId = '';
            // 根据院区查询相应的机房数据
            await this.useGetMachineRoomData(this.workStation.districtId);
            this.useGetItemData();
            this.mxDoSearch();
        },

        //  打开呼叫设置
        onOpenCallSet () {
            // openCallsetWindow()
            this.callSet_DialogVisible = true;
        },
        // 关闭呼叫设置
        async onCloseCallSet (isEditBtn) {
            this.callSet_DialogVisible = false;
            if (isEditBtn) {
                // 是否编辑过模块呼叫按钮
                await this.getCallButtonSetOfModule();
                this.getCalledBtn(this.editLayer.selectedItem.sId);
            }
        },

        // 打开呼叫
        onOpenCall () {
            this.call_DialogVisible = true;
        },
        // 关闭呼叫
        closeCallDialog () {
            this.call_DialogVisible = false;
        },
        // 列表快捷呼叫
        handleQuickCall (row, data, index) {
            if (data.buttonCode == 0) {
                this.onOpenCall();
                return;
            }
            data.loading = true;
            this.callBtnArray[index] = data;
            let jsonData = {
                stationId: data.stationId,
                sysModuleCode: data.moduleId,
                patientInfoId: row.sId,
                callBtnCode: data.buttonCode,
                captionsId: '',
            };
            callAction(jsonData)
                .then((res) => {
                    data.loading = false;
                    this.callBtnArray[index] = data;
                    if (res.success) {
                        data.isCalled = true;
                        this.$message.success(res.msg);
                        this.updateTableInfo(row.sId, row.index);
                        this.callBtnArray[index] = data;
                        return;
                    }
                    // this.getCalledBtn(patientInfoId)
                    this.$message.error(res.msg);
                })
                .catch(() => {
                    data.loading = false;
                    this.callBtnArray[index] = data;
                    cons.log(err);
                });
        },
        // 刷新表格行数
        refreshTableRowData (data) {
            this.tableData[this.editLayer.selectedItem.index] = data;
            this.$refs.mainTable.setCurrentRow(
                this.tableData[this.editLayer.selectedItem.index]
            );
        },
        // 最近天数
        changeTimes (val) {
            let target = this.optionsLoc.recentDayOptions.find(item => item.keyWord == val);
            if (target) {
                this.condition.dAppointmentTimeSt = moment().add(target.dates[0], 'days').toDate();
                this.condition.dAppointmentTimeEd = moment().add(target.dates[1], 'days').toDate();
            } else {
                this.condition.dAppointmentTimeSt = '';
                this.condition.dAppointmentTimeEd = '';
            }
            this.mxDoSearch();
        },
        changeAppointTime () {
            this.mxDoSearch();
        },
        // 多选改变事件
        handleSelectionChange (rows) {
            this.multipleSelection = rows;
        },
        // 行点击事件
        handleRowClick (row) {
            this.onClickRecord(row)
            this.onClickRow(row) // 不知道有什么用
            // this.callConfigBtnShow && this.getCalledBtn(this.editLayer.selectedItem.sId);
        },
        // 请求表格数据
        getData (p) {
            let params = deepClone(p);
            let dAppointmentTimeSt = params.condition.dAppointmentTimeSt;
            params.condition.dAppointmentTimeSt = dAppointmentTimeSt ? this.mxFormateOneDayStart(dAppointmentTimeSt) : '';
            // 当选择了结束时间，转换成23：59：59
            let dAppointmentTimeEd = params.condition.dAppointmentTimeEd;
            params.condition.dAppointmentTimeEd = dAppointmentTimeEd ? this.mxFormateOneDayEnd(dAppointmentTimeEd) : '';

            // let districtMachine = params.condition.districtMachine;
            // if (districtMachine && districtMachine.length) {
            //     params.condition.sDistrictId = districtMachine[0];
            //     params.condition.sMachineryRoomId = districtMachine[1];
            // }
            // delete params.condition.districtMachine;

            if (this.isFirst) {
                params.condition.sDistrictId = this.workStation.districtId;
                this.isFirst = false;
            }
            this.loading = true;
            this.multipleSelection = [];
            Api.getData(params)
                .then((res) => {
                    this.loading = false;
                    this.tableData = [];
                    if (res.success) {
                        this.tableData = res.data.recordList == null ? [] : res.data.recordList;
                        this.page.total = res.data.countRow;
                        // 赋选中状态

                        this.mxSetSelected();

                        // if (this.tableData.length) {
                        //     this.handleRowClick(this.tableData[0]);
                        // }

                        return;
                    }
                    this.$message.error(res.msg);
                })
                .catch((err) => {
                    this.loading = false;
                    console.log(err);
                }); // axios 方法，在调用页面
        },
        // 联级选择并失去焦点
        onCascaderChange (ref) {
            this.mxDoSearch();
            this.$refs[ref].dropDownVisible = false;
        },
        // 获取呼叫按钮
        async getCallButtonSetOfModule () {
            let params = {
                moduleId: this.iModuleId,
                stationId: this.workStation.stationId,
            };
            await getCallButtonSetOfModule(params)
                .then((res) => {
                    if (res.success) {
                        this.callBtnArray = res.data ? res.data : [];
                        this.callBtnArray.map((item) => {
                            item.loading = false;
                            item.isCalled = false;
                        });
                        return;
                    }
                    this.$message.error(res.msg);
                })
                .catch(() => {
                    console.log(err);
                });
        },
        // 获取已呼叫按钮
        getCalledBtn (patientInfoId) {
            let params = {
                patientInfoId: patientInfoId,
            };
            isCalledBtns(params)
                .then((res) => {
                    if (res.success) {
                        let arr = res.data;
                        this.callBtnArray.map((item, index) => {
                            item.isCalled = false;
                            this.callBtnArray[index] = item;
                            if (arr.includes(item.buttonCode)) {
                                item.isCalled = true;
                                this.callBtnArray[index] = item;
                            }
                        });
                        return;
                    }
                    this.$message.error(res.msg);
                })
                .catch((err) => {
                    console.log(err);
                });
        },
        // 打开弹窗时请求表格头数据
        async openLayer () {
            // this.mxGetTableList();
            if (!this.isOPen) {
                await this.useGetHospitalData();
                await this.useGetMachineRoomData(this.workStation.districtId);
                // 根据机房数据长度，获取检查项目数据
                this.optionsLoc.machineRoomArrOption.length && this.useGetItemData();
                // 赋值院区Id到查询条件对象中
                this.condition.sDistrictId = this.workStation.districtId;
                this.isOPen = true;
            }
            // 获取患者表格数据
            this.isMixinDynamicGetTableHead && this.mxGetTableList();
        },
    }
};
</script>

<style lang="scss" scoped>
$borderColor: #cdcecf;

.window-container {
    // position: fixed;
    width: 100%;
    height: 100%;
    // bottom: 0;
    // top: 44px;
    // left: 0;
    // border: solid 1px $borderColor;
    z-index: 4;
    border-right: none;
    :deep(.table-content .search) {
        margin-bottom: 1px;
    }
}

.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.4s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
    opacity: 0;
    transform: translateX(-200px);
}

// :deep(.el-drawer__open .el-drawer.ltr ) {
//     animation:ltr-drawer-in 800ms cubic-bezier(0,0,.2,1) 0s;
//     transition: all .4s ease;

// }
// :deep(.el-drawer__wrapper) {
//     height: calc(100% - 34px);
// }

:deep(.c-table-01 .c-pagination) {
    border: none;
    text-align: left;

    .el-pagination__total,
    .el-pagination__sizes {
        float: none;
    }

    .el-pagination {
        text-align: right;
    }
}

.btn-margin-l {
    margin-left: 10px;
}

.register {
    color: #3acebf;
}

.green {
    color: #2cc964;
}

.g-title {
    height: 30px;
    line-height: 30px;
    background-color: #eef5fb;
    border-bottom: solid 1px #eee;
}

.g-title h3 {
    margin: 0;
    padding: 0 10px;
    line-height: 30px;
    font-size: 14px;
    font-weight: bold;
}

.g-title .f-closeProcess {
    float: right;
    padding: 4px 15px 6px;
    height: 29px;
    border: none;
    border-left: 1px solid #eee;
    border-radius: 0px;
}

.el-cascader {
    line-height: inherit;
}
</style>
