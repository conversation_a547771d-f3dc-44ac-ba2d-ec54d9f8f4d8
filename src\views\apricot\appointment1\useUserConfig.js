import { ElMessage } from 'element-plus'
import { saveConfig, queryByKey } from '$supersetApi/userConfig.js';
import { useStore } from 'vuex'


// 配置数据
const configData = ref({})
const configModuleId  = ref(2)

// 获取配置
export default function useUserConfig() {
    return { configData, configModuleId }
}
// 请求用户配置
export async function useUserConfigRequest(configKey, iModuleId, defaultConfig = {}) {
    configModuleId.value = iModuleId
    const store = useStore()

    let params = {
        configKey: configKey,
        moduleId: configModuleId.value,
        userNo: store.getters['user/userSystemInfo'].sNo
    }

    const res = await queryByKey(params)
    if (!res) {
        ElMessage.error(res.msg)
    }
    if (res && res.success) {
        let data = res.data ? JSON.parse(res.data) : defaultConfig
        // 如果默认值比存储的值多，就赋值多余默认值
        if (res.data) {
            for (const key in defaultConfig) {
                if (Object.hasOwnProperty.call(defaultConfig, key)) {
                    if (data[key] === undefined) {
                        data[key] = defaultConfig[key]
                    }
                }
            }
        }


        configData.value = data
    }
}
// 保存配置
export async function useUserConfigSave(requestParams) {
    const res = await saveConfig(requestParams)

    const requestState = ref(false)
    if (!res) {
        ElMessage.error(res.msg)
    }
    if (res && res.success) {
        const obj = JSON.parse(requestParams.configValue)
        configData.value = obj
        ElMessage.success(res.msg)
        requestState.value = true
    }
}