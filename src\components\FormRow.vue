<template>
  <div class="form-row" :style="rowStyle">
    <label v-if="label || offset" :style="labelStyle" class="form-row-label">
      <span v-if="required" class="required">*</span> {{ label }}
    </label>
    <div class="form-row-slot">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "FormRow",
  props: {
    label: { type: String },
    required: { type: Boolean },
    labelWidth: { type: String, default: '80px' },
    offset: { type: String },
    align: { type: String }
  },
  data() {
    return {

    }
    
  },
  computed: {
    labelStyle() {
      if (!this.labelWidth) return {};
      return {
        width: this.labelWidth || "80px",
        
      };
    },
    rowStyle() {
        return {
            'text-align': this.align || ''
        }
    }
  },
};
</script>

<style lang="scss" scoped>
.form-row {
  display: flex;
  position: relative;
  align-items: left; 
  vertical-align: top;

  letter-spacing: normal;
  word-spacing: normal;
  text-align: left;
  text-rendering: auto;

  margin: 5px 10px 5px 0;

  &:last-child {
    margin-right: 0;
  }

  [type="text"],
  select {
    width: 100%;
  }
  .form-row-label {
    align-self: flex-start;
    flex-shrink: 0;
    padding-right: 10px;
    line-height: 40px;
    text-align: center;

  }
  .form-row-slot {
    align-self: flex-start;
    flex: 1;
    /* 统一 form control 行高 */
    line-height: 40px;
  }
}
</style>
