<template>
    <el-dialog v-model="visible"
        @close="close"
        title="修改密码"
        draggable
        width="560px"
        :close-on-click-modal="false"
        append-to-body
        :destroy-on-close="true"
        :show-close="!isRobustUserPwd && isRobustPwdConfig ? false : true"
        class="my-dialog  ">

        <div class=" w-20 h-8"></div>
        <el-form ref="refForm"
            :model="form"
            :rules="rules">
            <el-form-item prop="oldPassword"
                label="旧密码"
                labelWidth="90px">
                <el-input placeholder="请输入密码"
                    v-model="form.oldPassword"
                    type="password"
                    show-password></el-input>
            </el-form-item>
            <el-form-item prop="password"
                label="新密码"
                labelWidth="90px">
                <el-input placeholder="请输入密码"
                    v-model="form.password"
                    type="password"
                    show-password></el-input>
            </el-form-item>
            <el-form-item prop="password2"
                label="确认密码"
                labelWidth="90px">
                <el-input placeholder="请输入密码"
                    v-model="form.password2"
                    type="password"
                    show-password
                    @keyup.enter="submit"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="p-3 text-right">
                <el-button @click="submit"
                    :loading="loading"
                    type="primary">
                    <template #icon>
                        <Icon name="el-icon-check" />
                    </template>
                    保 存
                </el-button>
                <el-button v-if="isRobustUserPwd || !isRobustPwdConfig"
                    @click="close">
                    <template #icon>
                        <Icon name="el-icon-close" />
                    </template>
                    关 闭
                </el-button>
            </div>
        </template>

    </el-dialog>
</template>
<script>
// import { computed, ref, watch, onMounted } from 'vue';
import { useStore } from 'vuex';

import userApi from '@/api/auth/user'
import { getPwdRobustCheckParam as getPwdRobustCheckParamApi } from '$supersetApi/user'
import { ElMessage } from 'element-plus';
export default {

    setup () {
        const store = useStore();
        const isChangePwd = computed(() => {
            return store.getters['setting/isChangePwd']
        })
        const isRobustPwdConfig = ref(false);

        const visible = ref(false);

        const loading = ref(false);

        watch(isChangePwd, () => {
            visible.value = isChangePwd.value
        })

        const isRobustUserPwd = computed(() => {
            // 是否强密码用户 true=强密码用户；反之；
            return store.getters['user/isRobustUserPwd'] || false;
        })

        watch(isRobustPwdConfig, () => {
            if (!isRobustUserPwd.value && isRobustPwdConfig.value) {
                // 当需要校验密码并且为弱密码用户时，弹出密码框强制用户修改密码
                visible.value = true;
            }
        });


        const form = ref({
            oldPassword: "",
            password: "",
            password2: "",
        })

        const refForm = ref(null)

        const close = () => {
            store.dispatch('setting/setChangePwd', false)
            // visible.value = false;
        }


        const submit = () => {
            refForm.value.validate((valid) => {
                if (!valid) {
                    return false;
                }
                loading.value = true;
                userApi.changePassword({
                    newPassword: form.value.password,
                    oldPassword: form.value.oldPassword,
                }).then((res) => {
                    loading.value = false;
                    ElMessage.success(res.msg);
                    close();
                    store.commit({
                        type: 'user/setIsRobustUserPwd',
                        isRobustUserPwd: false
                    })
                }).catch(() => {
                    loading.value = false;
                })
            })

        }
        // 读取是否需要校验密码强弱，true=需要；反之；
        const getPwdRobustCheckParam = () => {
            isRobustPwdConfig.value = false
            getPwdRobustCheckParamApi().then(res => {
                if (res.success) {
                    isRobustPwdConfig.value = res.data || false;
                    return;
                }
                ElMessage.success(res.msg);
            })
        }


        onMounted(() => {
            getPwdRobustCheckParam()
        })

        var validatePass = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请输入密码"));
            }
            else {
                if (form.value.password2 !== "") {
                    refForm.value.validateField("password2");
                    callback();
                }
                if (!isRobustPwdConfig.value) {
                    callback();
                }
                const regex = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}$/;
                if (isRobustPwdConfig.value && regex.test(value)) {
                    callback();
                }
                callback(new Error('最少8位，必须包含大小写字母和数字'))
            }

        };
        var validatePass2 = (rule, value, callback) => {
            if (value === "") {
                callback(new Error("请再次输入密码"));
            } else if (value !== form.value.password) {
                callback(new Error("两次输入密码不一致!"));
            } else {
                callback();
            }
        };

        return {
            visible,
            loading,
            submit,
            close,
            form,
            refForm,
            isRobustPwdConfig,
            isRobustUserPwd,
            rules: ref({
                oldPassword: [
                    { required: true, message: "请输入旧密码", trigger: "blur" },
                    {
                        min: 6,
                        max: 15,
                        message: "长度在 6 到 15 个字符",
                        trigger: "blur",
                    },
                ],
                password: [
                    { required: true, message: "请输入密码", trigger: "blur" },
                    {
                        min: 6,
                        max: 15,
                        message: "长度在 6 到 15 个字符",
                        trigger: "blur",
                    },
                    { validator: validatePass, trigger: "blur" },
                ],
                password2: [
                    { required: true, message: "请再次输入密码", trigger: "blur" },
                    {
                        min: 6,
                        max: 15,
                        message: "长度在 6 到 15 个字符",
                        trigger: "blur",
                    },
                    { validator: validatePass2, trigger: "blur" },
                ],
            },)
        };
    }
};
</script>
