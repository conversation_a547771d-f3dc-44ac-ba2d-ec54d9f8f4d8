<template>
    <div class="text-right">
        <el-button link @click="onShowClick" style="top:-8px;">
            <el-icon class="setting-icon">
                <MoreFilled />
            </el-icon>
        </el-button>
    </div>

    <el-dialog title="操作设置" v-model="dialogVisible" :close-on-click-modal="false" append-to-body class="t-default my-dialog" width="600" @close="upDateParentData">
        <div style="max-height: 60vh; overflow: auto;">
            <el-table class="pop-table" :data="form" row-key="name" border ref="popTableRef" height="450">
                <el-table-column prop="label" :label="('排序')" align="center" width="90">
                    <el-icon class="drag-icon" :size="18">
                        <i class="el-icon-rank"></i>
                    </el-icon>
                </el-table-column>
                <el-table-column prop="label" :label="('显示')" align="center" width="90">
                    <template #default="scope">
                        <el-checkbox v-model="scope.row.isFold" @change="onSaveClick(0)"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="label" :label="('启用')" align="center" width="90">
                    <template #default="scope">
                        <el-checkbox v-model="scope.row.isShow" :disabled="(!!scope.row.key || false)" @change="onSaveClick(0)"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column prop="icon" :label="('操作名称')" min-width="120" align="left">
                    <template #default="scope">
                        <!-- <i :class="'fa ' + scope.row.icon"></i> -->
                        <span style="padding-left: 5px;"> {{ scope.row.name }} </span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" plain @click="onSaveClick(1)">保存至全局</el-button>
                <el-button type="primary" plain @click="onResetClick">恢复默认设置</el-button>
                <!-- <el-button-icon-fa icon="fa fa-save" type="primary" :disabled="loading" @click="onSaveClick(0)">保存</el-button-icon-fa> -->
                <el-button-icon-fa icon="el-icon-close" @click="handleCloseDialog">关闭</el-button-icon-fa>
            </span>
        </template>
    </el-dialog>

</template>

<script>
import { MoreFilled } from '@element-plus/icons-vue'

import Sortable from 'sortablejs'
import { deepClone } from '$supersetUtils/function'
import configs from '$supersetViews/apricot/case/report/configs/index.js'
import { useUserConfigSaveConfig } from '$supersetResource/js/projects/apricot/useUserConfig.js'

export default {
    components: {
        MoreFilled
    },
    emits: ['updateData'],
    props: ['iModuleId', 'formData', 'configKey'],
    data () {
        return {
            form: [],
            userInfo: {},
            loading: false,
            dialogVisible: false,
            hasChange: false
        }
    },
    watch: {
        dialogVisible (val) {
            if (val) {
                this.hasChange = false;
                this.form = deepClone(this.formData);
                this.form.map(item => {
                    item.isFold = !item.isFold;
                    item.isShow = !item.iIsHide;
                })
                this.userInfo = this.$store.getters['user/userSystemInfo'];
                this.$nextTick(() => {
                    this.rowDrop()
                })
            }
        },
    },
    methods: {
        onShowClick () {
            this.dialogVisible = true;
        },
        handleCloseDialog () {
            this.dialogVisible = false;
        },
        // 保存配置数
        useUserConfigSaveConfig: useUserConfigSaveConfig(),
        // 更新父组件数据
        upDateParentData () {
            if (!this.hasChange) return
            let form = deepClone(this.form)
            form.map(item => {
                item.isFold = !item.isFold;
                item.iIsHide = !item.isShow ?? false;
            })
            this.$emit('updateData', form);
        },
        //行拖拽
        rowDrop () {
            const popTable = (this.$refs.popTableRef.$el)
            const tbody = popTable.querySelector('tbody')
            Sortable.create(tbody, {
                handle: ".drag-icon",
                animation: 150, // 拖拽延时，效果更好看
                onEnd: (evt) => {
                    const { oldIndex, newIndex } = evt;
                    this.form.splice(newIndex, 0, ...this.form.splice(oldIndex, 1));
                    this.onSaveClick(0)
                }
            })
        },
        async onResetClick () {
            let form = deepClone(configs.businessMenus);
            let params = {
                configKey: this.configKey,
                configValue: JSON.stringify(form),
                configType: 0,
                moduleId: this.iModuleId,
                userNo: this.userInfo.sNo
            }
            await this.useUserConfigSaveConfig(params, this, () => {
                this.form = [...form];
                this.form.map(item => {
                    item.isFold = !item.isFold;
                    item.isShow = !item.iIsHide ?? false;
                })
            });
            this.hasChange = true;
        },
        async onSaveClick (configType) {
            let form = deepClone(this.form)
            form.map(item => {
                item.isFold = !item.isFold
            })
            let params = {
                configKey: this.configKey,
                configValue: JSON.stringify(form),
                configType: configType,
                moduleId: this.iModuleId,
                userNo: this.userInfo.sNo
            }
            await this.useUserConfigSaveConfig(params, this);
            this.hasChange = true;
        },
    }
}

</script>

<style lang="scss" scoped>

</style>
