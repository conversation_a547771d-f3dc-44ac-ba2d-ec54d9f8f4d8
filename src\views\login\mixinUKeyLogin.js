
import XTXSAB from './XTXSAB.js'

import { guangXiQuRenMinUKeyInit, guangXiQuRenMinUKeyLogin } from '$supersetApi/user'
import { setLoginCertId } from '$supersetUtils/auth'

const mixinUKeyLogin = {
    data () {
        return {
            hasUKeyLogin: '0', // 是否配置UKey登录
            uKeyForm: {
                userCertId: undefined,
                userPwd: undefined
            },
            uKeyUsersList: [], // UKEY用户
            uKeyServerCertInfo: {}, // broken服务端UKEY信息
        }
    },
    methods: {
        // 初始化
        mxInitUKeyMode () {
            let isNeedInitUKeyXTXSAB = this.$store.getters['user/isNeedInitUKeyXTXSAB']
            // console.log(isNeedInitUKeyXTXSAB)
            isNeedInitUKeyXTXSAB && XTXSAB.initXTXSAB();
            this.getUKeyUserList();
            this.getGuangXiQuRenMinUKeyInit();
            this.setIsNeedInitUKeyXTXSAB(false);
        },
        // 缓存
        setIsNeedInitUKeyXTXSAB (bool) {
            this.$store.commit({
                type: 'user/setIsNeedInitUKeyXTXSAB',
                isNeedInitUKeyXTXSAB: bool
            })
        },
        // 获取UKey用户列表
        getUKeyUserList () {
            this.uKeyUsersList = [];
            this.uKeyForm.userCertId = '';
            XTXSAB.GetUserList((cb) => {
                // console.log(cb)
                if (cb.retVal) {
                    let arr = cb.retVal.split("&&&").filter(item => item);
                    arr.map(item => {
                        let pieces = item.split('||');
                        let obj = {
                            label: pieces[0],
                            value: pieces[1]
                        }
                        this.uKeyUsersList.push(obj);
                    })
                    if (this.uKeyUsersList.length) {
                        this.uKeyForm.userCertId = this.uKeyUsersList[0].value;
                    }
                }
            });
        },
        // 获取远程服务器上的证书信息
        getGuangXiQuRenMinUKeyInit () {
            guangXiQuRenMinUKeyInit().then(res => {
                // console.log(res);
                if (res.success) {
                    this.uKeyServerCertInfo = res.data || {};
                    return
                }
                this.$message.error(res.msg)
            });
        },
        // 点击UKE登录按钮
        onClickUKeyLogin () {
            let uKeyServerCertInfo = this.uKeyServerCertInfo
            if (!Object.keys(uKeyServerCertInfo).length) {
                this.$message.error("没有服务端证书信息！");
                return
            }
            let certId = this.uKeyForm.userCertId;
            if (!this.uKeyUsersList.length && !certId) {
                this.$message.error("请检查UKey是否被拔出！");
                return
            }
            let password = this.uKeyForm.userPwd
            if (!password) {
                this.$message.error("请输入证书密码！");
                return
            }
            // 检查证书密码是否正确
            XTXSAB.VerifyUserPIN(certId, password, (res1) => {
                // console.log('VerifyUserPIN=', res1)
                if (!res1.retVal) {
                    // 检查密码输入错误次数，是否还允许登录
                    this.checkAllowLogin(certId);
                    return
                }
                // 密码正确，执行一下步骤
                // 获取用户证书
                XTXSAB.GetSignCert(certId, (res2) => {
                    if (!res2.retVal) {
                        this.$message.error("验证服务端签名失败！");
                        return false;
                    }
                    // 用户证书
                    var cert = res2.retVal;
                    // 验证服务端签名
                    XTXSAB.VerifySignedData(uKeyServerCertInfo.serverCert, uKeyServerCertInfo.random, uKeyServerCertInfo.signedData, (res3) => {
                        if (!res3.retVal) {
                            this.$message.error("验证服务端签名失败！");
                            return false;
                        }
                        // console.log('VerifySignedData', res3)
                        // 客户端对服务器随机数签名
                        XTXSAB.SignedData(certId, uKeyServerCertInfo.random, (res4) => {
                            if (res4.retVal == "") {
                                this.$message.error("签名失败!");
                                return false;
                            }

                            // 用户证书信息 
                            var userCertInfo = {
                                'userCertId': certId,
                                'userCert': cert,
                                'userSignedData': res4.retVal,
                                'serverRandom': uKeyServerCertInfo.random
                            };
                            // 登录系统
                            this.sendUKeyLogin(userCertInfo);
                        });
                    });
                })
            })
        },
        checkAllowLogin (certId) {
            XTXSAB.GetUserPINRetryCount(certId, (res) => {
                // console.log('GetUserPINRetryCount=', res)
                // 密码输入错误剩余的重试次数
                var retryCount = Number(res.retVal);
                if (retryCount > 0) {
                    this.$message.error("校验证书密码失败!您还有" + retryCount + "次机会重试!");
                    return;
                } else if (retryCount == 0) {
                    this.$message.error("您的证书密码已被锁死,请联系管理员进行解锁!");
                    return;
                } else {
                    this.$message.error("登录失败!");
                    return;
                }
            });
        },
        // 请求登录接口   备注：当UKeyDialog组件调用mixinUKeyLogin混入时，重写了sendUKeyLogin方法
        sendUKeyLogin (userCertInfo) {
            let loading = this.loginLoading()
            guangXiQuRenMinUKeyLogin(userCertInfo).then(res => {
                if (res.success) {
                    this.$message.success('登录成功');
                    // console.log('broken-登录', res);
                    setLoginCertId(userCertInfo.userCertId);
                    this.$store.commit({
                        type: 'user/setAccessToken',
                        accessToken: res.data.sessionId,
                        expires: new Date(Number(res.data.expireTime))
                    }) 
                    this.$router.push({ path: '/main/welcome_jump', query: { redirect: 'auto' } })
                    return
                }
                this.$message.error(res.msg)
            }).catch((err) => {
                console.log(err)
            }).finally(() => {
                loading.close()
            })
        },
        loginLoading () {
            /*登录loading*/
            return this.$loading({
                lock: true,
                text: '登录请求已提交，请等待......',
                background: 'rgba(0, 0, 0, 0.35)'
            });
        },
    },
    mounted () { }
}
export default mixinUKeyLogin