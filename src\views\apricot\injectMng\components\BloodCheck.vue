<template>
    <!-- 血糖测量 -->
    <el-dialog :close-on-click-modal="false"
        append-to-body
        :title="popTitle"
        v-model="visible"
        :destroy-on-close="true"
        @close="closeDialog"
        :show="show"
        width="700px"
        class="t-default">
        <template #header><div 
            class="header-title el-dialog__title">
            <span class="title-text">血糖测量：</span>
            <span v-if="initPatientInfo.sName">{{initPatientInfo.sName}}</span>
            <span v-if="initPatientInfo.sSexText">{{initPatientInfo.sSexText}}</span>
            <span v-if="initPatientInfo.sAge">{{initPatientInfo.sAge}}</span>
        </div></template>
        <div class="c-look-content c-inner-content scope-input-look"
            v-loading="loading1">
            <div class="c-item-01">
                <div class="c-input-search">
                    <el-form ref="refEditLayer"
                        :model="form"
                        label-width="230">
                        <template v-for="(item, index) in inputStyle">
                            <el-form-item :label="item.sLabel">
                                <el-input type="number" v-model="form[item.sProp]" clearable />
                            </el-form-item>
                        </template>
                        
                        <!-- <template v-for="(item, index) in inputStyle">
                            <form-style
                                :configData="item"
                                :formData="form"
                                :optionData="optionsLoc">
                            </form-style>
                        </template> -->
                    </el-form>
                </div>
            </div>
        </div>
        
        <template #footer><div 
            class="my-dialog-footer">
                <el-button-icon-fa _icon="fa fa-save"
                    type="primary"
                    :loading="loading"
                    @click="handleSubmitClick">保 存</el-button-icon-fa>
                    
                <el-button-icon-fa _icon="fa fa-close-1"
                    @click="visible = false">关 闭</el-button-icon-fa>

                <div class="g-page-footer">
            </div>
        </div></template>
    </el-dialog>
</template>
<script>
import FormStyle from '$supersetViews/components/FormStyle.vue'

import Configs from '../config/bloodCheck.js'

// 接口
import Api from '$supersetApi/projects/apricot/case/inject.js'
import { getInjectionRow } from '$supersetApi/projects/apricot/appointment/patientInfo.js'

export default {
    name: 'BloodCheck',
    components: {
        FormStyle
    },
    props: {
        popTitle: {
            type: String,
            default: '血糖测量'
        },
        show: {
            type: Boolean,
            default: false
        },
        // patientInfo: {
        //     type: Object,
        //     default: {},
        // }
    },
    inject:{
        initPatientInfo: {
            from: 'patientInfo',
            default: {}
        },
        getInitInJectData: {
            from:'getInitInJectData'
        }
    },
    watch: {
        show () {
            this.visible = this.show;
            if (this.visible) {
                this.initData()
            }
        },
        initPatientInfo: {
            handler(val) {
                if (!Object.keys(val).length) {
                    this.sPatientId = null
                    return
                }
                if(this.sPatientId == val.sId){
                    return
                }
                this.sPatientId = val.sId;
            },
            //immediate: true,
            deep: true
        }
    },
    data () {
        return {
            optionsLoc: {
                codeBloodType: []
            },
            visible: this.show,
            inputStyle: Configs.inputStyle,
            form: {},
            loading: false,
            loading1: false,
            sPatientId: null
        }
    },
    methods: {
        initData () {
            let val = this.sPatientId;
            this.loading1 = true;
            getInjectionRow({ sId: val }).then(res => {
                this.loading1 = false;
                if (res.success) {
                    let data = res.data || {};
                    this.form['fBloodSugar'] = data.fBloodSugar;
                    this.form['fBloodSugarHis'] = data.fBloodSugarHis;
                    this.form['fOneHours'] = data.fOneHours;
                    this.form['fTwoHours'] = data.fTwoHours;
                    this.form['fThreeHours'] = data.fThreeHours;
                    return;
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading1 = false;
            })


        },
        closeDialog () {
            this.$emit('update:show', false);
        },
        handleSubmitClick () {
            this.loading = true;
            this.form.sPatientId = this.sPatientId;
            Api.saveGlucoseMeasure(this.form).then(res => {
                this.loading = false;
                if (res.success) {
                    this.$message.success(res.msg);
                    this.getInitInJectData(this.sPatientId)
                    // this.$emit('hasChangeBlood');
                    this.closeDialog();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
                this.loading = false;
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.my-dialog-footer {
    height: auto;
    line-height: normal;
  
}

.header-title {
    > span:not(:first-child) {
        margin-right: 15px;
    }
}

.c-input-search {
    padding: 15px;
    padding-right: 100px;
    align-items: center;
    justify-content: center;
}
</style>
