<template>
    <div class="board-column">
        <div class="board-column-header">
            <slot></slot>
            {{ headerText }}
        </div>
        <draggable :list="computedList" 
            :set-data="setData" 
            class="board-column-content" 
            item-key="iIndex" 
            group="group">
            <template #item="{ element }">
                <div class="board-item">
                    {{ element.sCaption }}
                </div>
            </template>
        </draggable>
    </div>
</template>

<script>
import draggable from 'vuedraggable'

export default {
    name: 'DragSetTemplate',
    components: {
        draggable
    },
    props: {
        headerText: {
            type: String,
            default: 'Header'
        },
        list: {
            type: Array,
            default () {
                return []
            }
        }
    },
    computed: {
        computedList: {
            get () {
                return this.list;
            },
            set (val) {
                this.$emit('update:list', val);
            }
        }
    },
    data () {
        return {
            options: {
                animation: 150,
            }
        }
    },
    methods: {
        setData (dataTransfer) {
            dataTransfer.setData('Text', '')
        }
    }
}
</script>
<style lang="scss" scoped>
.board-column {
    min-width: 300px;
    min-height: 100px;
    height: auto;
    overflow: hidden;
    background: #f0f0f0;
    border-radius: 3px;

    .board-column-header {
        position: relative;
        height: 40px;
        line-height: 40px;
        overflow: hidden;
        padding: 0 20px;
        text-align: center;
        background: #333;
        color: #fff;
        border-radius: 3px 3px 0 0;
    }

    .board-column-content {
        height: auto;
        overflow-y: auto;
        border: 10px solid transparent;
        min-height: 300px;
        height: 330px;
        // display: flex;
        // justify-content: flex-start;
        // flex-direction: row;
        // align-items: center;

        .board-item {
            cursor: pointer;
            min-width: 100px;
            width: 46%;
            float: left;
            height: 45px;
            margin: 5px 5px;
            background-color: #fff;
            text-align: left;
            line-height: 36px;
            padding: 5px 10px;
            box-sizing: border-box;
            box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.2);
            border: 1px solid #afd8d3;
        }
    }
}
</style>

