<template>
  <el-dialog v-model="visible" title="主题风格" width="500px" append-to-body draggable>
    <ul class="list-theme">
      <li v-for="item in list" :key="item.id" @click="onClickSkin(item)" :class="{ active: item.id === current }"
        :style="{ 'background': item.cssVar['--theme-bg'] }">
        <div class="list-header "
          :style="{ 'background': item.cssVar['--theme-header-bg'], color: item.cssVar['--theme-header-color'] }">
          <span> {{ item.name }}</span>
          <span>~</span>
          <span>~</span>
          <span>~</span>
        </div>
        <div class="list-body">
          <div class="content">
            <div class="search">
              <div>~~~~</div>
              <div class="btn" :style="{ 'background': item.cssVar['--el-color-primary'] }"></div>
            </div>
            <div class="table">
              <div class="table-header" :style="{ 'background': item.cssVar['--theme-table-header-bg'] }">
                <span>~</span>
                <span>~</span>
                <span>~</span>
                <span>~</span>
              </div>
              <i v-if="current === item.id" class="fa fa-check"></i>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </el-dialog>
</template>
<script>
import resetScss from '@/resource/style/element-reset.scss?inline'
export default {
  name: 'ThemeSetting',
  computed: {
    visible: {
      get: function () {
        return this.$store.getters['setting/isShowSettingDialog']
      },
      set: function (val) {
        this.$store.dispatch('setting/setSettingDrawer', val);
      }
    }
  },
  data() {
    return {
      storageKey: 'themeSkin',
      current: 'theme2',
      list: [
        {
          id: 'theme1',
          model: 'light',
          name: '叶绿',
          cssVar: {}
        },
        {
          id: 'theme2',
          model: 'light',
          name: '天蓝',
          cssVar: {}
        },
        {
          id: 'theme3',
          model: 'light',
          name: '深蓝',
          cssVar: {}
        },
        {
          id: 'theme4',
          model: 'light',
          name: '墨黑',
          cssVar: {}
        },
        // {
        //     id: 'theme4',
        //     model: 'dark',
        //     titleColor: 'rgba(246, 202, 157, 0.7)',
        //     themeColor: '#009688',
        //     themeBg: '#1d1e23',
        //     themeFontColor: 'rgba(255, 255, 255, 0.7)',
        //     currentColor: '#fff',
        //     currentBg: '#009688',
        //     name: '黑墨绿'
        // },
      ],
    }
  },
  methods: {
    getCurrentTheme() {
      const theme = this.list.find(item => item.id == this.current)
      return theme
    },
    setStorageTheme(theme) {
      return localStorage.setItem(this.storageKey, theme)
    },
    onClickSkin(theme) {
      this.current = theme.id
      this.setStorageTheme(this.current)
      this.setTheme()
    },
    setTheme() {
      const theme = this.getCurrentTheme()
      document.documentElement.setAttribute('class', `${theme.id} ${theme.model}`)
    },
    getSCSS() {
      const text = resetScss // 读取scss文件的代码并获取对应css变量
      const themeTypeList = text.match(/html\.(theme\d)\:root/g).map(s => s.slice(5, 11))

      themeTypeList.forEach(str => {
        const reg = new RegExp(`html.${str}:root\\s?` + '{([\\w\\W]+?)}')
        const theme1 = text.match(reg)
        // console.log(theme1)
        const theme1CssObj = theme1&&theme1[1] ? cssToObject(theme1[1]) : {}
        // console.log(theme1CssObj)
        const target = this.list.find(item => item.id === str)
        if (target) target.cssVar = theme1CssObj
        
      })
    }
  },
  created() {
    this.current = localStorage.getItem(this.storageKey) || this.current

    this.setTheme()
    this.getSCSS()
  },
  mounted() {

  }
}

function cssToObject(cssText) {
  // 创建空对象
  const cssObj = {};

  // // 将所有行分割为数组
  // const cssLines = cssText.split('\n');

  // // 遍历每一行，并解析属性和值
  // for (let i = 0; i < cssLines.length; i++) {
  //   const line = cssLines[i].trim();

  //   if (line.startsWith('//') || line === '') {
  //     // 忽略注释和空行
  //     continue;
  //   }

  //   const match = line.match(/(.+?):\s*(.+?);/);
  //   if (match) {
  //     // 如果该行包含有效的属性和值，则将其添加到对象中
  //     const prop = match[1];
  //     const value = match[2];
  //     cssObj[prop] = value;
  //   }
  // }

  const matchs = cssText.match(/(.+?):\s*(.+?);/g);

    if (matchs && matchs[0]) {
      // 如果该行包含有效的属性和值，则将其添加到对象中
      matchs.forEach(str => {
        const match = str.match(/(.+?):\s*(.+?);/)
        const prop = String(match[1]).trim();
        const value = match[2];
        cssObj[prop] = value;
      })
    }

  // 返回对象
  return cssObj;
}
</script>
<style lang="scss" scoped>
.list-theme {
  padding: 40px 20px 20px;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  list-style-type: none;

  li {
    display: flex;
    flex-direction: column;
    width: 200px;
    height: 120px;
    margin-bottom: 20px;
    border: 1px solid #eee;
    font-size: 12px;
    cursor: pointer;
    &.active {
      filter: brightness(0.9);
    }
    .list-header {
      width: 100%;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      color: var(--theme-header-color);
    }

    .list-body {
      flex: 1;
      overflow: hidden;
      margin: 6px 10px;

      .content {
        display: flex;
        flex-direction: column;
        height: 100%;
        border: 1px solid #eee;
        background: #fff;

        .search {
          height: 20px;
          padding: 5px;
          width: 100%;

          >div {
            float: left;
          }

          .btn {
            width: 24px;
            height: 10px;
            float: right;
          }
        }

        .table {
          flex: 1;

          .table-header {
            height: 16px;
            width: 100%;
            display: flex;
            justify-content: space-around;
            border: 1px solid #eee;
            border-left: none;
            border-right: none;
          }

          i {
            font-size: 18px;
            text-align: center;
            font-style: normal;
            width: 100%;
            padding-top: 10px;
            display: block;
            font-weight: bold;
            color: #21df43;
          }
        }
      }
    }
  }
}
</style>
