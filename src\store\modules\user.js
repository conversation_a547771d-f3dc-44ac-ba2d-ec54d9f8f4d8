import authUserApi from '@/api/auth/user';

import { getAccessToken, removeAccessToken, setAccessToken } from '@/utils/accessToken';

import { cloneDeep } from 'lodash-es';

const state = {
  accessToken: getAccessToken(),
  nickname: '',
  expireTime: '',
  gender: 1,
  roleList: [],
  roles: '',
  'docLevelId': '',
  'userTypeId': '',
  state: 1,
  userId: '',
  userName: '',
  userNo: '',
  userData: {},
  userRight: [],
  userSystemInfo: {},
  isInited: false,
  validRouterName: '',
  codeTable: {},
  elementConfigData: {},
  heartbeat: {
    handle: null,
    delay: 1000 * 140
  },
  workStation: localStorage.getItem('workStation') ? JSON.parse(localStorage.getItem('workStation')) : {},
  signatureAppName: '',
  menuRight: [],       // 目录权限
  buttonRight: [],      // 按钮权限
  buttonRightMap: {},
  personalOnlineStorage: {
    // [moduleId]: { [storageKeyName]: Object }
    // 1: { ReportCaseIndexReportSetting: { ...} }
  },
  isRobustUserPwd: 1,  // 是否强(复杂)密码 1= 强， 0=弱；
  isNeedInitUKeyXTXSAB: true,  // 是否需要初始化UKey
};


const getters = {
  accessToken: (state) => state.accessToken,
  buttonRight: (state) => state.buttonRight,
  buttonRightMap: (state) => state.buttonRightMap,
  personalOnlineStorage: (state) => {
    return state.personalOnlineStorage
  },
  
  username(state) {
    return state.userSystemInfo.sName || ''
  },
  userId(state) {
    return state.userSystemInfo.sId || ''
  },
  userRight(state) {
    return state.userRight
  },
  userSystemInfo(state) {
    return state.userSystemInfo || {}
  },
  isInited(state) {
    return state.isInited
  },
  validRouterName(state) {
    return state.validRouterName
  },
  codeTable(state) {
    return state.codeTable
  },
  elementConfigData(state) {
    return state.elementConfigData
  },
  workStation(state) {
    return state.workStation
  },
  signatureAppName(state) {
    return state.signatureAppName || ''
  },
  isRobustUserPwd (state) {
    return state.isRobustUserPwd;
  },
  isNeedInitUKeyXTXSAB (state) {
    return state.isNeedInitUKeyXTXSAB;
  }
}

const mutations = {
  //设置用户数据
  setUserData(state, payload) {
    state.userData = payload.userData
  },
  //设置用户系统信息
  setUserSystemInfo(state, payload) {
    state.userSystemInfo = payload.userSystemInfo
  },
  //设置用户权限
  setUserRight(state, payload) {
    state.userRight = payload.userRight

    const menus = payload.userRight
    // 遍历组装
    let menuRight = []
    let buttonRight = []
    menus.forEach(menu => {
      let isMenu = false
      menu.modules?.forEach(moduleObj => {
        moduleObj.buttons?.forEach(button => {
          if (button.assign) {
            buttonRight.push(button.buttonCode)
            isMenu = true
          }
        })
      })
      if (isMenu) {
        menuRight.push(menu.menuCode)
      }
    })
    state.menuRight = menuRight
    state.buttonRight = buttonRight
    const buttonRightMap = {}
    buttonRight.forEach(key => {
      buttonRightMap[key] = true
    })
    state.buttonRightMap = buttonRightMap
  },
  setIsInited(state, payload) {
    state.isInited = payload.isInited
  },
  setValidRouterName(state, payload) {
    state.validRouterName = payload.validRouterName
  },
  // 设置码表值
  setCodeTable(state, payload) {
    state.codeTable[payload.key] = payload.value
  },
  // 设置元素配置
  setElementConfigData(state, payload) {
    // state.elementConfigData = payload.elementConfigData
    // for (const key in payload.elementConfigData) {
    // 	if (Object.hasOwnProperty.call(payload.elementConfigData, key)) {
    // 		state.elementConfigData[key] = payload.elementConfigData[key];
    // 	}
    // }
    const newObj = payload.elementConfigData || {}
    for (const key in newObj) {
      state.elementConfigData[key] = newObj[key]
    }

  },
  setSignatureAppName(state, payload) {
    state.signatureAppName = payload.signatureAppName
  },

  setAccessToken(state, { accessToken, expires }) {
    state.accessToken = accessToken;
    setAccessToken(accessToken, expires);
  },

  setPersonalOnlineStorage(state, payload) {
    state.personalOnlineStorage = payload.personalOnlineStorage
  },
  //设置密码强弱
  setIsRobustUserPwd (state, payload) {
    state.isRobustUserPwd = payload.isRobustUserPwd || 0
  },

  setIsNeedInitUKeyXTXSAB (state, payload) {
    state.isNeedInitUKeyXTXSAB = payload.isNeedInitUKeyXTXSAB
  },
}

const actions = {
  clearHeartbeat({ state }) {
    clearInterval(state.heartbeat.handle)
  },
  // 设置心跳 改为定时刷token
  setHeartbeat({ state, dispatch }) {
    clearInterval(state.heartbeat.handle)

    state.heartbeat.handle = setInterval(() => {
      // loginHeartbeat()
      // dispatch('refreshToken')
    }, state.heartbeat.delay);

  },
  setWorkStation({ state }, payload) {
    // console.log(payload.workStation);
    state.workStation = payload.workStation
  },
  login({ state, commit }, userInfo) {
    return authUserApi.login(userInfo).then(response => {
      if (response.success) {
        const { data } = response
        const accessToken = data.sessionId;
        state.expireTime = data.expireTime
        commit('setAccessToken', { accessToken });
        return Promise.resolve(response)
      } else {
        // ElMessage.error(`登录接口异常: ${response.msg} `);
        return Promise.reject(response)
      }

    }).catch(error => {
      // ElMessage.error(`登录接口异常:${error} `);
      return Promise.reject(error)
    })

  },
  async getUserInfo({ commit, dispatch, state }) {
    const { data, success } = await authUserApi.getCurrentUser();
    if (!data || !success) {
      return false;
    }
    const paramArr = [
      'gender',
      'docLevelId',
      'userTypeId',
      'roleList',
      'state',
      'userId',
      'userName',
      'userNo',
      'expireDate',
      'robust'
    ]
    const userSystemInfo = {
      sName: data.userName,
      sNo: data.userNo,
      sId: data.userId,
    }

    paramArr.forEach(k => {
      state[k] = data[k]
      userSystemInfo[k] = data[k]
    })

    state.nickname = data.userName
    state.userSystemInfo = userSystemInfo

    return { data: userSystemInfo }

  },
  async logout({ dispatch }) {
    await dispatch('resetAccessToken');
    await authUserApi.logout();
    window.location.href = window.location.origin + window.location.pathname;
  },
  resetAccessToken({ commit }) {
    // commit('setPermissions', []);
    commit('setAccessToken', '');
    removeAccessToken();
  },
  refreshToken({ state, commit }) {
    return authUserApi
      .refreshToken()
      .then((res) => {
        if (res) {
          const data = res.data
          const accessToken = data.sessionId;
          state.expireTime = data.expireTime
          commit('setAccessToken', { accessToken });
        }
        return Promise.resolve()
      })
      .catch(() => {
        return Promise.resolve()
      });
  },
  async initTokenTimer({ state, dispatch }) {
    // 等当前所有初始化请求完成再刷新token
    const initTimer = () => {
      // const expireInterval = (state.expireTime) - +new Date() - 60000
      const expireInterval = 3 * 60 * 1000
      setInterval(() => {
        dispatch('refreshToken')
      }, expireInterval);
    }
    if (!state.expireTime) {
      await dispatch('refreshToken')
    }

    initTimer()
  },
  setPersonalOnlineStorage({ state, commit }, { moduleId , configKey, configValue } ) {
    if (!moduleId || !configKey) return
    const personalOnlineStorage = cloneDeep(state.personalOnlineStorage)  
    let target = personalOnlineStorage[moduleId]
    
    if (!target) {
      personalOnlineStorage[moduleId] = {}
      target = personalOnlineStorage[moduleId]
    }  
    target[configKey] = configValue

    commit('setPersonalOnlineStorage', {personalOnlineStorage})
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
