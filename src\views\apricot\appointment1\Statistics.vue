<template>
    <div class="container-top">
        <div class="header">
            <h4>统计</h4>
            <!-- <span class="more">更多</span> -->
        </div>
        <div class="table-box">
            <el-table-extend
                v-if="tableList"
                :data="tableData"
                v-loading="tableLoading"
                :iModuleId="iModuleId"
                ref="mainTable"
                highlight-current-row
                border
                height="100%"
                storageKey="appointment1-Statistics">
                <el-table-column 
                    v-for="item in tableList"
                    :key="item.sProp"
                    :prop="item.sProp" 
                    :sortable="(!!item.iSort) ? 'custom' : defaultItem.iSort"
                    :column-key="item.sSortField ? item.sSortField : null"
                    :width="item.sWidth || defaultItem.sWidth"
                    :min-width="item.sMinWidth || defaultItem.sMinWidth"
                    :show-overflow-tooltip="item.showOverflowTooltip != undefined ? item.showOverflowTooltip : defaultItem.showOverflowTooltip"  
                    :label="item.sLabel"
                    :align="item.sAlign || defaultItem.sAlign"
                    :fixed="item.sFixed"
                    :formatter="item.formatter || defaultItem.formatter">
                        <template v-slot="scope">
                            {{ scope.row[`${item.sProp}`] }}
                        </template>
                </el-table-column>
            </el-table-extend>
        </div>
    </div>
</template>
<script setup>

    /**
     * 预约登记-右下角统计
     */
    import { ElMessage } from 'element-plus'
    import { transformDate } from '$supersetResource/js/tools'

    import { queryItemStatistics } from '$supersetApi/projects/apricot/appointment/index.js'

    import ConfigsItems from './configs/configsItems.js'

    import useUserConfig from './useUserConfig.js'
    const { configData } = useUserConfig()

    const defaultItem = { 
        iSort: false,
        sWidth: null,
        showOverflowTooltip: true,
        sAlign: 'center',
        sMinWidth: 100,
        formatter: null
    }

    const props = defineProps({
        modelValue: Object,
    })
    const emits = defineEmits(['update:modelValue'])

    const iModuleId = ref(2)
    const tableList = ref(ConfigsItems.statisticsTableConfig)
    const tableData = ref([])
    const tableLoading = ref(false)

    const modelParams = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emits('update:modelValue', val)
        }
    })

    // 获取统计数据
    function getItemStatisticsData () {
        let obj = modelParams.value;
        const endDate = configData.value.statisticsDays === 7 ? moment(obj.date).add(7, 'd').format('YYYYMMDD') : moment(obj.date).format('YYYYMMDD')
        let params = {
            startDate: moment(obj.date).format('YYYYMMDD'),
            endDate: endDate,
            roomId: obj.roomId,
            hospitalDistrictId: obj.hospitalId
        }
        if (!params.roomId) {
            ElMessage.warning('该院区下，无机房信息')
            return
        }
        tableLoading.value = true
        queryItemStatistics(params).then(res => {
            if (res.success) {
                tableData.value = res.data
                return
            }
            ElMessage.error(res.msg)
        }).finally(() => {
            tableLoading.value = false
        })
    }

    // 监听院区变化, 获取院区设备类型
    watch(() => modelParams.value, (later) => {
        if (!later.date) {
            return
        }
        getItemStatisticsData()
    }, { immediate: true, deep: true })

</script>
<style lang="scss" scoped>
.container-top {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    .header {
        position: relative;
        padding: 12px 12px 6px;
        h4 {
            font-size: 16px;
            font-weight: 400;
            margin: 0;
            border-left: 4px solid var(--el-color-primary);
            padding-left: 5px;
        }
        .more {
            font-size: 14px;
            position: absolute;
            right: 12px;
            top: 14px;
            color: var(--el-color-info);
            cursor: pointer;
            &:hover {
                opacity: 0.9;
            }
        }
    }
    .table-box {
        flex: 1;
        overflow: hidden;
        padding: 12px;
    }
}
</style>
