<template>
	<div>
		<template v-for="(menu, i) in menus" :index="menu.key || index + i">
			<el-sub-menu v-if="menu.children" >
				<template v-slot:title>
					<i v-if="menu.icon" :class="menu.icon"></i>
					<span>{{menu.title}}</span>
				</template>
				<!-- <ElMenuLevel :index="menu.key || index + i" :menus="menu.children" /> -->
			</el-sub-menu>
			<el-menu-item v-else :index="menu.key || index + i">
				<i v-if="menu.icon" :class="menu.icon"></i>
				<span>{{menu.title}}</span>
			</el-menu-item>
		</template>
	</div>
</template>

<script>
export default {
    name: 'ElMenuLevel',
    data() {
        return {}
    },
    props: {
        menus: {
            type: Array,
            default: () => {
                []
            }
		},
		index: {
			type: String
		}
    },
    created() {
        // console.log('menus:', this.menus)
    }
}
</script>

<style lang="scss">

</style>
