<template>
    <el-scrollbar class="my-scrollbar" v-if="isVisible">
        <el-empty v-if="!dataList.length" :image-size="80" description="暂无记录" style="height: 100%;" />
        <el-card
            v-else
            class="box-card"
            shadow="never"
            v-for="(item, index) in dataList"
            :key="index"
        >
            <div class="p-1 i-title">
                <span class="i-left" style="hei">{{ `第 ${index + 1} 条` }}</span>
            </div>
            <TextList
                :list="textList"
                :data="item"
                :configBtn="false"
                labelWidth="120px"
            >
                
                <template #dInjectionTime="{ row, style }">
                    <span :style="style" :title="mxFormatterTime(item[row.prop])">
                        {{ mxFormatterTime(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                
                <template #dInjectDate="{ row, style }">
                    <span :style="style" :title="mxToDate(item[row.prop])">
                        {{ mxToDate(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                
                <template #fFactDose="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + item['sFullUnitText']
                                : '（空）'
                        }}
                    </span>
                </template>
                
                <template #fRecipeDose="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + item['sRecipeDoseUnit']
                                : '（空）'
                        }}
                    </span>
                    </span>
                </template>
                <template #fBloodSugar="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + 'mmol/L'
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #sDrugDeliveryCode="{ style }">
                    <span :style="style" :title="item['sDrugDeliveryText']">
                        {{ item['sDrugDeliveryText'] || '（空）' }}
                    </span>
                </template>
                <template #sInjectionPosition="{ style }">
                    <span :style="style" :title="item['sInjectionPositionText']">
                        {{ item['sInjectionPositionText'] || '（空）' }}
                    </span>
                </template>
                <template #sNurseId="{ style }">
                    <span :style="style" :title="item['sNurseName']">
                        {{ item['sNurseName'] || '（空）' }}
                    </span>
                </template>
                <template #sMedicineSource="{ style }">
                    <span :style="style" :title="item['sMedicineSourceText']">
                        {{ item['sMedicineSourceText'] || '（空）' }}
                    </span>
                </template>
            </TextList>
        </el-card>
    </el-scrollbar>
</template>

<script setup>
    import Configs from "../config"
    import { mxToDate, mxFormatterDate, mxFormatterTime } from '$supersetViews/apricot/injectMng/config/famaterData';
    import { findInjectionListByPatientId } from '$supersetApi/projects/apricot/injectMng/inject.js';
    import { ElMessage } from 'element-plus';

    const { proxy } = getCurrentInstance();

    const patientInfo = inject('patientInfo', ref({}));

    const iModuleId = ref(5);

    const storageKeyList = ref({
        PET: 'PETInjectFormList',
        ECT: 'ECTInjectFormList',
        Others: 'OthersInjectFormList',
    });

    const eNumDeviceType = ref({
        '001': 'PET',
        '002': 'ECT',
        '003': 'PET',
    });

    // 文本组件配置
    var textList = reactive(Configs.injectTextList);

    // 设备类型编码
    var sDeviceTypeCode = computed(() => patientInfo?.value?.sDeviceTypeCode);

    // 是否显示
    var isVisible = ref(true);

    // 设备类型
    var textDeviceType = ref(null);

    var sPatientId = computed(() => patientInfo?.value?.sId);

    const dataList = ref([]);

    // 监听设备类型编码
    watch(sDeviceTypeCode, (val) => {
        textDeviceType.value = eNumDeviceType.value[val] || 'Others';
        isVisible.value = false;
        // 待处理，清除定时器
        setTimeout(() => {
            isVisible.value = true;
        }, 500);
    });
    // 监听患者ID
    watch(sPatientId, (val, oldVal) => {
        if (!val) {
            dataList.value = [];
            return
        }
        if (val && val !== oldVal) {
            getRecordData(val);
        } 
    });

    // 获取注射记录
    const getRecordData = (sPatientId) => {
        const params = {
            sPatientId,
        };
        findInjectionListByPatientId(params)
            .then((res) => {
                if (res.success) {
                    dataList.value = res?.data || [];
                    return;
                }
                ElMessage.error(res.msg);
                dataList.value = [];
            })
            .catch((err) => {
                dataList.value = [];
            });
    };

    // 暴露组件属性或方法可供父组件使用
    defineExpose({
        dataList
    })
</script>

<style scoped lang="scss">
    .el-card {
        margin-top: 15px;
        &:first-child {
            margin-top: 10px;
        }
        :deep(.container .grid-box) {
            border: none;
        }
        :deep(.el-card__body) {
            padding: 0;
        }
        .i-title {
            padding-left: 15px;
            padding-right: 15px;
            border-bottom: 1px solid #eee;
        }
        .i-left {
            line-height: 24px;
        }
    }
    :deep(.container .grid-box .cell-box) {
        align-items: center;
    }
</style>
