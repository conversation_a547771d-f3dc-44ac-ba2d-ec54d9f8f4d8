import request, {baseURL, stringify} from '$supersetUtils/request'
// 上机模块
export default {
    // 上机表格数据
    getMechineInit,
    machineScanOver,
    // 上机表格数据
    getData: getOperateComputerPage,
    // 保存上机
    saveData(data) {
        return request({
            url: baseURL.apricot + '/operate/computer/add',
            method: 'POST',
            data
        })
    },
    // 编辑上机
    editData(data) {
        return request({
            url: baseURL.apricot + '/operate/computer/edit',
            method: 'POST',
            data
        })
    },

    // 删除上机
    delData(params) {
        return request({
            url: baseURL.apricot + '/operate/computer/del',
            method: 'POST',
            params
        })
    },

    // 上机 挂起
    machineHoldon(data) {
        return request({
            url: baseURL.apricot + '/project/and/patient/machine/holdon',
            method: 'POST',
            data
        })
    },

    // 上机 取消挂起
    holdonCancel(params) {
        return request({
            url: baseURL.apricot + '/operate/computer/holdonCancel',
            method: 'POST',
            data: stringify(params)
        })
    },

    // 上机 允许离开
    machineAllowLeave(data) {
        return request({
            url: baseURL.apricot + '/project/and/patient/machine/allow/leave',
            method: 'POST',
            data
        })
    },
    // 上机 保存并离开
    operateComputerComplete(data) {
        return request({
            url: baseURL.apricot + '/operate/computer/complete',
            method: 'POST',
            data
        })
    },

}

export function getMechineInit(params) {
    return request({
        url: baseURL.apricot + '/operate/computer/find/init',
        method: 'POST',
        params
    })
}

// 开始上机
export function operateComputerStart(params) {
    return request({
        url: baseURL.apricot + '/operate/computer/start',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取上机
export function getOperateComputerPage(data) {
    return request({
        url: baseURL.apricot + '/operate/computer/find/page',
        method: 'POST',
        data
    })
}
// 标志上机完成

export function machineScanOver(params) {
    return request({
        url: baseURL.apricot + '/operate/computer/machineScanOver',
        method: 'POST',
        data: stringify(params)
    })
}