import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request' 

// 页面模块配置——查询
export function adjustModelPage(data) {
    return request({
        url: baseURL.apricot + '/adjust/model/find/page',
        method: 'POST',
        data
    })
}
// 页面模块配置——新增
export function adjustModelAdd(data) {
    return request({
        url: baseURL.apricot + '/adjust/model/add',
        method: 'POST',
        data
    })
}

// 页面模块配置——编辑
export function adjustModelEdit(data) {
    return request({
        url: baseURL.apricot + '/adjust/model/edit',
        method: 'POST',
        data
    })
}

// 页面模块配置——删除
export function adjustModelDel(params) {
    return request({
        url: baseURL.apricot + '/adjust/model/del',
        method: 'POST',
        params
    })
}

// 页面模块配置——查询(树)
export function adjustGetTree(data) {
    return request({
        url: baseURL.apricot + '/adjust/model/find/tree',
        method: 'POST',
        data
    })
}


// 页面模块元素配置——查询
export function adjustSetFindByPage(data) {
    return request({
        url: baseURL.apricot + '/adjust/set/find/page',
        method: 'POST',
        data
    })
}


// 页面模块元素配置——新增
export function adjustSetAdd(data) {
    return request({
        url: baseURL.apricot + '/adjust/set/add',
        method: 'POST',
        data
    })
}

// 页面模块元素配置——编辑
export function adjustSetEdit(data) {
    return request({
        url: baseURL.apricot + '/adjust/set/edit',
        method: 'POST',
        data
    })
}

// 页面模块元素配置——删除
export function adjustSetDel(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/del',
        method: 'POST',
        params
    })
}

// 页面模块元素配置——自动排序
export function adjustSetAutoSort(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/autoSort',
        method: 'POST',
		params
    })
}

// 页面模块元素配置——排序
export function adjustSetSort(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/sort',
        method: 'POST',
		params
    })
}

// 页面模块元素配置
export function getItemTreeData(params) {
    return request({
        url: baseURL.apricot + '/item/find/tree',
        method: 'POST',
        params
    })
}

// 显隐
export function adjustSetHide(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/hide',
        method: 'POST',
        params
    })
}

// 只读
export function adjustSetReadonly(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/readonly',
        method: 'POST',
        params
    })
}

// 必填
export function adjustSetRequired(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/required',
        method: 'POST',
        params
    })
}
// 列固定
export function  adjustSetTableFixed(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/table/fixed',
        method: 'POST',
        params
    })
}
// 列对齐
export function adjustSetTableAlign(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/table/align',
        method: 'POST',
        params
    })
}
// 列内容排序
export function adjustSetTableSort(params) {
    return request({
        url: baseURL.apricot + '/adjust/set/table/sort',
        method: 'POST',
        params
    })
}
// 批量修改表格 列宽，列背景颜色，列文字颜色
export function adjustSetBatch(data) {
    return request({
        url: baseURL.apricot + '/adjust/set/edit/batch',
        method: 'POST',
        data
    })
}
