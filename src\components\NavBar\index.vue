<template>
    <ReloginModal v-model="lockPage" />

    <div class="nav-bar-container" >
        <div v-if="isShowLogo" class="box-logo" style="margin-left: -18px;">
            <img :src="hospitalLogoUrl" alt="" style="width:auto;height: 41px;">
        </div>
        <div v-else class="box-logo" @click="moduleMenuClick('welcome_Index')">
            <span class="tt">{{ systemName }}</span>
        </div>
        <div class="tab-bar">
            <!-- 打开的tab页面 -->
            <div class="c-tabs">
                <div v-for="(tab, index) in tabList" :key="index" :class="{ 'tab-btn': 1, 's-acitve': tab.isActive }"
                    @click="moduleMenuClick(tab.routerName)">
                    <span class="tab-name">{{ tab.name }}</span>
                    <span class="c-icon" v-if="tabList.length > 1" @click.stop="closeTab(tab.routerName)">
                        <i class="fa fa-close-1"></i>
                    </span>

                </div>
            </div>
        </div>
        <div class="right-panel">
            <span v-if="resClientIp" class="ip-text" style="top:2px;">
                IP: {{ resClientIp }}
            </span>
            
            <span v-if="currentWorkStation.stationName" class="ip-text" style="top:2px;">
                <span>{{ currentWorkStation.stationName }}</span>
            </span>

            <!-- 工作站选取 -->
            <el-popover ref="workStationRef" :width="620" placement="bottom-end" trigger="hover"
                v-model:visible="isShowWorkStation" @show="getHospitalData">
                <div v-loading="stationLoad">
                    <div class="c-district">
                        <div class="c-item" v-for="(item, index) in districtData" :key="index"
                            :class="{ 'c-active': currentDistrict.sId === item.sId }" @click="handleChangeDistrict(item)">
                            {{ item.sDistrictPrefix }}</div>
                    </div>
                    <div class="c-station">
                        <p class="c-item" v-for="(item, index) in workStationData" :key="index"
                            :class="{ 'c-active': currentWorkStation.stationId === item.stationId }"
                            @click="handleWorkStation(item)">
                            {{ item.stationName }}</p>
                    </div>
                </div>
                <template #reference>
                    <div class="station-text-btn" title="工作站">
                        <el-button link class="btn-padding">
                            <i class="fa fa-work-station"></i>
                        </el-button>
                    </div>
                </template>
            </el-popover>


            <!-- 工作站遮罩层 -->
            <!-- <p v-if="isShowWorkStation" @click="isShowWorkStation = false" class=" h-full w-full m-0"
                style="position:fixed;top: 0;left: 0;right: 0; background:black;opacity: 0.3;z-index: 9999;border:none">
            </p> -->

            <!-- 客户端设置 -->
            <el-popover v-model:visible="isShowIpPop" placement="bottom-end" trigger="hover" :width="450">
                <ClientParams :isShow="isShowIpPop" @updateIP="(value) => { resClientIp = value }"
                    @closePop="isShowIpPop = false"></ClientParams>
                <template #reference>
                    <div class="btn-box" title="设置">
                        <el-button link class="btn-padding" @click="isShowIpPop = !isShowIpPop">
                            <i class="fa fa-gears" style="width: 18px"></i>
                        </el-button>
                    </div>
                </template>
            </el-popover>

            <!-- 客户端下载 -->
            <el-popover :width="260" placement="bottom-end" trigger="hover" @show="getLinkData">
                <div v-loading="loading1" style="min-height: 80px;">
                    <div v-for="(item, index) in linkList" :key="index" style="margin-bottom: 10px;">
                        <a @click="handleItemClick(item)" target="_blank">
                            <i class="fa fa-hand-o-down" style="margin-left: 10px; margin-right: 6px;top: 3px;"></i>
                            <span class="i-text-hidden">{{ item.name }}</span>
                        </a>
                    </div>
                </div>
                <template #reference>
                    <div class="btn-box" title="下载">
                        <el-button link class="btn-padding"><i class="fa fa-download"></i></el-button>
                    </div>
                </template>
            </el-popover>

            <UTD></UTD>

            <!-- 锁屏 -->
            <div class="btn-box" title="锁屏">
                <el-button link class="btn-padding" @click="onLockClick"><i class="fa fa-lock-fill"></i></el-button>
            </div>

            <div class="btn-box" title="主题">
                <el-button link class="btn-padding" @click="onThemeClick" ><i class="fa fa-skin"></i></el-button>
            </div>
            
            <!-- 全屏 -->
            <div class="btn-box" title="全屏">
                <screenfull class="c-screenfull" />
            </div>


            <!-- manual -->
            <!-- 当只有一个模块时不显示，弹出层 -->
            <div class="btn-box" v-if="menuList.length > 1">

                <el-popover placement="bottom-end" :width="328" trigger="hover" v-model:visible="isShowMenu">
                    <div class="c-menuList">
                        <div class="menulist-btn" v-for="(menu, index) in menuList" :key="index"
                            @click="moduleMenuClick(menu.routerName)">
                            <svg class="fa" aria-hidden="true">
                                <use :xlink:href="'#' + menu.icon"></use>
                            </svg>
                            <label>{{ menu.name }}</label>
                        </div>
                    </div>
                    <template #reference>
                        <el-button class="f-openModuleMenu" title="模块" link><i class="fa fa-menu"></i></el-button>
                    </template>
                </el-popover>

            </div>
            <!-- 头像菜单栏 -->
            <Avatar />
        </div>
    </div>
</template>

<script>
import axios from 'axios'
import { checkFileExists } from '$supersetResource/js/tools'

import ReloginModal from '$supersetViews/components/ReloginModal.vue'
import Screenfull from '$supersetViews/components/Screenfull.vue'
import ClientParams from './ClientParams.vue'
import UTD from './UTD.vue'
import Api from '$supersetApi/projects/apricot/system/thirdLink.js'
import { getHospitalData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { getWorkStationData } from '$supersetApi/projects/apricot/system/workStation.js'
import { removeAccessToken } from '@/utils/accessToken';

export default {
    name: "NavBar",
    components: {
        ReloginModal,
        Screenfull,
        ClientParams,
        UTD
    },
    data () {
        return {
            lockPage: false,
            curRouteProject: 'home',
            isShowMenu: false,
            isShowWorkStation: false,
            districtData: [],
            workStationData: [],
            resClientIp: '',
            linkList: [],
            currentDistrict: {},
            currentWorkStation: {},
            stationLoad: false,
            loading1: false,
            systemName: window.configs.names.systemName,
            systemNameEN: window.configs.names.systemNameEN,
            isShowIpPop: false,
            isEnableRobustPwd: false,
            hospitalLogoUrl: '',
            isShowLogo: false
        }
    },
    computed: {
        menuList () {
            return this.$store.getters['module_router/menuList']
        },
        tabList () {
            return this.$store.getters['module_router/RecentOpens']
        },
        userInfo () {
            return this.$store.getters['user/userSystemInfo']
        },
    },
    methods: {
        // 切换模块
        moduleMenuClick (routerName) {
            this.$store.commit({
                type: 'module_router/switchModuleRouter',
                activeRouterName: routerName
            })
            /*获取按钮携带的参数，并打开对应的路由，打开对应标签，为标签加上打开路由的事件*/

            if (this.isShowMenu) {
                this.isShowMenu = !this.isShowMenu;
            }
        },
        // 关闭tabs
        closeTab (routerName) {
            //提交一个mutations，删除一个标签
            this.$store.commit({
                type: 'module_router/removeRecentOpen',
                activeRouterName: routerName
            })
        },
        // 打开锁定弹窗
        onLockClick () {
            //锁定页面需要删除token
            removeAccessToken()

            this.lockPage = true
        },
        // 主题设置
        onThemeClick() {
            this.$store.dispatch('setting/setSettingDrawer', true);
        },
        // 选择工作站
        handleWorkStation (item) {
            this.currentWorkStation = item;
            this.currentWorkStation.districtName = this.currentDistrict.sHospitalDistrictName;
            localStorage.setItem('workStation', JSON.stringify(item));

            this.$store.dispatch('user/setWorkStation', { workStation: item });
            this.$nextTick(() => {
                this.isShowWorkStation = false;
            })
        },
        //  切换院区
        handleChangeDistrict (item) {
            this.currentDistrict = item;
            this.getWorkStationData(item.sId);
        },
        // 获取院区
        getHospitalData () {
            this.stationLoad = true;
            getHospitalData().then((res) => {
                if (res.success) {
                    this.districtData = res?.data || [];
                    if (!this.districtData.length) {
                        return
                    }
                    if (!localStorage.getItem('workStation')) {
                        // 处理头部页面样式
                        // $('.g-header').css({
                        //   'z-index': '2001'
                        // })
                        // 处理工作站页面数据
                        this.isShowWorkStation = true;

                    }
                    if (!this.currentDistrict.sId) {
                        this.currentDistrict = this.districtData[0];
                    }
                    this.getWorkStationData(this.currentDistrict.sId);
                    this.$nextTick(() => {
                        // this.$refs.workStationRef.updatePopper()
                    })
                    return
                }
                this.$message.error(res.msg);
                this.stationLoad = false;
            }).catch(() => {
                this.stationLoad = false;
            })
        },
        // 获取工作站
        getWorkStationData (sId) {
            this.stationLoad = true;
            getWorkStationData({
                districtId: sId,
                isEnable: 1
            }).then(res => {
                this.stationLoad = false;
                if (res.success) {
                    this.workStationData = res.data || [];
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.stationLoad = false;
            })
        },
        // 获取下载链接
        getLinkData () {
            let params = {
                condition: {
                    sLinkTypeCode: '2'
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 200,
                }
            };
            this.loading1 = true;
            Api.getThirdLinkData(params).then(res => {
                this.loading1 = false;
                if (res.success) {
                    let arr = res.data.recordList == null ? [] : res.data.recordList;
                    this.linkList = []
                    arr.forEach(e => {
                        let url = e.sLinkURL + '?';   // 新参数
                        let newArr = [];              // 转变后的参数数组
                        let params = {};
                        // 确保字符串

                        let tempStr = e.sLinkParam;
                        tempStr += ''
                        // 每行参数转数组组装
                        let tempArr = tempStr.split('&');
                        tempArr.forEach((item) => {
                            // 找 {} 取其值
                            let left = item.indexOf('{')
                            let right = item.indexOf('}')

                            if (left != -1 && right != -1) {
                                // 有括号
                                let key = item.slice(left + 1, right)
                                let keyArr = key.split("#")
                                let newKey = ""
                                keyArr.forEach(key => {
                                    params[key] = undefined
                                });
                                newArr.push(item.slice(0, left) + newKey)
                            } else {
                                // 没有括号
                                newArr.push(item)
                                let keyValArr = item.split('=');
                                if (keyValArr.length) {
                                    params[keyValArr[0]] = keyValArr[1]
                                }
                            }

                        });
                        url += newArr.join('&') // 从新转变为字符串
                        this.linkList.push({
                            code: e.sLinkTypeCode,
                            name: e.sLinkTypeName,
                            url: url,
                            baseUrl: e.sLinkURL,
                            type: e.sCallType,
                            params
                        })
                    });
                }
            }).catch(err => {
                console.log(err);
                this.loading1 = false;
            })
        },
        handleItemClick (item) {
            if (item.type == 'web_get' || item.type == 'client_get') {
                // 打开网页链接
                window.open(item.url, '_blank', 'noopener noreferrer');
            } else if (item.type === 'client_post' || item.type === 'web_post') {
                // 客户端接口调用
                axios({
                    url: item.baseUrl,
                    methods: 'post',
                    params: item.params
                }).then(res => {
                    if (res.data.success) {
                        return
                    }
                    this.$message.error(res.data.msg)
                }).catch(err => {
                    console.log(err);
                })
            }
        },
        handleChangeTheme () {
            this.$store.dispatch('setting/setSettingDrawer', true);
        },
    },
    mounted () {
        this.hospitalLogoUrl =  window.location.origin + window.location.pathname + `img/hospital/logo.jpg`;
        checkFileExists(this.hospitalLogoUrl, (res) => {
            this.isShowLogo = res;
        })
        this.$nextTick(() => {
            let workStation = localStorage.getItem('workStation');
            if (workStation) {
                workStation = JSON.parse(workStation);
                this.currentWorkStation = workStation;
                this.currentDistrict.sId = workStation.districtId;
                this.currentDistrict.sHospitalDistrictName = workStation.districtName;
            } else {
                this.getHospitalData();
            }
        })
    }
};
</script>

<style lang="scss" scoped>
$headerHeight: 45px;

.tt {
    position: relative;
    width: auto;
    font-size: 18px;
    line-height: 45px;
    letter-spacing: 2px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-bar-container {
    position: relative;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: $base-nav-bar-height;
    padding-left: $base-padding;
    overflow: hidden;
    user-select: none;
    box-shadow: $base-box-shadow;
    color: var(--theme-header-color);
    background: linear-gradient(to right, var(--el-color-primary), var(--el-color-primary-dark-2));

    .box-logo {
        flex: 0;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .tab-bar {
        flex: 1;
        display: flex;
        align-items: center;
        overflow-y: hidden;
        overflow-x: auto;
        margin-left: 5%;

        &::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.9);
        }

        .c-tabs {
            position: relative;
            display: block;
            height: $headerHeight;
            width: auto;
            white-space: nowrap;
            text-overflow: ellipsis;

            .tab-btn {
                position: relative;
                display: inline-block;
                cursor: pointer;
                font-size: 14px;
                margin-top: 10px;
                padding: 10px 35px 10px 20px;
                color: var(--theme-header-color);
                background: rgba(255, 255, 255, 0.22);
                border-color: rgba(255, 255, 255, 0.22);
                border-radius: 4px 4px 0 0;
                margin-left: 6px;

                &:hover {
                    // font-weight: bold;
                    background: rgba(255, 255, 255, 0.42);
                }

                .c-icon {
                    position: absolute;
                    top: 8px;
                    right: 5px;
                    padding: 1px 2px 3px;

                    &:hover {
                        color: #df5000;
                    }
                }

                &.s-acitve {
                    color: var(--el-color-primary);
                    background: var(--theme-bg);
                    border-color: var(--el-color-primary);
                    font-weight: bold;
                }


            }
        }
    }

    .right-panel {
        position: relative;
        top: -1px;
        flex: 0;
        display: flex;
        align-items: center;

        display: flex;
        align-content: center;
        align-items: center;
        justify-content: flex-end;
        height: $base-nav-bar-height;

        align-items: center;

        .btn-box {
            position: relative;
            cursor: pointer;

            &:hover {
                transform: scale(1.15);
            }

            // &:before {
            //     content: "";
            //     width: 1px;
            //     height: 20px;
            //     position: absolute;
            //     left: 0;
            //     top: 7px;
            //     bottom: 1px;
            //     background-color: rgba(255, 255, 255, 0.2);
            //     z-index: 1;
            // }

            button {
                border: none;
            }

            .el-button {
                color: var(--theme-header-color);
            }
        }
        .i-text-hidden {
            display: inline-block;
            width: calc(100% - 45px);
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .n-userBtn {
            padding: 9px;
        }

        .i-username {
            padding: 0 5px;
        }

        .f-openModuleMenu {
            padding: 8px;
        }

        .btn-padding {
            padding: 8px;
        }
    }

}


a {
    cursor: pointer;
}

.c-logo {
    float: left;
    height: $headerHeight;
    line-height: $headerHeight;
    font-size: 0;

    >.c-icon {
        float: left;

        >i.fa {
            margin-left: 10px;
            font-size: 24px;
        }
    }

    h1 {
        float: left;
        margin: 0;
        padding-left: 5px;
        line-height: $headerHeight;
        font-weight: bold;
        font-size: 18px;
    }
}





.c-menuList {
    display: block;

    .menulist-btn {
        position: relative;
        float: left;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        margin: 2px;
        width: 70px;
        height: 70px;
        padding: 0;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
            background: var(--theme-header-bg);
            color: var(--theme-header-color);
        }

        svg {
            font-size: 24px !important;
        }

        >label {
            display: block;
            //   font-size: 12px;
            margin-top: 2px;

            &:hover {
                cursor: inherit;
            }
        }

        &.is-disabled {
            svg {
                opacity: 0.5;
            }
        }
    }
}



.c-district {
    display: flex;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    overflow: hidden;

    .c-item {
        max-width: 150px;
        padding: 10px 15px;
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
    }

    .c-active {
        position: relative;

        &::after {
            content: "";
            position: absolute;
            width: 100%;
            border: 1px solid var(--theme-header-bg);
            left: 0;
            bottom: 0;
        }
    }
}

.c-station {
    min-height: 100px;
    max-height: 65vh;
    padding: 10px;
    overflow: auto;

    .c-item {
        padding: 2px 10px; 
        white-space: nowrap; /* 不换行 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        cursor: pointer;

        &.c-active {
            padding: 5px 10px;
            background-color: var(--theme-header-bg);
            color: var(--theme-header-color);
        }
    }
}

.ip-text {
    position: relative;
    display: inline-block;
    padding: 0 10px;
    color: var(--theme-header-color);
    white-space: nowrap;
    &:before {
        content: "";
        width: 1px;
        height: 16px;
        position: absolute;
        right: 0;
        top: 0;
        background-color: rgba(255, 255, 255, 0.3);
        z-index: 1;
    }
}

.station-text-btn {
    position: relative;
    display: flex;
    color: var(--theme-header-color);
    white-space: nowrap;
    margin-left: 4px;
    cursor: pointer;

    .el-button {
        color: var(--theme-header-color);

        &:hover {
            transform: scale(1.15);
        }
    }
}

:deep(.client-form .el-form-item__error) {
    bottom: -20px;
}
</style>
