<template>
    <!-- 项目设置 院区 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div>
                <el-button-icon-fa type="primary"
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain
                    type="primary"
                    icon="el-icon-refresh"
                    @click="mxGetTableList">刷新</el-button-icon-fa>
            </div>
            <div class="c-form-search search">

            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content">
                <!--  v-if="reRender" -->
                <el-table :data="tableData"
                    v-drag:[config]="tableData"
                    ref="mainTable"
                    id="hospitalTable"
                    highlight-current-row
                    @row-click="onClickRow"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort">
                        <template v-slot="scope">
                            <template v-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                <span v-else> 否 </span>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>

                    </el-table-column>
                    <el-table-column width="210px"
                        align="center">
                        <template v-slot:header="scope">
                            <span>操 作</span>
                            <!-- <i class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i> -->
                        </template>
                        <template v-slot="scope">
                            <el-button link
                                size="small"
                                type="primary"
                                @click="handleEdit(scope.row)">编辑
                                <template #icon>
                                    <Icon name="el-icon-edit"
                                        color="">
                                    </Icon>
                                </template>
                            </el-button>
                            <el-divider direction="vertical"></el-divider>
                            <el-button link
                                size="small"
                                @click.stop="onClickDel(scope.row)">
                                删除
                                <template #icon>
                                    <Icon name="el-icon-delete"
                                        color="">
                                    </Icon>
                                </template>
                            </el-button>
                            <el-divider direction="vertical"></el-divider>
                            <el-button size="small"
                                link
                                class="i-sort">排序
                                <template #icon>
                                    <Icon name="el-icon-rank"
                                        color="">
                                    </Icon>
                                </template>
                            </el-button>
                        </template>

                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            :close-on-click-modal="false"
            @close="closeDialog">
            <div class="flex">
                <el-form :model="form"
                    ref="setform"
                    label-width="90px"
                    :rules="rules">
                    <el-col :span="24">
                        <el-form-item label="院 区:"
                            prop="sHospitalDistrictName">
                            <el-input placeholder="院区名称"
                                v-model="form.sHospitalDistrictName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="简 称:"
                            prop="sDistrictPrefix">
                            <el-input placeholder="院区简称"
                                v-model="form.sDistrictPrefix"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="状   态:"
                            prop="">
                            <el-radio-group v-model="form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer>
                <div>
                    <el-button-icon-fa :loading="saveLoading"
                        icon="el-icon-check"
                        type="primary"
                        @click="handleSave">保存</el-button-icon-fa>
                    <el-button-icon-fa @click="closeDialog"
                        icon="el-icon-close">取消</el-button-icon-fa>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SetHospital',
    mixins: [mixinTable, mixinTableDrag],
    data () {
        return {
            dialogTitle: '新增',
            dialogVisible: false,
            saveLoading: false,
            form: {
                sHospitalDistrictName: '',
                iIsEnable: 1,
            },
            rules: {
                sHospitalDistrictName: [{
                    required: true,
                    message: '请输入院区名称',
                    trigger: 'blur'
                }],
                sDistrictPrefix: [{
                    required: true,
                    message: '请输入院区简称',
                    trigger: 'blur'
                }],
            },
            configTable: [
                {
                    sProp: 'sHospitalDistrictName', sLabel: '院区名称',
                    sAlign: 'left', sMinWidth: '100px',
                },

                {
                    sProp: 'sDistrictPrefix', sLabel: '院区简称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '140px',
                },
            ],
            tableData: [],
            reRender: true,
            condition: {
                // iIsEnable: '1'
            },
            page: { pageCurrent: 1, pageSize: 9999 },
            sortApi: Api.sortHospital,
        }
    },
    methods: {
        // 新增
        handleAdd () {
            this.form = {
                sHospitalDistrictName: '',
                iIsEnable: 1,
            },
            this.dialogTitle = '新增';
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs['setform'].clearValidate();
            })

        },
        closeDialog () {
            this.dialogVisible = false
        },
        handleEdit (row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.form = Object.assign({}, row)
            this.$nextTick(() => {
                this.$refs['setform'].clearValidate();
            })
        },
        handleSave () {
            this.saveLoading = true
            let params = Object.assign({}, this.form)
            this.$refs['setform'].validate((valid) => {
                if (valid) {
                    this.saveData(params)
                    return
                }
                this.saveLoading = false
            })
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledHospital({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sHospitalDistrictName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delHospital({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },

        /**
         * 保存数据
         */
        saveData (params) {
            if (!params.sId) {
                Api.addHospital(params).then((res) => {
                    this.saveLoading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.dialogVisible = false
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.saveLoading = false;
                })
                return
            }
            Api.editHospital(params).then((res) => {
                this.saveLoading = false;
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.dialogVisible = false
                    this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.saveLoading = false;
            })
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            Api.getHospitalData().then((res) => {
                if (res.success) {
                    this.tableData = res?.data || [];
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    mounted () { },
};
</script>
<style lang="scss" scoped>
:deep(.el-button--text .el-icon-delete) {
    color: #f56c6c;
}

.action-icon {
    font-size: 16px;
    cursor: pointer;
    margin: 0 5px;
}
</style>
