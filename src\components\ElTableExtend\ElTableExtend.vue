<template>
  <div class="eltableex-container">
    <el-table v-if="readyToShow" ref="tableRef" v-bind="$attrs" :default-sort="elTableDefaultSortObj"
      :row-class-name="getCustomRowClassName" @sort-change="onHandleSortChange"
      @selection-change="onHandleSelectionChange">
      <template v-slot:empty>
        <slot name="empty"></slot>
      </template>
      <template v-slot:default>
        <component v-for="(item, index) in react.hiddenSlotsList" :is="item" label=" " :key="index" />
        <component v-for="(item, index) in showingItemsList" :is="item" :label="item.props.label"
          :key="item.props.prop + item.props.label + index" />
      </template>
      <template v-slot:append>
        <slot name="empty"></slot>
      </template>
    </el-table>


    <el-popover v-if="configBtn === true" ref="popoverRef" trigger="click" placement="right-end" width="auto" :hide-after="0"
      @after-enter="onPopAfterEnter">
      <template #default>
        <div class="pop-header">
          <h3>{{ ('表格配置') }}</h3>

        </div>
        <!-- el-skeleton 的 width=表头宽度 -->
        <div v-if="!isRenderPopover" style="width: 580px; height:450px; overflow: hidden;">
            <el-skeleton :rows="12" />
        </div>
        <el-table v-else class="pop-table" :data="react.tableData" row-key="prop" ref="popTableRef" max-height="500">
          <el-table-column prop="a1" :label="('排序')" align="center" width="60">
            <el-icon class="drag-icon" :size="18">
              <Rank />
            </el-icon>
          </el-table-column>
          <el-table-column prop="a2" :label="('显示')" align="center" width="60">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isShow" @change="onChangeShow(scope)"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column prop="a3" :label="('列名称')" width="110" align="left">
            <template #default="scope">
              {{ scope.row.label }}
            </template>
          </el-table-column>
          <el-table-column prop="align" :label="('对齐方式')" align="center" width="130">
            <template #default="scope">
              <el-radio-group class="radio-group" @change="onChangeAlign(scope)" v-model="scope.row.align">
                <el-radio-button label="left">左</el-radio-button>
                <el-radio-button label="center">中</el-radio-button>
                <el-radio-button label="right">右</el-radio-button>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column prop="width" :label="('宽度px')" width="80" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.width" @blur="onChangeWidth(scope)" :placeholder="('自动')" />
            </template>

          </el-table-column>
          <el-table-column prop="isExport" :label="('导出')" width="80" align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isExport" @change="onChangeExport(scope)"></el-checkbox>
            </template>

          </el-table-column>

          <el-table-column prop="a4" :label="('可排序')" align="center" width="80">
            <template #default="scope">
              <el-checkbox v-model="scope.row.isSortable" @change="onChangeSortable(scope)"></el-checkbox>
            </template>
          </el-table-column>


        </el-table>
        <div class="flex items-center justify-between pt-2">
          <span class="flex">
            <el-button type="primary" @click="setStorage(1)">{{ ('保存到全局') }}</el-button>
            <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否恢复默认设置？')"
              :teleported="false" @confirm="onClickResetConfig">
              <template #reference>
                <el-button class="ml-2" type="primary" plain>{{ ('恢复默认设置') }}</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否读取全局设置？')"
                :teleported="false" :persistent="false" @confirm="onClickGlobalStorage">
                <template #reference>
                  <el-button class="ml-2" type="primary" plain>{{ ('恢复全局设置') }}</el-button>
                </template>
              </el-popconfirm>
          </span>
          <span class="flex">
            <el-button type="default" @click="onClickClose">{{ ('关闭') }}</el-button>
          </span>
        </div>
      </template>
      <template #reference>
        <div v-if="configBtn === true" class="config-btn">
          <!-- <img src="@/assets/svg/setting.svg" class="img" /> -->
          <el-icon class="img">
            <MoreFilled />
          </el-icon>
        </div>
      </template>
    </el-popover>
  </div>
</template>
<script>
import draggable from 'vuedraggable';
import Sortable from 'sortablejs'
import { Rank, MoreFilled } from '@element-plus/icons-vue'

import { getOnlineConfig, saveOnlineConfig, compareAndModifyTableHead, getGlobalOnlineConfig } from '@/utils'
import { useStore } from 'vuex';
import { isFunction } from 'lodash-es';


const defaultStorageData = {
  list: [],
  sortingProp: null,
  order: null
}

// el-table的方法
const methodsList = [
  'setCurrentRow',
  'toggleRowSelection',
  'toggleAllSelection',
  'toggleRowExpansion',
  'doLayout',
  'scrollTo',
  'setScrollTop',
  'setScrollLeft',
  'clearSelection',
  'clearSort',
  'clearFilter',
  'sort',
  'getSelectionRows',
]

const elTableMethods = {}
methodsList.forEach(key => {
  elTableMethods[key] = function (...args) {
    if (this.isElTableMethodsReady) {
      this.tableRef[key].call(this.tableRef, ...args)
    } else {
      console.info('isElTableMethodsReady: false')
    }
  }
})

export default {
  name: "ElTableExtend",
  components: {
    draggable,
    Rank, MoreFilled
  },
  props: {
    storageKey: {
      type: String,
      default: "", // 不填不启用localstorage
    },
    iModuleId: {
      default: "", // 不填不启用数据上传
    },
    configBtn: {
      type: [Object, Boolean],
      default: () => true
    },
    rowClassName: {
      default: "",
    },
  },
  // @sort-change="mxOnSortTable" 接管事件
  emits: ['sort-change', 'selection-change'],
  setup(props) {
    const _store = useStore()
    const buttonRef = ref();
    const popoverRef = ref();
    const tableRef = ref();
    const react = ref({
      slotItemList: [],
      tableData: [],
      hiddenSlotsList: []
    });
    const configButtonRef = computed(() => {
      if (props.configBtn && props.configBtn.ref) {
        return props.configBtn
      }
      return buttonRef
    });
    const elTableDefaultSortObj = ref(
      { prop: '', order: null }
    )

    const isElTableMethodsReady = computed(() => {
      return !!tableRef.value?.doLayout
    })

    const userNo = computed(() => {
      let userInfo = _store.getters["user/userSystemInfo"] || {};
      return userInfo.sId
    })

    const elTableMethods = {}
    methodsList.forEach(key => {
      elTableMethods[key] = ref(function (...args) {
        console.info('not ready : tableRef.value: ' + key)
      })

      // console.log(elTableMethods[key].value)
    })


    watch(isElTableMethodsReady, (val) => {
      if (val) {
        methodsList.forEach(key => {
          if (tableRef.value[key]) {
            elTableMethods[key].value = tableRef.value[key].bind(tableRef.value)
            // function (...args) {
            //   tableRef.value[key].call(tableRef.value, ...args)
            // }
          }
        })
      }
    }, { immediate: true })



    return {
      ...elTableMethods,
      userNo,
      react,
      
      isRenderPopover: ref(false),
      readyToShow: ref(false),
      buttonRef,
      popoverRef,
      tableRef,
      configButtonRef,
      selectionArr: ref([]),
      elTableDefaultSortObj,
      isElTableMethodsReady,

      getOnlineConfig,
      saveOnlineConfig,
      getGlobalOnlineConfig,
    };
  },
  created() {
    this.setDefaultData()
    this.getStorage().then(() => {
      this.initSort() // 带有排序记忆时需要触发一次排序事件 
      this.readyToShow = true
    });
  },
  computed: {
    showingLabels() {
      return this.react.tableData.filter((i) => i.isShow).map((i) => i.prop);
    },
    showingItemsList() {
      return this.react.slotItemList.filter(
        (i) => this.showingLabels.indexOf(i.props.prop) > -1
      );
    },
    showingConfigItemsList() {
      // if (item.fixed)
      return []
    },
    serverSortingProps() {
      return this.react.tableData.filter(
        (i) => i.customSort
      ).map((i) => i.prop);
    }

  },
  methods: {

    refreshLayout() {
      this.$nextTick(() => {
        this.doLayout();
      });
    },
    onChangeAlign(scope) {
      const index = scope.$index;
      const slot = this.react.slotItemList[index];
      const table = this.react.tableData[index];
      table.isShow = false;
      slot.props.align = scope.row.align;
      this.$nextTick(() => {
        // 解决不立即更新
        table.isShow = true;
        this.refreshLayout();
        this.setStorage();
      });
    },
    onChangeWidth(scope) {
      const index = scope.$index;
      const slot = this.react.slotItemList[index];
      const table = this.react.tableData[index];
      table.isShow = false;
      slot.props.width = scope.row.width;
      this.$nextTick(() => {
        // 解决不立即更新
        table.isShow = true;
        this.refreshLayout();
        this.setStorage();
      });
    },
    onChangeSortable(scope) {
      const index = scope.$index;
      const slot = this.react.slotItemList[index];
      const table = this.react.tableData[index];
      table.isShow = false;
      slot.props.sortable = table.isSortable ? (table.customSort ? 'custom' : true) : false;

      this.$nextTick(() => {
        // 解决不立即更新
        table.isShow = true;
        this.refreshLayout();
        this.setStorage();

        // if (!table.isSortable) {
        //   this.elTableDefaultSortObj.prop = ''
        //   this.elTableDefaultSortObj.order = null
        //   this.tableRef.$refs.tableHeaderRef.store.clearSort()
        // }
      });
    },
    
    onChangeShow() {
      this.refreshLayout();
      this.setStorage();
    },
    onChangeExport() {
      this.refreshLayout();
      this.setStorage();
    },
    /*  isGetGlobal 是否获取全局配置 */
    async getStorage(isGetGlobal = false) {
      if (!this.storageKey) return true;
      let storageStr = localStorage.getItem('ElTableExtend-' + this.storageKey);
      let storageObj = {
        ...defaultStorageData
      }


      if (this.iModuleId && this.storageKey) {
        if(isGetGlobal) {
            await this.getGlobalOnlineConfig()
        } else {
            await this.getOnlineConfig()
        }
        Object.assign(storageObj,
          this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])

      } else {
        try {
          const parsed = JSON.parse(storageStr)
          if (Array.isArray(parsed)) {
            storageObj.list = parsed
          } else if (parsed && parsed.list) {
            storageObj = parsed
          }
        } catch (error) {
          console.error(error)
        }
      }


      let storageList = storageObj.list;

      const propList = (storageList || []).map((i) => i.prop);
      const slotsList = this.getSlotList();
      let newList = compareAndModifyTableHead(slotsList, storageList);
    //   let newList = []

    //   if (propList.length !== slotsList.length) {
    //     // 如果表格加了新的一列数据，恢复成默认顺序
    //     newList = slotsList
    //   } else {

    //     // newList // 按localstor排序后的slot列表
    //     propList.forEach(prop => {
    //       const index = slotsList.findIndex(slot => slot.props.prop === prop)
    //       if (index > -1) {
    //         newList.push(slotsList.splice(index, 1)[0])
    //       }
    //     })
    //     newList = [...slotsList].concat(newList)
    //   }

      this.react.slotItemList = newList
      this.react.tableData = newList.map(this.dataObjConvert);

      // 通过给slot加属性
      let index, slotItem, tableItem
      storageList.forEach((item) => {
        index = this.react.tableData.findIndex(target => item.prop === target.prop)
        if (index > -1) {
          slotItem = this.react.slotItemList[index];
          tableItem = this.react.tableData[index];
          tableItem.isShow = item.isShow;
          tableItem.isSortable = !!item.isSortable;
          tableItem.isExport = item.isExport ?? true;

          slotItem.props.align = tableItem.align = item.align;
          slotItem.props.sortable = item.isSortable ? (item.customSort ? 'custom' : true) : false;

          if (item.width) {
            tableItem.width = item.width;
            slotItem.props.width = item.width;
          }

          if (item['min-width']) {
            tableItem['min-width'] = item['min-width'];
            slotItem.props['min-width'] = item['min-width'];
          }

        }
      });
      if(storageObj.sortingProp) {
        this.elTableDefaultSortObj.prop = storageObj.sortingProp;
        this.elTableDefaultSortObj.order = storageObj.order;
      }
    //   // 设置 elTableDefaultSortObj  
    //   this.elTableDefaultSortObj.prop = storageObj.sortingProp || this.elTableDefaultSortObj.prop
    //   this.elTableDefaultSortObj.order = storageObj.order || this.elTableDefaultSortObj.order
      return true;
    },
    // 设置缓存
    setStorage(isGlobal = 0) {
      if (!this.storageKey) return;

      const storageObj = {
        list: this.react.tableData,
        sortingProp: this.elTableDefaultSortObj.prop,
        order: this.elTableDefaultSortObj.order,
      }

      const storageString = JSON.stringify(storageObj)

      localStorage.setItem(
        "ElTableExtend-" + this.storageKey,
        storageString
      );
      this.saveOnlineConfig(storageObj, isGlobal)
    },
    getSlotList() {
      const slotsList = []
      this.$slots.default().forEach(slot => {
        if (!slot.children) {
          slotsList.push(slot)
        } else if (slot.children.length && slot.children.forEach) {
          slot.children.forEach(child => {
            slotsList.push(child)
          })
        } else {
          // todo 其他情况
          slotsList.push(slot)
        }
      })
      // console.log(slotsList)
      // type.name = ElTableColumn
      this.react.hiddenSlotsList = slotsList.filter(item => item.type && item.type.name && item.type.name === 'ElTableColumn' && item.props.isController)
      return slotsList.filter(item => item.type && item.type.name && item.type.name === 'ElTableColumn' && !item.props.isController)

    },
    setDefaultData() {
      this.react.slotItemList = this.getSlotList()
      // console.log(this.react.slotItemList)
      this.react.tableData = this.react.slotItemList.map(this.dataObjConvert);

      const defaultSortItem = this.react.slotItemList.find(i => i.props.isSortable === true && i.props.sOrder)
      // console.log(defaultSortItem)
      if (defaultSortItem) {
        defaultSortItem.props.sortable = defaultSortItem.props.sortable === 'custom' ? 'custom' : true;
        this.elTableDefaultSortObj.prop = defaultSortItem.props.prop
        this.elTableDefaultSortObj.order = defaultSortItem.props.sOrder
      } else {
        this.elTableDefaultSortObj.prop = null
        this.elTableDefaultSortObj.order = null
      }
    },
    dataObjConvert(item) {
      return ({
        prop: item.props.prop || " ",
        label: item.props.label || " ",
        // isShow: true,
        isShow: !item.props.iIsHide,
        align: item.props.align || "left",
        width: String(item.props.width || '').replace(/[px]/g, ''),
        'min-width': item.props['min-width'] || item.props.width || '',
        isSortable: !!item.props.isSortable,
        customSort: item.props.sortable === 'custom',
        sOrder: item.props.sOrder,
        isExport: !item.props.isExport,
      })
    },
    async onClickGlobalStorage() {
        this.getStorage(true).then(() => {
            setTimeout(() => {
                this.initSort() // 带有排序记忆时需要触发一次排序事件 
                this.setStorage();
            }, 10)
        });
    },
    // 点击重置
    onClickResetConfig() {
      // this.readyToShow = false  // TODO 组件不刷新的话，点恢复默认后排序不会重置

      this.setDefaultData()
      this.doLayout();
      this.setStorage();

      setTimeout(() => {
        this.$refs.tableRef.sort(this.elTableDefaultSortObj.prop, this.elTableDefaultSortObj.order)
        // this.readyToShow = true
        this.initSort() // 带有排序记忆时需要触发一次排序事件 
      }, 0);
    },
    onClickClose() {
      this.$el.click()
    },
    onPopAfterEnter() {
      this.isRenderPopover = true;
      this.$nextTick(() => {
        this.rowDrop()
      })
    },
    //行拖拽
    rowDrop() {
      const popTable = (this.$refs.popTableRef.$el)
      const tbody = popTable.querySelector('tbody')
      Sortable.create(tbody, {
        disabled: false, // 是否开启拖拽
        handle: ".drag-icon",
        // ghostClass: 'sortable-ghost', //拖拽样式
        animation: 150, // 拖拽延时，效果更好看
        group: { // 是否开启跨表拖拽
          pull: false,
          put: false
        },
        onEnd: ({ newIndex, oldIndex }) => {
          // console.log(newIndex, oldIndex)
          const currRow = this.react.tableData.splice(oldIndex, 1)[0]
          this.react.tableData.splice(newIndex, 0, currRow)
          const _currRow = this.react.slotItemList.splice(oldIndex, 1)[0]
          this.react.slotItemList.splice(newIndex, 0, _currRow)
          this.$nextTick(() => {
            this.refreshLayout();
            this.setStorage();
          });
        }
      })
    },
    onHandleSortChange({ column, prop, order }) {
      // console.log('onHandleSortChange', column, prop, order)
      // 保存当前选择的排序项和升序值
      this.elTableDefaultSortObj.prop = prop
      this.elTableDefaultSortObj.order = order
      this.setStorage()
      if (this.serverSortingProps.includes(prop)) {
        this.$emit('sort-change', column, prop, order, 0)
      } else {
        // 前端排序 不触发事件 
        // console.log(this.tableRef.$refs.tableHeaderRef.store.states.data.value[0] ) // 获取排序后的表格数组
      }
    },
    getCustomRowClassName({ row, rowIndex }) {
      const selectedClass = this.selectionArr.find(item => item === row) ? 'customSelectedClassname' : ''
      const listData = this.$attrs.data || []
      listData.every((item, index) => {
        if (item.index === undefined) {
          item.index = index
          return true
        }
      }) 
      let rowClass = this.rowClassName
      if (isFunction(this.rowClassName)) {
        rowClass = this.rowClassName({ row, rowIndex }) || ''
      }
      return rowClass + ' ' + selectedClass
    },
    onHandleSelectionChange(selection) {
      this.selectionArr = selection.filter(i => i)
      this.$emit('selection-change', selection.filter(i => i))
    },
    initSort() {
      // item.customSort
      const target = this.react.slotItemList.find(item => item.props.prop === this.elTableDefaultSortObj.prop)
      if (target && !!this.elTableDefaultSortObj.order) {
        if (this.serverSortingProps.includes(target.props.prop)) {
          const column = target.props
          const prop = this.elTableDefaultSortObj.prop
          const order = this.elTableDefaultSortObj.order
          this.$emit('sort-change', column, prop, order, 1)
        }
      }
    },
  },
  mounted() {

  },
};
</script>

<style lang="scss" scoped>
@mixin leftSpin {
  &::after {
    content: "";
    position: absolute;
    display: block;
    left: 0;
    top: 0;
    width: 2px;
    height: 100%;
    border-left: 5px solid var(--theme-header-bg);
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    height: 100%;
    z-index: 7;
  }
}

.eltableex-container {
  position: relative;
  width: 100%;
  height: 100%;
  color: #3c4353;
  font-size: 13px;
  border-right: 0;
  border-bottom: 0;
  overflow: hidden;
  box-sizing: border-box;
  // border-radius: var(--el-border-radius-base);
  // box-shadow: 0 0 2px gray;

  :deep(.current-row) {
    vertical-align: top;
  }

  :deep(.el-scrollbar) {
    --el-scrollbar-opacity: 0.62;
    --el-scrollbar-hover-opacity: 1;
  }

  :deep(tr.customSelectedClassname) {
    @include leftSpin;
  }

  :deep(tr.rowWithLeftSpin) {
    @include leftSpin;
  }

  :deep(tr.current-row) {
    @include leftSpin;
  }

  :deep(tr.customSelectedClassname td.el-table__cell) {
    background-color: var(--table-selected-row-bg);
  }

  :deep(tr.current-row td.el-table__cell) {
    background-color: var(--table-highlight-row-bg);
    padding: 5px 0;
    font-weight: bold;
  }

  // :deep(tr.customSelectedClassname.hover-row td.el-table__cell) {
  // }


}

.pop-header {
  display: flex;
  align-items: center;
  padding: 6px 0 10px;
  justify-content: space-between;

  h3 {
    margin: 0;
  }
}

.drag-icon {
  position: relative;
  display: inline-flex;
  min-height: 25px;
  align-items: center;
  justify-content: center;
  cursor: move;
  vertical-align: middle;
}

.radio-group {
  height: 30px;

  :deep(.el-radio-button__inner) {
    padding: 5px 11px;
    font-size: 12px;
    border-radius: 0;
  }
}

// .config-btn {
//   position: absolute;
//   top: 0;
//   right: 0;
//   width: 16px;
//   height: 36px;
//   // background: var(--theme-table-header-bg-color);

//   overflow: hidden;
//   z-index: 1;
//   // display: flex;
//   // width: auto;
//   // height: 24px;
//   // align-items: center;
//   // justify-content: center;

//   .triangle-wrap {
//     // display: inline-block;
//     // width: 8px;
//     // height: 8px;
//     // border: 13px solid transparent; /* 定义边框宽度和颜色 */
//     // // border-left: 20px solid #333; /*   */
//     // border-top: 13px solid var(--el-color-primary); /*   */
//     // border-right: 13px solid var(--el-color-primary); /*   */
//   }


//   .setting-icon {
//     position: absolute;
//     display: inline-block;
//     width: 22px;
//     height: 22px;
//     top: 0;
//     right: 0;
//     padding: 8px 0 0 0;
//     box-sizing: border-box;
//     font-size: 18px;
//     color: var(--theme-color);
//     background: var(--theme-bg);
//     cursor: pointer;
//   }


// }

.config-btn {
  position: absolute;
  display: block;
  visibility: hidden;
  top: 4px;
  right: 0;
  width: 24px;
  height: 24px;
  z-index: 3;
  // vertical-align: middle;
  cursor: pointer;

  .img {
    position: relative;
    width: 16px;
    height: 16px;
    padding: 4px;
    top: 1px;
    right: 0;
    font-size: 16px;
    color: #767d88;
    background: var(--theme-table-header-bg-color);
    cursor: pointer;
  }
}

.eltableex-container:hover {
  .config-btn {
    visibility: visible;
  }
}
</style>
