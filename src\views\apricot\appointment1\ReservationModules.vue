<template>
    <div class="container-top">
        <div class="up-btn-part">
            <div class="text">{{ transformDate(modelParams.date) }}</div>
            <div class="text">{{ props.modelValue.roomName }}</div>
            <div>
                <el-dropdown style="margin-right: 10px;" placement="bottom">
                    <el-button type="primary" plain>
                        <template #icon><Icon name="el-icon-setting"></Icon></template>
                    更多功能<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item v-if="$auth['report:appoint:printConfig']" @click="onOpenPrintSet" >
                                <i class="fa fa-print-setup i-height"></i>
                                <span>打印设置</span>
                            </el-dropdown-item>
                            <el-dropdown-item v-if="$auth['report:appoint:pageConfig']" @click="d_manage_visible = true" >
                                <i class="fa fa-page-setting i-height"></i>
                                <span>页面设置</span>
                            </el-dropdown-item>
                            <!-- <el-dropdown-item v-if="$auth['report:injection:callConfig']" @click="onMedicineAppoint" >
                                <i class="fa fa-statistics-histogram i-height"></i>
                                <span>药物统计</span>
                            </el-dropdown-item> -->
                            <el-dropdown-item v-if="$auth['report:appoint:numberSourceTemplate']" @click="openNumSourceMng" >
                                <i class="fa fa-wpforms i-height"></i>
                                <span>号源设置</span>
                            </el-dropdown-item>
                            <el-dropdown-item v-if="$auth['report:appoint:reservedNumberSource']" @click="openReNumSouceDialog" >
                                <i class="fa fa-drag-checkbox-group i-height"></i>
                                <span>留&emsp;&emsp;号</span>
                                </el-dropdown-item>
                            <el-dropdown-item v-if="$auth['report:appoint:addNumberSource']" @click="openAddNumSouce" >
                                <i class="fa fa-plus-square-o i-height"></i>
                                <span>加&emsp;&emsp;号</span>
                                </el-dropdown-item>
                            <el-dropdown-item v-if="$auth['report:appoint:addNumberSource']" @click="openDelNumSouceDialog" >
                                <i class="fa fa-delete i-height"></i>
                                <span>删&emsp;&emsp;号</span>
                            </el-dropdown-item>
                            <el-dropdown-item v-if="$auth['report:appoint:generateNumberSource']" @click="saveGenerateNumSource" :disabled="disGenerateNumSource" >
                                <i class="fa fa-onduty-record i-height"></i>
                                <span>生成号源</span>
                                </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
                <el-button v-auth="'report:appoint:orderedQuery'" type="primary" plain
                @click="handlePatientQueryClick"><template #icon><i class="fa fa-data-search" style="font-size: 14px;"></i></template> 已约查询</el-button>
            </div>
        </div>
        <div class="appointment-part" v-loading="loading">
            <div class="summary">
                <span>总数：{{ patientCount.registered }}</span>
                <span class="green">门诊：{{ patientCount.outpatientCount }}</span>
                <span class="blue">住院：{{ patientCount.inhospitalCount }}</span>
                <span class="orange">急诊：{{ patientCount.emergencyCount }}</span>
                <span class="red">体检：{{ patientCount.examinationCount }}</span>
                <span class="gray">其他：{{ patientCount.otherCount }}</span>
            </div>
            <el-tabs v-model="activeName">
                <el-tab-pane v-for="(tabsItem, index) in numberSources"
                    :key="index"
                    :label="`${tabsItem.typeName}[${tabsItem.numObj.registered}/${tabsItem.numObj.limit}]`"
                    :name="tabsItem.type">
                    <div v-if="tabsItem.numObj.limit === 0"
                        style="display: flex;height: 100%;justify-content: center;align-items: center;">
                        <img src="./svg/empty.svg">
                    </div>
                    <div v-else class="mx-padding">
                        <el-scrollbar class="scrollbar xx-el-scrollbar" ref="myScrollbar">
                        <el-row :gutter="15" style="padding: 15px 10px 10px; margin: 0px;">
                            <el-col class="db-only"
                                v-for="(hour, index) in tabsItem.numObj.hourItems"
                                :key="index">
                                <div class="appoint-list">
                                    <div class="sno-time">{{ hour.time }}</div>
                                    <div class="sno-sourse">
                                        <el-col :sm="12" :md="8" :lg="6" :xl="4"
                                            v-for="(grid, index) in hour.gridItems"
                                            :key="index">
                                            <div :ref="`num${grid.sId}`"
                                                class="sno-item"
                                                :class="getItemClasses(grid)"
                                                :style="{ backgroundColor: configData.appointCardBgColor}"
                                                @click="openReservationInfo(grid)">
                                                <!-- <div class="patient-type"></div> -->
                                                <div class="patient-info" >
                                                    <div class="appoint-number">
                                                        <span v-if="grid.sPatientName" class="status"
                                                            style="width:100%;">
                                                            {{ grid.sPatientName }}
                                                        </span>
                                                        <span v-if="grid.iReserved == 1 && grid.checkeStatus == 0"
                                                            class="status"
                                                            style="width:100%;color:#999;">[预留]</span>
                                                        <el-checkbox v-if="grid.checkeStatus == 0 && ((reserveNumSourceVisible == true && grid.iReserved != 1) || (delNumSourceVisible === true))"
                                                            @change="selectNumSource(grid)"
                                                            @click.native.stop
                                                            style="position: absolute;left: 0; z-index: 1;"></el-checkbox>
                                                        <span v-if="grid.checkeStatus > 1" class="i-state">{{statusObject[grid.checkeStatus]}}</span>
                                                        <span v-if="grid.iPatientType" class="type">{{visitTypeObject[grid.iPatientType]}}</span>
                                                    </div>
                                                    <div class="name" v-if="false">
                                                        <span v-if="grid.checkeStatus != 0"
                                                            :style="{ width: grid.sNum ? '35%' : '50%' }">{{ grid.iPatientTypeName }}</span>
                                                        <!-- <span v-if="grid.iReserved == 1 && grid.checkeStatus == 0"
                                                            :style="{ width: grid.sNum ? '35%' : '50%' }">[预留]</span> -->
                                                        <span :style="{ width: grid.sNum ? '20%' : '0' }">{{ grid.sNum }}</span>
                                                        <span v-if="grid.checkeStatus != 0"
                                                            class="right-align"
                                                            :style="{ width: grid.sNum ? '45%' : '50%' }">{{ grid.checkeStatusName }}</span>
                                                    </div>
                                                    <div class="items"
                                                        :title="grid.sItemName">{{ grid.sItemName }}
                                                    </div>
                                                </div>
                                            </div>
                                        </el-col>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                        </el-scrollbar>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>

        <el-dialog
            v-model="addNumSourceVisible" title="加号"
            class="my-dialog t-default"
            width="600">
            <el-form ref="form"
                :model="addNumForm"
                label-width="60px">
                <el-form-item label="时间：">
                    <el-date-picker style="width:100%"
                        v-model="modelParams.date"
                        type="date"
                        disabled
                        placeholder="选择日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="时间：">
                    <el-radio-group v-model="addNumForm.time">
                        <el-radio v-for="(item, index) in numTimeArr"
                            :key="index"
                            :label="item">
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="个数："
                    prop="count">
                    <el-input-number v-model="addNumForm.count"
                        :min="1"
                        :max="5"></el-input-number>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button-icon-fa icon="fa fa-save" type="primary" @click="handleSaveAddNum">保存</el-button-icon-fa>
                <el-button-icon-fa icon="fa fa-close-1" @click="addNumSourceVisible = false">关闭</el-button-icon-fa>
            </template>
        </el-dialog>

        <!-- 留号 -->
        <div class="buttom-part"
            v-if="reserveNumSourceVisible">
            <span><strong>预留项目：</strong></span>
            <el-select style="width:320px"
                v-model="reserveNumForm.itemId"
                placeholder="请选择检查项目"
                clearable>
                <el-option v-for="item in itemDataArr"
                    :key="item.sId"
                    :label="item.sItemName"
                    :value="item.sId">
                </el-option>
            </el-select>
            <el-button-icon-fa icon="fa fa-save" style="margin-left:10px"
                type="primary"
                @click="saveReserveNumSouce"
                :loading="saveReserveNumLoading">保存</el-button-icon-fa>
            <el-button-icon-fa icon="fa fa-close-1" style="margin-left:10px"
                @click="reserveNumSourceVisible = false;reserveNumSource=[]">取消</el-button-icon-fa>
        </div>

        <!-- 删号 -->
        <div class="buttom-part"
            v-if="delNumSourceVisible">
            <el-button-icon-fa icon="fa fa-save" style="margin-left:10px"
                type="danger"
                @click="saveDelReserveNumSouce"
                :loading="delNumLoading">删除</el-button-icon-fa>
            <el-button-icon-fa icon="fa fa-close-1" style="margin-left:10px"
                @click="delNumSourceVisible = false;reserveNumSource=[]">取消</el-button-icon-fa>
        </div>

        <!-- 预约登记管理设置 -->
        <PanelConfigDialog v-model="d_manage_visible" :configKey="'ReservationModulesPenalConfig'"></PanelConfigDialog>
        <!-- 号源模板 -->
        <NumberSourceMng v-model="d_NumSourceVisible_v"></NumberSourceMng>
        <!-- 用药统计 -->
        <MedicineAppoint v-model="d_MedicineAppoint_v"></MedicineAppoint>
        <!-- 预约查询 -->
        <PatientQuery v-model="d_PatientQuery_v" @clickRecord="onClickRecord" ref="refPatientQuery"></PatientQuery>
        <!-- 打印设置 -->
        <PrintSet 
        :dialogVisible="d_printSet_v" 
        :iModuleId="iModuleId"
        @closeDialog="closePrintSetDialog"></PrintSet>
    </div>
</template>
<script setup>
    /**
     * 预约登记-患者日历
     */
    import NumberSourceMng from './components/NumberSourceMng.vue'
    import MedicineAppoint from './components/MedicineAppoint.vue'
    // import PatientQuery from './components/PatientQuery.vue'
    import PanelConfigDialog from './components/PanelConfigDialog.vue'

    const PatientQuery = defineAsyncComponent(() => import('./components/PatientQuery.vue'))
    const PrintSet = defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintSet.vue'))

    import { queryNumSource, queryTimeByRoomId, addNumSource, reserveNumSource as apiReserveNumSource, generateNumSource, removeNumSource } from '$supersetApi/projects/apricot/appointment/index.js'
    import { getItemData } from '$supersetApi/projects/apricot/appointment/projectSet.js'

    import { ElMessage } from 'element-plus'
    import { transformDate } from '$supersetResource/js/tools'

    import { useNumberSource } from './useHighlightNumSource.js'
    import useUserConfig from './useUserConfig.js'
    
    import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'

    const visitTypeObject = appointmentEnum.visitTypeOptions.reduce((result, item) => {
        result[item.sValue] = item.sName[0];
        return result;
    }, {});
    const statusObject = {
        '1': '约',
        '2': '签',
        '3': '检',
    }

    const props = defineProps({
        modelValue: Object,
    })
    const emits = defineEmits(['update:modelValue', 'onReservationInfo'])

    const iModuleId = ref(2)  // 预约登记标识
    const refPatientQuery = ref(null)
    defineExpose({
        refPatientQuery
    })

    const { configData } = useUserConfig()

    //  打开打印设置
    const d_printSet_v = ref(false)
    function onOpenPrintSet () {
        d_printSet_v.value = true
    }
    function closePrintSetDialog () {
        d_printSet_v.value = false
    }

    // 打开预约查询弹窗
    const d_PatientQuery_v = ref(false)
    // const patientQueryComponent = ref(false)
    function handlePatientQueryClick () {
        d_PatientQuery_v.value = true
        // patientQueryComponent.value = true
    }

    // 打开药物预约
    const d_MedicineAppoint_v = ref(false)
    function onMedicineAppoint() {
        d_MedicineAppoint_v.value = true
    }

    // 打开号源管理
    const d_NumSourceVisible_v = ref(false)
    function openNumSourceMng() {
        d_NumSourceVisible_v.value = true
    }

    const d_manage_visible = ref(false)

    // 统计数量
    const patientCount = ref({})
    // 预约
    const activeName = ref('morning')
    // 号源数组
    const numberSources = reactive([
        { type: 'morning', typeName: '上午', numObj: { registered: 0, limit: 0 } },
        { type: 'afternoon', typeName: '下午', numObj: { registered: 0, limit: 0 } },
        { type: 'night', typeName: '晚间', numObj: { registered: 0, limit: 0 } }
    ])
    const { higlightId } = useNumberSource()

    const loading = ref(false)

    const modelParams = computed({
        get: function () {
            return props.modelValue
        },
        set: function (val) {
            emits('update:modelValue', val)
        }
    })
    // 数据发生变化，获取新号源
    watch(() => modelParams.value, (later) => {
        if (!later.date) {
            return
        }
        getNumSource()
    }, { immediate: true, deep: true })
    // 获取号源
    function getNumSource () {
        const val = modelParams.value
        if (!val.roomId) {
            return
        }
        loading.value = true
        let params = {
            date: transformDate(val.date, false, ''),
            roomId: val.roomId,
            hospitalDistrictId: val.hospitalId
        }
        queryNumSource(params).then((res) => {
            loading.value = false
            if (res.success) {
                patientCount.value = {
                    outpatientCount: res.data.outpatientCount, // 门诊统计
                    inhospitalCount: res.data.inhospitalCount, // 住院
                    emergencyCount: res.data.emergencyCount, //  急诊
                    examinationCount: res.data.examinationCount,// 体检
                    otherCount: res.data.otherCount, //其他
                    registered: res.data.registered
                }
                numberSources[0].numObj = res.data.morning;
                numberSources[1].numObj = res.data.afternoon;
                numberSources[2].numObj = res.data.night;
                selectHiglight()
                return
            }
            ElMessage.error(res.msg)
        }).catch(err => {
            loading.value = false
        }
        )
    }

    function selectHiglight() {
        if (!higlightId.value) {
            return
        }
        const higItem = numberSources.find(item => item.numObj?.hourItems.find(hourItem => hourItem.gridItems.find(grid => grid.sId === higlightId.value)))
        if (higItem) {
            activeName.value = higItem.type
        }
        setTimeout(() => {
            higlightId.value = ''
        }, 600);
    }

    // 日历样式
    const getItemClasses = (grid) => {
        return {
            'out-patient': grid.iPatientType === 1,
            'emergency-patient': grid.iPatientType === 2,
            'physical-patient': grid.iPatientType === 3,
            'hospital-patient': grid.iPatientType === 4,
            'others': grid.iPatientType === 5 || grid.iPatientType === 9,
            'reserve': grid.iReserved === 1,
            'higlight': grid.sId === higlightId.value
            // 'is-active': grid.sId === selectNumItem.value?.sId,
            // 'is-suggest': grid.sId === suggestTimeItem.value?.sId
        }
    }
    // 点击日历
    function openReservationInfo(info) {
        emits('onReservationInfo', info)
    }

    function onClickRecord(info) {
        info.sPatientId = info.sId
        openReservationInfo(info)
    }

    //--生成号源--// 
    // 生成号源按钮控制
    const disGenerateNumSource = computed(() => {
        if (numberSources[0].numObj.limit != 0 || numberSources[1].numObj.limit != 0 || numberSources[2].numObj.limit != 0) {
            return true
        } else {
            return false
        }
    })
    // 生成号源
    function saveGenerateNumSource () {
        let params = {
            date: transformDate(modelParams.value.date, false, ''),
            roomId: modelParams.value.roomId,
            hospitalDistrictId: modelParams.value.hospitalId
        }
        generateNumSource(params).then((res) => {
            if (res.success) {
                ElMessage.success('成功生成号源！')
                getNumSource()
                // 刷新
                modelParams.value.refresh = !modelParams.value.refresh
                return
            }
            ElMessage.error(res.msg)
        })
    }


    //--加号--// 
    // 新增号源表单，
    const addNumSourceVisible = ref(false)
    const addNumForm = reactive({ count: 1 })
     // 时间段集合
    const numTimeArr = ref([])

    function openAddNumSouce() {
        addNumSourceVisible.value = !addNumSourceVisible.value
        if (addNumSourceVisible.value) {
            getTimeByRoomId(modelParams.value)
        }
    }

    // 点击保存加号
    function handleSaveAddNum() {
        if (!addNumForm.time) {
            ElMessage.error('请选择加号时间！')
            return
        }
        if (!addNumForm.count) {
            ElMessage.error('请输入加号个数！')
            return
        }
        let params = {
            date: transformDate(modelParams.value.date, false, ''),
            time: addNumForm.time,
            templateId: addNumForm.templateId,
            count: addNumForm.count
        }

        
        addNumSource(params).then(res => {
            if (res.success) {
                ElMessage.success('新增号源成功！')
                modelParams.value.refresh = !modelParams.value.refresh
                return
            }
            ElMessage.error(res.msg);
        }).catch(err => {
            console.log(err)
        })
        addNumSourceVisible.value = false
    }
    // 获取加号时间段
    function getTimeByRoomId (val) {
        let params = {
            roomId: val.roomId,
            hospitalDistrictId: val.hospitalId,
        }
        queryTimeByRoomId(params).then(res => {
            if (res.success) {
                numTimeArr.value = res.data.times
                addNumForm.templateId = res.data.templateId
                return
            }
            ElMessage.error(res.msg);
        }).catch(err => {
            console.log(err)
        })
    }


    //--留号--//
    // 打开预留号源
    const reserveNumSourceVisible = ref(false)
    const saveReserveNumLoading = ref(false)
    const selectNumItem = ref({})
    const reserveNumForm = ref({ itemId: null })
    const itemDataArr = ref([])
    const reserveNumSource = ref([]) // 选中号源

    function openReNumSouceDialog () {
        reserveNumSourceVisible.value = !reserveNumSourceVisible.value
        selectNumItem.value = {}       // 取消单击号源选中
        nextTick(() => {
            handleGetItemData(modelParams.value.roomId)
        })
    }
    // 选中日期
    function selectNumSource(item) {
        let reserveNum = reserveNumSource.value;
        let idx = null;
        reserveNum.find((detail, index) => {
            if (detail.sId === item.sId) {
                idx = index;
            }
        });
        if (idx !== null) {
            reserveNum.splice(idx, 1);
            return
        }
        reserveNumSource.value.push(item)
    }
    // 保存留号
    async function saveReserveNumSouce() {
        if (!reserveNumForm.value.itemId) {
            ElMessage.error('请选择预留项目！')
            return
        }
        if (reserveNumSource.value.length == 0) {
            ElMessage.error('请选择预留时间！')
            return
        }
        let arr = []
        reserveNumSource.value.forEach(item => {
            arr.push({
                numSourceId: item.sId,
                itemId: reserveNumForm.value.itemId
            })
        })
        let isResFail = false
        let successArr = []
        let failItem = null
        for (let i = 0; i < arr.length; i++) {
            if (isResFail) {
                break
            }
            await apiReserveNumSource(arr[i]).then((res) => {
                if (!res.success) {
                    ElMessage.error(res.msg)
                    isResFail = true
                    failItem = arr[i]
                    return
                } else {
                    successArr.push(arr[i])
                }
            })
        }
        if (successArr.length == arr.length) {
            ElMessage.success('预留号源成功!')
            getNumSource()
        }
        if (failItem) {
            if (successArr.length > 0) {
                this.getNumSource()
            }
            this.$refs[`num${failItem.numSourceId}`][0].style.backgroundColor = '#e9aaaa'
            this.$refs[`num${failItem.numSourceId}`][0].style.animation = 'fade 3000ms infinite ease-in-out'
            // console.log(this.$refs[`num${failItem.numSourceId}`][0].style)
            setTimeout(() => {
                this.$refs[`num${failItem.numSourceId}`][0].style.backgroundColor = ''
                this.$refs[`num${failItem.numSourceId}`][0].style.animation = ''
            }, 5000)
            // console.log(this.$refs[`num${failItem.numSourceId}`][0].style)
        }
        reserveNumSource.value = []
        reserveNumSourceVisible.value = false
    }
    // 获取项目
    function handleGetItemData (val) {
        saveReserveNumLoading.value = true
        let params = {
            condition: { roomId: val, iIsEnable: 1 },
            page: {  pageCurrent: 1, pageSize: 9999 }
        }
        getItemData(params).then((res) => {
            if (res.success) {
                itemDataArr.value = res.data || [];
                return
            }
            ElMessage.error(res.msg)
        }).catch((err) => {
            console.log(err)
        }).finally(() => {
            saveReserveNumLoading.value = false
        })
    }

    /** 删除号源 **/
    const delNumSourceVisible = ref(false)
    const delNumLoading       = ref(false)

    // 打开删除号源界面
    function openDelNumSouceDialog() {
        delNumSourceVisible.value = ! delNumSourceVisible.value
    }
    // 保存删除号源
    function saveDelReserveNumSouce() {
        if (reserveNumSource.value.length == 0) {
            ElMessage.error('请选择号源')
            return
        }
        delNumLoading.value = true

        let numSourceId = []

        reserveNumSource.value.forEach(item => {
            numSourceId.push(item.sId)
        })

        removeNumSource( numSourceId ).then(res => {
            if (res.success) {
                ElMessage.success(res.msg)
                getNumSource()
            }else {
                ElMessage.error(res.msg)
            }
        }).finally(() => {
            delNumLoading.value = false
        })

        reserveNumSource.value = []
        delNumSourceVisible.value = false
    }

</script>
<style lang="scss" scoped>
.container-top {
    position: relative;
    // padding: 12px;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background: white;
    display: flex;
    flex-direction: column;
    .up-btn-part {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;

        padding: 10px;
        box-sizing: border-box;
        .text {
            font-size: 16px;
            font-weight: bold;
        }
    }
    :deep(.el-tabs__header) {
        margin: 0;
        background: #FAFAFA;
        .el-tabs__nav-wrap {
            padding-left: 5px;
        }
    }
    .el-tab-pane {
        // background: #EDF3F7;
    }
    .appointment-part {
        position: relative;
        flex: 1;
        overflow: hidden;
        box-sizing: border-box;
        .summary {
            position: absolute;
            top: 0px;
            right: 4px;
            z-index: 1;
            height: 40px;
            line-height: 39px;

            span {
                padding: 0 8px;
                // font-weight: 600;
                vertical-align: middle;
            }

            span.row-col {
                margin-left: 20px;
                cursor: pointer;
                font-weight: 500;
            }
        }
    }
}
.mx-padding {
    // padding: 10px 5px 5px 5px;
    overflow: hidden;
    box-sizing: border-box;
}
.appoint-list {
    position: relative;

    // overflow: auto;  第一方案
    .sno-time {
        width: 40px;
        position: absolute;
        top: 10px;
        left: 5px;
        border-right: 1px solid #edeeef;
        height: 100%;
        font-weight: 600;
    }

    .sno-sourse {
        padding-left: 40px;
    }
}
.sno-item {
    display: flex;
    // 第一方案
    // float: left;
    // width: 160px;
    // margin-bottom: 10px;
    // 第二方案
    // animation:fade 2000ms infinite ease-in-out;
    // width: 100%;
    // box-shadow: 0px 1px 4px rgba(175, 175, 175, 0.3);
    background-color: #d9eae8;
    border-radius: 4px;
    box-sizing: border-box;
    height: 60px;
    border: 1px solid transparent;
    cursor: pointer;
    // margin: 5px 1px 14px 7px;
    margin: 0 0 15px 0;
    border: 1px solid #ddd;

    .patient-type {
        width: 1px;
        height: 36px;
        margin-left: 0px;
        margin-top: 0px;
        border-radius: 4px 0px 0px 4px;
        box-sizing: border-box;
    }

    .patient-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-left: 6px;
        width: calc(100% - 3px);
        padding: 5px 8px 8px;
        box-sizing: border-box;
        
    }

    .appoint-number {
        line-height: 1.5;
    }
    // .name {
    //     position: relative;
    //     white-space: nowrap;
    //     color: #222;
    //     padding-top: 6px;
    //     line-height: 1.2;
    //     span {
    //         display: inline-block;
    //         width: 50%;
    //         white-space: nowrap;
    //         overflow: hidden;
    //         text-overflow: ellipsis;
    //     }

    //     span.time,
    //     span.right-align {
    //         padding-right: 5px;
    //         text-align: right;
    //         font-size: 14px;
    //         color: #868788
    //     }
    // }

    .status {
        font-size: 15px;
        font-weight: bold;
        color: #333;
        padding-right: 50px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .type { 
        position: absolute;  
        right: 0px; 
        top: 0;
        display: inline-block; 
        width: auto;
        padding: 4px;
        box-sizing: border-box;
        border-radius: 100%; 
        line-height: 1;
        font-size: 12px;
        color: #fff;
    }
    .i-state {
        display: inline-block;
        position: absolute;
        width: auto;
        right: 26px;
        top: 1px;
        border: 1px solid #999;
        color: #999;
        border-radius: 6px;
        box-sizing: border-box;
        padding: 3px;
        line-height: 1;
        font-size: 12px;
    }

    .items {
        color: #868788;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    &.higlight {
        animation: flash-animation 0.8s infinite;
    }
}
@keyframes flash-animation {
    0% {
        box-shadow: 0 0 2px #fff;
    }
    50% {
        box-shadow: 0 0 5px #0d9488, 0 0 10px #0d9488;
    }
    100% {
        box-shadow: 0 0 2px #fff;
    }
}
.sno-item:hover {
    border-color: var(--el-color-primary-light-5);
    .patient-type {
        border-right: none;
        height: 36px;
        left: 1px;
        top: 1px;
    }
}

.sno-item.is-active {
    border-color: var(--el-color-primary-light-5);

    .patient-type {
        border: 1px solid var(--el-color-primary-light-5);
        border-right: none;
    }
}
.sno-item.is-suggest {
    background-color: #bddaf1;
}

.sno-item.out-patient {
    .patient-type {
        background-color: #a0d4c3;
    }

    // .status {
    //     color: #5fb397;
    // }

    .name {
        color: #5fb397;
        
    }
    .type {
        background-color: #5fb397;
        border: 1px solid #5fb397;
    }
}

.sno-item.hospital-patient {
    .patient-type {
        background-color: #6da4da;
    }

    // .status {
    //     color: #4986c2;
    // }

    .name {
        color: #4986c2;
    }
    .type {
        background-color: #4986c2;
        border: 1px solid #4986c2;
    }
}

.sno-item.emergency-patient {
    .patient-type {
        background-color: #eabe7f;
    }

    // .status {
    //     color: #e2ab60;
    // }

    .name {
        color: #e2ab60;
    }
    .type {
        background-color: #e2ab60;
        border: 1px solid #e2ab60;
    }
}

.sno-item.physical-patient {
    .patient-type {
        background-color: #e3a5a8;
    }

    // .status {
    //     color: #cf797e;
    // }

    .name {
        color: #cf797e;
    }
    .type {
        background-color: #cf797e;
        border: 1px solid #cf797e;
    }
}

.sno-item.reserve {
    .patient-type {
        background-color: rgb(204, 192, 207);
    }

    // .status {
    //     color: #e71520;
    // }

    .name {
        color: #333;
    }
    .type {
        background-color: #e71520;
        border: 1px solid #e71520;
    }
}

.sno-item.others {
    .patient-type {
        background-color: rgb(208, 208, 215);
    }

    // .status {
    //     color: #808088;
    // }

    .name {
        color: #808088;
    }
    .type {
        background-color: #808088;
        border: 1px solid #808088;
    }
}
// buttom-part 留号
.buttom-part {
    padding: 10px;
    box-shadow: 0px 1px 10px rgba(28, 32, 35, 0.4);
    position: absolute;
    left: 0;
    bottom: 0;
    background-color: #fff;
    width: 100%;
    z-index: 2;
    text-align: center;
}

.i-height {
    height: 20px;
    line-height: 19px;
}
// 住院颜色
.blue {
    color: #4986c2;
}

// 门诊颜色
.green {
    color: #5fb397;
}

// 急诊颜色
.orange {
    color: #e7af63;
}

// 体检颜色{}
.red {
    color: #cf797e;
}

// 其他
.gray {
    color: #808088;
}
</style>
