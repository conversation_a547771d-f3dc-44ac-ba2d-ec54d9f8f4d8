<template>
    <span></span>

    <el-dialog append-to-body
        title="呼叫列表"
        v-model="visible"
        :close-on-click-modal="false"
        fullscreen
        @close="closeDialog"
        class="my-dialog my-full-dialog">
        <div v-loading="loading" class="g-content m-flexLaout-ty">
            <el-row>
                <el-col :span="6"
                    v-for="(parent, index) in targetObj"
                    :key="index">
                    <div class="m-flexLaout-ty">
                        <h4 class="c-title">{{parent.group}}</h4>
                        <div class="g-flexChild c-content">
                            <template v-for="(children, idx) in parent.arr">
                                <div class="c-item undraggable"
                                    :key="idx"
                                    v-if="!children.isDragge">
                                    <!-- <div class="i-text"
                                        :class="{'i-text-red': children.key === '延'}">{{children.sCallScreenNo}}</div> -->
                                    
                                    <span style="display: inline-block;margin-right: 15px">{{children.sCallScreenNo}}</span>
                                    <span>{{children.sName}}</span>
                                </div>
                            </template>
                            <draggable v-model="parent.arr"
                                item-key="id"
                                group="site"
                                animation="300"
                                dragClass="dragClass"
                                ghostClass="ghostClass"
                                chosenClass="chosenClass"
                                @start="onStart(parent.arr)"
                                @end="onEnd(parent.arr)">
                                <!-- :move="onMove" -->
                                <!-- filter=".undraggable" -->
                                <!-- :class="{'undraggable': !children.isDragge}" -->
                                <transition-group :style="style">
                                  <template #item="{ element: children }">
                                        <div class="c-item" 
                                            v-if="children.isDragge">
                                            <div class="i-text"
                                                :class="{'i-text-red': children.key === '延'}">{{children.key}}</div>
                                            <span>{{children.sName}}</span>
                                        </div>
                                  </template>
                                </transition-group>
                            </draggable>
                        </div>
                    </div>
                </el-col>
            </el-row>
        </div>
        <template #footer><div 
            class="my-dialog-footer">
            <div class="g-page-footer">
                <el-button-icon-fa                     :loading="loading"
                    type="primary"
                    _icon="fa fa-refresh"
                    size="small" @click="callScreenCallList">刷 新</el-button-icon-fa>
            </div>
        </div></template>
    </el-dialog>
</template>
<script>
import draggable from 'vuedraggable';
import { callScreenCallList } from '$supersetApi/projects/apricot/common/callSet.js';
export default {
    name: 'MyVoice',
    components: {
        draggable
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            visible: false,
            drag: false,
            targetObj: {
                waitConsultList: {
                    group: '等待问诊',
                    arr: [
                        // { sName: '王麻子', key: '1', isDragge: false }, { sName: '王麻子', key: '2', isDragge: false },   
                    ]
                },
                waitInjectList: {
                    group: '等待注射',
                    arr: [
                        // { sName: '王麻子', key: '1', isDragge: false }, { sName: '王麻子', key: '2', isDragge: false },
                    ]
                },
                waitMachineList: {
                    group: '等待检查',
                    arr: [
                        // { sName: '王麻子', key: '延', isDragge: false }
                    ]
                },
                onMachineList: {
                    group: '检查中',
                    arr: [
                        // { sName: '王麻子', key: '延', isDragge: false }
                    ]
                }
            },
            loading: false,
            style: 'min-height: 200px; display: block;'
        }
    },
    computed: {
    },
    watch: {
        dialogVisible (val) {
            this.visible = val;
            if(val) {
                this.callScreenCallList();
            }
        },
    },
    methods: {
        closeDialog () {
            this.$emit('update:dialogVisible', false);
        },

        onStart () {
            this.drag = true;
        },
        onEnd () {
            this.drag = false;
            this.$nextTick(() => {
                console.log(this.targetObj)
            })
        },
        callScreenCallList() {
            this.loading = true;
            callScreenCallList().then(res => {
                this.loading = false;
                if(res.success) {
                    let data = res.data || {};
                    Object.keys(data).map(key => {
                        console.log(key)
                        this.targetObj[key].arr = data[key] || [];
                    })
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading = false;
                console.log(err)
            })
        }
    },
    mounted () {

    },
}
</script>
<style lang="scss" scoped>
:deep(.my-full-dialog.el-dialog.is-fullscreen) {
    width: 80%;
    height: 80%;
    margin-top: 5%;
    .el-dialog__body {
        height: calc(100% - 100px);
    }
    .my-dialog {
        overflow: hidden;
        .el-dialog__body {
            overflow: auto;
            padding: 10px 0px;
        }
    }
    .g-content {
        padding: 5px;
    }
}
/*定义要拖拽元素的样式*/
.ghostClass {
    background-color: #2384d3 !important;
}

.chosenClass {
    background-color: #e6f0f8 !important;
    opacity: 1 !important;
}

.dragClass {
    background-color: #e6f0f8 !important;
    opacity: 1 !important;
    box-shadow: none !important;
    outline: none !important;
    background-image: none !important;
}
.el-row {
    height: 100%;
    border: 1px solid #eee;
    background: #f1f1f1;
    .el-col {
        height: 100%;
        &:not(:last-child) {
            border-right: 1px solid #fff;
        }
    }
    .c-title {
        margin: 0;
        padding: 15px;
        background-color: #4c9ad9;
        color: #fff;
        text-align: center;
    }
    .c-content {
        padding: 15px;
        overflow-x: hidden;
        .i-text {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 1px solid #333;
            border-radius: 50%;
            text-align: center;
            margin-right: 5px;
        }
        .i-text-red {
            color: red;
        }
    }
    .c-item {
        padding: 15px;
        margin: 0px 10px;
        background-color: #fff;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.12), 0 0 3px rgba(0, 0, 0, 0.04);
        color: #333;
        &:hover {
            background-color: #bddaf2;
            cursor: move;
        }
        &.undraggable {
            background-color: #e3e3e3;
            margin-bottom: 15px;
            cursor: default;
        }
    }
    .c-item + .c-item {
        border-top: none;
        margin-top: 15px;
    }
}
</style>
