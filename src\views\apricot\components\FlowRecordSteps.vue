<template>
  <div class="container w-full " :class="isShowListFlowRecord ? 'showing' : 'hiding'" >
    <el-steps v-if="isShowListFlowRecord" :active="computedActiveIndex" finish-status="success" 
        process-status="wait" align-center :space="140">
      <el-step v-for="(item, index) in flowData" :key="index" :status="item.flag ? 'finish' : 'wait'">
        <template v-slot:icon>
          <el-icon size="14">
            <CircleCheckFilled v-if="!!item.flag" />
            <RemoveFilled v-else />
          </el-icon>
        </template>
        <template v-slot:title>
            {{ item.operation }}
          <!-- <el-tooltip placement="right-start" :persistent="false">
            {{ item.operation }}
            
            <template  #content>
              <div class="r">
                操作人：{{ !!item.flag ? item.operator : '无' }}
              </div>
              <div class="r">
                {{ transformDate(item.operateTime) }}
              </div>
            </template>

          </el-tooltip> -->
        </template>
        <template #description>
            <div style="margin-top: 10px;"> {{ !!item.flag && item.operator ? item.operator : '- -' }}</div>
            <div>{{ !!item.flag && item.operateTime ? transformDate(item.operateTime) : '- -' }}</div>
        </template>
      </el-step>
    </el-steps>

  </div>
</template>
<script>
import { CircleCheckFilled, RemoveFilled } from '@element-plus/icons-vue'

import { flowRecordInfo } from '$supersetApi/projects/apricot/common'
export default {
  name: 'FlowRecordSteps',
  mixins: [],
  props: {
    patientInfo: {
      type: Object,
      default: () => null
    },
  },
  components: {
    CircleCheckFilled, RemoveFilled
  },
  data() {
    return {
      visible: false,
      loading1: false,
      tableData: [],
      flowData: [],
      isShowListFlowRecord: true
    }
  },
  computed: {
    computedActiveIndex() {
      return this.flowData.findIndex(item => item.flag != 1)
    }
  },
  created() {
    this.flowRecordInfo();

  },
  methods: {
    transformDate(time) {
        return moment(time).format('YYYY-MM-DD HH:mm')
    },
    flowRecordInfo() {
      if (!this.patientInfo) {
        return
      }

      const storageString = window.localStorage.getItem('oAutoSaveTime');
      let storageObj = {}
      try {
          const parsed = JSON.parse(storageString || '{}')
          Object.assign(storageObj, parsed)
        } catch (error) {
          console.error(error)
        }

        this.isShowListFlowRecord = !!storageObj.isShowListFlowRecord
      if (
        !this.isShowListFlowRecord
      ) {
        return
      }

      this.loading1 = true;

      flowRecordInfo({ sPatientId: this.patientInfo.sId }).then(res => {
        this.loading1 = false;
        this.flowData = [];
        if (res.success) {
          this.flowData = res.data || [];
          if (this.flowData.length) {
            this.flowData = this.flowData.filter(item => item.flag !== null)
          }
          return
        }
        this.$message.error(res.msg);
      }).catch(() => {
        this.loading1 = false;
      })
    },
  }
}
</script>
<style lang="scss" scoped>
.container {
  .showing {
    height: 90px;
  }
  .hiding {
    height: 0px;
  }
  :deep(.el-step__title) {
    font-size: 14px;
    line-height: 1;
  }
  :deep(.el-step__icon) {
    height: 20px;
  }
  :deep(.el-step.is-horizontal .el-step__line) {
    top: 9px;
  }
  :deep(.el-step.is-center .el-step__description) {
    padding: 0;
  }
}
</style>
