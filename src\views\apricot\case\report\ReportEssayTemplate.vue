<template>
    <DragAdjust class="g-template" :dragAdjustData="oTemplateEssayConfig.isHideQuill ? oneDA0 : twoDA0" hasDragBorder>
        <template v-slot:c1>
            <div class="w-full h-full flex flex-col">
                <div class="flex p-2.5  bt-1px">
                    <el-input class="flex-1" size="small" placeholder="搜索" v-model="filetText">
                        <template #suffix>
                            <i class="el-icon-search el-input__icon"></i>
                        </template>
                    </el-input>
                    <el-popover placement="bottom" width="300" trigger="click" v-model:visible="isShowPopover">
                        <p class="c-popover-title">设置</p>
                        <div style="padding: 10px">
                            <el-checkbox v-model="oTemplateEssayConfig.isFilterDevice" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isFilterDevice')">自动过滤设备类型</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isFilterProject" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isFilterProject')">自动过滤检查项目</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isFilterSex" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isFilterSex')">自动过滤性别</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isPrivateFirst" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isPrivateFirst')">优先显示个人模板</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isHidePublic" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isHidePublic')">隐藏公共模板</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isHidePrivate" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isHidePrivate')">隐藏个人模板</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isHideQuill" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isHideQuill')">隐藏模板内容</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isHideQuillToolbar" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isHideQuillToolbar')">隐藏编辑器工具栏</el-checkbox>
                            <el-checkbox v-model="oTemplateEssayConfig.isAllExpand" class="ml-2 mb-2.5"
                                @change="() => onCacheChange('isAllExpand')">全部展开目录</el-checkbox>
                        </div>
                        <div class="text-right">
                            <el-button-icon-fa icon="fa fa-close-1" size="small"
                                @click="isShowPopover = false">关闭</el-button-icon-fa>
                        </div>
                        <template #reference>
                            <el-button link class="ml-2" title="过滤器">
                                <el-icon size="18">
                                    <i class="fa fa-ellipsis-v"></i>
                                </el-icon>
                            </el-button>
                        </template>
                    </el-popover>

                    <div v-if="oTemplateEssayConfig.isHideQuill">
                        <el-button type="primary" link size="small" title="关闭" @click="$emit('closeClick')">
                            <el-icon size="22"><i class="el-icon-circle-close"></i></el-icon>
                        </el-button>
                    </div>

                </div>
                <div class="flex flex-col flex-1" v-loading="loading">
                    <DragAdjust class="w-full h-full" :dragAdjustData="computedDA1" hasDragBorder>
                        <template v-slot:[`c${index}`] v-for="(item, index) in filterList">
                            <div class="w-full h-full flex flex-col">
                                <h5 class="i-essay-title">{{ item.sNodeName }}</h5>
                                <!--  :expand-on-click-node="false" -->
                                <div class="flex-1" style="overflow: auto;">
                                    <el-tree :data="item.childList" :props="treeProps" :ref="`dTree${index}`"
                                        node-key="sNodeId" highlight-current v-contextmenu:contextmenuDiv
                                        :filter-node-method="filterNode" oncontextmenu='return false'
                                        :default-expanded-keys="expandKeys[`idx${index}`]" :draggable="!filetText.length && !isOperateNode"
                                        :allow-drop="allowDrop" @node-drop="handleNodeDrop"
                                        @node-expand="(data) => handleNodeTrigger(data, true, index)"
                                        @node-collapse="(data) => handleNodeTrigger(data, false, index)"
                                        @node-click="(data, node, component,) => handleTreeClick(data, node, component, index)"
                                        @node-contextmenu="(ev, data) => handleTreeContextmenu(ev, data, index)"
                                        style="margin-bottom: 10px;">
                                        <template #empty>
                                            <!-- 空数据 -->
                                            <el-empty :image-size="60" description=" " />
                                        </template>
                                        <template v-slot="{ node, data }">
                                            <span v-if="!data.isEdit">
                                                <i v-if="data.iNodeType != 3" class="el-icon-folder-opened"></i>
                                                <i v-if="data.iNodeType == 3" class="el-icon-document"></i>
                                                {{ data.sNodeName }}
                                            </span>
                                            <div v-else class="flex" :class="{ 'i-bold': data.iNodeType == 3 }">
                                                <i v-if="data.iNodeType != 3" class="el-icon-folder-opened i-icon"></i>
                                                <i v-if="data.iNodeType == 3" class="el-icon-document i-icon"></i>
                                                <el-input v-model="data.sNodeName" class="flex-1" size="small"></el-input>
                                                <el-button type="primary" size="small" link
                                                    @click="onSaveNodeClick(node, data, index)" style="margin-left: 5px;">
                                                    <el-icon size="20">
                                                        <i class="el-icon-check"></i>
                                                    </el-icon>
                                                </el-button>
                                                <el-button type="primary" size="small" link
                                                    @click="onCancelNodeClick(node, data, index)">
                                                    <el-icon size="20">
                                                        <i class="el-icon-close"></i>
                                                    </el-icon>
                                                </el-button>
                                            </div>
                                        </template>
                                    </el-tree>
                                </div>
                            </div>
                        </template>
                    </DragAdjust>
                    <!-- 右键选项 -->
                    <v-contextmenu ref="contextmenuDiv">
                        <v-contextmenu-item v-if="rightTreeData.iNodeType != 3" class="i-border" @click="addNodeClick(2)">
                            <div class="p-1"><i class="mr-1.5 el-icon-circle-plus-outline"></i>添加目录</div>
                        </v-contextmenu-item>
                        <v-contextmenu-item v-if="rightTreeData.iNodeType != 3" class="i-border" @click="addNodeClick(3)">
                            <div class="p-1"><i class="mr-1.5 el-icon-circle-plus-outline"></i>添加模板</div>
                        </v-contextmenu-item>
                        <v-contextmenu-item v-if="rightTreeData.iNodeType != 1" class="i-border" @click="renameNodeClick">
                            <div class="p-1"><i class="mr-1.5 el-icon-edit-outline"></i>重命名</div>
                        </v-contextmenu-item>
                        <v-contextmenu-item v-if="rightTreeData.iNodeType == 3" class="i-border" @click="copyNodeClick">
                            <div class="p-1"><i class="mr-1.5 el-icon-copy-document"></i>复&emsp;制</div>
                        </v-contextmenu-item>
                        <v-contextmenu-item v-if="rightTreeData.iNodeType != 1 && rightTreeData.sNodeId" class="i-border"
                            @click="onDeleteNodeClick">
                            <div class="p-1"><i class="mr-1.5 el-icon-delete"></i>删&emsp;除</div>
                        </v-contextmenu-item>
                    </v-contextmenu>
                </div>
            </div>
        </template>
        <template v-slot:c2>
            <div class="w-full h-full flex flex-col">
                <div class="flex p-2.5 bt-1px justify-between">
                    <div class="flex flex-1 items-center" style="margin-right: 18px;">
                        <div class="mr-2.5 w-1/2 flex items-center">
                            <label for="">模板名称：</label>
                            <el-input v-model="form.sNodeName" :disabled="!form.sNodeId" class="flex-1" size="small"
                                clearable></el-input>
                        </div>
                        <!-- <div class="mr-2.5 w-1/2 flex items-center">
                            <label for="">所属目录：</label>
                            <el-cascader v-model="form.parentIds" ref="cascader" class="flex-1" size="small" placeholder=" "
                                :disabled="!form.sNodeId" :options="filterDirectory" @change="handleParentIdsChange" :props="cascaderProps" />
                        </div> -->
                        <div class="mr-2.5 w-1/3 flex items-center">
                            <label for="">项目：</label>
                            <el-select v-model="form.sItemIds" :disabled="!form.sNodeId" class="flex-1" 
                                size="small" placeholder="" clearable multiple collapse-tags collapse-tags-tooltip>
                                <template v-for="item in optionsLoc.projectOptions">
                                    <el-option v-if="form.sDeviceTypeId === item.sDeviceTypeId" :key="item.sId" :label="item.sItemName"
                                    :value="item.sId">
                                    </el-option>
                                </template>
                            </el-select>
                        </div>
                        <div class="mr-2.5 w-1/4 flex items-center">
                            <label for="">性别：</label>
                            <el-select class="c-select flex-1" v-model="form.sSex" placeholder=" " size="small" clearable
                                :disabled="!form.sNodeId">
                                <el-option v-for="item in optionsLoc.SexType" :key="item.sValue" :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                    <div>
                        <el-button type="primary" link size="small" title="关闭" @click="$emit('closeClick')">
                            <el-icon size="22"><i class="el-icon-circle-close"></i></el-icon>
                        </el-button>
                    </div>
                </div>
                <div class="flex-1">
                    <DragAdjust :dragAdjustData="computedDA2" v-if="computedDA2.visible" class="m-editor-parent">
                        <template v-for="(item, index) in elementReportForm" v-slot:[item.name]>
                            <div class="c-box-content" :key="index">
                                <h5>
                                    <span>{{ item.sLabel }}</span>
                                </h5>
                                <div class="c-box" :style="{ backgroundColor: themeColor }">
                                    <quill-editor v-model="form[item.sProp]" :ref="`myQuillEditor${index}`"
                                        :domParams="{idx:index, superClass: 'm-editor-parent'}"
                                        :class="{ 'u-none-toolbar': oTemplateEssayConfig.isHideQuillToolbar }"
                                        storageKey="ReportIndexEditor" :iModuleId="6"
                                        :disabled="!form.sNodeId" @click.native="onClickQuill(1, item.sProp)">
                                    </quill-editor>
                                </div>
                                <div class="c-button flex-none">
                                    <!-- el-icon-document-copy fa-line-left-hide fa-plus-square-o -->
                                    <el-button size="small"
                                        @click="handleEssayClick(1, item.sProp, index)">覆盖</el-button>
                                    <el-button size="small"
                                        @click="handleEssayClick(2, item.sProp, index)">插入</el-button>
                                    <el-button size="small"
                                        @click="handleEssayClick(3, item.sProp, index)">追加</el-button>
                                </div>
                            </div>
                        </template>
                    </DragAdjust>
                </div>
                <div class="c-actions flex">
                    <div v-if="form.isChangeDir" class="mr-2.5 w-1/2 flex items-center">
                        <label for="">所属目录：</label>
                        <el-cascader v-model="form.parentIds" ref="cascader" class="flex-1 text-left" size="small" placeholder=" "
                            :disabled="!form.sNodeId" :options="filterDirectory" @change="handleParentIdsChange" :props="cascaderProps" />
                            <el-button type="primary" size="small" link
                                @click="onSaveContentClick" style="margin-left: 5px;">
                                <el-icon size="20">
                                    <i class="el-icon-check"></i>
                                </el-icon>
                            </el-button>
                            <el-button type="primary" size="small" link
                                @click="onChangeNodeDir(false)">
                                <el-icon size="20">
                                    <i class="el-icon-close"></i>
                                </el-icon>
                            </el-button>
                    </div>
                    <el-button v-if="!form.isChangeDir" @click="onChangeNodeDir(true)">
                        <template #icon>
                            <el-icon>
                                <i class="el-icon-refresh"></i>
                            </el-icon>
                        </template>
                        修改目录
                    </el-button>
                    <el-button class="" type="primary" @click="onSaveContentClick">
                        <template #icon>
                            <el-icon>
                                <i class="fa fa-save"></i>
                            </el-icon>
                        </template>
                        保存模板
                    </el-button>
                </div>
            </div>
        </template>
    </DragAdjust>
</template>
<script>
import { deepClone } from '$supersetUtils/function'
// 接口
import {
    queryModelEssayTree, createModelEssayTreeNode, saveModelEssayContent,
    deleteModelEssayTreeNode, editModelEssayTreeNode, updateSortBatch
} from '$supersetApi/projects/apricot/case/report.js'
import { getItemData } from '$supersetApi/projects/apricot/appointment/projectSet.js'

export default {
    name: 'ReportEssayTemplate',
    emits: ['closeClick', 'clickAssign'],
    props: {
        modelValue: {  // 是否显示
            type: Boolean,
            default: false
        },
        reportDisabled: { // 报告是否可编辑
            type: Boolean,
            default: false
        },
        themeColor: {  // 编辑器的背景色
            type: String,
            default: ''
        },
        editorPenalDA: {  // 拖拽配置
            type: Object,
            default: () => ({})
        },
        highlightArray: {  // 高亮词组
            type: Array,
            default: () => []
        },
        elementReportForm: {  // 编辑器表单
            type: Array,
            default: () => []
        },
        patientInfo: {   // 患者信息
            type: Object,
            default: () => ({})
        },
    },
    data () {
        const oTemplateEssayConfig = {
            isFilterDevice: false,
            isFilterProject: false,
            isFilterSex: false,
            isPrivateFirst: false,
            isHidePublic: false,
            isHidePrivate: false,
            isHideQuill: false,
            isHideQuillToolbar: false,
            isAllExpand: false
        }
        const propParams = {
            children: 'childList',
            label: 'sNodeName',
            value: 'sNodeId'
        }
        return {
            oneDA0: {
                type: 't-x',
                localStorageKey: '202308251111',
                panelConfig: [{
                    size: '100%',
                    name: "c1",
                    isFlexible: true
                }]
            },
            twoDA0: {
                type: 't-x',
                localStorageKey: '202308251000',
                panelConfig: [{
                    size: 300,
                    minSize: 100,
                    name: "c1",
                    isFlexible: false
                }, {
                    size: 'calc(100% - 300px)',
                    minSize: 0,
                    name: "c2",
                    isFlexible: true
                }]
            },
            isShowPopover: false,
            form: {
                sNodeName: undefined,
                parentIds: undefined,
                sSex: undefined,
                sInspectSee: undefined,
                sDiagnosticOpinion: undefined,
                sProcessRecord: undefined,
            },
            oQuillRender: {},
            loadingSave: false,
            filetText: '',  // 文字过滤
            dataList: [], // 原数据
            filterList: [],  // 过滤数据
            loading: false,
            time: null,
            clickTime: new Date().getTime(),
            oTemplateEssayConfig: localStorage.getItem('oTemplateEssayConfig') ? JSON.parse(localStorage.getItem('oTemplateEssayConfig')) : oTemplateEssayConfig,
            optionsLoc: { // 下拉数据
                SexType: [{
                    sName: '通用',
                    sValue: '3',
                }, {
                    sName: '男',
                    sValue: '1',
                }, {
                    sName: '女',
                    sValue: '2',
                }],
                projectOptions: []
            },
            cascaderProps: {
                ...propParams,
                expandTrigger: 'hover',
                checkStrictly: true
            },
            treeProps: { ...propParams },
            selectedTreeData: {}, // 点击选中数据
            rightTreeData: {},  // 右键选中数据
            expandKeys: {   // 展开节点
                idx0: [],
                idx1: []
            },
            isOperateNode: false,  // 是否操作节点
        }
    },
    computed: {
        // 范文树上下拖拽配置
        computedDA1 () {
            const panelConfig = [{
                size: '60%',
                minSize: 0,
                name: "c0",
                isFlexible: true
            },
            {
                size: '40%',
                minSize: 0,
                name: "c1",
                isFlexible: false
            }].filter(i => (this.oTemplateEssayConfig.isHidePrivate || this.oTemplateEssayConfig.isHidePublic) ? i.name === 'c0' : true);
            const DA = {
                type: 't-y',
                localStorageKey: '202308081140',
                panelConfig: panelConfig
            }
            return DA
        },
        // 范文内容树
        computedDA2 () {
            var DA = Object.assign({}, this.editorPenalDA);
            DA.localStorageKey = '202308081700';
            return DA
        },
        doAllowDrag () {
            return (node) => {
                // 只允许同级拖拽
                if (node.parent) {
                    return node.parent.children.indexOf(node) !== -1;
                } else {
                    return true;
                }
            };
        },
        filterDirectory () {
            // 过滤目录
            let childList = this.dataList[this.selectedTreeData.idx]?.childList || [];
            childList = deepClone(childList);
            const fn = function (childList) {
                if (!childList) return []
                return childList.filter((item, index) => {
                    if (item.iNodeType != 3 && item.childList.length) {
                        item.childList = fn(item.childList)
                    }
                    return item.iNodeType != 3
                })
            }
            return fn(childList)
        },
    },
    watch: {
        modelValue (val) {
            if (val) {
                this.$nextTick(() => {
                    this.selectedTreeData = {};
                    this.form = {};
                    this.rightTreeData = {};
                    this.isOperateNode = false;
                    this.queryModelEssayTree();
                    !this.oTemplateEssayConfig.isHideQuill && this.renderHighlightFormat();
                    this.getItemData();
                });
            }
        },
        filetText (val) {
            this.filterList.map((item, index) => {
                this.$refs[`dTree${index}`].filter(val);
            });
        }
    },
    mounted () {
        window.addEventListener('click', this.handleHideContextmenuClick);
    },
    beforeDestroy () {
        window.removeEventListener('click', this.handleHideContextmenuClick);
    },
    methods: {
        handleHideContextmenuClick () {
            // 隐藏v-contextmenu组件
            const contextmenuDiv = this.$refs.contextmenuDiv;
            contextmenuDiv && contextmenuDiv.hide();
        },
        // 设置
        onCacheChange (type) {
            localStorage.setItem('oTemplateEssayConfig', JSON.stringify(this.oTemplateEssayConfig));
            const data = this.dataList;
            if (type == 'isPrivateFirst' && data.length == 2) {
                // 交换数据位置；
                this.dataList = this.oTemplateEssayConfig.isPrivateFirst && data[0].isPersonal ? data : [data[1], data[0]];
            }
            if (['isPrivateFirst', 'isFilterDevice', 'isFilterSex', 'isHidePrivate', 'isHidePublic', 'isFilterProject'].includes(type)) {
                this.handleFilterDataList();
                this.setAllExpandNodeId(true)
            }
            if (type === 'isHideQuill') {
                this.$store.commit({
                    type: `apricot/report_module/setIsHideEssayContent`,
                    isHideEssayContent: this.oTemplateEssayConfig.isHideQuill
                });
            }
            if( type === 'isAllExpand' && this.oTemplateEssayConfig.isAllExpand) {
                // 默认展开全部节点
                // 递归找到目录节点id
                const recursionFn = function (childList) {
                    if (!childList.length) return [];
                    let aTarget = []
                    childList.map(item => {
                        if (item.childList.length) {
                            aTarget = [item.sNodeId, ...recursionFn(item.childList), ...aTarget];
                        }
                    });
                    return [...aTarget]
                }
                this.filterList.map((item, index) => {
                    if(!this.expandKeys[`idx${index}`].length) {
                        this.expandKeys[`idx${index}`] = [...recursionFn(item.childList)];
                    }
                })
            }
        },
        filterNode (value, data) {
            if (!value) return true;
            return data.sNodeName.indexOf(value) !== -1;
        },
        // tree拖拽成功完成时触发的事件
        handleNodeDrop (draggingNode, dropNode, dropType, ev) {
            const childNodes = dropNode.parent.childNodes;
            var sortList = [];
            childNodes.map((item, index) => {
                sortList.push({ iSort: index, sNodeId: item.data.sNodeId })
            });
            updateSortBatch(sortList).then(res => {
                if (res) {
                    this.queryModelEssayTree();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
                this.queryModelEssayTree();
            })

        },
        // 拖拽时判定目标节点能否被放置
        // 'prev'、'inner' 和 'next'，分前、插入、后
        allowDrop (draggingNode, dropNode, type) {
            // console.log(draggingNode, dropNode, type);
            if (draggingNode.data.level === dropNode.data.level) {
                if (draggingNode.data.sParentId === dropNode.data.sParentId) {
                    return type === "prev" || type === "next";
                } else {
                    return false;
                }
            } else {
                // 不同级进行处理
                return false;
            }
        },
        // el-tree组件点击事件
        handleTreeClick (data, node, component, idx) {
            // 隐藏v-contextmenu组件
            this.handleHideContextmenuClick();
            //计算两次点击时间差
            let delta = Date.now() - this.clickTime;
            this.clickTime = Date.now();
            if (delta > 0 && delta <= 300) {
                // 双击事件
                if (data.iNodeType == 3) {
                    if (this.reportDisabled) {
                        this.$message.warning('当前报告不可编辑！');
                        return;
                    }
                    let tempArr = [];
                    this.elementReportForm.map(item => {
                        if (!item.iIsHide) {
                            let obj = {};
                            obj.mark = item.sProp;
                            obj.sModelEssayText = data[item.sProp];
                            tempArr.push(obj);
                        }
                    });
                    data.close = true;
                    this.$emit('clickAssign', 1, tempArr, data);
                    // this.$emit('closeClick'); // 关闭模板penal
                }
                return
            }
            // 单击
            if (Object.keys(this.selectedTreeData).length) {
                // 取消上次选中节点高亮 
                data.iIsPersonal !== this.selectedTreeData.iIsPersonal && this.$refs[`dTree${this.selectedTreeData.idx}`].setCurrentKey(null);
            }
            this.selectedTreeData = deepClone(data);
            this.selectedTreeData.idx = idx;
            this.form = this.$options.data().form;

            if (data.iNodeType == 3) {
                // 范文节点
                this.form = deepClone(data);
                const modelEssayItems = data?.modelEssayItems || [];
                this.form.sItemIds = [];
                if(modelEssayItems.length) {
                    this.form.sItemIds = modelEssayItems.map(item => item.itemId);
                }
                // 递归获取父节点Id
                var recursionPidFn = function (node) {
                    if (!node.parent) return [];
                    let target = recursionPidFn(node.parent);
                    return [...target, node.data.sNodeId]
                }
                this.form.parentIds = recursionPidFn(node.parent);
            }
        },

        // el-tree组件右键事件
        handleTreeContextmenu (event, data, idx) {
            this.rightTreeData = data;
            this.rightTreeData.idx = idx;
            // 获取鼠标位置，显示右键菜单
            const contextmenuDiv = this.$refs.contextmenuDiv;
            const position = {
                top: event.clientY,
                left: event.clientX
            }
            // 显示右键
            data.sNodeId && contextmenuDiv.show(position);
        },
        // 记录节点的展开和关闭
        handleNodeTrigger (data, isExpanded, index) {
            if (isExpanded) {
                // 展开
                data.childList && this.expandKeys[`idx${index}`].push(data.sNodeId);
                // console.log('data.childList==', isExpanded, data.childList)
                return;
            }
            const recursionFn = function (childList) {
                if (!childList.length) return [];
                let aTarget = []
                childList.map(item => {
                    if (item.childList.length) {
                        aTarget = [item.sNodeId, ...recursionFn(item.childList), ...aTarget];
                    }
                });
                return [...aTarget]
            }
            const ids = [...recursionFn(data.childList), data.sNodeId];
            this.expandKeys[`idx${index}`] = this.expandKeys[`idx${index}`].filter(item => !ids.includes(item));
        },
        // 添加节点
        addNodeClick (type) {
            this.isOperateNode = true;
            const newChild = {
                isEdit: true,  // 编辑状态
                addIdx: Date.now(),  // 用于取消操作判断节点
                childList: [],
                sParentId: this.rightTreeData.sNodeId, // 父节点id
                iNodeType: type,  // 节点类型（1-设备类型；2-目录；3-模板）
                sNodeName: '',  // 节点名称
            };
            if (!this.rightTreeData.childList) {
                this.rightTreeData.childList = []
            }
            // 添加到展开节点数组中
            let expandKeys = this.expandKeys[`idx${this.rightTreeData.idx}`];
            !expandKeys.includes(this.rightTreeData.sNodeId) && expandKeys.push(this.rightTreeData.sNodeId);

            this.rightTreeData.childList.push(newChild);
            this.filterList[this.rightTreeData.idx].childList = [...this.filterList[this.rightTreeData.idx].childList];
        },
        // 保存节点
        onSaveNodeClick (node, data, idx) {
            if (data.sNodeId) {
                // 编辑节点
                const params = {
                    sNodeId: data.sNodeId,
                    sNodeName: data.sNodeName,
                    sParentId: data.sParentId,
                    sSex: data.sSex
                }
                editModelEssayTreeNode(params).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        // 返回成功
                        data.isEdit = false;
                        this.isOperateNode = false;
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err)
                })
                return
            }
            // 新增节点
            var params = { ...data };
            delete params.isEdit;
            delete params.addIdx;
            delete params.childList;
            createModelEssayTreeNode(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    if (res.data && res.data.iNodeType == 3) {
                        this.selectedTreeData = res.data;
                        this.selectedTreeData.idx = this.rightTreeData.idx;
                        this.form = this.$options.data().form;
                        Object.assign(this.form, res.data);
                        // 递归获取父节点Id
                        var recursionPidFn = function (node) {
                            if (!node.parent) return [];
                            let target = recursionPidFn(node.parent);
                            return [...target, node.data.sNodeId]
                        }
                        this.form.parentIds = recursionPidFn(node.parent);
                    }
                    this.isOperateNode = false;
                    // 返回成功
                    this.queryModelEssayTree();
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err)
            })
        },
        // 取消编辑
        onCancelNodeClick (node, data, idx) {
            this.isOperateNode = false;
            if (!data.addIdx) {
                data.isEdit = false;
                data.sNodeName = this.rightTreeData.orginName;
                return
            }
            const parent = node.parent
            const children = parent.data.childList || parent.data;
            const index = children.findIndex((d) => d.addIdx === data.addIdx)
            children.splice(index, 1)
            this.filterList[idx].childList = [...this.filterList[idx].childList];
            if(children.length === 0) {
                this.expandKeys[`idx${idx}`] = this.expandKeys[`idx${idx}`].filter(item => item !== parent.data.sNodeId);
            }
        },
        // 重命名
        renameNodeClick () {
            this.isOperateNode = true;
            this.rightTreeData.isEdit = true;
            this.rightTreeData.orginName = this.rightTreeData.sNodeName;
        },
        // 复制
        async copyNodeClick () {
            var isSuccess = false
            var nodeRes = {}
            // 新增节点
            const suffix = ' 复制';
            const node = {
                iNodeType: this.rightTreeData.iNodeType,
                sNodeName: this.rightTreeData.sNodeName + suffix,
                sParentId: this.rightTreeData.sParentId,
                sSex: this.rightTreeData.sSex,

            };
            // 保存节点
            await createModelEssayTreeNode(node).then(res => {
                if (res.success) {
                    isSuccess = true;
                    nodeRes = res?.data || {};
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err)
            })
            if (isSuccess) {
                const content = {
                    sDeviceTypeId: this.rightTreeData.sDeviceTypeId,
                    sNodeId: nodeRes.sNodeId,
                    sNodeName: nodeRes.sNodeName,
                    sParentId: nodeRes.sParentId,
                    sSex: nodeRes.sSex,
                    sDiagnosticOpinion: this.rightTreeData.sDiagnosticOpinion,
                    sInspectSee: this.rightTreeData.sInspectSee,
                    sProcessRecord: this.rightTreeData.sProcessRecord,
                    modelEssayItems: this.rightTreeData.modelEssayItems,
                }
                this.loadingSave = true;
                // 保存内容
                await saveModelEssayContent(content).then(res => {
                    this.loadingSave = false
                    if (res.success) {
                        this.$message.success(res.msg);
                        return;
                    }
                    this.$message.error(res.msg)
                }).catch(() => {
                    this.loadingSave = false
                })
                // 刷新数据
                this.queryModelEssayTree();
            }
        },
        // 删除节点
        onDeleteNodeClick () {
            this.$confirm(`确定删除"${this.rightTreeData.sNodeName}"吗，是否继续？`, '提示', { type: 'warning' }).then(() => {
                let jsonData = {
                    modelEssayTreeId: this.rightTreeData.sNodeId,
                };
                deleteModelEssayTreeNode(jsonData).then(res => {
                    if (res.success) {
                        this.$message.success('删除成功！');
                        this.queryModelEssayTree()
                        this.handleNodeTrigger(this.rightTreeData, false, this.rightTreeData.idx);
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch()
            }).catch(err => err);
        },
        // 修改所属目录
        handleParentIdsChange() {
            this.$refs.cascader.togglePopperVisible(false);
        },
        // 修改目录
        onChangeNodeDir (value) {
            if(!this.form.sNodeId) {
                this.$message.warning('请选择范文模板！');
                return;
            }
            this.form.isChangeDir = value;
        },
        // 保存范文内容
        onSaveContentClick () {
            if(!this.form.sNodeId) {
                this.$message.warning('请选择范文模板！');
                return;
            }
            let jsonData = {
                sDeviceTypeId: this.form.sDeviceTypeId,
                sNodeId: this.form.sNodeId,
                sNodeName: this.form.sNodeName,
                sParentId: this.form.parentIds[this.form.parentIds.length - 1],
                sSex: this.form.sSex,
                modelEssayItems: []
            };
            // 处理检查项目 
            const sItemIds = this.form?.sItemIds || [];
            if(sItemIds.length) {
                this.optionsLoc.projectOptions.map(item => {
                    if(sItemIds.includes(item.sId)) {
                        const oItem = {
                            itemId: item.sId,
                            itemName: item.sItemName,
                            modelEssayId: jsonData.sNodeId
                        }
                        jsonData.modelEssayItems.push(oItem);
                    }
                })
            }

            // const html2text = (html) => {
            //     const temp = document.createElement("div");
            //     temp.innerHTML = html;
            //     return temp.textContent || temp.innerText || "";
            // }
            // 处理范文内容
            this.elementReportForm.map((item) => {
                jsonData[item.sProp] = this.removeHighlightFormatToSave(this.form[item.sProp]) || '';
                // jsonData[item.sProp + 'Text'] = html2text(this.form[item.sProp]);
            });


            this.loadingSave = true;
            saveModelEssayContent(jsonData).then(res => {
                this.loadingSave = false
                if (res.success) {
                    this.$message.success(res.msg);
                    delete this.form.isChangeDir;
                    this.queryModelEssayTree();
                    return;
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loadingSave = false
            })
        },
        // 编辑器的双击事件
        onClickQuill (type, sProp) {
            if(!this.form.sNodeId) {
                return
            }
            let delta = Date.now() - this.clickTime; //计算两次点击时间差
            this.clickTime = Date.now();
            if (delta > 0 && delta <= 300) {
                // 双击事件
                if (sProp) {
                    let tempArr = [];
                    this.elementReportForm.map(item => {
                        if (!item.iIsHide && sProp === item.sProp) {
                            let obj = {};
                            obj.mark = item.sProp;
                            obj.sModelEssayText = this.form[item.sProp];
                            tempArr.push(obj);
                        }
                    });
                    this.$emit('clickAssign', type, tempArr, this.selectedTreeData);
                    return
                }
            }
        },
        // 渲染敏感词高亮样式
        renderHighlightFormat () {
            if (this.oTemplateEssayConfig.isHideQuill) return;
            this.oQuillRender = {};
            this.elementReportForm.map((item, index) => {
                this.textChangeToHighlight(`myQuillEditor${index}`);
            })
        },
        // 高亮样式处理
        async textChangeToHighlight (key) {
            let highlightArray = this.highlightArray || [];
            if (this.oQuillRender[key] || !highlightArray.length) {
                return
            }
            // 设置特殊文字高亮
            this.oQuillRender[key] = true;
            await this.$nextTick(() => {
                let quill = this.$refs[key].quill;
                let text = quill.getText();
                if (!text) {
                    // 编辑器内输入内容不存在时，结束函数
                    return
                }
                highlightArray.forEach(item => {
                    let reg = new RegExp(item, 'g');
                    let len = item.length;
                    let match;
                    while ((match = reg.exec(text)) !== null) {
                        const index = match.index;
                        // 变红加粗
                        quill.formatText(index, len, 'spanHighlight', true);
                    }
                });
            })
        },
        // 移除高亮样式
        removeHighlightFormatToSave (text) {
            let highlightArray = this.highlightArray;
            if (!highlightArray.length || !text) {
                // 高亮词为空，结束函数
                return text
            }
            return text.replace(/class="ql-font-spanHighlight"/g, '');
        },
        // 插入、覆盖、追加
        handleEssayClick (type, sProp, index) {
            if(!this.form.sNodeId) {
                this.$message.warning('请选择范文模板！');
                return;
            }
            if (this.reportDisabled) {
                this.$message.warning('当前报告不可编辑！');
                return;
            }
            let tempArr = [];
            this.elementReportForm.map(item => {
                if (!item.iIsHide) {
                    let obj = {};
                    obj.mark = item.sProp;
                    obj.sModelEssayText = this.form[item.sProp];
                    tempArr.push(obj);
                }
            });
            if (sProp) {
                let filterArr = tempArr.filter(item => item.mark === sProp);
                if (index > -1) {
                    // 选中纯文本内容 选中插入
                    const editorDom = this.$refs[`myQuillEditor${index}`]
                    const quill = editorDom.quill
                    const range = quill.getSelection()
                    if (range) {
                        if (range.length) {
                            filterArr[0].sModelEssayText = quill.getText(range.index, range.length);
                        }
                    }
                }
                this.$emit('clickAssign', type, filterArr, this.selectedTreeData);
                return
            }
            this.$emit('clickAssign', type, tempArr, this.selectedTreeData)
        },
        handleFilterDataList () {
            const { isHidePrivate, isHidePublic, isFilterDevice, isFilterProject, isFilterSex } = this.oTemplateEssayConfig;
            const sDeviceTypeId = this.patientInfo.sRoomId;
            const sProjectId = this.patientInfo.sProjectId;
            const sSex = this.patientInfo.sSex;
            // 过滤公有私有
            const filterData = deepClone(this.dataList.filter(item => item.iIsPersonal && !isHidePrivate || !item.iIsPersonal && !isHidePublic));
            const FilterSexFn = function (list) {
                // 过滤性别
                return list.filter(item => {
                    if (isFilterSex) {
                        if (item.childList.length) {
                            item.childList = [...FilterSexFn(item.childList)];
                        }
                        return item.iNodeType != 3 || !item.sSex || item.sSex == 3 || item.sSex == sSex;
                    }
                })
            }
            const FilterItemFn = function (list) {
                // 过滤项目
                return list.filter(item => {
                    if (isFilterProject) {
                        if (item.childList.length) {
                            item.childList = [...FilterItemFn(item.childList)]
                        }
                        return item.iNodeType != 3 || item.modelEssayItems && item.modelEssayItems.map(item => item.itemId).includes(sProjectId)
                    }
                })
            }
            let aTarget = []
            filterData.map(item_ => {
                const oItem = item_;
                // 过滤设备类型
                oItem.childList = item_.childList.filter(item => isFilterDevice ? item.sDeviceTypeId === sDeviceTypeId : true);
                if(isFilterSex) {
                    // 过滤性别
                    oItem.childList = FilterSexFn(oItem.childList)
                }
                if(isFilterProject) {
                    // 过滤检查项目
                    oItem.childList = FilterItemFn(oItem.childList)
                }
                aTarget.push(oItem);
            })
            this.filterList = aTarget;
        },
        // 设置所有展开节点
        setAllExpandNodeId(isReset = false) {
            if(this.oTemplateEssayConfig.isAllExpand) {
                // 默认展开全部节点
                // 递归找到目录节点id
                const recursionFn = function (childList) {
                    if (!childList.length) return [];
                    let aTarget = []
                    childList.map(item => {
                        if (item.childList.length) {
                            aTarget = [item.sNodeId, ...recursionFn(item.childList), ...aTarget];
                        }
                    });
                    return [...aTarget]
                }
                this.filterList.map((item, index) => {
                    if(!this.expandKeys[`idx${index}`].length || isReset) {
                        this.expandKeys[`idx${index}`] = [...recursionFn(item.childList)];
                    }
                })
            }
        },
        queryModelEssayTree () {
            this.loading = true;
            this.dataList = [];
            queryModelEssayTree().then(res => {
                this.loading = false;
                if (res.success) {
                    let data = res.data || [];
                    // 是否优先显示个人模板
                    this.dataList = data.length == 2 && this.oTemplateEssayConfig.isPrivateFirst ? [data[1], data[0]] : data;
                    this.handleFilterDataList();
                    this.setAllExpandNodeId();
                    this.$nextTick(() => {
                        if (this.selectedTreeData.sNodeId) {
                            this.$refs[`dTree${this.selectedTreeData.idx}`].setCurrentKey(this.selectedTreeData.sNodeId);
                        }
                    })
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading = false;
                console.log(err);
            })
        },
        // 获取检查项目
        async getItemData() {
            let params = {
                condition: {
                    iIsEnable: 1,
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 200,
                }
            }
            await getItemData(params).then(res => {
                if (!res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.optionsLoc.projectOptions = res.data || [];
            })
        }
    }
}
</script>
<style lang="scss" scoped>
* {
    box-sizing: border-box;
}

.c-popover-title {
    margin: 0;
    padding: 10px;
    background-color: var(--el-color-primary-light-9);
    border-left: 2px solid var(--el-color-primary);
    font-size: 16px;
}

.g-template {
    min-width: 300px;
    width: 100%;
    height: 100%;
    background-color: #f9fafc;

    .bt-1px {
        border-bottom: 1px solid var(--el-border-color);
    }

    .i-essay-title {
        margin: 0;
        padding: 10px;
        background: var(--el-color-primary-light-9);
        // border-left: 2px solid var(--el-color-primary);
        // border-bottom: 1px solid var(--el-border-color);
        font-size: 14px;
        font-weight: bold;
    }
    .c-actions {
        justify-content: right;
        margin-right: 52px;
        padding: 7px 10px;
        text-align: right;
        // border-top: 1px solid #eee;
    }
    .i-icon {
        position: relative;
        top: 4px;
        right: 4px;
    }
}

.c-box-content {
    height: 100%;
    display: flex;
    border-bottom: 1px solid var(--el-border-color);

    h5 {
        background: rgb(245, 245, 245);
        font-weight: bold;
        font-size: 14px;
        line-height: 1.5;
        color: rgb(44, 62, 80);
        padding: 0;
        margin: 0;
        width: 26px;
        text-align: center;
        display: flex;
        align-items: center;
        border-right: 1px solid #eee;
    }

    .c-box {
        flex: 1;
    }

    .c-button {
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-left: 1px solid #eee;
        background-color: #f5f5f5;

        .el-button {
            margin: 10px;
            padding: 5px 8px;
        }
    }

    :deep(.u-none-toolbar .ql-toolbar) {
        display: none !important;
    }
}

.i-bold {
    font-weight: bold;

    :deep(.el-input input) {
        font-weight: bold;
    }
}

.i-border:not(:last-child) {
    border-bottom: 1px solid var(--el-border-color);
}
:deep(.el-tree) {
    background: transparent;
}
:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
    background-color: var(--table-highlight-row-bg);
}

:global(.v-contextmenu-item--hover) {
    background-color: var(--el-color-primary);
}
</style>
