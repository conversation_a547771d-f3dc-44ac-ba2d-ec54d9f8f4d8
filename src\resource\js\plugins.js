/*拖拽调整大小的容器*/
+function(){
	var nMinSize = 5,
		nMaxSize = 0,
		store;                    //存储拖拽数据
	
	//mousedown 委托在拖拽块的控制器上
	$(document).on('mousedown', '.My-dragAdiust > .g-adjust-item > button', mousedown);
	
	//鼠标点击按下
	function mousedown(e){
		
		e.preventDefault();
		e.stopPropagation();
		//遮罩
		shade();
		//初始化数据
		initStore();
		
		//Dom元素获取
		var $_this = $(this),
			//拖动元素的容器
			$_oDragContainer = $_this.closest('.My-dragAdiust'),
			//拖动的元素
			$_dragObj = $_this.parent('.g-adjust-item'),
			//当前级的弹性计算元素
			$_siblingFlexChild = $_dragObj.siblings('.g-flexChild');
			
		nMinSize = Number($_dragObj.attr('data-minSize'));
		nMaxSize = Number($_dragObj.attr('data-maxSize'));
		//为拖拽控制块加上拖拽特征的类
		$_this.addClass('s-dragging');
		
		//记录鼠标按下时的参数
		store.dragObj = $_dragObj;
		store.siblingFlexChild = $_siblingFlexChild;
		store.dragCtrl = $_this;
		store.container = $_oDragContainer;
		//鼠标起始位置坐标
		store.clientXStar = e.clientX;
		store.clientYStar = e.clientY;
		
		//容器内部宽高
		store.nContainerW = $_oDragContainer.innerWidth();
		store.nContainerH = $_oDragContainer.innerHeight();
		
		//被拖拽元素的宽高
		store.nWidth = $_dragObj.outerWidth();
		store.nHeight = $_dragObj.outerHeight();
		
		//在document on mousedown 的情况下，进行 mousemove 的绑定
		$(document).on('mousemove', mousemove);
			//函数
		$(document).on('mouseup', mouseup);
	}
	
	
	
	//鼠标拖动要进行的动作
	function mousemove(e){
		
		e.preventDefault();
		e.stopPropagation();
		
		//记录鼠标移动后的位置
		store.clientXEnd = e.clientX,
		store.clientYEnd = e.clientY;
		
		
		
		if(!store.container.hasClass('t-y')){
			//鼠标移动的总的距离
			var nAbsX = store.clientXEnd - store.clientXStar,
				nNewWidth = store.dragObj.nextAll('.g-flexChild').length ? store.nWidth + nAbsX : store.nWidth - nAbsX,
				nCurrentMaxSize = store.nContainerW - calcFixSize('x');
				
			if(nMaxSize){
				nCurrentMaxSize = Math.min(nCurrentMaxSize, nMaxSize);
				//避免归零
				nCurrentMaxSize = Math.max(nCurrentMaxSize, 5);
			}
			//超出界限时，设置好界限值	
			if(nNewWidth < nMinSize){
				store.nNewWidth = nMinSize;
			}else if(nNewWidth > nCurrentMaxSize){
				store.nNewWidth = nCurrentMaxSize;
			}else{
				store.nNewWidth = nNewWidth;
				store.dragCtrl.css("transform", 'translateX(' + nAbsX + 'px)');
			}
			
		}else{
			//鼠标移动的总的距离
			var nAbsY = store.clientYEnd - store.clientYStar,
				nNewHeight = store.dragObj.nextAll('.g-flexChild').length ? store.nHeight + nAbsY : store.nHeight - nAbsY,
				nCurrentMaxSize = store.nContainerH - calcFixSize('y');
			if(nMaxSize){
				nCurrentMaxSize = Math.min(nCurrentMaxSize, nMaxSize);
				//避免归零
				nCurrentMaxSize = Math.max(nCurrentMaxSize, 5);
			}
			
			//超出界限时，设置好界限值	
			if(nNewHeight < nMinSize){
				store.nNewHeight = nMinSize;
			}else if(nNewHeight > nCurrentMaxSize){
				store.nNewHeight = nCurrentMaxSize;
			}else{
				store.nNewHeight = nNewHeight;
				store.dragCtrl.css("transform", 'translateY(' + nAbsY + 'px)');
			}	
		}
	}
	////
	//鼠标抬起，重新设置拖拽元素尺寸（百分比）
	function mouseup(e){
		
		e.preventDefault();
		e.stopPropagation();
		shade(false);
		//鼠标抬起后，解除移动的绑定
		$(document).off('mousemove', mousemove);
		//执行绑定好的函数后，顺手解绑
		$(document).off('mouseup', mouseup);
		//恢复状态
		store.dragCtrl.removeClass('s-dragging').css("transform", 'none');
		
		//未拖动，只是点击了
		if(store.clientXEnd === null || store.clientYEnd === null){
			return;
		}
		nMinSize = 5;
		//触发自定义事件，拖拽完成执行
		window.dragAdjustDragenData = {
			index: Number(store.dragObj.attr('data-index')),
			value: Math.max(store.nNewHeight, store.nNewWidth)
		};
		store.container[0].dispatchEvent(new Event('dragAdjustDragen'));
		
	}
	
	
	function calcFixSize(d){
		var $_fixAdjustItems = store.dragObj.siblings(':not(.g-flexChild)'),
			sum = 0;
			
		if(d === 'x'){
			$_fixAdjustItems.each(function(){
				sum += $(this).outerWidth();
			});
		}else if(d === 'y'){
			$_fixAdjustItems.each(function(){
				sum += $(this).outerHeight();
			});
		}
		
		
		return sum;
	}
	
	//参数存储对象初始化
	function initStore(){
		store = {
			"clientXStar": null,
			"clientYStar": null,
			"clientXEnd": null,
			"clientYEnd": null,
			"nContainerW": 0,
			"nContainerH": 0,
			"nWidth": 0,
			"nNewWidth": 0,
			"nHeight": 0,
			"nNewHeight": 0,
			"container": null,
			"dragObj": null,         
			"dragCtrl": null
		};
	}
	
	function shade(b){
		if(b === true || b === undefined){
			//显示遮罩
			$('body').append('<div id="i_drag_adjust_shade" style="position: fixed; top: 0; right: 0; bottom: 0; left: 0; opcity: 0; z-index: 999;"></div>');
		}else{
			//隐藏遮罩
			$('#i_drag_adjust_shade').remove();
		}
	}
}()

/*数组操作-------------------------------------------------*/
+function(){
	Array.prototype.remove = function(val) { 
		let index = this.indexOf(val); 
		if (index > -1) { 
			this.splice(index, 1); 
		} 
	};
	Array.prototype.clone = function(){ 
    return [].concat(this); 
    //或者 return this.concat();
	}
}();
/*-------------------------------------------------数组操作*/

/*Quill-Editor下拉菜单定位-----------------------------------*/
// +function(){
// 	$(document).on('click', '.ql-picker', function(e){
		
// 		let $picker = $(this);
// 		let $option = $picker.find('.ql-picker-options');
		
// 		let nWindowH = $(window).height();
		
// 		let nPickerW = $picker.outerWidth();
// 		let nPickerH = $picker.outerHeight();
// 		let nOptionW = $option.outerWidth();
// 		let nOptionH = $option.outerHeight();
		
// 		let offset = $picker.offset();
		
// 		let offsetBottom = nWindowH - offset.top - nPickerH;
// 		let nLeft = (offset.left + nPickerW/2 - nOptionW/2) + 'px';
		
// 		if(nOptionH > offsetBottom && offset.top > offsetBottom){
// 			$option.css({
// 				left: nLeft,
// 				top: offset.top - nOptionH + 'px',
// 				'margin-top': '-2px'
// 			});
// 		}else{
// 			$option.css({
// 				left: nLeft,
// 				top: offset.top + nPickerH + 'px',
// 				'margin-top': '2px'
// 			});
// 		}
// 	})
// }();
/*-----------------------------------Quill-Editor下拉菜单定位*/
