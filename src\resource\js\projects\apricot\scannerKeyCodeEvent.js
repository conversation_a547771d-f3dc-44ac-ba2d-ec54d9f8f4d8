/**
 * 扫码设备，监听键盘事件
 */
function newScannerKeyCodeEvent() {
    const maxTimestamp   = 140;
    let codeValue        = '';
    let triggerTimestamp = '';
    let lastTimestamp    = '';

    window.onkeydown = (e) => {
        const el = e || window.event || arguments.callee.caller.arguments[0];
        let keyVal  = el.key;
        let keyCode = el.keyCode;
        let differTime = 1000;


        // 输入框下，中文输入法会改成 229
        if (keyCode === 229) {
            if (el.code.includes('Digit')) {
                keyVal = el.code.substring(5);
            }else if(el.code.includes('Key')) {
                keyVal = el.code.substring(3);
            }else if (el.code.includes('Enter')) {
                keyCode = 13;
            }
        }

        // 字母，数字 - . / (包含了个108？ Enter) || 稀奇古怪的需要加shift的特殊字符 
        if (keyCode >= 48 && keyCode <= 111 || keyCode >= 186 && keyCode <= 222 || keyCode === 229) {
            
            triggerTimestamp = new Date().getTime();

            differTime = triggerTimestamp - lastTimestamp;

            if (!lastTimestamp) {
                // 第一次执行
                codeValue = keyVal;
            }else if (differTime <= maxTimestamp) {
                // 相隔 140毫秒执行
                codeValue += keyVal;
            }else {
                codeValue = keyVal;
                lastTimestamp = '';
            }
        }

        if (!lastTimestamp || differTime <= maxTimestamp) {
            lastTimestamp = new Date().getTime();
        }

        // 回车键，触发
        if (keyCode === 13 || keyCode === 108) {
            _triggerEvent();
        }

        instance.onkeydown && instance.onkeydown(el)
    }

    const instance = {};

    // 触发事件
    const _triggerEvent = () => {
        // （初步测试判定）codeValue为Process时，为中文输入
        if (codeValue.length > 3 && !codeValue.includes('Process')) {
            for (const key in instance) {
                if (Object.hasOwnProperty.call(instance, key)) {
                    const fun = instance[key];
                    fun(codeValue)
                }
            }
        }
        lastTimestamp = '';
        // 清除扫描值
        codeValue = '';
    }

    return instance
}

const scannerKeyCodeEvent = newScannerKeyCodeEvent();
export default scannerKeyCodeEvent;