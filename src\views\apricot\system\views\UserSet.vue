<template>
    <!-- 用户设置 -->
    <div class="c-flex-context c-container">
        <div class="c-content c-flexChild c-flex-y">
            <div class="c-item t-1">
                <strong>科室</strong>
            </div>
            <div class="c-item t-2 c-flexChild">
                <el-tree :data="deparmentTree"
                    :props="defaultProps"
                    default-expand-all
                    highlight-current
                    @node-click="handleDepartmentNodeClick"></el-tree>
            </div>
        </div>
        <div class="c-content t-2 c-flexChild c-flex-y">
            <div class="c-item t-1">
                <strong>成员列表</strong>
            </div>
            <div class="c-item t-3 c-flexChild">
                <el-table :data="tableData"
                    @row-click="onTableRow"
                    size="small"
                    border
                    stripe
                    
                    height="100%"
                    style="width: 100%">
                    <el-table-column type="index"
                        label="序号"
                        width="60">
                    </el-table-column>
                    <el-table-column prop="sName"
                        label="姓名"
                        min-width="100"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column prop="sNo"
                        label="工号"
                        min-width="100"
                        show-overflow-tooltip>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="c-content c-flexChild c-flex-y">
            <div class="c-item t-1 c-flex-x">
                <strong>可查询设备类型</strong>
                <el-button-icon-fa type="primary"
                    plain
                    _icon="fa fa-save"
                    size="small"
                    :loading="loading.device"
                    @click="onSaveDevice">保存</el-button-icon-fa>
            </div>
            <div class="c-item t-2 c-flexChild">
                <el-checkbox-group v-model="checkDeviceGroups"
                    size="small">
                    <el-checkbox v-for="(item,index) in deviceOptions"
                        :label="item.sId"
                        :key="index">{{item.sDeviceTypeName}}</el-checkbox>
                </el-checkbox-group>
            </div>
        </div>
        <div class="c-content c-flexChild"
            style="flex:2;overflow-y:auto">
            <div class="c-flex-y i-item">
                <div class="c-item t-1 c-flex-x">
                    <strong>用户类型</strong>
                    <el-button-icon-fa type="primary"
                        plain
                        _icon="fa fa-save"
                        size="small"
                        :loading="loading.userType"
                        @click="onSaveBussinessRole">保存</el-button-icon-fa>
                </div>
                <div class="c-item t-2 c-flexChild">
                    <el-checkbox-group v-model="checkBussinessGroups"
                        size="small">
                        <el-checkbox v-for="(item,index) in bussinessRoles"
                            :label="item.sValue"
                            :key="index">{{item.sName}}</el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
            <div class="c-flex-y i-item">
                <div class="c-item t-1 c-flex-x">
                    <strong>医生级别</strong>
                    <el-button-icon-fa type="primary"
                        plain
                        _icon="fa fa-save"
                        size="small"
                        :loading="loading.level"
                        @click="onSaveReportWriter">保存</el-button-icon-fa>
                </div>
                <div class="c-item t-2 c-flexChild">
                    <el-checkbox-group v-model="checkWriterGroups"
                        size="small"
                        :max="1">
                        <el-checkbox v-for="(item,index) in reportWriterRoles"
                            :label="item.sValue"
                            :key="index">{{item.sName}}</el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
            <div class="c-flex-y i-item">
                <div class="c-item t-1 c-flex-x">
                    <strong>打印控制</strong>
                    <el-button-icon-fa type="primary"
                        plain
                        _icon="fa fa-save"
                        size="small"
                        :loading="loading.print"
                        @click="onSavePrint">保存</el-button-icon-fa>
                </div>
                <div class="c-item t-2 c-flexChild">
                    <el-checkbox-group v-model="checkPrintDroups"
                        size="small">
                        <el-checkbox v-for="(item,index) in printControls"
                            :label="item.sValue"
                            :key="index">{{item.sName}}</el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { deepClone } from '$supersetUtils/function'
import Api from '$supersetApi/projects/apricot/system/userSet.js'
import { getDeviceTypeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'

import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
export default {
    name: 'UserSet',
    data () {
        return {
            deparmentTree: [],
            deviceOptions: [],
            checkDeviceGroups: [],
            defaultProps: {
                children: 'childs',
                label: 'sName'
            },
            tableData: [],
            selectedRow: {},
            checkBussinessGroups: [],
            bussinessRoles: [
                {
                    sName: '预约护士',
                    sValue: 'AppointDr'
                },
                {
                    sName: '签到护士',
                    sValue: 'RegisterDr'
                },
                {
                    sName: '问诊医生',
                    sValue: 'ConsultDr'
                },
                {
                    sName: '注射护士',
                    sValue: 'InjectDr'
                },
                {
                    sName: '上机技师',
                    sValue: 'MachineDr'
                },
                {
                    sName: '报告医生',
                    sValue: 'ReportDr'
                }
            ],
            checkWriterGroups: [],
            reportWriterRoles: [
                {
                    sName: '实习医生',
                    // sValue: 'PRACTICE',
                    sValue: 10
                },
                {
                    sName: '规培医生',
                    // sValue: 'TRAINING',
                    sValue: 11
                },
                {
                    sName: '进修医生',
                    // sValue: 'STUDY',
                    sValue: 12
                },
                {
                    sName: '报告医生',
                    // sValue: 'REPORT',
                    sValue: 20
                },
                {
                    sName: '审核医生',
                    // sValue: 'AUDIT',
                    sValue: 30
                },
                {
                    sName: '复审医生',
                    // sValue: 'RECHECK',
                    sValue: 40
                },
            ],
            checkPrintDroups: [],
            printControls: appointmentEnum.visitTypeOptions,
            loading: {
                device: false,
                userType: false,
                level: false,
                print: false
            }
        }
    },
    methods: {
        // 保存可查询的设备类型
        onSaveDevice () {
            if (!Object.keys(this.selectedRow).length) {
                this.$message.warning('请选择用户数据');
                return
            }
            let checkList = [];
            this.checkDeviceGroups.map(item => {
                let temp = this.deviceOptions.find(_ => _.sId === item)
                if (temp) {
                    checkList.push(temp)
                }
            })
            // 没有勾选项的时候调用删除接口
            if (!checkList.length) {
                Api.delDeviceType({ userId: this.selectedRow.sId }).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err)
                })
                return
            }
            // 存在勾选项的是时候，给勾选项添加用户信息属性，调用保存接口
            checkList.forEach(item => {
                item.sDeviceTypeId = item.sId;
                item.sUserId = this.selectedRow.sId;
                item.sUserName = this.selectedRow.sName;
                item.sUserNo = this.selectedRow.sNo;
            });
            this.loading.device = true;
            Api.saveDeviceType(checkList).then(res => {
                this.loading.device = false;
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading.device = false;
                console.log(err)
            })
        },
        // 保存用户类型
        onSaveBussinessRole () {
            if (!Object.keys(this.selectedRow).length) {
                this.$message.warning('请选择用户数据');
                return
            }
            let checkList = deepClone(this.bussinessRoles).filter(item => this.checkBussinessGroups.includes(item.sValue));
            // 没有勾选项的时候调用删除接口
            if (!checkList.length) {
                this.loading.userType = true
                Api.delBussinessRole({ userId: this.selectedRow.sId }).then(res => {
                    this.loading.userType = false
                    if (res.success) {
                        this.$message.success(res.msg);
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    this.loading.userType = false
                    console.log(err)
                })
                return
            }
            // 存在勾选项的是时候，给勾选项添加用户信息属性，调用保存接口
            checkList.forEach(item => {
                item.sUserId = this.selectedRow.sId;
                item.sUserName = this.selectedRow.sName;
                item.sUserNo = this.selectedRow.sNo;
                item.sBusRoleCode = item.sValue;
                item.sBusRoleName = item.sName;
                delete item.sName;
                delete item.sValue;
            });
            this.loading.userType = true
            Api.saveBussinessRole(checkList).then(res => {
                this.loading.userType = false
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading.userType = false
                console.log(err)
            })
        },
        // 保存报告医生级别
        onSaveReportWriter () {
            if (!Object.keys(this.selectedRow).length) {
                this.$message.warning('请选择用户数据');
                return
            }
            let checkList = deepClone(this.reportWriterRoles).filter(item => this.checkWriterGroups.includes(item.sValue));
            // 没有勾选项的时候调用删除接口
            if (!checkList.length) {
                this.loading.level = true;
                Api.delReportWriter({ userId: this.selectedRow.sId }).then(res => {
                    this.loading.level = false;
                    if (res.success) {
                        this.$message.success(res.msg);
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    this.loading.level = false
                    console.log(err)
                })
                return
            }
            // 存在勾选项的是时候，给勾选项添加用户信息属性，调用保存接口
            checkList.forEach(item => {
                item.sUserId = this.selectedRow.sId;
                item.sUserName = this.selectedRow.sName;
                item.sUserNo = this.selectedRow.sNo;
                item.sReportWriterCode = item.sValue;
                item.sReportWriterName = item.sName;
                delete item.sName;
                delete item.sValue;
            });
            let params = checkList[0];
            this.loading.level = true;
            Api.saveReportWriter(params).then(res => {
                this.loading.level = false;
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading.level = false;
                console.log(err)
            })
        },
        // 保存打印控制
        onSavePrint () {
            if (!Object.keys(this.selectedRow).length) {
                this.$message.warning('请选择用户数据');
                return
            }
            let checkList = deepClone(this.printControls).filter(item => this.checkPrintDroups.includes(item.sValue));
            // 没有勾选项的时候调用删除接口
            if (!checkList.length) {
                this.loading.print = true;
                Api.delPrint({ userId: this.selectedRow.sId }).then(res => {
                    this.loading.print = false;
                    if (res.success) {
                        this.$message.success(res.msg);
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    this.loading.print = false;
                    console.log(err)
                })
                return
            }
            // 存在勾选项的是时候，给勾选项添加用户信息属性，调用保存接口
            checkList.forEach(item => {
                item.sUserId = this.selectedRow.sId;
                item.sUserName = this.selectedRow.sName;
                item.sUserNo = this.selectedRow.sNo;
                item.sVisitTypeCode = item.sValue;
                item.sVisitTypeName = item.sName;
                delete item.sName;
                delete item.sValue;
            });
            this.loading.print = true;
            Api.savePrint(checkList).then(res => {
                this.loading.print = false;
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading.print = false;
                console.log(err)
            })
        },
        //部门树组件点击事件
        handleDepartmentNodeClick (item) {
            this.checkDeviceGroups = [];
            this.checkBussinessGroups = [];
            this.checkWriterGroups = [];
            this.checkPrintDroups = [];
            this.getPersonByDepartmentData(item);
        },
        // 表格行点击事件
        onTableRow (row) {
            this.selectedRow = row;
            this.getFindSet(row)
        },
        // 查找用户设置
        getFindSet (row) {
            Api.getFindSet({ sUserId: row.sId }).then(res => {
                if (res.success) {
                    let deviceTypeSet = res.data.deviceTypeSet || [];
                    deviceTypeSet.map(item => {
                        this.checkDeviceGroups.push(item.sDeviceTypeId);
                    });
                    this.checkBussinessGroups = res.data.busRoleSet ? res.data.busRoleSet.map(item => item.sBusRoleCode) : [];
                    this.checkWriterGroups = res.data.reportWriterSet ? [res.data.reportWriterSet.sReportWriterCode] : [];
                    this.checkPrintDroups = res.data.printSet ? res.data.printSet.map(item => item.sVisitTypeCode) : [];
                    return
                }
            }).catch(err => {
                console.log(err)
            })
        },
        // 根据部门编号查询员工
        getPersonByDepartmentData (item) {
            Api.getPersonByDepartmentData({ sDeparmentId: item.sId }).then(res => {
                this.selectedRow = {};
                this.tableData = [];
                if (res.success) {
                    this.tableData = res.data;
                    return;
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 部门数据
        getDepartmentData () {
            Api.getDepartmentData().then(res => {
                if (res.success) {
                    this.deparmentTree = res.data;
                    return;
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 获取设备下拉
        getDeviceOptions () {
            // this.deviceLoading = true;
            getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.deviceOptions = res?.data || [];
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => {
                // this.isSearch = true;
                // this.deviceLoading = true;
            })
        },
    },
    mounted () {
        this.getDepartmentData();
        this.getDeviceOptions();
    },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    padding: 11px 0 0;
    overflow-x: auto;
    .c-content {
        min-width: 250px;
        &.t-2 {
            min-width: 350px;
        }
        padding: 0 10px;
        border-right: 1px solid #eee;
        .c-item {
            overflow: auto;
            margin-bottom: 10px;
        }
        .c-item.t-1 {
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .el-tree {
            width: 100%;
            height: 100%;
            overflow: auto;
        }
        :deep(.el-tree > .el-tree-node ){
            min-width: 100%;
            display: inline-block;
        }
        :deep(.el-button.el-button--text ){
            margin-right: 40px;
        }
        .i-item {
            float: left;
            width: 210px;
            padding: 0 10px 30px;
            &:first-child {
                padding-left: 0;
            }
        }
        .el-checkbox,
        .el-radio {
            display: block;
            margin-top: 10px;
        }
    }
}
.c-flex-x {
    display: flex;
}
.c-flex-y {
    display: flex;
    flex-direction: column;
}
.c-flexChild {
    flex: 1;
}
:deep(.el-button.el-button--text) {
    padding: 2px 8px;
    font-size: 16px;
}
:deep(.el-checkbox__inner) {
    width: 16px;
    height: 16px;
    &:after {
        width: 4px;
        height: 8px;
        left: 4px;
    }
} 
</style>
