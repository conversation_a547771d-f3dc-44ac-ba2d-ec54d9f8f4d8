// 随访
export default {
    DA1: {
        type: 't-y',
        localStorageKey: '202101071826',
        panelConfig: [
            {
                size: 0,
                minSize: 45,
                name: "c1",
                isFlexible: true
            },
            {
                size: 180,
                minSize: 180,
                maxSize: 600,
                name: "c2",
                isFlexible: false
            }
        ]
    },
    followRecordInput: [
        {
            sProp: 'dPlanTime',
            sLabel: '预约随访时间',
            // iRequired: 1,
            sInputType: 'date-picker',
            width: '33.33%'
        },
        {
            sProp: 'dFollowDate',
            sLabel: '随访日期',
            // iRequired: 1,
            sInputType: 'date-picker',
            width: '33.33%'
        },
        {
            sProp: 'sDoctorId',
            sLabel: '随访医生',
            sInputType: 'option',
            sOptionProp: 'sDoctorIdOptions',
            width: '33.33%'
        },
        {
            sProp: 'sFollowMode',
            sLabel: '随访方式',
            sOptionProp: 'sFollowModeOptions',
            sInputType: 'option',
            width: '33.33%'
        },
        {
            sProp: 'sVisitState',
            sLabel: '随访状态',
            sOptionProp: 'sVisitStateOptions',
            sInputType: 'option',
            width: '33.33%'
        },
        {
            sProp: 'iIsAccord',
            sLabel: '随访符合',
            sOptionProp: 'iIsAccordOptions',
            sInputType: 'option',
            width: '33.33%'
        },
        {
            sProp: 'sReason',
            sLabel: '随访原因',
            //iRequired: 1,
            sInputType: 'textarea',
            width: '100%'
        },
        {
            sProp: 'sTreatment',
            sLabel: '治疗情况',
            sHeight: '150px',
            sInputType: 'textarea',
            width: '100%'
        },
        {
            sProp: 'sResult',
            sLabel: '随访结果',
            sHeight: '150px',
            sInputType: 'textarea',
            width: '100%'
        }

    ],
    // 已随访表格
    visitedTable: [
        {
            sProp: 'dFollowDate',
            sLabel: '随访日期',

        }, {
            sProp: 'sDoctorName',
            sLabel: '随访医生',
        }, {
            sProp: 'sFollowModeText',
            sLabel: '随访方式',
            sAlign: 'center'
        }, {
            sProp: 'sVisitStateText',
            sLabel: '随访状态',
            sAlign: 'center'
        },
        {
            sProp: 'sIsAccordText',
            sLabel: '随访符合',
            sAlign: 'center'
        },
        {
            sProp: 'Actions',
            sLabel: '操作',
            sWidth: '140px',
            sAlign: 'center'
        },
    ],
}