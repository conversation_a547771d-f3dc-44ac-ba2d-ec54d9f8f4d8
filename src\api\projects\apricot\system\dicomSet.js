import request, {baseURL, stringify} from '$supersetUtils/request'
export default {
    // Dicom节点管理操作接口 : Dicom Node Manage Controller
    getDicomNode,

    addDicomNode(data) {
        return request({
            url: baseURL.apricot + '/dicom/node/manage/addDicomNode',
            method: 'POST',
            data
        })
    },

    editDicomNode(data) {
        return request({
            url: baseURL.apricot + '/dicom/node/manage/editDicomNode',
            method: 'POST',
            data
        })
    },

    delDicomNode(params) {
        return request({
            url: baseURL.apricot + '/dicom/node/manage/deleteDicomNode',
            method: 'POST',
            data:stringify(params)
        })
    },

    dicomTestConnect(params) {
        return request({
            url: baseURL.image + '/ris/dicom/test/connect',
            method: 'POST',
            data:stringify(params)
        })
    },

    dicomEnable(params) {
        return request({
            url: baseURL.apricot + '/dicom/node/manage/enable',
            method: 'POST',
            data:stringify(params)
        })
    },

    dicomDefault(params) {
        return request({
            url: baseURL.apricot + '/dicom/node/manage/default',
            method: 'POST',
            data:stringify(params)
        })
    },

    dicomPictureType(params) {
        return request({
            url: baseURL.apricot + '/dicom/node/manage/picture/type',
            method: 'POST',
            data:stringify(params)
        })
    },

}

export function getDicomNode(data) {
    return request({
        url: baseURL.apricot + '/dicom/node/manage/queryAllDicomNode',
        method: 'POST',
        data
    })
}