<template>
    <el-dialog
		append-to-body
		title="预览查看"
		v-model="innerVisible"
        class="my-dialog my-full-dialog my-padding-dialog"
        width="1150px"
        fullscreen
        :close-on-click-modal="false"
        @open="handleOpenDialog"
        @close="handleCloseDialog"
	>
    <div class="c-dialog-body">
        <RenderMainGenerator ref="generator" :renderData="renderDataInner"></RenderMainGenerator>
    </div>
    <template #footer>
        <div style="padding: 10px">
            <el-button-icon-fa type="primary"
            _icon="fa fa-save"
            @click="onClickSave">保存</el-button-icon-fa>
        <el-button-icon-fa
            _icon="fa fa-close-1"
            @click="innerVisible = false">关闭</el-button-icon-fa>
        </div>
    </template>
    </el-dialog>
</template>
<script>
import RenderMainGenerator from './RenderMainGenerator';
import { deepClone } from '$supersetUtils/function';
export default {
    components: {
        RenderMainGenerator
    },
    emits: [ 'update:modelValue'],
    props: {
        modelValue: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        renderData: {
            default: () => []
        }
    },
    data() {
        return {
            renderDataInner: [],
        }
    },
    computed: {
        innerVisible: {
            get: function() {
                return this.modelValue;
            },
            set: function(val) {
                this.$emit('update:modelValue', val);
            }
        }
    },
    methods: {
        handleOpenDialog() {
            this.renderDataInner = deepClone(this.renderData);
        },
        handleCloseDialog() {
            this.renderDataInner = [];
        },
        onClickSave() {
            this.$refs['generator'].validate().then(res => {
                if (res.code === 200) {
                    this.$message({
                        type: 'info',
                        message: res.data
                    });
                }
            })
        },
        onClickClose() {

        }
    }
}
</script>
<style lang="scss" scoped>
:deep(.el-dialog.is-fullscreen) {
    width: 80%;
    height: 80%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    overflow: auto;
}
:deep(.plane-content .scrollbar .scrollbar-form) {
    padding: 10px;
}
</style>
