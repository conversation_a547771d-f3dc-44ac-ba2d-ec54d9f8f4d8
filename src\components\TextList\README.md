# TextList 通用文本展示组件

示例：
```
  <TextList storageKey="testtext-1" iModuleId="6"
      :list="TextListDataArray" :data="TextListDataObj" :configBtn="buttonRef" >
    <template #customBtn>
          <el-button>1</el-button>
    </template>
  </TextList>
  <el-button type="primary" :ref="(el) => buttonRef = el">替换按钮</el-button>
```

属性: 
| 属性名 | 说明 | 格式| 默认值 | 
|----|--------------|---|---| 
| storageKey  | 保存到localstorage时的唯一标识，不填则不保存   | string | 无  |  
| iModuleId  | 保存到服务器时的moduleId字段，与storageKey同时存在时启用数据上传   | string | 无  |  
| list  | 要展示的文本的配置数据，格式见示例   | array | []  |  
| data  | 数据源对象   | object | {}  |  
| configBtn  | 配置按钮是否显示，或用于替换默认按钮的ref对象   | boolean,Ref | true  |   
| labelWidth  | 统一设置每个文本内容的label-width   | string | 无  |  


插槽:
|  插槽名称 | 说明  |
|-----------|-------------------------|
| customBtn |  放在设置按钮旁边的按钮插槽    |


 配置数据示例：
 ``` 
 list: [
        {     
            sProp: 'sSexText',
            sLabel: '性别',
            iIsHide: false,  // 为true时排除掉该配置项
            prop: '',  // 与sProp任选一个
            label: '',  // 与sLabel任选一个
            isShow: true, // 不为false时默认显示该项（可设置）

            // 配置了样式属性时，会改变该配置项的默认样式
            align: 'center',  
            width: '25%',  
            height: '36px',  
            labelWidth: '100px',  
            color: '#2D3748',  
            isBold: false,  
            fontSize: '14px',  
        },
        // 只有prop或sProp必填
        {
            sProp: 'sAge',
            sLabel: '年龄', 
        },
 ],
data: {
  sName: "李潇潇",
  sNameSpell: "LI XIAO XIAO",
  sNuclearNum: "22000002",
}
 ```



