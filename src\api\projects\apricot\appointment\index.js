import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'

export function getItemTree(params) {
    return request({
        url: baseURL.apricot + '/item/find/tree',
        method: 'POST',
        data: stringify(params)
    })
}

//根据项目获取核医学号
export function getNuclearNum(params) {
    return request({
        url: baseURL.apricot + '/access/num/rule/related/new/num',
        method: 'POST',
        params
    })
}

//获取历史患者核医学号
export function getHistoryNuclearNum(data) {
    return request({
        url: baseURL.apricot + '/access/num/rule/related/history/sNuclearNum',
        method: 'POST',
        data
    })
}
// 获取影像号
export function brokenImgno(data) {
    return request({
        url: baseURL.broken + '/imgno',
        method: 'POST',
        data
    })
}

//获取当天项目时间段
export function getDayProjectTime(params) {
    return request({
        url: baseURL.apricot + '/plan/and/project/project/time',
        method: 'POST',
        params
    })
}

//保存预约
export function postPatientInfoAdd(data) {
    return request({
        url: baseURL.apricot + '/patient/info/add',
        method: 'POST',
        data
    })
}

//获取元素配置数据
export function getElementConfigBykeyword(data) {
    return request({
        url: baseURL.apricot + '/adjust/set/find/nodes',
        method: 'POST',
        data
    })
}

//获取预约表格统计数据
export function getItemAppointmentCount(params) {
    return request({
        url: baseURL.apricot + '/plan/and/project/project/date/count',
        method: 'POST',
        params
    })
}
 

//获取患者名字历史数据
export function getPatientNameHistoryData(data) {
    return request({
        url: baseURL.apricot + '/past/patient/info/pastcheck',
        method: 'POST',
        data
    })
}

//获取当天统计数据
// 每日预约分组统计
export function getDailyCount(params) {
    return request({
        url: baseURL.apricot + '/project/and/patient/appointment/daily/count',
        method: 'POST',
        params
    })
}

// 中文转拼音操作接口 : Pin Yin Controller
export function chineseToPinyin(params) {
    return request({
        url: baseURL.apricot + '/pinyin/chinese/pinyin',
        method: 'POST',
        params
    })
}

export function findKeyAutoItemSet(data) {
    return request({
        url: baseURL.apricot + '/auto/item/set/find/register/item',
        method: 'POST',
        data
    })
}

//  查询医嘱申请单
export function queryApply(params) {
    return request({
        url: baseURL.apricot + '/register/query/queryApply',
        method: 'POST',
        data: stringify(params)
    })
}

//  日历统计查询
export function queryCalendar(params) {
    return request({
        url: baseURL.apricot + '/register/query/queryCalendar',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取号源 
export function queryNumSource(params) {
    return request({
        url: baseURL.apricot + '/register/query/queryNumSource',
        method: 'POST',
        data: stringify(params)
    })
}

// 推荐号源 
export function recommendNumSource(params) {
    return request({
        url: baseURL.apricot + '/register/query/recommendNumSource',
        method: 'POST',
        data: stringify(params)
    })
}

// 加号
export function addNumSource(params) {
    return request({
        url: baseURL.apricot + '/register/numsource/addNumSource',
        method: 'POST',
        data: stringify(params)
    })
}
// 留号
export function reserveNumSource(params) {
    return request({
        url: baseURL.apricot + '/register/numsource/reserveNumSource',
        method: 'POST',
        data: stringify(params)
    })
}
// 生成号源 
export function generateNumSource(params) {
    return request({
        url: baseURL.apricot + '/register/numsource/generateNumSource',
        method: 'POST',
        data: stringify(params)
    })
}
export function removeNumSource(data) {
    return request({
        url: baseURL.apricot + '/register/numsource/removeNumSource',
        method: 'POST',
        data
    })
}
// 获取时间段 
export function queryTimeByRoomId(params) {
    return request({
        url: baseURL.apricot + '/register/template/queryTimeByRoomId',
        method: 'POST',
        data: stringify(params)
    })
}
// 统计
export function queryItemStatistics(params) {
    return request({
        url: baseURL.apricot + '/register/query/queryItemStatistics',
        method: 'POST',
        data: stringify(params)
    })
}

// 预约
export function appointmentBook(data) {
    return request({
        url: baseURL.apricot + '/appointment/book',
        method: 'POST',
        data
    })
}

// 新版改约-2023-09-28
export function appointmentBookSave(data) {
    return request({
        url: baseURL.apricot + '/appointment/book/save',
        method: 'POST',
        data
    })
}


// 签到
export function appointmentSignIn(params) {
    return request({
        url: baseURL.apricot + '/appointment/signIn',
        method: 'POST',
        data: stringify(params)
    })
}
// 取消签到
export function appointmentSignInCancel(params) {
    return request({
        url: baseURL.apricot + '/appointment/signInCancel',
        method: 'POST',
        data: stringify(params)
    })
}
// 取消检查
export function appointmentCancel(data) {
    return request({
        url: baseURL.apricot + '/appointment/cancel',
        method: 'POST',
        data
    })
}
// 改约
export function appointmentChange(data) {
    return request({
        url: baseURL.apricot + '/appointment/change',
        method: 'POST',
        data: data
    })
}
// 撤约
export function appointmentWithdraw(params) {
    return request({
        url: baseURL.apricot + '/appointment/withdraw',
        method: 'POST',
        data: stringify(params)
    })
}
