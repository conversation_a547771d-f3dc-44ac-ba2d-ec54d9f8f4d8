<template>
    <div class="c-remade m-flexLaout-tx">
        <div class="c-tabs">
            <template v-for="(item, index) in tabList.filter(item => item.isShow)" :key="index">
                <div class="c-tab-item" :class="{ 'c-active': activeTab === item.name, 'i-center': isCollapse }"
                    :title="item.label" :name="item.name" @click="onJumpClick(index)">
                    <i class="fa" :class="item.icon" :title="item.label" style="font-size: 20px;"></i>
                    <el-badge v-if="item.name === 'historyInfo'" :hidden="!pastCount" :value="pastCount" :max="99"
                        type="warning"></el-badge>
                    <el-badge v-if="item.name === 'scannerInfo'" :hidden="!scanningDataCount" :value="scanningDataCount"
                        :max="99" type="warning"></el-badge>
                    <el-badge v-if="item.name === 'injectInfo'" :hidden="!injectInfoData.length"
                        :value="injectInfoData.length" :max="99" type="warning"></el-badge>
                    <!-- <span style="font-size:12px; top:-4px;">{{item.label}}</span> -->
                </div>
            </template>
            
            <PanelInfoMenuConfig class="c-setting" :iModuleId="iModuleId" :formData="tabList" 
                :configKey="'ReportMngIndexPatientMenuConfig'" @updateData="updateDataConfig"></PanelInfoMenuConfig>
        </div>
        <div class="g-flexChild c-template c-patientInfo" v-loading="loading">
            <header class="c-head-item" :class="{ 'c-shadow': 1 }" style="position: relative;">
                <i v-if="info.sName" class="el-icon-user-solid i-icon-user"></i>
                <section class="i-item-01">
                    <el-row>
                        <el-col :span="12" :title="info.sName" class="i-name i-weight">
                            {{ info.sName }}
                        </el-col>
                        <el-col :span="6" class="i-name">
                            {{ info.sSexText }}
                            <i v-if="info.sSexText == '男'" class="fa fa-male2" style="color: dodgerblue"></i>
                            <i v-if="info.sSexText == '女'" class="fa fa-female1" style="color:hotpink"></i>
                        </el-col>
                        <el-col :span="6" class="i-name">{{ info.sBriefAge }}</el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="12" :title="info.sNuclearNum" class="i-name">{{ info.sNuclearNum }}</el-col>
                        <el-col :span="6" class="i-name">{{ info.sSourceText }}</el-col>
                        <el-col v-if="patientInfo.sCheckStateText" :span="6" class="i-name">
                            {{ patientInfo.sCheckStateText }}
                        </el-col>
                    </el-row>
                </section>
            </header>

            <section class="c-content">
                <el-scrollbar ref="scrollbar" class="c-scrollbar xx-el-scrollbar">
                    <section class="c-plan">
                        <template v-for="item in tabList.filter(item =>item.isShow)">
                            <section v-if="item.name=== 'patientInfo'" id="patientInfo" class="c-item">
                                <h4><span>基本信息</span></h4>
                                <TextList :list="textBaseConfig.filter(item => item.prop == 'iConsultation'? currentModule === 'Report' && patientInfo.iConsultation == 1 :true)" 
                                    :data="info" class="none-border" :labelWidth="80"
                                    :iModuleId="iModuleId" storageKey="ReportMngIndexPatientBaseInfo"
                                    style="padding-right: 10px;">
                                    <template #iConsultation="{ style }">
                                        <span :style="style" style="font-weight: bold"> 已发送 </span>
                                    </template>
                                    </TextList>

                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'checkInfo'" id="checkInfo" class="c-item">
                                <h4><span>检查信息</span></h4>
                                <TextList :list="textCheckConfig" :data="info" class="none-border" :labelWidth="80"
                                    :iModuleId="iModuleId" storageKey="ReportMngIndexPatientCheckInfo"
                                    style="padding-right: 10px;">
                                    <template #fBloodSugar="{ row, style }">
                                        <span :style="style" title="">
                                            {{ (info[row.prop] !== undefined  && info[row.prop] !== null) ? info[row.prop] + 'mmol/L': '（空）' }}
                                        </span>
                                    </template>
                                    <template #sNuclideSupName="{ row, style }">
                                        <span v-if="info.sNuclideSupName" v-html="info.sNuclideSupName" :style="style"></span>
                                        <span v-else :style="style">{{ info.sNuclideText }}</span>

                                        <span v-if="info.sTracerSupName" :style="style"
                                            v-html="info.sTracerSupName ? (info.sTracerSupName && info.sTracerSupName.substring(0, 1) === '-' ? info.sTracerSupName : '-' + info.sTracerSupName) : info.sTracerSupName"></span>
                                        <span v-else :style="style">
                                            {{ info.sTracerText ? (info.sTracerText.substring(0, 1) === '-' ? info.sTracerText : '-' + info.sTracerText) : info.sTracerText }}
                                        </span>
                                    </template>
                                    <template #dAppointmentTime="{ row, style }">
                                        <span :style="style" :title="transformDate(info[row.prop], true)">
                                            {{ transformDate(info[row.prop] ,true) || '（空）' }}
                                        </span>
                                    </template>
                                    <template #dConsultTime="{ row, style }">
                                        <span :style="style" :title="transformDate(info[row.prop], true)">
                                            {{ transformDate(info[row.prop] ,true) || '（空）' }}
                                        </span>
                                    </template>
                                    <template #dInjectTime="{ row, style }">
                                        <span :style="style" :title="transformDate(info[row.prop], true)">
                                            {{ transformDate(info[row.prop] ,true) || '（空）' }}
                                        </span>
                                    </template>
                                    <template #dFactCheckTime="{ row, style }">
                                        <span :style="style" :title="transformDate(info[row.prop], true)">
                                            {{ transformDate(info[row.prop] ,true) || '（空）' }}
                                        </span>
                                    </template>
                                </TextList>
                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'clinicInfo'" id="clinicInfo" class="c-item t-1 clearfix">
                                <div>
                                    <h4>问诊信息</h4> 
                                    <div class="i-btn cursor-pointer float-right" @click="onEditConsultInfoClick">
                                        <Icon type="el-icon" name="el-icon-edit-outline" size="18"></Icon>
                                        <span>编辑</span>
                                    </div>
                                </div>
                                <h5 style="margin: 0;padding: 10px 15px;"><span>临床诊断</span></h5>
                                <div class="c-textarea i-lczd" v-html="info.sClinicalDiagnosis"></div>

                                <h5 style="margin: 0;padding: 10px 15px;"><span>简要病史</span></h5>
                                <div class="c-textarea" v-html="info.sMedicalHistory"></div>

                                <h5 style="margin: 0;padding: 10px 15px;"><span>既往病史</span></h5>
                                <div class="c-textarea" v-if="info.sInquiryOtherMessage" v-html="info.sInquiryOtherMessage">
                                </div>
                                <div class="c-textarea" v-else v-html="info.sPastHistory"></div>

                                <div class="i-border"></div>
                            </section>

                            <section v-if="item.name=== 'historyInfo'" id="historyInfo" class="c-item" v-loading="loadObject.history">

                                <div>
                                    <h4>历史报告</h4>
                                    <el-popover v-model:visible="isShowHistoryMatchPop" placement="left" title="匹配条件："
                                        width="450" trigger="click" popper-class="back-popover" style="">
                                        <div class="back-popover-div" style="">
                                            <span>
                                                <el-checkbox v-model="historyRelatedCheck.hasRecordNo" :true-label="1"
                                                    :false-label="0" @change="onChangeHisReportConditions">病历号</el-checkbox>
                                            </span>
                                            <span>
                                                <el-checkbox v-model="historyRelatedCheck.hasNuclearNum" :true-label="1"
                                                    :false-label="0" @change="onChangeHisReportConditions">核医学号</el-checkbox>
                                            </span>
                                            <span>
                                                <el-checkbox v-model="historyRelatedCheck.hasIdNum" :true-label="1"
                                                    :false-label="0" @change="onChangeHisReportConditions">身份证号</el-checkbox>
                                            </span>
                                            <span>
                                                <el-checkbox v-model="historyRelatedCheck.hasHealthCardNO" :true-label="1"
                                                    :false-label="0" @change="onChangeHisReportConditions">健康卡号</el-checkbox>
                                            </span>
                                            <span>
                                                <el-checkbox v-model="historyRelatedCheck.hasVisitCard" :true-label="1"
                                                    :false-label="0" @change="onChangeHisReportConditions">就诊卡号</el-checkbox>
                                            </span>
                                            <span>
                                                <el-checkbox v-model="historyRelatedCheck.orderByAppointmentTime"
                                                    :true-label="1" :false-label="0"
                                                    @change="onChangeHisReportConditions">检查日期（升序）</el-checkbox>
                                            </span>
                                            <div class="mt-2.5">
                                                <el-button-icon-fa class="float-right" icon="fa fa-close-1"
                                                    @click="isShowHistoryMatchPop = false">关闭</el-button-icon-fa>
                                            </div>
                                        </div>
                                        <template #reference>
                                            <div class="i-btn cursor-pointer float-right">
                                                <i class="fa fa-filter" style="top: -2px;"></i>
                                                <span>筛选</span>
                                            </div>
                                        </template>
                                    </el-popover>
                                </div>
                                <div style="width: calc(100% - 30px);margin: 15px;">
                                    <el-table v-if="hisReportList.length"
                                        :data="hisReportList"
                                        row-key="sId"
                                        ref="hisDom"
                                        border
                                        stripe
                                        highlight-current-row
                                        style="width: 100%"
                                        :row-class-name="rowClassName"
                                        :expand-row-keys="expandsHistoryReport"
                                        @expand-change="expandHistoryReportChange"
                                        @row-click="onChangeHisReportClick">
                                        <el-table-column type="expand" >
                                            <template v-slot="{ row }">
                                                <!--  v-loading="hisReportTableRowLoading" -->
                                                <div style="padding: 10px 0px 0px 0px;background: #f9fbfa;">
                                                    <p class="" style="padding-left: 6px;">
                                                        <span class="i-point"></span>
                                                        <strong>图像</strong>
                                                    </p>
                                                    <div style="margin-left: 12px">
                                                        <el-button v-if="$auth['report:report:csView']" type="primary" size="small" plain @click.native.stop="rebuildOpen(row, 0)">阅图</el-button>
                                                        <el-button v-if="$auth['report:report:csRebuild']" type="primary" size="small" plain @click.native.stop="rebuildOpen(row, 1)">重建</el-button>
                                                        <el-button v-if="$auth['report:report:webView']" type="primary" size="small" plain @click.native.stop="openWebReadViewer(row)">web阅图</el-button>
                                                        <el-button v-if="$auth['report:report:webRebuild']" type="primary" size="small" plain @click.native.stop="openWebReBuildViewer(row)">web重建</el-button>
                                                    </div>
                                                    <p class="" style="padding-left: 6px;">
                                                        <span class="i-point"></span>
                                                        <strong>基本信息</strong>
                                                    </p>
                                                    <label-info label="身份证号">
                                                        {{ row.sIdNum }}
                                                    </label-info>
                                                    <label-info label="来源">
                                                        {{ row.sSourceText }}
                                                    </label-info>
                                                    <label-info label="核医学号">
                                                        {{ row.sNuclearNum }}
                                                    </label-info>
                                                    <label-info label="病历号">
                                                        {{ row.sMedicalRecordNO }}
                                                    </label-info>
                                                    <label-info label="检查日期">
                                                        {{ transformDate(row.dAppointmentTime) }}
                                                    </label-info>
                                                    <label-info label="检查类型">
                                                        {{ row.sRoomText }}
                                                    </label-info>
                                                    <label-info label="检查项目">
                                                        {{ row.sProjectName }}
                                                    </label-info>
                                                    <label-info label="门诊号">
                                                        {{ row.sOutpatientNO }}
                                                    </label-info>
                                                    <label-info label="住院号">
                                                        {{ row.sInHospitalNO }}
                                                    </label-info>
                                                    <label-info label="健康卡号">
                                                        {{ row.sHealthCardNO }}
                                                    </label-info>
                                                    <label-info label="就诊卡号">
                                                        {{ row.sVisitCard }}
                                                    </label-info>
                                                    <div style="padding: 10px 0px;">
                                                        <p class="secondTitle">
                                                            <span class="i-point"></span>
                                                            <strong>检查所见</strong>
                                                            <el-button-icon-fa icon="el-icon-document-copy" link type="primary" plain
                                                                size="small" title="复制" class="copy-btn"
                                                                style="padding: 5px; margin-left: 14px;" data-clipboard-action="copy"
                                                                data-clipboard-target="#text-result-sInspectSee"
                                                                @click="onClickCopyHistoryReport">
                                                            </el-button-icon-fa>
                                                        </p>
                                                        <div class="text-result" id="text-result-sInspectSee"
                                                            v-html="hisReportCurrentItem.sInspectSee"></div>
                                                    </div>
                                                    <div style="padding: 10px 0px;">
                                                        <p class="secondTitle">
                                                            <span class="i-point"></span>
                                                            <strong>诊断意见</strong>
                                                            <el-button-icon-fa icon="el-icon-document-copy" link type="primary" plain
                                                                size="small" title="复制" class="copy-btn"
                                                                style="padding: 5px; margin-left: 14px;" data-clipboard-action="copy"
                                                                data-clipboard-target="#text-result-sDiagnosticOpinion"
                                                                @click="onClickCopyHistoryReport">
                                                            </el-button-icon-fa>
                                                        </p>
                                                        <div class="text-result" id="text-result-sDiagnosticOpinion"
                                                            v-html="hisReportCurrentItem.sDiagnosticOpinion"></div>
                                                    </div>
                                                    <div v-if="historyFollowList.length">
                                                        <p style="padding: 0px 15px;">
                                                            <span class="i-point"></span>
                                                            <strong>随访记录（共{{ historyFollowList.length }}条）</strong>
                                                        </p>
                                                        <div v-for="(item, index) in historyFollowList" :key="index">
                                                            <p style="padding-left: 15px;">第{{ index + 1 }}条</p>
                                                            <label-info label="随访状态">
                                                                {{ item.sVisitStateText }}
                                                            </label-info>
                                                            <label-info label="随访医生">
                                                                {{ item.sDoctorName }}
                                                            </label-info>
                                                            <label-info label="随访方式">
                                                                {{ item.sFollowModeText }}
                                                            </label-info>
                                                            <label-info label="随访日期">
                                                                {{ transformDate(item.dFollowDate) }}
                                                            </label-info>
                                                            <label-info label="随访符合">
                                                                {{ item.sIsAccordText }}
                                                            </label-info>
                                                            <label-info label="随访原因" :title="item.sReason">
                                                                {{ item.sReason }}
                                                            </label-info>
                                                            <label-info label="治疗情况" :title="item.sTreatment">
                                                                {{ item.sTreatment }}
                                                            </label-info>
                                                            <label-info label="随访结果" :title="item.sResult">
                                                                {{ item.sResult }}
                                                            </label-info>

                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <!-- show-overflow-tooltip -->
                                        <el-table-column
                                            label="检查项目"
                                            prop="sProjectName"
                                            show-overflow-tooltip>
                                        </el-table-column>
                                        <el-table-column
                                            label="检查日期"
                                            prop="dAppointmentTime"
                                            show-overflow-tooltip
                                            width="110">
                                            <template v-slot="{ row }">
                                                {{ transformDate(row.dAppointmentTime) }}
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <div v-else>
                                        <el-empty :image-size="80" description=" " />
                                    </div>
                                </div>
                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'scannerInfo'" id="scannerInfo" class="c-item clearfix" v-loading="loadObject.scanner">
                                <div>
                                    <h4>病例资料</h4>
                                    <div class="i-btn cursor-pointer float-right" @click="onEditLookImage">
                                        <Icon type="el-icon" name="el-icon-upload2" size="18" style="top: -2px;"></Icon>
                                        <span>上传</span>
                                    </div>
                                </div>
                                <div class="c-img" v-if="ImageUrls.length"
                                    :class="{ 'img-anticlocelise': imageViewerSetting.rotate == 'anticlocelise', 'img-clocelise': imageViewerSetting.rotate == 'clocelise' }">
                                    <div class="c-img-item" v-for="(item, index) in ImageUrls" :key="index"
                                        @click="onClickImage(index)">
                                        <el-image :src="item" lazy fit="contain" :initial-index="index">
                                        </el-image>
                                    </div>
                                    <div class="c-img-item t-1" v-if="ImageUrls.length % 2 > 0"></div>
                                    <el-image-viewer ref="imgViewe2" v-if="imgModal" @switch="onSwitch" @close="onClose"
                                        :url-list="ImageUrls" :initial-index="imgSrcIndex">
                                    </el-image-viewer>
                                </div>
                                <div v-else>
                                    <el-empty :image-size="80" description=" " />
                                </div>
                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'injectInfo'" id="injectInfo" class="c-item" v-loading="loadObject.inject">

                                <div>
                                    <h4>注射记录</h4>
                                    <el-popover v-model:visible="isShowSetPop" placement="left" title="注射信息标签显示配置：" width="350"
                                        trigger="click">
                                        <div style="padding: 0 15px;">
                                            <div style="margin-top:30px;">
                                                <el-col class="u-text-ellipsis" :span="6">满针时间：</el-col>
                                                <el-radio-group v-model="cacheReportInfoShowSet.isShowdInjectionSta"
                                                    class="margin_t" size="small" @change="handleCacheReportInfoShowSet">
                                                    <el-radio :label="1">显示</el-radio>
                                                    <el-radio :label="0">隐藏</el-radio>
                                                </el-radio-group>
                                            </div>
                                            <div style="margin-top:15px;">
                                                <el-col class="u-text-ellipsis" :span="6">满针剂量：</el-col>
                                                <el-radio-group v-model="cacheReportInfoShowSet.isShowfFullNeedle"
                                                    class="margin_t" size="small" @change="handleCacheReportInfoShowSet">
                                                    <el-radio :label="1">显示</el-radio>
                                                    <el-radio :label="0">隐藏</el-radio>
                                                </el-radio-group>
                                            </div>
                                            <div style="margin-top:15px;">
                                                <el-col class="u-text-ellipsis" :span="6">空针时间：</el-col>
                                                <el-radio-group v-model="cacheReportInfoShowSet.isShowdInjectionEnd"
                                                    class="margin_t" size="small" @change="handleCacheReportInfoShowSet">
                                                    <el-radio :label="1">显示</el-radio>
                                                    <el-radio :label="0">隐藏</el-radio>
                                                </el-radio-group>
                                            </div>
                                            <div style="margin-top:15px;">
                                                <el-col class="u-text-ellipsis" :span="6">空针剂量：</el-col>
                                                <el-radio-group v-model="cacheReportInfoShowSet.isShowfEmptyNeedle"
                                                    class="margin_t" size="small" @change="handleCacheReportInfoShowSet">
                                                    <el-radio :label="1">显示</el-radio>
                                                    <el-radio :label="0">隐藏</el-radio>
                                                </el-radio-group>
                                            </div>
                                            <div style="margin-top:15px;">
                                                <el-col class="u-text-ellipsis" :span="6">渗漏：</el-col>
                                                <el-radio-group v-model="cacheReportInfoShowSet.isShowsStainPosition"
                                                    class="margin_t" size="small" @change="handleCacheReportInfoShowSet">
                                                    <el-radio :label="1">显示</el-radio>
                                                    <el-radio :label="0">隐藏</el-radio>
                                                </el-radio-group>
                                            </div>
                                            <div
                                                style="margin-top: 20px;padding-top: 10px;border-top: 1px solid #eee;text-align: right;">
                                                <el-button size="small" @click="isShowSetPop = false">关闭</el-button>
                                            </div>
                                        </div>
                                        <template #reference>
                                            <Icon type="el-icon" name="MoreFilled" class="i-btn cursor-pointer float-right i-hover"
                                                size="18" title="显示配置">
                                            </Icon>
                                        </template>
                                    </el-popover>
                                </div>
                                <template v-if="injectInfoData.length" > 
                                    <template v-for="(item, index) in injectInfoData" :key="index">
                                        <p v-if="injectInfoData.length > 1 " style="padding-left: 15px;"> {{ `第${(index + 1)}条` }}</p>
                                        <label-info label="药物核素">
                                            <span v-if="item.sNuclideSupName" v-html="item.sNuclideSupName"></span>
                                            <span v-else>{{ item.sNuclideText }}</span>

                                            <span v-if="item.sTracerSupName"
                                                v-html="item.sTracerSupName ? (item.sTracerSupName && item.sTracerSupName.substring(0, 1) === '-' ? item.sTracerSupName : '-' + item.sTracerSupName) : item.sTracerSupName"></span>
                                            <span v-else>{{ item.sTracerText ? (item.sTracerText.substring(0, 1) === '-' ? item.sTracerText : '-' + item.sTracerText) : item.sTracerText }}</span>
                                        </label-info>
                                        <label-info label="给药方式">
                                            {{ item.sDrugDeliveryText }}
                                        </label-info>
                                        <label-info label="注射部位">
                                            {{ item.sInjectionPositionText }}
                                        </label-info>
                                        <label-info label="渗漏" v-if="cacheReportInfoShowSet.isShowsStainPosition">
                                            {{ item.sStainPosition }}
                                        </label-info>
                                        <label-info label="满针时间" v-if="cacheReportInfoShowSet.isShowdInjectionSta">
                                            {{ transformDate(item.dInjectionSta, true) }}
                                        </label-info>
                                        <label-info label="满针剂量" v-if="cacheReportInfoShowSet.isShowfFullNeedle">
                                            {{ item.fFullNeedle }}<span v-if="item.fFullNeedle > 0">{{
                                                item.sFullUnitText }}</span>
                                        </label-info>
                                        <label-info label="空针时间" v-if="cacheReportInfoShowSet.isShowdInjectionEnd">
                                            {{ transformDate(item.dInjectionEnd, true) }}
                                        </label-info>
                                        <label-info label="空针剂量" v-if="cacheReportInfoShowSet.isShowfEmptyNeedle">
                                            {{ item.fEmptyNeedle }}<span v-if="item.fEmptyNeedle > 0">{{
                                                item.sFullUnitText }}</span>
                                        </label-info>
                                        <label-info label="注射时间">
                                            {{ transformDate(item.dInjectionTime, true) }}
                                        </label-info>
                                        <label-info label="注射剂量">
                                            {{ item.fFactDose }}<span v-if="item.fFactDose > 0">{{
                                                item.sFullUnitText
                                            }}</span>
                                        </label-info>
                                        <label-info label="药物来源">
                                            {{ item.sMedicineSourceText }}
                                        </label-info>
                                        <label-info label="注射人">
                                            {{ item.sNurseName }}
                                        </label-info>
                                        <label-info label="注射日期">
                                            {{ trans2Day(item.dInjectDate) }}
                                        </label-info>
                                    </template>
                                </template>
                                <div v-else>
                                    <el-empty :image-size="80" description=" " />
                                </div>
                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'machineInfo'" id="machineInfo" class="c-item t-2" v-loading="loadObject.machine">
                                <div>
                                    <h4>上机记录</h4>
                                </div>
                                <template v-if="machineListData.length">
                                    <div v-for="(item, index) in machineListData" :key="index" class="i-item">
                                        <div v-if="machineListData.length > 1" style="padding: 5px 15px;">{{ '第' + (index + 1) +
                                            '条' }}</div>
                                        <label-info label="采集时间">
                                            {{ transformDate(item.dOperateEnd, true) }}
                                        </label-info>
                                        <label-info label="显像类型">
                                            {{ item.sImgTypeName }}
                                        </label-info>
                                        <label-info label="采集范围">
                                            {{ item.sTargetSiteName }}
                                        </label-info>
                                        <label-info label="污染情况">
                                            {{ item.sStain }}
                                        </label-info>
                                        <label-info label="机房">
                                            {{ item.sMachineryRoomText }}
                                        </label-info>
                                        <label-info label="操作人">
                                            {{ item.sOperator }}
                                        </label-info>
                                    </div>
                                </template>
                                <div v-else>
                                    <el-empty :image-size="80" description=" " />
                                </div>
                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'followInfo'" id="followInfo" class="c-item clearfix" v-loading="loadObject.followup">
                                <div>
                                    <h4>随访预约</h4>
                                </div>
                                <div class="item-form">
                                    <el-form :model="followRecordForm" :rules="sVisitRules" label-width="100px"
                                        ref="visitedForm" label-position="right" class="block">
                                        <el-col :span="24">
                                            <el-form-item label="预约时间：" prop="dPlanTime">
                                                <el-date-picker style="width: 100%;" v-model="followRecordForm.dPlanTime"
                                                    type="date" placeholder="选择日期">
                                                </el-date-picker>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="24">
                                            <el-form-item label="随访方式：" prop="sFollowMode">
                                                <el-select v-model="followRecordForm.sFollowMode" style="width: 100%"
                                                    placeholder="请选择">
                                                    <el-option v-for="item in optionsLoc.ApricotReportFollowMode"
                                                        :key="item.sValue" :label="item.sName" :value="item.sValue"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="24">
                                            <el-form-item label="随访状态：" prop="sVisitState">
                                                <el-select v-model="followRecordForm.sVisitState" style="width: 100%;"
                                                    placeholder="请选择">
                                                    <el-option v-for="item in optionsLoc.ApricotReportFollowUp"
                                                        :key="item.sValue" :label="item.sName" :value="item.sValue"></el-option>
                                                </el-select>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="24">
                                            <el-form-item label="随访原因：" prop="sReason">
                                                <el-input v-model="followRecordForm.sReason" type="textarea" :rows="2"
                                                    placeholder="随访原因"></el-input>
                                            </el-form-item>
                                        </el-col>
                                    </el-form>
                                    <div class="save-btn mb-2">
                                        <el-button type="primary" size="small" plain :loading="followUpLoading"
                                            @click="addFollowupRecord">保存</el-button>
                                    </div>
                                </div>
                                <div v-if="followList.length" style="margin: 10px 15px;">
                                    <el-table :data="followList" border stripe highlight-current-row size="small"
                                        @row-dblclick="onEditFollowUpClick">
                                        <el-table-column prop="dPlanTime" label="预约时间" show-overflow-tooltip align="center">
                                            <template v-slot="{ row }">
                                                {{ transformDate(row.dPlanTime) }}
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="sVisitStateText" label="随访状态" show-overflow-tooltip
                                            align="center">
                                        </el-table-column>
                                        <el-table-column prop="sIsAccordText" show-overflow-tooltip label="随访符合" align="center">
                                        </el-table-column>
                                        <el-table-column label="操作" width="60px" align="center">
                                            <template v-slot="scope">
                                                <el-button type="primary" link @click="onEditFollowUpClick(scope.row)" size="small">
                                                    查看
                                                </el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </div>
                                <div class="i-border"></div>
                            </section>
                            <section v-if="item.name=== 'criticalInfo'" id="criticalInfo" class="c-item clearfix" v-loading="loadObject.emergency">
                                <div>
                                    <h4>危急值</h4>
                                    <div class="i-btn cursor-pointer float-right" @click="onEditCriticalValueClick">
                                        <Icon type="el-icon" name="el-icon-more-outline" size="18"></Icon>
                                        <span>更多</span>
                                    </div>
                                </div>
                                <div class="item-form">
                                    <el-form :model="sCriticalValueForm" ref="CriticalValForm" label-width="110px"
                                        label-position="right" :rules="sCriticalValueRules">
                                        <el-form-item label="危急标志：" prop="iEmergencyFlag">
                                            <el-select v-model="sCriticalValueForm.iEmergencyFlag" placeholder="标志"
                                                style="width: 100%;">
                                                <el-option v-for="(item, index) in optionsLoc.EmergencyFlag" :key="index"
                                                    :label="item.sName" :value="Number(item.sValue)"></el-option>
                                            </el-select>
                                        </el-form-item>
                                        <el-form-item label="危急值内容：" prop="sEmergency">
                                            <el-input v-model="sCriticalValueForm.sEmergency" type="textarea" :rows="2"
                                                placeholder="危急值内容"></el-input>
                                        </el-form-item>
                                    </el-form>
                                    <div class="save-btn">
                                        <el-button type="primary" size="small" plain @click="onSubmitCriticalVal">发送</el-button>
                                    </div>
                                </div>
                                <div class="i-tags">
                                    <div v-for="(item, index) in criticalValueList" :key="index" class="i-tag-item"
                                        :class="{ 'c-active': criticalValueCurrentIdx === index }"
                                        @click="onChangeCriticalValue(item, index)">
                                        {{ transformDate(item.dUpTime) }}</div>
                                </div>
                                <div v-if="criticalValueList.length" style="margin-top: 10px;">
                                    <label-info label="危急标志">
                                        {{ criticalValueList[criticalValueCurrentIdx].sEmergencyFlag }}
                                    </label-info>
                                    <label-info label="状态" :title="criticalValueList[criticalValueCurrentIdx].sDealState">
                                        {{ criticalValueList[criticalValueCurrentIdx].sDealState }}
                                    </label-info>
                                    <label-info label="上报时间">
                                        {{ transformDate(criticalValueList[criticalValueCurrentIdx].dUpTime, true) }}
                                    </label-info>
                                    <label-info label="危急值内容" :title="criticalValueList[criticalValueCurrentIdx].sEmergency">
                                        {{ criticalValueList[criticalValueCurrentIdx].sEmergency }}
                                    </label-info>
                                </div>

                                <div class="i-border"></div>
                            </section>
                        </template>
                    </section>
                </el-scrollbar>
            </section>
        </div>
        <!-- 问诊信息修改 -->
        <EditConsultInfo v-model:dialogVisible="dialog.d_EditConsultInfo_v" :patientInfo="patientInfo"
            @closeDialog="closeEditConsultInfo"></EditConsultInfo>

        <!-- 注射信息修改 -->
        <EditInjectInfo v-model:dialogVisible="dialog.d_EditInjectInfo_v" :patientInfo="patientInfo"
            @updateData="getInjectInit" @closeDialog="closeEditInjectInfo"></EditInjectInfo>

        <!-- 随访标记 -->
        <FollowUpMark v-model:dialogVisible="dialog.d_FollowUpMark_v" :userInfo="userInfo" 
            :patientInfo="patientInfo" :selectedVisitRow="selectedVisitRow" :followList="followList" 
            @getFollowList="getFollowList"></FollowUpMark>

        <!-- 危急值 -->
        <CriticalValue v-model:dialogVisible="dialog.d_criticalValue_v" :patientInfo="patientInfo"
            @emergencyqueryList="getEmergencyqueryList"></CriticalValue>

        <!-- 扫描件 -->
        <LookImage v-model:dialogVisible="dialog.lookImage" :isOnlyShowUpload="true" :patientInfo="patientInfo"></LookImage>
    </div>
</template>
<script>
import 'tiff.js';
import ClipboardJs from 'clipboard';
import { getAccessToken } from '@/utils/accessToken';

// import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
import { getOptionName } from '$supersetResource/js/tools'
import Configs from './configs/info.js'
// 全局参数
import { getPatientBriefById } from '$supersetApi/projects/apricot/case/index.js'
import { getPatientQueryPastPatients } from '$supersetApi/projects/apricot/case/consult.js'
import { findInjectionListByPatientId } from '$supersetApi/projects/apricot/injectMng/inject.js'
import { getOperateComputerPage } from '$supersetApi/projects/apricot/case/machine.js'
import ApiAssist from '$supersetApi/projects/apricot/assistServe/index.js'

import { useUserConfigQueryByKey } from '$supersetResource/js/projects/apricot/useUserConfig.js';
import { compareAndModifyArrays } from '@/utils'

import {
    getPatientResultById, emergencyqueryList, queryClientConfig, readClientId,
    verificationCode, registerEmergency
} from '$supersetApi/projects/apricot/case/report.js'
import { getFollowList, addFollowupRecord } from '$supersetApi/projects/apricot/followupVisits/index.js'
import { getStoreNameByRoute } from '$supersetResource/js/projects/apricot'
import { getFiles } from '$supersetApi/projects/apricot/common/files.js'
import { openWebReadImgOrRebuild, openCaseImageWin } from '$supersetResource/js/projects/apricot/index.js'

export default {
    name: 'PatientInfo',
    components: {
        EditConsultInfo: defineAsyncComponent(() => import('$supersetViews/apricot/case/components/EditConsultInfo.vue')),
        EditInjectInfo: defineAsyncComponent(() => import('$supersetViews/apricot/case/components/EditInjectInfo.vue')),
        FollowUpMark: defineAsyncComponent(() => import('$supersetViews/apricot/case/report/FollowUpMark.vue')),
        PanelInfoMenuConfig: defineAsyncComponent(() => import('$supersetViews/apricot/case/report/PanelInfoMenuConfig.vue')),
        CriticalValue: defineAsyncComponent(() => import('$supersetViews/apricot/components/CriticalValue.vue')),
        LookImage: defineAsyncComponent(() => import('$supersetViews/apricot/components/LookImage.vue')),
        // ElImageViewer,
    },
    data () {
        let historyRelatedCheck = {
            hasRecordNo: 0,
            hasNuclearNum: 0,
            hasIdNum: 0,
        };
        return {
            activeTab: 'patientInfo',
            defaultList: [
                { name: 'patientInfo', label: '基本信息', icon: 'fa-distribution', isShow: true, },
                { name: 'checkInfo', label: '检查信息', icon: 'fa-case-line', isShow: true, },
                { name: 'clinicInfo', label: '问诊信息', icon: 'fa-stethoscope-1', isShow: true, },
                { name: 'historyInfo', label: '历史报告', icon: 'fa-report-1', isShow: true, },
                { name: 'scannerInfo', label: '病例资料', icon: 'fa-material-fill', isShow: true, right: 'ScanData' },
                { name: 'injectInfo', label: '注射记录', icon: 'fa-injection', isShow: true, right: 'Inject' },
                { name: 'machineInfo', label: '上机记录', icon: 'fa-machine', isShow: true, right: 'Machine' },
                { name: 'followInfo', label: '随访预约', icon: 'fa-phone-square', isShow: true, right: 'FollowUp' },
                { name: 'criticalInfo', label: '危急值', icon: 'fa-bell-o', isShow: true, right: 'CriticalValue' },
            ],
            tabList: [],
            info: {},
            iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
            textBaseConfig: [...Configs.textBaseConfig],
            textCheckConfig: [...Configs.textCheckConfig],
            loading: false,
            urlModule: '',
            currentModule: 'no-module-set',
            currentId: null,
            dialog: {
                d_EditConsultInfo_v: false,
                d_FollowUpMark_v: false,
                d_criticalValue_v: false,
                lookImage: false,
                d_EditInjectInfo_v: false
            },
            injectInfoData: [],
            machineListData: [],
            checked: false,
            historyRelatedCheck: JSON.parse(localStorage.getItem('historyRelatedCondition')) || historyRelatedCheck,
            hisReportList: [],
            hisReportCurrentIdx: 0,
            hisReportCurrentItem: {},
            hisReportTableRowLoading: false,
            expandsHistoryReport: [],
            followList: [],
            followCurrentIx: 0,
            criticalValueList: [],
            criticalValueCurrentIdx: 0,
            imgServerURL: `${window.configs.urls.apricot}/attachments/preview?sFilePath=`,
            ImageUrls: [],
            pastCount: undefined,
            scanningDataCount: undefined,
            isFirstScroll: true,
            loadObject: {
                scanner: false,
                inject: false,
                machine: false,
                followup: false,
                history: false,
                emergency: false
            },
            clientParams: {
                needVerifyLicence: null, //阅图是否需要授权
                verificationStatus: null, // 授权状态
                clientId: null,
            },
            imgModal: false,  // 大图模态框控制
            imgSrcIndex: 0, // 当前点击图片下标
            imageViewerSetting: {  // 图片显示缓存配置
                rotate: null
            },
            scrollBarTop: 0,
            rights: {},
            followRecordForm: {
                sFollowMode: "1",
                sVisitState: '1'
            },
            followUpLoading: false,// 保存随访按钮loading
            sVisitRules: {
                //dPlanTime:[{type: 'date', required: true,message: '预约时间'}],
                // sFollowMode: [{type: 'date', required: true,message: '随访方式'}]
                // sVisitState:[{required: true, message: '随访状态',trigger: 'change'}],
                // iIsAccord:[{required: true,message: '随访符合',trigger: 'change'}]
            },
            optionsLoc: {
                ApricotReportFollowMode: [],
                ApricotReportFollowUp: [],
                EmergencyFlag: [],
                iIsAccordOptions: [{
                    sName: '符合',
                    sValue: 1
                },
                {
                    sName: '不符合',
                    sValue: 2
                }
                ]
            },
            sCriticalValueForm: {},
            sCriticalValueRules: {
                iEmergencyFlag: [{ required: true, message: '危急标志', trigger: 'change' }],
                sEmergency: [{ required: true, message: '危急值内容', trigger: 'blur' }]
            },
            historyFollowList: [], // 历史报告随访记录
            clipboardInstance: null,
            cacheReportInfoShowSet: {
                isShowdInjectionSta: 1,
                isShowfFullNeedle: 1,
                isShowdInjectionEnd: 1,
                isShowfEmptyNeedle: 1,
                isShowsStainPosition: 1
            },
            isShowSetPop: false,
            isShowHistoryMatchPop: false,
            selectedVisitRow: {},
            MngIndexPenalConfig: []
        }
    },
    computed: {
        patientInfo () {
            let iModule = this.$store.state.apricot[this.urlModule];
            if (iModule && iModule.patientInfo) {
                return iModule.patientInfo;
            }
            return {};
        },
        IsUpdatePatientInfo () {
            let iModule = this.$store.state.apricot[this.urlModule];
            if (iModule && iModule.IsUpdatePatientInfo) {
                return iModule.IsUpdatePatientInfo
            }
            return false;
        },
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
        isCollapse () {
            return this.$store.getters['apricot/report_module/infoLayerMenuCollapse'];
        },
        criticalInfoCount () {
            return this.criticalValueList.length
        },
        isOpenInNewPage () {
            let obj = this.$store.getters['user/personalOnlineStorage']
            let settings = obj['6'] && obj['6']['ReportCaseIndexReportSetting']
            if (settings) {
                return settings.openScannerImageInNewPage || 1
            } else {
                const local = window.localStorage.getItem('oAutoSaveTime') || '{}'
                try {
                    const jsonObj = JSON.parsed(local)
                    return jsonObj.openScannerImageInNewPage || 1
                } catch (error) {
                }
            }
            return 1
        }
    },
    watch: {
        patientInfo: {
            async handler (val) {
                if (val.sId && this.currentId !== val.sId) {
                    this.currentId = val.sId;
                    this.getInfo(val.sId);
                    this.getInjectInit();  // 报告提交的时候可能会校验注射信息，因此提前请求；
                    this.initPageData(this.tabList);
                }
            },
            immediate: true
        },
        IsUpdatePatientInfo (val) {
            if (val) {
                this.getInfo();
                this.$store.commit({
                    type: `apricot/${this.urlModule}/setIsUpdatePatientInfo`,
                    IsUpdatePatientInfo: false
                });
            }
        },
        'dialog.lookImage': {
            handler (val) {
                if (!val) {
                    this.initImageViewer();
                    this.getServerFiles();
                }
            }
        },
    },
    activated () {
        if (this.$refs.scrollbar.wrapRef) (this.$refs.scrollbar.wrapRef.scrollTop = this.scrollBarTop)
    },
    beforeUnmount () {
        // 必须移除监听器，不然当该vue组件被销毁了，监听器还在就会出错
        this.$refs.scrollbar.wrapRef.removeEventListener('scroll', this.onScroll);
    },
    created () {
        let cacheReportInfoShowSet = localStorage.getItem('cacheReportInfoShowSet');
        let oDefaultSet = {...this.cacheReportInfoShowSet}
        if (cacheReportInfoShowSet) {
            this.cacheReportInfoShowSet = JSON.parse(cacheReportInfoShowSet);
            const cacheSetKeys =  Object.keys(cacheReportInfoShowSet);
            Object.keys(oDefaultSet).map(item => {
                if(!cacheSetKeys.includes(item)){
                    this.cacheReportInfoShowSet[item] = oDefaultSet[item]
                }
            })
        }
    },
    async mounted () {
        let useUserConfigQuery = useUserConfigQueryByKey()
        await useUserConfigQuery('ReportMngIndexPatientMenuConfig', this, this.defaultList);
        this.tabList = compareAndModifyArrays(this.defaultList, this.MngIndexPenalConfig, 'name');
        this.activeTab = this.tabList.filter(item=>item.isShow)[0]?.name;

        this.urlModule = getStoreNameByRoute(this.$route.name);
        this.currentModule = this.$store.state.apricot[this.urlModule].currentModule;
        
        this.optionsLoc.ApricotReportFollowMode = this.$store.getters['dict/map'].ApricotReportFollowMode || [];
        this.optionsLoc.ApricotReportFollowUp = this.$store.getters['dict/map'].ApricotReportFollowUp || [];
        this.optionsLoc.EmergencyFlag = this.$store.getters['dict/map'].EmergencyFlag || [];
        this.optionsLoc.Emergency = this.$store.getters['dict/map'].Emergency || [];
        // this.mxGetCodeTable('Emergency');
        // this.getInfo(); 
        // this.onJumpClick(2)
        this.$nextTick(() => {
            this.$refs.scrollbar.wrapRef.addEventListener('scroll', this.onScroll, true);
            this.addScrollHeight();
        })
    },
    methods: {
        transformDate(time){
            if(!time) {
                return null
            }
            return moment(time).format('YYYY-MM-DD HH:mm');
        },
        trans2Day(time){
            if(!time) {
                return null
            }
            return moment(time).format('YYYY-MM-DD');
        },
        updateDataConfig(data) {
            let $lastItem = document.querySelector('.c-scrollbar .c-item:last-child');
            $lastItem && ($lastItem.style.marginBottom = 'unset');
            const oldTabsNameList = this.tabList.filter(o => o.isShow).map(item => item.name);
            this.tabList = data;
            this.addScrollHeight();
            const filterList = data.filter(item => item.isShow && !oldTabsNameList.includes(item.name));
            this.initPageData(filterList);
        },
        initPageData(list) {
            list.map(item => {
                if(item.isShow) {
                    if(item.name === 'historyInfo') {
                        this.getPatientQueryPastPatients();
                    }
                    if(item.name === 'scannerInfo') {
                        this.getServerFiles(); // 病例资料
                        this.initImageViewer();
                    }
                    if(item.name === 'machineInfo') {
                        this.getOperateComputerPage();
                    }
                    if(item.name === 'followInfo') {
                        this.getFollowList(this.patientInfo.sId, false);
                    }
                    if(item.name === 'criticalInfo') {
                        this.getEmergencyqueryList(true);
                    }
                }
            })
        },
        addScrollHeight() {
            this.$nextTick(() =>{
                let $scrollbar = document.querySelector('.c-scrollbar');
                let $lastItem = document.querySelector('.c-scrollbar .c-item:last-child');
                if(!$lastItem) return;
                // console.log($scrollbar.offsetHeight, $lastItem.offsetHeight);
                if ($lastItem.offsetHeight < $scrollbar.offsetHeight) {
                    // console.log($lastItem.style.marginBottom)
                    $lastItem.style.marginBottom = ($scrollbar.offsetHeight - $lastItem.offsetHeight + 10) + 'px';
                }
            })
        },
        handleCacheReportInfoShowSet () {
            localStorage.setItem('cacheReportInfoShowSet', JSON.stringify(this.cacheReportInfoShowSet))
        },
        //
        querySearch (queryString, cb) {
            var emergency = this.optionsLoc.Emergency || [];
            var results = queryString ? emergency.filter(this.createFilter(queryString)) : emergency;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        createFilter (queryString) {
            return (emergency) => {
                return (emergency.sName.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
            };
        },
        handleSelect (item) {
            this.sCriticalValueForm.sEmergency = item.sName;
        },
        // 初始化，获取图片显示配置缓存信息
        initImageViewer () {
            let imageViewerSetting = localStorage.getItem('imageViewerSetting')
            this.imageViewerSetting = imageViewerSetting ? JSON.parse(imageViewerSetting) : this.$options.data().imageViewerSetting;
        },
        // 点击显示大图浏览
        onClickImage (index) {
            if (this.isOpenInNewPage < 1) {
                //本页
                this.imgModal = true;
                this.onSwitch(index, true);
            } else {
                // 新开页
                let params = {
                    curIdx: index,
                    sPatientId: this.patientInfo.sId,
                    sInnerIndex: this.patientInfo.sInnerIndex,
                    sNuclearNum: this.patientInfo.sNuclearNum,
                    sName: this.patientInfo.sName
                }
                openCaseImageWin.call(this, params);
            }
        },
        // 切页大图
        onSwitch (index, isClickImage) {
            this.imgSrcIndex = index;
        },
        // 关闭浏览
        onClose () {
            this.imgModal = false;
        },
        onJumpClick (index) {
            this.$nextTick(() => {
                const dom = document.getElementById(this.tabList.filter(item => item.isShow)[index].name);
                dom && dom.scrollIntoView({ behavior: 'smooth' }) // 你也可以设置丝滑滚动
            })
        },
        // 滚动监听器
        async onScroll () {
            // 获取所有锚点元素
            const navContents = document.querySelectorAll('.c-plan .c-item');
            // 所有锚点元素的 offsetTop
            const offsetTopArr = [];
            for (let i = 0; i < navContents.length; i++) {
                offsetTopArr.push(navContents[i].offsetTop)
            }
            // 获取当前文档流的 scrollTop
            const scrollTop = Math.ceil(this.$refs.scrollbar.wrapRef.scrollTop);
            this.scrollBarTop = scrollTop;
            // console.log(scrollTop)
            // 定义当前点亮的导航下标
            let navIndex = 0;
            for (let n = 0; n < offsetTopArr.length; n++) {
                // 如果 scrollTop 大于等于第n个元素的 offsetTop 则说明 n-1 的内容scrollBarTop已经完全不可见
                // 那么此时导航索引就应该是n了
                if (scrollTop >= offsetTopArr[n]) {
                    navIndex = n;
                }
            }
            // navIndex = navIndex >= navContents.length? navIndex - 1:navIndex;
            this.activeTab = this.tabList.filter(item => item.isShow)[navIndex].name;
            // if (navIndex > 2 && this.isFirstScroll) {
            //     // 滚动到第三个tab触发
            //     this.isFirstScroll = false;
            //     await this.getOperateComputerPage();
            //     await this.getFollowList(this.patientInfo.sId, false);
            //     await this.getEmergencyqueryList(true);
            // }
        },
        // 更新容器宽度
        // onChangeWidth(isExpand) {
        //   this.$emit('onChangeCache', isExpand)
        // },
        // 打开问诊编辑弹窗
        onEditConsultInfoClick () {
            this.dialog.d_EditConsultInfo_v = true;
        },
        // 关闭问诊编辑弹窗
        closeEditConsultInfo () {
            this.dialog.d_EditConsultInfo_v = false;
            this.getInfo();
        },
        // 打开注射编辑弹窗
        onEditInjectInfoClick () {
            this.dialog.d_EditInjectInfo_v = true;
        },
        // 关闭注射编辑弹窗
        closeEditInjectInfo () {
            this.dialog.d_EditInjectInfo_v = false;
        },
        // 打开扫描资料
        onEditLookImage () {
            this.dialog.lookImage = true;
        },
        // 打开随访弹窗
        onEditFollowUpClick (row) {
            this.selectedVisitRow = row
            this.dialog.d_FollowUpMark_v = true;
        },

        // 获取扫描资料列表
        getServerFiles () {
            this.ImageUrls = [];
            let jsonData = {
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex,
                sNuclearNum: this.patientInfo.sNuclearNum
            }
            this.loadObject.scanner = true;
            getFiles(jsonData).then(res => {
                this.loadObject.scanner = false;
                if (res.success) {
                    let serverImages = res.data || [];
                    serverImages.map((item, index) => {
                        let suffix = item.sFileType;
                        let url = `${this.imgServerURL}${item.sDiskSymbol}${item.sFilePath}&aptSessionId=${getAccessToken()}`
                        item['url'] = url;
                        this.ImageUrls.push(url);
                        if (suffix === 'tif' || suffix === 'tiff') {
                            this.setTiffFile2DataURL(item, index);
                        }
                    })
                    this.scanningDataCount = serverImages.length;
                    return
                }
                this.scanningDataCount = 0;
                this.$message.error(res.msg);
                return
            }).catch(err => {
                this.loadObject.scanner = false;
                this.scanningDataCount = 0
                console.log(err)
            })
        },

        // tif文件 转 basedata
        setTiffFile2DataURL (item, index) {
            let filename = `${this.imgServerURL}${item.sDiskSymbol}${item.sFilePath}&aptSessionId=${getAccessToken()}`
            let xhr = new XMLHttpRequest();
            xhr.open('GET', filename, true);
            xhr.responseType = 'arraybuffer';
            let that = this;
            xhr.onload = function (e) {
                var buffer = xhr.response;
                Tiff.initialize({ TOTAL_MEMORY: 50 * 1024 * 1024 }); //支持最大50M
                var tiff = new Tiff({
                    buffer: buffer
                });
                var imgData = tiff.toDataURL();
                if (imgData) {
                    item['url'] = imgData;
                    that.ImageUrls[index] = imgData;
                }
            }
            xhr.send();
            this.xhrList.push(xhr);
        },

        // 注射信息
        async getInjectInit (id) {
            let sId = id || this.patientInfo.sId;
            this.loadObject.inject = true;
            await findInjectionListByPatientId({
                sPatientId: sId
            }).then(res => {
                this.loadObject.inject = false;
                if (res.success) {
                    this.injectInfoData = res.data || [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                this.loadObject.inject = false;
                console.log(err);
            })
        },
        // 上机信息
        async getOperateComputerPage (id) {
            let params = {
                condition: {
                    sPatientId: id || this.patientInfo.sId
                },
                orders: {
                    orderInfoList: [
                        {
                            iIndex: 0,
                            sDirection: 'asc',
                            sOrderDbName: '',
                            sOrderField: 'dOperateEnd',
                            sOrderTable: '',
                        }
                    ]
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 30
                }
            }
            this.loadObject.machine = true;
            await getOperateComputerPage(params).then(res => {
                this.loadObject.machine = false;
                if (res.success) {
                    this.machineListData = res.data.recordList || [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                this.loadObject.machine = false;
                console.log(err);
            })
        },
        // 加载提示
        loadFindTip (text) {
            return this.$loading({
                lock: true,
                text: text || '加载中...',
                // 
                background: 'rgba(0, 0, 0, 0.1)',
                customClass: 'my-loading'
            });
        },
        // 执行客户端阅图重建
        async rebuildOpen (row, iIsRebuid = 0) {
            let loading = this.loadFindTip();
            await this.getClientConfig()
            const needVerifyLicence = this.clientParams.needVerifyLicence

            // 若接口链接超时，默认值为null,
            if (needVerifyLicence == null) {
                loading.close()
                return
            }
            if (needVerifyLicence) {
                await this.getClientId()
                const clientId = this.clientParams.clientId
                // 没有获取到id，默认值为null,
                if (!clientId) {
                    loading.close()
                    return
                }
                await this.getVerificationCode()
                if (!this.clientParams.verificationStatus) {
                    loading.close()
                    return
                }
            }
            const params = {
                sImgPatientId: row.sImgPatientId,
                sImgStudyDate: row.sImgStudyDate,
                sImgAccessionNumber: row.sImgAccessionNumber,
                iIsRebuid: iIsRebuid
            }
            ApiAssist.rebuildOpenView(params).then(res => {
                loading.close();
                if (res && !res.success) {
                    this.$message.error(res.msg)
                    return
                }
            }).catch(() => {
                loading.close();
            })
        },
        // 打开web重建 
        async openWebReBuildViewer (row, iIsRebuild = 1) {
            let loading = this.loadFindTip();
            await this.getClientConfig()
            const needVerifyLicence = this.clientParams.needVerifyLicence
            // 若接口链接超时  默认值为null,
            if (needVerifyLicence == null) {
                loading.close()
                return
            }
            if (needVerifyLicence) {
                await this.getClientId()
                const clientId = this.clientParams.clientId
                if (!clientId) {
                    loading.close()
                    return
                }
                await this.getVerificationCode()
                if (!this.clientParams.verificationStatus) {
                    loading.close()
                    return
                }
            }
            loading.close();
            let info = {
                sPatientId: row.sId,      // 对比患者
                deviceTypeId: row.sRoomId,
                past: 1,
                sId: this.patientInfo.sId  // 当前患者
            }
            openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info);
        },
        // 打开web阅图
        openWebReadViewer (row) {
            this.openWebReBuildViewer(row, 0)
        },
        // 查询客户端配置
        async getClientConfig () {
            await queryClientConfig().then(res => {
                if (res.success) {
                    this.clientParams.needVerifyLicence = res.data.needVerifyLicence;
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 查询本机主板序列号
        async getClientId () {
            await readClientId().then(res => {
                if (res.success) {
                    this.clientParams.clientId = res.data;
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 获取授权状态
        async getVerificationCode () {
            let params = {
                clientId: this.clientParams.clientId
            }
            await verificationCode(params).then(res => {
                if (res.success) {
                    this.clientParams.verificationStatus = res.success;
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        //实现只有一个展开折叠
        expandHistoryReportChange (row, expandedRows) {
            this.expandsHistoryReport = [];
            if (expandedRows.length) {
                // 展开行
                if (row) {
                    this.onChangeHisReportClick(row);
                    this.$nextTick(() => {
                        this.$refs.hisDom[0].setCurrentRow(row);
                    })
                    
                }
            } else {
                this.$refs.hisDom[0].setCurrentRow();
            }
        },
        onChangeHisReportClick (row) {
            // 处理表格展开或折叠行
            if(this.expandsHistoryReport.includes(row.sId)) {
                // 收起
                this.expandsHistoryReport = [];
                this.$refs.hisDom[0].setCurrentRow();
            } else {
                // 展开
                this.expandsHistoryReport = [row.sId];
            }
            // 点击相同行，不处理；
            if(this.hisReportCurrentIdx === row.index) return;
            this.hisReportCurrentIdx = row.index;
            this.getPatientResultById(row.sId);
            this.getFollowList(row.sId, true);
            this.scrollToHisReportDom(row);
        },
        // 滚动条滚动到历史相关定位；
        scrollToHisReportDom(row) {
            const scrollTop = Math.ceil(this.$refs.scrollbar.wrapRef.scrollTop);
            const offsetTop = document.querySelectorAll('.c-plan .c-item')[3].offsetTop;
            if(scrollTop - (offsetTop + (row.index + 1) * 40) >  200) {
                this.$refs.scrollbar.wrapRef.scrollTop = offsetTop + (row.index + 1) * 40;
            }
        },
        onChangeHisReportConditions () {
            localStorage.setItem('historyRelatedCondition', JSON.stringify(this.historyRelatedCheck));
            this.getPatientQueryPastPatients()
        },
        rowClassName({ row, rowIndex }) {
            if (row.index === undefined) row.index = rowIndex;
        },
        // 获取历史患者数据
        async getPatientQueryPastPatients () {
            let patientInfo = this.patientInfo
            let jsonData = {
                sName: patientInfo.sName,
                sSex: patientInfo.sSex,
                sPatientInfoId: patientInfo.sId
            }
            if (this.historyRelatedCheck.hasRecordNo) {
                jsonData.sMedicalRecordNO = patientInfo.sMedicalRecordNO
            }
            if (this.historyRelatedCheck.hasNuclearNum) {
                jsonData.sNuclearNum = patientInfo.sNuclearNum
            }
            if (this.historyRelatedCheck.hasIdNum) {
                jsonData.sIdNum = patientInfo.sIdNum
            }
            if (this.historyRelatedCheck.hasVisitCard) {
                jsonData.sVisitCard = patientInfo.sVisitCard
            }
            if (this.historyRelatedCheck.hasHealthCardNO) {
                jsonData.sHealthCardNO = patientInfo.sHealthCardNO
            }
            if (this.historyRelatedCheck.orderByAppointmentTime == 1) {
                jsonData.ascOrDesc = 'asc'
                jsonData.orderField = 'dAppointmentTime'

            }
            this.loadObject.history = true;
            await getPatientQueryPastPatients(jsonData).then(res => {
                this.loadObject.history = false;
                if (res.success) {
                    const data = res.data.pastPatientInfos || [];
                    this.hisReportList = data.filter(item => item.sId !== this.patientInfo.sId);
                    if (this.hisReportList.length) {
                        // this.getPatientResultById(this.hisReportList[0].sId)
                        const row = this.hisReportList[0];
                        this.onChangeHisReportClick(row);
                        this.$nextTick(() => {
                            this.$refs.hisDom[0].setCurrentRow(row);
                        })
                    }
                    this.pastCount = this.hisReportList.length || 0
                    return
                }
                this.$message.error(res.msg);
                this.pastCount = 0;
            }).catch(err => {
                this.loadObject.history = false;
                this.pastCount = 0
                console.log(err)
            })
        },
        // 获取患者结果通过id
        getPatientResultById (id) {
            this.hisReportCurrentItem = {};
            this.hisReportTableRowLoading = true;
            getPatientResultById({ sPatientId: id }).then(res => {
                this.hisReportTableRowLoading = false;
                if (res.success) {
                    this.hisReportCurrentItem = res.data || {}
                    return;
                }
                this.$message.error(res.msg)
            }).catch(()=> {
                this.hisReportTableRowLoading = false;
            })
        },
        onChangeFollow (item, index) {
            this.followCurrentIx = index;
        },
        // 随访记录
        async getFollowList (sPatientId, history = false) {
            let jsonData = {
                condition: {
                    sPatientId: sPatientId
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 30
                }
            }
            if (!history) {
                this.loadObject.followup = true;
            }
            await getFollowList(jsonData).then(res => {
                if (!history) {
                    this.loadObject.followup = false;
                    if (res.success) {
                        this.followList = res.data.recordList || [];
                        this.followCurrentIx = 0;
                        return
                    }
                    this.$message.error(res.msg)
                    return
                }
                if (res.success) {
                    this.historyFollowList = res.data.recordList || [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                this.loadObject.followup = false;
            });
        },
        onEditCriticalValueClick () {
            this.dialog.d_criticalValue_v = true;
        },
        onChangeCriticalValue (item, index) {
            this.criticalValueCurrentIdx = index;
        },
        // 获取危急值列表
        async getEmergencyqueryList (isfirstRequest) {
            let jsonData = {
                pageCurrent: 1,
                pageSize: 30,
                iTimeout: 5,
                sImgPatientId: this.patientInfo.sImgPatientId,
                sInHospitalNO: this.patientInfo.sInHospitalNO,
                sMedicalRecordNO: this.patientInfo.sMedicalRecordNO,
                sName: this.patientInfo.sName,
                sNuclearNum: this.patientInfo.sNuclearNum,
                sOutpatientNO: this.patientInfo.sOutpatientNO,
            };
            this.loadObject.emergency = true;
            await emergencyqueryList(jsonData).then(res => {
                this.loadObject.emergency = false;
                if (res.success) {
                    this.criticalValueList = res.data.records || [];
                    this.criticalValueCurrentIdx = 0;
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loadObject.emergency = false;
                console.log(err);
            })
        },
        // 患者基本信息
        getInfo (id) {
            // this.loading = true
            let sId = id ? id : this.patientInfo.sId
            getPatientBriefById({ sId: sId }).then(res => {
                this.loading = false
                if (res.success) {
                    console.log(this.patientInfo)
                    this.info = res.data;
                    this.info.sVisitCard = this.patientInfo.sVisitCard;
                    this.info.sHealthCardNO = this.patientInfo.sHealthCardNO;
                    return;
                }
                this.info = {}
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false
            })
        },

        // 发送危急值 
        onSubmitCriticalVal () {
            // let isFalse = ['', undefined, null]
            // if(isFalse.includes(this.sCriticalValueForm.iEmergencyFlag) || isFalse.includes(this.sCriticalValueForm.sEmergency)) {
            //     this.$message.warning('请录入危急值标志或危急值内容！')
            //     return
            // }
            const CriticalValFormRef =  this.$refs.CriticalValForm[0]
            CriticalValFormRef.validate(valid => {
                if (!valid) {
                    this.$message({
                        message: '请填写正确信息',
                        type: 'warning'
                    });
                    return false
                }
                let jsonData = {
                    sPatientInfoId: this.patientInfo.sId,
                    sNuclearNum: this.patientInfo.sNuclearNum,
                    sName: this.patientInfo.sName,
                    sItemName: this.patientInfo.sProjectName,
                    sUpUserId: this.userInfo.sId,
                    sUpUserNo: this.userInfo.sNo,
                    sUpUserName: this.userInfo.sName,
                }
                Object.assign(jsonData, this.sCriticalValueForm);
                jsonData.sEmergencyFlag = getOptionName(jsonData.iEmergencyFlag + '', this.optionsLoc.EmergencyFlag);
                let loading = this.$loading({
                    lock: true,
                    text: '发送中，请稍后...',
                    background: 'rgba(0, 0, 0, 0.1)',
                })
                registerEmergency(jsonData).then(res => {
                    loading.close();
                    this.getEmergencyqueryList()
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.sCriticalValueForm = {}
                        return
                    }

                    this.$message.error(res.msg)
                }).catch(err => {
                    if (err?.success === false) {
                        this.getEmergencyqueryList()
                    }
                    loading.close()
                })
            })

        },
        // 保存随访预约
        addFollowupRecord () {
            const visitedFormRef = this.$refs.visitedForm[0]
            visitedFormRef.validate(valid => {
                if (!valid) {
                    this.$message({
                        message: '请填写正确信息',
                        type: 'warning'
                    });
                    return false
                }

                // 验证成功
                let data = this.followRecordForm;
                let patientInfo = this.patientInfo;
                data.sPatientId = patientInfo.sId;
                //id赋值
                //data.sIsAccordText = getOptionName(this.followRecordForm['iIsAccord'], this.optionsLoc.iIsAccordOptions);
                data.sFollowModeText = getOptionName(this.followRecordForm['sFollowMode'], this.optionsLoc.ApricotReportFollowMode);
                data.sVisitStateText = getOptionName(this.followRecordForm['sVisitState'], this.optionsLoc.ApricotReportFollowUp);
                data.sDoctorId = this.userInfo.sId;
                data.sDoctorNo = this.userInfo.sNo;
                let loading = this.$loading({
                    lock: true,
                    text: '保存中，请稍后...',
                    background: 'rgba(0, 0, 0, 0.1)',
                })
                addFollowupRecord(data).then(res => {
                    loading.close();
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success'
                        });

                        visitedFormRef.resetFields()
                        //新增成功后刷新表格
                        this.getFollowList(patientInfo.sId, false);
                        return
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error'
                    });
                }).catch(err => {
                    loading.close();
                    console.log(err)
                })

            })
        },
        // copy
        onClickCopyHistoryReport () {
            if (this.clipboardInstance) {
                this.clipboardInstance.destroy();
            }

            this.clipboardInstance = new ClipboardJs(".copy-btn", {
                // text: (trigger) => {
                //     return html;
                // }
            });
            this.clipboardInstance.on('success', () => {
                this.$message({
                    showClose: true,
                    message: '复制成功',
                    type: 'success',
                });
            });
            this.clipboardInstance.on('error', () => {
                this.$message({
                    showClose: true,
                    message: '复制失败',
                    type: 'danger',
                });
            });
        }
    },

}
</script>
<style lang="scss" scoped>
.c-remade {
    background: #f9fafc;
}
.c-tabs {
    width: 43px;
    border-right: 1px solid #e3e5e8;

    .c-tab-item {
        @apply hover: scale-110 transform display: block;
        padding: 10px;
        cursor: pointer;
        position: relative;
        color: rgb(125, 125, 125);
        margin: 5px 0;

        &.c-active {
            color: var(--theme-header-bg);
            transform: scale(1.2);
        }

        &.i-center {
            text-align: center;
        }

        .el-badge {
            position: absolute;
            right: 5px;
            top: 6px;
        }
    }
    .c-setting {
        position: absolute;
        bottom: 0;width: 100%; 
        padding: 8px 0; 
        border-top: 1px solid #ddd; 
        text-align: center; 
        cursor: pointer;
        display: none;
    }
    &:hover {
        .c-setting {
            display: block;
        }
    }
}

.c-flag {
    position: absolute;
    right: 0;
    top: 0;
    width: 30px;
    height: 100%;
    background-color: #eee;
    text-align: center;
    overflow: hidden;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 1px 2px;
}


.c-patientInfo {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    // border-right: 1px solid #dcdee2;

    header {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 8px 10px 8px 25px;
        box-sizing: border-box;
        
        // border-bottom: 1px solid #eee;
        &.c-head-item {
            position: relative;
            margin-bottom: 8px;
        }

        &.c-shadow {
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
        }
        .i-icon-user {
            position: absolute;
            top: 12px;
            left: 4px; 
            font-size:16px;
            color: var(--el-color-primary);
        }

        .i-item-01 {
            width: 100%;
            // width: calc(100% - 80px);
            height: 100%;

            // padding-left: 8px;
            .el-row,
            p {
                overflow: hidden;
                margin: 0;
                min-height: 20px;
                text-overflow: ellipsis;
                white-space: nowrap;

                &:first-child {
                    // font-weight: bold;
                }

                >.el-col {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                label {
                    &:first-child {
                        display: inline-block;
                        width: 70px;
                        color: #777;
                        text-align-last: justify;
                        font-weight: normal;
                    }
                }
            }
        }

        .i-name {
            margin: 4px 0;
            font-size: 14px;
            font-weight: bold;
            &.i-weight {
                font-size: 16px;
                color: var(--el-color-primary);
            }
        }
    }

    .c-content {
        flex: 1;
        height: 0px;

        .c-scrollbar {
            height: 100%;
        }

        h4 {
            font-size: 14px;
            // font-weight: 500;
            padding-left: 5px;
            margin: 20px 10px 8px 6px;
            color: var(--theme-color);
            color: #656b76;
            border-left: 3px solid var(--theme-header-bg);

            display: inline-block;

        }

        .c-plan {
            padding: 0px 0px 6px 0px;
        }

        :deep(.m-labelInfo) {
            float: none;
            padding-left: 15px;
            padding-bottom: 5px;

            label {
                display: inline-block;
                min-width: 70px;
                text-align-last: justify;
                color: #838a9d;
            }

            span {
                border-bottom: none;
                color: #3c4353;
            }
        }

        .c-item {
            padding: 0;

            .text-result {
                padding: 0px 15px;
                line-height: 1.5;
                // background-color: #efefef;
            }

            .no-number {
                background-color: rgb(220, 230, 238);
                padding: 3px 10px;
            }

            &.t-1 {
                .c-textarea {
                    min-height: 60px;
                    margin: 0px 20px 10px;
                    color: #4e5055; //#3c4353 
                    line-height: 1.5;

                    &.i-lczd {
                        min-height: 40px;
                    }
                }
            }

            &.t-2 {
                .i-item {
                    margin-top: 10px;

                    &:first-child {
                        margin-top: 0;
                    }

                    &:nth-child(odd) {
                        // background-color: #258452;
                    }
                }
            }

            .i-btn {
                display: flex;
                align-items: center;
                margin-right: 20px;
                margin-top: 17px;
                font-size: 13px;
                border-radius: 2px;
                color: rgb(135, 142, 146);
                @apply hover: bg-gray-200;
                > span {
                    padding-left: 3px;
                }
            }
            .i-hover {
                display: none;
            }
            &:hover {
                .i-hover{
                    display: inline-block;
                }
            }

            .i-tags {
                display: flex;
                flex-wrap: wrap;
                padding: 0 10px 0px 14px;
                margin-top: 10px;

                .i-tag-item {
                    width: 100px;
                    padding: 8px 8px 6px;
                    margin-bottom: 4px;
                    margin-right: 4px;
                    background-color: #fff;
                    border: 1px solid #e5e5e5;
                    background-color: #f2f2f2;
                    cursor: pointer;
                    overflow: hidden;
                    box-sizing: border-box;

                    &.t-2 {
                        width: 140px;
                    }

                    &.c-active {
                        // color: #2384d3;
                        color: var(--el-color-primary-dark-2);
                        font-weight: bold;
                        background-color: #eaeaea;
                        background-color: var(--el-color-primary-light-9);
                        position: relative;

                        .date {
                            font-weight: normal;
                            color: #93979f;
                            // color: var(--el-color-primary-dark-1);
                        }

                        &::after {
                            content: '';
                            width: 100%;
                            height: 1px;
                            position: absolute;
                            left: 1px;
                            top: 0;
                            background-color: var(--el-color-primary);
                        }

                    }

                    >div {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        line-height: 1.5;
                    }

                    >div.date {
                        color: #93979f;
                    }
                }
            }

            .c-img {
                display: flex;
                flex-wrap: wrap;
                width: 100%;
                padding: 0px 10px 10px;
                box-sizing: border-box;

                .c-img-item {
                    flex: 1;
                    min-width: 40%;
                    // height: 250px;
                    margin: 5px;
                    padding: 5px;
                    border: 1px solid rgb(238, 238, 238);
                    overflow: hidden;
                    cursor: pointer;

                    .el-image {
                        width: 100%;
                        height: 100%;
                    }

                    &.t-1 {
                        height: 1px;
                        border: none;
                    }
                }
            }

            .secondTitle {
                padding-left: 10px;
            }

            .i-point {
                position: relative;
                top: -2px;
                display: inline-block;
                width: 6px;
                height: 6px;
                margin-right: 5px;
                border-radius: 50%;
                background: #42c5c2;
            }
        }
    }

    .i-border {
        margin: 0;
        border-bottom: 1px solid #eaeaea;
    }

    .my-divider {
        background-color: #e6e6e6;
    }
}

:deep(.el-image-viewer__mask) {
    opacity: .95;
}

:deep(.el-image-viewer__close .el-icon-circle-close) {
    color: #cdcdcd
}

.img-clocelise {

    :deep(.el-image img) {
        transform: scale(0.8) rotate(90deg);
        cursor: pointer;
    }

    :deep(.el-image-viewer__canvas) {
        transform: scale(0.8) rotate(90deg);
    }
}

.img-anticlocelise {

    :deep(.el-image img) {
        transform: scale(0.8) rotate(-90deg);
        cursor: pointer;
    }

    :deep(.el-image-viewer__canvas) {
        transform: scale(0.8) rotate(-90deg);
    }
}


.item-form {
    display: flex;
    flex-direction: column;
    padding: 10px;

    .el-form {
        margin-right: 15px;

        .el-form-item--mini.el-form-item {
            margin-bottom: 14px;
        }
    }

    .save-btn {
        text-align: right;
        margin-right: 20px;
    }
}

.back-popover-div {

    span {
        display: inline-block;
        padding: 5px;
    }
}

:deep(.el-form-item__error) {
    bottom: -13px;
}
</style>

