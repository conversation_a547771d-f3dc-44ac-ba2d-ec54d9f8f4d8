<template>
  <el-form-item v-if="!configData.iLayourValue && !configData.iIsHide" :prop="configData.sProp" :class="formItemClass"
    :style="{ width: configData.iLayourValue ? '100%' : (configData.sWidth && configData.sWidth.includes('%') ? configData.sWidth : '') }">
    <ConfigsForm :configData="configData" :formData="formData" :optionData="optionData" :formItemClass="formItemClass"
      :verifyObjName="verifyObjName" :isPickerOptions="isPickerOptions">
      <template v-slot:custom>
        <slot v-if="configData.iCustom" name="custom">
        </slot>
      </template>
    </ConfigsForm>
  </el-form-item>
  <div v-else-if="configData.iLayourValue && !configData.iIsHide" class="block float-left box-border" 
   :style="{ width: configData.iLayourValue * 100 / 24 + '%' }" :span="configData.iLayourValue">
    <el-form-item :prop="configData.sProp" :class="formItemClass"
      :style="{ width: configData.iLayourValue ? '100%' : (configData.sWidth && configData.sWidth.includes('%') ? configData.sWidth : '') }">
      <ConfigsForm :configData="configData" :formData="formData" :optionData="optionData" :formItemClass="formItemClass"
        :verifyObjName="verifyObjName" :isPickerOptions="isPickerOptions">
        <template v-slot:custom>
          <slot v-if="configData.iCustom" name="custom">
          </slot>
        </template>
      </ConfigsForm>
    </el-form-item>
  </div>

</template>
<script>
import ConfigsForm from './ConfigsForm'
export default {
  components: {
    ConfigsForm
  },
  props: {
    configData: { // 自定义数据
      type: Object,
      default: () => ({})
    },
    formData: { // 绑定的表单数据对象
      type: Object,
      default: () => ({})
    },
    optionData: { // 下拉选项
      type: Object,
      default: () => ({})
    },
    formItemClass: {
      type: String,
      default: ''
    },
    verifyObjName: { // 验证对象名称
      type: String,
      default: 'rules'
    },
    isPickerOptions: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    getParent(parents, field) {
      if (!parents[field] && parents.$parent) {
        return this.getParent(parents.$parent, field)
      }
      return parents;
    },
    // 如果没有必填，创建必填
    createRequired() {
      // 是否有验证对象
      const parent = this.getParent(this.$parent, this.verifyObjName)
      if (parent[`${this.verifyObjName}`]) {
        // 是否存在字段
        let rulesField = parent[`${this.verifyObjName}`][`${this.configData.sProp}`]
        if (rulesField) {
          let isExist = false
          for (const iterator of rulesField) {
            if (iterator.required) {
              isExist = true
              break;
            }
          }
          // 没有必填,添加必填
          if (!isExist) {
            rulesField.push({
              required: true,
              message: '必填'
            })
          }
        } else {
          // 不存在字段，初始化
          parent[`${this.verifyObjName}`][`${this.configData.sProp}`] = [{
            required: true,
            message: '必填'
          }]
        }
      } else {
        console.log('没有验证对象')
      }
    },
    // 如果没有长度验证，创建长度验证
    createLimit() {
      const parent = this.getParent(this.$parent, this.verifyObjName)
      // 是否有验证对象
      if (parent[`${this.verifyObjName}`]) {
        // 是否存在字段
        let rulesField = parent[`${this.verifyObjName}`][`${this.configData.sProp}`]
        if (rulesField) {
          let isExist = false
          for (const iterator of rulesField) {
            if (iterator.max) {
              isExist = true
              break;
            }
          }
          // 没有最大长度限制
          if (!isExist) {
            // 当前验证只能是字符串
            rulesField.push({
              max: Number(this.configData.iLimitLength),
              message: `超过最大长度${this.configData.iLimitLength}`,
            })
          }
        } else {
          // 不存在字段，初始化
          parent[`${this.verifyObjName}`][`${this.configData.sProp}`] = [{
            max: Number(this.configData.iLimitLength),
            message: `超过最大长度${this.configData.iLimitLength}`,
          }]
        }
      } else {
        console.log('没有验证对象')
      }
    },
    // 创建正则
    // 正则正确填写： ^[1-9]\\d*$ 
    // 不需要在左右二边加斜线 /xxx/,
    // \ 被js自动转义了需要多加一条如：\\
    createRegEx() {
      const parent = this.getParent(this.$parent, this.verifyObjName)
      if (parent[`${this.verifyObjName}`]) {
        // 是否存在字段
        let rulesField = parent[`${this.verifyObjName}`][`${this.configData.sProp}`]
        if (rulesField) {
          let isExist = false
          for (const iterator of rulesField) {
            if (iterator.validator) {
              isExist = true
              break;
            }
          }
          // 没有验证
          if (!isExist) {
            rulesField.push({
              validator: (rule, value, callback) => {
                if (value == undefined || value == null || value == '') {
                  callback()
                }
                let reg = new RegExp(this.configData.sRegEx);
                if (reg.test(value)) {
                  callback()
                }
                callback(new Error(this.configData.sRegExText || '请填入正确内容'))
              }
            })
          }
        } else {
          // 不存在字段，初始化
          parent[`${this.verifyObjName}`][`${this.configData.sProp}`] = [{
            validator: (rule, value, callback) => {
              if (value == undefined || value == null || value == '') {
                callback()
              }
              let reg = new RegExp(this.configData.sRegEx);
              if (reg.test(value)) {
                callback()
              }
              callback(new Error(this.configData.sRegExText || '请填入正确内容'))
            }
          }]
        }
      } else {
        console.log('没有验证对象')
      }
    }
  },
  created() {
    // 后端传递的值有必填字段
    if (this.configData.iRequired) {
      this.createRequired();
    }
    // 后端传递的值有长度验证
    if (this.configData.iLimitLength) {
      this.createLimit();
    }
    // 后端传递的值有正则验证
    if (this.configData.sRegEx) {
      this.createRegEx();
    }
  }
}
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 0px;
  margin-right: 0px;

  :deep(.el-form-item__content) {
    width: 100%;
  }
}
</style>
