<template>
  <!-- 集中打印 -->
  <LayoutTable>
    <template v-slot:header>
      <SearchList v-model:modelValue="condition" v-model:list="searchConfig" :optionData="optionsLoc"
        :iModuleId="iModuleId" storageKey="CentralizedPrintIndexSearch" @changeSearch="mxDoSearch"
        @reset="mxOnClickReset">
        <!-- 可选查询条件输入框 -->
        <!-- <template v-slot:value>
                <el-input v-model="condition.value" clearable @keyup.native.enter="mxDoSearch">
                  <template #prepend>
                    <div>
                      <el-select v-model="selectKey" placeholder="请选择" style="width: 105px"
                        @change="onChangeConditionKey">
                        <el-option v-for="(item, index) in optionsLoc.keyOptions" :key="index" :label="item.sName"
                          :value="item.sValue"></el-option>
                      </el-select>
                    </div>
                  </template>
                </el-input>
              </template> -->
        <!-- 就诊类型 -->
        <template v-slot:sVisitTypeCode="{ data }">
          <el-select v-model="condition.sVisitTypeCode" placeholder="" clearable @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.visitTypeOptions" :key="item.sValue" :value="item.sValue"
              :label="item.sName">
            </el-option>
          </el-select>
        </template>
        <!-- 院区 -->
        <template v-slot:sDistrictId="{ data }">
          <el-select v-model="condition.sDistrictId" placeholder="" clearable @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.districtData" :key="item.sId" :value="item.sId"
              :label="item.sDistrictPrefix">
            </el-option>
          </el-select>
        </template>
        <!-- 设备类型 -->
        <template v-slot:sDeviceTypeId="{ data }">
          <el-select v-model="condition.sDeviceTypeId" placeholder="" clearable @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.deviceTypeData" :key="item.sId" :value="item.sId"
              :label="item.sDeviceTypeName">
            </el-option>
          </el-select>
        </template>
        <!-- 开单日期 -->
        <template v-slot:sStudyDate>
          <el-date-picker v-model="sStudyDate" type="daterange" align="right" unlink-panels range-separator="至"
            start-placeholder="" end-placeholder="" :shortcuts="pickerOptions.shortcuts" style="height:100%;" @change="onChangeDates">
          </el-date-picker>
        </template>
      </SearchList>
    </template>
    <template v-slot:action>
      <div class="flex items-center justify-between">
        <div class="flex items-center justify-start">
          <el-button v-auth="'report:print:printConfig'" type="primary" plain @click="onOpenPrintSet">打印设置</el-button>
        </div>
        <div class="flex items-center justify-end">

          <el-radio-group v-model="condition.iPrint" @change="mxDoSearch" style="margin-right: 10px;">
            <el-radio :label="0">未打印</el-radio>
            <el-radio :label="1">已打印</el-radio>
            <el-radio :label="null">全部</el-radio>
          </el-radio-group>
          <!-- <el-checkbox v-model="isCheckedStudyDate" @change="mxDoSearch">检查日期</el-checkbox> -->
          <el-checkbox v-auth="'report:print:browserReport'" v-model="isPreviewModel">预览模式</el-checkbox>
          <span class="mr-4"></span>
          <el-button @click="mxOnClickReset(onReset)">
            <template #icon>
              <Icon name="el-icon-refresh-left">
              </Icon>
            </template>
            重置</el-button>
          <el-button v-auth="'report:print:query'" type="primary" :loading="loading" @click="mxDoSearch">
            <template #icon>
              <Icon name="el-icon-search" color="white"></Icon>
            </template>
            查询
          </el-button>

        </div>
      </div>

    </template>
    <template v-slot:content>
      <DragAdjust class="flex w-full h-full" :dragAdjustData="computedDA1">
        <template v-slot:c1>
            <el-table-extend :data="tableData" ref="mainTable" class="mainTable" :row-class-name="mxRowClassName"
              highlight-current-row @row-click="onClickRow" :iModuleId="iModuleId" storageKey="CentralizedPrintIndexTable"
              @sort-change="mxOnSort" stripe height="100%" style="width: 100%">
              <el-table-column type="index" prop="_index" label="序号" align="center" width="60">
              </el-table-column>
              <el-table-column v-for="item in tableConfig.filter(i => !i.iIsHide)"
                :show-overflow-tooltip="item.sProp !== 'img'" :key="item.index" :prop="item.sProp" :label="item.sLabel"
                :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth"
                :sortable="(!!item.iSort) ? 'custom' : false" :column-key="item.sSortField ? item.sSortField : null">
                <template v-slot="{ row }">
                  <template v-if="item.sProp === 'iPrint'">
                    <i v-if="row[item.sProp]" class="fa fa-sure-fill" style="color:#67c23a; vertical-align: middle;"></i>
                    <el-button v-auth="'report:print:markPrint'" v-else type="primary" plain size="small"
                      @click="onMarkPrint(row)">标记</el-button>
                  </template>
                  <template v-else-if="item.sProp === 'sImgStudyDate'">
                    <!-- 检查日期 -->
                    <template v-if="row.sImgStudyDate">
                      {{ row.sImgStudyDate.replace(/^(\d{4})(\d{2})(\d{2})$/, "$1-$2-$3") }}
                    </template>
                  </template>
                  <template v-else-if="item.sProp === 'Actions'">
                    <!-- 刻录 -->
                    <CdburnBtn v-if="$auth['report:print:cdBurn']" :buttonMsg="{ name: '刻录' }" :patientInfo="row">
                      <el-button-icon-fa icon="fa fa-cd-fill" link>刻录</el-button-icon-fa>
                    </CdburnBtn>

                    <!-- 回传图像 -->
                    <el-button-icon-fa v-auth="'report:print:sendDicom'" icon="fa fa-data-back" link
                      @click="uploadImage(row)" style="margin-left:10px;">回传图像</el-button-icon-fa>
                  
                  </template>
                  <template v-else-if="item.sProp === 'Print'">
                    <!-- 报告预览 -->
                    <!--  style="margin-right: 10px;" -->
                    <span v-auth="'report:print:browserReport'">
                      <ReportPreviewBtn
                        :propParams="{ patient: row, idKey: 'sPatientInfoId', deviceTypeIdKey: 'sDeviceTypeId', iModuleId: iModuleId }">
                        <el-button-icon-fa icon="fa fa-article" link>预览报告</el-button-icon-fa>
                      </ReportPreviewBtn>
                    </span>

                    <!-- 打印 -->
                    <span v-auth="'report:print:printReport'">
                      <ReportPrintBtn
                        :propParams="{ patient: row, isBatch: false, idKey: 'sPatientInfoId', deviceTypeIdKey: 'sDeviceTypeId', iModuleId: iModuleId }"
                        :isCentralizedPrint="true">
                        <el-button-icon-fa icon="fa fa-print-line2" link>打印报告</el-button-icon-fa>
                      </ReportPrintBtn>
                    </span>

                  </template>
                  <template v-else>
                    {{ row[item.sProp] }}
                  </template>
                </template>
              </el-table-column>
            </el-table-extend>
        </template>
        <template v-slot:c2>
          <Preview v-model:patient="editLayer.selectedItem"></Preview>
        </template>
      </DragAdjust> 
    </template>
    <template v-slot:footer>
      <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
        :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next" :total="page.total">
      </el-pagination>
    </template>


    <!-- 预览模式 -->
  </LayoutTable>

  <!-- 打印设置 -->
  <PrintSet :dialogVisible="d_printSet_v" :iModuleId="iModuleId" @closeDialog="closePrintSetDialog"></PrintSet>
</template>
<script>
// vue组件
import FormStyle from '$supersetViews/components/FormStyle.vue'

import config from './config/index'
import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { transformDate } from '$supersetResource/js/tools'
import { deepClone } from '$supersetUtils/function'
import { getToken } from '$supersetUtils/auth'

import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'


import { fileDownandopen, downAndPrintBatch, BPMMsgCreate } from '$supersetApi/projects/apricot/case/report.js'
import { printOutList, printOutReportPath, printOutMarkPrint } from '$supersetApi/projects/apricot/centralizedPrint/index.js'
import { getHospitalData, getDeviceTypeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'

export default {
  name: 'apricot_CentralizedPrint',
  mixins: [mixinTable],
  components: {
    FormStyle,
    ReportPreviewBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPreviewBtn.vue')),
    ReportPrintBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPrintBtn.vue')),
    CdburnBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/CdburnBtn.vue')), // 刻录
    Preview: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/Preview.vue')), // 预览
    PrintSet: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintSet.vue'))
  },
  data() {
    return {
      iModuleId: 11, // 集中打印标识 ，eName: 'CENTRALIZED_PRINT'， 在mixinPrintPreview混合模块中调用
      searchConfig: config.elementConfigData.searchConfig,
      tableConfig: config.elementConfigData.tableConfig,
      loading: false,
      condition: {
        iPrint: null
      },
      sStudyDate: [moment(), moment()],
      selectKey: localStorage.getItem('centralizedPrintDefaultSelectedKey') || 'sNuclearNum',
      optionsLoc: {
        visitTypeOptions: appointmentEnum.visitTypeOptions,
        keyOptions: [
          { sName: '核医学号', sValue: 'sNuclearNum' },
          { sName: '姓名', sValue: 'sName' },
          { sName: '申请单号', sValue: 'sApplyNO' },
          { sName: '医嘱号', sValue: 'sOrderNO' },
          { sName: '门诊号', sValue: 'sOutpatientNO' },
          { sName: '住院号', sValue: 'sInHospitalNO' },
          { sName: '就诊卡号', sValue: 'sVisitCard' },
          { sName: '病历号', sValue: 'sMedicalRecordNO' },
        ],
        districtData: [],
        deviceTypeData: []
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          value() {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end]
          }
        }, {
          text: '最近一个月',
          value() {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end]
          }
        }, {
          text: '最近三个月',
          value() {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            return [start, end]
          }
        }]
      },
      isCheckedStudyDate: true,
      isPreviewModel: window.localStorage.getItem('CentralizedPrintIndex-isShowPreviewModel') == 'true',
      isFirstQuery: true,
      d_printSet_v: false,
      page: {  // 分页	
        pageCurrent: 1,
        pageSize: localStorage.centralizedPrintIndexPageSize ? JSON.parse(localStorage.centralizedPrintIndexPageSize) : 30,
        total: 0
      },
    }
  },
  computed: {
    workStation() {
      let temp = this.$store.getters['user/workStation'];
      return temp
    },
    userInfo() {
      let temp = this.$store.getters["user/userSystemInfo"];
      if (temp.__proto__.constructor === Object) {
        return temp;
      } else {
        return {};
      }
    },
    computedDA1() {
      const panelConfig = [{
        size: 0,
        minSize: 100,
        name: "c1",
        isFlexible: true
      },
      {
        size: 750,
        minSize: 10,
        maxSize: 1700,
        name: "c2",
        isFlexible: false
      }
      ].filter(i => this.isPreviewModel ? 1 : i.name === 'c1')
      const DA1 = {
        type: 't-x',
        localStorageKey: '202308100000',
        panelConfig: panelConfig
      }
      return DA1
    }
  },
  watch: {
    'page.pageSize'(val) {
        localStorage.setItem('centralizedPrintIndexPageSize', val);
    },
    workStation(val) {
      this.$nextTick(() => {
        if (val) {
          this.condition['sDistrictId'] = val.districtId;
        }
      })
    },
    isPreviewModel(val) {
      window.localStorage.setItem('CentralizedPrintIndex-isShowPreviewModel', val)
    }
  },
  methods: {
    //  打开打印设置
    onOpenPrintSet() {
      this.d_printSet_v = true;
    },
    closePrintSetDialog() {
      this.d_printSet_v = false;
    },
    onChangeDates() {
      this.mxDoSearch();
    },
    onMarkPrint(row) {
      let loading = this.$loading({
        lock: true,
        text: '正在标记中，请稍等',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      printOutMarkPrint({ sPatientId: row.sPatientInfoId }).then(res => {
        loading.close()
        if (res.success) {
          this.$message.success(res.msg);
          this.mxGetTableList();
          return
        }
        this.$message.error(res.msg)
      }).catch(() => {
        loading.close()
      })
    },
    // 图像回传
    uploadImage(item) {
      let jsonData = {
        sPatientId: item.sPatientInfoId,
        sMsgPoint: '250'
      }
      let loading = this.$loading({
        lock: true,
        text: '发送中，请稍等...',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      BPMMsgCreate(jsonData).then(res => {
        loading.close();
        if (res.success) {
          this.$message.success(res.msg);
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err);
        loading.close();
      })
    },
    // 报告预览
    openReportPreviewWindow(item) {
      let jsonData = {
        iPrint: 0,
        sPatientId: item.sPatientInfoId
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      printOutReportPath(jsonData).then(res => {
        loading.close();
        if (res.success) {
          let suffix = res.data.sFileType
          if (suffix !== 'pdf') {
            let downfileUrl = window.configs.urls.downfileUrl;
            downfileUrl = downfileUrl.includes('http') || downfileUrl.includes('https') ? downfileUrl : window.location.origin + downfileUrl;
            fileDownandopen({
              sUrlStr: downfileUrl,
              sFilePath: res.data.sFilePath,
            }).then(res => {
              if (!res.success) {
                this.$message.error(res.msg);
              }
            }).catch(err => {
              console.log(err)
            })
            return
          }
          let apricot = window.configs.urls.apricot;
          apricot = apricot.includes('http') || apricot.includes('https') ? apricot : window.location.origin + apricot;
          window.open(apricot + `/printing/previewpdf?sFilePath=` + res.data.sFilePath, '_blank', 'noopener');
          return
        }
        this.$message.error(res.msg);
      }).catch(() => {
        loading.close();
      })
    },
    // 报告打印
    openReportPrintWindow(item) {
      let jsonData = {
        iPrint: 1,
        sPatientId: item.sPatientInfoId
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      printOutReportPath(jsonData).then(res => {
        loading.close()
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        if (!res.data) {
          this.$message.error('没有获取到文件路径');
          return
        }
        this.downAndPrintBatch([res.data.sFilePath], item)
      }).catch(err => {
        loading.close()
        console.log(err)
      })
    },
    openImgReportPrint(item) {
      let apricot = window.configs.urls.apricot;
      apricot = apricot.includes('http') || apricot.includes('https') ? apricot : window.location.origin + apricot;
      window.open(apricot + `/printout/imgReport?sPatientId=` + item.sPatientInfoId);
    },
    // 调用打印机打印
    downAndPrintBatch(array) {
      let downfileUrl = window.configs.urls.downfileUrl;
      downfileUrl = downfileUrl.includes('http') || downfileUrl.includes('https') ? downfileUrl : window.location.origin + downfileUrl;
      let jsonArray = array.map(item => {
        return {
          sUrlStr: downfileUrl,
          sFilePath: item,
          // sPrinterFlag: templateItem.sActivePrinter,
          // iCopies: templateItem.iCopies
        }
      });
      const loading = this.loadFindTip('打印中...');
      downAndPrintBatch(jsonArray).then(res => {
        loading.close();
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        this.$message.success(res.msg);
      }).catch(err => {
        loading.close();
        console.log(err)
      })
    },
    // 加载提示
    loadFindTip(text) {
      return this.$loading({
        lock: true,
        text: text || '文件生成中...',
        // 
        background: 'rgba(0, 0, 0, 0.1)',
        customClass: 'my-loading'
      });
    },
    // 改变condition key值
    onChangeConditionKey(val) {
      localStorage.setItem('centralizedPrintDefaultSelectedKey', val);
    },
    onReset() {
      this.sStudyDate = [moment(), moment()];
      this.condition.iPrint = null;
      this.condition['sDistrictId'] = this.workStation.districtId;
    },
    getData(obj) {
      let temp = deepClone(obj)
      let params = Object.assign({}, temp.condition, temp.page);
      params[this.selectKey] = params.value;
      delete params.value;

      if (this.sStudyDate) {
        let sStudyDateSta = this.sStudyDate[0] || ''
        if (sStudyDateSta) {
          params.sStudyDateSta = transformDate(sStudyDateSta)
        }
        let sStudyDateEnd = this.sStudyDate[1] || ''
        if (sStudyDateEnd) {
          params.sStudyDateEnd = transformDate(sStudyDateEnd)
        }
      }


      if (this.isFirstQuery) {
        params.sDistrictId = this.workStation.districtId;
        this.isFirstQuery = false;
      }
      printOutList(params).then(res => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data.records || [];
          this.page.total = res.data.total;
          this.tableData.length && this.tableData.map(item => {
            item.sPatientId = item.sPatientInfoId
            item.sId = item.sPatientInfoId
          });
          // 赋选中状态
          this.mxSetSelected()
          return
        }
        this.$message.error(res.msg);
      }).catch(err => {
        this.loading = false;
        this.tableData = []
        console.log(err)
      })
      return
    },
    // 获取院区数据
    getDistrictData() {
      getHospitalData().then((res) => {
        if (res.success) {
          this.optionsLoc.districtData = res?.data || [];
          this.condition['sDistrictId'] = this.workStation.districtId;
        }
      }).catch(() => {
      })
    },
    // 获取设备类型数据
    getDeviceTypeData() {
      getDeviceTypeData().then((res) => {
        if (res.success) {
          this.optionsLoc.deviceTypeData = res?.data || [];
        }
      }).catch(() => {
      })
    }
  },
  created() {
    this.getDistrictData();
    this.getDeviceTypeData();
  }
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.el-radio {
  margin-right: 10px;
}

.mainTable {
  border-right: 1px solid #ddd;

  :deep(.current-row) {
    vertical-align: middle !important;
  }
}
</style>
