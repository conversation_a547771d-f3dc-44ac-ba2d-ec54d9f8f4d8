<template>
  <el-menu class="main-menu" mode="horizontal" :ellipsis="false">
    <el-sub-menu index="2">
      <template #title>
        <div class="flex items-center">
          <el-icon style="margin-right: 2px;">
            <User />
          </el-icon>
          <span class="user-name" style=" ">
            {{ userName }}
          </span>
        </div>
      </template>
      <el-menu-item index="2-2" @click="setChangePwd">
        <span class="w-28 ml-4">修改密码 </span>
      </el-menu-item>
      <el-menu-item index="2-3" @click="handleLogout">
        <span class="w-28 ml-4">登出 </span>
      </el-menu-item>
    </el-sub-menu>
  </el-menu>
  <!-- <el-dropdown @command="handleCommand">
    <span class="avatar-dropdown"> 
      <div class="user-name">
        <el-icon style="margin-right: 8px;">
          <User />
        </el-icon>
        <span class="text" style=" ">
        {{ userName }}
        </span>
        <el-icon style="margin-left: 8px;">
          <ArrowDown />
        </el-icon>
      </div>
    </span>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="changePwd">修改密码</el-dropdown-item>
        <el-dropdown-item command="changePwd">修改密码</el-dropdown-item>
        <el-dropdown-item command="logout" divided>登出</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown> -->

  <changePwd></changePwd>
</template>

<script>
export default {
  name: 'Avatar',
};
</script>

<script setup>
import { ref, computed } from 'vue';
import { useStore } from 'vuex';
import { ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';

import changePwd from './changePwd.vue'

import { User, ArrowDown } from '@element-plus/icons-vue'

const avatar = ref('');
const store = useStore();
const router = useRouter();
const userName = computed(() => store.getters['user/username'])


const handleCommand = (command) => {
  switch (command) {
    case 'logout':
      handleLogout();
      break;
    case 'changePwd':
      store.dispatch('setting/setChangePwd', true)
      break;
    default:
      break;
  }
};

const handleLogout = async () => {
  await store.dispatch('user/logout');
};

const setChangePwd = async () => {
  store.dispatch('setting/setChangePwd', true)

};

</script>

<style lang="scss" scoped>
.main-menu {
  position: relative;
  top: 1px;
  background: transparent;
  margin-left: 5px;

  &:before {
        content: "";
        width: 1px;
        height: 16px;
        position: absolute;
        left: 0;
        top: 16px;
        background-color: rgba(255, 255, 255, 0.3);
        z-index: 1;
    }

  :deep(.el-sub-menu__title) {
    padding: 0 39px 0 15px;
    --el-menu-text-color: var(--theme-header-color);
  }

  :deep(.el-sub-menu__title:hover) {
    background-color: transparent;
  }

  :deep(.el-sub-menu .el-sub-menu__title:hover) {
    color: var(--theme-header-color);
  }

  :deep(.el-sub-menu.is-active .el-sub-menu__title) {
    color: var(--theme-header-color);
  }


  :deep(.el-menu--popup) {
    min-width: 10px;
  }

  :deep(.el-sub-menu__title) { 
    line-height: 45px;
  }
}

.user-name {
  // color: var(--theme-header-color);
  // --el-menu-text-color: var(--el-text-color-primary);
}
</style>
