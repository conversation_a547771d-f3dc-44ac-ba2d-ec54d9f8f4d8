<template>
    <div class="c-flex" v-loading="loading">
        <RenderMainGenerator ref="generator" :fCoefficient="fCoefficient" :fDosage="fCoefficient" :renderData="renderData">
        </RenderMainGenerator>
        <Teleport v-if="isTeleportTrue" to=".my-consult-doctors">
            <el-form v-show="renderData.length" ref="form" :model="formData" :rules="rules" inline>
                <FormList :list="formConfig" :formData="formData" :optionData="optionsLoc" :iModuleId="iModuleId"
                    :configBtn="isShowConfigBtn" storageKey="ConsultMngConsultInfoForm">
                    <template v-slot:sConsultId="{ style }">
                        <el-select v-model="formData.sConsultId" class="my-select" placeholder=" " :style="style">
                            <el-option v-for="(item, index) in optionsLoc.doctorOptions" v-show="!item?.iIsAuxiliary" :key="index" :label="item.userName"
                                :value="item.userId"></el-option>
                        </el-select>
                        
                    </template>
                    <template v-slot:dConsultTime="{ style }">
                        <el-date-picker :disabled-date="disabledDateConsultTime" :style="style"
                            v-model="formData.dConsultTime" type="datetime" format="YYYY-MM-DD HH:mm">
                        </el-date-picker>
                    </template>
                </FormList>
            </el-form>
        </Teleport>

        <Teleport v-if="isTeleportTrue && $auth['report:inquisition:save']" to=".my-consult-save" $attrs>
            <el-button-icon-fa type="primary" icon="fa fa-save" @click="onClickSave">保存</el-button-icon-fa>
        </Teleport>

        <PrintTemDialog :dialogVisible.sync="visibleTemplate"></PrintTemDialog>
    </div>
</template>
<script>
// 深克隆
import { deepClone } from '$supersetUtils/function';
// 混入
import { mixinPrintPreview } from '$supersetResource/js/projects/apricot/index.js'
// 接口
import { getPatientBriefById } from '$supersetApi/projects/apricot/case/index.js';
import { getConsultTemplateData, addPatientConsult, editPatientConsult, delPatientConsult } from '$supersetApi/projects/apricot/case/consult.js';
import { queryUserListByType } from '$supersetResource/js/projects/apricot/useHandlerSelect.js' // 获取全部用户

export default {
    components: {
        RenderMainGenerator: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/customGenerator/RenderMainGenerator.vue')),
        PrintTemDialog: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintTemDialog.vue'))
    },
    mixins: [mixinPrintPreview],
    props: {
        deviceTypeKey: String,
        isConsultModule: {
            type: Boolean,
            default: false
        }
    },
    emits: ['onClose'],
    inject: {
        patientInfo: {
            from: 'patientInfo',
            default: () => ({})
        },
        updateTableInfo: {
            from: 'updateTableInfo',
            default: function() {}
        },
        configValue: {
            from: 'configValue',
            default: () => ({})
        },
    },
    data () {
        return {
            iModuleId: 3, // 文字管理标识 ，eName: 'INQUISITION'， 在mixinPrintPreview混合模块中调用
            loading: false,
            loadingSave: false,
            loadingDel: false,
            renderData: [],
            formData: {
                sConsultId: undefined,
                dConsultTime: undefined
            },
            optionsLoc: {
                doctorOptions: []
            },
            pickerOptions0: {
                disabledDate (time) {
                    return time.getTime() > Date.now() + 8.64e6;
                }
            },
            fCoefficient: undefined,  // 处方系数
            fDosage: undefined, // 定量剂量
            sId: undefined,
            deviceTypeId: undefined,
            isTeleportTrue: false,
            disabledDateConsultTime: time => {
                return time.getTime() > Date.now() + 8.64e6;
            },
            isLoadingDoctors: false,
            formConfig: [{
                sProp: 'sConsultId',
                sLabel: '问诊医生',
                iLayourValue: 12,
                iRequired: 1,
                iCustom: 1,
                height: '30px'
            },{
                sProp: 'dConsultTime',
                sLabel: '问诊时间',
                iLayourValue: 12,
                iRequired: 1,
                iCustom: 1,
                height: '30px'
            }],
            rules: {
                sConsultId: [{ required: true, message: '', trigger: 'blur' }],
                dConsultTime: [{ required: true, message: '', trigger: 'blur' }],
            }
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
        isShowConfigBtn() {
            return this.configValue?.isShowConfigBtn || false;
        }
    },
    watch: {
        patientInfo: {
            async handler (val, oldVal) {
                if (val && oldVal && val.sId === oldVal.sId) return
                if (!this.optionsLoc.doctorOptions.length) {
                    this.optionsLoc.doctorOptions = await queryUserListByType(3);
                }
                if (!val.sId) {
                    this.noData()
                    return
                }
                this.deviceTypeId = val[this.deviceTypeKey]
                this.sId = val.sId;
                this.getConsultTemplateData();
            }
        },
    },
    methods: {
        noData () {
            this.renderData = [];
            this.formData = this.$options.data().formData;
            this.fCoefficient = undefined;  // 处方系数
            this.fDosage = undefined; // 定量剂量
        },
        // 获取问诊模板数据
        getConsultTemplateData () {
            getConsultTemplateData({ sPatientId: this.sId }).then(res => {
                this.loading = false;
                if (res.success) {
                    const data = res.data.data || res.data;
                    if (!data.sTemplateDataJson) {
                        // 发现不是问诊模板患者，跳转显示非模板问诊
                        // this.$emit('changeOldTemplate')
                        this.$message.error('未返回sTemplateDataJson数据');
                        this.renderData = [];
                        return
                    }
                    if (data.sDataJson) {
                        const attrs = JSON.parse(data.sDataJson)
                        const tree = JSON.parse(data.sTemplateDataJson)

                        const setTemplateData = (list) => {
                            list.forEach(item => {
                                if (item.layout === 'formItem') {
                                    const findObj = attrs.find(attr => attr.FieldName === item.field)
                                    if (findObj && findObj.FieldValue !== undefined && findObj.FieldValue !== null) {
                                        item.value = findObj.FieldValue
                                        // console.log(item.value)
                                    } else if (item.children) {
                                        setTemplateData(item.children)
                                    }
                                }
                            })
                        }
                        setTemplateData(tree)
                        data.sTemplateDataJson = JSON.stringify(tree)
                    }
                    this.initConsult(data)
                    return
                }
                this.$message.error(res.msg);
                this.renderData = [];
            }).catch(() => {
                this.loading = false;
                this.renderData = [];
            })
        },
        // 显示显示非该用户类型的医生
        AddDocOptionToShow(params){
            const targetItem = this.optionsLoc.doctorOptions.find(item => item.userId === params.sConsultId);
            if(!targetItem) {
                this.optionsLoc.doctorOptions.push({
                    userId: params.sConsultId,
                    userNo: params.sConsultNo,
                    userName: params.sConsultName,
                    sValue: params.sConsultId,
                    sName: params.sConsultName,
                    iIsAuxiliary: 1,
                })
            }
        },
        initConsult (data) {
            data = deepClone(data)
            this.formData.sMemo = data.sMemo;
            this.fCoefficient = data.fCoefficient;
            this.fDosage = data.fDosage;
            this.renderData = JSON.parse(data.sTemplateDataJson);
            this.formData.sCustomTemplateId = data.sCustomTemplateId; // 模板 id
            if (data.sPatientId) {
                this.AddDocOptionToShow(data)
                // 已问诊
                this.formData.sId = data.sId; // 问诊 id
                this.formData.sConsultId = data.sConsultId; // 问诊医生 id
                this.formData.dConsultTime = data.dConsultTime; // 问诊时间
                return
            }
            // 未问诊
            delete this.formData.sId;
            this.setDoctorAndTime();
            this.loading = true
            // 获取申请单信息，赋值到问诊模板中
            getPatientBriefById({ sId: this.sId }).then(res => {
                this.loading = false

                if (res.success) {
                    // 需要赋值到模板中的属性
                    const attrs = ['sClinicalDiagnosis', 'sMedicalHistory', 'sInquiryOtherMessage', 'fHeight', 'fWeight', 'fBloodSugar', 'fRecipeDose', 'sRecipeDoseUnit'];
                    const getSpecialAttr = (list) => {
                        list.forEach(item => {
                            // 包含这三个属性
                            if (item.layout === 'formItem' && attrs.includes(item.field)) {
                                item.value = item.field === 'sInquiryOtherMessage' ? res.data.sPastHistory : res.data[item.field]
                            } else if (item.children) {
                                getSpecialAttr(item.children);
                            }
                        });
                    }
                    const dataTemplate = JSON.parse(data.sTemplateDataJson)
                    getSpecialAttr(dataTemplate);
                    this.renderData = dataTemplate;

                    return;
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false
            })
        },
        onClickSave () {
            if (!this.patientInfo.sId) {
                this.$message.warning('请选择患者数据');
                return
            }
            if(!this.formData.dConsultTime) {
                this.$message.warning('问诊时间不能为空！');
                return
            }
            // 验证收据
            this.$refs['generator'].validate().then(res => {
                if (res.code === 200) {
                    this.loadingSave = true;
                    const params = Object.assign({}, this.formData);
                    // 赋值医生工号和医生名字
                    this.optionsLoc.doctorOptions.find(item => {
                        if (item.userId === params.sConsultId) {
                            params.sConsultNo = item.userNo;
                            params.sConsultName = item.userName;
                        }
                    })
                    
                    params.sPatientId = this.sId;
                    // 模板json赋值
                    params.sTemplateDataJson = res.data;
                    params.sDataJson = [];
                    // 特殊属性字段
                    const attrs = ['sClinicalDiagnosis', 'sMedicalHistory', 'sInquiryOtherMessage', 'sInjection', 'sInspect', 'fHeight', 'fWeight', 'fBloodSugar', 'fRecipeDose', 'sRecipeDoseUnit'];
                    // 获取设置特殊字段参数
                    const getSpecialAttr = (list) => {
                        list.forEach(item => {
                            // 包含这三个属性
                            if (item.layout === 'formItem') {
                                if (attrs.includes(item.field)) {
                                    const dataType = toString.call(item.value).slice(8, -1)

                                    if (dataType === 'Array') {
                                        params[item.field] = item.value.join(',');
                                    } else {
                                        params[item.field] = item.value;
                                    }
                                }
                                params.sDataJson.push({
                                    FieldName: item.field,
                                    FieldValue: item.value,
                                    FieldLabel: item.label
                                })
                            } else if (item.children) {
                                getSpecialAttr(item.children);
                            }
                        });
                    }
                    getSpecialAttr(JSON.parse(res.data));
                    // 问诊信息数据json
                    params.sDataJson = JSON.stringify(params.sDataJson);

                    if (!params.sCustomTemplateId) {
                        // 没有找到问诊
                        this.$message.warning('未找到关联的问诊模板，无信息保存!');
                        return
                    }

                    if (this.formData.sId) {
                        // 编辑
                        editPatientConsult(params).then(res => {
                            this.loadingSave = false;
                            if (res.success) {
                                this.$message.success(res.msg);
                                // 编辑时，根据缓存设置判断是否需要打印；
                                this.isConsultModule && this.mxOnPrintByCache(this.configValue, false);
                                // 非问诊模块， 关闭弹窗
                                // 江门中心不需要主动关闭弹窗
                                // !this.isConsultModule && this.$emit('onClose');
                                return;
                            }
                            this.$message.error(res.msg);
                        }).catch(() => {
                            this.loadingSave = false;
                        })
                    } else {
                        // 新增
                        addPatientConsult(params).then(res => {
                            this.loadingSave = false;
                            if (res.success) {
                                this.$message.success(res.msg);
                                this.formData.sId = res.data.sId;
                                // 新增时，根据缓存设置判断是否需要打印；
                                this.isConsultModule && this.mxOnPrintByCache(this.configValue);
                                this.isConsultModule && this.getConsultTemplateData();
                                this.isConsultModule && this.updateTableInfo();
                                // 非问诊模块， 关闭弹窗
                                // 江门中心不需要主动关闭弹窗
                                // !this.isConsultModule && this.$emit('onClose');
                                return;
                            }
                            this.$message.error(res.msg);
                        }).catch(() => {
                            this.loadingSave = false;
                        })
                    }
                }
            })
        },
        onClickDel () {
            if (!this.formData.sId) {
                this.$message.warning('当前并无问诊信息');
                return;
            }
            this.$confirm(`您确定要删除吗？`,
                {
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: '是',
                    cancelButtonText: '否',
                    confirmButtonClass: ''
                }).then(() => {
                    this.loadingDel = true;

                    delPatientConsult({ sId: this.formData.sId }).then(res => {
                        this.loadingDel = false;
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.loading = true;
                            this.getConsultTemplateData();
                            return;
                        }
                        this.$message.error(res.msg);
                    }).catch(() => {
                        this.loadingDel = false;
                    })

                })
        },
        onClickRefresh () {
            this.loading = true;
            this.getConsultTemplateData();
        },
        // 设置问诊医生和问诊时间
        setDoctorAndTime () {
            this.formData['sConsultId'] = this.userInfo.sId;
            this.formData['dConsultTime'] = new Date()
        },

    },
    mounted () {
        this.isTeleportTrue = true;
    },

}
</script>
<style lang="scss" scoped>
.c-flex {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .c-flex-auto {
        flex: 1;
    }

    .c-flex-fixed {
        padding: 0px 15px;
        height: 60px;
        line-height: 60px;
        border-top: 1px solid #eee;

        &.c-tabls-button {
            text-align: right;
        }

        .form-box {
            height: 30px;
            line-height: 30px;
            display: inline-block;
            margin-right: 20px;

            >div {
                display: inline-block;
                margin: 0 6px;
            }
        }
    }
}

.my-select {
    width: 100px;

    :deep(.el-input) {
        width: 100%;
    }
}

</style>
