@import './variable.scss';

.my-popover {
    max-width: 565px;
}

.u-none {
    opacity: 0 !important;
}

.c-flex-context {
    box-sizing: border-box;
}

.c-check-input {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    .el-input {
        :deep(input) {
            padding-left: 36px;
        }
    }
    label {
        height: 20px;
        line-height: 20px;
        margin-left: 10px;
        padding-left: 0;
        top: -2px;
        border-radius: 2px;
    }
    &.i-start {
        .i-check {
            width: 10px;
            height: 10px;
            display: block;
            text-align: center;
            border: 1px solid #c1c1c3;
            position: relative;
            margin-right: 2px;
            background-color: #edf2fc;
            margin: 0px 10px;

            &:after {
                content: '';
                position: absolute;
                width: 6px;
                height: 10px;
                left: 1px;
                top: -4px;
                border-right: 2px solid #2484d1;
                border-bottom: 2px solid #2484d1;
                transform: skew(-30deg, 45deg);
                border-radius: 2px;
            }
        }
        .el-input {
            :deep(input) {
                border-color: #2484d1;
            }
        }
    }

    &.i-disable {
        .i-check {
            width: 10px;
            height: 10px;
            display: block;
            text-align: center;
            margin-right: 2px;
            border: 1px solid #c1c1c3;
            background-color: white;
            margin: 0px 10px;
        }
    }
    .c-i-innner {
        height: 31px;
        background-color: #f2f4f8;
        display: flex;
        align-items: center;
        cursor: no-drop;
        position: absolute;
        z-index: 2;
        left: 1px;
        top: -1px;
    }
}

@mixin base-scrollbar {
    &:hover {
        &::-webkit-scrollbar {
            width: 12px;
            height: 12px;
        }
        &::-webkit-scrollbar-thumb {
            background-color: #aaa;
        }
        &::-webkit-scrollbar-thumb:hover {
          background-color: #666;
        }
    }
    &::-webkit-scrollbar {
        width: 12px;
        height: 12px;
        background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: transparent;
        background-clip: padding-box;
        border: none;
        cursor: pointer;
        border-radius: 7px;
        transition: 0.15s background-color;
    }

    &::-webkit-scrollbar-track {
        background-color: transparent;
    }

    &::-webkit-scrollbar-track:hover {
        background-color: transparent;
        // background-color: #f8fafc;
    }

    &::-webkit-scrollbar-button {
        height: 0;
        width: 0;
    }
}

@media (min-width: 640px) {
    .container {
        max-width: 99999px;
    }
}
@media (min-width: 768px) {
    .container {
        max-width: 99999px;
    }
}
@media (min-width: 1024px) {
    .container {
        max-width: 99999px;
    }
}
@media (min-width: 1280px) {
    .container {
        max-width: 99999px;
    }
}
@media (min-width: 1536px) {
    .container {
        max-width: 99999px;
    }
}
