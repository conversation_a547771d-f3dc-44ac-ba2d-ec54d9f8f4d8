<template>

    <div class="c-flex-context">
        <div class="c-form">
            <h4 class="modulesName">
                <!-- 模块名称：<span>{{ getModuleName(iModuleId) }}</span> -->
            </h4>
            <div class="pull-right">
                <!-- <el-button-icon-fa _icon="fa fa-save"
                    type="primary"
                    @click="saveSelected"
                    :loading="btnLoading">保存</el-button-icon-fa> -->
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table :data="tableData"
                    id="itemTable"
                    ref="mainTable"
                    border
                    stripe
                    height="100%"
                    style="width: 100%"
                    @select="saveSelected"
                    >
                    <el-table-column type="selection"
                        width="55"
                        align="center"></el-table-column>
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort">
                        <!-- <template v-slot="scope">
                            <template>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template> -->
                        <!-- <template v-slot:header
                            v-slot="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>

</template>
<script>
// import { systemModuleOption } from '$supersetResource/js/projects/apricot/enum.js'
import Api from '$supersetApi/projects/apricot/system/modulePrintConfig.js'
// import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: '',
    //mixins: [mixinTable],
    components: {},
    props: {
        iModuleId: ''
    },
    emits:['updateRelatedTemplate'],
    data () {
        return {
            loading: false,
            tableData: [],
            selectedTemplates: [],
            btnLoading: false,
            saveTemlate: false,
            configTable: [
                {
                    sProp: 'sName',
                    sLabel: '模板名称',
                    sAlign: 'left',
                    sMinWidth: '300px'
                },
                 {
                    sProp: 'sType',
                    sLabel: '内容类型',
                    sAlign: 'center',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sClassify',
                    sLabel: '模板类型',
                    sAlign: 'center',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'sDeviceTypeName',
                    sLabel: '设备类型',
                    sAlign: 'center',
                    sMinWidth: '100px',
                },
               
                ],
        }
    },
    methods: {
        // 模块名称
        // getModuleName(val) {
        //     let target = systemModuleOption.find(item => val === item.sValue);
        //     return target ? target.sName : null;
        // },

        /**
         * 保存数据
         */
        saveSelected () {
            const selectedRow = this.$refs.mainTable.getSelectionRows()
            this.btnLoading = true
            const iModuleId = this.iModuleId
            let params = []
            if (selectedRow.length > 0) {
                selectedRow.forEach(item => {
                    params.push({
                        iModuleId: iModuleId,
                        sTemplateId: item.sId,
                    })
                })
            } else {
                params = [{
                    iModuleId: iModuleId,
                    sTemplateId: '',
                }]
            }
            Api.saveTemlateSelect(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    this.$emit('updateRelatedTemplate', true);
                    return
                }
                this.$message.error(res.msg)
                this.getModuleTemlateSelected(iModuleId)
            }).catch(err => {
                console.log(err)
            })
        },

        // 获取模块打印设置数据
        getModuleTemlateSelected (moduleId) {
            this.loading = true
            let params = {
                iModuleId: moduleId
            }
            let selected = []
            this.$refs.mainTable.clearSelection()
            Api.getTemlateSelected(params).then(res => {
                this.loading = false
                if (res.success) {
                    selected = res.data;
                    if (selected.length > 0) {
                        let arrSelect = []
                        this.tableData.forEach(item => {
                            //debugger
                            const ressult = selected.find(obj => item.sId === obj.sTemplateId)
                            if (ressult) {
                                arrSelect.push(item)
                            }
                        })
                        if (this.$refs.mainTable) {
                            arrSelect.forEach(item => {
                                this.saveTemlate = false
                                this.$refs.mainTable.toggleRowSelection(item);
                            })
                        }
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
                this.loading = false;
            })
        },


        /**
         * 获取表格数据
         */
        async getData () {
            this.loading = true
            await Api.getTemplateData().then((res) => {
                this.loading = false
                if (res.success) {
                    this.tableData = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch((err) => {
                console.log(err)
                this.loading = false
            })
        },
    },
    async mounted () {
        await this.getData()
        this.getModuleTemlateSelected(this.iModuleId)

    },
    created () {

    }

};
</script>
<style lang="scss" scoped>

.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 15px;
    .c-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .modulesName {
            font-size: 14px;
            margin: 0;
            span {
                font-weight: bold;
            }
        }
        .pull-right {
            margin-right: 15px;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 10px 10px 0;
        overflow: auto;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;
            > button {
                margin-top: 13px;
            }
        }
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
    // .m-labelInput {
    //     width: 100%;
    //     margin-left: 0;
    // }
}
</style>
