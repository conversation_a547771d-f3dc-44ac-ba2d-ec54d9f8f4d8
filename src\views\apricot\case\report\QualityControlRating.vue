<template>
    <!-- <el-badge class="ml-2 m-badge" 
        :value="getName(qualityData.iQualityLevel) ?? '----'" 
        :hidden="['', null, undefined].includes(qualityData.iQualityLevel)">
        <el-button size="small" plain @click="onOpenDialog" type="default">{{ dialogParams.title }}</el-button>
    </el-badge> -->

    <el-button size="small"
        plain
        @click="onOpenDialog">
        {{ dialogParams.title }}
        <span v-if="!['', null, undefined].includes(qualityData.iQualityLevel)"
            style="color: #e6a23c;">{{`(${getName(qualityData.iQualityLevel)})`}}</span>
    </el-button>

    <el-dialog :title="dialogParams.title"
        v-model="isShowQualityControl"
        append-to-body
        class="t-default my-dialog"
        width="1000"
        :close-on-click-modal="false"
        :show-close="!isReportStepCall"
        @close="closeDialog">
        <!-- 评级 -->
        <div class="g-content">
            <div class="c-table">
                <el-table :data="templateItems"
                    ref="mainTable"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column show-overflow-tooltip
                        prop="sItemName"
                        label="评级项目"
                        align="center"
                        width="180px">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip
                        prop="sProp"
                        label="质量等级"
                        width="180px">
                        <template v-slot="{row, $index}">
                            <el-select v-model="row.iItemLevelGrade"
                                v-select-name="{ formData: row, fields: { sProp: 'iItemLevelGrade', sPropName: 'sItemLevelName' } }"
                                placeholder="请选择评分"
                                @change="onGradeChange">
                                <el-option v-for="item in JSON.parse(row.sItemLevelJson)"
                                    :key="item.iItemLevelGrade"
                                    :label="item.sItemLevelName + '--' + item.iItemLevelGrade + '分'"
                                    :value="item.iItemLevelGrade">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column show-overflow-tooltip
                        prop="sItemDesc"
                        label="评级项目说明"
                        min-width="120px">
                    </el-table-column>
                    <el-table-column show-overflow-tooltip
                        prop="sProp"
                        label="备注"
                        min-width="120px">
                        <template v-slot="{row, $index}">
                            <el-input v-model="row.sItemMemo"
                                clearable></el-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex">
                <div class="flex items-center mt-4"
                    style="width: 360px;">
                    <span style="width: 180px;text-align:center">质量等级：</span>
                    <el-select v-model="qualityData.iQualityLevel"
                        class="flex-1"
                        placeholder="请选择评分">
                        <el-option v-for="item in QualityLevelOptions"
                            :key="item.sValue"
                            :label="item.sName"
                            :value="Number(item.sValue)"></el-option>
                    </el-select>
                </div>
                <div class="flex-1 flex items-center mt-4 ml-10">
                    <span>质量总分：</span>
                    <span v-if="qualityData.iTotalGrade">{{ qualityData.iTotalGrade + '分' }}</span>
                </div>
            </div>
            <div class="flex items-center mt-4">
                <span style="width: 180px;text-align:center">质量备注：</span>
                <el-input v-model="qualityData.sQualityDesc"
                    type="textarea"
                    :rows="4"
                    class="flex-1"></el-input>
            </div>
            <div class="flex">
                <div class="flex items-center mt-4"
                    style="width: 360px;">
                    <span style="width: 180px;text-align:center">评级医生：</span>
                    <span v-if="qualityData.sCreator">{{ qualityData.sCreator }}</span>
                    <span v-else>{{ userInfo.sName }}</span>
                </div>
                <div class="flex-1 flex items-center mt-4 ml-10">
                    <span>评级时间：</span>
                    <span v-if="qualityData.dCreateDate">{{ toDateString(qualityData.dCreateDate) }}</span>
                </div>
            </div>
        </div>

        <template #footer>
            <el-button-icon-fa icon="fa fa-save"
                type="primary"
                @click="onSaveQualityReport">保存</el-button-icon-fa>
            <el-button-icon-fa icon="fa fa-close-1"
                v-if="!isReportStepCall"
                @click="isShowQualityControl = false">关闭</el-button-icon-fa>
        </template>

    </el-dialog>
</template>
<script>

// 枚举
import { caseEnum } from '$supersetResource/js/projects/apricot/enum.js'
// 接口
import { qualityReportFindByPatientId, qualityTemplateItemFindById, saveQualityReport } from '$supersetApi/projects/apricot/case/report.js'

export default {
    name: 'QualityControlRating',
    props: {
        dialogParams: {
            type: Object,
            default: () => ({ title: '评级', type: 1 })
        },
        reportInfo: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['onContinueSubmitStep'],
    data () {
        return {
            qualityData: {
                iQualityLevel: '',
                sQualityDesc: '',
                iTotalGrade: 0
            },
            iOriginQualityLevel: '',
            QualityLevelOptions: caseEnum.reportQuality,
            isShowQualityControl: false,
            isReportStepCall: false,
            templateItems: [],
            sPatientId: '',
            isSave: false
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    watch: {
        'reportInfo.sPatientId': {
            handler (val) {
                if (val && this.sPatientId !== val) {
                    this.qualityReportFindByPatientId(false, true);
                    this.sPatientId = val
                }
            },
            immediate: true
        }
    },
    methods: {
        toDateString (time) {
            return time ? moment(time).format('YYYY-MM-DD HH:mm') : '';
        },
        // 获取sName属性
        getName (val) {
            let item = this.QualityLevelOptions.find(item => item.sValue == val);
            return item ? item.sName : null;
        },
        // 打开弹窗
        async onOpenDialog () {
            this.qualityReportFindByPatientId();
            this.isShowQualityControl = true;
            this.isSave = false;
        },
        closeDialog() {
            if(!this.isSave) {
                this.qualityData.iQualityLevel = this.iOriginQualityLevel
            }
        },
        // 获取表格数据 isReportStepCall 是流程步骤调用
        async qualityReportFindByPatientId (isReportStepCall = false, isInit = false) {
            this.isReportStepCall = isReportStepCall;
            const { sPatientId } = this.reportInfo
            let params = {
                iReportType: this.dialogParams.type,
                sPatientId,
            }
            await qualityReportFindByPatientId(params).then(async (res) => {
                this.qualityData = {
                    iQualityLevel: '',
                    sQualityDesc: '',
                    iTotalGrade: 0
                };
                this.loading = false;
                if (!res.success) {
                    this.$message.error(res.msg)
                    return
                }
                const data = res.data || {};
                this.qualityData = data;
                this.iOriginQualityLevel = data.iQualityLevel;
                if (this.qualityData.sReportId) {
                    this.templateItems = JSON.parse(data.sTemplateJson);
                    const items = data.items;
                    items.length && this.templateItems.map(o => {
                        const temp = items.find(detail => detail.sItemId === o.sItemId);
                        if (temp) {
                            o.iItemLevelGrade = temp.iItemLevelGrade;
                            o.sItemLevelName = temp.sItemLevelName;
                            o.sItemMemo = temp.sItemMemo;
                        }
                    })
                    if (isInit) return;
                    this.isReportStepCall && this.$emit('onContinueSubmitStep', this.dialogParams.type, true, false);
                } else {
                    if (isInit) return;
                    isReportStepCall && (this.isShowQualityControl = true);
                    await this.qualityTemplateItemFindById(data.sTemplateId);
                }
            }).catch(() => {
                this.qualityData = {
                    iQualityLevel: '',
                    sQualityDesc: '',
                    iTotalGrade: 0
                };
            })
        },

        // 获取表格数据
        async qualityTemplateItemFindById (sTemplateId) {
            this.templateItems = [];
            await qualityTemplateItemFindById({ sTemplateId }).then((res) => {
                if (res.success) {
                    this.templateItems = res.data || [];
                    this.qualityData.sTemplateJson = JSON.stringify(this.templateItems)
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => { })
        },
        // 保存评级内容
        onSaveQualityReport () {
            const { sPatientId } = this.reportInfo;
            let params = {
                ...this.qualityData,
            };
            params.sQualityLevelText = this.getName(params.iQualityLevel);
            params.items = [];
            this.templateItems.map(item => {
                const { iItemLevelGrade, sItemId, sItemLevelJson, sItemLevelName, sItemMemo, sItemName } = item;
                const { sReportId } = this.qualityData;
                let temp = {
                    iItemLevelGrade,
                    sItemId,
                    sItemLevelJson,
                    sItemLevelName,
                    sItemMemo,
                    sItemName,
                    sPatientId,
                    sReportId,
                }
                iItemLevelGrade && params.items.push(temp)
            });
            if (this.isReportStepCall && !this.qualityData.iTotalGrade) {
                this.$message.warning('请录入评级信息！');
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '保存中，请稍等...',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            saveQualityReport(params).then(res => {
                loading.close();
                if (res.success) {
                    !this.isReportStepCall && this.$message.success(res.msg);
                    this.isReportStepCall && this.$emit('onContinueSubmitStep', this.dialogParams.type, true, true);
                    this.isShowQualityControl = false;
                    this.isSave = true
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                loading.close();
            })
        },
        // 计算分数
        onGradeChange () {
            this.qualityData.iTotalGrade = 0;
            this.templateItems.map(item => {
                const iItemLevelGrade = Number(item.iItemLevelGrade ?? 0);
                if (iItemLevelGrade > 0) {
                    this.qualityData.iTotalGrade += iItemLevelGrade;
                }
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.g-content {
    height: 100%;
    padding: 0px 10px 10px;
    display: flex;
    flex-direction: column;
    .c-table {
        height: 250px;
    }
}

.m-badge {
    :deep(.el-badge__content) {
        font-size: 10px;
        color: #e6a23c !important;
        background-color: #fff;
        border-color: #e6a23c;
        right: 23px !important;
        top: 2px !important;
        padding: 0px 4px;
    }
}
</style>
