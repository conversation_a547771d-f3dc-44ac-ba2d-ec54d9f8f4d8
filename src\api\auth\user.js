import request, { stringify, baseURL } from '$supersetUtils/request'
// let base = baseURL.system

export default {
  logout() {
    return request({
      url: baseURL.system + '/login/logout',
      method: 'post'
    })
  },
  login(data) {
    return request({
      url: baseURL.system + '/login/pcLogin',
      method: 'post',
      data
    })
  },
  add(data) {
    return request({
      url: baseURL.system + '/user/createUser',
      method: 'post',
      data
    })
  },
  del(params) {
    return request({
      url: baseURL.system + '/user/deleteUser',
      method: 'post',
      params
    })
  },
  update(data) {
    return request({
      url: baseURL.system + '/user/editUser',
      method: 'post',
      data
    })
  },
  getCurrentUser(params) {
    return request({
      url: baseURL.system + '/user/getCurrentUser',
      method: 'post',
      params
    })
  },
  pageList(data) {
    return request({
      url: baseURL.system + '/user/queryUserList',
      method: 'post',
      data
    })
  },
  resetPassword(params) {
    return request({
      url: baseURL.system + '/user/resetPassword',
      method: 'post',
      params
    })
  },
  changePassword(params) {
    return request({
      url: baseURL.system + '/user/changePassword',
      method: 'POST',
      data: stringify(params)
    })
  },  
  refreshToken(data){
    return request({
      url: baseURL.system + '/login/refreshToken',
      method: 'post',
      data
    })
  },
}
