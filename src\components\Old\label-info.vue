<template>
  <div class="m-labelInfo">
    <label>{{ label }}</label>
    <span :title="title" :class="[bold !== false ? 's-bold' : '']">
      <slot></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'label-info',
  data: function () {
    return {
      /*msg: ""*/
    }
  },
  props: {
    label: {
      type: String,
      default: 'label'
    },
    bold: {
      default: false
    },
    title: {
      type: String,
      default: ''
    }

  },
}
</script>
<style lang="scss" scoped>
.s-bold {
  font-weight: bold;
}
</style>
 