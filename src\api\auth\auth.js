import request, { stringify, baseURL } from '$supersetUtils/request'

export default {
  queryRightPage(data) {
    return request({
      url: baseURL.system + '/right/queryRightPage',
      method: 'POST',
      data
    })
  },
  queryRoleRightTree(params) {
    return request({
      url: baseURL.system + '/right/queryRoleRightTree',
      method: 'POST',
      params
    })
  },
  queryUserRightTree(params) {
    return request({
      url: baseURL.system + '/right/queryUserRightTree',
      method: 'POST',
      params
    })
  },
  

}
