export default {
  /**
   * 报告管理
   */
  // 搜索条件
  searchStyle: [{
    sProp: 'dAppointmentTimeSt',
    sLabel: '预约日期从',
    iLayourValue: 3,
    sInputType: 'date-picker',
    iCustom: 1
  },
  {
    sProp: 'dAppointmentTimeEd',
    sLabel: '到',
    iLayourValue: 3,
    sInputType: 'date-picker',
    iCustom: 1
  },
  {
    sProp: 'sRecentDays',
    sLabel: '最近天数',
    iLayourValue: 3,
    sInputType: 'option',
    iCustom: 1
  },
  {
    sProp: 'sSource',
    sLabel: '就诊类型',
    sOptionProp: 'sSourceOptions',
    sInputType: 'option',
    iLayourValue: 3,
  },
  {
    sProp: 'iIsImaging',
    sLabel: '收图状态',
    sOptionProp: 'iIsImagingOptions',
    sInputType: 'option',
    iLayourValue: 3,
    iCustom: 1,
  },
  {
    sProp: 'iIsPrint',
    sLabel: '打印状态',
    sOptionProp: 'iIsPrintOptions',
    sInputType: 'option',
    iLayourValue: 3,
    iCustom: 1,
  },
  {
    sProp: 'iIsReportCommit',
    sLabel: '报告提交',
    iCustom: 1,
    sOptionProp: 'iIsReportCommit',
    sInputType: 'option',
    iLayourValue: 3
  },
  {
    sProp: 'iIsApprove',
    sLabel: '报告审核',
    iCustom: 1,
    sOptionProp: 'iIsApprove',
    sInputType: 'option',
    iLayourValue: 3
  },
  {
    sProp: 'iIsFinalApprove',
    sLabel: '报告复审',
    iCustom: 1,
    sOptionProp: 'iIsFinalApprove',
    sInputType: 'option',
    iLayourValue: 3
  },
  {
    sProp: 'sName',
    sLabel: '姓名',
    sInputType: 'text',
  },
  {
    sProp: 'sNuclearNum',
    sLabel: '核医学号',
    sInputType: 'text',

  },
  {
    sProp: 'sMedicalRecordNO',
    sLabel: '病历号',
    sInputType: 'text',

  },
  {
    sProp: 'sApplyNO',
    sLabel: '申请单号',
    sInputType: 'text',

  },
  {
    sProp: 'sInHospitalNO',
    sLabel: '住院号',
    sInputType: 'text',

  },
  {
    sProp: 'sOutpatientNO',
    sLabel: '门诊号',
    sInputType: 'text',

  }, {
    sProp: 'sOrderNO',
    sLabel: '医嘱号',
    sInputType: 'text',

  }, { 
    prop: 'sImageNo', 
    label: '影像号',
    sInputType: 'text',
    isShow: false
  }, { 
    prop: 'sPhysicalExamNo', 
    label: '体检号', 
    sInputType: 'text',
    isShow: false
  }, { 
    prop: 'sVitisNo', 
    label: '就诊号',
    sInputType: 'text',
    isShow: false
  }, { 
    prop: 'sCardNum', 
    label: '社保卡号', 
    sInputType: 'text',
    isShow: false
  }, { 
    prop: 'sHealthCardNO', 
    label: '健康卡号',
    sInputType: 'text',
    isShow: false
    },
  // {
  //     sProp: 'value',
  //     sLabel: '可选择查询',
  //     iCustom: 1,
  //     iLayourValue: 6,
  // },  

    {
        sProp: 'sDistrictId',
        sLabel: '院区',
        sInputType: 'option',
        sOptionProp: 'districtArrOption',
        iLayourValue: 3,
        iCustom: 1,
    },
    {
        sProp: 'sMachineryRoomId',
        sLabel: '机房',
        sInputType: 'option',
        sOptionProp: "machineRoomArrOption",
        iLayourValue: 3,
        iCustom: 1,
    },
    {
        sProp: 'sProjectId',
        sLabel: '项目',
        sInputType: 'option',
        sOptionProp: "itemsArrOption",
        iLayourValue: 3,
        iCustom: 1,
    },
  {
    sProp: 'dInjectTime',
    sLabel: '注射日期',
    iCustom: 1,
    iLayourValue: 3
  },
  {
    sProp: 'iConsultation',
    sLabel: '会诊状态',
    iCustom: 1,
    sOptionProp: 'iConsultation',
    sInputType: 'option',
    iLayourValue: 3
  },

  ],
  searchForm: [{
    sProp: 'sPracticeName',
    sLabel: '书写医生',
    sInputType: 'text',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sReporterName',
    sLabel: '报告医生',
    sInputType: 'text',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sApproveName',
    sLabel: '审核医生',
    sInputType: 'text',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sFinalExamineName',
    sLabel: '复审医生',
    sInputType: 'option',
    sOptionProp: 'sFinalExamineNameOptions',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sPositionText',
    sLabel: '检查部位',
    sHeight: '30px',
    sInputType: 'text',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sNuclideText',
    sLabel: '核素',
    sHeight: '30px',
    sInputType: 'text',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sTracerText',
    sLabel: '药物',
    sHeight: '30px',
    sInputType: 'text',
    iCustom: 1,
    iLayourValue: 3
  },

  {
    sProp: 'sInpatientAreaText',
    sLabel: '病区',
    sInputType: 'option',
    sOptionProp: 'sInpatientAreaTextOptions',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sApplyDepartText',
    sLabel: '申请科室',
    sInputType: 'option',
    sOptionProp: 'sApplyDepartTextOptions',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sApplyPersonName',
    sLabel: '申请医生',
    sInputType: 'option',
    sOptionProp: 'sApplyPersonNameOptions',
    iLayourValue: 3,
    iCustom: 1,
  }, {
    sProp: 'sQualitative',
    sLabel: '阴阳性',
    sInputType: 'option',
    sOptionProp: 'qualitative',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'tags',
    sLabel: '收藏标签',
    sInputType: 'option',
    sOptionProp: 'collectTagOptions',
    iCustom: 1,
    iLayourValue: 3
  }, {
    sProp: 'sCollectionReason',
    sLabel: '收藏备注',
    sInputType: 'text',
    iLayourValue: 3
  }, {
    sProp: 'sMedicalHistory',
    sLabel: '简要病史',
    sInputType: 'text',
    iLayourValue: 6
  }, {
    sProp: 'sClinicalDiagnosis',
    sLabel: '临床诊断',
    sInputType: 'text',
    iLayourValue: 6
  }, {
    sProp: 'sInspectSeeText',
    sLabel: '检查所见',
    sInputType: 'text',
    iLayourValue: 6
  }, {
    sProp: 'sDiagnosticOpinionPureText',
    sLabel: '诊断意见',
    sInputType: 'text',
    iLayourValue: 6
  }],
  innerSearchItems: [
    {
      sProp: 'dAppointmentTimeSt',
      sLabel: '日期从',
      sInputType: 'date-picker',
      iCustom: 1,
      width: '50%',
    },
    {
      sProp: 'dAppointmentTimeEd',
      sLabel: '到',
      sInputType: 'date-picker',
      iCustom: 1,
      width: '50%',
    },
    {
      sProp: 'sRecentDays',
      sLabel: '最近天数',
      sInputType: 'option',
      iCustom: 1,
      width: '50%',
    },
    {
      sProp: 'sSource',
      sLabel: '就诊类型',
      sOptionProp: 'sSourceOptions',
      sInputType: 'option',
      width: '50%',
    },
    {
      sProp: 'sName',
      sLabel: '姓名',
      sInputType: 'text',
      width: '50%',
    },
    {
      sProp: 'sNuclearNum',
      sLabel: '核医学号',
      sInputType: 'text',
      width: '50%',

    },
    {
      sProp: 'sMedicalRecordNO',
      sLabel: '病历号',
      sInputType: 'text',
      width: '50%',

    },
    {
      sProp: 'sApplyNO',
      sLabel: '申请单号',
      sInputType: 'text',
      width: '50%',

    },
    {
      sProp: 'sInHospitalNO',
      sLabel: '住院号',
      sInputType: 'text',
      width: '50%',

    },
    {
      sProp: 'sOutpatientNO',
      sLabel: '门诊号',
      sInputType: 'text',
      width: '50%',

    }, {
      sProp: 'sOrderNO',
      sLabel: '医嘱号',
      sInputType: 'text',
      width: '50%',
    },

  ],
  // 患者 table 表
  patientTable: [
    // {
    //     sProp: 'iPriority',
    //     sLabel: '是否紧急',
    //     sAlign: 'center',
    //     sWidth: '90px'
    // },
    {
        sProp: 'tag',
        sLabel: '标签',
        sAlign: 'center',
        sWidth: '70px'
    },
    {
        sProp: 'sName',
        sLabel: '姓名',
        sWidth: '100px'
    },
    {
        sProp: 'sNameSpell',
        sLabel: '姓名拼音',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sSexText',
        sLabel: '性别',
        sAlign: 'center',
        sWidth: '70px'
    },
    {
        sProp: 'sAge',
        sLabel: '年龄',
        sAlign: 'center',
        sWidth: '70px'
    },
    {
        sProp: 'sEthnicGroupName',
        sLabel: '民族',
        sMinWidth: '80px',
        iIsHide: true
    },
    {
        sProp: 'sLockUserName',
        sLabel: '锁定',
        sWidth: '110px'
    },
    {
        sProp: 'iConsultation',
        sLabel: '会诊状态',
        sWidth: '80px'
    },
    {
        sProp: 'iIsRegister',
        sLabel: '签到',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iIsImaging',
        sLabel: '收图',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iInstanceCount',
        sLabel: '图像数量',
        sAlign: 'center',
        sWidth: '80px'
    },
    {
        sProp: 'iIsReport',
        sLabel: '报告',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iIsReportCommit',
        sLabel: '提交',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iIsApprove',
        sLabel: '审核',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iIsFinalApprove',
        sLabel: '复审',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iIsPrintText',
        sLabel: '打印',
        sAlign: 'center',
        sWidth: '60px'
    },
    {
        sProp: 'iIsPrintImg',
        sLabel: '图像打印',
        sAlign: 'center',
        sWidth: '80px'
    },
    {
        sProp: 'sReporterName',
        sLabel: '报告医生',
        sWidth: '110px'
    },
    {
        sProp: 'sExamineName',
        sLabel: '审核医生',
        sWidth: '110px'
    },
    {
        sProp: 'sFinalExamineName',
        sLabel: '复审医生',
        sWidth: '110px'
    },
    {
        sProp: 'sPracticeName',
        sLabel: '书写医生',
        sWidth: '110px'
    },
    {
        sProp: 'sApplyDepartText',
        sLabel: '申请科室',
        sWidth: '110px'
    },
    {
        sProp: 'sRoomText',
        sLabel: '设备类型',
        sWidth: '110px'
    },
    {
        sProp: 'sNuclearNum',
        sLabel: '核医学号',
        sWidth: '110px'
    },
    {
        sProp: 'sProjectName',
        sLabel: '检查项目',
        sWidth: '110px',
    },
    {
        sProp: 'dAppointmentTime',
        sLabel: '预约日期',
        sWidth: '150px',
        isSortable: true,
        customSort: 'custom',
        sOrder: 'descending'
    },
    {
        sProp: 'dInjectTime',
        sLabel: '注射日期',
        sWidth: '150px'
    },
    {
        sProp: 'dFactCheckTime',
        sLabel: '上机时间',
        sWidth: '150px'
    },
    {
        sProp: 'dReportTime',
        sLabel: '提交时间',
        sWidth: '150px'
    },
    {
        sProp: 'dExamineDate',
        sLabel: '审核时间',
        sWidth: '150px'
    },
    {
        sProp: 'dFinalExamineDate',
        sLabel: '复审时间',
        sWidth: '150px'
    },
    {
        sProp: 'sQualitativeText',
        sLabel: '阴阳性',
        sWidth: '110px'
    },
    {
        sProp: 'sOutpatientNO',
        sLabel: '门诊号',
        sWidth: '110px'
    },
    {
        sProp: 'sInHospitalNO',
        sLabel: '住院号',
        sWidth: '110px'
    },
    {
        sProp: 'sMedicalRecordNO',
        sLabel: '病历号',
        sWidth: '110px'
    },
    {
        sProp: 'sApplyNO',
        sLabel: '申请单号',
        sWidth: '110px'
    },
    {
        sProp: 'sOrderNO',
        sLabel: '医嘱号',
        sWidth: '110px'
    },
    {
        sProp: 'sClinicalDiagnosis',
        sLabel: '临床诊断',
        sWidth: '150px'
    },
    {
        sProp: 'sCollectionTag',
        sLabel: '收藏标签',
        sWidth: '150px'
    },
    {
        sProp: 'sCollectionReason',
        sLabel: '收藏备注',
        sWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'fHeight',
        sLabel: '身高（cm）',
        sAlign: 'center',
        sMinWidth: '100px',
        iIsHide: true
    },
    {
        sProp: 'fWeight',
        sLabel: '体重（kg）',
        sAlign: 'center',
        sMinWidth: '100px',
        iIsHide: true
    },
    {
        sProp: 'sIdNum',
        sLabel: '身份证号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'dBirthday',
        sLabel: '出生日期',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sDistrictName',
        sLabel: '院区',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sMachineryRoomText',
        sLabel: '机房',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sMachineStationName',
        sLabel: '工作站',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sVitisNo',
        sLabel: '就诊号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sInspectSeeText',
        sLabel: '检查所见',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sDiagnosticOpinionPureText',
        sLabel: '诊断意见',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sMedicalHistory',
        sLabel: '简要病史',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sSourceText',
        sLabel: '就诊类型',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sEncounter',
        sLabel: '就诊次数',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sPositionText',
        sLabel: '检查部位',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sTestModeText',
        sLabel: '检查方式',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sNuclideText',
        sLabel: '核素',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sNuclideSupName',
        sLabel: '核素全称',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sTracerText',
        sLabel: '示踪剂',
        sMinWidth: '100px',
        iIsHide: true
    },
    {
        sProp: 'sTracerSupName',
        sLabel: '示踪剂全称',
        sMinWidth: '120px',
        iIsHide: true
    },
    {
        sProp: 'fRecipeDose',
        sLabel: '处方剂量',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'iIsPregnant',
        sLabel: '怀孕',
        sAlign: 'center',
        sMinWidth: '80px',
        iIsHide: true
    },
    {
        sProp: 'sPhone',
        sLabel: '联系电话',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sAddress',
        sLabel: '住址',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sMaritalStatusName',
        sLabel: '婚姻状况',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sMedicalCaseNO',
        sLabel: '病案号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sImageNo',
        sLabel: '影像号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sInpatientAreaText',
        sLabel: '病区名称',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sInpatientWardText',
        sLabel: '病房名称',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sBedNum',
        sLabel: '床号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sVisitCard',
        sLabel: '就诊卡号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sCardNum',
        sLabel: '社保卡号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sHealthCardNO',
        sLabel: '健康卡号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'fBloodSugar',
        sLabel: '检查空腹血糖',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sPresentHistory',
        sLabel: '现病史',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sPastHistory',
        sLabel: '既往史疾病',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sCheckIntent',
        sLabel: '检查目的',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sClinicalSymptoms',
        sLabel: '临床症状',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sChiefComplaint',
        sLabel: '主诉',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sInvoiceNum',
        sLabel: '发票号',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sFeeTypeText',
        sLabel: '费用类型',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'fFees',
        sLabel: '费用',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sChargeStateText',
        sLabel: '收费状态',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'dApplyDate',
        sLabel: '申请时间',
        sMinWidth: '150px',
        iIsHide: true
    },
    {
        sProp: 'sApplyPersonName',
        sLabel: '申请医生',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sChiefPhysicianName',
        sLabel: '主治医生',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sChiefPhysicianPhone',
        sLabel: '医生电话',
        sMinWidth: '110px',
        iIsHide: true
    },
    {
        sProp: 'sPhysicalExamNo',
        sLabel: '体检号',
        sMinWidth: '110px',
        iIsHide: true
    }
],

  innerTableColorConfigs: [
    {
      prop: 'iReportStatus',
      label: '已书写',
      value: '12',
      color: '#fff'
    },
    {
      prop: 'iReportStatus',
      label: '未书写',
      value: '11',
      color: '#fff'
    },
    {
      prop: 'iIsApprove',
      label: '已审核',
      value: '22',
      color: '#fff'
    },
    {
      prop: 'iIsApprove',
      label: '未审核',
      value: '21',
      color: '#fff'
    },
  ]
}
