<template>
    <div class="container">
        <DragAdjustFlex v-model="dragA1">
            <template v-slot:c1>
                <div class="container-left">
                    <div class="item-01">
                        <!-- 左侧院区、设备、日历 -->
                        <CalendarView v-model="modelParams" @getHospitalData="getHospitalData">
                        </CalendarView>
                    </div>
                    <div class="item-02">
                        <!-- 下方统计 -->
                        <Statistics v-model="modelParams"></Statistics>
                    </div>
                </div>
            </template>
            <template v-slot:c2>
                <div slot="c2" class="container-right">
                    <DragAdjustFlex v-model="dragA2">
                        <template v-slot:c1>
                            <!-- 申请单来源 -->
                            <ApplicationForm @selectDataItem="selectApplicationPatient" ref="refApplicationForm"></ApplicationForm>
                        </template>
                        <template v-slot:c2>
                            <!-- 预约 -->
                            <ReservationModules v-model="modelParams" @onReservationInfo="onReservationInfo" ref="refReservationModules"></ReservationModules>
                        </template>
                    </DragAdjustFlex>
                </div>
            </template>
        </DragAdjustFlex>
        <!-- 预约弹窗 -->
        <DialogAppointment
            v-model="dialogVisibleAppointment"
            :iModuleId="iModuleId"
            :modelParams="modelParams"
            :districtData="districtData"
            :appointInfo="appointInfo"
            @emitRfresh="appointmentRefresh"
        ></DialogAppointment>
    </div>
</template>

<script setup>
    import ApplicationForm from './ApplicationForm.vue'
    import CalendarView from './CalendarView.vue'
    import Statistics from './Statistics.vue'
    import ReservationModules  from './ReservationModules.vue'
    import DialogAppointment from './DialogAppointment.vue'

    import { deepClone } from '$supersetUtils/function'

    import ConfigsItems from './configs/configsItems.js'
    import { useUserConfigRequest } from './useUserConfig.js'

    const dragA1 = reactive(ConfigsItems.dragA1)
    const dragA2 = reactive(ConfigsItems.dragA2)

    // 院区、机房，设备类型参数互传
    const modelParams = reactive({
        hospitalId: '',
        roomId: '',
        date: null,
        roomName: '',
        sDeviceTypeId: '', // 设备类型
        refresh: false     // 控制刷新
    })
    const iModuleId = ref(2)
    useUserConfigRequest('ReservationModulesPenalConfig', iModuleId.value, ConfigsItems.panelConfig)
    // 预约弹窗
    const dialogVisibleAppointment = ref(false)
    
    // 选择申请单患者
    const appointInfo = ref({
        applyItem: {},   // 存储申请单患者信息
        selectItem: {},  // 存储选择的患者信息
        sPatientId: null
    })

    // 选中申请单
    const selectApplicationPatient = (item) => {
        appointInfo.value.applyItem = deepClone(item)
        appointInfo.value.selectItem = {}
        appointInfo.value.sPatientId = ''

        if (item.iAppointState === 1) {
            appointInfo.value.sPatientId = item.sPatientInfoId
        }

        dialogVisibleAppointment.value = true
    }

    // 点击日历
    const onReservationInfo = (item) => {

        appointInfo.value.applyItem = {}
        appointInfo.value.selectItem = deepClone(item)
        appointInfo.value.sPatientId = ''
        if (item.sPatientId) {
            appointInfo.value.sPatientId = item.sPatientId
        }
        dialogVisibleAppointment.value = true
    }

    // 院区、机房
    const districtData = ref([])
    function getHospitalData(data) {
        districtData.value = data
    }

    // 刷新申请单
    const refApplicationForm = ref(null)
    const refReservationModules = ref(null)

    // 预约单操作后，刷新处理
    const appointmentRefresh = (type) => {
        if (type === 'numSource') {
            modelParams.refresh = !modelParams.refresh

            // 已预查询，弹窗已打开
            if (refReservationModules.value.refPatientQuery && refReservationModules.value.refPatientQuery.visible) {
                refReservationModules.value.refPatientQuery.mxGetTableList()
            }

        }else if (type === 'applyForm') {
            refApplicationForm.value.doRefreshQueryApply(appointInfo.value.sPatientId)
        }
    }
</script>

<script>
    // 缓存用
    export default {
        name: "apricot_Appointment1"
    }
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    height: 100%;
    .container-left {
        display: flex;
        flex-direction: column;
        height: 100%;
        .item-01 {
            min-height: 432px;
            background: white;
            margin-bottom: 10px;
        }
        .item-02 {
            background: white;
            flex: 1;
            overflow: hidden;
        }
    }
    .container-right {
        display: flex;
        height: 100%;
        width: 100%;
    }
}
</style>
