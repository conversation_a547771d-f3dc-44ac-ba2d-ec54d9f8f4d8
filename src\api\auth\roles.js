import request, { stringify, baseURL } from '$supersetUtils/request'
// let base = baseURL.system

export default {
  pageList(data) {
    return request({
      url: baseURL.system + '/role/queryRoleList',
      method: 'POST',
      data
    })
  },
  get(data) {
    return request({
      url: baseURL.system + '/role/get',
      method: 'POST',
      data
    })
  },
  add(data) {
    return request({
      url: baseURL.system + '/role/createRole',
      method: 'POST',
      data
    })
  },
  del(params) {
    return request({
      url: baseURL.system + '/role/deleteRole',
      method: 'POST',
      params
    })
  },
  update(data) {
    return request({
      url: baseURL.system + '/role/editRole',
      method: 'POST',
      data
    })
  },
  getRoleById(params) {
    return request({
      url: baseURL.system + '/role/getRoleById',
      method: 'POST',
      params
    })
  },
  
  assignRight(data) {
    return request({
      url: baseURL.system + '/role/assignRight',
      method: 'POST',
      data
    })
  },
}
