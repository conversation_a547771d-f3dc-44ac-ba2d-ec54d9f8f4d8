<template>
    <el-dialog v-model="dialogFormVisible" title="预约登记管理设置" draggable class="t-default"
        width="600px">
        <el-form :model="form" label-width="200px">
            <el-form-item label="自动选择首个申请单：">
                <el-radio-group v-model="form.isLoadApplyFirst">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="自动查询申请单：">
                <!-- 控件触发查询申请单 -->
                <el-radio-group v-model="form.isAutomaticLoadApply">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="预约窗弹出历史患者：">
                <el-radio-group v-model="form.isShowHistoryPatient">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="保存后自动签到：">
                <el-radio-group v-model="form.isCallSignIn">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="保存后自动打印袋贴：">
                <el-radio-group v-model="form.isSaveAutoPrintPocket">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="保存后关闭预约单：">
                <el-radio-group v-model="form.isSaveAndCloseAppointment">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item label="申请单查询时间范围：">
                <el-select v-model="form.applyTimeDays" placeholder="" style="width:200px;">
                    <el-option label="无" :value="0"></el-option>
                    <el-option v-for="index in 60" :key="index" :label="`${index}天`" :value="index"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="统计天数：">
                <el-select v-model="form.statisticsDays" placeholder="" style="width:200px;">
                    <el-option v-for="item in [1, 7]" :key="item" :label="`${item}天`" :value="item"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="号源卡片背景色：">
                <el-color-picker
                    small="small"
                    v-model="form.appointCardBgColor"
                    show-alpha
                    :predefine="predefineColors"
                    @active-change="changesColors">
                </el-color-picker>
                <div style="width: 70px;height: 30px; margin-left:20px" :style="{background: form.appointCardBgColor}"></div>
            </el-form-item>
            <el-form-item label="药物选择方式：">
                 <el-radio-group v-model="form.medicineChooseType">
                    <el-radio :label="1">关联选择</el-radio>
                    <el-radio :label="2">随机选择</el-radio>
                </el-radio-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button-icon-fa type="primary" icon="fa fa-save" plain @click="onSaveClick(1)">保存至全局</el-button-icon-fa>
                <el-button-icon-fa type="primary" icon="fa fa-save" :disabled="loading" @click="onSaveClick(0)">保存</el-button-icon-fa>
                <el-button-icon-fa
                    icon="fa fa-close-1"
                    @click="dialogFormVisible = false">关闭</el-button-icon-fa>
            </span>
        </template>
    </el-dialog>
</template>


<script>
import { deepClone } from "$supersetUtils/function"
import useUserConfig, { useUserConfigSave } from '../useUserConfig.js'

export default {
    props: ['configKey', 'modelValue'],
    data () {
        return {
            form: {},
            iModuleId: null,
            userInfo: {},
            loading: false,
            predefineColors:[ 
                '#d9eae8',
                '#d4e8e6',
                '#efefda',
                '#e8e8ec',
                '#f3fae8',
                'rgba(229, 247, 231, 1)',
                'rgba(228, 237, 230, 1)',
                'rgba(238, 242, 252, 1)',
                'rgba(238, 239, 251, 1)',
                '#EAEAEF',
                'rgba(242, 244, 238, 1)',
                'rgba(227, 233, 228, 1)',
                'rgba(228, 235, 227, 1)',
                'rgba(252, 244, 234, 1)',
                'rgba(252, 240, 236, 1)',
                '#d8e5f5',
                'rgba(227, 231, 238, 1)',
                '#dfedff'
            ],
        }
    },
    computed: {
        dialogFormVisible: {
            get: function() {
                return this.modelValue
            },
            set: function(val) {
                this.$emit('update:modelValue', val)
            }
        }
    },
    watch: {
        dialogFormVisible (val) {
            if (val) {
                const { configData, configModuleId } = useUserConfig();
                this.iModuleId = configModuleId.value
                this.form = deepClone(configData.value)
                this.userInfo = this.$store.getters['user/userSystemInfo']
            }
        }
    },
    methods: {
        async onSaveClick (configType) {
            let params = {
                configKey: this.configKey,
                configValue: JSON.stringify(this.form),
                configType: configType,
                moduleId: this.iModuleId,
                userNo: this.userInfo.sNo
            }
            this.loading = true;
            await useUserConfigSave(params)

            this.loading = false;
            this.dialogFormVisible = false;
        },
        changesColors(val) {
            this.form.appointCardBgColor = val
        }
    }
}
</script>

<style lang="scss" scoped>
</style>
