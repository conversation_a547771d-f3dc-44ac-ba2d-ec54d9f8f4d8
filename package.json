{"name": "apt-taxus", "version": "1.0.0", "author": {"name": ""}, "scripts": {"lint": "eslint \"src/**/*.{js,vue}\"", "serve": "cross-env NODE_ENV=development vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max_old_space_size=4096 vite build", "preview": "cross-env vite preview", "build:preview": "vite build --mode production && vite preview", "dev:mock": "cross-env USE_MOCK=true vite", "build:mock": "cross-env USE_CHUNK_MOCK=true vite build && vite preview", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged", "deploy-gh": "GH=1 bash deploy-gh.sh"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "axios": "^0.21.1", "clipboard": "^2.0.6", "core-js": "^2.6.11", "echarts": "^5.4.1", "element-plus": "^2.3.7", "file-saver": "^2.0.5", "jquery": "^3.4.1", "js-cookie": "^3.0.1", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "qs": "^6.11.1", "screenfull": "^5.1.0", "sortablejs": "^1.15.0", "tiff.js": "^1.0.0", "v-contextmenu": "^3.0.0", "vite-plugin-resolve-extension-vue": "^0.1.0", "vue": "^3.2.33", "vue-router": "^4.0.15", "vuedraggable": "^4.1.0", "vuex": "^4.0.2", "xlsx": "^0.17.4", "xlsx-style-vite": "0.0.2"}, "devDependencies": {"@types/node": "^16.7.1", "@vitejs/plugin-legacy": "^1.5.3", "@vitejs/plugin-vue": "^1.9.2", "@vue/compiler-sfc": "^3.2.11", "@vue/eslint-config-prettier": "^6.0.0", "babel-eslint": "^10.1.0", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-vue": "^7.15.0", "lint-staged": "^11.1.1", "prettier": "^2.3.2", "sass": "^1.37.0", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.2", "stylelint-config-recess-order": "^2.4.0", "stylelint-config-standard": "^22.0.0", "svg-sprite-loader": "^6.0.9", "terser": "^5.18.0", "unplugin-auto-import": "^0.7.2", "unplugin-icons": "^0.14.3", "unplugin-vue-components": "^0.19.5", "vite": "^4.3.9", "vite-plugin-babel-import": "^2.0.5", "vite-plugin-element-plus": "^0.0.12", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^2.9.4", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-style-import": "^1.2.1", "vite-plugin-windicss": "^1.9.0", "windicss": "^3.5.6"}, "engines": {"node": "^12 || >=14"}, "vite": {}}