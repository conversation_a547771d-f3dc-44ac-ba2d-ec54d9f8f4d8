<template>
	<div class="m-flexLaout-ty m-collapse">
		<div class="g-flexChild m-flexLaout-tx">
			<h5 :class="isCollapse?'h5-collapseHead-v':'h5-collapseHead-h'" v-text="headline"></h5>
			<div class="g-flexChild c-auxiliaryArea" v-if="isCollapse">
				<slot name="content"></slot>
			</div>
		</div>

		<div class="m-collapse-foot">
			<i class="fa" :class="isCollapse?'fa-angle-up':'fa-angle-down'" @click="clickCollapse"></i>
		</div>
	</div>
</template>

<script>
	export default {
		name: 'Collapse',
		data() {
			return {
				isCollapse: false,
			}
		},
		props: {
			headline:{
				type:String,
				default:'标题'
			}
		},
		computed: {

		},
		watch: {
//			isCollapse() {
//				this.isCollapse = isCollapse;
//			}
		},
		methods: {
			clickCollapse() {
				this.isCollapse = !this.isCollapse;
				console.log(this.isCollapse, '-----this.isCollapse')
				localStorage.setItem('isCollapse', this.isCollapse);
			}
		},
		created(){
			this.isCollapse = JSON.parse(localStorage.isCollapse || false);
			console.log(this.isCollapse,'this.isCollapse');
		}
	}
</script>

<style lang="scss">
	.h5-collapseHead-h {
		width: 100%;
		padding: 5px;
		margin: 0;
		font-weight: bold;
		background-color: #F4F8FB;
		border-bottom: 1px solid #cdcecf;
	}
	
	.h5-collapseHead-v {
		width: 30px;
		padding: 0 8px;
		margin: 0;
		font-weight: bold;
		background-color: #F4F8FB;
		word-wrap: break-word;
		writing-mode: vertical-rl;
		/*从左向右 从右向左是 writing-mode: vertical-rl;*/
		writing-mode: tb-rl;
		/*IE浏览器的从左向右 从右向左是 writing-mode: tb-rl；*/
		text-align: center;
	}
	
	.m-collapse-foot {
		text-align: center;
		i {
			cursor: pointer;
		}
	}
</style>