import store from '@/store/index.js'
export default {
  name: 'auth',
  mounted(el, { value, modifiers }) {
    // 后端返回的按钮权限标识
    const buttonRight = store.getters['user/buttonRight']
    // 是否有权限判断
    let isAuth = false
    // 字符串
    if (typeof value === 'string' || typeof value === 'number') {
      isAuth = buttonRight.includes(value)
    } else if (Array.isArray(value)) {
      if (modifiers.every) {
        // 多个必须包含
        isAuth = value.every(item => buttonRight.includes(item))
      } else {
        // 数组只要包含一个
        isAuth = value.some(item => buttonRight.includes(item))
      }
    } else {
      return
    }
    // 没有权限
    if (!isAuth) {
      // v-auth.hide='xx' 直接移除
      // if (modifiers.hide) {
        // console.log(el)
        // debugger
        // el.parentNode.removeChild(el)
        el.remove()
        return
      // }
      // 正常复制一个不带事件的相同元素
      // const cloneNode = el.cloneNode(true)
      // cloneNode.disabled = true
      // cloneNode.classList.add('is-disabled')
      // cloneNode.setAttribute('title', '无访问权限')
      // el.parentNode.replaceChild(cloneNode, el)
    }
  },
  // unmounted(el) {

  // }
}
