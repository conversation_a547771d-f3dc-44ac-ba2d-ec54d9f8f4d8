<template>
    <div class="m-flexLaout-ty" style="overflow: auto;">
        <div class="g-flexChild m-flexLaout-ty" style="position: relative;">
            <div class="c-item t-3">
                <div class="i-form">
                    <el-form ref="refEditLayer" :rules="rules" :model="formData" label-position="right" :show-message="false"
                        style="overflow:hidden;">
                        <FormList ref="formList" :list="machineInfoFormConfig" :formData="formData" :optionData="optionsLoc"
                            :configBtn="isShowConfigBtn" :iModuleId="iModuleId" storageKey="MachineMngInfoForm">
                            <template v-slot:sStain="{ style }">
                                <el-select v-model="formData.sStain" placeholder=" " filterable allow-create
                                    default-first-option clearable :style="style">
                                    <el-option v-for="(item, index) in optionsLoc.ApricotReportStain" :key="index"
                                        :label="item.sName" :value="item.sName">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-slot:dOperateEnd="{ style }">
                                <el-date-picker class="c-date-picker" v-model="formData.dOperateEnd" type="datetime"
                                    format="YYYY-MM-DD HH:mm" :style="style">
                                </el-date-picker>
                            </template>
                            <template v-slot:sTargetSiteCode="{ style }">
                                <el-select v-model="formData.sTargetSiteCode" placeholder=" " clearable :style="style" @change="onChangeTargetSite">
                                    <el-option v-for="(item, index) in optionsLoc.ApricotReportTargetSite"
                                        :key="index"
                                        :label="item.sName"
                                        :value="item.sValue">
                                    </el-option>
                                </el-select>
                            </template>
                            <!-- 饮水量 -->
                            <template v-slot:iIntake="{ style }">
                                <el-autocomplete
                                    v-model="formData.iIntake"
                                    :fetch-suggestions="() => optionsLoc.waterOptions"
                                    :select-when-unmatched="true"
                                    clearable
                                    :style="style">
                                    <template #suffix>ml</template>
                                </el-autocomplete>
                            </template>
                            <!-- 采集速度 -->
                            <template v-slot:fPickingRate="{ style }">
                                <el-autocomplete
                                    v-model="formData.fPickingRate"
                                    :fetch-suggestions="() => optionsLoc.RateOptions"
                                    :select-when-unmatched="true"
                                    clearable
                                    :style="style"
                                    @select="(item)=>onChangePickingRate(item.value)"
                                    @change="onChangePickingRate">
                                    <template #suffix>分钟/床</template>
                                </el-autocomplete>
                            </template>
                            <!-- 采集数量 -->
                            <template v-slot:iNumber="{ style }">
                                <el-autocomplete
                                    v-model="formData.iNumber"
                                    :fetch-suggestions="() => optionsLoc.NumberOptions"
                                    :select-when-unmatched="true"
                                    clearable
                                    :style="style">
                                    <template #suffix>位</template>
                                </el-autocomplete>
                            </template>
                        </FormList>
                    </el-form>
                </div>

                <Teleport v-if="isTeleportTrue && $auth['report:machine:markDelay']" to=".my-machine-delay">
                    <el-popover v-model:visible="isShowDelayPopover" placement="top" width="450" trigger="click">
                        <template #reference>
                            <el-button-icon-fa
                                type="primary" plain
                                _icon="fa fa-paperclip"
                                @click="openDelayPop"
                                style="margin-right: 10px;">标记延迟</el-button-icon-fa>
                        </template>
                        <div class="delay-scan">
                            <div class="title">
                                <h3>延迟扫描</h3>
                                <div class="set-up">
                                    <div v-if="!showSetDalayTime">延迟：{{ DalayScanTime }}min</div>
                                    <el-input v-show="showSetDalayTime" v-model="DalayScanTime">
                                        <template #append>
                                            <span style="padding: 0 10px">min</span>
                                        </template>
                                    </el-input>
                                </div>
                                <div>
                                    <el-button-icon-fa type="primary" plain size="small" circle
                                        :icon="showSetDalayTime ? 'el-icon-check' : 'el-icon-edit'"
                                        @click="showDalayTime(row)"></el-button-icon-fa>
                                </div>
                            </div>
                            <p style="display:flex;align-items: center;">
                                <span style="display: inline-block">部位：</span>
                                <el-select v-model="delayData.sDelayBody" filterable allow-create default-first-option
                                    multiple clearable placeholder=" " :teleported="false" @change="onDelayBodyChange" style="flex: 1">
                                    <el-option v-for="(item, index) in optionsLoc.ApricotReportDelayBody" :key="index"
                                        :label="item.sName" :value="item.sName">
                                    </el-option>
                                </el-select>
                            </p>
                            <p style="display:flex; align-items: center">
                                <span style="display: inline-block">时间：</span>
                                <el-date-picker v-model="delayData.dDelayTime" type="datetime" format="YYYY-MM-DD HH:mm"
                                    :teleported="false" style="flex: 1">
                                </el-date-picker>
                            </p>
                            <div style="text-align:right; margin-top: 30px;">
                                <el-button-icon-fa class="pull-left" icon="el-icon-refresh-left"
                                    @click="handleCancelHoldOnClick">取消延迟</el-button-icon-fa>
                                <el-button-icon-fa type="primary" icon="fa fa-save"
                                    @click="handleHoldOnClick">保存</el-button-icon-fa>
                                <el-button-icon-fa icon="fa fa-close-1"
                                    @click="isShowDelayPopover=false">关闭</el-button-icon-fa>
                            </div>
                        </div>
                    </el-popover>
                </Teleport>

                <Teleport v-if="isTeleportTrue && $auth['report:machine:allowLeave']" to=".my-machine-leave">
                    <el-button-icon-fa type="primary" plain 
                        _icon="fa fa-paperclip" @click="onAllowLeave"
                        style="margin-right: 10px;">允许离开</el-button-icon-fa>
                </Teleport>

                <Teleport v-if="isTeleportTrue && $auth['report:machine:save']" to=".my-machine-save">
                    <el-button-icon-fa type="primary" icon="fa fa-save" v-loading="saveLoading" @click="onSaveData">保存</el-button-icon-fa>
                </Teleport>
            </div>
        </div>
    </div>
</template>

<script>

import { deepClone } from '$supersetUtils/function'

import Configs from "../config"
import { queryUserListByType } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
import Api from '$supersetApi/projects/apricot/case/machine.js'
import { getMachineRoomData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
export default {
    props: {
        patientInfo: {
            type: Object,
            default: () => ({})
        },
        iModuleId: {
            type: [String, Number],
            default: ''
        },
        formValueConfig: {
            type: Object,
            default: () => ({})
        },
        // isSaveAndAllowLeave: {
        //     // 配置：是否点击保存触发允许离开
        //     type: Boolean,
        //     default: false
        // },
        // isShowConfigBtn: {
        //     type: Boolean,
        //     default: true
        // },
        configValue: {
            type: Object,
            default: () => ({})
        },
    },
    emits: ['onChangeActiveTab'],
    inject: {
        updateTableInfo: {
            from: 'updateTableInfo',
        },
    },
    data () {
        return {
            machineInfoFormConfig: [...Configs.machineInfoFormConfig],
            //采集数据表格
            collectionTable: [...Configs.collectionTable],
            optionsLoc: {
                // 显像类型
                ApricotReportImagingType: this.$store.getters['dict/map'].ApricotReportImagingType || [],
                // 采集范围
                ApricotReportTargetSite: this.$store.getters['dict/map'].ApricotReportTargetSite || [],
                // 污染情况
                ApricotReportStain: this.$store.getters['dict/map'].ApricotReportStain || [],
                // 饮水类型
                ApricotReportDrinkType: this.$store.getters['dict/map'].ApricotReportDrinkType || [],
                // 延迟部位
                ApricotReportDelayBody: this.$store.getters['dict/map'].ApricotReportDelayBody || [],
                sMachineryRoomOptions: [],
                DoctorOptions: [],
                waterOptions: [
                    {value: '100'},
                    {value: '200'},
                    {value: '300'},
                    {value: '400'},
                    {value: '500'},
                    {value: '600'},
                    {value: '700'},
                    {value: '800'},
                    {value: '900'},
                    {value: '1000'},
                ],
                NumberOptions: [
                    { value: '10'},
                    { value: '20'},
                    { value: '30'},
                    { value: '40'},
                    { value: '50'},
                    { value: '60'},
                    { value: '70'},
                    { value: '80'},
                    { value: '90'},
                    { value: '100'},
                ],
                RateOptions: [],
            },
            pickerOptions: {
                disabledDate: time => {
                    return time.getTime() > new Date().getTime()
                }
            },
            formData: {},
            defaultData: {
                sImgTypeCode: undefined,
                sTargetSiteCode: undefined,
                sStain: undefined,
                sMachineryRoomId: undefined,
                dOperateEnd: undefined,
                iIntake: undefined,
                sDrinkTypeCode: undefined,
                fPickingRate: undefined,
                iNumber: undefined,
                iCountingRate: undefined,
                sOperatorId: undefined,
                sMemo: undefined
            },
            rules: {},
            tableData: [],
            sPatientId: null,
            isTeleportTrue: false,
            delayData: {
                sDelayBody: [],
                dDelayTime: undefined
            },
            isShowDelayPopover: false,
            isLoadingDoctors: false,
            showSetDalayTime: false,
            DalayScanTime: localStorage.getItem('DalayScanTime') ? localStorage.getItem('DalayScanTime') : 50,// 设置延迟时间
            saveLoading: false
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
        workStation () {
            let temp = this.$store.getters['user/workStation'];
            return temp
        },
        // 配置：是否点击保存触发允许离开
        isSaveAndAllowLeave() {
            return this.configValue.isSaveAndAllowLeave || false;
        },
        isShowConfigBtn() {
            let isShowConfigBtn = true;
            isShowConfigBtn = this.configValue.isShowConfigBtn;
            return isShowConfigBtn;
        },
        // fPickingRate() {
        //     return this.configValue.fPickingRate;
        // }
    },
    watch: {
        patientInfo: {
            async handler (val, oldVal) {
                if (val && oldVal && val.sId === oldVal.sId) return
                this.isShowDelayPopover = false;
                if (val.sId) {
                    await this.getMachineRoomData();
                    await this.getDoctorsData();
                    this.initForm();
                    this.delayData.sDelayBody = val.sDelayBody ? val.sDelayBody.split(',') : val.sDelayBody;
                    this.delayData.dDelayTime = val?.dDelayTime;
                    return
                }
                this.delayData.sDelayBody = [];
                this.delayData.dDelayTime = undefined;
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].resetFields()
            },
            immediate: true
        },
    },
    methods: {
        onChangePickingRate(value) {
            let oCacheSomeMachineInfo = this.getCache();
            oCacheSomeMachineInfo.fPickingRate = value;
            this.setCache(oCacheSomeMachineInfo);
        },
        onChangeTargetSite(value) {
            let oCacheSomeMachineInfo = this.getCache();
            oCacheSomeMachineInfo.sTargetSiteCode = value;
            this.setCache(oCacheSomeMachineInfo);
        },
        getCache() {
            let target = window.localStorage.getItem('oCacheSomeMachineInfo') || '{}';
            target = JSON.parse(target);
            return target;
        },
        setCache(data) {
            window.localStorage.setItem('oCacheSomeMachineInfo', JSON.stringify(data));
        },
        // 读取并设置表单的默认值（非日期相关输入框）
        setFormDefaultValue () {
            // 默認值，字段輸入框類型是select，配置Index;
            if (!this.$refs.formList) {
                return
            }
            // 排除日期
            const cacheConfigList = this.$refs.formList.react.tableData.filter(item => item.isShow && item.sProp.slice(0, 1) !== 'd');
            // console.log(cacheConfigList);
            cacheConfigList.forEach(item => {
                // 请检查本地defaultInjectList 是否配置sOptionProp，若配置不完全，则手动完善；
                let tempItem = this.machineInfoFormConfig.find(o => item.sProp === o.sProp && o.sInputType === 'option');
                const defaultValue = item.defaultValue;
                if (tempItem) {
                    let options = this.optionsLoc[tempItem.sOptionProp] || [];
                    if (defaultValue.length) {
                        let targetItem = options[defaultValue];
                        this.formData[item.sProp] = targetItem?.sValue;
                        if (item.sProp === 'sStain') {
                            this.formData[item.sProp] = targetItem?.sName;
                        }
                    }
                } else {
                    if (defaultValue.length) {
                        this.formData[item.sProp] = ['iCountingRate'].includes(item.sProp) ? (+defaultValue) : defaultValue;
                    }
                }
            })
            // 初始化日期
            const dateConfigList = this.$refs.formList.react.tableData.filter(item => item.isShow && item.sProp.slice(0, 1) === 'd');
            dateConfigList.map(item => {
                if(item.sProp === 'dOperateEnd' && item.defaultValue.length) {
                    this.formData[item.sProp] = new Date();
                }
            })
        },
        // 初始化数据
        initForm () {
            this.formData = deepClone(this.defaultData);
            this.setFormDefaultValue();
            // 操作人
            this.formData.sOperatorId = this.userInfo.sId;
            // 机房id
            if (!this.formData.sMachineryRoomId) {
                let sMachineryRoomOptions = this.optionsLoc.sMachineryRoomOptions;
                sMachineryRoomOptions.find(item => {
                    if (item.sDeviceTypeId === this.patientInfo.sRoomId) {
                        this.formData.sMachineryRoomId = item.sId;
                    }
                })
            }
            if (this.optionsLoc.ApricotReportImagingType.length) {
                let info = this.patientInfo;
                // 延迟显像   
                this.formData.sImgTypeCode = info.dDelayTime || info.sDelayBody ? '2' : '1'
            }
            this.$nextTick(() => {
                // 采集范围 若改变了采集范围则取上次修改的值，而不是取默认值
                let oCacheSomeMachineInfo = this.getCache();
                if(oCacheSomeMachineInfo?.sTargetSiteCode) {
                    this.formData.sTargetSiteCode = oCacheSomeMachineInfo?.sTargetSiteCode;
                }

                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            })
        },
        openDelayPop () {
            if(!this.patientInfo.sId) {
                this.$message.warning('请选择患者数据');
                setTimeout(() => {
                    this.isShowDelayPopover = false
                }, 10)
                return
            }
        },
        showDalayTime () {
            this.showSetDalayTime = !this.showSetDalayTime;
            if (this.DalayScanTime !== '' && !this.showSetDalayTime) {
                localStorage.setItem('DalayScanTime', this.DalayScanTime);
            }
        },
        // 延迟部位改变
        onDelayBodyChange (val) {
            if (val && val.length) {
                if (!this.delayData.dDelayTime) {
                    let time = new Date().getTime() + this.DalayScanTime * 60 * 1000
                    this.delayData.dDelayTime = new Date(time);
                }
            } else {
                this.delayData.dDelayTime = undefined
            }
        },
        handleCancelHoldOnClick () {
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
            Api.holdonCancel({
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex,
            }).then(res => {
                loading.close();
                this.isShowDelayPopover = false
                if (res.success) {
                    this.$message.success(res.msg);
                    this.updateTableInfo();
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close()
                console.log(err)
            })
        },
        // 标记延迟
        handleHoldOnClick () {
            if (!this.delayData.sDelayBody || !this.delayData.sDelayBody.length) {
                this.$message.warning('请选填延迟部位！');
                return
            }
            if (!this.delayData.dDelayTime) {
                this.$message.warning('请填写延迟时间！');
                return
            }
            let sDelayBody = this.delayData.sDelayBody.join(',');
            Api.machineHoldon({
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex,
                sDelayBody: sDelayBody,
                dDelayTime: this.delayData.dDelayTime
            }).then(res => {
                this.isShowDelayPopover = false
                if (res.success) {
                    this.$message.success(res.msg);
                    this.updateTableInfo();
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err)
            })
        },
        // 允许离开
        onAllowLeave () {
            if(!this.patientInfo.sId) {
                this.$message.warning('请选择患者数据');
                return
            }
            let params = {
                patientInfoId: this.patientInfo.sId
            }
            Api.machineScanOver(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg)
                    this.updateTableInfo();
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                console.log(err)
            })
        },
        // 获取sName属性
        getName (arr, val) {
            let item = arr.find(item => item.sValue == val);
            return item ? item.sName : null;
        },
        // 保存数据
        onSaveData () {
            if(!this.patientInfo.sId) {
                this.$message.warning('请选择患者数据');
                return
            }
            this.$refs.refEditLayer.validate(valid => {
                if (!valid) {
                    this.$message.warning('请正确填写必填项');
                    return;
                }
                let params = deepClone(this.formData);
                params.sMachineryRoomText = this.getName(this.optionsLoc.sMachineryRoomOptions, params.sMachineryRoomId);
                params.sImgTypeName = this.getName(this.optionsLoc.ApricotReportImagingType, params.sImgTypeCode);
                params.sDrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, params.sDrinkTypeCode);
                params.sTargetSiteName = this.getName(this.optionsLoc.ApricotReportTargetSite, params.sTargetSiteCode);
                this.optionsLoc.DoctorOptions.find(item => {
                    if (item.sValue === params.sOperatorId) {
                        params.sOperatorNo = item.userNo;
                        params.sOperator = item.sName;
                    }
                })
                params.sPatientId = this.patientInfo.sId;
                this.saveLoading = true;
                if(this.formData.sId) {
                    Api.editData(params).then(res => {
                        this.saveLoading = false;
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.initForm();
                            // 刷新MachineRecord.vue表格
                            this.$eventbus.emit('onRefreshMachineRecordTable', {res:res.data, isEdit: true });
                            return
                        }
                        this.$message.error(res.msg);
                    }).catch(() => {
                        this.saveLoading = false;
                    })
                    return
                }
                Api.saveData(params).then((res) => {
                    this.saveLoading = false;
                    if (res.success) {
                        this.initForm();
                        // 刷新MachineRecord.vue表格
                        this.$eventbus.emit('onRefreshMachineRecordTable',  {res:res.data, isEdit: false });
                        // 上机状态值为0时，刷新表格
                        !this.patientInfo.iIsMachine && !this.isSaveAndAllowLeave && this.updateTableInfo();
                        // 根据配置，调用允许接口
                        this.isSaveAndAllowLeave && this.onAllowLeave();
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(() => {
                    this.saveLoading = false;
                })
            })
        },
        // element组件 数字输入框 当数据返回为null 界面显示为0，设置为undefined 则不显示
        setNull2Undefined (obj) {
            if (Object.prototype.toString.call(obj) !== '[object Object]') return
            if (!Object.keys(obj).length) return
            for (let i in obj) {
                if (obj[i] === null) {
                    obj[i] = undefined
                }
            }
        },
        // 获取机房数据
        async getMachineRoomData () {
            let param = {
                iIsEnable: 1,
                sDistrictId: this.patientInfo.sDistrictId,
            }
            await getMachineRoomData(param).then(res => {
                if (res.success) {
                    this.optionsLoc.sMachineryRoomOptions = res?.data || [];
                    for (let i in this.optionsLoc.sMachineryRoomOptions) {
                        this.optionsLoc.sMachineryRoomOptions[i].sName = this.optionsLoc.sMachineryRoomOptions[i].sRoomName;
                        this.optionsLoc.sMachineryRoomOptions[i].sValue = this.optionsLoc.sMachineryRoomOptions[i].sId;
                    }
                }
            }).catch(err => {
                console.log(err);
            })
        },
        // 获取医生数据
        async getDoctorsData () {
            if (this.optionsLoc.DoctorOptions.length || this.isLoadingDoctors) {
                // 不再获取数据
                return
            }
            this.isLoadingDoctors = true;
            this.optionsLoc.DoctorOptions = await queryUserListByType(5)
        },
        // 显示显示非该用户类型的医生
        AddDocOptionToShow(params){
            const targetItem = this.optionsLoc.DoctorOptions.find(item => item.userId === params.sOperatorId);
            if(!targetItem) {
                this.optionsLoc.DoctorOptions.push({
                    userId: params.sOperatorId,
                    userNo: params.sOperatorNo,
                    userName: params.sOperator,
                    sValue: params.sOperatorId,
                    sName: params.sOperator,
                    iIsAuxiliary: 1,
                })
            }
        },
    },
    mounted () {
        this.optionsLoc.RateOptions = [];
        const ApricotReportPickingRate =  this.$store.getters['dict/map'].ApricotReportPickingRate || []
        ApricotReportPickingRate.length && ApricotReportPickingRate.map(item => {
            this.optionsLoc.RateOptions.push({value: item.sValue})
        })
        this.isTeleportTrue = true;
        // 兄弟组件 接收Machine.vue组件上机信息修改
        this.$eventbus.on('machineInfoEdit', res => {
            if(res.sOperatorId) {
                this.AddDocOptionToShow(res)
            }
            this.$emit('onChangeActiveTab');
            this.$nextTick(() => {
                this.initForm()
                this.formData = res;
            })
        });
        this.$eventbus.on('machineFormInit', row => {
            if(row.sId === this.formData.sId) {
                this.initForm()
            }
        });
    },
    beforeUnmount() {
        this.$eventbus.off('machineInfoEdit');
        this.$eventbus.off('machineFormInit');
    }
}
</script>
<style lang="scss" scoped>
.pull-left {
    float: left;
}

.delay-scan {
    padding: 0 10px 20px;

    h3 {
        color: #333;
        margin: 0;
    }

    .title {
        display: flex;
        align-items: center;
        height: 50px;
    }

    .set-up {
        flex: 1;
        text-align: right;
        margin-left: 50px;
        margin-right: 10px;
    }
}
// :deep(.el-input .el-input__suffix) {
//     height: 0;
// }
</style>
