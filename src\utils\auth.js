import Cookies from 'js-cookie'
import { tokenName } from '@/utils/base_config';

// const Cookies = require('js-cookie')
/*TokenKey？？？？？？？？？？？？？？？？？？？？？？？？？？？？？？*/
const TokenKey = tokenName

export function getToken() {
    return Cookies.get(TokenKey)
}

export function setToken(token) {
    Cookies.set(TokenKey, token)
}

export function removeToken() {
    Cookies.remove(TokenKey)
}

export function setDeviceTheme(theme) {
    Cookies.set('supersetDeviceTheme', theme)
}

export function getDeviceTheme() {
    if (Cookies.get('supersetDeviceTheme')) {
        return Cookies.get('supersetDeviceTheme');
    }
    return 'theme3';
}

/**
 * 缓存用户密码登录的历史用户
 * @param {array} users 
 */
export function setLoginUserOptions(users) {
    localStorage.setItem('LOGINUSERS', users)
}

export function getLoginUserOptions() {
    return localStorage.getItem('LOGINUSERS')
}

/**
 * 缓存UKEy登录返回的certId
 * @param {string} certId 
 */
export function setLoginCertId(certId) {
    localStorage.setItem('UKEYLOGINCERTID', certId)
}

export function getLoginCertId() {
    return localStorage.getItem('UKEYLOGINCERTID')
}

export function removeLoginCertId() {
    localStorage.removeItem('UKEYLOGINCERTID')
}

/**
 * 缓存二维码登录返回的AuthId
 * @param {string} authId 
 */
export function setQRCodeAuthId(authId) {
    localStorage.setItem('QRCODEAUTHID', authId)
}

export function getQRCodeAuthId() {
    return localStorage.getItem('QRCODEAUTHID')
}

export function removeQRCodeAuthId() {
    localStorage.removeItem('QRCODEAUTHID')
}


