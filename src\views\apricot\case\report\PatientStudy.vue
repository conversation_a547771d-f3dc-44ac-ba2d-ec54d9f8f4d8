<template>
    <div class="c-template">
        <div class="c-box-01  bg-light-300">
            <!-- min-width: 545px; -->
            <div class="c-scroll "
                style="display: flex;flex-direction: column; flex: 1;overflow: hidden;"
                v-loading="loading">
                <el-row style="padding-right: 21px;height: 35px;line-height: 35px;background-color: #f0f0f0;overflow: hidden;">
                    <el-col :span="4">
                        <el-checkbox v-if="!isHideElement"
                            v-model="isChecked"
                            style="margin-left: 5px;margin-top: 4px;"
                            @change="onCheckedChange">全选</el-checkbox>
                        <span>&nbsp;</span>
                    </el-col>
                    <el-col :span="4">设备</el-col>
                    <el-col :span="4">数目</el-col>
                    <el-col :span="4">日期</el-col>
                    <el-col :span="4">检查ID</el-col>
                    <el-col :span="4">序列描述</el-col>
                </el-row>
                <div v-if="!filterData.length"
                    style="text-align:center; line-height: 100px;">
                    <span>暂无数据</span>
                </div>
                <div v-else class="c-item">
                    <el-scrollbar class="my-scrollbar">
                        <el-collapse v-model="activeNames">
                            <el-collapse-item v-for="(item, index) in filterData"
                                :name="index"
                                :key="index">
                                <template v-slot:title>
                                    <div class="i-header"
                                        style="flex:1;">
                                        <el-col :span="4">
                                            <i :class="activeNames.includes(index) ? 'el-icon-arrow-down': 'el-icon-arrow-right'"
                                                style="color: #aaa;margin-left: 20px;"></i>
                                        </el-col>
                                        <el-col :span="4"
                                            :title="item.modality">{{item.modality}}</el-col>
                                        <el-col :span="4">{{item.seriesList ? item.seriesList.length : 0}}</el-col>
                                        <el-col :span="4"
                                            :title="item.studyDate">{{item.studyDate}}</el-col>
                                        <el-col :span="4"
                                            :title="item.studyId">{{item.studyId}}</el-col>
                                        <el-col :span="4"
                                            :title="item.studyDesc">{{item.studyDesc}}</el-col>
                                    </div>
                                </template>
                                <el-table class="n-borderTop-none"
                                    :ref="'child'+ item.studyId"
                                    :data="item.seriesList"
                                    border
                                    highlight-current-row
                                    stripe>
                                    <el-table-column v-if="!isHideElement"
                                        type="selection"
                                        align="center"
                                        width="60">
                                    </el-table-column>
                                    <el-table-column prop="modality"
                                        label="设备"
                                        min-width="80"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="instanceCount"
                                        label="数目"
                                        min-width="50"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="isRebuid"
                                        label="可重建"
                                        min-width="70"
                                        show-overflow-tooltip
                                        align="center">
                                        <template #default="scope">
                                            {{scope.row.isRebuid ? '是' : '否'}}
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="seriesDate"
                                        label="序列日期"
                                        min-width="80"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="seriesTime"
                                        label="序列时间"
                                        min-width="80"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column :fit="false"
                                        prop="seriesDesc"
                                        min-width="80"
                                        label="序列描述"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column v-if="rights.deleteSeries"
                                        width="100"
                                        align="center"
                                        label="操作"
                                        show-overflow-tooltip>
                                        <template v-slot="{row}">
                                            <el-button-icon-fa link
                                                icon="el-icon-delete"
                                                @click="onDelSerie(row, item)">删除</el-button-icon-fa>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-collapse-item>
                        </el-collapse>
                    </el-scrollbar>
                </div>
            </div>
        </div>
        <div class="c-button"
            v-if="!isHideElement">
            <div class="c-item c-message">
                <el-button-icon-fa
                    _icon="fa fa-node-tree"
                    type="primary"
                    @click="onClickSend">发送 dicom</el-button-icon-fa>
                <el-button-icon-fa
                    _icon="fa fa-close-1"
                    @click="$emit('closeDialog')">关闭</el-button-icon-fa>
            </div>
        </div>
        <div class="c-item-list"
            v-show="msgObj.visible"
            v-loading="nodeLoading">
            <div class="i-checkBox">
                <el-checkbox-group v-if="msgObj.messageList.length"
                    v-model="msgObj.formMessage">
                    <div v-for="(item, index) in msgObj.messageList" :key="index">
                        <el-checkbox
                            :label="item.sId"
                            :key="item.sId">{{item.sServerName}}</el-checkbox>
                    </div>
                </el-checkbox-group>
                <div v-else
                    style="text-align:left;line-height: 2;">暂无数据</div>
            </div>
            <div class="i-button">
                <el-button-icon-fa type="primary"
                    :loading="msgObj.loading"
                    icon="el-icon-position"
                    :disabled="msgObj.messageList.length===0"
                    @click="onSend">发送</el-button-icon-fa>
                <el-button-icon-fa 
                    icon="el-icon-close"
                    @click="msgObj.visible=false">关闭</el-button-icon-fa>
            </div>
        </div>
    </div>
</template>
<script>
import { deepClone } from '$supersetUtils/function'

// import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/case/report.js'
import { getDicomNode } from '$supersetApi/projects/apricot/system/dicomSet.js'

export default {
    name: 'PatientStudy',
    // mixins: [mixinTable],
    emits: ['setImageSum', 'closeDialog'],
    props: {
        params: {
            type: Object,
            default: () => ({})
        },
        enableRebuild: {
            type: Number,
            default: 1
        },
        disableRebuild: {
            type: Number,
            default: 0
        },
        visible: {
            type: Boolean,
            default: false
        },
        isHideElement: {
            type: Boolean,
            default: false
        },
        rights: {
            type: Object,
            default: () => ({
                deleteSeries: true,
            })
        }
    },
    data () {
        return {
            msgObj: {           // 消息发送
                formMessage: [],
                messageList: [],
                loading: false,
                visible: false,
            },
            filterData: [],
            nodeLoading: false,
            multipleSelection: [],
            activeNames: [],
            isChecked: false,
            timeInterval: null,
            loading: false
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    watch: {
        'params': {
            handler (val, oldVal) {
                clearInterval(this.timeInterval);
                if (!Object.keys(this.params).length) {
                    this.filterData = [];
                    return
                }
                if(this.isHideElement) {
                    val.sPatientInfoId = val.sId;
                    // 问诊管理模块 点击相同行数据，结束
                    if(val && oldVal && val.sPatientInfoId === oldVal.sPatientInfoId) return;
                    // 问诊管理模块需调用定时器
                    this.timeInterval = setInterval(() => {
                        this.getData(true);
                    }, 10 * 1000);
                }
                this.getData();
            },
            immediate: true
        },
        enableRebuild () {
            this.filterTableData()
        },
        disableRebuild () {
            this.filterTableData()
        },
        visible (val) {
            if (val) {
                this.msgObj.visible = false;
            }
        },

    },
    methods: {
        onCheckedChange () {
            this.filterData.forEach(item => {
                const dom = this.$refs['child' + item.studyId];
                dom[0].toggleAllSelection()
            })
        },
        // 删除序列
        onDelSerie (row, item) {
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                let jsonData = {
                    delPerson: this.userInfo.sName,
                    seriesUid: row.seriesInstanceUid,
                    studyDate: item.studyDate
                }
                let loading = this.$loading({
                    lock: true,
                    text: '正在删除中，请稍等',
                    background: 'rgba(0, 0, 0, 0.2)'
                });
                Api.delSeriesUid(jsonData).then((res) => {
                    loading.close()
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.getData();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    loading.close();
                })
            })
        },
        // 发送数据
        onSend () {
            if (!this.msgObj.formMessage.length) {
                this.$message.warning('请选择发送地址！');
                return
            }
            let selections = [];
            this.filterData.map((item) => {
                let dom = this.$refs['child' + item.studyId];
                // console.log(dom)
                selections = selections.concat(dom[0].getSelectionRows())
            })
            if (!selections.length) {
                this.$message.warning('请选择发送序列！');
                return
            }
            let multipleSelection = selections.map(item => item.seriesInstanceUid);
            // 发送请求
            this.msgObj.messageList.map(item => {
                if (this.msgObj.formMessage.includes(item.sId)) {
                    let jsonData = {
                        sAETitle: item.sAETitle,
                        sIp: item.sIp,
                        iPort: item.iPort,
                        seriesInstanceUidList: multipleSelection
                    }
                    this.imgPatientDicomSend(jsonData, item.sServerName)
                }
            });
        },
        // 发送
        async imgPatientDicomSend (jsonData, sName) {
            let loading = this.$loading({
                lock: true,
                text: '正在发送中，请稍等',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            await Api.imgPatientDicomSend(jsonData).then(res => {
                loading.close()
                // console.log(res);
                if (!res.success) {
                    this.$message({
                        message: sName + '节点，提示：' + res.msg,
                        type: 'error',
                        duration: 5000
                    });
                    return
                }
                this.$message({
                        message: sName + '节点，提示：' + res.msg,
                        type: 'success',
                        duration: 5000
                    });
            }).catch(err => {
                console.log(err);
                loading.close()
            })
        },
        // 点击发送Dicom按钮
        onClickSend () {
            this.msgObj.visible = !this.msgObj.visible;
            this.msgObj.formMessage = [];
            this.getDicomNode()
        },
        // 获取dicom 节点数据
        getDicomNode () {
            if (!this.msgObj.visible) {
                return
            }
            let jsonData = {
                condition: {
                    iIsEnable: 1
                },
                page: {
                    pageSize: 500,
                    pageCurrent: 1
                }
            }
            this.nodeLoading = true;
            getDicomNode(jsonData).then(res => {
                this.nodeLoading = false;
                if (res.success) {
                    this.msgObj.messageList = res.data.recordList || [];
                    return
                }
            }).catch(err => {
                this.nodeLoading = false;
                console.log(err)
            })
        },
        // 过滤可重建或不可重建数据
        filterTableData () {
            this.isChecked = false;
            let tempData = deepClone(this.tableData)
            this.activeNames = []
            if (this.tableData.length === 0) {
                this.filterData = [];
                return
            }
            let isRebuid = 1;
            if (!this.enableRebuild && this.disableRebuild) {
                isRebuid = 0
            }
            this.filterData = tempData.filter(item => {
                if (item.seriesList && item.seriesList.length) {
                    if (!this.enableRebuild && !this.disableRebuild) {
                        item.seriesList = []
                    } else if (this.enableRebuild && this.disableRebuild) {
                        let temp = item.seriesList
                        item.seriesList = temp;
                    } else {
                        item.seriesList = item.seriesList.filter(_ => isRebuid == _.isRebuid);
                    }
                }
                return item;
            })
            let len = this.filterData.length;
            if (len) {
                for (let i = 0; i < len; i++) {
                    this.activeNames.push(i)
                }
            }
        },
        // 获取表格数据
        getData (isInterval = false) {
            const params1 = {
                studyInstanceUid: this.params.studyInstanceUid,
                sPatientInfoId: this.params.sPatientInfoId
            }
            const params2 = {
                studyInstanceUid: this.params.studyInstanceUid,
            }
            this.multipleSelection = [];
            let targetFns = {
                otherModule: Api.findStudyByPatientInfoId,
                reportModule: Api.getPatientStudyInfo
            }
            let fn = this.isHideElement ? targetFns.otherModule : targetFns.reportModule
            let params = this.isHideElement ? params1 : params2;
            !isInterval && (this.loading = true);
            fn(params).then(res => {
                this.loading = false;
                this.tableData = [];
                if (res.success) {
                    this.tableData = res.data || [];
                    this.setImageSum(this.tableData);
                    // 赋选中状态
                    this.filterTableData()
                    return
                }
                this.filterTableData()
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        },
        setImageSum(data) {
            let imageSum = 0
            data.map(item => {
                item.seriesList && item.seriesList.map(_item => {
                    imageSum += _item.instanceCount;
                })
            })
            this.$emit('setImageSum', imageSum);
        }
    },
    beforeUnmount(){
        clearInterval(this.timeInterval);
    },
    activated() {
        if(this.isHideElement && this.params.sId) {
            // 问诊管理模块需调用定时器
            clearInterval(this.timeInterval);
            this.timeInterval = setInterval(() => {
                this.getData(true);
            }, 10 * 1000);
        }
    },
    deactivated() {
        clearInterval(this.timeInterval);
    }
}
</script>
<style lang="scss" scoped>
.c-template {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    .c-box-01 {
        display: flex;
        flex: 1;
        height: 0px;
        overflow: auto;
        border: var(--el-border);
        .c-item {
            // height: 100%;
            flex: 1;
            overflow: auto;
        }
        .c-table {
            margin: 0 auto;
            padding: 0px 0px 20px 60px;
            .el-table {
                border-top: 0px;
            }
        }
    }

    .c-message {
        position: relative;
        text-align: right;
        padding-top: 10px;
    }
    
    .c-item-list {
        position: absolute;
        right: 0;
        bottom: 38px;
        width: 300px;
        min-height: 70px;
        max-height: calc(100% - 80px);;
        padding: 15px 5px 15px 15px;
        border: 1px solid #eee;
        background: white;
        box-shadow: 1px 1px 10px 1px #eee;
        display: flex;
        flex-direction: column;
        overflow: auto;
        z-index: 5;
        .i-checkBox {
            flex: 1;
            overflow: auto;
        }
        .i-button {
            margin-top: 10px;
            border-top: 1px solid #eee;
            padding-top: 10px;
            text-align: right;
        }
    }

    :deep(.el-col ){
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
    }
    :deep(.el-collapse-item > div) {
        .el-collapse-item__header  {
            height: 35px;
        }
        .el-collapse-item__arrow {
            color: white;
        }
    }
}
</style>
