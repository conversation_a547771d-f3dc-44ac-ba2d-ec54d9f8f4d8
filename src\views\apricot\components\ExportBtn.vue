<template>
  <div class="inline-block" @click="onClickBtn">
    <slot>
      <el-button :class="{
        'm-vertical-btn t2': buttonMsg.icon,
        'margin_l': !buttonMsg.icon,
        'm-vertical-text': buttonMsg.isFold,
        't-border': buttonMsg.isBorder
      }" :disabled="buttonMsg.isReadOnly" :plain="buttonMsg.plain" :link="buttonMsg.link" :type="buttonMsg.type">
        <svg v-if="buttonMsg.icon" class="fa" aria-hidden="true" style="font-size:34px">
          <use :xlink:href="'#' + buttonMsg.icon"></use>
        </svg>
        <label>{{ buttonMsg.name }}</label>
      </el-button>
    </slot>
  </div>
  <!-- 导出序列 -->
  <el-dialog append-to-body :modelValue="dialogVisible" :close-on-click-modal="false" class="t-default my-dialog"
    width="900px" top="10vh" destroy-on-close @close="handleCloseDialog">
    <template #header>
      <div class="header-title el-dialog__title">
        <span>导出序列：</span>
        <span v-if="patientInfo.sName">{{ patientInfo.sName }}</span>
        <span v-if="patientInfo.sSexText">{{ patientInfo.sSexText }}</span>
        <span v-if="patientInfo.sAge">{{ patientInfo.sAge }}</span>
      </div>
    </template>
    <div class="c-dialog-body">
      <div class="c-checkGroup">
        <el-checkbox v-model="enableRebuild" :true-label="1" :false-label="0"
          @change="handleCheckBoxChange">可重建</el-checkbox>
        <el-checkbox v-model="disableRebuild" :true-label="1" :false-label="0"
          @change="handleCheckBoxChange">不可重建</el-checkbox>
      </div>
      <div class="c-item t-1 m-flexLaout-ty">
        <!-- 表格 -->
        <div class="g-flexChild">
          <el-table :data="filterList" ref="mainTree" @selection-change="handleSelectionChange" @row-click="onRowClick"
            border height="60vh">
            <el-table-column type="selection" width="55">
            </el-table-column>
            <el-table-column label="设备" prop="sModality" min-width="120" show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="数目" prop="iInstanceCount" min-width="80" show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="序列描述" prop="sSeriesDesc" min-width="200" show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="日期" prop="iSeriesDate" min-width="120" show-overflow-tooltip>
            </el-table-column>
            <el-table-column label="时间" prop="sSeriesTime" min-width="120" show-overflow-tooltip>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="text-right">
        <el-checkbox v-model="includeReportFile" @change="onChangeIncludeReportFile">含报告</el-checkbox>
        <el-checkbox v-model="includeXpcViewer" @change="onChangeIncludeXpcViewer">含阅图软件</el-checkbox>
      </div>
    </div>
    <template #footer>
      <span v-if="exportImageAbsolutePath" style="margin-right: 30px;">导出路径：{{ exportImageAbsolutePath }}</span>
      <el-button-icon-fa type="primary" :plain="exportImageAbsolutePath ? true: false" icon="el-icon-thumb" @click="onCheckDestDir">
        {{ exportImageAbsolutePath ? '更换目录' : '选择目录'  }}
      </el-button-icon-fa>
      <el-button-icon-fa v-if="exportImageAbsolutePath" :loading="loading" type="primary" icon="el-icon-check" @click="handleBurn">
        {{ loading ? '导出中' : '开始导出' }}</el-button-icon-fa>
      <el-button-icon-fa _icon="fa fa-close-1" @click="handleCloseDialog">关闭</el-button-icon-fa>
    </template>

    <el-dialog append-to-body :modelValue="d_showDir_v" class="t-default my-dialog" width="900px" top="10vh"
      :close-on-click-modal="false" title="目录" @close="handleCloseDirDialog">
      <div v-loading="treeLoading" style="height: 62vh;overflow: auto;padding: 15px 30px;">
        <el-tree :data="treeData" :props="defaultProps" ref="dTree" node-key="absolutePath" highlight-current lazy
          :load="loadNode" @node-expand="handleNodeExpand" @node-click="handleNodeClick">
        </el-tree>
      </div>
      <template #footer>
        <span style="margin-right: 30px;">当前选中路径：{{ selectedItem.absolutePath }}</span>
        <el-button-icon-fa type="primary" :loading="loading" icon="el-icon-check" @click="handleBurn">{{ loading ? '导出中' :
          '开始导出' }}</el-button-icon-fa>
        <el-button-icon-fa _icon="fa fa-close-1" @click="d_showDir_v = false">关闭</el-button-icon-fa>
      </template>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { deepClone } from "$supersetUtils/function"
import { cdburnSeries, cdExportRecord, cdburnGetSubFile } from '$supersetApi/projects/apricot/case/report.js'
export default {
  name: 'ExportBtn',
  props: {
    buttonMsg: {
      type: Object,
      default: () => ({})
    },
    patientInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      enableRebuild: 1, // 是否可重建 1=是；0 =否
      disableRebuild: 0, // 是否不可重建 1=是；0 =否
      multipleSelection: [],
      filterList: [],
      seriesList: [],
      dialogVisible: false,
      loading: false,
      d_showDir_v: false,
      treeLoading: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      selectedItem: {},
      nodeLoading: false,
      exportImageAbsolutePath: localStorage.getItem('exportImageAbsolutePath') || undefined,
      includeReportFile: JSON.parse(localStorage.getItem('exportImageIncludeReportFile') ?? 'true'),
      includeXpcViewer: JSON.parse(localStorage.getItem('exportImageIncludeXpcViewer') ?? 'true')
    }
  },
  methods: {
    onChangeIncludeReportFile(){
        localStorage.setItem('exportImageIncludeReportFile', this.includeReportFile);
    },
    onChangeIncludeXpcViewer(){
        localStorage.setItem('exportImageIncludeXpcViewer', this.includeXpcViewer);
    },
    // 打开目录弹窗
    onCheckDestDir() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择需要导出的序列！');
        return
      }
      this.d_showDir_v = true;
      this.treeData = [];
      this.selectedItem = {};
      this.cdburnGetSubFile();
    },
    // 关闭弹窗
    handleCloseDirDialog() {
      this.d_showDir_v = false;
    },
    // 树节点展开事件
    handleNodeExpand(data, node) {
      if (!this.nodeLoading) {
        node.loaded = false;
        node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
      }
      this.$refs.dTree.setCurrentKey(node.data.absolutePath);
      this.selectedItem = data;
    },
    // 树节点点击事件
    handleNodeClick(data, node) {
      this.selectedItem = data;
      if (node.expanded) {
        node.loaded = false;
        node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
      }
    },
    // 加载路径节点
    loadNode(node, resolve) {
      if (node.level === 0) {
        return
      }
      let params = {
        absolutePath: node.data.absolutePath
      }
      this.nodeLoading = true;
      cdburnGetSubFile(params).then(res => {
        this.nodeLoading = false;
        if (!res.success) {
          this.$message.error(res.msg);
          resolve([])
          return
        }
        let arr = res.data || [];
        resolve(arr)
      }).catch(err => {
        console.log(err);
        this.nodeLoading = false;
        resolve([])
      })
    },
    // 获取本地磁盘路径
    cdburnGetSubFile() {
      let params = {
        absolutePath: '/'
      }
      this.treeLoading = true;
      cdburnGetSubFile(params).then(res => {
        this.treeLoading = false;
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        this.treeData = res.data || [];
      }).catch(err => {
        this.treeLoading = false;
        console.log(err);
      })
    },
    handleCheckBoxChange() {
      this.handleFilterList()
    },
    handleFilterList() {
      if (!this.enableRebuild && !this.disableRebuild) {
        this.filterList = [];
        return
      }
      if (this.enableRebuild && !this.disableRebuild) {
        this.filterList = this.seriesList.filter(data => data.iIsRebuid === 1);
        return
      }
      if (this.disableRebuild && !this.enableRebuild) {
        this.filterList = this.seriesList.filter(data => data.iIsRebuid === 0);
        return
      }
      this.filterList = deepClone(this.seriesList);
    },
    // 表格多选事件
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 行点击事件
    onRowClick(row) {
      this.$refs.mainTree.toggleRowSelection(row);
    },
    // 执行导出
    handleBurn() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择需要导出的序列！');
        return
      }
      if(Object.keys(this.selectedItem).length) {
        // 选中目录，赋值路径
        this.exportImageAbsolutePath = this.selectedItem.absolutePath;
        localStorage.setItem('exportImageAbsolutePath', this.exportImageAbsolutePath);
      }
      
      if (!this.exportImageAbsolutePath) {
        this.$message.warning('请选择导出目录！');
        return
      }
      
      let series = [];
      this.multipleSelection.map(item => {
        series.push({
          studyInstanceUid: item.sStudyInstanceUid,
          seriesInstanceUid: item.sSeriesInstanceUid
        })
      });
      this.loading = true;
      cdExportRecord({
        sPatientId: this.patientInfo.sId,
        destDir: this.exportImageAbsolutePath,
        includeReportFile: this.includeReportFile,
        includeXpcViewer: this.includeXpcViewer,
        series: series
      }).then(res => {
        this.loading = false;
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        this.$message.success(res.msg);
        this.handleCloseDirDialog();
        this.handleCloseDialog();
      }).catch(err => {
        this.loading = false;
        console.log(err);
      })
    },
    // 关闭弹窗 
    handleCloseDialog() {
      this.dialogVisible = false
    },
    // 导出图像按钮点击事件
    onClickBtn() {
      let item = this.patientInfo;
      if (!Object.keys(item).length) {
        this.$message.warning('请选择一条患者数据！')
        return
      }
      if (!item.sImgPatientId) {
        this.$message.warning('图像患者标识不存在！')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      this.multipleSelection = [];
      cdburnSeries({
        iImgStudyDate: item.sImgStudyDate,
        sImgAccessionNumber: item.sImgAccessionNumber,
        sImgPatientId: item.sImgPatientId,
        sNuclearNum: item.sNuclearNum
      }).then(res => {
        loading.close();
        this.seriesList = [];
        this.filterList = [];
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        if (!res.data || res.data.length === 0) {
          // console.log('没有查找图像序列')
          this.$message.error('没有查找图像序列');
          return
        }
        this.dialogVisible = true;
        this.seriesList = res.data;
        this.handleFilterList();
      }).catch(err => {
        console.log(err);
        loading.close();
      })
    },
  },
  created() {
    this.handleFilterList()
  }
}
</script>

<style lang="scss" scoped>
.c-dialog-body {
  .c-checkGroup {
    margin-bottom: 10px;
  }

  .c-item.t-1 {
    padding-bottom: 10px;

    .i-upload {
      width: 180px;
      padding-left: 20px;
    }
  }
}

.margin_l {
  margin-left: 10px;
}

// :deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
//     background-color: #c3dbf2;
// }
:deep(.el-tree-node__expand-icon) {
  font-size: 20px
}
</style>
