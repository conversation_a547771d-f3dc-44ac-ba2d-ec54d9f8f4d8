<template>
    <div class="c-print">
        <el-tabs v-model="activeName">
            <el-tab-pane label="工作站呼叫管理" name="CallConfig">
                <component v-if="activeName === 'CallConfig'" :is="activeName"></component>
            </el-tab-pane>
             <el-tab-pane label="呼叫屏设置" name="ScreenConfig">
                <component v-if="activeName === 'ScreenConfig'" :is="activeName"></component>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script>
    import CallConfig from './components/CallConfig.vue'
    import ScreenConfig from './components/ScreenConfig.vue'
    export default {
        name: 'CallManagement',
        components: {
            CallConfig,
            ScreenConfig
        },
        data () {
            return {
                activeName: 'CallConfig'
            }
        },
        methods: {

        },
    }
</script>
<style scoped>
.c-print{
    width: 100%;
    height: 100%;
    display: flex;
    padding: 10px 10px 10px 10px;
    box-sizing: border-box;
}
.el-tabs {
    width: 100%;
}
</style>