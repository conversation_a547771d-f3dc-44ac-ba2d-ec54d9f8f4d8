import Api from '$supersetApi/projects/apricot/case/report.js'

const isCollapse = localStorage.getItem('infoLayerMenuCollapse-ReportCaseIndex');
const oTemplateEssayConfig = JSON.parse(localStorage.getItem('oTemplateEssayConfig'));
const state = {
  patientInfo: {},
  currentModule: '',
  IsUpdatePatientInfo: false,
  isOpenEssayLayout: false,  // 是否打开范文模板面板
  isHideEssayContent: oTemplateEssayConfig?.isHideQuill || false,  // 是否隐藏范文内容
  infoLayerMenuCollapse: String(isCollapse || 'false') !== 'false',
  CommonSentence: [],
  StructBookmark: [],
}

const getters = {
  CommonSentence(state) {
    return state.CommonSentence
  },
  StructBookmark(state) {
    return state.StructBookmark
  },
  patientInfo(state) {
    return state.patientInfo
  },
  isOpenEssayLayout(state) {
    return state.isOpenEssayLayout
  },
  isHideEssayContent(state) {
    return state.isHideEssayContent
  },
  infoLayerMenuCollapse(state) {
    return String(state.infoLayerMenuCollapse) !== 'false'
  }
}

const mutations = {
  setPatientInfo(state, payload) {
    state.patientInfo = payload.patientInfo
  },
  setCurrentModule(state, payload) {
    state.currentModule = payload.name
  },
  setIsUpdatePatientInfo(state, payload) {
    state.IsUpdatePatientInfo = payload.IsUpdatePatientInfo
  },
  setIsOpenEssayLayout(state, payload) {
    state.isOpenEssayLayout = payload.isOpenEssayLayout
  },
  setIsHideEssayContent(state, payload) {
    state.isHideEssayContent = payload.isHideEssayContent
  },
  setInfoLayerMenuCollapse(state, payload) {
    localStorage.setItem('infoLayerMenuCollapse-ReportCaseIndex', payload);
    state.infoLayerMenuCollapse = payload
  },
  setCommonSentence(state, payload) {
    state.CommonSentence = payload
  },
  setStructBookmark(state, payload) {
    state.StructBookmark = payload
  }
}

const actions = {
  loadCommonSentence({ state, commit }, force = false) {
      if (state.CommonSentence.length && !force) {
          return false
      }

      let list = []
      return Api.queryCommonWords().then(res => {
          if (res && res.success) {
              list = res.data || []
              commit(`setCommonSentence`, list);
              return list
          }
      })
  },
  loadStructBookmark({ state, commit }, force = false) {
    if (state.StructBookmark.length && !force) {
        return false
    }

    let list = []
    return Api.queryAllBookmark().then(res => {
        if (res && res.success) {
            list = res.data || []
            commit(`setStructBookmark`, list);
            return list
        }
    })
},
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions,
  modules: {}
}
