<template>
    <!-- <div class="link-list">
        <span v-for="(item, index) in linkList" :key="index">
            <el-button class="my-button" 
                size="large" 
                type="primary" 
                plain 
                @click="handleItemClick(item)">{{ item.sLinkTypeName }}</el-button>
        </span>
    </div> -->

    <el-dropdown v-bind="$attrs">
        <span class="el-dropdown-link">
            临床资料
            {{ linkList.length ? `(${linkList.length})` : '(-)'}}
            <el-icon class="el-icon--right"
                style="top: 2px; left: -6px;">
                <arrow-down />
            </el-icon>
        </span>
        <template #dropdown>
            <el-dropdown-menu v-if="linkList.length">
                <!-- divided -->
                <el-dropdown-item v-for="(item, index) in linkList"
                    :key="index"
                    :divided="!!index"
                    @click="handleItemClick(item)">{{ item.sLinkTypeName }}</el-dropdown-item>
            </el-dropdown-menu>
            <el-empty v-else
                :image-size="50"
                description="暂无资料"
                style="padding: 10px 50px" />
        </template>
    </el-dropdown>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';
import Api from '$supersetApi/projects/apricot/system/thirdLink.js';
import { useStore } from 'vuex';
import { useHandleThirdLinkItem } from '$supersetResource/js/projects/apricot/useHandlerThirdLink.js';

const store = useStore();
// 登录用户信息
const userInfo = computed(() => {
    const loginUser = store._state.data.user.userSystemInfo;
    return loginUser || {};
})
// 注入患者信息
const patientInfo = inject('patientInfo', {});
// 链接数组
const linkList = ref([]);
// 暴露组件属性或方法可供父组件使用
defineExpose({
    linkList
})
// 点击事件
function handleItemClick (item) {
    const patientInfo_ = patientInfo?.value || {};
    if (!patientInfo_.sId) {
        ElMessage.warning('请选择患者数据!');
        return
    }
    const userInfo_ = userInfo.value;
    useHandleThirdLinkItem(item, patientInfo_, userInfo_)
};

onMounted(() => {
    getData();
});
// 获取数组
function getData () {
    let params = {
        condition: {
            sLinkTypeCode: '1'
        },
        page: {
            pageCurrent: 1,
            pageSize: 200,
        }
    };
    Api.getThirdLinkData(params).then(res => {
        if (res.success) {
            linkList.value = res.data?.recordList || [];
            return
        }
        ElMessage.error(res.msg)
    }).catch(err => {
        console.log(err);
    })
}; 
</script>

<style lang="scss" scoped>
.link-list {
    span {
        display: inline-block;
        padding: 10px;

        .my-button {
            min-width: 250px;
            border: none;

            span {
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }
}
:deep(.el-dropdown-menu__item--divided) {
    margin: 2px 0;
}
:deep(.el-tooltip__trigger:focus-visible) {
    outline: unset;
}
</style>
