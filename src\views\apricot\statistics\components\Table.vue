<template>
    <div class="c-template m-flexLaout-ty">
        <div class="c-item t-1">
            <h5><strong>{{pageBaseInfo.headline}}</strong><span v-if="pageBaseInfo.searchTimeStr">（{{pageBaseInfo.searchTimeStr}}）</span></h5>
            <div>
                <span v-if="pageBaseInfo.printTimeStr">打印时间：<span style="color:#777">（{{pageBaseInfo.printTimeStr}}）</span></span>
                <el-button-icon-fa size="small"
                    _icon="fa fa-export-01"
                    :disabled="!tableData.length"
                    @click="onExport">导出</el-button-icon-fa>
                <el-button-icon-fa size="small"
                    _icon="fa fa-print"
                    :disabled="!tableData.length"
                    @click="onPrintClick">预览打印</el-button-icon-fa>
            </div>
        </div>
        <div class="c-item g-flexChild">
            <div style="height: calc(100% - 10px);">
                <el-table size="small"
                    :data="tableData"
                    ref="tableRef"
                    border
                    stripe
                    height="100%">
                    <el-table-column v-if="tableProps.length"  
                        type="index" 
                        label="序号" 
                        width="60px">
                    </el-table-column>
                    <template v-for="item in tableProps">
                        <el-table-column :key="item.index"
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :align="item.sAlign"
                            :min-width="item.sMinWidth"
                            :width="item.sWidth"
                            :sortable="!!item.iSort"
                            show-overflow-tooltip
                            v-if="!item.iIsHide">
                            <template v-slot="scope">
                                <span>{{scope.row[item.sProp]}}</span>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>
        <Print v-model:show="print.show"
            :tableProps="tableProps"
            :tableData="tableData"
            :eChartsData="eChartsData"
            :pageBaseInfo="pageBaseInfo"></Print>
    </div>
</template>
<script>
import Print from "./Print"
import { exportExcelFn } from '$supersetResource/js/exportTable'
export default {
    name: 'Table',
    components: {
        Print
    },
    props: {
        title: String,
        tableProps: {
            type: Array,
            default: () => []
        },
        tableData: {
            type: Array,
            default: () => []
        },
        pageBaseInfo: {
            type: Object,
            default: () => ({})
        },
        eChartsData: {
            type: Array,
            default: () => []
        },
    },
    data () {
        return {
            // tableData: [],
            print: {
                show: false
            }
        }
    },
    watch: {
        tableProps () {
            this.$nextTick(() => {
                this.$refs.tableRef.doLayout()
            })
        }
    },
    methods: {
        onPrintClick () {
            this.print.show = true;
        },
        onExport () {
            exportExcelFn(`${this.pageBaseInfo.headline}(${this.pageBaseInfo.searchTimeStr})`, '.el-table')
        },
    }
}
</script>
<style lang="scss" scoped>
.c-template {
    padding: 0 15px  ;
}
.c-item.t-1 {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    > h5 {
        margin: 0;
        font-size: 13px;
    }
}
</style>
