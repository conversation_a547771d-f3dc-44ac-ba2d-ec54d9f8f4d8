export default{
    DA1: {
        type: 't-x',
        localStorageKey: '202309211606',
        panelConfig: [
            {
                size: 0,
                minSize: 0,
                name: 'c1',
                isFlexible: true
            },
            {
                size: window.innerWidth / 2,
                // minSize: 0,
                maxSize: window.innerWidth / 4 * 3,
                name: 'c2',
                isFlexible: false
            },
        ],
    },
    searchListConfig:[
        {
            sProp: 'dAppointmentTimeSt',
            sLabel: '预约开始',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1,
        },
        {
            sProp: 'dAppointmentTimeEd',
            sLabel: '预约结束',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1,
        },
        {
            sProp: 'sName',
            sLabel: '姓名',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sExamDocName',
            sLabel: '检测医生',
            iLayourValue: 4,
            sInputType: 'text',
            iCustom: 1,
        },
        {
            sProp: 'sReportDocName',
            sLabel: '审核医生',
            iLayourValue: 4,
            sInputType: 'text',
            iCustom: 1,
        },
        {
            sProp: 'sQualitative',
            sLabel: '阴阳性',
            iLayourValue: 4,
            sInputType: 'option',
            sOptionProp: 'qualitativeOptions',
        },
        {
            sProp: 'iReportStatus',
            sLabel: '报告状态',
            iLayourValue: 4,
            sInputType: 'option',
            sOptionProp: 'reportStatusOptions',
        },
        {
            sProp: 'iSendStatus',
            sLabel: '发送状态',
            iLayourValue: 4,
            sInputType: 'option',
            sOptionProp: 'sendStatusOptions',
        },
    ],
    tableConfig: [
        {
            sProp: 'iReportStatus',
            sLabel: '报告状态',
            sAlign: 'left',
            sMinWidth: '90px',
        },
        {
            sProp: 'iSendStatus',
            sLabel: '发送状态',
            sAlign: 'left',
            sMinWidth: '90px',
        },
        {
            sProp: 'sName',
            sLabel: '姓名',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sMinWidth: '60px',
        },
        {
            sProp: 'sBriefAge',
            sLabel: '年龄',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'sExamDocName',
            sLabel: '检测医生',
            sAlign: 'center',
            sMinWidth: '110px',
        },
        {
            sProp: 'sReportDocName',
            sLabel: '审核医生',
            sAlign: 'center',
            sMinWidth: '110px',
        },
        {
            sProp: 'sQualitativeName',
            sLabel: '阴阳性',
            sAlign: 'center',
            sMinWidth: '110px',
        },
        {
            sProp: 'sExamResult',
            sLabel: '检测结果',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'dReportTime',
            sLabel: '报告时间',
            sAlign: 'left',
            sMinWidth: '150px',
        },
        {
            sProp: 'dPrintTime',
            sLabel: '打印时间',
            sAlign: 'left',
            sMinWidth: '150px',
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约时间',
            sAlign: 'left',
            sMinWidth: '150px',
            isSortable: true,
            sOrder: 'ascending'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
    ],
}