<template>
    <div class="c-look-content scope-scheme">
        <el-tabs v-model="activeName" tab-position="left">
            <el-tab-pane v-for="item in tabList" :key="item.name" :label="item.label"
                :name="item.name">
                <component v-if="activeName == item.is" :is="item.is"></component>
            </el-tab-pane>
        </el-tabs>

    </div>
</template>
<script>
/** 项目设置 */
import { SetHospital, SetDeviceType, SetMachineRoom, SetNuclide, SetTracer, SetItemPosition, SetItem, SetTestMode, SetAccessNumRule, CheckMedicine, AutoItemSet, WorkStation } from './components/index.js'

export default {
    name: 'SchemeProjectConfig',
    components: {
        SetHospital,
        SetDeviceType,
        SetMachineRoom,
        SetNuclide,
        SetTracer,
        SetItemPosition,
        SetItem,
        SetTestMode,
        SetAccessNumRule,
        CheckMedicine,
        AutoItemSet,
        WorkStation,

    },
    props: {

    },
    data () {
        return {
            activeName: 'SetHospital',
            tabList: [
                { name: 'SetHospital', label: '院区', is: 'SetHospital'},
                { name: 'SetDeviceType', label: '设备类型', is: 'SetDeviceType',},
                { name: 'SetMachineRoom', label: '机房', is: 'SetMachineRoom',},
                { name: 'WorkStation', label: '工作站', is: 'WorkStation'},
                { name: 'SetItem', label: '检查项目', is: 'SetItem'},
                { name: 'SetNuclide', label: '核素', is: 'SetNuclide'},
                { name: 'SetTracer', label: '示踪剂', is: 'SetTracer'},
                { name: 'SetItemPosition', label: '检查部位', is: 'SetItemPosition'},
                { name: 'SetTestMode', label: '检查方式', is: 'SetTestMode'},
                { name: 'CheckMedicine', label: '检查用药', is: 'CheckMedicine'},
                { name: 'AutoItemSet', label: '项目匹配', is: 'AutoItemSet'},
                { name: 'SetAccessNumRule', label: '核医学号规则', is: 'SetAccessNumRule'}
            ],
            lazy: {},

        }
    },
    watch: {
    },
    methods: {
    },
    created() {
        setTimeout(() => {  
            let activePanel = this.$route.query.activePanel;
            if (activePanel) {
                this.activeName = 'CheckMedicine'
            }
        }, 300);
    }
    
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs) {
    flex-direction:row;
}

:deep(.m-labelInput) {
    width: calc(100% - 10px);
    .el-select {
        width: 100%;
    }
}
:deep(.my-dialog) {
    min-width: 700px;
    .el-dialog__body {
        height: calc(100% - 45px);
        overflow: hidden;
        padding: 20px 30px 20px 20px;
    }
}

:deep(.el-form-item--small .el-form-item__error) {
    bottom: -14px;
    left:0px
}
.dialog-footer {
    padding: 10px 20px;
}
.delete-color {
    color: #f56c6c;
}
.el-table .cell {
    .el-button--small {
    padding: 0px 0px;
}
}
.scope-scheme {
    height: 100%;
    padding: 15px;
    box-sizing: border-box;
    
    .el-tab-pane {
        padding: 0px 0px;
        .c-flex-context {
                padding-left: 15px;
                height: 100%;
                display: flex;
                flex-direction: column;
            // :deep(.c-search) {
            //     display: flex;
            //     padding-bottom: 10px;
            //     justify-content: space-between;
            // }    
            :deep(.c-form) {
                display: flex;
                padding-bottom: 10px;
                justify-content: space-between;

                .c-form-search {
                    display: flex;
                }
            }
            
            :deep(.c-flex-auto) {
                flex: 1;
                height: 0;
                .c-content{
                    height: 100%;
                }
            }
        }
        :deep(.m-flexLaout-tx) {
            .c-tree {
                width: 250px;
                    padding-right: 15px;
                    border-right: 1px solid #eee;
                h4 {
                    padding:0 10px 10px 10px; 
                    border-bottom: 1px solid #eee;
                }
                .span-ellipsis {
                    width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
            .m-flexLaout-ty {
                margin-left: 10px;
            }
        }
    }
    :deep(.el-tabs .el-tabs__header ){
        margin: 0;
    }
    :deep(.el-tree-node__content ){
        height: 28px;
    }
    .i-hover {
        &:hover {
            font-weight: bold;
        }
    }
}
</style>
