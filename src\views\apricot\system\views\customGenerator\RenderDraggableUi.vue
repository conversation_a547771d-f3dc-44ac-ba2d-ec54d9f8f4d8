<template>
    <!-- 走栅栏布局 -->
    <el-col v-if="dataItem.layout" 
        :id="dataItem.navigation ? dataItem.field : null" 
        class="draggable-box"
        :class="{ 'active': activeId === dataItem.id }" 
        @click.stop.native="onClickActiveItem(dataItem)">
        <!-- 显示表单 -->
        <el-form-item v-if="dataItem.layout === 'formItem'" 
            :required="dataItem.required"
            :label="dataItem.titleShow ? dataItem.label : ''" 
            :labelWidth="dataItem.labelWidth || 100" 
            class="form-item">
            <div class="grid-box" :style="dataItem.style"
                :class="{ 'check-input': dataItem.checkStyle, 'not-title': !dataItem.titleShow }">
                <!-- <label v-show="dataItem.titleShow" :style="dataItem.labelStyle">{{ dataItem.label }}</label> -->

                <!-- 判断显示不同的组件 -->
                <div class="c-check-innner" v-if="dataItem.checkStyle" 
                    :class="[dataItem.value ? 'i-start' : 'i-disable']">
                    <i class="i-check"></i>
                </div>
                <el-input v-if="dataItem.tag === 'input'" 
                    v-model="dataItem.value"
                    :readonly="!!dataItem.readonly" 
                    :disabled="!!dataItem.disabled" 
                    :clearable="!!dataItem.clearable"
                    :placeholder="dataItem.placeholder">
                </el-input>

                <!-- 文本域 -->
                <el-input v-else-if="dataItem.tag === 'input-textarea'"
                    v-model="dataItem.value"    
                    type="textarea"
                    :readonly="!!dataItem.readonly" 
                    :disabled="!!dataItem.disabled"
                    :clearable="!!dataItem.clearable" 
                    :placeholder="dataItem.placeholder" 
                    :maxlength="dataItem.maxlength"
                    :rows="dataItem.rows" 
                    resize="none" 
                    :show-word-limit="dataItem.showWordLimit">
                </el-input>

                <el-input-number v-else-if="dataItem.tag === 'input-number'" 
                    v-model="dataItem.value"
                    controls-position="right" 
                    :min="dataItem.min" 
                    :max="dataItem.max" 
                    :step="dataItem.step"
                    :step-strictly="dataItem.stepStrictly" 
                    :readonly="!!dataItem.readonly" 
                    :disabled="!!dataItem.disabled"
                    :clearable="!!dataItem.clearable" 
                    :placeholder="dataItem.placeholder">
                </el-input-number>

                <el-select v-else-if="dataItem.tag === 'select'" 
                    v-model="dataItem.value" 
                    :readonly="!!dataItem.readonly"
                    :disabled="!!dataItem.disabled" 
                    :clearable="!!dataItem.clearable" 
                    :multiple="!!dataItem.multiple"
                    :filterable="!!dataItem.filterable" 
                    :allow-create="!!dataItem.allowCreate"
                    :placeholder="dataItem.placeholder" 
                    value-key="value" 
                    default-first-option>
                    <template v-if="dataItem.options.length">
                        <el-option v-for="item in dataItem.options" 
                            :key="item.value" 
                            :label="item.value"
                            :value="item.value">
                        </el-option>
                    </template>
                </el-select>

                <el-time-picker v-else-if="dataItem.tag === 'time-picker'" 
                    v-model="dataItem.value"
                    :placeholder="dataItem.placeholder" 
                    :picker-options="dataItem.pickerOptions" 
                    :format="dataItem.format"
                    :value-format="dataItem.valueFormat" 
                    :clearable="!!dataItem.clearable" 
                    :readonly="!!dataItem.readonly"
                    :disabled="!!dataItem.disabled">
                </el-time-picker>

                <el-date-picker v-else-if="dataItem.tag === 'date-picker'" 
                    v-model="dataItem.value" align="right"
                    :type="dataItem.type" 
                    :placeholder="dataItem.placeholder" 
                    :format="dataItem.format"
                    :value-format="dataItem.valueFormat" 
                    :clearable="!!dataItem.clearable" 
                    :readonly="!!dataItem.readonly"
                    :disabled="!!dataItem.disabled">
                </el-date-picker>

                <el-radio-group v-else-if="dataItem.tag === 'radio-group'"
                    v-model="dataItem.value"
                    :class="{ 'radio-button': dataItem.optionType === 'button' }"
                    :disabled="!!dataItem.disabled">
                    <template v-if="dataItem.optionType === 'default'">
                        <el-radio v-for="(item, index) in dataItem.options" 
                            :label="item.value" 
                            :key="index">{{ item.value}}</el-radio>
                    </template>
                    <template v-else>
                        <el-radio-button v-for="(item, index) in dataItem.options" 
                            :label="item.value" 
                            :key="index">{{ item.value }}</el-radio-button>
                    </template>
                </el-radio-group>

                <el-checkbox-group v-else-if="dataItem.tag === 'checkbox-group'"
                    :class="{ 'checkbox-button': dataItem.optionType === 'button' }" 
                    v-model="dataItem.value" 
                    :disabled="!!dataItem.disabled">
                    <template v-if="dataItem.optionType === 'default'">
                        <el-checkbox :key="index" v-for="(item, index) in dataItem.options" 
                            :label="item.value">{{ item.value }}</el-checkbox>
                    </template>
                    <template v-else>
                        <el-checkbox-button v-for="(item, index) in dataItem.options" 
                            :label="item.value" 
                            :key="index">{{ item.value }}</el-checkbox-button>
                    </template>
                </el-checkbox-group>

            </div>
        </el-form-item>
        <!-- 显示布局 -->
        <template v-else-if="dataItem.layout === 'layoutItem'">
            <template v-if="dataItem.tag === 'plane-box'">
                <draggable v-model="dataItem.children" 
                    item-key="id" 
                    :animation="340"
                    :group="{ name: 'componentsGroup', type: 'transition-group' }" 
                    class="drag-wrapper">
                    <template #item="{ element: item }">
                        <el-col :span="item.span">
                            <RenderDraggableUi :dataItem="item" 
                                :activeId="activeId" 
                                @onClickRemove="onClickRemove" 
                                @onClickActiveItem="onClickActiveItem">
                            </RenderDraggableUi>
                        </el-col>
                    </template>
                </draggable>

            </template>
            <template v-else>
                <div class="c-box-content"
                    :style="{ 'flex-direction': dataItem.titlePosition === 'left' ? 'row' : 'row-reverse' }">
                    <h5 style="border-right:1px solid #eee">{{ dataItem.label }}</h5>
                    <div class="c-box" :style="{ 'padding-left': dataItem.titlePosition === 'left' ? '8px' : '0px' }">
                        <draggable v-model="dataItem.children" 
                            item-key="id" 
                            :animation="340"
                            :group="{ name: 'componentsGroup', type: 'transition-group' }" 
                            class="drag-wrapper">
                            <template #item="{ element: item }">
                                <el-col :span="item.span">
                                    <RenderDraggableUi :dataItem="item" 
                                        :activeId="activeId" 
                                        @onClickRemove="onClickRemove" 
                                        @onClickActiveItem="onClickActiveItem">
                                    </RenderDraggableUi>
                                </el-col>
                            </template>
                        </draggable>
                    </div>
                </div>
            </template>
        </template>
        <!-- 修饰组件 -->
        <template v-else>
            <div v-if="dataItem.tag === 'divider'" 
                class="xx-divider" 
                :style="{
                    backgroundColor: dataItem.backgroundColor,
                    height: dataItem.height,
                    marginTop: `${dataItem.marginTop}px`,
                    marginBottom: `${dataItem.marginBottom}px`
                }">
            </div>

            <h3 v-if="dataItem.tag === 'title'" 
                :style="{
                    color: dataItem.color,
                    fontWeight: dataItem.fontWeight,
                    textAlign: dataItem.textAlign,
                    fontSize: `${dataItem.fontSize}px`,
                    marginTop: `${dataItem.marginTop}px`,
                    marginBottom: `${dataItem.marginBottom}px`
                }">
                {{ dataItem.label }}
            </h3>
        </template>
        <!-- 选中时，显示删除 -->
        <div class="draggable-action" :class="{ 'active': activeId === dataItem.id }" 
            @click.stop="onClickRemove(dataItem)">
            <i class="el-icon-delete del"></i>
        </div>
    </el-col>
</template>
<script>
import draggable from 'vuedraggable';
export default {
    name: 'RenderDraggableUi',
    components: {
        draggable
    },
    props: ['dataItem', 'activeId'],
    methods: {
        onClickActiveItem (dataItem) {
            this.$emit('onClickActiveItem', dataItem);
        },
        onClickRemove (dataItem) {
            this.$emit('onClickRemove', dataItem);
        }
    }
}
</script>
<style lang="scss" scoped>
.draggable-box {
    position: relative;
    border: 2px solid white;
    cursor: move;

    &:hover {
        border-color: #d2d2d2;
        // .draggable-action {
        //     display: inline-block;
        // }
    }

    &.active {
        border-color: #4dba87;

    }

    .el-form-item {
        margin-bottom: 14px;
    }

    .drag-wrapper {
        min-height: 80px;
        // padding: 20px;
        height: 100%;
    }

    .draggable-action {
        display: none;
        position: absolute;
        top: -5px;
        right: -11px;
        width: 20px;
        height: 20px;
        background: #ffffff;
        text-align: center;
        border-radius: 50%;
        border: 1px solid #eee;
        cursor: pointer;
        z-index: 2;

        &:hover {
            .del {
                color: red;
            }
        }

        &.active {
            display: inline-block;
        }

        .del {
            color: #fd8e8e;
        }
    }
}

.xx-divider {
    display: block;
    height: 1px;
    width: 100%;
    margin: 24px 0;
    background-color: #D2D2D2;
}

.form-item {
    width: 100%;
    margin-right: 0px;

    :deep(.el-select) {
        width: 100%;
        height: inherit;

        // background: inherit;
        .select-trigger {
            height: inherit;
        }

        .el-input {
            height: inherit;
            font-size: inherit;
            color: inherit;

            input {
                height: inherit;
                font-size: inherit;
                color: inherit;
            }
        }

        &.i-set-padding {
            .el-input .el-input__inner {
                padding-left: 40px;
            }
        }
    }

    .grid-box {
        width: calc(100% - 10px);

        &.not-title {
            .el-radio-group {
                margin: 0px;
                margin-top: 6px;

                &.radio-button {
                    margin-top: 1px;
                }
            }

            .el-checkbox-group {
                margin: 0px;
                margin-top: 6px;

                &.checkbox-button {
                    margin-top: 1px;
                }
            }

            :deep(.el-input) {
                .el-input__inner {
                    padding-top: 0px;
                }
            }

            :deep(.el-date-editor),
            :deep(.el-date-editor) {

                .el-icon-time,
                .el-icon-date {
                    padding-top: 3px;
                }
            }

        }

        &.check-input {
            >label {
                margin-left: 40px;
            }

            :deep(.el-input) {
                input {
                    padding-left: 36px;
                }
            }
        }

        .el-radio-group {
            // margin: 10px 0px 0px 10px;

            &.radio-button {
                margin-top: 10px;
            }
        }

        .el-checkbox-group {

            // margin: 10px 0px 0px 10px;
            .el-checkbox {
                height: 32px;
            }

            &.checkbox-button {
                margin-top: 10px;
            }
        }

        :deep(.el-input-number) {
            height: inherit;
            width: 100%;
            // background: inherit;

            .el-input {
                height: inherit;
                font-size: inherit;
                color: inherit;

                input {
                    height: calc(100% - 2px);
                    font-size: inherit;
                    color: inherit;
                }
            }

            .el-input-number__decrease {
                height: 50% !important;
                top: initial !important;
                line-height: initial !important;

                .el-icon-arrow-down {
                    top: calc((100% - 11px) / 2);
                    position: relative;
                }
            }

            .el-input-number__increase {
                height: 50% !important;
                line-height: 0px !important;

                .el-icon-arrow-up {
                    top: calc((100% - 11px) / 2);
                    position: relative;
                }
            }
        }

        :deep(.el-input) {
            width: 100%;
            height: inherit;
            font-size: inherit;
            color: inherit;

            .el-input__inner {
                height: calc(100% - 2px);
                // background: inherit;
                color: inherit; // 打开会跟着标题颜色一样
            }
        }

        :deep(.el-cascader) {
            height: inherit;
            font-size: inherit;
            color: inherit;

            .el-input__inner {
                height: calc(100% - 2px);
                // background: inherit;
                color: inherit; // 打开会跟着标题颜色一样
            }
        }

        :deep(.el-textarea) {
            height: inherit;
            font-size: inherit;

            .el-textarea__inner {
                height: inherit;
                // background: inherit;
            }
        }

        :deep(.el-date-editor) {
            width: 100%;
            height: inherit;

            .el-icon-date,
            .el-icon-time {
                line-height: initial;
                padding-top: 6px;
            }
        }

        :deep(.el-autocomplete) {
            height: inherit;
            width: inherit;
        }

        :deep(.el-color-picker) {
            height: 100%;
            width: 100%;

            .el-color-picker__trigger {
                height: 100%;
                width: 100%;
            }
        }
    }
}

.c-box-content {
    display: flex;
    border: 1px solid #eee;

    h5 {
        background: rgb(244, 248, 251);
        font-weight: 700;
        font-size: 14px;
        color: rgb(44, 62, 80);
        padding: 10px 5px;
        margin: 0;
        width: 30px;
        text-align: center;
        display: flex;
        align-items: center;
        overflow: hidden;
        box-sizing: border-box;
    }

    .c-box {
        flex: 1;
        padding: 12px 15px 12px 10px;

        .el-textarea {
            height: 100%;
        }
    }
}

.c-check-innner {
    z-index: 1;
    height: calc(100% - 3px);
    background-color: #f2f4f8;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: no-drop;
    position: absolute;
    bottom: -1px;
    left: 1px;

    &.i-start {
        .i-check {
            width: 10px;
            height: 10px;
            display: block;
            text-align: center;
            border: 1px solid #c1c1c3;
            position: relative;
            margin-right: 2px;
            background-color: #edf2fc;
            margin: 0px 10px;

            &:after {
                content: "";
                position: absolute;
                width: 6px;
                height: 10px;
                left: 1px;
                top: -4px;
                border-right: 2px solid #2484d1;
                border-bottom: 2px solid #2484d1;
                transform: skew(-30deg, 45deg);
                border-radius: 2px;
            }
        }

        .el-input {
            :deep(input) {
                border-color: #2484d1;
            }
        }
    }

    &.i-disable {
        .i-check {
            width: 10px;
            height: 10px;
            display: block;
            text-align: center;
            margin-right: 2px;
            border: 1px solid #c1c1c3;
            background-color: white;
            margin: 0px 10px;
        }
    }
}
</style>
