<template>
    <div @click="dialogFormVisible = true" class="i-item" v-bind="$attrs">
        <i class="el-icon-more" title="设置" style="font-size: 16px;color:#666"></i>  
       
        <el-dialog v-model="dialogFormVisible" title="菜单配置" class="t-default"
            :close-on-click-modal="false" append-to-body
            destroy-on-close width="700px"
            @close="upDateParentData">
            <div style="max-height: 60vh; overflow: auto;">
                <el-table class="pop-table" :data="form" row-key="name" border ref="popTableRef" height="450">
                    <el-table-column prop="label" :label="('排序')" align="center" width="80">
                        <el-icon class="drag-icon" :size="18">
                            <i class="el-icon-rank"></i>
                        </el-icon>
                    </el-table-column>
                    <el-table-column prop="label" :label="('显示')" align="center" width="80">
                        <template #default="scope">
                            <el-checkbox v-model="scope.row.isShow" @change="onSaveClick(0)"></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column prop="icon" :label="('图标')" width="60" align="center">
                        <template #default="scope">
                            <el-icon :size="18">
                                <i :class="'fa ' + scope.row.icon"></i>
                            </el-icon>
                        </template>
                    </el-table-column>
                    <el-table-column prop="icon" :label="('菜单名称')" min-width="120" align="left">
                        <template #default="scope">
                            <!-- <i :class="'fa ' + scope.row.icon"></i> -->
                            <span style="padding-left: 5px;"> {{ scope.row.label }} </span>
                            
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" plain @click="onSaveClick(1)">保存至全局</el-button>
                    <!-- <el-button-icon-fa icon="fa fa-save" type="primary" :disabled="loading" @click="onSaveClick(0)">保存</el-button-icon-fa> -->
                    <el-button-icon-fa icon="el-icon-close" @click="dialogFormVisible = false" >关闭</el-button-icon-fa>
                </span>
            </template>
        </el-dialog>
    </div>
</template>


<script>
import Sortable from 'sortablejs'
import { deepClone } from "$supersetUtils/function"

import { useUserConfigSaveConfig } from '$supersetResource/js/projects/apricot/useUserConfig.js'

export default {
    emits: ['updateData'],
    props: ['iModuleId', 'formData', 'configKey','modelValue'],
    data () {
        return {
            form: [],
            userInfo: {},
            loading: false,
            dialogFormVisible: false,
            // tableData: [
            //     { name: 'patientInfo', label: '基本信息', icon: 'fa-distribution', isShow: true, },
            //     { name: 'checkInfo', label: '检查信息', icon: 'fa-case-line', isShow: true, },
            //     { name: 'clinicInfo', label: '问诊信息', icon: 'fa-stethoscope-1', isShow: true, },
            //     { name: 'historyInfo', label: '历史报告', icon: 'fa-report-1', isShow: true, },
            //     { name: 'scannerInfo', label: '病例资料', icon: 'fa-material-fill', isShow: true, right: 'ScanData' },
            //     { name: 'injectInfo', label: '注射记录', icon: 'fa-injection', isShow: true, right: 'Inject' },
            //     { name: 'machineInfo', label: '上机记录', icon: 'fa-machine', isShow: true, right: 'Machine' },
            //     { name: 'followInfo', label: '随访预约', icon: 'fa-phone-square', isShow: true, right: 'FollowUp' },
            //     { name: 'criticalInfo', label: '危急值', icon: 'fa-bell-o', isShow: true, right: 'CriticalValue' },
            // ]
        }
    },
    watch: {
        dialogFormVisible (val) {
            if (val) {
                this.form = deepClone(this.formData);
                // console.log(this.formData)
                this.userInfo = this.$store.getters['user/userSystemInfo'];
                this.$nextTick(() => {
                    this.rowDrop()
                })
            }
        },
    },
    methods: {
        // 保存配置数
        useUserConfigSaveConfig: useUserConfigSaveConfig(),
        // 更新父组件数据
        upDateParentData () {
            this.$emit('updateData', this.form);
        },
        //行拖拽
        rowDrop() {
            const popTable = (this.$refs.popTableRef.$el)
            const tbody = popTable.querySelector('tbody')
            Sortable.create(tbody, {
                handle: ".drag-icon",
                animation: 150, // 拖拽延时，效果更好看
                onEnd: (evt) => {
                    const { oldIndex, newIndex } = evt;
                    this.form.splice(newIndex, 0, ...this.form.splice(oldIndex, 1));
                    this.onSaveClick(0)
                }
            })
        },
        async onSaveClick (configType) {
            let params = {
                configKey: this.configKey,
                configValue: JSON.stringify(this.form),
                configType: configType,
                moduleId: this.iModuleId,
                userNo: this.userInfo.sNo
            }
            // this.loading = true;
            await this.useUserConfigSaveConfig(params, this);
            // this.loading = false;
            // this.dialogFormVisible = false;
        },
    }
}
</script>

<style lang="scss" scoped>
.drag-icon {
    cursor: pointer;
}
</style>