import request from '$supersetUtils/request'
import { baseURL } from '$supersetUtils/request'
import axios from 'axios'

// OCR文字提取操作接口 : Ocr Service Controller
export const getTranslate = (data) => {
    console.log(axios.CancelToken.source())
    return request({
        url: window.configs.urls.OCR + '/ocr/translate',
        method: 'POST',
        data,
        cancelToken: axios.CancelToken.source().token
    })
}

export const getOcrContent = (params) => {
    return request({
        url: baseURL.apricot + '/attachments/ocr/content',
        method: 'GET',
        params,
    })
}