/**
 * 子项目路由配置
 */
/*组件引入------------------------------*/

/*import Appointment from '$supersetViews/apricot/appointment/Index.vue'
import Case from '$supersetViews/apricot/case/Index.vue'
import Statistics from '$supersetViews/apricot/statistics/Index.vue'*/

/*------------------------------组件引入*/

/*方法引入------------------------------*/
//添加模块权限的方法
import { moduleListInit } from '$supersetResource/js/tools'
/*------------------------------方法引入*/



// 2. 定义路由
const moduleList = [
  // {
  // 	path: 'appointment', 
  // 	name: 'Appointment',
  // 	component: () => import('$supersetViews/apricot/appointment/Index.vue'),
  // 	meta: {
  // 		name: '预约登记',
  // 		subtitle: 'appointment',
  // 		icon: 'fa-poly-appointment_bak',
  // 	}
  // },
  {
    path: 'appointment1',
    name: 'Appointment1',
    component: () => import('$supersetViews/apricot/appointment1/Index.vue'),
    meta: {
      name: '预约登记',
      subtitle: 'appointment',
      icon: 'fa-poly-appointment_bak',
      menuCode: '001001'
    }
  },
  // {
  // 	path: 'case', 
  // 	name: 'Case',
  // 	component: () => import('$supersetViews/apricot/case/Index.vue'),
  // 	meta: {
  // 		name: '病例管理',
  // 		subtitle: 'caseManagement',
  // 		icon: 'fa-poly-case-management',
  // 	}
  // },
  {
    path: 'consult',
    name: 'Consult',
    /*component: Case,*/
    component: () => import('$supersetViews/apricot/consultMng/Index.vue'),
    meta: {
      name: '问诊管理',
      subtitle: 'consultManagement',
      icon: 'fa-poly-echometer_bak',
      menuCode: '001002'
    }
  },
  {
    path: 'injectMng',
    name: 'injectMng',
    component: () => import('$supersetViews/apricot/injectMng/Index.vue'),
    meta: {
      name: '注射管理',
      subtitle: 'injectManagement',
      icon: 'fa-poly-injection',
      menuCode: '001003'
    }
  },
  {
    path: 'machine',
    name: 'Machine',
    component: () => import('$supersetViews/apricot/machineMng/Index.vue'),
    meta: {
      name: '上机管理',
      subtitle: 'machineManagement',
      icon: 'fa-poly-machine-inspection_bak',
      menuCode: '001004'
    }
  },
  {
    path: 'report',
    name: 'Report',
    component: () => import('$supersetViews/apricot/reportCase/index.vue'),
    meta: {
      name: '报告管理',
      subtitle: 'reportManagement',
      icon: 'fa-poly-report',
      menuCode: '001005'
    }
  },
  // {
  //   path: 'teachCase',
  //   name: 'TeachCase',
  //   component: () => import('$supersetViews/apricot/teachCase/Index.vue'),
  //   meta: {
  //     name: '教学病例',
  //     subtitle: 'teachCase',
  //     icon: 'fa-teach',
  //   }
  // },
  // {
  //   path: 'teachComment',
  //   name: 'TeachComment',
  //   component: () => import('$supersetViews/apricot/teachComment/Index.vue'),
  //   meta: {
  //     name: '教学点评',
  //     subtitle: 'teachComment',
  //     icon: 'fa-poly-medical-report',
  //   }
  // },
  {
    path: 'followUpVisitMng',
    name: 'FollowUpVisitMng',
    component: () => import('$supersetViews/apricot/followUpVisitMng/Index.vue'),
    meta: {
      name: '随访管理',
      subtitle: 'followUpManagement',
      icon: 'fa-poly-follow-up',
      menuCode: '001008'
    }
  },
  {
    path: 'retrieve',
    name: 'Retrieve',
    component: () => import('$supersetViews/apricot/retrieve/Index.vue'),
    meta: {
      name: '综合查询',
      subtitle: 'retrieveSearch',
      icon: 'fa-search-1',
      menuCode: '001009'
    }
  },
  {
    path: 'centralizedPrint',
    name: 'CentralizedPrint',
    component: () => import('$supersetViews/apricot/centralizedPrint/Index.vue'),
    meta: {
      name: '集中打印',
      subtitle: 'centralizedPrint',
      icon: 'fa-printer-picture-text',
      menuCode: '001010'
    }
  },
  // {
  //   path: 'reportAssignment',
  //   name: 'reportAssignment',
  //   component: () => import('$supersetViews/apricot/reportAssignment/Index.vue'),
  //   meta: {
  //     name: '报告分配',
  //     subtitle: 'reportAssignment',
  //     icon: 'fa-report-publish',
  //   }
  // },
  // {
  //     path: 'remoteConsult',
  //     name: 'RemoteConsult',
  //     component: () => import('$supersetViews/apricot/remoteConsult/Index.vue'),
  //     meta: {
  //         name: '会诊管理',
  //         subtitle: 'consultionManagement',
  //         icon: 'fa-ploy-consultation',
  //     }
  // },
  {
    path: 'statistics',
    name: 'Statistics',
    component: () => import('$supersetViews/apricot/statistics/Index.vue'),
    meta: {
      name: '统计',
      subtitle: 'statistics',
      icon: 'fa-poly-statistics-bar_bak',
      menuCode: '001011'
    }
  },
  {
    path: 'airReport',
    name: 'AirReport',
    component: () => import('$supersetViews/apricot/airReport/Index.vue'),
    meta: {
      name: '呼气报告',
      subtitle: 'airReport',
      icon: 'fa-poly-medical-report',
      menuCode: '001015'
    }
  },
  {
    path: 'system',
    name: 'System',
    component: () => import('$supersetViews/apricot/system/Index.vue'),
    meta: {
      name: '系统管理',
      subtitle: 'systemSet',
      icon: 'fa-setting',
      menuCode: '001013'
    },

  },
  {
    path: 'userManage',
    name: 'userManage',
    component: () => import('$supersetViews/authManage/Index.vue'),
    meta: {
      name: '用户管理',
      subtitle: 'user Management',
      icon: 'fa-head-portrait-photo-entrance',
      menuCode: '001012'
    },
  },
  {
    path: 'interface',
    name: 'Interface',
    component: () => import('$supersetViews/apricot/interface/Index.vue'),
    meta: {
      name: '接口管理',
      subtitle: 'InterfaceManage',
      icon: 'fa-configure',
      menuCode: '001014'
    },
  },
]

//路由模块初始化
moduleListInit(moduleList, {
  projectName: 'apricot'
})

export default moduleList
