

// DOM elements and their event listener functions stored in a Map, where the key is the DOM element and the value is an array of corresponding handler functions
const nodeList = new Map();

const isClient = typeof window !== 'undefined' && typeof document !== 'undefined'

// Mouse operations are divided into two stages:
// 1. mousedown - mouse button is pressed
// 2. mouseup - mouse button is released
// Both stages must be completed to trigger a complete click event

// startClick is the Event parameter for the mousedown event handler
let startClick;

if (isClient) {
    // Listen for the mousedown event and set the startClick object
    document.addEventListener('mousedown', function (e) {
        startClick = e;
    });

    // Listen for the mouseup event and loop through the event handler functions
    // However, the execution of the handlers depends on the conditions specified in the createDocumentHandler function
    document.addEventListener('mouseup', function (e) {
        for (const handlers of nodeList.values()) {
            for (const { documentHandler } of handlers) {
                documentHandler(e, startClick);
            }
        }
    });
}

// 生成事件处理函数，供 mouseup 事件调用
function createDocumentHandler(el, binding) {
    // ClickOutside 允许通过参数设置例外的 target，点击该参数对应的 DOM 不会触发处理函数
    let excludes = [];

    // 正常情况，参数是数组形式，直接将该参数数组设置为例外 DOM
    if (Array.isArray(binding.arg)) {
        excludes = binding.arg;
    } else if (binding.arg instanceof HTMLElement) {
        // 容错处理，如果参数是 HTMLElement，将其 push 到 excludes 中
        excludes.push(binding.arg);
    }

    // 返回一个函数，参数是 mouseup 和 mousedown 事件处理函数的 Event 参数
    return function (mouseup, mousedown) {
        // popper（若存在的话，如下拉框等）
        const popperRef = binding.instance.popperRef;

        // mouseup 事件触发的 target
        const mouseUpTarget = mouseup.target;

        // mousedown 事件触发的 target
        const mouseDownTarget = mousedown?.target;

        // 校验情况1：ClickOutside 是否绑定了处理函数
        const isBound = !binding || !binding.instance;

        // 校验情况2：是否存在事件触发的 target
        const isTargetExists = !mouseUpTarget || !mouseDownTarget;

        // 校验情况3：事件触发的 target 是否在 el 内
        const isContainedByEl = el.contains(mouseUpTarget) || el.contains(mouseDownTarget);

        // 校验情况4：事件触发的 target 是否为 el
        const isSelf = el === mouseUpTarget;

        // 校验情况5：事件触发的 target 是否在例外 DOM 数组内
        const isTargetExcluded =
            (excludes.length && excludes.some((item) => item?.contains(mouseUpTarget))) ||
            (excludes.length && excludes.includes(mouseDownTarget));

        // 校验情况6：若存在 popper，事件触发的 target 是否在 popper 内
        const isContainedByPopper =
            popperRef && (popperRef.contains(mouseUpTarget) || popperRef.contains(mouseDownTarget));

        // 若满足以上 6 中校验情况的任意一种，直接跳出，否则执行 ClickOutside 指令绑定的处理函数
        if (
            isBound ||
            isTargetExists ||
            isContainedByEl ||
            isSelf ||
            isTargetExcluded ||
            isContainedByPopper
        ) {
            return;
        }

        binding.value(mouseup, mousedown);
    };
}


// 点击元素外部时触发特定操作，常用于处理点击外部关闭弹出框、下拉菜单或其他元素的需求。
// clickOutside 实现代码
export default {
    name: 'click-outside',
    beforeMount(el, binding) {
        // 当前的 DOM 元素可能存在多个处理好函数
        // 1.确定监听的 DOM 元素中是否存在当前 el，若不存在，初始化为空数组[]
        if (!nodeList.has(el)) {
            nodeList.set(el, []);
        }

        // 将事件处理函数添加到 el 对应的处理函数列表中
        nodeList.get(el).push({
            // 调用 createDocumentHandler 方法返回处理函数，内部会对是否校验各种边界情况，判断是否需要执行 binding.value 绑定的处理函数
            documentHandler: createDocumentHandler(el, binding),
            bindingFn: binding.value,
        });
    },
    updated(el, binding) {
        if (!nodeList.has(el)) {
            nodeList.set(el, []);
        }

        // 获取当前 el 绑定的所有处理函数
        const handlers = nodeList.get(el);

        // 获取更新前的处理函数索引
        const oldHandlerIndex = handlers.findIndex((item) => item.bindingFn === binding.oldValue);

        // 设置新的处理函数，用于替换旧的处理函数
        const newHandler = {
            documentHandler: createDocumentHandler(el, binding),
            bindingFn: binding.value,
        };

        // 若原先存在处理函数，替换；若不存在，添加；
        if (oldHandlerIndex >= 0) {
            // replace the old handler to the new handler
            handlers.splice(oldHandlerIndex, 1, newHandler);
        } else {
            handlers.push(newHandler);
        }
    },
    unmounted(el) {
        // 移除 el 绑定的所有监听事件处理函数
        nodeList.delete(el);
    },
};
