export default {
    VisitedMngPenalConfig:{
        // leftLayoutWidth: '50%',
        tabHistoryReport: true,
        tabPathological: true,
        tabClinicData: true,
        // rightTabPanels: ['HistoryReport', 'Pathological', 'ClinicData'],
        historyReportMatchConditions: {
            hasRecordNo: 0,
            hasNuclearNum: 0,
            hasIdNum: 0,
        },
        
        isShowConfigBtn: true,
        patientInfoBgColor: 'rgba(243, 250, 232, 1)',
        tabBgColor: '#fff',
    },
    searchStyle: [{
            sProp: 'dAppointmentTimeSt',
            sLabel: '预约开始',
            sValue: '',
            sWidth: '100%',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1
        }, {
            sProp: 'dAppointmentTimeEd',
            sLabel: '预约截止',
            sValue: '',
            sWidth: '100%',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1
        }, {
            sProp: 'sRecentDays',
            sLabel: '最近天数',
            sValue: '',
            sWidth: '100%',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1
        }, {
            sProp: 'dReportCommitTimeSta',
            sLabel: '报告开始',
            sValue: '',
            sWidth: '100%',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1
        }, {
            sProp: 'dReportCommitTimeEnd',
            sLabel: '报告截止',
            sValue: '',
            sWidth: '100%',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1
        },
        {
            sProp: 'sDistrictId',
            sLabel: '院区',
            sInputType: 'option',
            sOptionProp: 'districtArrOption',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sMachineryRoomId',
            sLabel: '机房',
            sInputType: 'option',
            sOptionProp: "machineRoomArrOption",
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sProjectId',
            sLabel: '项目',
            sInputType: 'option',
            sOptionProp: "itemsArrOption",
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'text',
            iLayourValue: 4
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'text',
            iLayourValue: 4
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'text',
            iLayourValue: 4
        },
        {
            sProp: 'sName',
            sLabel: '姓名',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'text',
            iLayourValue: 4
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'text',
            iLayourValue: 4
        },

        {
            sProp: 'dFollowUpTime',
            sLabel: '随访日期',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'date-picker',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sVisitState',
            sLabel: '随访状态',
            sWidth: '100%',
            sHeight: '30px',
            iLayourValue: 4,
            sInputType: 'option',
            sOptionProp: 'sVisitStateOptions'
            
        },
        {
            sProp: 'sReporterName',
            sLabel: '报告医生',
            sInputType: 'text',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sInputType: 'option',
            iCustom: 1,
            iLayourValue: 4
        },
        {
            sProp: 'sFollowUpResult',
            sLabel: '随访结果',
            sWidth: '100%',
            sHeight: '30px',
            sInputType: 'text',
            iLayourValue: 8
        },
        { 
            prop: 'sImageNo', 
            label: '影像号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sOutpatientNO', 
            label: '门诊号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sPhysicalExamNo', 
            label: '体检号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sVitisNo', 
            label: '就诊号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sCardNum', 
            label: '社保卡号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sHealthCardNO', 
            label: '健康卡号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
    ],
    // 患者 table 表
    patientTable: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sAlign: 'left',
            sMinWidth: '100px',
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sWidth: '80px',
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sWidth: '80px',
        },
        {
            sProp: 'sVisitStateText',
            sLabel: '随访状态',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sRoomText',
            sLabel: '设备类型',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约日期',
            sAlign: 'left',
            sMinWidth: '150px',
        },
        {
            sProp: 'sFollowUpRecord',
            sLabel: '随访记录',
            sAlign: 'left',
            sMinWidth: '200px',
        },
        // {
        //     sProp: 'dFollowUpTime',
        //     sLabel: '随访日期',
        //     sAlign: 'left',
        //     sMinWidth: '150px',
        // },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sIdNum',
            sLabel: '身份证号',
            sAlign: 'left',
            sMinWidth: '170px',
        },
        {
            sProp: 'sVitisNo',
            sLabel: '就诊号',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sReporterName',
            sLabel: '报告医生',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sAlign: 'left',
            sMinWidth: '110px',
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sAlign: 'left',
            sMinWidth: '110px',
        }
    ],
    // 文本组件配置
    textListConfig: [
        { prop: 'sName', label: '姓名', isShow: true, width: '25%', },
        { prop: 'sSexText', label: '性别', isShow: true, width: '25%' },
        { prop: 'sAge', label: '年龄', isShow: true, width: '25%' },
        { prop: 'sSourceText', label: '来源', isShow: true, width: '25%' },
        { prop: 'sNuclearNum', label: '核医学号', isShow: true, width: '25%' },
        { prop: 'sMedicalRecordNO', label: '病历号', isShow: true, width: '25%' },
        { prop: 'fHeight', label: '身高', isShow: true, width: '25%' },
        { prop: 'fWeight', label: '体重', isShow: true, width: '25%' },
        { prop: 'fRecipeDose', label: '处方剂量', isShow: true, width: '25%' },
        { prop: 'sNuclideText', label: '核素', isShow: true, width: '25%' },
        { prop: 'sTracerText', label: '药物', isShow: true, width: '25%' },
        { prop: 'fBloodSugar', label: '空腹血糖', isShow: true, width: '25%' },
        { prop: 'sInjection', label: '注射要求', isShow: true, width: '50%' },
        { prop: 'sProjectName', label: '检查项目', isShow: true, width: '50%' },
    ],
    // 已随访表格
    visitedTable: [
        {
            sProp: 'dFollowDate',
            sLabel: '随访日期',

        }, {
            sProp: 'sDoctorName',
            sLabel: '随访医生',
        }, {
            sProp: 'sFollowModeText',
            sLabel: '随访方式',
            sAlign: 'center'
        }, {
            sProp: 'sVisitStateText',
            sLabel: '随访状态',
            sAlign: 'center'
        },
        {
            sProp: 'sIsAccordText',
            sLabel: '随访符合',
            sAlign: 'center'
        },
        {
            sProp: 'Actions',
            sLabel: '操作',
            sWidth: '140px',
            sAlign: 'center'
        },
    ],
    DA0: {
        type: 't-x',
        localStorageKey: '202306091025',
        panelConfig: [{
            size: 0,
            minSize: 300,
            name: 'c-left',
            isFlexible: true
        },
        {
            size: '50%',
            minSize: 575,
            // maxSize: window.innerWidth / 4 * 3,
            name: 'c-right',
            isFlexible: false

        }]
    },
}