// 枚举类型变量
// 预约模块
export const appointmentEnum = {
    // 性别		枚举,1:男;2:女;3:其他;4:未知
    sexOptions: [{
        sName: '男',
        sValue: '1',
    }, {
        sName: '女',
        sValue: '2',
    }, {
        sName: '其他',
        sValue: '3',
    }, {
        sName: '未知',
        sValue: '4',
    }],
    // 年龄单位		枚举,Y:岁;M:月;W:周;D:天
    ageUnitOptions: [{
        sName: '岁',
        sValue: 'Y',
    }, {
        sName: '月',
        sValue: 'M',
    }, {
        sName: '周',
        sValue: 'W',
    }, {
        sName: '天',
        sValue: 'D',
    }],
    // 就诊类型 	枚举,1:门诊;2:急诊;3:体检;4:住院;5:外院;9:其他
    visitTypeOptions: [{
        sName: '门诊',
        sValue: '1',
    }, {
        sName: '急诊',
        sValue: '2',
    }, {
        sName: '体检',
        sValue: '3',
    }, {
        sName: '住院',
        sValue: '4',
    }, {
        sName: '外院',
        sValue: '5',
    }, {
        sName: '其他',
        sValue: '9',
    }],
    //  执行状态		枚举,0:未登记；1:登记；2:其他；
    execStateOptions: [{
        sName: '未登记',
        sValue: '0',
    }, {
        sName: '登记',
        sValue: '1',
    }, {
        sName: '其他',
        sValue: '2',
    }],
    //  签到状态		枚举, 布尔值,1:已签到;0:未签到；
    iIsRegisterOptions: [{
        sName: '未签到',
        sValue: '0',
    }, {
        sName: '已签到',
        sValue: '1',
    }, {
        sName: '全部',
        sValue: '',
    }],
}
// 病例管理
export const caseEnum = {
    // 问诊 
    menses: [{
            sName: '正常',
            sValue: '1'
        },
        {
            sName: '异常',
            sValue: '2'
        },
        {
            sName: '推迟',
            sValue: '3'
        },
        {
            sName: '提前',
            sValue: '4'
        },
        {
            sName: '已停经',
            sValue: '5'
        }
    ],
    // 报告
    imgQuality: [
        {
            sName: '优质',
            sValue: '5'
        }, 
        {
            sName: '良好',
            sValue: '4'
        },
        {
            sName: '一般',
            sValue: '3'
        },
        {
            sName: '较差',
            sValue: '2'
        },
        {
            sName: '待评',
            sValue: '1'
        }
    ],
    reportQuality: [
        {
            sName: '优质',
            sValue: '5'
        }, 
        {
            sName: '良好',
            sValue: '4'
        },
        {
            sName: '一般',
            sValue: '3'
        },
        {
            sName: '较差',
            sValue: '2'
        },
        {
            sName: '待评',
            sValue: '1'
        }
    ],
    qualitative: [{
            sName: '阴性',
            sValue: '1'
        },
        {
            sName: '阳性',
            sValue: '2'
        }
    ],
    diagnosticAccord: [{
            sName: '符合',
            sValue: '1'
        },
        {
            sName: '不符合',
            sValue: '2'
        }
    ]
}

// 问诊管理
export const consultEnum = {
    consult: [{
            sName: '全部',
            sValue: ''
        },
        {
            sName: '已问诊',
            sValue: '1'
        },
        {
            sName: '未问诊',
            sValue: '0'
        }
    ]
}

// 报告管理
export const reportEnum = {
    inject: [{
            sName: '全部',
            sValue: ''
        }, {
            sName: '已注射',
            sValue: '1'
        },
        {
            sName: '未注射',
            sValue: '0'
        }
    ],
    machine: [{
            sName: '全部',
            sValue: ''
        }, {
            sName: '已上机',
            sValue: '1'
        },
        {
            sName: '未上机',
            sValue: '0'
        }
    ],
    report: [{
            sName: '全部',
            sValue: ''
        }, {
            sName: '已提交',
            sValue: '1'
        },
        {
            sName: '未提交',
            sValue: '0'
        }
    ],
    approve: [{
            sName: '全部',
            sValue: ''
        }, {
            sName: '已审核',
            sValue: '1'
        },
        {
            sName: '未审核',
            sValue: '0'
        }
    ],
    finalApprove: [{
            sName: '全部',
            sValue: ''
        }, {
            sName: '已复审',
            sValue: '1'
        },
        {
            sName: '未复审',
            sValue: '0'
        }
    ],
    consultation: [{
            sName: '全部',
            sValue: ''
        }, {
            sName: '已发送',
            sValue: '1'
        },
        {
            sName: '未发送',
            sValue: '0'
        }
    ],
    iIsImaging:[
        {
            sName:'全部',
            sValue:''
        },
        {
            sName:'已收图',
            sValue:1
        },
        {
            sName:'未收图',
            sValue:0
        }
    ],
    iIsPrint:[
        {
            sName:'全部',
            sValue:''
        },{
            sName:'已打印',
            sValue:1
        },{
            sName:'未打印',
            sValue:0
        }
    ],
}
// 打印设置各个模块标识
export const systemModuleOption = [
// {
//     sName: '预约登记',
//     sValue: 1,
//     eName: 'APPOINT'
// }, 
{
    sName: '预约登记',
    sValue: 2,
    eName: 'REGISTER'
}, {
    sName: '问诊管理',
    sValue: 3,
    eName: 'INQUISITION'
}, {
    sName: '注射管理',
    sValue: 4,
    eName: 'INJECTION'
}, {
    sName: '上机管理',
    sValue: 5,
    eName: 'MACHINE'
}, {
    sName: '报告管理',
    sValue: 6,
    eName: 'REPORT'
}, {
    sName: '教学病例',
    sValue: 7,
    eName: 'TEACH_CASE'
}, {
    sName: '教学点评',
    sValue: 8,
    eName: 'TEACH_COMMENT'
}, {
    sName: '随访管理',
    sValue: 9,
    eName: 'FOLLOWUP'
}, {
    sName: '综合查询',
    sValue: 10,
    eName: 'RETRIEVE_SEARCH'
}, {
    sName: '集中打印',
    sValue: 11,
    eName: 'CENTRALIZED_PRINT'
}, {
    sName: '统计',
    sValue: 12,
    eName: 'STATISTICS'
}]
// 模板维护
export const templateEnum = {
    classifyOptions: [{
            sName: '报告',
            sValue: 1
        },
        {
            sName: '袋贴',
            sValue: 2
        },
        {
            sName: '随访',
            sValue: 3
        },
        {
            sName: '预约单',
            sValue: 4
        },
        {
            sName: '检查单',
            sValue: 5
        },
        {
            sName: '检查同意书',
            sValue: 6
        },
        {
            sName: '注射单',
            sValue: 7
        },
        {
            sName: '上机单',
            sValue: 8
        }
    ],
    typeOptions: [{
            sName: '纯文',
            sValue: 1
        },
        {
            sName: '图文',
            sValue: 2
        },
        {
            sName: '纯图',
            sValue: 3
        }
    ],
    defaultedOptions: [ {
        sName: '是',
        sValue: 1
    }, {
        sName: '否',
        sValue: 0
    }]
}
// 会诊管理
export const remoteConsultEnum = {
    ReportOptions: [{
            sName: '全部',
            sValue: ''
        },
        {
            sName: '未提交',
            sValue: 0
        },
        {
            sName: '已提交',
            sValue: 1
        }
    ],
    CommitApproveOptions: [{
            sName: '全部',
            sValue: ''
        },
        {
            sName: '未审核',
            sValue: 0
        },
        {
            sName: '已审核',
            sValue: 1
        }
    ],
    ConsultationTypeOptions: [{
            sName: '全部',
            sValue: ''
        },
        {
            sName: '发起会诊',
            sValue: 1
        },
        {
            sName: '接收会诊',
            sValue: 2
        }
    ],
    ConsultationResultOptions: [{
        sName: '全部',
        sValue: ''
    }, {
        sName: '已回传',
        sValue: 1
    }, {
        sName: '未回传',
        sValue: 0
    }]

}
// 呼叫类型
export const callTypeOptions = [{
    sName: '预约',
    sValue: 'ApricotReportAppoint'
}, {
    sName: '问诊',
    sValue: 'ApricotReportConsult'
}, {
    sName: '注射',
    sValue: 'ApricotReportInject'
}, {
    sName: '上机',
    sValue: 'ApricotReportMachine'
}, {
    sName: '报告',
    sValue: 'ApricotReportReport'
}]
// 签到状态
export const iIsRegisterOptions = [
    {
        sValue: '',
        sName: '全部'
    },
    {
        sValue: '1',
        sName: '已签到'
    },
    {
        sValue: '0',
        sName: '未签到'
    }]
    
// 问诊状态
export const iIsConsultOptions = [
    {
        sValue: '',
        sName: '全部'
    },
        {sValue: '1',
        sName: '已问诊'
    },
    {
        sValue: '0',
        sName: '未问诊'
    }]
// 注射状态
export const iIsInjectOptions = [
        {
            sValue: '',
            sName: '全部'
        },
        {
            sValue: '1',
            sName: '已注射'
        },
        {
            sValue: '0',
            sName: '未注射'
        }
        ]
// 上机状态
export const iIsMachineOptions = [{
            sValue: '',
            sName: '全部'
        },
        {
            sValue: '1',
            sName: '已完成'
        },
        {
            sValue: '0',
            sName: '未完成'
        }]

export const recentDayOptions = [
    {
        sName: '今天',
        keyWord: 1,
        dates: [0, 0],
    }, {
        sName: '昨天',
        keyWord: 2,
        dates: [-1, -1],
    }, {
        sName: '近两天',
        keyWord: 3,
        dates: [-1, 0],
    }, {
        sName: '近三天',
        keyWord: 4,
        dates: [-2, 0],
    }, {
        sName: '近七天',
        keyWord: 5,
        dates: [-6, 0],
    }, {
        sName: '明天',
        keyWord: 6,
        dates: [1, 1],
    }, {
        sName: '后两天',
        keyWord: 7,
        dates: [1, 2],
    }, {
        sName: '后三天',
        keyWord: 8,
        dates: [1, 3],
    }, {
        sName: '后七天',
        keyWord: 9,
        dates: [1, 7],
    },
]

// 接收短信用户类型(1-患者;2-开单医生;3-登记医生;4-问诊医生;5-注射医生;6-上机医生;7-报告医生;8-审核医生;9-复审医生)

// 接收角色
export const receiverTypeOptions = [{
        sValue: 1,
        sName: '患者'
    }, {
        sValue: 2,
        sName: '开单医生'
    }, {
        sValue: 3,
        sName: '登记医生'
    }, {
        sValue: 4,
        sName: '问诊医生'
    }, {
        sValue: 5,
        sName: '注射医生'
    }, {
        sValue: 6,
        sName: '上机医生'
    }, {
        sValue: 7,
        sName: '报告医生'
    }, {
        sValue: 8,
        sName: '审核医生'
    }, {
        sValue: 9,
        sName: '复审医生'
    }
]
