import XLSX from 'xlsx';
import * as XLSX_STYLE from 'xlsx-style-vite'

import {
    exportTable, exportReportTable, exportResearchTable, exportFollowupTable
} from '$supersetApi/projects/apricot/appointment/patientInfo.js';

const mixinExportExcel = {
    data () {
        return {};
    },
    methods: {
        // 格式化导出数据；
        formatExcelData (data) {
            // let headerList = this.$refs.mainTable?.showingItemsList || [];
            const cacheList = this.$refs.mainTable?.react?.tableData || [];
            let headerList = cacheList.filter(item => item.isExport);
            const excludePropList = ['_select', '_index', 'sLockUserName', 'tag'];
            headerList = headerList.filter(item => !excludePropList.includes(item.prop));

            const exportData = data.map((row, idx) => {
                // 返回需要导出的数据字段
                let pingData = {
                    '序号': (idx + 1) + ''
                };
                headerList.map(item => {
                    const { label, prop } = item;
                    if (this.FlowStateEnum.includes(prop)) {
                        pingData[label] = row[prop] ? '√' : '';
                    } else if (prop.slice(0, 1) === 'd') {
                        pingData[label] = row[prop] ? moment(row[prop]).format('YYYY-MM-DD HH:mm') : '';
                    } else if (prop == 'iConsultation') {
                        pingData[label] = row[prop] ? '已发送' : '未发送';
                    }
                    else {
                        pingData[label] = row[prop] ?? ''
                    }
                })
                return pingData;
            });
            return { headerList, exportData }
        },
        // 勾选导出
        mxExportSelectionTable () {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请至少选择一项进行导出');
                return;
            }

            // 数据处理
            const { headerList, exportData } = this.formatExcelData(this.multipleSelection);

            // 新建一个工作簿
            let wb = XLSX.utils.book_new();
            let sheetName = 'Sheet1' //excel的表名称
            wb.SheetNames.push(sheetName)

            // 数组生成一个工作表
            let ws = XLSX.utils.json_to_sheet(exportData);

            // 设置每列的宽度（单位：px）
            let wsCols = [{ wch: 60 * 0.12 }]
            headerList.map(item => {
                let width = item.width || item['min-width'];
                width = width ? parseInt(width) : '';
                wsCols.push({ wch: width * 0.12 })
            })
            ws['!cols'] = wsCols;

            // 设置每行的高度（单位：px）
            let wsRows = []
            for (let i in exportData) {
                wsRows.push({ hpx: 30 }) // 其他行高度为 30px
            }
            ws['!rows'] = wsRows

            // 设置单元格样式
            setPubExcel(ws);
            wb.Sheets[sheetName] = ws;

            // 设置表格的全局默认样式
            const defaultCellStyle = {
                font: { name: "Arial", sz: 10, color: { rgb: '000000' } },
            };
            //写入的样式
            let wopts = {
                bookType: 'xlsx',
                bookSST: false,
                type: 'binary',
                cellStyles: true,
                defaultCellStyle: defaultCellStyle,
            }
            let wbout = XLSX_STYLE.write(wb, wopts);
            // let wbout = XLSX.write(wb, wopts);

            this.createAndDownloadExcel(wbout);

            // 设置单元格样式
            function setPubExcel (data) {
                const excludes = ['!ref', '!merges', '!cols', '!rows'];
                const borderStyle = {
                    top: {
                        style: 'thin',
                    },
                    bottom: {
                        style: 'thin',
                    },
                    left: {
                        style: 'thin',
                    },
                    right: {
                        style: 'thin',
                    }
                }
                for (let key in data) {
                    if (excludes.includes(key)) {
                        continue
                    } else {
                        if (key.match(/\d+/g).join('') == '1') {
                            // 设置字体
                            data[key].s = {
                                border: borderStyle,
                                font: {
                                    bold: true
                                },
                                rows: { hpx: 150 }
                            }
                        } else {
                            data[key].s = {
                                border: borderStyle,
                                rows: { hpx: 180 }
                            }
                        }
                    }
                }
            }
        },
        // 生成并下载文件
        createAndDownloadExcel (wbout) {
            // 创建二进制对象并创建url
            const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' });
            const url = URL.createObjectURL(blob);

            // 创建a标签模拟点击进行下载
            const a = document.createElement('a');
            const FileNames = {
                6: '报告管理',
                9: '随访管理',
                10: '综合查询',
            };
            const filename = `${FileNames[this.iModuleId]}_${new Date().getTime()}.xlsx`;
            a.href = url;
            // a.download = filename;
            a.download = filename;
            document.body.appendChild(a);
            a.click();

            // 清除临时对象和URL
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            // 将字符串转换为ArrayBuffer
            function s2ab (s) {
                const buf = new ArrayBuffer(s.length);
                const view = new Uint8Array(buf);
                for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
                return buf;
            }
        },
        // 处理数据并发起导出请求
        mxDoExportTable (condition, sModuleName, aTableHeaderConfig = []) {
            if (!this.tableData.length) {
                this.$message.warning('没有可导出数据！');
                return;
            }
            // 构造数组
            let targetArray = [];
            targetArray.push({
                sName: 'sLoginUserId',
                sValue: this.userInfo.sId,
            });
            Object.keys(condition).map((key) => {
                let dataType = Object.prototype.toString.call(condition[key]);
                if (dataType == '[object Array]') {
                    condition[key].length &&
                        targetArray.push({
                            sName: key,
                            sValue: condition[key],
                        });
                } else if (dataType == '[object Object]') {
                    Object.keys(condition[key]).length &&
                        targetArray.push({
                            sName: key,
                            sValue: condition[key],
                        });
                } else if (![undefined, null, ''].includes(condition[key])) {
                    // 开始日期转换
                    if (['dAppointmentTimeSt', 'dInjectTimeSt', 'dFollowUpTimeSt', 'dMachineTimeSt'].includes(key)) {
                        condition[key] = moment(condition[key]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
                    }
                    // 结束日期转换
                    if (['dAppointmentTimeEd', 'dInjectTimeEd', 'dFollowUpTimeEd', 'dMachineTimeEd'].includes(key)) {
                        condition[key] = moment(condition[key]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
                    }
                    targetArray.push({
                        sName: key,
                        sValue: condition[key],
                    });
                }
            });
            if (this.multipleSelection && this.multipleSelection.length) {
                // 选中导出数据
                let sPatientIds = this.multipleSelection.reduce((str, item) => (str ? str + ',' + `'${item.sId}'` : str + `'${item.sId}'`), '');
                targetArray.push({
                    sName: 'ids',
                    sValue: sPatientIds,
                });
            }
            // 传输对象
            let jsonData = {
                conditions: targetArray,
                orders: {},
                sModuleName: sModuleName,
            };
            // 获取表格排序字段
            const elTableDefaultSortObj = this.$refs.mainTable && this.$refs.mainTable.elTableDefaultSortObj || {};
            // 自定义导出排序字段；
            var sOrderTable = '', sOrderField = '';
            aTableHeaderConfig.find(item => {
                if (item.sProp === elTableDefaultSortObj.prop) {
                    sOrderTable = item.sOrderTable;
                    sOrderField = item.sOrderField;
                }
            })
            // 转换字符串
            const oOrderStringToApiString = { descending: 'desc', ascending: 'asc' };
            // 构造接口排序对象
            const orderObject = {
                iIndex: 1,
                sOrderDbName: '',
                sOrderTable: sOrderTable,
                sOrderField: sOrderField ? sOrderField : elTableDefaultSortObj.prop,
                sDirection: oOrderStringToApiString[elTableDefaultSortObj.order],
            };
            elTableDefaultSortObj.order && (jsonData.orders.orderInfoList = [orderObject]);
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.2)',
            });
            exportTable(jsonData).then((res) => {
                loading.close();
                const { success, data, msg } = res;
                if (success) {
                    this.mxDownloadExcel(data);
                    return;
                }
                this.$message.error(msg);
            }).catch((err) => {
                loading.close();
                console.log(err);
            });
        },
        // 通过模块导出表格
        mxDoExportTableByModule (condition, sModuleName, isAnonymous, filename, otherFieldList) {
            if (!this.tableData.length) {
                this.$message.warning('暂无可导出的数据!');
                return
            }
            let isCombine = true;  // 是否合并导出
            const cacheList = this.$refs.mainTable?.react?.tableData || [];
            let showingItemsList = cacheList.filter(item => item.isExport);
            if (otherFieldList && otherFieldList.length) {
                // 合并其他表格的数据
                showingItemsList = [...showingItemsList, ...otherFieldList];
                isCombine = false;
            }
            let exportExcelFields = [];
            showingItemsList.map((item, index) => {
                const props = item || {};
                if (['_select', '_index', 'sLockUserName', 'tag'].includes(props.prop)) return
                let tempObj = {
                    caption: props.label,
                    engName: props.prop,
                    formatPattern: props.prop.slice(0, 1) === 'd' ? 'yyyy-MM-dd HH:mm' : '',
                    dataType: props.prop.slice(0, 1) === 'd' ? 'date' : '',
                    index: index,
                    isShow: true,
                    // isSort: Number(!!props.sortable),
                    width: parseInt(props.width || props['min-width'])
                }
                props.isExport && exportExcelFields.push(tempObj)
            })
            // 转换查询条件
            Object.keys(condition).map((key) => {
                // 开始日期转换
                if (['dAppointmentTimeSt', 'dInjectTimeSt', 'dFollowUpTimeSt', 'dMachineTimeSt'].includes(key)) {
                    // condition[key] = moment(condition[key]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
                    condition[key] = moment(condition[key]).startOf('day')
                }
                // 结束日期转换
                if (['dAppointmentTimeEd', 'dInjectTimeEd', 'dFollowUpTimeEd', 'dMachineTimeEd'].includes(key)) {
                    // condition[key] = moment(condition[key]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
                    condition[key] = moment(condition[key]).endOf('day')
                }
                if (['', undefined, null].includes(condition[key])) {
                    delete condition[key]
                }
            })
            let jsonData = {
                exportExcelFields,
                filename: filename,
                isColIndex: true,
                isCustomReport: false,
                moduleName: sModuleName,
                queryCondition: condition,
                isAnonymous,
                isCombine
                // sheetName: ''
            }
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.2)',
            });

            const fnObject = {
                report: exportReportTable,
                research: exportResearchTable,
                followup: exportFollowupTable
            }
            fnObject[sModuleName](jsonData).then((res) => {
                loading.close();
                const { success, data, msg } = res;
                if (success) {
                    this.mxDownloadExcel(data);
                    return;
                }
                this.$message.error(msg);
            }).catch((err) => {
                loading.close();
                console.log(err);
            });
        },
        // 下载
        mxDownloadExcel (data) {
            let a = document.createElement('a');
            // a.download = this.items[index].sUploadName;
            let downURL = `${window.configs.urls.apricot}/export/template/download/excel?sFilePath=`;
            a.href = `${downURL}&sFileName=${data.sFileName}&sModuleName=${data.sModuleName}`;
            a.setAttribute('target', '_blank');
            document.body.append(a);
            a.click();
            a.remove();
        },
    },
};
export default mixinExportExcel;
