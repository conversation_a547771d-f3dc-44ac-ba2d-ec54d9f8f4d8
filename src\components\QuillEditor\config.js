const toolNameMap = {
  undo: '撤销键入',
  redo: '恢复撤销',
  bold: '加粗',
  italic: '斜体',
  underline: '下划线',
  strike: '删除线',
  superscript:  '上标',
  subscript: '下标',
  clean: '清除格式',
  brush: '格式刷',
  color: '文字颜色',
  background: '文字背景颜色',
  orderedList: '编号列表',
  bulletList: '无编号列表',
  
  font: '字体',
  size: '字体大小',
  lineheight: '行高',
  addspace: '首行缩进',
  align: '对齐方式',
  symbol: '特殊符号',
  markspace: '转换空格',
  removespace: '清除空格',
  help: '快捷键说明',
  commonSentence: '插入常用语',
  blueTrigger: '书签',
  winTrigger: '最大化'
}
const toolConfigMap = {
  undo: 'undo',
  redo: 'redo',
  brush: 'brush',
  bold: 'bold',
  italic: 'italic',
  underline: 'underline',
  strike: 'strike',
  orderedList: { 'list': 'ordered' },
  bulletList: { 'list': 'bullet' },
  subscript: { 'script': 'sub' },
  superscript: { 'script': 'super' },
  color: { 'color': [] },
  background: { 'background': [] },
  size: { 'size': ['56px', '48px', '34px', '32px', '29px', '24px', '21px', '20px', '18px', false, '14px', '12px', '10px'] },
  lineheight: { 'lineheight': ['1.5', '1.0', '2.0', '2.5', '16px', '24px', '32px', '48px', '56px'] },
  font: { 'font': [ 'SimSun', 'SimHei', 'MicrosoftYaHei', 'KaiTi', 'FangSong', 'Arial', 'Times-New-Roman'] },
  align: { 'align': [] },
  clean: 'clean',
  addspace: 'addspace',
  markspace: 'markspace',
  removespace: 'removespace',
  help: 'help',
  symbol: 'symbol',
  commonSentence: 'commonSentence',
  blueTrigger: 'blueTrigger',
  winTrigger: 'winTrigger',
}

const defaultToolList = Object.keys(toolNameMap).map(k => {
  return {
    isShow: true,
    prop: k,
    label: toolNameMap[k]
  }
})
// console.log(defaultToolList)

const toolIconSrcMap = Object.assign({}, toolNameMap)
// const pickerToolNamesPlus = ['addspace', 'markspace', 'removespace', 'symbol']
const toolNameList = Object.keys(toolIconSrcMap)
// const pickerToolNames2 = Object.keys(toolIconSrcMap).concat(pickerToolNamesPlus)
toolNameList.forEach(key => {
  toolIconSrcMap[key] = `<div class="edui-for-${key} edui-icon " title="${toolNameMap[key]}"></div>`
})

toolIconSrcMap['align'] = {
  '': `<div class="edui-for-justifyleft  edui-icon "></div>`,
  center: `<div class="edui-for-justifycenter  edui-icon "></div>`,
  justify: `<div class="edui-for-justifyjustify  edui-icon "></div>`,
  right: `<div class="edui-for-justifyright  edui-icon "></div>`
}

toolIconSrcMap['script'] = {
  sub: `<div class="edui-for-subscript edui-icon "></div>`,
  super: `<div class="edui-for-superscript edui-icon "></div>`,
}

toolIconSrcMap['list'] = {
  bullet: `<div class="edui-for-insertunorderedlist  edui-icon "></div>`,
  check: `<div class="edui-for-insertunorderedlist edui-icon "></div>`,
  ordered: `<div class="edui-for-insertorderedlist edui-icon "></div>`,
}

toolIconSrcMap['markspace'] = `<div class="edui-for-markspace edui-icon "></div>`
toolIconSrcMap['removespace'] = `<div class="edui-for-removespace edui-icon "></div>`

toolIconSrcMap['list'] = {
  bullet: `<div class="edui-for-insertunorderedlist  edui-icon "></div>`,
  check: `<div class="edui-for-insertunorderedlist edui-icon "></div>`,
  ordered: `<div class="edui-for-insertorderedlist edui-icon "></div>`,
}

toolIconSrcMap['color'] =  `<div class="edui-for-color edui-icon  ql-picker-label" title="${toolNameMap['color']}">
<svg viewBox="0 0 18 18"> 
<line class="ql-color-label ql-stroke" x1="3" x2="15" y1="15" y2="15"></line>
</svg>
</div>`

toolIconSrcMap['background'] =  `<div class="edui-for-background edui-icon  ql-picker-label" title="${toolNameMap['background']}">
<svg viewBox="0 0 18 18"> 
<line class="ql-color-label ql-stroke" x1="3" x2="15" y1="15" y2="15"></line>
</svg>
</div>`

toolIconSrcMap['commonSentence'] = `<div class="edui-for-drafts edui-icon " title="${toolNameMap['commonSentence']}">
</div>`

toolIconSrcMap['blueTrigger'] = `<div class="edui-for-pasteplain edui-icon " title="${toolNameMap['blueTrigger']}">
</div>`

const maxWinSvg = `<svg  t="1637824425355" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10463"><path d="M243.2 780.8v-179.2H153.6v179.2c0 49.28 40.32 89.6 89.6 89.6h179.2v-89.6H243.2zM780.8 153.6h-179.2v89.6h179.2v179.2h89.6V243.2c0-49.28-40.32-89.6-89.6-89.6zM243.2 243.2h179.2V153.6H243.2c-49.28 0-89.6 40.32-89.6 89.6v179.2h89.6V243.2z m537.6 537.6h-179.2v89.6h179.2c49.28 0 89.6-40.32 89.6-89.6v-179.2h-89.6v179.2z" p-id="10464" fill="#518CF1"></path></svg>`

const restoreWinSvg = `<svg t="1637824296192" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6336"><path d="M341.065143 910.189714v-146.285714c0-53.686857-43.885714-97.572571-97.572572-97.572571h-146.285714a48.786286 48.786286 0 0 0 0 97.499428h146.285714v146.285714a48.786286 48.786286 0 1 0 97.499429 0z m-292.571429-617.910857c0 26.916571 21.796571 48.786286 48.713143 48.786286h146.285714c53.686857 0 97.572571-43.885714 97.572572-97.572572v-146.285714a48.786286 48.786286 0 0 0-97.499429 0v146.285714h-146.285714a48.786286 48.786286 0 0 0-48.786286 48.786286z m910.409143 0a48.786286 48.786286 0 0 0-48.713143-48.786286h-146.285714v-146.285714a48.786286 48.786286 0 1 0-97.499429 0v146.285714c0 53.686857 43.885714 97.572571 97.499429 97.572572h146.285714a48.786286 48.786286 0 0 0 48.713143-48.786286z m0 422.765714a48.786286 48.786286 0 0 0-48.713143-48.713142h-146.285714c-53.686857 0-97.572571 43.885714-97.572571 97.572571v146.285714a48.786286 48.786286 0 1 0 97.499428 0v-146.285714h146.285714a48.786286 48.786286 0 0 0 48.786286-48.786286z" fill="#518CF1" p-id="6337"></path></svg>`

toolIconSrcMap['winTrigger'] = `<div class="quill-trigger-win" s-data="max" title="${toolNameMap['winTrigger']}">
${maxWinSvg}
</div>`

const titleConfig = [
  {
    className: '.ql-markspace',
    title: toolNameMap['markspace']
  },
  {
    className: '.ql-removespace',
    title: toolNameMap['removespace']
  },
  {
    className: '.ql-lineheight',
    title: '行高'
  },
  {
    className: '.ql-font',
    title: '字体'
  },
  {
    className: '.ql-size',
    title: '字体大小'
  },
  {
    className: '.ql-list[value="ordered"]',
    title: '编号列表'
  },
  {
    className: '.ql-list[value="bullet"]',
    title: '项目列表'
  },
  {
    className: '.ql-align',
    title: '对齐方式'
  },
  {
    className: '.ql-color',
    title: '字体颜色'
  },
  {
    className: '.ql-background',
    title: '背景颜色'
  },
  {
    className: '.ql-clean',
    title: '清除字体格式'
  },
  {
    className: '.ql-script[value="sub"]',
    title: '下标'
  },
  {
    className: '.ql-script[value="super"]',
    title: '上标'
  },
  {
    className: '.ql-align .ql-picker-item:first-child',
    title: '居左对齐'
  },
  {
    className: '.ql-align .ql-picker-item[data-value="center"]',
    title: '居中对齐'
  },
  {
    className: '.ql-align .ql-picker-item[data-value="right"]',
    title: '居右对齐'
  },
  {
    className: '.ql-align .ql-picker-item[data-value="justify"]',
    title: '两端对齐'
  }

];

export default {
  titleConfig,
  defaultToolList,
  toolConfigMap,
  toolIconSrcMap,
  maxWinSvg,
  restoreWinSvg
}
