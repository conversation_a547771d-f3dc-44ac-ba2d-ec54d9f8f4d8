<template>
    <!-- 项目设置 核医学号规则 -->
    <div class="c-flex-context" >
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa
                    type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    icon="el-icon-refresh"
                    @click="mxGetTableList()">刷新</el-button-icon-fa> 
            </div>
            <div class="c-form-search">
                <div>
                    <el-select v-model="condition.sDeviceTypeId"
                        clearable
                        @change="mxDoSearch">
                        <el-option v-for="(item, index) in optionsLoc.deviceTypeDataOptions"
                            :key="index"
                            :label="item.sDeviceTypeName"
                            :value="item.sId">
                        </el-option>
                    </el-select>
                </div>

                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch" :loading="loading"></el-button-icon-fa>
                </div>
            </div>
        </div>


        <div class="c-flex-auto ">
            <div class="c-content">
                <el-table :data="tableData"
                    ref="mainTable"
                    size="small"
                    id="accessNumRuleTable"
                    @row-dblclick="mxOpenDialog(4, '111')"
                    @row-click="onClickRow"
                    v-if="reRender"
                    :default-sort="{ prop: 'sDeviceTypeName', order: 'ascending' }"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort">
                        <template v-slot="scope"
                            >
                            <template v-if="item.sProp === 'action'">
                                <el-button
                                    link 
                                    type="primary"
                                    size="small"
                                    @click.stop="handleEdit(scope.row)">编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button
                                    link
                                    size="small"
                                    @click.stop="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <!-- <el-divider direction="vertical"></el-divider>
                               <el-button size="small" link class="i-sort">排序
                                    <template #icon>
                                        <Icon name="el-icon-rank" color="">
                                        </Icon>
                                    </template>
                                </el-button> -->
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                <span v-else> 否 </span>
                            </template>
                            <template v-else-if="item.sProp === 'iSerialRepeat'">
                                {{ setSerialRepeatText(scope.row[`${item.sProp}`]) }}
                            </template>
                            <template v-else>
                                {{ scope.row[`${item.sProp}`] }}
                            </template>
                        </template>
                        <!-- <template v-slot:header>
                            <span>{{item.sLabel}}</span>
                            
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer; font-size:14px; margin-left:5px"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog">
            <div class="flex">
                <el-form :model="editLayer.form"
                    ref="refEditLayer"
                    label-width="140px"
                    label-position="right"
                    :rules="rules">
                    <el-col :span="24">
                        <el-form-item prop="sDeviceTypeId" label="设备类型：">
                            <el-select v-model="editLayer.form.sDeviceTypeId"
                                clearable
                                @change="handleChangeDeviceType">
                                <el-option v-for="(item, index) in optionsLoc.deviceTypeDataOptions"
                                    :key="index"
                                    :label="item.sDeviceTypeName"
                                    :value="item.sId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="检查项目：">
                            <el-select v-model="editLayer.form.sItemId"
                                clearable
                                @change="selectedItem">
                                <el-option v-for="(item,index) in optionsLoc.itemDataOptions"
                                    :key="index"
                                    :label="item.sItemName"
                                    :value="item.sId"></el-option>
                            </el-select>
                            <!-- <el-input readonly v-model="editLayer.form.sItemName"
                                placeholder="检查项目名称"></el-input> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="规则名称：" prop="sRuleName">
                            <el-input placeholder="规则名称" v-model="editLayer.form.sRuleName"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="自定义前缀：" prop="sPrefix">
                            <el-input placeholder="自定义前缀" v-model="editLayer.form.sPrefix"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="日期前缀格式：" prop="sDateFormat">
                            <el-select v-model="editLayer.form.sDateFormat"
                                placeholder="请选择"
                                default-first-option
                                clearable>
                                <el-option v-for="item in optionsLoc.sDateFormat"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col> 
                    <el-col :span="24">
                        <el-form-item label="流水号循环规则：" prop="iSerialRepeat">                                
                            <el-select v-model="editLayer.form.iSerialRepeat"
                                placeholder="请选择"
                                clearable>
                                <el-option v-for="item in optionsLoc.serialRepeat"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="Number(item.sValue)"></el-option>
                            </el-select>   
                        </el-form-item>
                    </el-col> 
                    <el-col :span="24">
                        <el-form-item label="流水号长度：" prop="iSerialLength">
                            <el-input-number v-model="editLayer.form.iSerialLength"
                                controls-position="right"
                                :min="1"
                                placeholder="流水号长度"
                                clearable
                                style="width:100%;"></el-input-number>
                        </el-form-item>
                    </el-col> 
                    <el-col :span="24">
                        <el-form-item label="流水号初始值：" prop="initialValue">
                            <el-input-number
                                v-model="editLayer.form.initialValue"
                                controls-position="right"
                                placeholder="流水号初始值"
                                clearable
                                :min="1"
                                style="width:100%;"></el-input-number>
                        </el-form-item>
                    </el-col> 
                    <el-col>
                        <el-form-item label="启   用：" prop="iIsEnable">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="示    例：">
                            <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap; color:green">
                                <span>{{exempleText.pre}}</span>
                                <strong>{{exempleText.cen}}</strong>
                                <span>{{exempleText.next}}</span>
                            </div>  
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer><div  class="dialog-footer">
                <el-button-icon-fa icon="el-icon-check" type="primary" @click="handleSave" :loading="editLayer.loading">保存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Sortable from 'sortablejs'
import { deepClone } from '$supersetUtils/function'
import { transformDate } from '$supersetUtils/function.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
export default {
    name: 'SetAccessNumRule',
    mixins: [mixinTable],
    props: {
    },
    data () {
        var computedlength = (rule, value, callback) => {
            let form = this.editLayer.form;   //iSerialLength
            let max = ''
            if (form.iSerialLength) {
                max = Number('9'.repeat(form.iSerialLength))
            } else {
                max = 0
            }
            if (value == '') {
                callback(new Error('请输入流水号长度'));
            } else if (max < form.initialValue) {
                callback(new Error('初始值必须小于流水号长度最大值'));
            } else {
                callback();
            }
        };
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            rules:{
                sDeviceTypeId: [{required: true, message: '请选择设备类型'}],
                sItemId: [{required: true, message: '请选择检查项目'}],
                sRuleName: [{ required: true, message: '请输入名称' }],
                iCircleType: [{ required: true, message: '请选择类型' }],
                iSerialRepeat: [{ required: true, message: '请选择循环规则' }],
                iSerialLength: [{ required: true, message: '请输入流水号长度' }, { required: true, validator: computedlength, trigger: 'blur' }],
                initialValue: [{ required: true, message: '请输入流水号初始值' }, { required: true, validator: computedlength, trigger: 'blur' }],
            },
            configTable: [
               
                
                {
                    sProp: 'sRuleName', sLabel: '规则名称',
                    sAlign: 'left', sMinWidth: '130px',
                },
                {
                    sProp: 'sDeviceTypeName',
                    sLabel: '设备类型',
                    sAlign: '',
                    sMinWidth: '120px',
                    iSort: 1
                },
                {
                    sProp: 'sItemName',
                    sLabel: '检查项目',
                    sAlign: '',
                    sMinWidth: '120px'
                },
                {
                    sProp: 'sPrefix', sLabel: '自定义前缀',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'sDateFormat', sLabel: '日期前缀格式',
                    sAlign: 'left', sMinWidth: '120px',
                },
                {
                    sProp: 'iSerialRepeat', sLabel: '流水号循环规则',
                    sAlign: 'left', sMinWidth: '120px',
                },
                {
                    sProp: 'iSerialLength', sLabel: '流水号长度',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'initialValue', sLabel: '流水号初始值',
                    sAlign: 'left', sMinWidth: '120px',
                },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '100px',
                },
                {
                    sProp: 'action', sLabel: '操作', 
                    sFixed: 'right', sAlign: 'center', sWidth: '200px',
                }
            ],
            tableData: [],
            reRender: true,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                    sDateFormat: 'yy',
                    iSerialRepeat: 1,
                    iSerialLength: 6,
                    initialValue: 1,
                    sRuleName: ''
                }
            },
            condition: {
            },
            page: { pageCurrent: 1, pageSize: 9999 },
            optionsLoc: {
                serialRepeat: [
                    { sValue: '1', sName: '年循环' },
                    { sValue: '2', sName: '月循环' },
                    { sValue: '3', sName: '日循环' },
                    { sValue: '4', sName: '不循环' },
                ],
                isEnable: [
                    { sValue: '', sName: '全部' },
                    { sValue: '1', sName: '启用' },
                    { sValue: '0', sName: '禁用' },
                ],
                sDateFormat: [
                    { sValue: 'yy' },
                    { sValue: 'yyMM' },
                    { sValue: 'yyMMdd' },
                    { sValue: 'yyyy' },
                    { sValue: 'yyyyMM' },
                    { sValue: 'yyyyMMdd' },
                ],
                deviceTypeDataOptions: [],
                itemDataOptions: []
            },
            exempleText: {},
        }
    },
    watch: {
        'editLayer.form': {
            handler (val) {
                this.exempleText = {
                    pre: '',
                    cen: '',
                    next: ''
                };
                if (val.sPrefix) {
                    // 自定义前缀
                    this.exempleText.pre += val.sPrefix
                }
                // if (val.iItemPrefix && this.selectedNode.sPrefix) {
                //     // 项目项目前缀
                //     this.exempleText.pre += this.selectedNode.sPrefix
                // }
                if (val.sDateFormat) {
                    // 日期格式
                    this.exempleText.cen += transformDate(new Date(), false, val.sDateFormat)
                }
                if (val.iSerialLength && val.initialValue) {
                    // 流水号长度和初始值
                    let zerolen = val.iSerialLength - String(val.initialValue).length;
                    if (zerolen > 0) {
                        let zeroString = '0'.repeat(zerolen);
                        this.exempleText.next += zeroString + val.initialValue
                    } else {
                        this.exempleText.next += val.initialValue;
                    }
                }
            },
            deep: true,
        }
    },
    methods: {
        // 新增
        handleAdd() {
            this.dialogTitle = '新增'
            this.dialogVisible = true;
            this.$nextTick(() => {
                this.$refs.refEditLayer && this.$refs.refEditLayer.resetFields();
                this.editLayer.form = { ... this.defualtVal.editLayer};
            });
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        handleEdit(row) {
            this.getItemData(row.sDeviceTypeId)
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({}, row)
        },
        handleSave() {
            this.editLayer.loading = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)  
                  return
                }
                this.editLayer.loading = false
            })
        },
        setSerialRepeatText (val) {
            let item = this.optionsLoc.serialRepeat.find(_ => val == _.sValue)
            return item ? item.sName : ''
        },
        // 右键事件
        onContextmenu (event, obj) {
            this.rightData = obj
            this.contextmenuShow(event)
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledAccessNumRule({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sRuleName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delAccessNumRule({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        // 如果编辑的是删除的，清空编辑内容
                        this.$refs.refEditLayer && this.$refs.refEditLayer.resetFields();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        /**
         * 保存数据
         */
        saveData (data) {
            let params = deepClone(data);
            delete params.select;
            if (!this.editLayer.form.sId) {
                Api.addAccessNumRule(params).then((res) => {
                    this.editLayer.loading = false;
                    
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.editLayer.form = res.data;
                        this.dialogVisible = false
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return
            }
            Api.editAccessNumRule(params).then((res) => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.dialogVisible = false
                    this.editLayer.form['iVersion'] = res.data.iVersion;
                    this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            Api.getAccessNumRuleData(params).then((res) => {
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.loading = false;
                    // 排序
                    this.$nextTick( ()=>{
                        this.$refs.mainTable.sort('sDeviceTypeName', 'ascending')
                    })
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        autoSort () {
            Api.autoSortAccessNumRule({}).then(res => {
                if (res.success) {
                    this.mxGetTableList();
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        //行拖拽
        rowDrop () {
            const tbody = document.getElementById("accessNumRuleTable").querySelector('.el-table__body-wrapper tbody')
            const _this = this
            let updateFnc = ({ newIndex, oldIndex }) => {
                if (newIndex === oldIndex) return;
                Api.sortAccessNumRule({
                    iIndexOld: _this.tableData[oldIndex].iIndex,
                    iIndexNew: _this.tableData[newIndex].iIndex,
                    sId: _this.tableData[oldIndex].sId,
                    iVersion: _this.tableData[oldIndex].iVersion,
                }).then(res => {
                    if (res.success) {
                        _this.tableData[oldIndex].iVersion += 1
                        _this.tableData[newIndex].iVersion += 1
                        const currRow = _this.tableData.splice(oldIndex, 1)[0]
                        _this.tableData.splice(newIndex, 0, currRow)

                        _this.reRender = false
                        _this.$nextTick(() => {
                            _this.reRender = true
                            this.mxGetTableList(); // 由于其他的数据项item也发送了变化，需要那最新的
                            if (_this.editLayer.form.iVersion) _this.editLayer.form.iVersion += 1
                            _this.$nextTick(() => {
                                new Sortable(document.getElementById("accessNumRuleTable").querySelector('.el-table__body-wrapper tbody'), {
                                    animation: 200,
                                    onEnd: updateFnc
                                })
                                // 赋选中状态
                                this.mxSetSelected()
                            })
                        })
                    }
                })
            }
            new Sortable.create(tbody, {
                handle: ".i-sort",
                animation: 200,
                onEnd: updateFnc
            })
        },


        // 单个规则查询
        queryAccessNumRule (item, node) {
            let jsonData = {
                sDeviceTypeId: item.sParentId ? item.sDeviceTypeId : item.sId,
                sItemId: item.sParentId ? item.sId : ''
            };
            Api.queryAccessNumRule(jsonData).then(res => {
                if (res.success) {
                    if (res.data) {
                        this.$nextTick(() => {
                            // this.editLayer.form = res.data;
                            this.$nextTick(() => {
                                let target = this.tableData.find(item => item.sId === res.data.sId)
                                target && this.$refs.mainTable.setCurrentRow(target);
                            })
                        })
                    } 
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
        // 获取设备类型
        getDeviceTypeData() {
            Api.getDeviceTypeData().then( res=>{
                if(res.success) {
                    this.optionsLoc.deviceTypeDataOptions = res?.data || []
                } 
            }).catch( err=>{
                console.log(err)
            })
        },
        handleChangeDeviceType(val) {
            this.editLayer.form.sItemId = ''
            this.getItemData(val)
        },
        getItemData(val) {
            const params = {
               condition:{
                    sDeviceTypeId: val
               },
                page:{
                    pageCurrent: 1,
                    pageSize: 9999
                }
            }
            Api.getItemData(params).then( res=>{
                if(res.success) {
                    this.optionsLoc.itemDataOptions = res.data || []
                }
            }).catch( err=>{
                console.log(err)
            })
        },
        selectedItem() {
            const target = this.optionsLoc.deviceTypeDataOptions.find( item =>
                item.sId === this.editLayer.form.sDeviceTypeId
            )
            const target1 = this.optionsLoc.itemDataOptions.find( item=> item.sId === this.editLayer.form.sItemId);
            this.editLayer.form.sItemName = target1?.sItemName || '';
            if(this.editLayer.form.sId) return
            this.editLayer.form.sRuleName = target.sDeviceTypeName + '/' + target1.sItemName
        }
    },
    mounted () {
        // this.getTreeData();
        // this.getItemTreeData();
        this.getDeviceTypeData()
        this.$nextTick(() => {
            this.rowDrop()
        })
    },
};
</script>
<style lang="scss" scoped>
.c-form .c-form-search {
    >div{
        width: 240px;
        margin:0 5px;
    }
}
.i-sort {
    font-size: 12px;
}
</style>
