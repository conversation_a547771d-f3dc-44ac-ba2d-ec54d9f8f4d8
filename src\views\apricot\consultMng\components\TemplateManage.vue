<template>
    <el-dialog title="模板管理" append-to-body align-center :close-on-click-modal="false" v-model="dialogVisible"
        class="t-default my-dialog" @open="onOpen" width="1080px">
        <div class="content">
            <el-button v-if="activeName === 'tab2'" type="primary" @click="onClickLinkAdd" 
                style="position:absolute;top: 5px; right: 0;z-index: 1;">
                <el-icon class="el-icon-plus"></el-icon>
                <span>新增</span>
            </el-button>
            <el-tabs v-model="activeName" type="card">
                <el-tab-pane label="模板维护" name="tab1">
                    <div class="tab1" v-loading="loading">
                        <el-table :data="tableData" border height="100%" style="width: 100%">
                            <el-table-column prop="sTemplateName" label="模板名称" min-width="150" show-overflow-tooltip>
                            </el-table-column>
                            <el-table-column prop="sMemo" label="备注" min-width="200" show-overflow-tooltip>
                            </el-table-column>
                            <!-- <el-table-column prop="iIsEnable" label="状态" width="100" align="center">
                                <template v-slot="{row, $index}">
                                    <el-switch @click.stop.native="onChangeEnable(row)"
                                        v-model="row.iIsEnable" :active-value="1" :inactive-value="0"></el-switch>
                                </template>
                            </el-table-column> -->
                            <el-table-column prop="action" label="操作" width="150" align="center" show-overflow-tooltip>
                                <template v-slot="{row, $index}">
                                    <div class="action-item">
                                        <el-button type="primary" link @click.stop="onClickEdit(row)">
                                            <el-icon class="el-icon-edit-outline"></el-icon>
                                            <span>编辑</span>
                                        </el-button>
                                        <el-divider direction="vertical"></el-divider>
                                        <el-button link @click.stop="onClickDel(row, $index)">
                                            <el-icon class="el-icon-delete"></el-icon>
                                            <span>删除</span>
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="模板绑定" name="tab2">
                    <div class="tab1">
                        <el-table :data="linkList" ref="linkRef" border highlight-current-row height="100%" style="width: 100%">
                            <el-table-column prop="sDeviceTypeName" label="设备类型" align="center" width="200"
                                show-overflow-tooltip>
                                 <template v-slot="{row, $index}">
                                    <template v-if="!row.isEdit">{{ row.sDeviceTypeName }}</template>
                                    <el-select v-else v-model="row.sDeviceTypeId" v-select-name="{ formData: row, fields: {sPropName: 'sDeviceTypeName'} }">
                                        <el-option v-for="item in deviceOptions" :label="item.sDeviceTypeName"
                                            :value="item.sId"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column prop="sDeviceTypeName" label="检查项目" align="center" width="200"
                                show-overflow-tooltip>
                                 <template v-slot="{row, $index}">
                                    <template v-if="!row.isEdit">{{ row.sProjectName }}</template>
                                    <el-select v-else v-model="row.sProjectId" v-select-name="{ formData: row, fields: {sPropName: 'sProjectName'} }" clearable>
                                        <el-option v-for="item in optionsLoc.itemsArrOption.filter(o => !row.sDeviceTypeId ? true : row.sDeviceTypeId === o.sDeviceTypeId)" 
                                            :label="item.sItemName"
                                            :value="item.sId"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column prop="iSex" label="性别" align="center" width="110" show-overflow-tooltip>
                                <template v-slot="{row, $index}">
                                    <template v-if="!row.isEdit">{{ getSexName(row.iSex) }}</template>
                                    <el-select v-else v-model="row.iSex" v-select-name="{ formData: row, fields: {sPropName: 'sSex'} }">
                                        <el-option v-for="item in optionSex" :label="item.label"
                                            :value="item.value"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column prop="sCustomTemplateId" label="模板名称" align="center">
                                <template v-slot="{row, $index}">
                                    <template v-if="!row.isEdit">{{ row.sTemplateName }}</template>
                                    <el-select v-else v-model="row.sCustomTemplateId" v-select-name="{ formData: row, fields: {sPropName: 'sTemplateName'} }" >
                                        <!-- @change="row.sId && onClickRelation(row.sId, row.sCustomTemplateId, row.iSex)" -->
                                        <el-option v-for="item in tableData" :label="item.sTemplateName"
                                            :value="item.sId"></el-option>
                                    </el-select>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center" width="150">
                                <template v-slot="{row, $index}">
                                   <div class="action-item">
                                        <el-button type="primary" v-if="row.isEdit" link @click="onClickLinkSave(row)">
                                            <el-icon class="fa fa-save"></el-icon>
                                            <span>保存</span>
                                        </el-button>
                                        <el-button type="primary" v-else link @click="onClickLinkEdit(row)">
                                            <el-icon class="el-icon-edit-outline"></el-icon>
                                            <span>编辑</span>
                                        </el-button>
                                        <el-divider direction="vertical"></el-divider>
                                        <el-button v-if="row.isEdit && row.sId" link @click="onCancelClick(row)">
                                            <el-icon class="el-icon-close"></el-icon>
                                            <span>取消</span>
                                        </el-button>
                                        <el-button v-else link @click.stop="onClickLinkDel(row, $index)">
                                            <el-icon class="el-icon-delete"></el-icon>
                                            <span>删除</span>
                                        </el-button>
                                    </div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <template v-slot:footer>
            <div class="dialog-footer">
                <div style="float: left;">
                    <el-button type="primary" plain @click="onClickExport">
                        <el-icon class="el-icon-download"></el-icon>
                        <span>导出配置</span>
                    </el-button>
                    <el-button type="primary" plain @click="onClickImport">
                        <el-icon class="el-icon-upload2"></el-icon>
                        <span>导入配置</span>
                    </el-button>
                    <input type="file" ref="fileInput" @change="onFileInputChange" style="display: none;" />
                </div>

                <el-button-icon-fa v-if="activeName === 'tab1'" type="primary" _icon="fa fa-stethoscope-1"
                    @click="onClickAddTemplate">添加模板</el-button-icon-fa>

                <el-button-icon-fa _icon="fa fa-close-1" @click="$emit('update:modelValue', false)">关闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
    <AddTemplateDialog v-model="generatorVisible" :editRow="editRow" @handleSave="handleSave"></AddTemplateDialog>
</template>
<script>
import AddTemplateDialog from './AddTemplateDialog.vue'
import { getDeviceTypeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import {  useGetItemData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'

import { getDevicesRelationTemplate, getCustomTemplate, setDeviceRelationTemplate, disableCustomTemplate, 
    delCustomTemplate, addCustomTemplate, addDeviceRelationTemplate, editDeviceRelationTemplate ,delDeviceRelationTemplate } from '$supersetApi/projects/apricot/case/consult.js'
export default {
    components: {
        AddTemplateDialog
    },
    props: {
        modelValue: {
            type: Boolean,
            default: () => { return false }
        }
    },
    emits: ['update:modelValue'],
    data () {
        return {
            activeName: 'tab1',
            generatorVisible: false,
            optionSex: [
                { value: 0, label: '通用' },
                { value: 1, label: '男' },
                { value: 2, label: '女' }
            ],
            deviceOptions: [],
            tableData: [],
            editRow: {},
            loading: false,
            linkList: [],
            optionsLoc: {
                itemsArrOption: []
            },
            splicingStrings: '',
            originItem: {},
        }
    },
    computed: {
        dialogVisible: {
            get: function () {
                return this.modelValue
            },
            set: function (val) {
                this.$emit('update:modelValue', val)
            }
        }
    },
    methods: {
        onClickLinkAdd() {
            let tempObj = {
                sId: undefined,
                sDeviceTypeId: undefined,
                sProjectId: undefined,
                sCustomTemplateId: undefined,
                iSex: undefined,
                sDeviceTypeName: undefined,
                sProjectName: undefined,
                sTemplateName: undefined,
                sSex: undefined,
                isEdit: true
            }
            this.linkList.unshift(tempObj);
        },
        onClickLinkEdit(row) {
            this.originItem = { ...row }
            row.isEdit = true;
        },
        onCancelClick(row) {
            Object.keys(this.originItem).map(key => {
                row[key] = this.originItem[key]
            })
            row.isEdit = false;
            this.originItem = {};
        },
        onClickLinkSave(row) {
            let jsonData = { ...row };
            if(!row.sDeviceTypeId){
                this.$message.warning('请选择设备类型！');
                return
            }
            if(row.iSex===undefined || row.iSex === null){
                this.$message.warning('请选择性别！');
                return
            }
            if(!row.sCustomTemplateId){
                this.$message.warning('请选择模板！');
                return
            }
            let originArr = this.linkList.filter(o => o.sId !== row.sId).map(item => {
                return '' + item.sDeviceTypeId + (item?.sProjectId || null) + item.iSex + item.sCustomTemplateId;
            });
            this.splicingStrings = '' +  row.sDeviceTypeId + (row?.sProjectId || null) + row.iSex + row.sCustomTemplateId;
            if(originArr.includes(this.splicingStrings)) {
                this.$message.error('存在重复关联！');
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '保存中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            if(jsonData.sId) {
                editDeviceRelationTemplate(jsonData).then(res => {
                    loading.close()
                    if(res.success) {
                        delete row.isEdit;
                        this.$message.success(res.msg);
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(() => {
                    loading.close()
                });
                return
            } 
            addDeviceRelationTemplate(jsonData).then(res => {
                loading.close()
                if(res.success) {
                    this.$message.success(res.msg);
                    this.getDevicesRelationTemplate();
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                loading.close()
            });
            
        },
        onClickLinkDel(row, index) {
            if(!row.sId) {
                this.linkList.splice(index, 1);
                return
            }
            this.$confirm('确认删除关联吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                let loading = this.$loading({
                    lock: true,
                    text: '删除中，请稍等',
                    background: 'rgba(0, 0, 0, 0.1)'
                });
                let { sId } = row;
                delDeviceRelationTemplate({ sId }).then(res => {
                    loading.close()
                    if(res.success) {
                        this.$message.success(res.msg);
                        this.getDevicesRelationTemplate();
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(() => {
                    loading.close()
                })
            })
        },
        getSexName (sex) {
            const findItem = this.optionSex.find(item => item.value == sex);
            if (findItem) {
                return findItem.label;
            }
            return ''
        },
        getCustomTemplate () {
            const params = {
                condition: {},
                page: {
                    pageSize: 500,
                    pageCurrent: 1
                }
            }
            this.loading = true
            getCustomTemplate(params).then(res => {
                this.loading = false
                if (res.success) {
                    this.tableData = res.data.recordList || [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(()=>{
                this.loading = false
            })
        },
        // 获取设备列表
        getDeviceTypeData () {
            getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.deviceOptions = res?.data || [];
                    return
                }
                this.$success.error(res.msg);
            })
        },
        // 获取设备关联
        getDevicesRelationTemplate () {
            const params = {
                condition: {
                    iIsEnable: 1,
                },
                page: {
                    pageSize: 500,
                    pageCurrent: 1
                }
            }
            getDevicesRelationTemplate(params).then(res => {
                if (res.success) {
                    this.linkList = res.data.recordList || [];
                    let targetItem = this.linkList.find(item => {
                        const tempString= item.sDeviceTypeId + (item?.sProjectId || null) + item.iSex + item.sCustomTemplateId;
                        return tempString === this.splicingStrings;
                    });
                    if(targetItem) {
                        this.$refs.linkRef.setCurrentRow(targetItem);
                        this.splicingStrings = '';
                    }
                    return
                }
                this.$success.error(res.msg)

            })
        },
        useGetItemData,
        onOpen () {
            if (!this.deviceOptions.length) {
                this.getDeviceTypeData();
            }
            if(!this.optionsLoc.itemsArrOption.length) {
                this.useGetItemData();
            }
            this.getCustomTemplate();
            this.getDevicesRelationTemplate();
        },
        onClickRelation (sDeviceTypeId, sCustomTemplateId, iSex) {
            const params = {
                sCustomTemplateId,
                sDeviceTypeId,
                iSex
            }
            setDeviceRelationTemplate(params).then(res => {
                if (res.success) {
                    this.$message.success('关联成功');
                    return;
                }
                this.$message.error('关联失败');
            })
        },
        // 改变状态
        onChangeEnable (row) {
            const params = {
                iIsEnable: row.iIsEnable,
                sId: row.sId
            }
            disableCustomTemplate(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg)
                    return
                }
                this.$message.error(res.msg)
                row.iIsEnable = row.iIsEnable === 1 ? 0 : 1
            })
        },
        onClickDel (row, index) {
            this.$confirm('确定要删除 ' + row.sTemplateName + ' 吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                delCustomTemplate({ sId: row.sId }).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg)
                        this.tableData.splice(index, 1)
                        return
                    }
                    this.$message.error(res.msg)
                })
            })

        },
        onClickEdit (row) {
            this.editRow = row;
            this.generatorVisible = true;
        },
        onClickAddTemplate () {
            this.editRow = {};
            this.generatorVisible = true;
        },
        // 模板保存后的处理
        handleSave () {
            this.getCustomTemplate();
        },
        onClickExport () {
            const json = JSON.stringify(this.tableData);
            const fileBlob = new Blob([json], { type: 'text/json' });
            const linkEl = document.createElement('a');
            linkEl.href = URL.createObjectURL(fileBlob);
            linkEl.download = '问诊模板_' + new Date().toLocaleString() + '.json';
            linkEl.click();
        },
        onClickImport () {
            this.$refs.fileInput.click()
        },
        onFileInputChange (e) {
            const files = e.target.files
            const file = files[0]
            e.target.value = ''
            if (!file) return

            const fileReader = new FileReader();
            fileReader.onload = (event) => {
                const result = event.target.result
                let resultObj = []
                try {
                    resultObj = JSON.parse(result)
                } catch (error) {
                    this.$message.error('导入出错！')
                    return
                }
                this.forEachTemplate(resultObj);
            }
            fileReader.readAsText(file)

        },
        forEachTemplate (resultObj) {
            if (resultObj) {
                const len = resultObj.length;
                let loadCount = 0;

                resultObj.forEach(item => {
                    const params = {
                        iIsEnable: item.iIsEnable,
                        sMemo: item.sMemo,
                        sTemplateJson: item.sTemplateJson,
                        sTemplateName: item.sTemplateName,
                    }
                    addCustomTemplate(params).then(res => {
                        loadCount += 1;
                        if (!res.success) {
                            setTimeout(() => {
                                this.$message.info(res.msg)
                            }, 0);
                        }
                        if (loadCount == len) {
                            this.getCustomTemplate();
                            this.$message.success('导入执行完成！')
                        }
                    }).catch(() => {
                        loadCount += 1;
                        if (loadCount == len) {
                            this.getCustomTemplate();
                            this.$message.info('导入执行完成！')
                        }
                    });
                })
            } else {
                this.$message.info('无导入数据')
            }
        }

    }
}
</script>
<style lang="scss" scoped>
.content {
    overflow: hidden;
    width: 100%;
    height: 500px;

    .tab1 {
        width: calc(100% - 5px);
        height: calc(100% - 20px);
        margin: 10px 0 10px 5px;
        display: flex;
        border: 1px solid var(--el-border-color-lighter);
        border-bottom: none;
        box-sizing: border-box;
    }
}
.dialog-footer {
    overflow: hidden;
}
</style>
