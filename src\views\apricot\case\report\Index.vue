<template>
    <div class="case-report-index" style="height: 100%;overflow: hidden;">
        <ReportProcessLayout class="i-Layout"
            :class="{'m-vertical-btn-nogrey': menuDisabled}"
            ref="ReportProcessLayout"
            :isShowInnerList="isShowInnerList">
            <template v-slot:businessMenu>
                <!--菜单面板------------------------------------------------------------>
                <MenuPanel  :popperClass="menuPanelClass" :foldPanelWidth="480">
                    <template #settingMenu>
                        <MenuCache :iModuleId="iModuleId" :formData="businessMenus" 
                        :configKey="'ReportMngReportEditMenuConfig'" @updateData="updateMenuList" />
                    </template>
                    <template v-for="(item, idx) in [{name: 'menu', groups: showBusinessMenus}, {name: 'foldMenu', groups: foldBusinessMenus}]"
                        v-slot:[item.name] :key="idx">  
                        <template v-for="(menu, index) in item.groups" :key="index + menu.name">
                            <!-- 报告预览 -->
                            <ReportPreviewBtn v-if="menu.mark=='isPopover'"
                                v-show="!menu.iIsHide"
                                :buttonMsg="menu"
                                :propParams="{ patient: patientInfo,  isBatch: false, idKey:'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"></ReportPreviewBtn>

                            <!-- 打印 -->
                            <ReportPrintBtn v-else-if="menu.mark=='isPrint'"
                                v-show="!menu.iIsHide"
                                :buttonMsg="menu"
                                :propParams="{ patient: patientInfo,  isBatch: false, idKey:'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }">
                                <el-button class="m-vertical-btn t2">
                                    <svg class="fa" aria-hidden="true">
                                        <use :xlink:href="'#fa-print-text' "></use>
                                    </svg>
                                    <label>打印报告</label>
                                </el-button>
                            </ReportPrintBtn>

                            <!-- 报告下载 -->
                            <ReportPDFDown v-else-if="menu.mark == 'isPDF'"
                                v-show="!menu.iIsHide"
                                :buttonMsg="menu"
                                :propParams="{ patient: patientInfo,  isBatch: false, idKey:'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"></ReportPDFDown>
                            
                            <!-- 刻录 -->
                            <CdburnBtn v-else-if="menu.mark == 'isBurn'"
                                v-show="!menu.iIsHide"
                                :buttonMsg="menu"
                                :patientInfo="patientInfo"></CdburnBtn>

                            <!-- 第三方链接 -->
                            <ThirdLinkBtn v-else-if="menu.mark == 'isClinic'"
                                v-show="!menu.iIsHide"
                                :buttonMsg="menu"></ThirdLinkBtn>
                            
                            <ApplyListInfo v-else-if="menu.mark == 'isApplyOrder'"
                                v-show="!menu.iIsHide"
                                :isReportModule="true"
                                :buttonMsg="menu"
                                :patientInfo="patientInfo"></ApplyListInfo>

                            <!-- 图像导出 -->
                            <ExportBtn v-else-if="menu.mark == 'exportBtn'" 
                                :buttonMsg="menu"
                                :patientInfo="patientInfo">
                                </ExportBtn>

                            <!-- 发送图像 -->
                            <SendImage v-else-if="menu.mark == 'sendImage'" 
                                :buttonMsg="menu"
                                :multipleSelection = [patientInfo]
                                :isBatch="false">
                                </SendImage>

                            <!-- 修改报告医生、修改审核医生 -->
                            <ChangeDoctor v-else-if="['isChangePractice', 'isChangeWrite', 'isChangeAuditor'].includes(menu.mark)"
                                v-show="!menu.iIsHide"
                                :buttonMsg="menu"
                                :patientInfo="patientInfo"
                                :reportInfo="form"
                                @refresh="queryCaseReport"></ChangeDoctor>

                            <!-- 状态回传 -->
                            <SendMessage v-else-if="menu.mark == 'sendMessage'" 
                                :buttonMsg="menu"
                                :patientInfo="patientInfo">
                            </SendMessage>

                            <el-button v-else
                                v-show="!menu.iIsHide"
                                class="m-vertical-btn t2"
                                :class="{ 't-border': menu.isBorder, 'm-vertical-text': menu.isFold }"
                                :disabled="menu.isReadOnly || menuDisabled"
                                @click="businessMenuClick(menu.fn)">
                                <svg class="fa"
                                    aria-hidden="true">
                                    <use :xlink:href="'#' + menu.icon"></use>
                                </svg>
                                <label>{{ menu.name }}</label>
                            </el-button>
                        </template>
                    </template>

                    <template v-slot:close>
                      <el-button plain class="m-vertical-btn t2" @click="closeProcess">
                        <i class="fa fa-share " style="font-size: 24px;"></i>
                          <label class="relative pt-1">关闭</label>
                      </el-button>
                    </template>
                    
                </MenuPanel>
                <!------------------------------------------------------------菜单面板-->
                <el-dialog title="提示"
                    v-model="isShowCloseWarning"
                    append-to-body
                    class="my-tip-dialog"
                    top="30vh"
                    width="420px">
                    <div class="relative  ">
                        <i class="el-icon-warning"
                            style="color: #E6A23C;font-size: 18px;margin-right: 5px;"></i>
                        <span>报告未保存，是否保存报告？</span>
                    </div>
                    <template #footer>
                        <el-button-icon-fa icon="el-icon-check"
                            type="primary"
                            @click="onClosePanelAndSave">是</el-button-icon-fa>
                        <el-button-icon-fa icon="el-icon-close"
                            @click="onClosePanelAndNoSave">否</el-button-icon-fa>
                        <el-button
                            @click="isShowCloseWarning = false">取 消</el-button>
                    </template>
                </el-dialog>
            </template>
            <template v-slot:businessEditor>
                <!--报告书写------------------------------------------------------------>
                <div class=" flex flex-col  h-full" style="background: var(--theme-bg);">
                  <div class="m-flexLaout-tx u-autoHeight">
                    <div class="c-item-patent-01 g-flexChild items-center">
                      <div class="c-item-01 flex  items-center">
                        <el-button link @click="isShowLeftPart = !isShowLeftPart" :disabled="auxiliaryPanel.reportTemplate.isShow">
                          <i v-if="isShowLeftPart" class="block fa fa-arrow-left text  "></i>
                          <i v-else class="block fa fa-arrow-left text transform rotate-180"></i>
                        </el-button>
                        <el-button-icon-fa icon="fa fa-report-template" class="t-defult" size="small" @click="showReportTemplate"
                          plain>范文模板</el-button-icon-fa>
                        <!-- <el-button class="t-defult" size="small"
                          @click="dialog.lesionDescribe = true" plain>+ 关键病灶</el-button> -->
                        <QualityControlRating ref="ImageQualityControlRating" :dialogParams="{title: '影像评级', type: 1}" :reportInfo="form" 
                            @onContinueSubmitStep="onContinueSubmitStep"></QualityControlRating>
                        <QualityControlRating ref="ReportQualityControlRating" :dialogParams="{title: '报告评级', type: 2}" :reportInfo="form" 
                            @onContinueSubmitStep="onContinueSubmitStep"></QualityControlRating>

                        <el-button-icon-fa v-auth="'report:report:testValueModify'" icon="fa fa-distribution" class="t-defult" size="small" @click="openInspectionSet"
                          plain>检验值</el-button-icon-fa>

                        <template v-for="(items, index) in elementConfigData.ApricotReportEditor1">
                          <div class="quali-tx ml-4" :key="index" v-if="items.sProp === 'sQualitative'">
                            <el-radio-group v-model="qualityForm.sQualitative">
                              <el-radio v-for="item in optionsLoc.qualitative" :key="item.sValue" :label="item.sValue"
                                @change="handleChangeSelect('sQualitative', item)">{{ item.sName }}</el-radio>
                            </el-radio-group>
                          </div>
                        </template>
                      </div>
                      <div class="c-item-01 flex items-center">
                        
                        <span v-if="lockInfo.sLockUserName" class="i-lump mr-1"><i class="fa fa-lock-lump" style="color:#E6A23C"></i>
                          【{{ lockInfo.sLockUserName }}】</span>

                        <!-- 收藏 -->
                        <div class="mx-1">
                          <EnshrinePatient   
                            :buttonMsg="{ }"
                            :patient="patientInfo"
                            @callBack="(val) => {patientInfo.iCollect = val.iCollect;patientInfo.sCollectionTag = val.sCollectionTag; patientInfo.sCollectionTagId = val.sCollectionTagId}"></EnshrinePatient>
                        </div>
                      </div>

                    </div>
                    <div v-if="elementReportForm && elementReportForm.length" class="i-textarea-actions">
                      <div class="i-actions">
                        <div class="theme" title="更换背景色">
                            <i class="fa fa-palette" style="position:absolute;top: 2px;font-size:20px;"></i>
                            <span style="opacity: 0;">
                                <el-color-picker size="small" v-model="themeColor" show-alpha :predefine="predefineColors"
                                    @active-change="changesTheme">
                                </el-color-picker>
                          </span>
                        </div>
                        <div class="i-actions-item" title="更换风格" @click="showCombinedEditor">
                          <i class="fa" :class="isCombined ? 'fa-file-text' : 'fa-file-text-o'"></i>
                        </div>
                        <div class="i-actions-item" title="更换方向" @click="handleEditorLayout()">
                          <i class="fa fa-rows" :class="isVertical ? 'fa-columns' : 'fa fa-rows'"></i>
                        </div>
                        <div class="i-actions-item" title="全屏" @click="handleFullScreen(true)">
                          <i class="fa fa-full-screen"></i>
                        </div>

                        <div class="i-actions-item" title="显示/隐藏右侧列表" @click="onClickCollapRightPart">
                          <i v-if="!isShowInnerList" class="block fa fa-arrow-left text  "></i>
                          <i v-else class="block fa fa-arrow-left text top-0.5 transform rotate-180"></i>
                        </div> 
                      </div>

                    </div>
                  </div>
                  <div class="g-flexChild m-flexLaout-ty" :class="{ 'm-editorArea-full': isFullScreen }">
                    <div v-if="isFullScreen" style="overflow:hidden;">
                      <div class="isFullScreen-action">
                        <span title="更换背景色">
                            <i class="fa fa-palette" style="position:absolute;top: 0px;font-size:20px;"></i>
                            <span style="opacity: 0;">
                                <el-color-picker size="small" v-model="themeColor" show-alpha :predefine="predefineColors"
                                    @active-change="changesTheme">
                                </el-color-picker>
                            </span>
                        </span>
                        <span title="更换风格">
                          <i class="fa" :class="isCombined ? 'fa-file-text' : 'fa-file-text-o'" @click="showCombinedEditor"></i>
                        </span>
                        <span title="更换方向">
                          <i class="fa fa-rows" style="font-size:15px" :class="isVertical ? 'fa-columns' : 'fa fa-rows'"
                            @click="handleEditorLayout(true)"></i>
                        </span>
                        <span title="取消全屏">
                          <i class="fa fa-exit-full-screen" @click="handleFullScreen"></i>
                        </span>

                      </div>
                    </div>
                    <div class="g-flexChild u-border-bottom m-full-parent  " style="background: rgb(245, 245, 245);">
                    
                      <!--富文本编辑器------------------------------------->
                      <CombinedEditor class="c-combinedEditor-box" v-if="isCombined" :class="[disabled ? 'i-disabled' : '']"
                        :dataArray="elementReportForm" v-model:sProcessRecord="form.sProcessRecord"
                        v-model:sInspectSee="form.sInspectSee" v-model:sDiagnosticOpinion="form.sDiagnosticOpinion" ref="CombinedEditor"
                        storageKey="ReportIndexEditor" :iModuleId="iModuleId" :disabled="disabled" :style="{ backgroundColor: themeColor }"
                        @input="combinedEditorInput">
                      </CombinedEditor>
                      <DragAdjust :dragAdjustData="DA1" v-if="DA1.visible" v-show="!isCombined" id="mainEditorBox">
                        <template v-for="(item, index) in elementReportForm" v-slot:[item.name]>
                            <div class="c-box-content" :key="index">
                              <h5>
                                <span>
                                    {{item.sLabel}} 
                                    <span v-if="isEditContent">*</span>
                                    <span v-else>&nbsp;</span>
                                </span>
                              </h5>
                              <div class="c-box" :style="{ backgroundColor: themeColor }">
                                <quill-editor v-model="form[item.sProp]"
                                    :domParams="{idx:index, superClass: 'm-full-parent'}"
                                    :ref="oEditorRefNames[item.sProp]" :disabled="disabled"
                                    storageKey="ReportIndexEditor" :iModuleId="iModuleId"
                                    @focus="onFocusQuill(index, oEditorRefNames[item.sProp])"
                                    @blur="onBlurQuill(index, oEditorRefNames[item.sProp])">
                                </quill-editor>
                              </div>
                            </div>
                        </template>
                      </DragAdjust>

                      <!-------------------------------------富文本编辑器-->
                    </div>
                  </div>
                  <div v-if="isShowFormEnd" class="c-auxiliaryArea">
                    <div class="">
                      <div v-for="(item, index) in (elementConfigData.ApricotReportFormEnd || []).filter(i => !i.iIsHide || i.isShow)"
                        class="item-doc-infor" :key="index" :span="item.iLayourValue">
                        <span>{{ item.sLabel + '：' }}</span>
                        <span class="name-time">
                          {{ item.sProp.slice(0, 1) === 'd' ? transformDate(form[item.sProp]) : form[item.sProp] }}
                        </span>
                      </div>
                    </div>
                    <div class="flex flex-none items-center">
                      <el-button-icon-fa :disabled="!isShowSaveBtn" icon="fa fa-save" type="primary" @click="() => onSave()">
                        保存
                      </el-button-icon-fa>
                    </div>
                  </div>
                </div>
                <!-- ----------------------------------------------------------报告书写 -->
            </template>
            <template v-slot:patientList>
              <div class=" relative w-full h-full ">
                <slot name="patientList"></slot>
              </div>
            </template>
            <template #essayTemplate>
                <!--报告模板---------------------------------------->
                <div class="c-reportTemplate"
                    :class="{ 's-show': auxiliaryPanel.reportTemplate.isShow }">
                    <ReportEssayTemplate
                        v-model="auxiliaryPanel.reportTemplate.isShow"
                        :reportDisabled="disabled"
                        :elementReportForm="elementReportForm"
                        :editorPenalDA="DA1"
                        :themeColor="themeColor"
                        :patientInfo="patientInfo"
                        :highlightArray="highlightArray"
                        @closeClick="hideReportTemplate"
                        @clickAssign="onClickAssign"></ReportEssayTemplate>
                </div>
                <!----------------------------------------报告模板-->
            </template>
        </ReportProcessLayout>
        
        <PrintTemDialog v-model:dialogVisible="visibleTemplate"></PrintTemDialog>
        <!-- 修改痕迹 -->
        <ModifyTrace v-model:dialogVisible="dialog.modifyTrace"></ModifyTrace>

        <!-- 备份记录 -->
        <BackupsRecord v-model:dialogVisible="dialog.backupsRecord" 
            :currentSaveReport="staticReportContent"
            @restoreReport="restoreReport"></BackupsRecord>

        <!-- 关联图像 -->
        <MergeCase v-model:dialogVisible="dialog.mergeCase"
            :moduleName="moduleName"></MergeCase>

        <!-- 患者信息修改 -->
        <PatientInfoModifi :dialogVisible="dialog.d_PatientInfoModifi_v"
            :iModuleId="iModuleId"
            :patientInfo="patientInfo"
            @closeDialog="closePatientInfoModifi"></PatientInfoModifi>

        <!-- +检验数据 -->
        <InspectionSet v-model:dialogVisible="d_InspectionSet_visible"
            :paramsId="inspectionSetsId"
            @addInspectData="onAddInspectData"
            @closeDialog="closeInspectionSet"></InspectionSet>

        <!-- 图像导入 -->
        <ImageImport v-model="dialog.imageImport" :patientInfo="patientInfo"></ImageImport>

        <!-- 打印记录 -->
        <PrintRecord v-model:dialogVisible="d_printRecord_v"
            :patientInfo="patientInfo"></PrintRecord>

        <!-- 打印设置 -->
        <PrintSet :dialogVisible="d_printSet_v" :iModuleId="iModuleId" @closeDialog="d_printSet_v = false"></PrintSet>

        <SignatureVerification v-model:dialogVisible="d_signatureV_v" :patientInfo="patientInfo" :sReportId="form.sId"></SignatureVerification>

        <!-- 流程控制 -->
        <FlowRecord v-model:dialogVisible="dialog.d_FlowRecord_v" :patientInfo="patientInfo"></FlowRecord>

        <ReportSetting v-model="dialog.ReportSetting_Dialog"></ReportSetting>


        <el-dialog title="云签章"
            v-model="isShowCloudSignature"
            append-to-body
            :close-on-click-modal="false"
            :before-close="beforeCloseCloudSign"
            class="my-tip-dialog t-default my-dialog"
            align-center
            width="420px">
            <div v-loading="qRCodeLoading">
                <div v-if="isShowDirectionPage" class="h-xs flex flex-col justify-center items-center">
                    <i class="fa fa-paper-sign mb-4" style="font-size: 5rem;color: #f1f1f1;"></i>
                    <span class="text-lg underline cursor-pointer" @click="jumpToDirection">请前往授权页面进行授权！</span> 
                </div>
                <template v-else>
                    <div class="m-2 flex justify-center sign-type" v-if="mxIsOpenPinCA">
                        <div class="mr-8 cursor-pointer" :class="{'active': mxReportCloudSignLoginType == 1}" @click="mxClickCloudSignLoginType(1)">扫码签名</div>
                        <div class="cursor-pointer" :class="{'active': mxReportCloudSignLoginType == 2}"  @click="mxClickCloudSignLoginType(2)">密码签名</div>
                    </div>
                    <div style="height: 360px;overflow:hidden;">
                        <template v-if="mxReportCloudSignLoginType == 1">
                            <div class="qrcode_img"><img width="100%"
                                    :src="`data:image/jpg;base64,${qrCode.qrCodeUrl}`"></div>
                            <div style="text-align: center">{{expirationTip}}请打开手机“{{signAppName}}”APP扫一扫</div>
                            <div class="codes_tips">
                                <span v-if="qrCode.iDurateTime > 0">二维码有效期：<b>{{qrCode.iDurateTime}}</b>S</span>
                                <span v-if="qrCode.iDurateTime == 0">
                                    二维码已过期，请点击
                                    <el-button type="primary"
                                        plain
                                        @click="getCloudQRCode">刷新</el-button>
                                </span>
                            </div>
                        </template>
                        <form v-if="mxReportCloudSignLoginType == 2 && mxIsOpenPinCA" class="block m-4">
                            <!-- 用户名 -->
                            <div class="mt-12">
                                <el-input v-model="userInfo.userNo"
                                    disabled
                                    clearable
                                    size="large">
                                    <template #prefix>
                                        <div class="inline-block px-0">
                                            <Icon name="el-icon-user"
                                                :strokeWidth="4"
                                                size="20"
                                                color="#999" />
                                        </div>
                                    </template>
                                </el-input>
                            </div>
                            <!-- 密码 -->
                            <div class="mt-4">
                                <el-input v-model="mxPinForm.sPass"
                                    show-password
                                    size="large"
                                    type="password"
                                    @keyup.enter="mxClickJiangMenPin">
                                    <template #prefix>
                                        <div class="inline-block px-0">
                                            <Icon name="el-icon-lock"
                                                :strokeWidth="4"
                                                size="20"
                                                color="#999" />
                                        </div>
                                    </template>
                                </el-input>
                            </div>
                            <div class="text-center mt-12">
                                <el-button size="large" type="primary" style="width: 100%;" @click="mxClickJiangMenPin">登录</el-button>
                            </div>
                        </form>
                    </div>
                </template>
            </div>
        </el-dialog>
        <el-dialog title="电子签章"
            v-model="isShowSignStatus"
            :close-on-click-modal="false"
            :before-close="beforeCloseSign"
            append-to-body
            class="my-tip-dialog  t-default my-dialog"
            align-center
            width="500px">
            <div style="padding:20px 15px 50px 15px; text-align: left; font-size: 18px">
                <div>
                    <i v-if="signSussess == 2"
                        style="color:#538af7"
                        class="el-icon-edit-outline"></i>
                    <i v-if="signSussess == 1"
                        style="color:#67c23a"
                        class="el-icon-success"></i>
                    <i v-if="signSussess == 0"
                        style="color:#f56c6c"
                        class="el-icon-error"></i>
                    <span>&nbsp;{{messageToStatus}}</span>
                    <span v-if="signSussess == 2"
                        class="loading">
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </div>

            </div>
        </el-dialog>
        <el-dialog 
            title="请选择阴阳性" 
            append-to-body 
            :close-on-click-modal="false" 
            v-model="sQualiParams.visible" 
            width="450px"
            top="25vh"
            class="t-default my-dialog">
            <div style="padding:20px 30px 30px">
                <el-radio-group
                    v-model="qualityForm.sQualitative">
                    <el-radio v-for="item in optionsLoc.qualitative"
                        :key="item.sValue"
                        :label="item.sValue"
                        @change="handleChangeSelect('sQualitative',item)">{{item.sName}}</el-radio>
                </el-radio-group>
        </div>
        </el-dialog>

        <UKeyDialog v-model="d_UKeyDialog_v" @handleUKeySign="mxHandleUKeySign" :mxBusinessType="mxBusinessType"></UKeyDialog>

        <KIPDialog v-model="d_KIPDialog_v" ref="KIPDialog" :patientInfo="patientInfo" @onKIPAfterSubmitStep="onKIPAfterSubmitStep"></KIPDialog>
    </div>
</template>

<script> 

import FormStyle from '$supersetViews/components/FormStyle.vue'
import ReportProcessLayout from './ReportProcessLayout.vue'
import CombinedEditor from '@/components/plugins/other/CombinedEditor.vue'
import ReportSetting from '@/views/apricot/reportCase/components/ReportSetting.vue'

import { getOnlineConfig, saveOnlineConfig, compareAndModifyMenuList } from '@/utils'
import { useUserConfigQueryByKey } from '$supersetResource/js/projects/apricot/useUserConfig.js';


import { deepClone } from '$supersetUtils/function'
import { menuGrouping, menuPartitioning, transformDate } from '$supersetResource/js/tools'
import {
    getStoreNameByRoute, mixinDictionaryGroup, mixinElementConfigs,
    openWebReadImgOrRebuild, mixinPrintPreview
} from '$supersetResource/js/projects/apricot'
import mixinJiangMen from './mixins/mixinJiangMen.js'
import mixinUKeySign from './mixins/mixinUKeySign.js'
import Configs from './configs/index.js'
import onKeyCodeEvent from '$supersetResource/js/projects/apricot/scannerKeyCodeEvent.js';

// 接口
import Api from '$supersetApi/projects/apricot/case/report.js'
import ApiCase from '$supersetApi/projects/apricot/case/index.js'
import ApiAssist from '$supersetApi/projects/apricot/assistServe/index.js'
import { getBPMSetFindKeys } from '$supersetApi/projects/apricot/system/bpmSet.js'
// 枚举
import { caseEnum } from '$supersetResource/js/projects/apricot/enum.js'

import { getLoginCertId, setQRCodeAuthId, getQRCodeAuthId } from '$supersetUtils/auth'

import { createNamespacedHelpers } from 'vuex'
import { cloneDeep } from 'lodash-es'

let { mapMutations } = createNamespacedHelpers('apricot/report_module')

export default {
    mixins: [mixinDictionaryGroup, mixinElementConfigs,  mixinPrintPreview, mixinUKeySign, mixinJiangMen],
    name: "Report",
    props: {
        moduleName: {
            type: String,
            default: 'ReportMng'
        },
        currentProcess: {
            default: ''
        },
    },
    emits: ['closeClick'],
    components: {
        FormStyle,
        ReportProcessLayout,
        CombinedEditor,
        ReportSetting,
        MenuCache: defineAsyncComponent(() => import('./MenuCache.vue')),
        ReportEssayTemplate: defineAsyncComponent(() => import('./ReportEssayTemplate.vue')),
        ModifyTrace: defineAsyncComponent(() => import('./ModifyTrace.vue')),
        BackupsRecord: defineAsyncComponent(() => import('./BackupsRecord.vue')),
        MergeCase: defineAsyncComponent(() => import('./MergeCase')),
        InspectionSet: defineAsyncComponent(() => import('./inspectionSet.vue')),
        PatientInfoModifi: defineAsyncComponent(() => import('$supersetViews/apricot/common/PatientInfoModifi.vue')),
        CdburnBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/CdburnBtn.vue')),
        ReportPreviewBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPreviewBtn.vue')),
        ReportPrintBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPrintBtn.vue')),
        
        ThirdLinkBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ThirdLinkBtn.vue')),
        ApplyListInfo: defineAsyncComponent(() => import('$supersetViews/apricot/components/ApplyListInfo.vue')),

        ReportPDFDown: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPDFDown.vue')),
        ChangeDoctor: defineAsyncComponent(() => import('$supersetViews/apricot/components/ChangeDoctor.vue')),
        EnshrinePatient: defineAsyncComponent(() => import('$supersetViews/apricot/components/EnshrinePatient.vue')),
        ImageImport: defineAsyncComponent(() => import('$supersetViews/apricot/components/ImageImport.vue')),
        PrintRecord: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintRecord.vue')),
        PrintSet: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintSet.vue')),
        SendMessage: defineAsyncComponent(() => import('$supersetViews/apricot/case/report/SendMessage.vue')),

        SignatureVerification: defineAsyncComponent(() => import('$supersetViews/apricot/components/SignatureVerification.vue')),
        PrintTemDialog: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintTemDialog.vue')),
        ExportBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ExportBtn.vue')),
        SendImage: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/SendImage.vue')),
        FlowRecord: defineAsyncComponent(() => import('$supersetViews/apricot/components/FlowRecord.vue')),
        QualityControlRating: defineAsyncComponent(() => import('./QualityControlRating.vue')),
        KIPDialog: defineAsyncComponent(() => import('./KIPDialog.vue')),
        UKeyDialog: defineAsyncComponent(() => import('./UKeyDialog.vue')),
    },
    data () {
        return {
            iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
            isEnshrine: false, // 是否收藏
            disabled: true,  // 报告是否可编辑
            menuDisabled: false,
            oEditorRefNames: {
                sProcessRecord: 'myProcessRecord',  
                sInspectSee: 'myInspectSee', 
                sDiagnosticOpinion: 'myDiagnosticOpinion' 
            },
            dialog: {    // 弹窗显隐控制参数
                modifyTrace: false,
                backupsRecord: false,
                mergeCase: false,
                lesionDescribe: false,
                d_PatientInfoModifi_v: false,
                elecSignature: false,
                imageImport: false,
                d_FlowRecord_v: false,
                ReportSetting_Dialog: false,

            },
            form: {},  // 表单数据
            iTeachCase: 0,
            rules: {}, // 表单规则校验
            auxiliaryPanel: {
                reportTemplate: {
                    isShow: false,
                    isOpen: false,
                    showTree: true
                },
            },
            dA1Animation: false,
            DA1: {
                type: "t-y",
                localStorageKey: "202101121006",
                visible: true,
                panelConfig: [],
                defaultSize: [],
            },
            optionsLoc: {   // 下拉框数组
                imgQuality: caseEnum.imgQuality,
                reportQuality: caseEnum.reportQuality,
                qualitative: caseEnum.qualitative,
                diagnosticAccord: caseEnum.diagnosticAccord,
                ApricotReportNotMale: [],
                ApricotReportNotFemale: []
            },
            businessMenus: [], // 业务按钮数组
            MngIndexPenalConfig: [],
            configs: Configs, // 本地配置数据
            countDown: null,
            staticReportContent: {},  //获取到的报告内容，用于比较用户是否修改
            focusQuillEleRef: '',
            iIsRelease: 0, // 是否发布
            isEditContent: false,
            lockInfo: {},
            isCombined: false,
            isVertical: true,
            isFullScreen: false,
            isQuillRender: {},
            highlightArray: [],
            isShowCloseWarning: false,
            qualityForm: {},
            loadingContainer: null,
            d_InspectionSet_visible: false, // 检验窗口可见性
            isShowCloudSignature: false, // 云签章弹出二维码
            qRCodeLoading: false,// 云签章二维码加载
            qRCodeTimer: null,// 云签章二维码倒计时定时器
            qrCode: { iDurateTime: 0 }, // 云签章二维码倒计时
            isTokenExpiration: 0, // 令牌有没有过期  重新扫码= 1，表示过期
            signStatus: { signSavedStatus: false, signAuditedStatus: false, signReAuditedStatus: false, signCommitedStatus: false },// 签名是否成功
            signBusinessType: null,// 签章业务类型  1保存  2审核  3复审 4提交
            signSwitch: {},  // 保存 提交 审核 复审 签章开关  =1需要签章
            expirationTip: '',
            cascaderShowed: true,
            clientParams: {
                needVerifyLicence: null,//阅图是否需要授权
                verificationStatus: null, // 授权状态
                clientId: null,
            },
            isShowSignStatus: false, // 和正弹出框
            messageToStatus: '', // 和正弹出框提示内容
            signSussess: 2,  // 和正是否签章成功, 1成功 0失败 2等待中初始状态
            isShowQualityControlPopover: false, // 是否显示质控popover
            d_printRecord_v: false,
            d_signatureV_v: false,
            d_printSet_v: false,
            themeColor: localStorage.getItem('reportTheme')?localStorage.getItem('reportTheme'):'#cee7d1', //主题颜色
            predefineColors:[ 
                '#CCE8CF',
                '#C7EDCC',
                '#DCE2F1',
                '#E9EBFE',
                '#EAEAEF',
                '#E3EDCD',
                '#cee7d1',
                '#CFE8CC',
                '#FFF2E2',
                '#FDE6E0',
                '#FFFFFF',
            ],
            isShowFormEnd: false,
            sQualiParams: {
                visible: false,
                steps: null, // 1保存  2审核
            },
            isRightPartCollapsed: false,
            auditBtns: [], 
            bakReport: {},
            hasImageQuality: false,  // 是否有影像评估
            hasReportQuality: false,  // 是否有报告评估
            d_KIPDialog_v: false,
            isShowDirectionPage: false,  //签章是否开启页面重定向
            sRedirectUrl: '', // 重定向地址
        };
    },
    watch: {
        elementReportForm: {
            handler (val) {
                this.$nextTick(() => {
                    if (val && val.length) {
                        this.setQuillLayout();
                        return
                    }
                })
            },
            immediate: true
        },
        form: {
            handler (val) {
                if (this.isEditContent || !Object.keys(val).length) {
                    return
                }
                let staticReportContent = this.staticReportContent;
                let arr = [
                    { sName: 'sProcessRecord' },
                    { sName: 'sInspectSee' },
                    { sName: 'sDiagnosticOpinion' }
                ];
                arr.forEach(item => {
                    if (staticReportContent[item.sName] != this.form[item.sName] && this.isQuillRender.editorFocus) {
                        this.isEditContent = true;
                    }
                })
                if (this.isEditContent) {
                    console.log('编辑了!')
                    // // 锁定报告
                    // this.lockReport();
                    this.setIntervalOnSave();
                }
            },
            deep: true,
        },
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
        patientInfo () {
            const module = getStoreNameByRoute(this.$route.name);
            let info = this.$store.getters['apricot/' + module + '/patientInfo'];
            this.bakReport = {};
            return info || {};
        },
        inspectionSetsId () {
            let sIds = {}
            if (Object.keys(this.patientInfo).length) {
                sIds = {
                    sProjectId: this.patientInfo.sProjectId,
                    sReportId: this.patientInfo.reportId,
                    sPatientId: this.patientInfo.sId
                }
            }
            // 无报告id时获取报告后取id
            if(!sIds.sReportId) {
                sIds.sReportId = this.form.reportId
            }
            return sIds
        },
        isFixedWostation() {
            return this.$store.getters['user/workStation'].stationTypeCode === this.iModuleId.toString()
        },
        showBusinessMenus () {
            let arr = this.businessMenus.filter( (item) => {
                return !item.iIsHide && !item.isFold && (item.rights ? this.$auth[item.rights] : true)
            });
            arr = arr.concat(this.auditBtns);
            return menuGrouping(arr);
        },
        foldBusinessMenus () {
            let arr = this.businessMenus.filter( (item) => {
                return !item.iIsHide && item.isFold && (item.rights ? this.$auth[item.rights] : true)
            });
            // return menuPartitioning(arr);
            return [ ...arr ];
        },
        menuPanelClass () {
            if (this.menuDisabled) {
                return 'i-reportFoldMenuPanel m-vertical-btn-nogrey';
            }
            return 'i-reportFoldMenuPanel';
        },
        pageSetting: {
          get() {
            let obj = this.$store.getters['user/personalOnlineStorage'];
            let settings = obj[this.iModuleId]['ReportCaseIndexReportSetting'];
            return settings || {}
          }
        },
        elementReportForm () {
            if (!this.elementConfigData.ApricotReportForm) {
                return []
            }
            return this.elementConfigData.ApricotReportForm.filter(item => {
                return !item.iIsHide
            });
        },
        signAppName() {
            let temp =  this.$store.state.user.signatureAppName
            if(temp) {
               return temp 
            } else {
                return ''
            }
        },
         isShowInnerList: {
          get() {
            if (this.pageSetting.hasOwnProperty('isShowInnerList')) {
              return !!this.pageSetting.isShowInnerList 
            } else {
              const local = window.localStorage.getItem('oAutoSaveTime') || '{}'
              try {
                const jsonObj = JSON.parsed(local)
                return jsonObj.isShowInnerList              
              } catch (error) {
                
              }
            }
            
            return false
          },
          set(val) {
            const dataObj = cloneDeep(this.pageSetting)
            if (dataObj.hasOwnProperty('isShowInnerList')) {
              dataObj.isShowInnerList = val ? 1 : 0 
              saveOnlineConfig.call(this, dataObj, 0, 
                this.iModuleId, 'ReportCaseIndexReportSetting', this.userInfo.sId)

            } else {
              const local = window.localStorage.getItem('oAutoSaveTime') || '{}'
              try {
                const jsonObj = JSON.parsed(local)
                jsonObj.isShowInnerList = val ? 1 : 0 
                window.localStorage.setItem('oAutoSaveTime', JSON.stringify(jsonObj))
              } catch (error) {
                console.error(error)
              }
            }
            
          }
        },

        isShowLeftPart: {
          get() {
            return !this.$store.getters['apricot/report_module/infoLayerMenuCollapse'];
          },
          set(val) {
            this.$store.commit('apricot/report_module/setInfoLayerMenuCollapse', val ? 'false': 'true' )
          }
        },
        isShowSaveBtn() {
          return this.auditBtns.find(item => item.name == '保存(S)' && !item.isReadOnly)
        }
    },
    methods: {
        transformDate: transformDate,
        ...mapMutations([
            'setPatientInfo'
        ]),
        changesTheme(val) {
            this.themeColor = val
            localStorage.setItem('reportTheme',val)
        },
        // 按钮点击事件
        businessMenuClick (fnName) {
            if (this[fnName]) {
                this[fnName]();
            }
        },
        // 关闭面板
        closeProcess () {
            if (this.isEditContent) {
                this.isShowCloseWarning = true;
                return
            }
            this.unlockReport()
            setTimeout(() => {
                this.$emit('closeClick')
            }, 300);
        },
        onClosePanelAndSave () {
            this.onSave();
        },
        onClosePanelAndNoSave () {
            this.unlockReport()
            setTimeout(() => {
                this.$emit('closeClick')
            }, 300);
        },
        handleChangeSelect (type, val) {
            this.handleChangeQuality(type, val)
        },
        // 发送会诊
        onSendConsultation () {
            let params = {
                sPatientId: this.patientInfo.sId
            }
            Api.sendConsultation(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg)
                    this.patientInfo['iConsultation'] = 1
                    this.setPatientInfo({
                        patientInfo: this.patientInfo
                    })
                    this.queryCaseReport(true)
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 取消会诊
        onCancelConsultation () {
            let params = {
                sPatientId: this.patientInfo.sId
            }
            Api.cancelConsultation(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg)
                    this.patientInfo['iConsultation'] = 0
                    this.setPatientInfo({
                        patientInfo: this.patientInfo
                    })
                    this.queryCaseReport(true)
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },

        // 修改阴阳性，质控  userInfo
        handleChangeQuality (type, val) {
            let jsonData = {}
            jsonData.sPatientId = this.patientInfo.sId
            if (type === 'sQualitative') {
                jsonData.sQualitative = val ? val.sValue : '',
                jsonData.sQualitativeText = val ? val.sName : ''
                
            }

            Api.changeQualitativeOrMore(jsonData).then(res => {
                if (res.success) {
                    // this.$message.success(res.msg)
                    if (type === 'sQualitative') { 
                        if(this.sQualiParams.visible && this.sQualiParams.steps == 1 ) {
                            this.onSave(false)
                        }
                        if(this.sQualiParams.visible && this.sQualiParams.steps == 2 ) {
                            this.requestauditReport(1)
                        } 
                        this.sQualiParams.visible = false
                        this.sQualiParams.steps = null
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 弹出二维码
        showCloudQRCode () {
            clearInterval(this.qRCodeTimer)
            this.isShowCloudSignature = true
            this.qRCodeTimer = setInterval(() => {
                if (this.qrCode.iDurateTime == 0 || !this.isShowCloudSignature) {
                    clearInterval(this.qRCodeTimer)
                    return
                }
                this.qrCode.iDurateTime--
            }, 1000)
        },
        // 关闭二维码
        closeCloudQRCode() {
            this.isShowCloudSignature = false
            // clearInterval(this.qRCodeTimer)
            this.qrCode.iDurateTime = 0
        },
        // 关闭云签章弹框前
        beforeCloseCloudSign () {
            this.$confirm('签名未成功，关闭弹框会终止签名和后续操作, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.closeCloudQRCode();
            }).catch(() => {
            });
        },
        // 获取二维码 isDirection是否重定向页面
        getCloudQRCode (isDirection = false) {
            this.qRCodeLoading = true
            this.isTokenExpiration = 1 // 令牌过期
            // if(this.signBusinessType == 2 || this.signBusinessType == 3 || this.signBusinessType == 4) {
            //     this.isTokenExpiration = 1 // 令牌过期
            // }
            let params = {
                sPatientId: this.patientInfo.sId,
                sUserId: this.userInfo.sId,
                sUserCode: this.userInfo.sNo,
                sUserName: this.userInfo.sName
            }
            Api.getCloudCode(params).then(res => {
                this.qRCodeLoading = false
                if (res.success) {
                    this.qrCode.qrCodeUrl = res.data.sQrCode
                    this.qrCode.iDurateTime = res.data.iDurateTime
                    this.qrCode.sAuthId = res.data.sAuthId
                    res.data.sAuthId && setQRCodeAuthId( res.data.sAuthId);
                    if (isDirection) {
                        this.isShowCloudSignature = true;
                        this.sRedirectUrl = res.data.sRedirectUrl ?? '';
                        window.directionPage = window.open(this.sRedirectUrl, 'directionURL');
                        setTimeout(() => { this.getScanningResult(true) }, 5000);
                        return
                    }
                    this.showCloudQRCode()
                    setTimeout(() => { this.getScanningResult() }, 5000)
                    return
                }
                this.$message.error(res.msg)
            }).catch((err) => {
                this.qRCodeLoading = false
                console.log(err)
            })
        },
        // 获取二维码扫码结果
        async getScanningResult (isDirection = false) {
            // 关闭弹框不再发送请求
            if (!isDirection && !this.isShowCloudSignature) {
                return
            }
            let params = {
                sPatientId: this.patientInfo.sId, //this.patientInfo.sId
                sUserId: this.userInfo.sId,
                sUserCode: this.userInfo.sNo,
                sUserName: this.userInfo.sName,
                sAuthId: this.qrCode.sAuthId
            }
            await Api.getScanResult(params).then(res => {
                if (res.success) {
                    let iOnceAgain = res.data.iOnceAgain
                    if (iOnceAgain == 0) {
                        // clearInterval(this.qRCodeTimer)
                        this.isShowCloudSignature = false
                        this.handleSignAutograph(this.signBusinessType)
                        if(window.directionPage) {
                            window.directionPage.close();
                            window.directionPage = null
                        }

                    } else {
                        setTimeout(() => { this.getScanningResult(isDirection) }, 5000)
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                clearInterval(this.qRCodeTimer)
                this.qrCode.iDurateTime = 0
                console.log(err)
            })
        },
        // 深圳禾正医院获取签名结果
        getSignResult (sAuthId) {
            // 关闭弹框不再发送请求
            if (!this.isShowSignStatus) {
                return
            }
            let params = {
                sPatientId: this.patientInfo.sId,
                sUserId: this.userInfo.sId,
                sUserCode: this.userInfo.sNo,
                sUserName: this.userInfo.sName,
                sAuthId: sAuthId,
                iBusinessType: this.signBusinessType
            }
            Api.getSignStatus(params).then(res => {
                if (res.success) {
                    let iOnceAgain = res.data.iOnceAgain
                    if (iOnceAgain == 0 && !res.data.sPicAuthId) {
                        //  this.$message.success(res.msg)
                        this.messageToStatus = res.msg
                        this.signSussess = 1  // 1签名成功
                        res.data.sAuthId && setQRCodeAuthId( res.data.sAuthId);
                        this.isShowSignStatus = false
                        setTimeout(() => {
                            this.messageToStatus = ''
                            this.signSussess = 2 
                            // 2审核
                            if (this.signBusinessType == 2) {
                                this.signStatus.signAuditedStatus = res.success
                                this.requestauditReport(1)
                            }
                            // 3复审
                            if (this.signBusinessType == 3) {
                                this.signStatus.signReAuditedStatus = res.success
                                this.requestFinalAuditReport(1)
                            }
                            // 4提交
                            if (this.signBusinessType == 4) {
                                this.signStatus.signCommitedStatus = res.success
                                this.requesCommitReport()
                            }
                        }, 1000)
                        return
                    }
                    if (iOnceAgain == 0 && res.data.sPicAuthId) {
                        // 下载签名图片
                        this.messageToStatus = `请在手机“${this.signAppName}”APP授权签名图片下载！`
                        this.getPictureResult(res.data.sPicAuthId)
                        return
                    }
                    let sAuthId = res.data.sAuthId
                    setTimeout(() => { this.getSignResult(sAuthId) }, 5000)
                    return
                }
                const msg = res.msg ? res.msg : '授权签名已超时，请重新发送！'
                this.messageToStatus = msg
                this.signSussess = 0
                // this.$message.error(msg)
            }).catch(err => {
                console.log(err)
                if(err?.code === 500) {
                    this.isShowSignStatus = false;
                }
            })
        },
        // 关闭弹框前
        beforeCloseSign () {
            if (this.signSussess == 0) {
                this.isShowSignStatus = false
                return
            }
            this.$confirm('签名未成功，关闭弹框会终止签名和后续操作, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.isShowSignStatus = false
            }).catch(() => {
            });
        },
        // 获取签名图片下载状态、结果
        getPictureResult (sPicAuthId) {
            // 关闭弹框不再发送请求
            if (!this.isShowSignStatus) {
                return
            }
            let params = {
                sPatientId: this.patientInfo.sId,
                sUserId: this.userInfo.sId,
                sUserCode: this.userInfo.sNo,
                sUserName: this.userInfo.sName,
                sAuthId: sPicAuthId,
                iBusinessType: this.signBusinessType
            }
            Api.getPictureStatus(params).then(res => {
                if (res.success) {
                    if (res.data.iOnceAgain == 0) {
                        this.messageToStatus = res.msg
                        this.signSussess = 1  //1签名成功
                        // this.$message.success(res.msg)  
                        setTimeout(() => {
                            this.isShowSignStatus = false
                            this.messageToStatus = ''
                            this.signSussess = 2
                            // 2审核
                            if (this.signBusinessType == 2) {
                                this.signStatus.signAuditedStatus = res.success
                                this.requestauditReport(1)
                            }
                            // 3复审
                            if (this.signBusinessType == 3) {
                                this.signStatus.signReAuditedStatus = res.success
                                this.requestFinalAuditReport(1)
                            }
                            // 4提交
                            if (this.signBusinessType == 4) {
                                this.signStatus.signCommitedStatus = res.success
                                this.requesCommitReport()
                            }
                        }, 3000)
                        return
                    }
                    let sAuthId = res.data.sAuthId
                    setTimeout(() => { this.getPictureResult(sAuthId) }, 5000)
                    return
                }
                this.messageToStatus = res.msg
                this.signSussess = 0
                // this.message.error(res.msg)
            })
        },
        // 判断是否开启CA重定向
        async judgeCaRedirectWebSwitch() {
            let open = false;
            this.isShowDirectionPage = false;
            const params = ['CaRedirectWebSwitch']
            await getBPMSetFindKeys(params).then(res => {
                if(res.success) {
                    const data = res?.data?.[0] || {};
                    open = (data?.sValue || 0) == 1;
                    this.isShowDirectionPage = open
                }
            }).catch(err => {
                console.log(err);
            })
            return open
        },
        jumpToDirection() {
            window.directionPage = window.open(this.sRedirectUrl, 'directionURL');
        },
        // 签名
        async handleSignAutograph (businessType) {
            // 广西区医院
            const certId = getLoginCertId();
            if(certId) {
                this.mxHandleUKeySign(certId, businessType)
                return
            }
            // 江门中心
            const signType =  this.mxReportCloudSignLoginType;
            const sAuthId = this.getReportCloudSignPinAuthId();
            if(this.mxIsOpenPinCA && signType == 2 && sAuthId) {
                // 使用过密码签名，则使用江门pin签名方式
                this.mxHandleJiangMenSign();
                return
            }
            let params = {
                sPatientId: this.patientInfo.sId,
                sUserId: this.userInfo.sId,
                sUserCode: this.userInfo.sNo,// this.userInfo.sNo
                sUserName: this.userInfo.sName,
                iBusinessType: businessType,
                sAuthId: getQRCodeAuthId() || ''
            }
            let load = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            await Api.toSignAutograph(params).then(async res => {
                if (res.success) {
                    let iWaitting = res.data.iWaitting
                    // iWaitting == 1 深圳和正签名流程,需要异步等待签名结果
                    if (iWaitting && iWaitting == 1) {
                        this.isShowSignStatus = true
                        // this.messageToStatus = `请在手机“${this.signAppName}”APP授权签名图片下载！`
                        this.messageToStatus = `正在签名中，请稍等`
                        this.signSussess = 2
                        const sAuthId = res.data.sAuthId
                        setTimeout(() => { this.getSignResult(sAuthId) }, 2000)
                        return
                    }
                    // // iWaitting != 1 中七医院签名流程， 即可返回签名结果                   
                    // 中七医院iReshowQrCode为1key过期，弹出二维码
                    if (res.data.iReshowQrCode && res.data.iReshowQrCode == 1) {
                        // 湛江中心
                        let isDirectionPage = await this.judgeCaRedirectWebSwitch();
                        if(isDirectionPage) {
                            this.getCloudQRCode(true)
                            return;
                        }
                        if(this.mxIsOpenPinCA == false) {
                            // 江门中心 若未开启pin 密签章，重置为二维码验证
                            this.mxReportCloudSignLoginType = 1
                        }
                        this.expirationTip = ''
                        // key过期获取二维码
                        this.getCloudQRCode()
                        return
                    }
                    if (businessType == 1) {
                        this.signStatus.signSavedStatus = res.success
                    }
                    // 2审核
                    if (businessType == 2) {
                        this.signStatus.signAuditedStatus = res.success
                        // key过期签名后重新审核
                        if (this.isTokenExpiration == 1) {
                            this.isTokenExpiration = 0
                            this.requestauditReport(1)
                        }
                    }
                    if (businessType == 3) {
                        this.signStatus.signReAuditedStatus = res.success
                        // key过期签名后重新复审
                        if (this.isTokenExpiration == 1) {
                            this.isTokenExpiration = 0
                            this.requestFinalAuditReport(1)
                        }
                    }
                    if (businessType == 4) {
                        this.signStatus.signCommitedStatus = res.success
                        // key过期签名后重新提交
                        if (this.isTokenExpiration == 1) {
                            this.isTokenExpiration = 0
                            this.requesCommitReport()
                        }
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            }).finally(() => {
                load.close()
            })
        },
        // 按钮设置收藏显示视图、setCollect 设置值， onceLoad 第一次加载时候赋初值
        setPublish (iIsRelease, onceLoad = false) {
            this.iIsRelease = onceLoad ? this.patientInfo.iIsRelease : iIsRelease;
            this.businessMenus.find((item) => {
                if (item.fn === 'handlerPublish') {
                    if (this.iIsRelease) {
                        item.name = '取消发布';
                        item.icon = 'fa-cancel-publish'
                    } else {
                        item.name = '发布报告';
                        item.icon = 'fa-report-publish'
                    }
                }
            });
        },
        // 打开患者信息弹窗
        openPatientInfoEditDialog () {
            this.dialog.d_PatientInfoModifi_v = true
        },
        // 关闭患者信息弹窗
        closePatientInfoModifi () {
            this.dialog.d_PatientInfoModifi_v = false;
        },
        //  打开检验窗口
        openInspectionSet () {
            this.d_InspectionSet_visible = true
        },
        // 关闭检验窗口
        closeInspectionSet () {
            this.d_InspectionSet_visible = false
        },
        // 获取高亮字样 
        async reportHighlight () {
            await Api.reportHighlight().then(res => {
                if (res.success) {
                    this.highlightArray = res.data || [];
                }
            }).catch(err => {
                console.log(err)
            })
        },
        async textChangeToHighlight (key) {
            let highlightArray = this.highlightArray;
            if (!this.$refs[key] || this.isQuillRender[key] || !highlightArray.length) {
                // 当元素不存在或元素已渲染或非手动输入或高亮词为空，结束函数
                return
            }
            // 设置特殊文字高亮
            this.isQuillRender[key] = true;
            await this.$nextTick(() => {
                // let targetText = ['脑', '未见明显异常'];
                let quill = this.$refs[key].quill;
                // let text = quill.getText();
                let text = '';
                let quillDeltaList = quill.getContents().ops;

                text = quillDeltaList.reduce((prev, curr) => {
                  if (curr.insert && curr.insert.mention) {
                    prev += 'X' // 结构化文字占位1个
                  } else {
                    prev += curr.insert || ''
                  }
                  return prev
                }, '')
                // console.log(quillDeltaList)
                // console.log(text)
                if (!text) {
                    // 编辑器内输入内容不存在时，结束函数
                    return
                }
                highlightArray.forEach(item => {
                    let reg = new RegExp(item, 'g');
                    let len = item.length;
                    let match;
                    while ((match = reg.exec(text)) !== null) {
                        const index = match.index;
                        // 变红加粗
                        quill.formatText(index, len, 'spanHighlight', true);
                    }
                });
            })
        },
        renderHighlightFormat () {
            let saveKeys = ['myProcessRecord', 'myInspectSee', 'myDiagnosticOpinion'];
            saveKeys.map(item => {
                this.textChangeToHighlight(item);
            })
        },
        async removeHighlightFormatToSave (form) {
            let highlightArray = this.highlightArray;
            if (!highlightArray.length) {
                // 高亮词为空，结束函数
                return
            }
            let saveKeys = ['sProcessRecord', 'sInspectSee', 'sDiagnosticOpinion'];
            for (let i = 0; i < saveKeys.length; i++) {
                let key = saveKeys[i];
                if (!form[key]) {
                    continue
                }
                // 同步   清除高亮
                form[key] = form[key].replace(/class="ql-font-spanHighlight"/g, '')
            }
        },
        // 合并型编辑器 内容变化触发
        combinedEditorInput (valArr, changedDataIndexArr) {
            let arr = [
                'sProcessRecord',
                'sInspectSee',
                'sDiagnosticOpinion'
            ]
            this.isQuillRender.editorFocus = true;
            changedDataIndexArr.forEach(i => {
                if (arr[i]) this.isQuillRender[arr[i]] = true;
            })
            this.focusQuillEleRef = 'CombinedEditor';
        },
        resetQuillRenderState () {
            this.isQuillRender = {};
        },
        // 富文本失去焦点
        onBlurQuill () { },
        // 富文本获取焦点
        onFocusQuill (idx, ref) {
            if (this.form.canEdited) {
                this.isQuillRender.editorFocus = true;
            }
            this.focusQuillEleRef = ref || '';
            if (!this.dA1Animation) return;
            if (!this.isVertical) return; 
        },
        showCombinedEditor () {
            this.isCombined = !this.isCombined

        },
        //  切换富文本编辑器是否全屏
        handleFullScreen (val) {
            this.isFullScreen = !this.isFullScreen;
            this.$nextTick(() => {
                if (!this.isFullScreen && this.isVertical) {
                    this.setQuillLayout();
                    return
                }
                let suffix = 'suffix';
                this.setQuillLayout(suffix);
            })
        },
        // 改变富文本编辑器的大小
        handleEditorLayout (val) {
            // if (this.isVertical === val) return
            this.isVertical = !this.isVertical;
            this.isCombined = false
            this.$nextTick(() => {
                if (!this.isFullScreen && this.isVertical) {
                    this.setQuillLayout();
                    return
                }
                let suffix = 'suffix';
                this.setQuillLayout(suffix);
            })
        },
        // 设置中间富文本布局配置
        setQuillLayout (suffix = '') {
            if (!this.elementReportForm) {
                return;
            }

            let size = 300 
            let dom = document.querySelector('#mainEditorBox')
            let availHeight = dom ?  (dom.offsetHeight * 0.66) : 999;
            let availWidth = dom ? (dom.offsetWidth * 0.66) : 999;
            size = this.isVertical ? availHeight : availWidth; 

            this.DA1.type = this.isVertical ? 't-y' : 't-x';

            let len = this.elementReportForm.length; 
            len && this.elementReportForm.forEach((item, index) => {
                item.name = 'c' + index;
            })
            this.DA1.localStorageKey = `QuillLayout_${len}_v${this.isVertical ? 1 : 0}`;
            let storage;
            storage = this.getDA1Storage(this.DA1.localStorageKey); 
            if (len === 2) {
                this.DA1.panelConfig = storage || [
                    {
                        size: size,
                        minSize: 130,
                        name: 'c0',
                        isFlexible: true
                    },
                    {
                        size: this.isVertical ? 220 : 400,
                        minSize: this.isVertical ? 130 : 400,
                        name: 'c1',
                        isFlexible: false
                    }
                ]
            } else {
                this.DA1.panelConfig = storage || [
                    {
                        size: this.isVertical ? 200 : 280,
                        minSize: this.isVertical ? 130 : 280,
                        name: 'c0',
                        isFlexible: false
                    },
                    {
                        size: size,
                        minSize: 140,
                        // maxSize: 500,
                        name: 'c1',
                        isFlexible: true
                    },
                    {
                        size: this.isVertical ? 200 : 280,
                        minSize: this.isVertical ? 130 : 280,
                        name: 'c2',
                        isFlexible: false
                    },
                ]
            }
            !storage && localStorage.setItem('dragAdjustData_' + this.DA1.localStorageKey, JSON.stringify(this.DA1.panelConfig));
            this.DA1.visible = false
            this.$nextTick(() => {
              this.DA1.visible = true
            })
        },
        // 获取AD1存储在浏览器中的值
        getDA1Storage (key) {
            let storage = localStorage[window.localStorageRootKey["dragAdjustData"] + '_' + key];
            storage = storage ? JSON.parse(storage) : null;
            return storage;
        },
        // 显示报告模板
        showReportTemplate () {
            this.auxiliaryPanel.reportTemplate.isShow = !this.auxiliaryPanel.reportTemplate.isShow;
            this.$store.commit({
                type: `apricot/report_module/setIsOpenEssayLayout`,
                isOpenEssayLayout: this.auxiliaryPanel.reportTemplate.isShow
            });
        },
        // 显示报告模板编辑弹窗
        isShowReportTemplateDialog () {
            this.auxiliaryPanel.reportTemplate.isOpen = !this.auxiliaryPanel.reportTemplate.isOpen;
        },
        // 隐藏报告模板
        hideReportTemplate () {
            this.auxiliaryPanel.reportTemplate.isShow = false;
            this.$store.commit({
                type: `apricot/report_module/setIsOpenEssayLayout`,
                isOpenEssayLayout: this.auxiliaryPanel.reportTemplate.isShow
            });
        },
        // 修改痕迹
        showDialogModifyTrace () {
            this.dialog.modifyTrace = true;
        },
        // 备份记录
        openBackupsRecodeDialog () {
            this.dialog.backupsRecord = true;
        },
        // 关联图像
        showDialogMergeCase () {
            this.dialog.mergeCase = true;
        },
        // 客户端-阅图
        onRebuildOpen0 () {
            this.rebuildOpen(0)
        },
        // 客户端-重建
        onRebuildOpen1 () {
            this.rebuildOpen(1)
        },
        // 执行客户端阅图重建
        async rebuildOpen (iIsRebuid = 0) {
            let loading = this.loadFindTip();
            if (this.moduleName != 'remoteConsultMng') {
                await this.getClientConfig()
                const needVerifyLicence = this.clientParams.needVerifyLicence
                // 若接口链接超时，默认值为null,
                if (needVerifyLicence == null) {
                    loading.close()
                    return
                }
                if (needVerifyLicence) {
                    await this.getClientId()
                    const clientId = this.clientParams.clientId
                    // 没有获取到id，默认值为null,
                    if (!clientId) {
                        loading.close()
                        return
                    }
                    await this.getVerificationCode()
                    if (!this.clientParams.verificationStatus) {
                        loading.close()
                        return
                    }
                }
            }
            const params = {
                sImgPatientId: this.patientInfo.sImgPatientId,
                sImgStudyDate: this.patientInfo.sImgStudyDate,
                sImgAccessionNumber: this.patientInfo.sImgAccessionNumber,
                iIsRebuid: iIsRebuid
            }
            ApiAssist.rebuildOpenView(params).then(res => {
                loading.close();
                if (res && !res.success) {
                    this.$message.error(res.msg)
                    return
                }
            }).catch(() => {
                loading.close();
            })
        },
        // 打开web重建 
        async openWebReBuildViewer (iIsRebuild = 1) {
            let loading = this.loadFindTip();
            if (this.moduleName != 'remoteConsultMng') {
                await this.getClientConfig()
                const needVerifyLicence = this.clientParams.needVerifyLicence
                // 若接口链接超时  默认值为null,
                if (needVerifyLicence == null) {
                    loading.close()
                    return
                }
                if (needVerifyLicence) {
                    await this.getClientId()
                    const clientId = this.clientParams.clientId
                    if (!clientId) {
                        loading.close()
                        return
                    }
                    await this.getVerificationCode()
                    if (!this.clientParams.verificationStatus) {
                        loading.close()
                        return
                    }
                }
            }
            loading.close()
            let info = {
                sPatientId: this.patientInfo.sId,
                deviceTypeId: this.patientInfo.sRoomId
            }
            openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info);
        },
        // 打开web阅图
        openWebReadViewer () {
            this.openWebReBuildViewer(0)
        },
        openFlowRecordDialog() {
          this.dialog.d_FlowRecord_v = true
        },
        openReportSettingDialog() {
          this.dialog.ReportSetting_Dialog = true
        },
        // 发布
        handlerPublish () {
            let jsonData = {
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex
            }
            if (this.iIsRelease) {
                let loading = this.loadFindTip();
                Api.reportReleaseCancel(jsonData).then(res => {
                    loading.close();
                    let { success, msg } = res;
                    if (success) {
                        this.$message.success(msg);
                        this.setPublish(false);
                        return
                    }
                    this.$message.error(msg)
                }).catch(() => {
                    loading.close()
                })
                return
            }
            let loading = this.loadFindTip();
            Api.reportRelease(jsonData).then(res => {
                loading.close();
                let { success, msg } = res;
                if (success) {
                    this.$message.success(msg);
                    this.setPublish(true);
                    return
                }
                this.$message.error(msg)
            }).catch(() => {
                loading.close()
            })
        },
        // 提交质量评估后，继续提交流程
        async onContinueSubmitStep(type, value, isDoCallBack = false) {
            const key = type == 1 ? 'hasImageQuality' : 'hasReportQuality';
            this[key] = value;
            if(!isDoCallBack) return
            if(type == 1) {
                this.onSave();
                return;
            } 
            // 判断是否开启 KIP发送
            const useKip = await this.judgeOpenKIPDialog();
            if(useKip) {
                this.d_KIPDialog_v = true;
                return
            }
            if (this.isEditContent) {
                this.onSave(true, this.requestauditReport, 1);
                return
            }
            this.requestauditReport(1)
        },
        // 保存   isFunctionCall = true被动触发(流程函数调用)； callBack=回调函数; state=状态
        async onSave (isFunctionCall = false, callBack, state) {
            this.signBusinessType = 1 // 签章业务类型 1保存
            this.signStatus.signSavedStatus = false // 清除保存签名状态
            if (!this.isEditContent) {
                // 当没有编辑内容的时候，结束函数。
                if (this.isShowCloseWarning) {
                    // 当点击关闭按钮的时候 ，做关闭面板处理
                    this.onClosePanelAndNoSave()
                    return;
                }
                // // 手动点击保存按钮时提示用户
                // this.$message.warning('未编辑，无需保存！');
                // return
            }
            // 校验质控表单  
            let obj = this.$store.getters['user/personalOnlineStorage']
            let reportSettingData = obj[this.iModuleId] && obj[this.iModuleId]['ReportCaseIndexReportSetting'] || {}

            if (reportSettingData.isRequiredQualitative > 0 && [undefined, null, ''].includes(this.qualityForm.sQualitative)) {
                // 校验阴阳性必填
                this.sQualiParams.visible = true
                this.sQualiParams.steps = 1
                return
            }
            // 影像评级判断
            if(this.form.iCheckState === 1 && reportSettingData.isRequiredQuality > 0 && !this.hasImageQuality) {
                // 需要填写影像评级
                const ImageQualityControlRatingRef = this.$refs.ImageQualityControlRating
                ImageQualityControlRatingRef && await ImageQualityControlRatingRef.qualityReportFindByPatientId(true);
                if(!this.hasImageQuality) return
            }

            let form = deepClone(this.form);
            // 清除文字高亮
            await this.removeHighlightFormatToSave(form);

            if (!form.reportId) {
                return
            }

            const html2text = (html) => {
                const temp = document.createElement("div");
                temp.innerHTML = html;
                return temp.textContent || temp.innerText || "";
            }
            form.sProcessRecordText = html2text(form.sProcessRecord)
            form.sInspectSeeText = html2text(form.sInspectSee)
            form.sDiagnosticOpinionPureText = html2text(form.sDiagnosticOpinion)

            delete form.sDiagnosticed
            delete form.sImgQuality
            delete form.sQualitative
            delete form.recordTeach
            delete form.sReportQuality
            delete form.sQualitativeText
            delete form.sDiagnosticedText
            delete form.sImgQualityText
            delete form.sReportQualityText
            delete form.sRecord
            delete form.sTeach
            delete form.sRecordText
            delete form.sTeachText
            delete form.recordTeach
            delete form.btns
            // 女性校验
            let notFemaleSee = [], notFemalePure = [], notMaleSee = [], notMalePure = []
            if (this.patientInfo.sSex == '2') {
                notFemaleSee = this.toOneArray(this.optionsLoc.ApricotReportNotFemale, form.sInspectSeeText)
                notFemalePure = this.toOneArray(this.optionsLoc.ApricotReportNotFemale, form.sDiagnosticOpinionPureText)
            }
            // 男性校验
            if (this.patientInfo.sSex == '1') {
                notMaleSee = this.toOneArray(this.optionsLoc.ApricotReportNotMale, form.sInspectSeeText)
                notMalePure = this.toOneArray(this.optionsLoc.ApricotReportNotMale, form.sDiagnosticOpinionPureText)
            }
            if (notFemaleSee.length || notFemalePure.length || notMaleSee.length || notMalePure.length) {
                let sSex = this.patientInfo.sSex === '1' ? '男' : '女'
                let seeWomanStr = notFemaleSee.length ? `检查所见中包含了“<strong style="color:red">${notFemaleSee.join(',')}</strong>”字符，` : ''
                let pureWomanStr = notFemalePure.length ? `诊断意见中包含了“<strong style="color:red">${notFemalePure.join(',')}</strong>”字符，` : ''
                let seeManStr = notMaleSee.length ? `检查所见中包含了“<strong style="color:red">${notMaleSee.join(',')}</strong>”字符，` : ''
                let pureManStr = notMalePure.length ? `诊断意见中包含了“<strong style="color:red">${notMalePure.join(',')}</strong>”字符，` : ''
                let tipsStr = seeWomanStr + pureWomanStr + seeManStr + pureManStr + `可能与患者<strong style="color:red">${sSex}</strong>性性别特征不一致，确定是否继续保存？`
                this.$confirm(tipsStr, '提示', {
                    dangerouslyUseHTMLString: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '返回',
                    type: 'warning'
                }).then(() => {
                    // 发起报告编辑请求
                    this.requestEditReport(isFunctionCall, form, callBack, state);
                });
                return
            }
            // 发起报告编辑请求
            this.requestEditReport(isFunctionCall, form, callBack, state);
        },
        // 发起报告编辑请求
        requestEditReport (isFunctionCall, form, callBack, state) {
            let loading = null;
            // 非自动保存或者函数触发时，显示加载中状态
            if(!isFunctionCall) (loading = this.loadFindTip())
            Api.editReport(form).then(async (res) => {
                (!isFunctionCall) && loading.close();
                let { success, msg } = res;

                if (!success) {
                    this.$message({ type: "error", message: msg });
                    return
                }

                if (this.isShowCloseWarning) {
                    // 当点击关闭按钮的时候 ，做关闭面板处理
                    this.onClosePanelAndNoSave();
                    return;
                }
                this.isEditContent = false;
                this.doBakReport();  // 同步备份报告
                clearInterval(this.countDown);
                // 点击触发时，重新加载报告
                if (!isFunctionCall) {
                    this.queryCaseReport(false);
                }

                // 函数触发时，执行回调函数
                if (isFunctionCall) {
                    callBack && callBack(state);
                    return
                }
                this.$message.success('保存成功！');
                // 保存后签名
                if (this.signSwitch.iReportSignSwitch == 1) {
                    this.handleSignAutograph(1)
                }

            }).catch(() => {
                (!isFunctionCall) && loading.close()
            })
        },

        // 遍历多维数组找出关键词
        toOneArray (arr, str) {
            let res = []
            if (arr.length) {
                arr.forEach(item => {
                    if (str.indexOf(item.sName) != -1) {
                        res.push(item.sName)
                    }
                    if (item.childs) {
                        res = res.concat(this.toOneArray(item.childs, str))
                    }
                })
            }
            return res
        },
        // 提交
        onSubmit () {
            // 需要验证  提交成功，刷新报告内容，修改编辑状态，并解锁报告
            if (this.form.iCheckState == 2) {
                // 撤销
                this.$confirm('确定要撤销提交吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let loading = this.loadFindTip();
                    Api.cancelCommitReport({ reportId: this.form.reportId }).then(res => {
                        loading.close()
                        let { success, msg } = res;
                        if (success) {
                            this.$message({ type: "success", message: msg });
                            this.queryCaseReport();
                            this.isEditContent = false;
                            // this.unlockReport()
                            return;
                        }
                        this.$message({ type: "error", message: msg });
                    }).catch(err => {
                        console.log(err)
                        loading.close()
                    })
                }).catch((e) => {
                    console.log(e);
                });
                return
            }
            // 提交
            // （需要电子签章）如果提交前编辑了，提示先保存
            if (this.isEditContent && this.signSwitch.iCommitSignSwitch == 1) {
                this.$message.warning('请先保存报告！')
                return
            }
            let oCache = window.localStorage.getItem('oAutoSaveTime');
            oCache = oCache ? JSON.parse(oCache) : {};
            let $PatientInfo = this.$refs.ReportProcessLayout?.$refs?.PatientInfo;
            if (oCache.isRequiredConsult && $PatientInfo && !$PatientInfo?.info?.sClinicalDiagnosis) {
                // 校验问诊信息
                this.$confirm('临床诊断信息未填写，是否继续提交报告？', '提示', { 
                    distinguishCancelAndClose: true,
                    confirmButtonText: '继续提交',
                    cancelButtonText: '填写临床诊断',
                    type: 'warning'
                }).then(() => {
                    if (this.isEditContent) {
                        this.onSave(true, this.requesCommitReport);
                        return;
                    }
                    this.requesCommitReport()
                }).catch((action) => {
                    // console.log(action)
                    if(action === 'cancel') {
                        $PatientInfo.onEditConsultInfoClick()
                    }
                });
                return
            }
            if (oCache.isRequiredInject && $PatientInfo && !$PatientInfo?.injectInfoData?.sId) {
                // 校验问诊信息
                this.$confirm('注射信息未填写，是否继续提交报告？', '提示', {
                    distinguishCancelAndClose: true,
                    confirmButtonText: '继续提交',
                    cancelButtonText: '取消',
                    // cancelButtonText: '填写注射信息',
                    type: 'warning'
                }).then(() => {
                    if (this.isEditContent) {
                        this.onSave(true, this.requesCommitReport);
                        return;
                    }
                    this.requesCommitReport()
                }).catch((action) => {
                    // if(action === 'cancel') {
                    //     $PatientInfo.onEditInjectInfoClick()
                    // }
                });
                return
            }
            this.$confirm('确定要提交报告吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if (this.isEditContent) {
                    this.onSave(true, this.requesCommitReport);
                    return;
                }

                this.requesCommitReport()
            }).catch((e) => {
                console.log(e);
            });
        },
        // 发送提交请求
        async requesCommitReport () {
            // iCommitSignSwitch == 1需要签名再提交
            if (this.signSwitch.iCommitSignSwitch == 1 && !this.signStatus.signCommitedStatus) {
                this.signBusinessType = 4
                await this.handleSignAutograph(4)
                // console.log( '....', this.signStatus.signAuditedStatus)
                if (!this.signStatus.signCommitedStatus) {
                    return
                }
            }
            this.signStatus.signCommitedStatus = false // 清除签名状态
            let loading = this.loadFindTip();
            Api.commitReport({ reportId: this.form.reportId }).then(res => {
                loading.close()
                let { success, msg } = res;
                if (success) {
                    this.$message({ type: "success", message: msg });
                    this.queryCaseReport();
                    // this.unlockReport()
                    return;
                }
                this.$message({ type: "error", message: msg });
            }).catch(() => {
                loading.close()
            })
        },
        // 提交kip后，继续提交流程
        async onKIPAfterSubmitStep() {
            if (this.isEditContent) {
                this.onSave(true, this.requestauditReport, 1);
                return
            }
            this.requestauditReport(1)
        },
        // 判断是否开启kip提交
        async judgeOpenKIPDialog() {
            let useKip = false;
            // const params = ['KIPSwitch']
            // await getBPMSetFindKeys(params).then(res => {
            //     if(res.success) {
            //         const data = res?.data?.[0] || {};
            //         useKip = (data?.sValue || 0) == 1;
            //     }
            // }).catch(err => {
            //     console.log(err);
            // })
            return useKip
        },
        // 审核，撤销审核； 刷新报告内容，修改编辑状态，并解锁报告
        onClickAudit () {
            if (this.form.iCheckState == 3) {
                this.$confirm('确定要撤销审核报告？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let loading = this.loadFindTip();
                    Api.cancelAuditReport({ reportId: this.form.reportId }).then(res => {
                        loading.close()
                        let { success, msg } = res;
                        if (success) {
                            this.$message({ type: "success", message: msg });
                            this.isEditContent = false;
                            this.queryCaseReport();
                            // this.unlockReport()
                            return;
                        }
                        this.$message({ type: "error", message: msg });
                    }).catch(() => {
                        loading.close()
                    })
                }).catch((e) => {
                    console.log(e)
                });
                return
            }
            //审核需要签章时，请提示用户先保存
            if (this.isEditContent && this.signSwitch.iAuditSignSwitch == 1) {
                this.$message.warning('请先保存报告！')
                return
            }
            this.$confirm('确定要提交报告审核吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                distinguishCancelAndClose: true,
                type: 'warning'
            }).then(async () => {
                // 报告评级判断
                let obj = this.$store.getters['user/personalOnlineStorage']
                let reportSettingData = obj[this.iModuleId] && obj[this.iModuleId]['ReportCaseIndexReportSetting'] || {};
                if(reportSettingData.isRequiredImageQuality > 0 && !this.hasReportQuality) {
                    // 需要填写报告评级
                    const ReportQualityControlRatingRef = this.$refs.ReportQualityControlRating;
                    ReportQualityControlRatingRef && await ReportQualityControlRatingRef.qualityReportFindByPatientId(true);
                    if(!this.hasReportQuality) return
                }
                const useKip = await this.judgeOpenKIPDialog();
                if(useKip) {
                    this.d_KIPDialog_v = true;
                    return
                }
                if (this.isEditContent) {
                    this.onSave(true, this.requestauditReport, 1);
                    return
                }
                this.requestauditReport(1)
            }).catch(e => {
                // console.log(e)
                // if (e === "cancel") {
                //     if (this.isEditContent) {
                //         this.onSave(true, this.requestauditReport, 0);
                //         return
                //     }
                //     this.requestauditReport(0)
                // }
            });
        },
        // 审核退回
        onClickAuditBack () {
            // 需要签章时，请提示用户先保存
            if (this.isEditContent && this.signSwitch.iAuditSignSwitch == 1) {
                this.$message.warning('请先保存报告！')
                return
            }
            this.$confirm('确定要退回报告吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                distinguishCancelAndClose: true,
                type: 'warning'
            }).then(() => {
                if (this.isEditContent) {
                    this.onSave(true, this.requestauditReport, 0);
                    return
                }
                this.requestauditReport(0)
            })
        },
        // 发送审核/退回请求 1审核，0退回
        async requestauditReport (isPassFlag = 1) {
            if (isPassFlag && [undefined, null, ''].includes(this.qualityForm.sQualitative)) {
                // this.$message.warning('请填选阴阳性');
                this.sQualiParams.visible = true
                this.sQualiParams.steps = 2
                return
            }
            // iAuditSignSwitch == 1 需要签名在审核
            if (isPassFlag == 1 && this.signSwitch.iAuditSignSwitch == 1 && !this.signStatus.signAuditedStatus) {
                this.signBusinessType = 2
                await this.handleSignAutograph(2)
                if (!this.signStatus.signAuditedStatus) {
                    return
                }
            }
            this.signStatus.signAuditedStatus = false // 清除签名状态
            let params = { reportId: this.form.reportId, passFlag: isPassFlag }
            let loading = this.loadFindTip();
            Api.auditReport(params).then(res => {
                loading.close();
                let { success, msg } = res;
                if (success) {
                    this.$message({ type: "success", message: isPassFlag ? msg: '退回成功' });
                    if (this.form.auditLevel == 2 && isPassFlag == 1) {
                        // 根据缓存设置判断是否需要打印；
                        this.mxOnPrintByCache('reportModuleSetData', false)
                    }
                    this.queryCaseReport();
                    // this.unlockReport()
                    return;
                }
                this.$message({ type: "error", message: msg });
            }).catch(() => {
                loading.close();
            })


        },
        // 复审，撤销复审
        onClickDoubleAudit () {
            if (this.form.iCheckState == 4) {
                this.$confirm('确定要撤销审核报告？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    let loading = this.loadFindTip();
                    Api.cancelRecheckReport({ reportId: this.form.reportId }).then(res => {
                        loading.close()
                        let { success, msg } = res;
                        if (success) {
                            this.$message({ type: "success", message: msg });
                            this.queryCaseReport();
                            this.isEditContent = false;
                            // this.unlockReport()
                            return;
                        }
                        this.$message({ type: "error", message: msg });
                    }).catch(() => {
                        loading.close()
                    })
                }).catch(err => {
                    console.log(err)
                });
                return
            }
            // 需要签章时，请提示用户先保存
            if (this.isEditContent && this.signSwitch.iReauditSignSwitch == 1) {
                this.$message.warning('请先保存报告！')
                return
            }
            this.$confirm('确定要审核报告吗？', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                distinguishCancelAndClose: true,
                type: 'warning'
            }).then(() => {

                if (this.isEditContent) {
                    this.onSave(true, this.requestFinalAuditReport, 1);
                    return
                }
                this.requestFinalAuditReport(1);
            }).catch(e => {
                // if (e === "cancel") {
                //     if (this.isEditContent) {
                //         this.onSave(true, this.requestFinalAuditReport, 0);
                //         return
                //     }
                //     this.requestFinalAuditReport(0);
                // }
            });
        },
        onClickDoubleAuditBack () {
            // 需要签章时，请提示用户先保存
            if (this.isEditContent && this.signSwitch.iReauditSignSwitch == 1) {
                this.$message.warning('请先保存报告！')
                return
            }
            this.$confirm('确定要退回报告吗？', '提示', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                distinguishCancelAndClose: true,
                type: 'warning'
            }).then(() => {
                if (this.isEditContent) {
                    this.onSave(true, this.requestFinalAuditReport, 0);
                    return
                }
                this.requestFinalAuditReport(0);
            });
        },
        // 发送复审请求
        async requestFinalAuditReport (isPassFlag = 1) {
            // if (isPassFlag && [undefined, null, ''].includes(this.form.sQualitative)) {
            //     this.sQualiVisible = true
            //     // this.$message.warning('请填选阴阳性');
            //     return
            // }

            if (isPassFlag == 1 && this.signSwitch.iReauditSignSwitch == 1 && !this.signStatus.signReAuditedStatus) {
                this.signBusinessType = 3
                await this.handleSignAutograph(3)
                if (!this.signStatus.signReAuditedStatus) {
                    return
                }
            }
            this.signStatus.signReAuditedStatus = false // 清除签名状态
            let params = { reportId: this.form.reportId, passFlag: isPassFlag };
            let loading = this.loadFindTip();
            Api.finalAuditReport(params).then(res => {
                loading.close()
                let { success, msg } = res;
                if (success) {
                    this.$message({ type: "success", message: isPassFlag ? msg: '退回成功' });
                    if (this.form.auditLevel == 3 && isPassFlag == 1) {
                        // 根据缓存设置判断是否需要打印；
                        this.mxOnPrintByCache('reportModuleSetData', false)
                    }
                    this.queryCaseReport();
                    // this.unlockReport()
                    return;
                }
                this.$message({ type: "error", message: msg });
            }).catch(err => {
                console.log(err)
                loading.close()
            })
        },

        onOpenImageImport() {
            this.dialog.imageImport = true;
        },
        // 打开打印记录
        onPrintRecordClick () {
            this.d_printRecord_v = true;
        },
        // 打开签章验证
        onSignVerifyClick () {
            this.d_signatureV_v = true;
        },
        //  打开打印设置
        onOpenPrintSet() {
            if(!this.isFixedWostation) {
                this.$message.warning('请切换到报告工作站！')
                return
            }
            this.d_printSet_v = true;
        },
        // 报告模板树赋值
        // status  1 = 覆盖，2 = 插入，3 = 追加
        // obj.type = 1 是检查所见  2 = 诊断意见  3 = 检查技术;
        // isBatch 是否批量插入，覆盖，追加范文模板
        // 是弹出模板，需要关闭
        // 202112 meng 改为combinedEditor， 模板内容改为不操作quill而是直接改变量内容，插入的话需要获取editor的光标位置
        // template  模板对象
        onClickAssign (status, targetArr, template) {
            let elementArray = {
                sProcessRecord: this.$refs.myProcessRecord ? this.$refs.myProcessRecord.quill : '',        // 检查技术
                sInspectSee: this.$refs.myInspectSee ? this.$refs.myInspectSee.quill : '',       // 检查所见
                sDiagnosticOpinion: this.$refs.myDiagnosticOpinion ? this.$refs.myDiagnosticOpinion.quill : '', // 诊断意见
            }

            const CombinedEditorRef = this.$refs.CombinedEditor

            switch (status) {
                case 1: 
                    // 覆盖
                    this.handleTemplateCover(elementArray, CombinedEditorRef, targetArr, template);
                    break;
                case 2: // 插入
                    targetArr.forEach(item => {
                        if (CombinedEditorRef) {
                            CombinedEditorRef.outerInsertContent(item)   // 不从外部赋值，从组件内部操作插入
                        } else {
                            const editor = elementArray[item.mark]
                            if (editor) {
                                const index = editor.selection.savedRange.index;
                                const value = item.sModelEssayText;
                                editor.clipboard.dangerouslyPasteHTML(index, value, 'user')
                                this.isQuillRender.editorFocus = true;
                            }
                        }
                    })
                    break;
                case 3: // 追加
                    targetArr.forEach(item => {
                        // if (this.form[item.mark] === null) {
                        //     this.form[item.mark] = item.sModelEssayText
                        // } else {
                        //     this.form[item.mark] += item.sModelEssayText
                        // }
                        // if (elementArray[item.mark]) {
                        //     let index = elementArray[item.mark].selection.savedRange.index
                        //     this.$nextTick(() => {
                        //         elementArray[item.mark].setSelection(index)
                        //     })
                        //     this.isQuillRender.editorFocus = true
                        // }
                        if (CombinedEditorRef) {
                            CombinedEditorRef.outerSetContent(item, true)   // 不从外部赋值，从组件内部操作插入
                        } else {
                            const editor = elementArray[item.mark]
                            if (editor) {
                                if (editor.getText().trim().length > 0) {

                                    editor.setSelection(1e5)
                                }
                                editor.clipboard.dangerouslyPasteHTML(editor.selection.savedRange.index, item.sModelEssayText, 'user')
                                this.isQuillRender.editorFocus = true;
                            }
                        }
                    })
                    break;
            }
        },
        handleTemplateCover (elementArray, CombinedEditorRef, targetArr, template) {
            // if(template.iIsPart){
            //     this.$message.warning('局部模板不允许覆盖！');
            //     return
            // }
            var tips = '您确定要覆盖吗？';
            var hasConfirm = false;
            if(targetArr.length === 1) {
                if(this.form[targetArr[0].mark] && this.form[targetArr[0].mark].trim()) {
                    hasConfirm = true
                }
            } else {
                this.elementReportForm.map(item => {
                    if(this.form[item.sProp] && this.form[item.sProp].trim()){
                        hasConfirm = true
                    }
                })
            }
            if ([1, 2, '1', '2'].includes(template.sSex) && template.sSex != this.patientInfo.sSex) {
                tips = '选中模板性别与此病例性别不一致，是否覆盖？'
                hasConfirm = true
            }
            if(!hasConfirm) {
                doCover(elementArray, CombinedEditorRef, template, this)
                return
            }
            this.$confirm(tips, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                doCover(elementArray, CombinedEditorRef, template, this)
            }).catch(() => { });

            function doCover(elementArray, CombinedEditorRef, template, $vue) {
                targetArr.forEach(item => {
                    // $vue.form[item.mark] = item.sModelEssayText;
                    // $vue.$nextTick(() => {
                    //     elementArray[item.mark] && elementArray[item.mark].setSelection(elementArray[item.mark].container.textContent.length)
                    // })
                    // $vue.isQuillRender.editorFocus = true

                    // 改为使用paste操作插入，为了能够接入撤销操作
                    if (CombinedEditorRef) {
                        CombinedEditorRef.outerSetContent(item)   // 不从外部赋值，从组件内部操作覆盖
                    } else {
                        const editor = elementArray[item.mark]
                        if (editor) {
                            const value = item.sModelEssayText;
                            editor.setText('', 'user')
                            editor.clipboard.dangerouslyPasteHTML(0, value, 'user')
                            $vue.isQuillRender.editorFocus = true;
                        }
                    }
                })
                if(template.close) {
                    // 关闭范文模板pena
                    targetArr.length !== 1 && $vue.hideReportTemplate()
                }
            }
        },
        // 插入检验数据
        onAddInspectData (paramsStr) {
            const CombinedEditorRef = this.$refs.CombinedEditor
            this.d_InspectionSet_visible = false
            if (CombinedEditorRef) {
                CombinedEditorRef.outerInsertContent({ mark: "sInspectSee", sModelEssayText: paramsStr })   // 不从外部赋值，从组件内部操作插入
            } else {
                const editor = this.$refs.myInspectSee ? this.$refs.myInspectSee.quill : ''    // 检查所见
                if (editor) {
                    const index = editor.selection.savedRange.index;
                    const value = paramsStr;
                    editor.clipboard.dangerouslyPasteHTML(index, value, 'user')
                    this.isQuillRender.editorFocus = true;
                }
            }
        },
        // 设置收藏显示视图、setCollect 设置值， onceLoad 第一次加载时候赋初值
        setEnrineView (setCollect, onceLoad = false) {
            this.isEnshrine = setCollect
            if (onceLoad) {
                this.isEnshrine = this.patientInfo.iCollect
            }
            this.businessMenus.find((item) => {
                if (item.fn === 'onClickEnshrine') {
                    if (this.isEnshrine) {
                        item.name = '取消病例';
                        item.icon = 'fa-collect-case'
                    } else {
                        item.name = '收藏病例';
                        item.icon = 'fa-hollow-start'
                    }
                }
            });
        },
        // 点击收藏
        onClickEnshrine () {
            if (this.isEnshrine) {
                ApiCase.delCollection({ sPatientId: this.patientInfo.sId }).then(res => {
                    if (res.success) {
                        this.$message.success('取消收藏成功！')
                        this.setEnrineView(0)
                        return;
                    }
                    this.$message.error(res.msg)
                })
            } else {
                ApiCase.addCollection({ sPatientId: this.patientInfo.sId, sInnerIndex: this.patientInfo.sInnerIndex }).then(res => {
                    if (res.success) {
                        this.$message.success('添加收藏成功！')
                        this.setEnrineView(1)
                        return;
                    }
                    this.$message.error(res.msg)
                })
            }
        },
        // 设置教学病例
        onChnageTeachCase (value) {
            const params = {
                iSwitch: value,
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex
            }
            Api.setTeachcase(params).then(res => {
                if (res.success) {
                    // this.$message.success(value ? '标记教学病历成功！' : '撤销教学病历成功！')
                    return;
                }
                this.iTeachCase = !value;
                this.$message.error(res.msg);
                return;
            })
        },
        // 还原备份
        restoreReport(data) {
            if(this.disabled) {
                this.$message.warning('当前报告不可编辑，无法还原报告！');
                return
            }
            this.form.sInspectSee = data.sInspectSee;
            this.form.sDiagnosticOpinion = data.sDiagnosticOpinion;
        },
        // 定时保存编辑内容
        setIntervalOnSave () {
            clearInterval(this.countDown);
            if (!this.form.canEdited) {
                return
            }
            // 获取localstorage的自动备份定时时长
            let oCache = JSON.parse(window.localStorage.getItem('oAutoSaveTime') || '{}');
            let isSaved = true;
            let seconds = oCache.iAutoBackupTime || 20;
            let now = new Date().getTime();
            let then = now + seconds * 1000;
            // let then = now + seconds * 60 * 1000;
            this.countDown = setInterval(() => {
                const secondLeft = Math.round((then - new Date().getTime()) / 1000);
                // console.log('secondLeft=',secondLeft);
                if (secondLeft <= 0) {
                    now = new Date().getTime();
                    then = now + seconds  * 1000;
                    // then = now + seconds * 60 * 1000;
                    compareReportContent();
                    return;
                }
            }, 1000)
            // 比较报告内容
            let compareReportContent = async () => {
                const form = this.form;
                const bakReport =  this.bakReport || {};
                let isEditArr = [
                    bakReport.sDiagnosticOpinion !== form.sDiagnosticOpinion,
                    bakReport.sInspectSee !== form.sInspectSee,
                    // bakReport.sProcessRecord !== form.sProcessRecord
                ]
                isSaved = true;
                // console.log(isSaved , isEditArr , this.isEditContent)
                if (isSaved && isEditArr.includes(true) && this.isEditContent) {
                    // 编辑框内容有修改&&还没有自动保存
                    await this.doBakReport(true);
                    isSaved = false;
                    return
                }
            }
        },
        // 执行备份
        async doBakReport() {
            const form = this.form;
            const html2text = (html) => {
                const temp = document.createElement("div");
                temp.innerHTML = html;
                return temp.textContent || temp.innerText || "";
            }
            const jsonData = {
                sDiagnosticOpinion: form.sDiagnosticOpinion,
                sDiagnosticOpinionPureText: html2text(form.sDiagnosticOpinion),
                sInspectSee: form.sInspectSee,
                sInspectSeeText: html2text(form.sInspectSee),
                // sProcessRecord: form.sProcessRecord,
                // sProcessRecordText: html2text(form.sProcessRecord),
                sPatientId: form.sPatientId,
                sReportId: form.reportId,
                sId: form.sId,
            }
            this.bakReport = jsonData;
            // console.log(jsonData);
            await Api.bakReport(jsonData).then(res  => {
                console.log('bakReport: res=', res);
                if (!res.success) {
                    this.$message.error(res.msg);
                } 
            }).catch(err => {
                console.log(err)
            })
        },
        // 加载提示
        loadFindTip (text) {
            return this.$loading({
                lock: true,
                text: text,
                // 
                background: 'rgba(0, 0, 0, 0.1)',
                customClass: 'my-loading'
            });
        },
        // 获取报告
        async queryCaseReport (isShowLoad = true) {
            let loading;
            // if(isShowLoad)(loading =  this.loadFindTip());
            if (isShowLoad) {
                if (this.loadingContainer) {
                    loading = this.loadingContainer;
                } else {
                    loading = this.loadFindTip();
                }
            }
            // 获取富文本滚动条高度
            let $myProcessRecord = this.$refs.myProcessRecord;
            let $myInspectSee = this.$refs.myInspectSee;
            let $myDiagnosticOpinion = this.$refs.myDiagnosticOpinion;
            let myProcessRecordScrollTop = $myProcessRecord && $myProcessRecord.quill
             ? $myProcessRecord.quill.root.scrollTop : 0;
            let myInspectSeeScrollTop = $myInspectSee && $myInspectSee.quill
             ? $myInspectSee.quill.root.scrollTop : 0;
            let myDiagnosticOpinionScrollTop = $myDiagnosticOpinion && $myDiagnosticOpinion.quill
             ? $myDiagnosticOpinion.quill.root.scrollTop : 0; 
            // 获取光标位置
            let $focusQuillEleRef = this.focusQuillEleRef ? this.$refs[this.focusQuillEleRef].quill  : '';
            if (this.isCombined) {
                $focusQuillEleRef = this.$refs['CombinedEditor'].$refs.myQuillEditor.quill;
            }
            let cursorIdx = $focusQuillEleRef? $focusQuillEleRef.selection.savedRange.index : 0;
            try {
                let { data, success, msg } = await Api.queryAndLockCaseReport({ sPatientId: this.patientInfo.sId });
                isShowLoad && loading.close()
                if (!success) {
                    this.$message.error(msg)
                    return
                }
                // 表单复制
                data = data || {};
                this.resetQuillRenderState();
                this.staticReportContent = deepClone(data);
                this['form'] = deepClone(data);

                this.renderHighlightFormat();

                this.signSwitch = {
                    iAuditSignSwitch: data.iAuditSignSwitch,   // 审核
                    iReauditSignSwitch: data.iReauditSignSwitch, // 复审
                    iReportSignSwitch: data.iReportSignSwitch,  // 保存
                    iCommitSignSwitch: data.iCommitSignSwitch,  // 提交

                }
                // 质控数据处理
                this.qualityForm['sDiagnosticed'] = data.sDiagnosticed
                this.qualityForm['sImgQuality'] = data.sImgQuality
                this.qualityForm['sQualitative'] = data.sQualitative
                this.qualityForm['sReportQuality'] = data.sReportQuality
                this.qualityForm['sRecord'] = data.sRecord
                this.qualityForm.recordTeach = this.qualityForm.sRecord ? this.strToArray(this.qualityForm.sRecord) : [] //将字符串转化为二维数组回显
                // 教学病例
                this.iTeachCase = this.patientInfo.iTeachCase;
                this.$nextTick(() => {
                    this.$refs.refEditLayer1 && this.$refs.refEditLayer1.clearValidate();
                });
                // 报告流程状态
                if (this.form.sCheckStateText) {
                    this.patientInfo['sCheckStateText'] = this.form.processStatus || '';
                    this.setPatientInfo({
                        patientInfo: this.patientInfo
                    })
                }
                this.lockInfo['sLockUserName'] = data.sLockUserName
                this.setDisabled(!data.canEdited);
                //会诊状态
                // this.form.iconsultation = 0
                if (data.iconsultation == 1 && this.moduleName === 'ReportMng') {
                    // 报告管理进入报告模块，发送远程会诊后不可编辑报告
                    this.setDisabled(true);
                }
                this.$nextTick(() => {
                    this.businessMenus.map(item => {
                        if (item.name == '发送会诊') {
                            item.iIsHide = data.iconsultation == 1 ? 1 : 0;
                        }
                        if (item.name == '取消会诊') {
                            item.iIsHide = data.iconsultation == 1 ? 0 : 1;
                        }
                    })
                })
                // 报告内容复制，用于区分
                this.setShowButtons(data);
                this.showFormEnd(data);
                if($myProcessRecord){
                    // $myProcessRecord.quill.blur();
                    this.$nextTick(() => {
                        $myProcessRecord.quill.root.scrollTop = myProcessRecordScrollTop;
                    })
                }
                if($myInspectSee){
                    // $myInspectSee[0].quill.blur();
                    this.$nextTick(() => {
                        $myInspectSee.quill.root.scrollTop = myInspectSeeScrollTop;
                    })
                }
                if($myDiagnosticOpinion){
                    // $myDiagnosticOpinion.quill.blur();
                    this.$nextTick(() => {
                        $myDiagnosticOpinion.quill.root.scrollTop = myDiagnosticOpinionScrollTop;
                    })
                }
                 
                    
                this.$nextTick(() => {
                    //  定位光标
                    if($focusQuillEleRef && data.canEdited) {
                        $focusQuillEleRef.clipboard.dangerouslyPasteHTML(cursorIdx, '');
                        setTimeout(()=> {this.isQuillRender.editorFocus = true}, 1000)
                        
                    }
                })

                if (data.needUpdateLock) {
                    // 定时发送锁报告心跳
                    this.doLockHeartBeat();
                }
                this.loadingContainer = null;
            }
            catch (err) {
                isShowLoad && loading.close()
                console.log(err)
            }
        },
        // 将字符串转化为二维数组
        strToArray (str) {
            var arr1 = str.split(';')
            var arr2 = new Array(arr1.length);
            for (var i = 0; i < arr1.length; i++) {
                //定义临时数组
                var temp = new Array(2);
                //将一维数组再次拆分，准备存入二维数组
                temp = arr1[i].split("/");
                //定义将要存放数据的一维数组的每一个元素都为一个数组(实际上就是定义二维数组了)
                arr2[i] = new Array(temp.length);
                //遍历临时数组将其值存入二维数组
                for (var j = 0; j < temp.length; j++) {
                    arr2[i][j] = temp[j];//完成字符串转换为二维数组
                }
            }
            return arr2
        },
        showFormEnd (data) {
            let levelflag = ['sFinalExamineName', 'dFinalExamineDate'],
                practiveflag = ['sPracticeName', 'dPracticeTime'];
            let target = this.elementConfigData.ApricotReportFormEnd.filter(item => {
                // item.iClearable = 0;
                if (!practiveflag.includes(item.sProp) && !levelflag.includes(item.sProp)) {
                    // 排除其他输入框的显示
                    item.isShow = true
                    return item
                }
                // 判断显示实习医生和复审医生输入框
                if (data.showPracticeDoctor && practiveflag.includes(item.sProp) || data.auditLevel == 3 && levelflag.includes(item.sProp)) {
                    item.isShow = true
                    return item
                }
            });
            this.elementConfigData.ApricotReportFormEnd = target;
            this.isShowFormEnd = target.length > 0
        },
        // 控制按钮的显示与隐藏
        setShowButtons (data) {
            const auditLevel = data.auditLevel;
            let btns = data.btns || [];
            let normalKey = ['1', '2', '3', '4', '5', '6'];
            let mainKeys = auditLevel == 2 ? normalKey : [...normalKey, '7', '8', '9'];
            this.auditBtns = this.configs.businessMenus.filter(item => {
                // ['SAVE', 'COMMIT', 'CANCEL_COMMIT', 'AUDIT', 'AUDIT_BACK', 'CANCEL_AUDIT', 'RECHECK', 'RECHECK_BACK', 'CANCEL_RECHECK']
                // ['保存', '提交', '撤销提交', '审核', '审核退回', '撤销审核', '复审', '复审退回', '撤销复审']
                // return (['1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(item.key))  
                return mainKeys.includes(item.key)
            }).map(item => {
                let iIsHide = 1;
                let isReadOnly = false;
                let target = btns.find(btn => item.key == btn.btnId);
                if (target) {
                    if (this.form.iconsultation == 1 && this.moduleName === 'ReportMng') {
                        // 报告管理进入报告模块，发送远程会诊后不可编辑报告
                        iIsHide = 0;
                        isReadOnly = true;
                    } else {
                      iIsHide = 0;
                      isReadOnly = !target.available;
                    }
                }
                return {
                  ...item, iIsHide, isReadOnly
                }
            });
        },
        // 报告是否可编辑 status = true 锁、 false 解锁
        setDisabled (status = true) {
            this.$nextTick(() => {
                this.disabled = status;
            })
        },
        // 锁定报告
        async lockReport () {
            try {
                let { success, msg } = await Api.lockReport({ reportId: this.form.reportId });
                if (success) {
                    // 锁定成功  调用心跳锁和定时保存
                    this.lockInfo['sLockUserName'] = this.userInfo.sName
                    this.doLockHeartBeat();
                    this.setIntervalOnSave();
                    return
                }
                this.$message.error(msg)
                this.queryCaseReport();
                clearInterval(this.lockHeartBeatTimes);
                clearInterval(this.countDown);
            }
            catch (err) {
                console.log(err)
            }
        },
        // 解锁报告
        async unlockReport () {
            // 停止心跳锁和定时保存
            clearInterval(this.lockHeartBeatTimes);
            clearInterval(this.countDown);
            try {
                if (this.lockInfo.sLockUserName == this.userInfo.sName) {
                  let res = await Api.unlockReport({ reportId: this.form.reportId });
                  if (res.success) {
                    this.lockInfo.sLockUserName = ''
                  }
                }
            }
            catch (err) {
                console.log(err)
            }
        },
        // 刷新锁
        async updateLockTime () {
            try {
                await Api.updateLockTime({ reportId: this.form.reportId });
            }
            catch (err) {
                console.log(err)
            }
        },
        // 定时心跳
        doLockHeartBeat () {
            clearInterval(this.lockHeartBeatTimes);
            if (!this.form.locked) {
                return
            }
            // 心跳时长
            let seconds = 30;

            this.lockHeartBeatTimes = setInterval(() => {
                // 发送请求
                this.updateLockTime();
            }, seconds * 1000);
            
            // let startTime = new Date().getTime();
            // let endTime = startTime + seconds * 1000;
            // this.lockHeartBeatTimes = setInterval(() => {
            //     const secondLeft = endTime - new Date().getTime();
            //     // console.log(secondLeft)
            //     if (secondLeft <= 0) {
            //         startTime = new Date().getTime();
            //         endTime = startTime + seconds * 1000;
            //         // 发送请求
            //         this.updateLockTime();
            //         return;
            //     }
            // }, 1000);
        },
        // 查询客户端配置
        async getClientConfig () {
            await Api.queryClientConfig().then(res => {
                if (res.success) {
                    this.clientParams.needVerifyLicence = res.data.needVerifyLicence
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 查询本机主板序列号
        async getClientId () {
            await Api.readClientId().then(res => {
                if (res.success) {
                    this.clientParams.clientId = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 获取授权状态
        async getVerificationCode () {
            let params = {
                clientId: this.clientParams.clientId
            }
            await Api.verificationCode(params).then(res => {
                if (res.success) {
                    this.clientParams.verificationStatus = res.success
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        hanldeGetAppName(){
            if(![null,'',undefined].includes(this.signAppName)) {
                return
            }
            let params = ['CaAppName']
            getBPMSetFindKeys(params).then( res =>{
                if(res.success) {
                    this.$store.commit({
                            type: "user/setSignatureAppName",
                            signatureAppName: res.data[0].sValue
                        })
                }
            }).catch( err =>{
            })
        },
        onClickCollapRightPart() {
          this.isShowInnerList = !this.isShowInnerList
        },
        updateMenuList (data) {
            this.businessMenus = data;
        },
        async initPage() {
          // 按模块 加载mapMutations
          if (this.moduleName != 'ReportMng') {
              mapMutations = createNamespacedHelpers('apricot/remoteConsult_module').mapMutations;
              // console.log(mapMutations);
          }
          let useUserConfigQuery = useUserConfigQueryByKey()
          await useUserConfigQuery('ReportMngReportEditMenuConfig', this, this.configs.businessMenus);
          this.configs.businessMenus = compareAndModifyMenuList(this.configs.businessMenus, this.MngIndexPenalConfig, 'name');

          // this.dA1Animation = localStorage['reportDAAnimation'] !== undefined ? JSON.parse(localStorage['reportDAAnimation']) : false
          this.dA1Animation = false
          
          this.loadingContainer = this.loadFindTip();
          await this.reportHighlight();
          await this.queryCaseReport();
          this.mxGetCodeTable('ApricotReportNotMale');// 男性特征词
          this.mxGetCodeTable('ApricotReportNotFemale'); // 女性特征词

          this.businessMenus = this.configs.businessMenus;
          this.setEnrineView(true, true);
          this.setPublish(true, true);
          
          await getOnlineConfig.call(this, this.iModuleId, 'ReportCaseIndexReportSetting', this.userInfo.sId);
          this.handleFillProcessRecord();

          Object.values(this.signSwitch).includes(1) && this.judgeCaLoginInPin();
        },
        // 填充检查技术
        handleFillProcessRecord() {
            const {isShowProcessRecord, isFillProcessRecord, structuralProcessRecord}  = this.pageSetting;
            if(!structuralProcessRecord) return
            if(isShowProcessRecord && isFillProcessRecord ){
                let text = structuralProcessRecord;
                // 使用正则表达式匹配{{}}内的内容  
                let regex = /\{([^}]*)\}/g; 
                let replacements = this.patientInfo;
                
                // 替换函数，根据匹配到的内容返回替换后的字符串  
                function replacer(match, p1) {  
                    // p1 是匹配到的第一个捕获组（即{{}}内的内容）  
                    return replacements[p1] || ''; // 如果没有找到替换项，则返回一个空字符串（或根据需要返回其他默认值）  
                }  
                // 使用String.prototype.replace()方法和替换函数进行替换  
                let newText = text.replace(regex, replacer);  
                
                // 输出替换后的文本  
                if(this.form.canEdited && this.form.iReportStatus == 10 && this.form.sProcessRecord === null){
                    this.form.sProcessRecord = newText
                }
            }
        }
    },
    beforeUnmount () {
        clearInterval(this.countDown);
        clearInterval(this.lockHeartBeatTimes);
        clearInterval(this.qRCodeTimer)
        clearInterval(this.scanStatusTimer)
        this.handleEditorLayout(true);
        window.onunload = () => { }
        window.onbeforeunload = () => { }
        onKeyCodeEvent.onkeydown = null
    },
    mounted() {
        this.initPage()

        // 关闭浏览器是调用解锁接口
        window.onbeforeunload = () => {
            if (this.isEditContent) {
                return ''
            }
        }

        window.onunload = () => {
            this.isEditContent && this.unlockReport();
        }

        onKeyCodeEvent.onkeydown = (event => {
            // 当前页面下
            if (this.$route.name === 'apricot_Report' && this.currentProcess === 'Report') {
                if (event.keyCode === 83 && event.ctrlKey) {
                    // ctrl + s 调用保存
                    event.preventDefault();
                    this.isEditContent && this.onSave();
                }
                if (event.keyCode === 65 && event.ctrlKey) {
                    // ctrl + a 调用提交
                    event.preventDefault();
                    this.onSubmit();
                }
                if (event.keyCode === 69 && event.ctrlKey) {
                    // ctrl + e 调用审核
                    event.preventDefault();
                    this.onClickAudit();
                }
                if (event.keyCode === 82 && event.ctrlKey) {
                    // ctrl + r 调用复审
                    event.preventDefault();
                    this.onClickDoubleAudit();
                }
            }
        }),
        this.hanldeGetAppName()
    },
    // created () {
      // ??? 跟mounted冲突？
    // },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style lang="scss" scoped>
.m-editorArea-full {
    position: fixed !important;
    top: 55px;
    left: 10px;
    right: 10px;
    bottom: 0;
    z-index: 8;
    overflow: hidden;
    background: #fafafa;
    height: calc(100% - 65px);
}
#mainEditorBox {
    border-top: 1px solid #eee;
    box-sizing: border-box;
    :deep(.g-adjust-item ){
        transition: height 0.2s linear;
    }
}
.i-Layout {
    overflow: hidden;
}
.c-auxiliaryArea {
    padding: 7px 15px 7px 15px;
    border-top: 1px solid #eee;
    display: flex;
    flex: 1 0 auto;
    justify-content: space-between;
    align-items: center;
    background: rgb(245, 245, 245);
}
.c-item-patent-01 {
    display: flex;
    align-items: center;
    padding: 5px 0 5px 8px;
    justify-content: space-between;
    flex-wrap: wrap;
    background: rgb(249, 250, 252);
    .i-lump {
        > i {
            font-size: 16px;
            position: relative; 
            top: 2px;
            left: 8px;
        }
    }
    .el-checkbox {
        margin-right: 10px;
    }
}
.i-textarea-actions {
    position: relative;
    padding: 0 0 0 5px;
    overflow: hidden;
    background: rgb(249, 250, 252);
    width: 180px;
    .i-actions {
        display: flex;
        position: absolute;
        bottom:  5px;
        // border: 1px solid #eee;
        .i-actions-item {
            padding: 5px 10px;
            // border-right: 1px solid #eee;
            cursor: pointer;
            &:last-child {
                border-right: 0;
            }
            &:hover {
              background: var(--el-color-primary-light-9);
            }
        }
        .theme {
            padding: 0 5px;
            // border-right: 1px solid #eee;
            display: flex;
            justify-content: center;
            align-items: center;
            &:hover {
              background: var(--el-color-primary-light-9);
            }
        }
    }
}

.c-reportTemplate {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    // width: calc(100% - 10px);
    // height: calc(100% - 10px);
    transition: transform 0.3s;
    transform: translateX(-100%);
    border-right: 1px solid #eee;
    z-index: -1;
    &.s-show {
        transform: translateX(0);
        margin-left: 0;
        z-index: 3;
        // box-shadow: 1px 2px 10px rgba(0,0,0,.3);
    }
}
.c-box-content {
    height: 100%;
    display: flex;
    h5 {
        background: rgb(245, 245, 245);
        font-weight: 700;
        font-size: 16px;
        line-height: 1.4;
        color: rgb(44, 62, 80);
        padding: 5px;
        margin: 0px;
        width: 22px;
        text-align: center;
        display: flex;
        align-items: center;
        border-right: 1px solid #eee;
        > i {
            font-size: 14px;
            position: absolute;
            top: 8px;
            right: 5px;
            background: #fafafa;
            color: #909399;
            cursor: pointer;
            &:hover {
                font-weight: bold;
            }
        }
    }
    .c-box {
        flex: 1;
        :deep(.ql-container.ql-snow) {
            border-bottom: none;
        }
    }
}
.c-combinedEditor-box {
    position: relative;
}

.inline-form {
    width: 200px;
    white-space: nowrap;
    display: inline-block;
    .el-form-item {
        margin-bottom: 0;
    }
}
:deep(.inline-form) {
    .el-form-item.is-required:not(.is-no-asterisk)
        > .el-form-item__label:before {
        content: "";
        width: 7px;
        height: 7px;
        display: inline-block;
        background-color: #f56c6c;
        border-radius: 50%;
    }
}
:deep(.m-labelInput) {
    margin-left: 6px;
}
.quali-tx {
    display: inline-block;
    // margin: 0 10px 0 10px;
    :deep(.el-radio-group) {
        flex-wrap: nowrap;
    }
    .el-radio {
        margin-right: 10px;
    }
}

.loading {
    height: 5px;
    margin-left: 10px;
    display: inline-block;
    span {
        display: inline-block;
        width: 5px;
        height: 100%;
        margin-right: 5px;
        border-radius: 50%;
        background: rgb(83, 138, 247);
        animation: load 2.5s ease infinite;
    }
    span:last-child {
        margin-right: 0px;
    }
    span:nth-child(1) {
        animation-delay: 0.5s;
    }
    span:nth-child(2) {
        animation-delay: 1s;
    }
    span:nth-child(3) {
        animation-delay: 1.5s;
    }
    span:nth-child(4) {
        animation-delay: 2s;
    }
    span:nth-child(5) {
        animation-delay: 2.5s;
    }
}
@keyframes load {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0.2;
    }
}
.isFullScreen-action {
    padding: 6px 0px 3px 0px;
    background: #fafafa;
    float: right;
    border-left: 1px solid #eee;
    > span:first-child {
        padding: 0 5px;
    }
    > span {
        display: inline-block;
        padding: 5px 10px;
        vertical-align: middle;
        text-align: center;
        border-right: 1px solid #eee;
        cursor: pointer;
    }
}

:deep(.my-tip-dialog) {
    .el-dialog__body {
        padding: 10px 20px;
    }
}

.qrcode_img {
    text-align: center;
    padding: 25px;
    img {
        width: 200px;
        height: 200px;
    }
}

.codes_tips {
    font-size: 18px;
    color: rgb(93, 110, 159);
    padding: 10px 0px 20px 0px;
    text-align: center;
    height: 56px;
}

.item-doc-infor {
    position: relative;
    display: block;
    float: left;
    box-sizing: border-box;
    padding: 2px 12px 2px 0;
    min-width: 152px;
    .name-time {
        font-weight: 600;
        color: rgb(70, 170, 116);
    }
}

:deep(.el-color-predefine__color-selector) {
    border:1px solid #c8c8c8;
}
.sign-type {
    .active {
        position: relative;
        color: var(--el-color-primary);
        &::after {
            position: absolute;
            bottom: -4px;
            left: 0;
            content: '';
            width: 100%;
            height: 2px;
            background-color: var(--el-color-primary);
        }
    }
}
</style>
