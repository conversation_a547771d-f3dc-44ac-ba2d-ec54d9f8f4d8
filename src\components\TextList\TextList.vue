<template>
  <div class="container">

    <div class="flex hover-show">
      <div class="grid-box">
        <div class="cell-box" v-for="(item, index) in showingContent" :label="(item.label)" :key="item.label + index"
          :style="{
            width: item.width + '%'
          }">
          <div class="cell-label" :style="{
            width: String(item.labelWidth) + 'px'
          }">
            <span>
              {{ item.label }}：
            </span>
          </div>
          <div class="cell-content" :class="{'cell-full':item.width === '100'}" 
            :style="{ 
                height: String(item.height) === 'auto' ? 'auto' : String(item.height) + 'px', 
                lineHeight: String(item.height) === 'auto' ? 'auto' : String(item.height) + 'px'}">
            <slot :name="item.prop" :row="item" :style="getElementInjectStyle(item)">
              <span :style="getElementInjectStyle(item)"  
                :title="data[item.prop]"> 
                    {{ data[item.prop] || '（空）' }}  
                    {{ data[item.prop] ? item.sUnit : '' }} 
              </span>
            </slot>
          </div>
        </div>
      </div>
      <el-popover v-if="delayRender1 && configBtn === true" ref="popoverRef" trigger="click" :placement="isSmallScreen ? 'right-end' : 'left'" 
        :width="isSmallScreen ? '580' : 'auto'" :teleported="true" transition="none" :hide-after="0" @after-enter="onPopAfterEnter">
        <template #default>
          <div class="pop-header">
            <h3>{{ ('信息展示栏配置') }}</h3>

          </div>
          <div class="w-full overflow-auto" >
            <!-- el-skeleton 的 width=表头宽度 -->
            <div v-if="!isRenderPopover" style="width: 790px; height:450px; overflow: hidden;">
                <el-skeleton :rows="12" />
            </div>
            <el-table v-else class="pop-table" :data="react.tableData" row-key="prop" ref="popTableRef" height="450">
              <el-table-column prop="label" :label="('排序')" align="center" width="60">
                <el-icon class="drag-icon" :size="18">
                  <Rank />
                </el-icon>
              </el-table-column>
              <el-table-column prop="label" :label="('显示')" align="center" width="60">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isShow" @change="onChangeItemProperty(scope, 'isShow')"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="label" :label="('字段名称')" width="120" align="left">
                <template #default="scope">
                  {{ scope.row.label }}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="align" :label="('对齐方式')" align="center" width="130">
              <template #default="scope">
                <el-radio-group class="radio-group" @change="onChangeAlign(scope)" v-model="scope.row.align">
                  <el-radio-button label="left">左</el-radio-button>
                  <el-radio-button label="center">中</el-radio-button>
                  <el-radio-button label="right">右</el-radio-button>
                </el-radio-group>
              </template>
            </el-table-column> -->
              <el-table-column prop="width" :label="('宽度%')" width="120" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    宽度(%)
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false" :persistent="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in widthOptions" :label="i" :value="i"
                              @click="setAllItemsVal('width', i)">
                              全部设为{{ i }}%
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <el-select v-model="scope.row.width" allow-create filterable :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'width')">
                    <el-option v-for="i in widthOptions" :label="i" :value="i"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="height" :label="('高度')" width="110" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    高度
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false" :persistent="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in heightOptions" :label="i" :value="i"
                              @click="setAllItemsVal('height', i)">
                              全部设为{{ i }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <el-select v-model="scope.row.height" allow-create filterable :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'height')">
                    <el-option v-for="i in heightOptions" :label="i" :value="i"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('文字大小')" width="120" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    文字大小
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false" :persistent="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in fontSizeOptions" :label="i" :value="i"
                              @click="setAllItemsVal('fontSize', i)">
                              全部设为{{ i }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <el-select v-model="scope.row.fontSize" allow-create filterable :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'fontSize')">
                    <el-option v-for="i in fontSizeOptions" :label="i" :value="i"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('文字颜色')" width="120" align="center">
                <template #default="scope">
                  <el-color-picker v-model="scope.row.color" :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'color')" />
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('字体加粗')" width="80" align="center">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isBold" @change="onChangeItemProperty(scope, 'isBold')"></el-checkbox>
                </template>
              </el-table-column>


            </el-table>
          </div>
          <div class="flex items-center justify-between pt-2">
            <div class="flex ">
              <el-button type="primary" @click="setStorage(1)">{{ ('保存到全局') }}</el-button>
              <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否恢复默认设置？')"
                :teleported="false" :persistent="false" @confirm="onClickResetConfig">
                <template #reference>
                  <el-button type="primary" plain>{{ ('恢复默认设置') }}</el-button>
                </template>
              </el-popconfirm>
            </div>
            <span class="flex">
              <el-button type="default" @click="onClickClose">{{ ('关闭') }}</el-button>
            </span>
          </div>
        </template>
        <template #reference>
          <div ref="buttonRef" class="config-btn" v-if="configBtn === true">
            <el-icon class="setting-icon">
              <MoreFilled />
            </el-icon>
          </div>
        </template>
      </el-popover>


    </div>


    <!-- <template v-slot:default>
      </template>
      <template v-slot:append>
      </template> -->


  </div>
</template>
<script>
import draggable from 'vuedraggable';
import Sortable from 'sortablejs'
import { Rank, MoreFilled, ArrowDown } from '@element-plus/icons-vue'
import { cloneDeep, isArray } from 'lodash-es';
import { useStore } from 'vuex';

import { getOnlineConfig, saveOnlineConfig, compareAndModifyArrays } from '@/utils'

const defaultWidth = '25'
// const defaultHeight = ''
const defaultHeight = '32'
const defaultLabelWidth = '92px'
const defaultFontSize = '14'
const defaultColor = '#3c4353'
const defaultIsBold = false

const defaultStorageData = {
  list: []
}

export default {
  name: "TextList",
  components: {
    draggable,
    Rank, MoreFilled, ArrowDown
  },
  props: {
    storageKey: {
      type: String,
      default: "", // 不填不启用localstorage
    },
    iModuleId: {
      default: "", // 不填不启用数据上传
    },
    list: {
      type: Array,
      default: [], //  
    },
    data: {
      type: Object,
      default: () => ({}), //  
    },
    configBtn: {
      type: [Object, Boolean],
      default: () => true
    },
    labelWidth: {
        default:  undefined, 
    }
  },
  setup(props) {
    const _store = useStore()

    const isSmallScreen = window.document.body.clientWidth < 1400 ? true : false

    const buttonRef = ref();
    const popoverRef = ref();
    const react = ref({
      defaultConfigContent: [],
      tableData: [],
    });
    const configButtonRef = computed(() => {
      if (props.configBtn && props.configBtn.ref) {
        return props.configBtn
      }
      return buttonRef
    });

    const userNo = computed(() => {
      let userInfo = _store.getters["user/userSystemInfo"] || {};
      return userInfo.sId
    })

    return {
      userNo,
      react,
      isSmallScreen,
      delayRender1: ref(false),
      isRenderPopover: ref(false),

      buttonRef,
      popoverRef,
      configButtonRef,
      getOnlineConfig,
      saveOnlineConfig,
      widthOptions: [20, 25, 33.3, 50, 100],
      heightOptions: ['auto', 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50],
      fontSizeOptions: [12, 14, 16, 18, 20, 22, 24, 26, 28],
    };
  },
  created() {
    this.setDefaultData();
    this.getStorage();
  },
  computed: {
    showingContent() {
      return this.react.tableData.filter((i) => i.isShow)
    },
  },
  methods: {
    getElementInjectStyle(item) {
        return {
            'color': String(item.color),
            'font-size': String(item.fontSize) + 'px',
            'font-weight': String(item.isBold) === 'true' ? 'bold' : 'normal',
            'line-height': '20px'
        }
    },
    onChangeItemProperty(scope, key) {
      const index = scope.$index;
      const table = this.react.tableData[index];
      table[key] = scope.row[key];

      this.setStorage();
    },
    // 获取本地缓存
    async getStorage() {
      if (!this.storageKey) return;
      let storageStr = localStorage.getItem('TextList-' + this.storageKey);
      let storageObj = {
        ...defaultStorageData
      }

      if (this.iModuleId && this.storageKey) {
        await this.getOnlineConfig()
        Object.assign(storageObj, 
        this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])
      } else {
        try {
          const parsed = JSON.parse(storageStr)
          if (Array.isArray(parsed)) {
            storageObj.list = parsed
          } else if (parsed && parsed.list) {
            storageObj = parsed
          }
        } catch (error) {
          console.error(error)
        }
      }

      let list = storageObj.list;

      const propList = list.map((i) => i.prop);
      const inputList = this.getInputList();
      let newList = compareAndModifyArrays(inputList, list);
    //   let newList = []

    //   if (propList.length !== inputList.length) {
    //     // 如果表格加了新的一列数据，恢复成默认顺序
    //     // console.log('new data - 恢复成默认顺序', inputList,propList)
    //     newList = inputList
    //   } else {
    //     // newList // 按localstor排序后的slot列表
    //     propList.forEach(prop => {
    //       const index = inputList.findIndex(slot => slot.prop === prop)
    //       if (index > -1) {
    //         newList.push(inputList.splice(index, 1)[0])
    //       }
    //     })
    //     newList = [...inputList].concat(newList)
    //   }

      this.react.defaultConfigContent = newList

      // 将store存贮的属性值还原到当前数据
      let index
      let newTable = cloneDeep(newList).map((item) => {
        index = list.findIndex(target => item.prop === target.prop)
        if (index > -1) {
          return this.dataObjConvert({ ...item, ...list[index] })
        }
        return this.dataObjConvert(item)
      });

      this.react.tableData = newTable

    },
    // 设置缓存
    setStorage(isGlobal = 0) {
      if (!this.storageKey) return;

      const storageObj = {
        list: this.react.tableData,
      }

      const storageString = JSON.stringify(storageObj)

      localStorage.setItem(
        "TextList-" + this.storageKey,
        storageString
      );
      this.saveOnlineConfig(storageObj, isGlobal)
    },
    getInputList() {
      const inputList = isArray(this.list) ? this.list : []
      return inputList.map(i => {
        const obj = {
          ...i,
          prop: i.sProp || i.prop,
          label: i.sLabel || i.label,
          width: i.width || (i.iLayourValue ? `${i.iLayourValue / 24 * 100}` : '') || '',
        }

        if (obj.width === '100%' && !obj.height) {
          obj.height = 'auto'
        }

        return obj
      })
        .filter(item => item.prop)
        .filter(item => !item.iIsHide)

    },
    setDefaultData() {
      this.react.defaultConfigContent = this.getInputList()
      this.react.tableData = this.react.defaultConfigContent.map(this.dataObjConvert);
    },
    dataObjConvert(item) {
      return {
        prop: item.prop || "",
        label: item.label || " ",
        isShow: item.isShow === false ? false : true,
        align: item.align || "left",
        width: String(item.width || defaultWidth).replace(/\%/g, ''),
        height: String(item.height || defaultHeight).replace(/[px]/g, ''),
        labelWidth: String(item.labelWidth || this.labelWidth || defaultLabelWidth).replace(/[px]/g, ''),
        color: item.color || defaultColor,
        isBold: !!item.isBold || defaultIsBold,
        fontSize: String(item.fontSize || defaultFontSize).replace(/[px]/g, ''),
        sUnit: item.sUnit,
        iCustom: item.iCustom
      }
    },
    // 点击重置
    onClickResetConfig() {
      this.setDefaultData()
      this.setStorage();
    },
    onClickClose() {
      this.$el.click()
    },
    onPopAfterEnter() {
      this.isRenderPopover = true;
      this.$nextTick(() => {
        this.rowDrop()
      })
    },
    //行拖拽
    rowDrop() {
      const popTable = (this.$refs.popTableRef.$el)
      const tbody = popTable.querySelector('tbody')
      Sortable.create(tbody, {
        disabled: false, // 是否开启拖拽
        handle: ".drag-icon",
        // ghostClass: 'sortable-ghost', //拖拽样式
        animation: 150, // 拖拽延时，效果更好看
        group: { // 是否开启跨表拖拽
          pull: false,
          put: false
        },
        onEnd: ({ newIndex, oldIndex }) => {
          // console.log(newIndex, oldIndex)
          const currRow = this.react.tableData.splice(oldIndex, 1)[0]
          this.react.tableData.splice(newIndex, 0, currRow)
          const _currRow = this.react.defaultConfigContent.splice(oldIndex, 1)[0]
          this.react.defaultConfigContent.splice(newIndex, 0, _currRow)
          this.$nextTick(() => {
            this.setStorage();
          });
        }
      })
    },
    setAllItemsVal(key, val) {
      this.react.tableData.forEach(item => {
        item[key] = val
      })
      this.setStorage();
    }
  },
  mounted() {
    // 卡顿延迟加载
    setTimeout(() => {
        this.delayRender1 = true 
    }, 100);
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  color: var(--theme-color);
  // background: rgba(255, 255, 255, 1);
  font-size: 13px;
  border-right: 0;
  border-bottom: 0;
  // overflow: hidden;
  box-sizing: border-box;



  .grid-box {
    width: 100%;
    position: relative;
    flex: 1;
    // display: table;
    box-sizing: border-box;
    padding:10px 0;
    border-top: var(--el-border);
    border-bottom: var(--el-border);
    // margin: 18px 0 0;
    // border-top: 2px solid #dcdfe6;
    // border-bottom: 2px solid #dcdfe6;
    // background-color: #f3fae8;
    // border-right: var(--el-border);
    // border-left: var(--el-border);


    .cell-box {
      position: relative;
      float: left;
      display: flex;
      flex-direction: row;
      align-items: baseline;
      height: auto;
      box-sizing: border-box;
      margin: 0 0 0 0;
      padding:0 10px;
      // border-right: var(--el-border);
      // border-bottom: var(--el-border);
      // border-top: 1px solid transparent;
      // border-left: var(--el-border);

      // &:last-child {
      //   border-right: var(--el-border);
      // }

      .cell-label {
        display: flex;
        flex: 0 0 auto;
        box-sizing: border-box;
        height: auto;
        min-height: 32px;
        color: #838a9d;
        align-items: center;
        justify-content: flex-end;
        // background: var(--el-fill-color-light);

        // border-left: var(--el-border);
        // border-bottom: var(--el-border);

        span {
          font-size: 13px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 18px;
        }

      }

      .cell-content {
        padding: 0 0px 0 8px;
        color: #3c4353;
        // display: flex;
        // flex: 1 1 auto;
        // box-sizing: border-box;
        // align-items: center;
        // justify-content: flex-start;
        // // border-left: var(--el-border);
        // // border-bottom: var(--el-border);
        // word-break: break-all;
        // overflow: auto;
        
        overflow: hidden; 
        white-space: nowrap; 
        text-overflow: ellipsis;
        &.cell-full {
            overflow: auto;
            white-space: normal;
            line-height: 1.5;
        }
        span {
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 20px;
          position: relative;
          top: -1px;

        }

      }
    }

  }
  &.none-border .grid-box {
    border: none;
  }

  .hover-show:hover {
    .config-btn {
      visibility: visible;
    }
  }

  .config-btn {
    position: absolute;
    display: flex;
    visibility: hidden;
    align-items: center;
    justify-content: center;
    top: 0px;
    right: 4px;
    width: 18px;
    height: 18px;
    border: none;
    border-radius: 4px;
    color: #888;
    // background: var(--el-color-primary);
    // border-radius: 50%;
    overflow: hidden;
    z-index: 3;
    cursor: pointer;

    &:hover {
      background: #ccc;
      // filter: contrast(1.5);
      // transition: all ease .4s;
    }

    .setting-icon {
      position: relative;
      display: inline-block;
      width: auto;
      top: 0px;
      right: 0;
      font-size: 16px;
      color: var(--el-button-bg-color);
    }
  }




}

.pop-header {
  display: flex;
  align-items: center;
  padding: 6px 0 10px;
  justify-content: space-between;

  h3 {
    margin: 0 0 0 10px;
  }
}

.drag-icon {
  position: relative;
  display: inline-flex;
  min-height: 25px;
  align-items: center;
  justify-content: center;
  cursor: move;
  vertical-align: middle;
}

.radio-group {
  height: 30px;

  :deep(.el-radio-button__inner) {
    padding: 5px 11px;
    font-size: 12px;
    border-radius: 0;
  }
}

// 处理popover内弹窗遮挡
.pop-table {
  min-height: 420px;
  width: fit-content;
  max-width: 9999px;
  // .el-table--enable-row-transition .el-table__body td.el-table__cell
  :deep(.el-table__cell) {
    position: static;

    .cell {
      overflow: visible;
    }
  }

  :deep(.el-table__header-wrapper) {
    overflow: visible;
  }
}
</style>
