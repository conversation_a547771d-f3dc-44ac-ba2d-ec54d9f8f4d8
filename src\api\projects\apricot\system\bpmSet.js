import request, { baseURL } from '$supersetUtils/request'
export default {
    // 流程控制设置表操作接口 : BPM Set Controller
    getBPMSetData(data) {
        return request({
            url: baseURL.apricot + '/b/p/m/set/find/page',
            method: 'POST',
            data
        })
    },
    addBPMSet(data) {
        return request({
            url: baseURL.apricot + '/b/p/m/set/add',
            method: 'POST',
            data
        })
    },
    editBPMSet(data) {
        return request({
            url: baseURL.apricot + '/b/p/m/set/edit',
            method: 'POST',
            data
        })
    },
    delBPMSet(params) {
        return request({
            url: baseURL.apricot + '/b/p/m/set/del',
            method: 'POST',
            params
        })
    },

    sortBPMSet(params) {
        return request({
            url: baseURL.apricot + '/b/p/m/set/sort',
            method: 'POST',
            params
        })
    },
    autoSortBPMSet(params) {
        return request({
            url: baseURL.apricot + '/b/p/m/set/autoSort',
            method: 'POST',
            params
        })
    },
    // 院区维护---end
}


export function getBPMSetFindKeys(data) {
    return request({
        url: baseURL.apricot + '/b/p/m/set/find/keys',
        method: 'post',
        data 
    })
}