<template>
    <div class="container">
        <div class="plane item-01">
            <div class="title">
                <h4>患者信息</h4>
            </div>
            <el-scrollbar class="scrollbar">
                <div style="overflow: hidden;box-sizing: border-box;padding: 0 10px;">
                    <FormList
                    storageKey="reservation-patient1"
                    iModuleId="2"
                    :list="formList"
                    :formData="props.formData"
                    :optionData="props.optionData">
                        <!-- 患者姓名 -->
                        <template v-slot:sName="{ row, style }">
                            <el-input class="i-history"
                            v-model="props.formData[row.sProp]"
                            :readonly="!!row.iReadonly"
                            clearable
                            :style="style"
                            @input="() => {if(!props.formData[row.sProp].length){isSpellShow = false}}"
                            @blur="onNameBlur">
                                <template #append >
                                    <el-popover v-if="isShowHistoryPopover" 
                                        :visible="isHistoryShow"
                                        class="i-sNameHistory"
                                        placement="bottom-end"
                                        :width="800"
                                        :hide-after="0"
                                        @before-leave="hidePopover">
                                        <div style="padding: 10px;background: var(--el-color-warning-light-9);display: flex; align-items: center; justify-content: space-between;">
                                            <strong style="color: var(--el-color-primary)">历史数据</strong>
                                            <el-button class="pull-right" 
                                                type="primary"
                                                link
                                                @click="isHistoryShow=false">
                                                <el-icon :size="20"> <Close /></el-icon>
                                            </el-button>
                                        </div>
                                        <el-table-extend :data="patientNameHistoryData" 
                                            stripe border
                                            highlight-current-row 
                                            max-height="400px"
                                            :iModuleId="2" storageKey="ReservationHistoryTable"
                                            @row-click="historyTableRowClick">
                                            <el-table-column type="index" label="序号" prop="_index" align="center" width="60"
                                                class-name="tableColOverflowVisible"></el-table-column>
                                            <el-table-column v-for="item in historyTableConfig.filter(i => !i.iIsHide)"
                                                show-overflow-tooltip :key="item.index" :prop="item.sProp"
                                                :label="item.sLabel" :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth"
                                                :min-width="item.sMinWidth" :sortable="(!!item.iSort) ? 'custom' : false"
                                                :isSortable="item.isSortable" :sOrder="item.sOrder"
                                                :column-key="item.sSortField ? item.sSortField : null">
                                                <template v-slot="scope">
                                                    <template v-if=" ['dBirthday', 'dAppointmentTime'].includes(item.sProp)">
                                                        {{transformDate(scope.row[`${item.sProp}`])}}
                                                    </template>
                                                    <template v-else>
                                                        {{scope.row[`${item.sProp}`]}}
                                                    </template>
                                                </template>
                                            </el-table-column>
                                        </el-table-extend>
                                        <template #reference>
                                            <el-button size="small"
                                            link
                                            :disabled="!!row.iReadonly"
                                            style="padding:0 5px"
                                            @click="onClickHistory">历史</el-button>
                                        </template>
                                    </el-popover>
                                </template>

                            </el-input>
                        </template>
                        <!-- 姓名拼音 -->
                        <template v-slot:sNameSpell="{ row, style }">
                            <el-popover placement="bottom"
                                width="250"
                                :visible="isSpellShow"
                                @show="isSpellShow = !!props.optionData.pinyinOptions.length">
                                <el-radio-group v-model="props.formData.sNameSpell"
                                    @change="selectNameSpell"
                                    style="display: flex; flex-direction: column;align-items: baseline;padding: 10px;">
                                    <el-radio v-for="(item, index) in props.optionData.pinyinOptions"
                                        :key="index"
                                        :label="item"
                                        style="margin: 8px 0;">
                                        <span style="font-size: 16px;">{{item}}</span>
                                    </el-radio>
                                </el-radio-group>
                                <template #reference>
                                <el-input v-model="props.formData.sNameSpell"
                                    :readonly="!!row.iReadonly"
                                    clearable
                                    size="small"
                                    ref="refInput"
                                    @focus="focusNameSpell"
                                    @blur="blurNameSpell"
                                    @clear="isSpellShow = !!props.optionData.pinyinOptions.length"
                                    :style="style"></el-input>
                                </template>
                            </el-popover>
                        </template>
                        <!-- 年龄 -->
                        <template v-slot:iAge="{ row, style }">
                            <el-input class="i-top"
                                v-model="props.formData[row.sProp]"
                                :readonly="!!row.iReadonly"
                                v-input-number
                                @input="(value) => props.formData[row.sProp] = value.replace(/[\+\-\*/]/g, '')"
                                @blur="computedBirthDayByAge">
                                <template #append>
                                    <el-select
                                    v-model="props.formData.sAgeUnit"
                                    v-select-name="{ formData: props.formData, fields: { sProp: 'sAgeUnit', sPropName: 'sAgeUnitText' } }"
                                    @change="computedBirthDayByAge"
                                    placeholder=" "
                                    :style="{...style, width: '65px' }">
                                    <el-option v-for="item in props.optionData.sAgeUnitOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                    </el-select>
                                </template>

                            </el-input>
                        </template>
                        <!-- 出生日期 -->
                        <template v-slot:dBirthday="{ row, style }">
                            <el-date-picker v-model="props.formData[row.sProp]" :style="style"
                                type="date"
                                :readonly="!!row.iReadonly"
                                :picker-options="pickerOptions"
                                @blur="computedAgeByBirthDay">
                            </el-date-picker>
                        </template>
                        <!-- 身份证号 -->
                        <template v-slot:sIdNum="{ row, style }">
                            <el-input v-model="props.formData[row.sProp]" :style="style"
                            :readonly="!!row.iReadonly"
                            clearable
                            @blur="computedAgeByIdNums"></el-input>
                        </template>
                        <!-- 身高 -->
                        <template v-slot:fHeight="{ row, style }">
                            <el-input
                                v-model="props.formData[row.sProp]"
                                :readonly="!!row.iReadonly"
                                v-input-number="true" :style="style"
                                controls-position="right"
                                @input="(value) => props.formData[row.sProp] = value.replace(/[\+\-\*/]/g, '')">
                                <template #suffix><span style="color: #333;">{{ row.sUnit }}</span></template>
                            </el-input>
                        </template>
                        <!-- 体重 -->
                        <template v-slot:fWeight="{ row, style }">
                            <el-input class="i-weight"
                                v-model="props.formData[row.sProp]"
                                :readonly="!!row.iReadonly"
                                controls-position="right"
                                v-input-number="true"
                                :style="style"
                                @input="(val)=>handleInputWeight(row, val)"
                                @change="(val)=>handleInputWeight(row, val)">
                                <template #suffix><span style="color: #333;">{{ row.sUnit }}</span></template>
                            </el-input>
                        </template>
                        <!-- 是否怀孕 -->
                        <template v-slot:iIsPregnant="{ row, style }">
                            <el-select v-model="props.formData[row.sProp]"
                                clearable :style="style"
                                :disabled="props.formData.sSex == 1"
                                placeholder=" ">
                                <el-option v-for="item in props.optionData.pregnantOptions"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </template>
                    </FormList>
                </div>
            </el-scrollbar>
        </div>
        <div class="plane item-02">
            <div class="title">
                <h4>申请单信息</h4>
            </div>
            <el-scrollbar class="scrollbar" ref="scrollApplyForm">
                <div style="overflow: hidden;box-sizing: border-box;padding: 0 10px;">
                    <FormList
                    storageKey="reservation-patient2"
                    iModuleId="2"
                    :list="formList1"
                    :formData="props.formData"
                    :optionData="props.optionData">
                        <!-- 问诊室 -->
                        <template v-slot:sConsultRoomId="{ row, style }">
                            <el-select v-model="props.formData[row.sProp]" v-select-name="{ formData: props.formData, fields: row }"
                                clearable :style="style"
                                placeholder="">
                                <el-option v-for="item in props.optionData.consultRoomOption"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </template>
                        <!-- 注射室 -->
                        <template v-slot:sInjectionRoomId="{ row, style }">
                            <el-select v-model="props.formData[row.sProp]" v-select-name="{ formData: props.formData, fields: row }"
                                clearable :style="style"
                                placeholder="">
                                <el-option v-for="item in props.optionData.injectionRoomOption"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </template>
                        <!-- 影像号 -->
                        <template v-slot:sImageNo="{ row, style }">
                            <el-input v-model="props.formData[row.sProp]"
                                :readonly="!!row.iReadonly"
                                clearable
                                :style="style">
                                <!-- <template #append>
                                    <el-button
                                        size="small"
                                        link
                                        style="padding:0 5px"
                                        :loading="loadingImgNo"
                                        @click="getImageNo()">
                                        <template #icon>
                                            <el-icon><Refresh /></el-icon>
                                        </template>
                                    </el-button>
                                </template> -->
                            </el-input>
                        </template>
                        <!-- 空腹血糖 -->
                        <template v-slot:fBloodSugar="{ row, style }">
                            <el-input
                                v-model="props.formData[row.sProp]"
                                :readonly="!!row.iReadonly"
                                v-input-number="true"
                                @input="(value) => props.formData[row.sProp] = value.replace(/[\+\-\*/]/g, '')"
                                :style="style"
                                controls-position="right">
                                <template #suffix><span style="color: #333;">{{ row.sUnit }}</span></template>
                            </el-input>
                        </template>
                        <!-- 总费用 -->
                        <template v-slot:fFees="{ row, style }">
                            <el-input
                                v-model="props.formData[row.sProp]"
                                :readonly="!!row.iReadonly"
                                v-input-number="true"
                                @input="(value) => props.formData[row.sProp] = value.replace(/[\+\-\*/]/g, '')"
                                :style="style"
                                controls-position="right">
                                <template #suffix><span style="color: #333;">{{ row.sUnit }}</span></template>
                            </el-input>
                        </template>
                    </FormList>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>
<script setup>
    /**
     * 预约单-患者信息
     */
    import { Close, Refresh } from '@element-plus/icons-vue'
    import { ElMessage } from 'element-plus'
    import { useStore } from 'vuex'
    import useUserConfig from '../useUserConfig.js'
    import { getEventbus } from '@/utils'

    import { transformDate } from '$supersetResource/js/tools'
    import { chineseToPinyin, getHistoryNuclearNum as apiGetHistoryNuclearNum, 
        getPatientNameHistoryData as apiGetPatientNameHistoryData, brokenImgno } from '$supersetApi/projects/apricot/appointment/index.js'
    // import { getWorkStationData } from '$supersetApi/projects/apricot/system/workStation.js'

    import ConfigsItems from '../configs/configsItems.js'

    // const store = useStore()

    const props = defineProps({
        formData: {},
        rules: {},
        refEditLayer: null,
        visible: null,
        nuclearParams: {}, // 药物参数
        defaultCheckInfo: Object,
        optionData: {},    // 下拉
    })

    const formList = ref(ConfigsItems.appointmentInputList1)
    const formList1 = ref(ConfigsItems.appointmentInputList1_1)
    const historyTableConfig = ref(ConfigsItems.historyTableConfig)

    // 患者姓名数据
    const isHistoryShow = ref(false) // 显示 HIS 历史数据
    const isHistoryPatient = ref(false)
    const patientNameHistoryData = ref([])
    function getPatientNameHistoryData(isAutoShow) {
        patientNameHistoryData.value = []
        
        // TODO 如果已经预约，查的不对，查询返回无设备类型字段
        let sName = props.formData.sName
        let sRoomId = props.defaultCheckInfo.sDeviceTypeId
        if (!sName) {
            return
        }

        apiGetPatientNameHistoryData({
            sName,
            sRoomId
        }).then(res => {
            if (res.success) {
                patientNameHistoryData.value = res.data || [];
                let sexs = {}
                props.optionData.iSexList?.map(item => {
                    sexs[item.sValue] = item.sName
                });
                let data = patientNameHistoryData.value
                for (let i = 0; i < data.length; i++) {
                    let item = data[i]
                    item.index = i + 1
                    item.sSexText = item.sSexText || sexs[item.sSex];
                }
                if(!isAutoShow) {
                    isHistoryShow.value = true
                    return
                }
                setTimeout(()=> {
                    if(patientNameHistoryData.value.length) {
                        isHistoryShow.value = true
                    }
                }, 300)
                return
            }
            ElMessage.error(res.msg)
        }).catch(err => {
            console.log(err)
        })
    }
    const iTimeOut = ref(null)
    function onClickHistory () {
        if(isHistoryShow.value) {
            clearTimeout(iTimeOut.value)
            iTimeOut.value = setTimeout(() => {
                isHistoryShow.value = false;
            }, 100)
            return
        }
        isHistoryPatient.value = true
        getPatientNameHistoryData()
    }
    // 点击历史表格数据
    function historyTableRowClick (row) {
        isHistoryShow.value = false
        isHistoryPatient.value = true
        getHistoryNuclearNum(row)
        let target = {
            sName: row.sName,
            sSex: row.sSex,
            sSexText: row.sSexText,
            sIdNum: row.sIdNum,
            dBirthday: row.dBirthday ? new Date(row.dBirthday) : row.dBirthday,
            sAddress: row.sAddress,
            sPhone: row.sPhone,
        }
        for (const key in target) {
            if (Object.hasOwnProperty.call(target, key)) {
                props.formData[key] = target[key]
            }
        }
        if (!props.formData.sNameSpell) {
            setPinYin()
        }
        if (props.formData.sIdNum) {
            computedAgeByIdNums()
            return
        }
        if (props.formData.dBirthday) {
            computedAgeByBirthDay()
            return
        }
    }
    // 获取历史患者的核医学号
    function getHistoryNuclearNum (row) {
        apiGetHistoryNuclearNum({
            sName: row.sName,
            sNuclearNum: row.sNuclearNum,
            sInHospitalNO: row.sInHospitalNO,
            sMedicalRecordNO: row.sMedicalRecordNO,
            sOutpatientNO: row.sOutpatientNO,
            sPatientIndex: row.sPatientIndex,
            sRoomId: props.defaultCheckInfo.sDeviceTypeId
        }).then(res => {
            if (res.success) {
                props.formData['sNuclearNum'] = res.data || '';
                props.defaultCheckInfo['isHistory'] = true;
                return
            }
            ElMessage.error(res.msg)
        })
    }

    // 拼音显示
    const isSpellShow = ref(false)
    const refInput =ref()

    // 稀奇古怪，input 会无缘无故自动获取焦点，触发打开拼音选择浮层
    function selectNameSpell() {
        isSpellShow.value = false
        // console.log('radio-change')
        nextTick(() => {
            refInput.value.blur()
        })
    }
    function focusNameSpell() {
        isSpellShow.value = true
        // console.log('input-focus')
    }
    function blurNameSpell() {
        if(props.formData.sNameSpell){
            isSpellShow.value = false
            setTimeout(() => {
                refInput.value.blur()
            }, 100);
        }
        // console.log('input-blur')
    }
    function setPinYin () {
        let sName = props.formData.sName
        // let dom = document.querySelector('.i-history')
        // if(!dom) {
        //     console.log('!dom')
        //     return
        // }
        if (!sName) {
            props.optionData['pinyinOptions'] = []
            props.formData['sNameSpell'] = undefined
            props.refEditLayer.clearValidate('sNameSpell')
            // dom && dom.removeAttribute('sname')
            isSpellShow.value = false
            return
        }
        // console.log(sName, dom.getAttribute('sname'))
        // if (sName === dom.getAttribute('sname')) { 
        //     return 
        // }
        // dom.setAttribute('sname', props.formData.sName)
        chineseToPinyin({ sChineseText: props.formData.sName }).then(res => {
            if (res.success) {
                let data = res.data || []
                props.optionData['pinyinOptions'] = data
                if (data.length === 1) {
                    props.formData['sNameSpell'] = data[0]
                } else if (data.length > 1) {
                    if (!data.includes(props.formData.sNameSpell)) {
                        props.formData['sNameSpell'] = ''
                    } else {
                        nextTick(() => {
                            isSpellShow.value = false
                        })
                    }
                } else {
                    props.formData['sNameSpell'] = ''
                }
                props.refEditLayer.clearValidate('sNameSpell')
                if (data.length > 1) {
                    isSpellShow.value = true
                }
                return
            }
            ElMessage.error(res.msg)
            props.optionData['pinyinOptions'] = []
        })
    }

    // 不可选出生日期
    const pickerOptions = {
        disabledDate: time => {
            return time.getTime() > new Date().getTime()
        }
    }
    // 根据身份证号计算年龄，出生日期
    function computedAgeByIdNums () {
        let identityCard = props.formData.sIdNum;
        let len = (identityCard + '').length;
        let regx = new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/);
        // 身份证号码只能为15位或18位其它不合法
        if (len == 0 || (len != 15 && len != 18) || regx.test(identityCard) == false) {
            return
        }
        
        let birthDate = getComputeStringBirthday();
        props.formData['dBirthday'] = birthDate.toISOString()

        let nowDateTime = new Date();

        if (birthDate.getTime() >= nowDateTime.getTime()) {
            props.formData['sAgeUnit'] = 'Y';
            props.formData['iAge'] = '';
            return
        }
        if (nowDateTime.getFullYear() > birthDate.getFullYear()) {
            let age = nowDateTime.getFullYear() - birthDate.getFullYear();
            //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
            if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
                age--;
            }
            props.formData['sAgeUnit'] = 'Y';
            props.formData['iAge'] = age;
            return
        }
        if (nowDateTime.getMonth() > birthDate.getMonth()) {
            let age = nowDateTime.getMonth() - birthDate.getMonth();
            //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
            if (nowDateTime.getDate() < birthDate.getDate()) {
                age--;
            }
            props.formData['sAgeUnit'] = 'M';
            props.formData['iAge'] = age;
            return
        }
        if (nowDateTime.getDate() > birthDate.getDate() >= 7) {
            let age = nowDateTime.getDate() - birthDate.getDate();
            props.formData['sAgeUnit'] = 'W';
            props.formData['iAge'] = Math.floor(age / 7);
            return
        }
        let age = nowDateTime.getDate() - birthDate.getDate();
        props.formData['sAgeUnit'] = 'D';
        props.formData['iAge'] = age;

        // TODO 根据身份证判断性别
    }
    function getComputeStringBirthday() {
        let identityCard = props.formData.sIdNum;
        let len = (identityCard + '').length;
        let regx = new RegExp(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/);
        // 身份证号码只能为15位或18位其它不合法
        if (len == 0 || (len != 15 && len != 18) || regx.test(identityCard) == false) {
            return ''
        }
        var strBirthday = "";
        //处理18位的身份证号码从号码中得到生日
        //时间字符串里，必须是“/”
        if (len == 18) {
            strBirthday = identityCard.substr(6, 4) + "/" + identityCard.substr(10, 2) + "/" + identityCard.substr(12, 2);
        }
        if (len == 15) {
            strBirthday = "19" + identityCard.substr(6, 2) + "/" + identityCard.substr(8, 2) + "/" + identityCard.substr(10, 2);
        }
        return  new Date(strBirthday);
    }

    // 根据生日计算年龄 规则（小于一个月单位为天，一个月到三个月单位为周，三到十二个月单位为月，大于等于十二个月单位岁）
    function computedAgeByBirthDay () {
        if (!props.formData.dBirthday) {
            props.formData['iAge'] = '';
            props.formData['sAgeUnit'] = 'Y';
            return;
        }
        let birthDate = new Date(props.formData.dBirthday);
        let nowDateTime = new Date();

        if (birthDate.getTime() >= nowDateTime.getTime()) {
            props.formData['iAge'] = '';
            props.formData['sAgeUnit'] = 'Y';
            return;
        }
        let disMonth = nowDateTime.getFullYear() * 12 + nowDateTime.getMonth() - birthDate.getFullYear() * 12 - birthDate.getMonth()

        if (disMonth > 12) {
            let age = nowDateTime.getFullYear() - birthDate.getFullYear();
            //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
            if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
                age--;
            }
            props.formData['sAgeUnit'] = 'Y';
            props.formData['iAge'] = age;
            return
        }
        if (disMonth == 12 && nowDateTime.getDate() >= birthDate.getDate()) {
            props.formData['sAgeUnit'] = 'Y';
            props.formData['iAge'] = 1;
            return
        }
        if ((disMonth > 3 && disMonth <= 12) || (disMonth == 3 && nowDateTime.getDate() >= birthDate.getDate())) {
            let age = disMonth;
            //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
            if (nowDateTime.getDate() < birthDate.getDate()) {
                age--;
            }
            props.formData['sAgeUnit'] = 'M';
            props.formData['iAge'] = age;
            return
        }
        if ((disMonth > 1 && disMonth <= 3) || disMonth == 1 && nowDateTime.getDate() >= birthDate.getDate()) {
            let age = (nowDateTime.getTime() - birthDate.getTime()) / (7 * 24 * 60 * 60 * 1000);
            props.formData['sAgeUnit'] = 'W';
            props.formData['iAge'] = Math.round(age);
            return
        }
        let age = (nowDateTime.getTime() - birthDate.getTime()) / (24 * 60 * 60 * 1000);
        props.formData['sAgeUnit'] = 'D';
        props.formData['iAge'] = Math.floor(age);
        return
    }
    // 根据年龄计算生日（岁，月，周，日）
    function computedBirthDayByAge () {
        let formData = props.formData;
        let iAge = formData.iAge;
        // 如果配置数据 isAgeComputeBirthday 为 false，则不进行计算
        // if (!window.configs.businessParams.isAgeComputeBirthday) return
        if (!iAge) return
        let date = new Date();
        let birdthDay = '';
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        // 分别计算当前年龄单位为（岁，月，周，日）
        if (formData.sAgeUnit === "Y") {
            birdthDay = (year - iAge) + '-' + month + '-' + day
            birdthDay = (year - iAge) + `-${month}-${day}`
            // console.log(birdthDay)
        }
        if (formData.sAgeUnit === "M") {
            let month2Year = Math.floor(iAge / 12);
            let remainder = iAge % 12;
            birdthDay = (year - month2Year - 1) + '-' + (remainder > month ? 12 - remainder + month : month - remainder) + '-' + day;
        }

        if (formData.sAgeUnit === "W") {
            birdthDay = date.getTime() - iAge * 7 * 60 * 60 * 24 * 1000;
        }
        if (formData.sAgeUnit === "D") {
            birdthDay = date.getTime() - iAge * 60 * 60 * 24 * 1000;
        }
        props.formData['dBirthday'] = new Date(birdthDay);
    }

    // 处理身高输入
    function handleInputWeight (item) {
        let value = document.querySelector('.i-weight input').value;
        props.formData[item.sProp] = value = value.replace(/[\+\-\*/]/g, '');
        if(configData?.value?.medicineChooseType == 2) {
            props.formData['fRecipeDose'] = value > 0 ? (value * 0.1).toFixed(2) : undefined;
            return  
        }
        let nuclears = props.nuclearParams;
        if (!Object.keys(nuclears).length || nuclears.iIsInvariable) {
            return
        }
        let regx = item.sRegEx ? new RegExp(item.sRegEx) : null;
        let bool = (value && regx && regx.test(value)) || (value && !regx)
        props.formData['fRecipeDose'] = bool || (value > 0) ? (value * nuclears.fCoefficient).toFixed(2) : undefined;
    }

    // 用户配置
    const { configData } = useUserConfig()

    function onNameBlur() {
        !props.formData.sId && configData.value.isShowHistoryPatient && getPatientNameHistoryData(true)
        setPinYin()
    }

    const isShowHistoryPopover = ref(true)

    function hidePopover() {
        isShowHistoryPopover.value = false;
        let timeout = setTimeout(() =>{
            isShowHistoryPopover.value = true;
            clearTimeout(timeout);
        }, 30)
    }

    // const workStation = computed(() => {
    //     let temp = store.getters['user/workStation']
    //     return temp
    // })

    // 获取问诊室，注射室工作站'ApricotReportConsult'  ApricotReportInject
    // function getWorkStationByType (typeCode) {
    //     getWorkStationData({
    //         districtId: workStation.value.districtId,
    //         stationTypeCode: typeCode,
    //     }).then((res) => {
    //         if (res.success) {
    //             if(typeCode == 3) {
    //                 props.optionData.consultRoomOption = res.data || [];
    //                 props.optionData.consultRoomOption.map(item =>{
    //                     item.sValue = item.stationId
    //                     item.sName = item.stationName
    //                 })
    //                 return
    //             }
    //             props.optionData.injectionRoomOption = res.data || [];
    //             props.optionData.injectionRoomOption.map(item =>{
    //                 item.sValue = item.stationId
    //                 item.sName = item.stationName
    //             })
    //         }
    //     }).catch(() => {
    //     })
    // }
    const loadingImgNo = ref(false);
    function getImageNo() {
        const formData = {...props.formData}
            let jsonData = {
                sName: formData.sName,
                sSexCode: formData.sSex,
                dBirthday: formData.dBirthday,
                sIdNum: formData.sIdNum,
                sVisitTypeCode: formData.sSource,
                sInHospitalNO: formData.sInHospitalNO,
                sOutpatientNO: formData.sOutpatientNO
            };
            let tipMsg = {
                sName: '请填写患者姓名',
                sSexCode: '请填选患者性别',
                dBirthday: '请填选患者出生日期',
                sIdNum: '请填写患者身份证号',
                sVisitTypeCode: '请填选患者就诊类型',
                sInHospitalNO: '住院号和门诊号至少填写一项'
            }
            for (let key in jsonData) {
                if (key == 'sInHospitalNO' || key == 'sOutpatientNO') {
                    continue
                }
                if (!jsonData[key]) {
                    ElMessage.warning(tipMsg[key]);
                    return
                }
            }
            if (!jsonData.sInHospitalNO && !jsonData.sOutpatientNO) {
                ElMessage.warning(tipMsg.sInHospitalNO);
                return
            }
            loadingImgNo.value = true;
            brokenImgno(jsonData).then(res => {
                loadingImgNo.value = true;
                if (res.success) {
                    props.formData['sImageNo'] = res.data;
                    return
                }
                ElMessage.error(res.msg);
            }).catch(err => {
                loadingImgNo.value = false;
                console.log(err);
            })
    }

    // 监听性别变化
    watch(() => props.formData.sSex, val => {
        if (!props.visible) {
            return
        }
        let item_ = formList.value.find(item => item.sProp === 'iIsPregnant')
        if (!item_) {
            return
        }
        if (val == 1) {
            // 男性
            props.formData.iIsPregnant = 3
        } else if (val == 2) {
            // 女性
            props.formData.iIsPregnant = undefined
        } else {
            props.formData.iIsPregnant = 2
        }
    })
    // 打开弹窗监听，获取下拉数据
    watch(() => props.visible, (later) => {
        if(!later) {
            isSpellShow.value = false;
            isHistoryShow.value && (isHistoryShow.value = false);
            return
        }
        // if (!props.optionData.consultRoomOption.length) {
        //     getWorkStationByType(3)
        // }
        // if (!props.optionData.injectionRoomOption.length) {
        //     getWorkStationByType(4)
        // }
        setPinYin()
        if (!props.formData.iIsPregnant && props.formData.sSex == 1) {
            props.formData.iIsPregnant = 3
        }
        // 申请单来源的，才确定是否触发自动历史患者浮层
        if (props.defaultCheckInfo.isApplyForm && configData.value.isShowHistoryPatient) {
            getPatientNameHistoryData(true)
        }
        // 当申请单的身份证号有值时，计算生日、年龄（需求来自：重庆大坪医院）
        if(props.formData.sIdNum) {
            computedAgeByIdNums();
            return
        }
        // 当申请单的出生日期有值时，计算年龄
        if(props.formData.dBirthday) {
            computedAgeByBirthDay()
            return
        }
        // 当申请单只有年龄有值时，计算出生日期
        if(props.formData.iAge) {
            computedBirthDayByAge()
            return
        }
    }, { immediate: true, deep: true })

    const scrollApplyForm = ref(null)
    onMounted(() => {
        nextTick(() => {
            scrollApplyForm.value.update()
        })
    })

    const $eventbus = getEventbus();

    $eventbus.on('isMergedComputedAgeByIdNums', res => {
        if(res) {
            computedAgeByIdNums();
        }
    });
    $eventbus.on('isMergedComputedAgeByBirthDay', res => {
        if(res) {
            computedAgeByBirthDay();
        }
    });

    $eventbus.on('isMergedHandleInputWeight', res => {
        if(res) {
            handleInputWeight({props: 'fWeight'});
        }
    });

</script>
<style scoped lang="scss">
.container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .plane {
        width: 100%;
        background: white;

    }
    .item-01 {
        min-height: 200px;
        background: white;
        margin-bottom: 10px;
    }
    .item-02 {
        flex: 1;
        overflow: hidden;   
    }
    .scrollbar {
        height: calc(100% - 41px);
    }
}
.title {
    position: relative;
    height: 18px;
    padding:11px 10px;
    font-size: 16px;
    font-weight: 400;
    color: rgb(26, 32, 44);
    margin: 0 auto;
    border-bottom: 1px solid #eee;
    > h4 {
        border-left: 2px solid var(--el-color-primary);
        color: #494d4f;
        margin:0;
        padding-left:5px;
        font-weight: 400;
        line-height: 18px;
    }
}
</style>