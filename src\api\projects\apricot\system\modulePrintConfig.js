import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'
export default {

    getTemplateData(data) {
        return request({
            url: baseURL.apricot + '/template/findAllTemplate',
            method: 'POST',
            data
        })
    },
    saveTemlateSelect(data){
        return request({
            url: baseURL.apricot + '/modulePrint/saveModulePrint',
            method: 'POST',
            data
        })
    },
    // 查询打印设置
    getTemlateSelected(params){
        return request({
            url: baseURL.apricot + '/modulePrint/getModulePrintTemplateIdList',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 获取模块标识
    getModulesId() {
        return request({
            url: baseURL.apricot + '/sysModule/getAllSysModule',
            method: 'POST',
        })
    }
}

