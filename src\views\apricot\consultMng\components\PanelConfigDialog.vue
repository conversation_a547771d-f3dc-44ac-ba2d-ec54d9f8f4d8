<template>
    <span @click="dialogFormVisible = true" class="i-item">
        <i class="fa fa-page-setting"></i>
        <span>页面设置</span>

        <el-dialog v-model="dialogFormVisible" title="问诊管理设置" :close-on-click-modal="false" append-to-body destroy-on-close
            class="t-default my-dialog" width="700px">
            <div style="max-height: 60vh; overflow: auto;">
                <el-form :model="form" label-width="160px">
                    <!-- <el-form-item label="左侧界面宽度：">
                        <el-input v-model="form.leftLayoutWidth" autocomplete="off" style="width: 200px;" />
                    </el-form-item> -->
                    <el-form-item label="显示面板：">
                        <div class="flex flex-col">
                            <el-checkbox v-model="form.tabConsultForm"  label="ConsultForm">问诊信息</el-checkbox>
                            <el-checkbox v-model="form.tabHistoryReport" label="HistoryReport">历史报告</el-checkbox>
                            <el-checkbox v-model="form.tabScannerData" label="ScannerData">扫描资料</el-checkbox>
                            <el-checkbox v-model="form.tabClinicData" label="ClinicData">临床资料</el-checkbox>
                        </div>
                    </el-form-item>
                    <el-form-item label="组件配置按钮：">
                        <el-radio-group v-model="form.isShowConfigBtn">
                            <el-radio :label="true">显示</el-radio>
                            <el-radio :label="false">隐藏</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="数据查询：">
                        <el-checkbox v-model="form.isSearchFormCurrentWStation">只查询当前工作站数据</el-checkbox>
                    </el-form-item>
                    <el-form-item label="历史报告优先显示：">
                        <el-radio-group v-model="form.isHistoryPanelFirst">
                            <el-radio :label="true">是</el-radio>
                            <el-radio :label="false">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="历史报告匹配条件：">
                        <el-checkbox v-model="form.historyReportMatchConditions.hasRecordNo"
                            :true-label="1" :false-label="0">病历号</el-checkbox>
                        <el-checkbox v-model="form.historyReportMatchConditions.hasNuclearNum"
                            :true-label="1" :false-label="0">核医学号</el-checkbox>
                        <el-checkbox v-model="form.historyReportMatchConditions.hasIdNum"
                            :true-label="1" :false-label="0">身份证号</el-checkbox>
                    </el-form-item>
                    <el-form-item label="自动打印：">
                        <el-radio-group v-model="form.autoPrintType">
                            <el-radio :label="2">每次保存打印</el-radio>
                            <el-radio :label="1">首次保存打印</el-radio>
                            <el-radio :label="0">保存不打印</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="打印文件：">
                        <el-checkbox-group v-model="form.autoPrintClassifys" :disabled="!form.autoPrintType">
                            <div v-for="(item, index) in btnsOption" :key="index">
                                <el-checkbox :label="item.iClassify">{{ item.sClassify }}</el-checkbox>
                            </div>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="问诊单背景色：">
                        <el-color-picker small="small" v-model="form.patientInfoBgColor" show-alpha
                            :predefine="predefineColors" @active-change="changesTheme">
                        </el-color-picker>
                    </el-form-item>
                    <el-form-item label="tab页背景色：">
                        <el-color-picker small="small" v-model="form.tabBgColor" show-alpha :predefine="predefineColors"
                            @active-change="changestabBgColor">
                        </el-color-picker>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button-icon-fa icon="fa fa-save" type="primary" plain
                        @click="onSaveClick(1)">保存至全局</el-button-icon-fa>
                    <el-button-icon-fa icon="fa fa-save" type="primary" :disabled="loading"
                        @click="onSaveClick(0)">保存</el-button-icon-fa>
                    <el-button-icon-fa icon="el-icon-close" @click="dialogFormVisible = false">关闭</el-button-icon-fa>
                </span>
            </template>
        </el-dialog>
    </span>
</template>


<script>
import { deepClone } from "$supersetUtils/function"

import { exGetPrintClassifyOfModule } from '$supersetResource/js/projects/apricot/mixinPrintPreview.js'
import { useUserConfigSaveConfig } from '$supersetResource/js/projects/apricot/useUserConfig.js'

export default {
    emits: ['updateData'],
    props: ['iModuleId', 'formData', 'configKey'],
    data () {
        return {
            dialogFormVisible: false,
            form: {},
            btnsOption: [],
            userInfo: {},
            loading: false,
            predefineColors: [
                '#f3fae8',
                'rgba(229, 247, 231, 1)',
                'rgba(228, 237, 230, 1)',
                'rgba(238, 242, 252, 1)',
                'rgba(238, 239, 251, 1)',
                '#EAEAEF',
                'rgba(242, 244, 238, 1)',
                'rgba(227, 233, 228, 1)',
                'rgba(228, 235, 227, 1)',
                'rgba(252, 244, 234, 1)',
                'rgba(252, 240, 236, 1)',
                '#FFFFFF',
            ],
        }
    },
    watch: {
        dialogFormVisible (val) {
            if (val) {
                this.form = deepClone(this.formData);
                this.userInfo = this.$store.getters['user/userSystemInfo'];
                this.getPrintClassifyOfModule(this.iModuleId);
            }
        }
    },
    methods: {
        // 获取可打印模板类型
        getPrintClassifyOfModule: exGetPrintClassifyOfModule(),
        // 保存配置数
        useUserConfigSaveConfig: useUserConfigSaveConfig(),
        // 更新父组件数据
        upDateParentData () {
            this.$emit('updateData', this.form);
        },
        async onSaveClick (configType) {
            let params = {
                configKey: this.configKey,
                configValue: JSON.stringify(this.form),
                configType: configType,
                moduleId: this.iModuleId,
                userNo: this.userInfo.sNo
            }

            this.loading = true;
            await this.useUserConfigSaveConfig(params, this, this.upDateParentData);
            this.loading = false;
            this.dialogFormVisible = false;
        },
        changesTheme (val) {
            this.form.patientInfoBgColor = val
        },
        changestabBgColor (val) {
            this.form.tabBgColor = val
        }
    }
}
</script>

<style lang="scss" scoped>
.i-item {
    margin: 0 -10px;
    padding: 0 10px;
    display: flex;

    >i {
        height: 20px;
    }
}
</style>