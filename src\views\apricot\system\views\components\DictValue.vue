<template>
    <Panel class="panel-view">
        <template v-slot:header>
            <!-- 搜索框 -->
            <div class="relative flex gap-3 items-center h-full top-1">
                <div class="inline-block flex-1 text-base">
                    字典名称： <span class=" font-bold">{{ dictName }}</span>
                </div>
                <div class="action-button-group">
                    <el-button-icon-fa icon="el-icon-plus"
                        @click="mxOpenDialog(1, '新增字典业务值')"
                        type="primary">新增</el-button-icon-fa>
                </div>
            </div>
        </template>
        <template v-slot:content>
            <div class="table-layout-fixed">
                <!-- <div class="table-title">
                    <el-button :disabled="0 && !auth['/bsuser/dictValue/add']" size="small" @click="mxOpenDialog(1, '新增字典值')" ticon="el-icon-plus">添加</el-button>
                </div> -->
                <div class="table-container">
                    <!-- 内容 -->
                    <el-table :data="tableData"
                        v-drag:[config]="tableData"
                        ref="mainTable"
                        border
                        stripe
                        height="100%"
                        style="width: 100%">
                        <el-table-column prop="itemCode"
                            show-overflow-tooltip
                            label="字典项编码"></el-table-column>
                        <el-table-column prop="itemName"
                            show-overflow-tooltip
                            label="字典项名称"></el-table-column>
                        <el-table-column prop="remark"
                            show-overflow-tooltip
                            sortable
                            min-width="110"
                            label="备注说明"></el-table-column>

                        <el-table-column show-overflow-tooltip
                            label="操作"
                            width="220"
                            align="center">
                            <template v-slot="scope">
                                <el-button title="编辑"
                                    @click="mxOnClickRowAction(scope.row, 2, '编辑')"
                                    type="primary"
                                    link
                                    size="small">
                                    编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit"
                                            size="14"></Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button title="删除"
                                    @click="mxOnClickRowDel(scope.row, `确定要删除【 ${scope.row.itemName} 】吗？`)"
                                    size="small"
                                    link
                                    class="color-red">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete"
                                            size="14"></Icon>
                                    </template>
                                </el-button>

                                <el-button size="small"
                                    link
                                    class="i-sort">排序
                                    <template #icon>
                                        <Icon name="el-icon-rank"
                                            size="14">
                                        </Icon>
                                    </template>
                                </el-button>
                            </template>
                        </el-table-column>

                    </el-table>
                </div>
            </div>

            <!-- 新增-编辑弹窗  -->
            <el-dialog v-model="editLayer.visible"
                :title="editLayer.playerText"
                :close-on-click-modal="false"
                :destroy-on-close="true"
                append-to-body
                width="620px"
                class="t-default">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules">
                    <el-row>
                        <el-col :span="24">
                            <el-form-item prop="itemCode"
                                label="字典项编码"
                                class="item-block"
                                labelWidth="110px">
                                <el-input v-model="editLayer.form.itemCode"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="itemName"
                                label="字典项名称"
                                class="item-block"
                                labelWidth="110px">
                                <el-input v-model="editLayer.form.itemName"></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="24">
                            <el-form-item prop="state" label="状态" class="item-block" labelWidth="110px">
                                <el-switch
                                    v-model="editLayer.form.state"
                                    active-color="#13ce66"
                                    inactive-color="#ff4949"
                                    :active-value="1"
                                    :inactive-value="0">
                                </el-switch>
                            </el-form-item>
                        </el-col> -->

                        <el-col :span="24">
                            <el-form-item prop="remark"
                                label="备注说明"
                                class="item-block"
                                labelWidth="110px">
                                <el-input type="textarea"
                                    :rows="2"
                                    v-model="editLayer.form.remark"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <div>
                        <el-button @click="mxDoSaveData('refEditLayer')"
                            :loading="editLayer.loading"
                            type="primary"
                            class="i-primary">
                            <template #icon>
                                <Icon name="el-icon-document-checked"
                                    size="14"></Icon>
                            </template>
                            保 存
                        </el-button>
                        <el-button ticon="el-icon-close"
                            class="i-warning"
                            @click="editLayer.visible = false">
                            <template #icon>
                                <Icon name="el-icon-close"
                                    size="14"></Icon>
                            </template>
                            关 闭
                        </el-button>

                    </div>
                </template>
            </el-dialog>
        </template>
    </Panel>
</template>
<script>
import { ElMessage } from 'element-plus'

// 接口
import ApiDictionary from '@/api/projects/apricot/system/dictionary';


import TableCURDMixin from "@/resource/js/common/mixin/TableCURDMixin";
import TableAPIMixin from "@/resource/js/common/mixin/TableAPIMixin";

import { mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'

const STATES = {
    '': '不限',
    1: "禁用",
    0: "正常",
};
export default {
    components: {
    },
    props: {
        dictId: '',
        dictName: '',
    },
    mixins: [TableCURDMixin, TableAPIMixin, mixinTableDrag],
    data () {
        return {
            notFirstLoad: true,
            condition: {
                dictId: this.dictId,
            },
            defualtVal: {
                editLayer: {
                    state: 1
                }
            },
            ApiTable: {
                pageList: ApiDictionary.getItemByDict,
                add: ApiDictionary.createItem,
                update: ApiDictionary.editItem,
                del: ApiDictionary.deleteItem,
            },
            rules: {
                itemCode: [{ required: true, message: '必填' }],
                itemName: [{ required: true, message: '必填' }],
            },
            sortApi: ApiDictionary.settingDictSort, // 排序接口
            sortKey: 'itemId' //排序字段
        }
    },
    watch: {
        dictId: {
            handler () {
                if (this.dictId !== undefined) {
                    this.mxGetTableList();
                } else {
                    this.tableData = [];
                }
            },
            immediate: true
        }
    },
    methods: {
        callBackSave (params) {
            if (this.actionState == 1) {
                params.dictId = this.dictId;
                // 新增
                this.ApiTable.add(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        ElMessage.success(res.msg);
                        this.editLayer.visible = false;
                        this.mxGetTableList();

                        return;
                    }
                    ElMessage.error(res.msg);
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            } else {
                if (params.dictValueList === null) {
                    delete params.dictValueList;
                }
                if (params.app) {
                    delete params.app;
                }
                // 编辑
                this.ApiTable.update(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        ElMessage.success(res.msg);
                        this.editLayer.visible = false;
                        this.mxGetTableList();

                        return;
                    }
                    ElMessage.error(res.msg);
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        callBackPageList (params) {
            // params.condition.dictId = this.dictId;
            this.ApiTable.pageList({ dictId: this.dictId }).then((res) => {
                if (res.success) {
                    this.tableData = res.data || [];
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected();
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        callBackDel (item) {
            this.ApiTable.del({ itemId: item.itemId }).then((res) => {
                if (res.success) {
                    ElMessage.success(res.msg);
                    this.mxGetTableList();
                    return;
                }
                ElMessage.error(res.msg);
            }).catch((e) => {
                console.log(e)
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.top-1 {
    padding: 4px;
}
</style>
