<template>
  <div class="container " >
    <div class="main-form-list" ref="mainRef">
      <el-popover v-if="delayRender1 && configBtn === true" ref="popoverRef" trigger="click" :placement="isSmallScreen ? 'right-end' : 'left'" :width="isSmallScreen ? '580' : 'auto'" :teleported="true"
        transition="none" :hide-after="0" @after-enter="onPopAfterEnter">
        <template #default>
          <div class="pop-header">
            <h3>{{ ('表单配置') }}</h3>
          </div>
          <div class="w-full overflow-auto">
            <!-- el-skeleton 的 width=表头宽度 -->
            <div v-if="!isRenderPopover" style="width: 1020px; height:450px; overflow: hidden;">
                <el-skeleton :rows="12" />
            </div>
            <el-table v-else class="pop-table" :data="react.tableData" row-key="prop" ref="popTableRef" height="450" highlight-current-row>
              <el-table-column prop="label" :label="('排序')" align="center" width="50">
                <el-icon class="drag-icon" :size="18">
                  <Rank />
                </el-icon>
              </el-table-column>
              <el-table-column prop="label" :label="('显示')" align="center" width="50">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isShow" @change="onChangeItemProperty(scope, 'isShow')"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="label" :label="('字段名称')" width="120" align="left">
                <template #default="scope">
                  {{ scope.row.label }}
                </template>
              </el-table-column>
              <el-table-column prop="align" :label="('对齐方式')" align="center" width="130">
                <template #default="scope">
                  <el-radio-group class="radio-group" @change="onChangeItemProperty(scope, 'align')"
                    v-model="scope.row.align">
                    <el-radio-button label="left">左</el-radio-button>
                    <el-radio-button label="center">中</el-radio-button>
                    <el-radio-button label="right">右</el-radio-button>
                  </el-radio-group>
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('宽度%')" width="120" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    宽度(%)
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false" :persistent="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in widthOptions" :label="i" :value="i"
                              @click="setAllItemsVal('width', i)">
                              全部设为{{ i }}%
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <el-select v-model="scope.row.width" allow-create filterable :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'width')">
                    <el-option v-for="i in widthOptions" :label="i" :value="i"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="height" :label="('高度px')" width="110" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    高度
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false" :persistent="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in heightOptions" :label="i" :value="i"
                              @click="setAllItemsVal('height', i)">
                              全部设为{{ i }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <el-select v-model="scope.row.height" allow-create filterable :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'height')">
                    <el-option v-for="i in heightOptions" :label="i" :value="i"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('文字大小')" width="120" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    文字大小
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false" :persistent="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in fontSizeOptions" :label="i" :value="i"
                              @click="setAllItemsVal('fontSize', i)">
                              全部设为{{ i }}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <el-select v-model="scope.row.fontSize" allow-create filterable :teleported="false" :persistent="false"
                    @change="onChangeItemProperty(scope, 'fontSize')">
                    <el-option v-for="i in fontSizeOptions" :label="i" :value="i"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('文字颜色')" width="80" align="center">
                <template #default="scope">
                  <el-color-picker v-model="scope.row.color" :teleported="false" :persistent="false" size="default"
                    @change="onChangeItemProperty(scope, 'color')" />
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('背景颜色')" width="80" align="center">
                <template #default="scope">
                  <el-color-picker v-model="scope.row.bgColor" :teleported="false" :persistent="false" size="default"
                    @change="onChangeItemProperty(scope, 'bgColor')" />
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('文字加粗')" width="80" align="center">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isBold" @change="onChangeItemProperty(scope, 'isBold')"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="width" :label="('必填')" width="80" align="center">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.required" @change="onChangeItemProperty(scope, 'required')"></el-checkbox>
                </template>
              </el-table-column>
              <!-- 注射或者上机表单 -->
              <el-table-column v-if="iModuleId == 4|| iModuleId == 5" prop="defaultValue" :label="('默认值')" width="100" align="center">
                <template #default="scope">
                  <el-input v-model="scope.row.defaultValue" :disabled="scope.row.disabledSetDefault" @blur="onChangeItemProperty(scope, 'defaultValue')"></el-input>
                </template>
                <template #header>
                    <span>默认值</span>
                    <el-tooltip placement="top">
                        <template #content>
                            <div>默认值规则：</div>
                            <div class="pl-3">下拉框：期望值：n（初始值0），表单初始化时找到下拉框第n项的数据填入；不填任何值，表单初始化时为空。</div>
                            <div class="pl-3">日期框：期望值：1，表单初始化时填入日期；不填任何值：表单初始化时为空。</div>
                            <div class="pl-3">普通文本/数字/文本域：无特殊规则；不填任何值，表单初始化时为空。</div>
                        </template>
                        <el-icon class="fa fa-question-circle" size="16"></el-icon>
                    </el-tooltip>
                </template>
              </el-table-column>


            </el-table>
          </div>
          <div class="flex items-center justify-between pt-2">
            <span class="flex">
              <el-button type="primary" @click="setStorage(1)">{{ ('保存到全局') }}</el-button>
              <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否恢复默认设置？')"
                :teleported="false" :persistent="false" @confirm="onClickResetConfig">
                <template #reference>
                  <el-button class="ml-2" type="primary" plain>{{ ('恢复默认设置') }}</el-button>
                </template>
              </el-popconfirm>

               <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否读取全局设置？')"
                :teleported="false" :persistent="false" @confirm="onClickGlobalStorage">
                <template #reference>
                  <el-button class="ml-2" type="primary" plain>{{ ('恢复全局设置') }}</el-button>
                </template>
              </el-popconfirm>
            </span>
            <span class="flex">
              <el-button type="default" @click="onClickClose">{{ ('关闭') }}</el-button>
            </span>

          </div>
        </template>
        <template #reference>
          <div v-if="configBtn === true" class="config-btn">
            <!-- <div class="triangle-wrap"> -->
            <el-icon class="setting-icon">
              <MoreFilled />
            </el-icon>
            <!-- </div> -->
            <!-- <span class="str">
          更多
        </span> -->
          </div>
        </template>
      </el-popover>

      <div class="grid-box" v-if="reloading">
        <el-skeleton :rows="parseInt(list.length / 3)" animated :throttle="100" />
      </div>
      <div v-else class="grid-box">
        <div class="cell-box" v-for="(item, index) in showingContent" :label="(item.label)" :key="item.label + index"
          :style="{
            width: (item.width || '100') + '%',
          }">
          <el-form-item :prop="item.prop" :label="item.label" :labelWidth="item.labelWidth" :required="item.required"
            :rules="getValidator(item)" :class="item.formItemClass" size="default" :style="{
              width: '100%'
            }">
            <template v-if="item.iCustom">
              <slot :name="item.prop" :row="item" :style="getElementInjectStyle(item)"></slot>
            </template>
            <template v-else>
              <!-- 文本 -->
              <el-input v-if="item.sInputType === 'text'" v-model="formData[item.sProp]" :readonly="!!item.iReadonly"
                clearable :style="{ ...getElementInjectStyle(item) }"></el-input>
              <!-- 数字 -->
              <!-- :precision="2" -->
              <el-input-number v-else-if="item.sInputType === 'number'" size="default" v-model="formData[item.sProp]"
                :min="0" :max="99999999" :readonly="!!item.iReadonly" :controls="false"
                :style="{ ...getElementInjectStyle(item) }"></el-input-number>
              <!-- 颜色 -->
              <el-color-picker v-else-if="item.sInputType === 'color'" type="color" :predefine="predefineColors"
                v-model="formData[item.sProp]" :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item) }"
                @change="(val) => { changeColorPicker(val, item.sProp) }"></el-color-picker>
              <!-- 日期 -->
              <el-date-picker v-else-if="item.sInputType === 'date-picker'" v-model="formData[item.sProp]" type="date"
                size="default" :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item) }"
                :picker-options="isPickerOptions ? pickerOptions : {}">
              </el-date-picker>
              <!-- 日期时间 -->
              <el-date-picker v-else-if="item.sInputType === 'dateTime-picker'" v-model="formData[item.sProp]"
                type="datetime" size="default" :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item) }"
                :picker-options="isPickerOptions ? pickerOptions : {}">
              </el-date-picker>
              <!-- 时间 -->
              <el-time-picker v-else-if="item.sInputType === 'time-picker'" v-model="formData[item.sProp]"
                :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item) }">
              </el-time-picker>

              <!-- 下拉选择 -->
              <el-select v-else-if="item.sInputType === 'option'" v-model="formData[item.sProp]" v-select-name="{ formData, fields: item }"
                :disabled="!!item.iReadonly" placeholder=" " clearable :style="{ ...getElementInjectStyle(item) }"
                @change="$forceUpdate()">
                <el-option v-for="_i in optionData[item.sOptionProp]" v-show="_i.iIsAuxiliary != 1"
                  :key="_i[item.optionValue]" :label="_i[item.optionLabel]"
                  :value="typeof formData[item.sProp] === 'number' ? Number(_i[item.optionValue]) : _i[item.optionValue]">
                </el-option>
              </el-select>

              <!-- 文本-单位 -->
              <div style="width: 100%; height: 100%; display: flex;" v-else-if="item.sInputType === 'text-unit'">
                <el-input size="default" v-model="formData[item.sProp]" controls-position="right"
                  :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item), flex: 1 }">
                  <template #suffix>
                    <span class="i-suffix-span">{{ item.sUnit }}</span>
                  </template>
                </el-input>
              </div>
              <!-- 数字-单位 -->
              <div style="width: 100%; height: 100%; display: flex;" v-else-if="item.sInputType === 'number-unit'">
                <el-input-number size="default" v-model="formData[item.sProp]" :controls="false" :precision="2" :min="0"
                  :max="99999999" :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item), flex: 1 }">
                  <template #suffix>
                    <span class="i-suffix-span">{{ item.sUnit }}</span>
                  </template>
                </el-input-number>
              </div>
              <!-- 文本域 -->
              <el-input v-else-if="item.sInputType === 'textarea'" type="textarea"
                :rows="item.sHeight ? Math.round(parseInt(item.sHeight) / 36) : 2" v-model="formData[item.sProp]"
                :readonly="!!item.iReadonly" :style="{ ...getElementInjectStyle(item) }"></el-input>
              <!-- 输入值后自动标记- 文本1 -->
              <div v-else-if="item.sInputType === 'input-01'" class="c-check-input"
                :class="[(formData[item.sProp] && formData[item.sProp].length) ? 'i-start' : 'i-disable']">
                <div class="c-i-innner">
                  <i class="i-check"></i>
                </div>
                <el-input v-model="formData[item.sProp]" :readonly="!!item.iReadonly"
                  :style="{ ...getElementInjectStyle(item), flex: 1 }"></el-input>
              </div>
              <!-- 输入值后自动标记- 文本2 -->
              <div v-else-if="item.sInputType === 'input-02'" class="c-check-input"
                :class="[(formData[item.sProp] && formData[item.sProp].length) ? 'i-start' : 'i-disable']">
                <div class="c-i-innner">
                  <i class="i-check"></i>
                </div>
                <el-input v-model="formData[item.sProp]" :readonly="!!item.iReadonly"
                  :style="{ ...getElementInjectStyle(item), flex: 1 }">
                  <template #suffix>
                    <span class="i-suffix-span">{{ item.sUnit }}</span>
                  </template>
                </el-input>
              </div>
              <!-- 选中值后自动标记-下拉框 -->
              <div v-else-if="item.sInputType === 'option-01'" class="c-check-input"
                :class="[(formData[item.sProp] && formData[item.sProp].length) ? 'i-start' : 'i-disable']">
                <div class="c-i-innner">
                  <i class="i-check"></i>
                </div>
                <el-select class="i-set-padding" v-model="formData[item.sProp]" :readonly="!!item.iReadonly" placeholder=" " v-select-name="{ formData, fields: item }"
                  :style="{ ...getElementInjectStyle(item), flex: 1 }">
                  <el-option v-for="_i in optionData[item.sOptionProp]" v-show="_i.iIsAuxiliary != 1"
                    :key="_i[item.optionValue]" :label="_i[item.optionLabel]" :value="_i[item.optionValue]">
                  </el-option>
                </el-select>
              </div>
            </template>

          </el-form-item>
          <!-- <div class="cell-label" :style="{
            width: String(item.labelWidth)
          }">
            <span upperLabel>
              {{ item.label }}
            </span>
          </div>
          <div class="cell-content" :style="{}">

          </div> -->

        </div>
      </div>

    </div>


  </div>
</template>
<script>
import draggable from 'vuedraggable';
import Sortable from 'sortablejs'
import { Rank, MoreFilled, ArrowDown } from '@element-plus/icons-vue'
import { cloneDeep, isArray } from 'lodash-es';
import { unref } from 'vue';
import { useStore } from 'vuex';

import { getOnlineConfig, saveOnlineConfig, compareAndModifyArrays, getGlobalOnlineConfig } from '@/utils'

const defaultWidth = '25'
// const defaultHeight = ''
const defaultHeight = '36'
const defaultLabelWidth = '100px'
const defaultFontSize = '16'
const defaultColor = '#3c4353'
const defaultBgColor = ''

const defaultIsBold = false

const defaultStorageData = {
  list: []
}

export default {
  name: "FormList",
  components: {
    draggable,
    Rank, MoreFilled, ArrowDown
  },
  props: {
    storageKey: {
      type: String,
      default: "", // 不填不启用localstorage
    },
    iModuleId: {
      default: "", // 不填不启用数据上传
    },
    list: {  // 自定义数据
      type: Array,
      default: [], //  
    },
    formData: { // 绑定的表单数据对象
      type: Object,
      default: () => ({})
    },
    rules: { //  表单校验
      type: Object,
      default: () => ({})
    },
    optionData: { // 下拉选项
      type: Object,
      default: () => ({})
    },
    configBtn: {
      type: [Object, Boolean],
      default: () => true
    },
    labelWidth: {
      default: "",
    },
    isPickerOptions: false
  },
  watch: {
    // 注射管理设备类型不一样，保存key不同
    storageKey(val,oldValue) {
      this.initForm()
    }
  },
  setup(props, context) {
    const _store = useStore()

    const isSmallScreen = window.document.body.clientWidth < 1400 ? true : false


    const buttonRef = ref();
    const popoverRef = ref();
    const mainRef = ref();
    const react = ref({
      defaultConfigContent: [],
      tableData: [],

    });

    const configButtonRef = computed(() => {
      if (props.configBtn && props.configBtn.ref) {
        return props.configBtn
      }
      return buttonRef
    });

    const showingContent = computed(() => {
      return react.value.tableData.filter((i) => i.isShow)
    })

    const userNo = computed(() => {
      let userInfo = _store.getters["user/userSystemInfo"] || {};
      return userInfo.sId
    })

    const formDataRef = computed(() => {
      return props.formData
    });

    const stopWatchFormdata = ref(false)

    watch(formDataRef, (formDataObj) => {
      if (stopWatchFormdata.value) return

      const defaultValueItems = react.value.tableData.filter(item => {
        if (item.defaultValue === undefined || item.defaultValue === null) {
          return false
        }
        if (String(item.defaultValue).trim() !== '') {
          return true
        }
      })

      defaultValueItems.forEach(item => {
        stopWatchFormdata.value = true
        if (formDataObj[item.prop] === undefined || formDataObj[item.prop] === null) {
          formDataObj[item.prop] = item.defaultValue
        }
      })
      stopWatchFormdata.value = false
    }, { deep: true })

    return {
      userNo,
      react,
      isSmallScreen,

      // buttonRef,
      // configButtonRef,
      popoverRef,
      mainRef,
      defaultHeight,
      delayRender1: ref(false),
      isRenderPopover: ref(false),
      reloading: ref(false),

      showingContent,
      predefineColors: window.configs.colors.elementConfig,
      pickerOptions: {
        disabledDate: time => {
          return time.getTime() > new Date().getTime()
        }
      },
      getOnlineConfig,
      getGlobalOnlineConfig,
      saveOnlineConfig,
      widthOptions: [20, 25, 33.3, 50, 100],
      heightOptions: [ 30, 32,34, 36, 38, 40, 42, 44, 46, 48, 50],
      fontSizeOptions: [12, 14, 16, 18, 20, 22, 24, 26, 28],
    };
  },


  methods: {
    /*  读取配置 isGetGlobal 是否获取全局配置 */
    async getStorage(isGetGlobal = false) {
      if (!this.storageKey) {
        this.setDefaultData()
        return
      };
      this.reloading = true
      let storageStr = localStorage.getItem('FormList-' + this.storageKey);
      let storageObj = {
        ...defaultStorageData
      }
      if (this.iModuleId && this.storageKey) {
        if(isGetGlobal) {
            await this.getGlobalOnlineConfig()
        } else {
            await this.getOnlineConfig()
        }
        Object.assign(storageObj,  this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])
      } else {
        try {
          const parsed = JSON.parse(storageStr)
          if (Array.isArray(parsed)) {
            storageObj.list = parsed
          } else if (parsed && parsed.list) {
            storageObj = parsed
          }
        } catch (error) {
          console.error(error)
        }
      }


      let list = storageObj.list;

      const propList = list.map((i) => i.prop);
      const inputList = this.getInputList();
      let newList = compareAndModifyArrays(inputList, list);
    //   let newList = []

    //   if (propList.length !== inputList.length) {
    //     // 如果表格加了新的一列数据，恢复成默认顺序
    //     newList = inputList
    //   } else {
    //     // newList // 按localstor排序后的slot列表
    //     propList.forEach(prop => {
    //       const index = inputList.findIndex(slot => slot.prop === prop)
    //       if (index > -1) {
    //         newList.push(inputList.splice(index, 1)[0])
    //       }
    //     })
    //     newList = [...inputList].concat(newList)
    //   }


      this.react.defaultConfigContent = newList

      // 将store存贮的属性值还原到当前数据
      let index
      let newTable = cloneDeep(newList).map((item) => {
        index = list.findIndex(target => item.prop === target.prop)
        if (index > -1) {
          return this.dataObjConvert({ ...item, ...list[index] })
        }
        return this.dataObjConvert(item)
      });


      this.react.tableData = newTable

      this.reloading = false

    },
    // 设置缓存
    setStorage(isGlobal = 0) {
      if (!this.storageKey) return;

      const storageObj = {
        list: this.react.tableData
      }

      const storageString = JSON.stringify(storageObj)

      localStorage.setItem(
        "FormList-" + this.storageKey,
        storageString
      );

      this.saveOnlineConfig(storageObj, isGlobal)

    },
    getInputList() {
      const inputList = isArray(this.list) ? this.list : []
      return inputList.map(i => {
        const obj = {
          ...i,
          prop: i.sProp || i.prop,
          label: i.sLabel || i.label,
          width: i.width || (i.iLayourValue ? `${(i.iLayourValue / 24 * 100).toFixed(2).slice(0, -1)}` : '') || '',
          required: !!i.iRequired || !!i.required,
        }


        return obj
      })
        .filter(item => item.prop)
        .filter(item => !item.iIsHide)


    },
    setDefaultData() {
      this.react.defaultConfigContent = this.getInputList()
      this.react.tableData = this.react.defaultConfigContent.map(this.dataObjConvert);
    },
    dataObjConvert(item) {
      return {
        ...item,
        prop: item.prop || "",
        label: item.label || " ",
        isShow: item.isShow === false ? false : true,
        align: item.align || "left",
        width: String(item.width || defaultWidth).replace(/\%/g, ''),
        height: item.height || defaultHeight,
        labelWidth: item.labelWidth || this.labelWidth || defaultLabelWidth,
        color: item.color || defaultColor,
        isBold: !!item.isBold || defaultIsBold,
        fontSize: item.fontSize || defaultFontSize,
        bgColor: item.bgColor || defaultBgColor,
        required: !!item.required,

        optionLabel: item.optionLabel || 'sName',
        optionValue: item.optionValue || 'sValue',
        defaultValue: item.defaultValue || '',
        disabledSetDefault: !!item.disabledSetDefault
      }
    },
    getElementInjectStyle(item) {
      const _height = String(item.height).replace(/[px]/g, '') + 'px'
      return {
        '--el-component-size': _height,
        '--el-input-height': _height,
        '--el-input-inner-height': `calc(${_height} - 2px)`,
        '--el-input-number-controls-height': `calc(${_height} / 2) !important`,
        '--el-input-text-color': item.color,
        '--el-text-color-regular': item.color,
        '--el-fill-color-blank': item.bgColor,
        '--el-font-size-base': String(item.fontSize).replace(/[px]/g, '') + 'px',
        '--el-input-font-weight': item.isBold ? 900 : 400,
        '--el-input-text-align': item.align,
        width: '100%'
      }
    },
    onChangeItemProperty(scope, key) {
      const index = scope.$index;
      const table = this.react.tableData[index];
      table[key] = scope.row[key];

      this.setStorage();
    },
    setAllItemsVal(key, val) {
      this.react.tableData.forEach(item => {
        item[key] = val
      })
      this.setStorage();
    },
    // 读取全局设置
    async onClickGlobalStorage() {
        this.getStorage(true).then(() => {
            setTimeout(() => {
                this.setStorage();
            }, 10)
        });
    },
    // 点击重置
    onClickResetConfig() {
      this.setDefaultData();
      this.setStorage();
    },
    onClickClose() {
      this.$el.click()
    },

    onPopAfterEnter() {
      this.isRenderPopover = true;
      this.$nextTick(() => {
        this.rowDrop()
      })
    },
    //行拖拽
    rowDrop() {
      const popTable = (this.$refs.popTableRef.$el)
      const tbody = popTable.querySelector('tbody')
      Sortable.create(tbody, {
        disabled: false, // 是否开启拖拽
        handle: ".drag-icon",
        // ghostClass: 'sortable-ghost', //拖拽样式
        animation: 150, // 拖拽延时，效果更好看
        group: { // 是否开启跨表拖拽
          pull: false,
          put: false
        },
        onEnd: ({ newIndex, oldIndex }) => {
          const currRow = this.react.tableData.splice(oldIndex, 1)[0]
          this.react.tableData.splice(newIndex, 0, currRow)
          const _currRow = this.react.defaultConfigContent.splice(oldIndex, 1)[0]
          this.react.defaultConfigContent.splice(newIndex, 0, _currRow)
          this.$nextTick(() => {
            this.setStorage();
          });
        }
      })
    },
    getValidator(item) {
      const inputRules = this.rules ? (this.rules[item.prop] || []) : []
      let validArr = []
      if (isArray(inputRules)) {
        validArr = [...inputRules]
      }

      const requiredValidator = {
        required: true,
        message: '',
        trigger: 'blur'
      }
      const limitValidator = {
        max: Number(item.iLimitLength),
        message: `超过最大长度${item.iLimitLength}`,
        trigger: 'change'
      }
      const regValidator = {
        validator: (rule, value, callback) => {
          if (value == undefined || value == null || value == '') {
            callback()
          }
          let reg = new RegExp(item.sRegEx);
          if (reg.test(value)) {
            callback()
          }
          callback(new Error(item.sRegExText || '请填入正确内容'))
        },
        trigger: 'change'
      }

      if (item.iRequired) validArr.push(requiredValidator)
      if (item.iLimitLength) validArr.push(limitValidator)
      if (item.sRegEx) validArr.push(regValidator)

      return validArr
    },
    getFilteredFormData() {
      // 过滤掉不显示的字段值
      const propList = this.react.tableData.filter((i) => !i.isShow).map(i => i.prop)
      const outputData = cloneDeep(unref(this.formData))
      propList.forEach(prop => {
        delete outputData[prop]
      })

      this.$emit('update:formData', outputData)
      return outputData
    },
    initForm() { 
        this.getStorage();
    }
    
  },
  created() {
  },
  mounted() {
    // 更改配置后更新formdata
    watch(this.react.tableData, () => {
      this.getFilteredFormData()
    })


    this.initForm()

    // 卡顿延迟加载
    setTimeout(() => {
      this.delayRender1 = true 
    }, 50);
  }
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 100%;
  // color: var(--theme-color);
  // background: var(--theme-bg);
  font-size: 13px;
  border-right: 0;
  border-bottom: 0;
  overflow: hidden;
  box-sizing: border-box;

  .main-form-list {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    &:hover {
      .config-btn {
        visibility: visible;
      }
    }

    .config-btn {
      position: absolute;
      display: flex;
      visibility: hidden;
      align-items: center;
      justify-content: center;
      top: 0;
      right: 4px;
      width: 18px;
      height: 18px;
      border: none;
      border-radius: 4px;
      color: #888;
      // background: var(--el-color-primary);
      // border-radius: 50%;
      overflow: hidden;
      z-index: 3;
      cursor: pointer;

      &:hover {
        background: #ccc;
        // filter: contrast(1.5);
        // transition: all ease .4s;
      }

      .setting-icon {
        position: relative;
        display: inline-block;
        width: auto;
        top: 0px;
        right: 0;
        font-size: 16px;
        color: var(--el-button-bg-color);
      }
    }


    .grid-box {
      position: relative;
      flex: 1;
      display: table;
      padding: 18px 0 0 0;
      box-sizing: border-box;

      .cell-box {
        position: relative;
        float: left;
        display: flex;
        flex-direction: row;
        box-sizing: border-box;
        padding: 0 5px;

        :deep(.el-form-item__label) {
          margin-bottom: 0;
          font-weight: bold;
          color: #3c4353;

        }

        .cell-label {
          display: flex;
          flex: 0 0 auto;
          box-sizing: border-box;
          padding: 0 10px;
          height: 36px;

          align-items: center;
          justify-content: flex-end;


          span {
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 18.48px;
            vertical-align: top;

          }

        }

        .cell-content {
          display: flex;
          flex: 1 1 auto;
          box-sizing: border-box;
          padding: 0 10px;

          color: rgba(45, 55, 72, 1);
          align-items: center;
          justify-content: flex-start;

          word-break: break-all;
          overflow: auto;

          span {
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 0px;
            line-height: 18.48px;

          }

        }

        :deep(.el-input-number .el-input__inner) {
            text-align: left;
        }
      }

    }
  }



}

.pop-header {
  display: flex;
  align-items: center;
  padding: 6px 0 10px;
  justify-content: space-between;

  h3 {
    margin: 0 0 0 10px;
  }
}

.drag-icon {
  position: relative;
  display: inline-flex;
  min-height: 25px;
  align-items: center;
  justify-content: center;
  cursor: move;
  vertical-align: middle;
}

.radio-group {
  height: 30px;

  :deep(.el-radio-button__inner) {
    padding: 5px 11px;
    font-size: 12px;
    border-radius: 0;
  }
}




.i-suffix-span {
  color: #666;
  font-size: 14px;
}



.cell-box {
  :deep(.el-select) {
    width: 100%;

    &.i-set-padding {
      .el-input .el-input__inner {
        padding-left: 40px;
      }
    }
  }

  :deep(.el-input-number) {
    width: 100%;


    .el-input-number__decrease {
      height: 50% !important;
      top: initial !important;
      line-height: initial !important;

      .el-icon-arrow-down {
        top: calc((100% - 11px) / 2);
        position: relative;
      }
    }

    .el-input-number__increase {
      height: 50% !important;
      line-height: 0px !important;

      .el-icon-arrow-up {
        top: calc((100% - 11px) / 2);
        position: relative;
      }
    }
  }

  :deep(.el-date-editor) {
    width: 100%;

    .el-icon-date,
    .el-icon-time {
      line-height: initial;
      padding-top: 6px;
    }
  }


  :deep(.el-color-picker) {
    height: 100%;
    width: 100%;

    .el-color-picker__trigger {
      height: 100%;
      width: 100%;
    }
  }

  :deep(.el-form-item) {
    margin-right: 0;
  }

  :deep(.el-input__wrapper) {
    box-sizing: border-box;
  }

  // .i-suffix-value {
  //   width: 80px;
  //   height: inherit;
  //   display: inline-block;

  //   :deep(input) {
  //     border-left: 0px;
  //   }
  // }
  :deep(.el-input__suffix) {
    height: 0;
  }
}

// 处理popover内弹窗遮挡
.pop-table {
  min-height: 420px;
  width: fit-content;
  max-width: 9999px;
  // .el-table--enable-row-transition .el-table__body td.el-table__cell
  :deep(.el-table__cell) {
    position: static;

    .cell {
      overflow: visible;
    }
  }

  :deep(.el-table__header-wrapper) {
    overflow: visible;
  }
}
</style>
