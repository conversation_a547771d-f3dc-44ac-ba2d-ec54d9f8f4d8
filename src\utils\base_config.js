/*请求相关数据配置-------------------------------*/

export const tokenName = 'aptSessionId' 

export const requestConfig = {
    resCode: {
        // TOKEN_EXPIRED: 1000090,    //核素旧系统用到
        TOKEN_EXPIRED: 1008001,
        TOKEN_NOT_EXIST: 1000002, // 您还没有登录或登录已超时,需要重新登录
        TOKEN_NOT_ENABLE: 1000003 // 检查登录状态失败,请稍后重试！
    },
    rootPath: process.env.NODE_ENV === 'production' ? window.location.origin : window.location.origin + '/superset'
}

export const rootPath = requestConfig.rootPath
/*请求相关数据配置-------------------------------*/
window.configs = window.configs || {}
window.configs.urls = window.configs.urls || {}
let urls = window.configs.urls || {}

if (process.env.NODE_ENV === 'development') {
    // 新接口测试 
    // window.configs.urls.system = '/auth'
    // window.configs.urls.photoDownUrl = '/_api/report/facecollect/show'
    // window.configs.urls.apricot = '/_api/report'
    window.configs.urls.system = 'http://192.168.1.201:19102/auth'
    window.configs.urls.photoDownUrl = 'http://192.168.1.201:19102/report/facecollect/show'
    window.configs.urls.apricot = 'http://192.168.1.201:19102/report'
    window.configs.urls.image = 'http://192.168.1.201:19102/image'
    window.configs.urls.broken = 'http://192.168.1.201:19102/broken'
    window.configs.urls.webReadUrl = 'http://192.168.1.201:19102/app/webdicom/#/'
    window.configs.urls.webReBuildUrl = 'http://192.168.1.201:19102/app/webdicom/#/'
    window.configs.urls.OCR = 'http://192.168.1.201:8151'
    window.configs.urls.downfileUrl = 'http://192.168.1.201:19102/report/download/downfile'
}


/*各项目api基础路径-----------------------------*/
export const baseURL = {
    system: urls.system, // 基础登录 http://192.168.1.253:9001
    apricot: urls.apricot, // 新版报告系统
    reporting: urls.reporting, // 新版报告系统
    image: urls.image, // 图像
    broken: urls.broken, // 申请单
    apricotAssist: urls.apricotAssist, // 本地服务
}
/*-----------------------------各项目api基础路径*/


// 查看图片地址
export const photoDownUrl = urls.photoDownUrl // 192.168.1.253
export const photoUpUrl = urls.photoUpUrl


// // 加载 icon
// export function loadIconFont() {
//     const loadList = [
//         'superset',
//         'device'
//     ]
//     loadList.forEach(item => {
//         import(`../resource/iconfonts/${item}/iconfont.css`);
//         import(`../resource/iconfonts/${item}/iconfont.js`);
//     });
// }
