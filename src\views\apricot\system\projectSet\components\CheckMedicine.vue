<template>
    <!-- 检查用药 -->
    <div class="scope-CheckProjectTree c-container">
        <DragAdjust :dragAdjustData="DA0" hasDragBorder>
            <template v-slot:c1>
                <div class="c-left"
                    v-contextmenu:contextmenuDiv
                    @node-contextmenu="onContextmenu"
                    v-loading="treeLoading">
                    <!--  @node-expand="nodeExpand"  -->
                    <el-scrollbar ref="scrollPane01">
                        <el-tree :data="treeData"
                            node-key="sId"
                            highlight-current
                            :expand-on-click-node="false"
                            ref="tree"
                            @node-click="onNodeClick"
                            :props="defaultProps"
                            default-expand-all
                            @node-collapse="nodeCollapse"
                            :default-expanded-keys="treeExpandIndex">
                            <template v-slot="{ data }">
                                <span :title="data.sName"
                                    class="tree-name">{{data.sName}}</span>
                                <span v-if="data.sItemId" 
                                    :title="oItemsCount[data.sItemId]? `(${oItemsCount[data.sItemId]})`: '(0)'">
                                    {{ oItemsCount[data.sItemId]? `(${oItemsCount[data.sItemId]})`: '(0)' }}
                                </span>
                            </template>
                        </el-tree>
                    </el-scrollbar>
                    <v-contextmenu ref="contextmenuDiv">
                        <v-contextmenu-item @click="refreshData">刷新</v-contextmenu-item>
                    </v-contextmenu>
                </div>
            </template>
            
            <template v-slot:c2>
                <div class="c-right">
                    <div class="c-flex-context">
                        <div class="c-search">
                            <el-button-icon-fa type="primary"
                                icon="el-icon-plus"
                                @click="handleAdd">添加</el-button-icon-fa>
                            <el-button-icon-fa plain
                                type="primary"
                                icon="el-icon-refresh"
                                @click="mxGetTableList">刷新</el-button-icon-fa>

                            <!-- <div class="c-search-right float-right flex">
                                <div>
                                    <el-select style="width:100%"
                                        clearable=""
                                        v-model="condition.sDeviceTypeId"
                                        placeholder="设备类型"
                                        @change="onChangeDeviceType">
                                        <el-option v-for="(item, index) in optionsLoc.sDeviceTypeOptions"
                                            :key="index"
                                            :value="item.sId"
                                            :label="item.sDeviceTypeName"></el-option>
                                    </el-select>
                                </div>
                                <div>
                                    <el-select style="width:100%"
                                        clearable=""
                                        v-model="condition.sItemId"
                                        placeholder="项目名称"
                                        @change="mxGetTableList;">
                                        <el-option v-for="(item, index) in (condition.sDeviceTypeId ? optionsLoc.sItemOptions.filter(item => item.sDeviceTypeId=== condition.sDeviceTypeId): optionsLoc.sItemOptions)"
                                            :key="index"
                                            :value="item.sId"
                                            :label="item.sItemName"></el-option>
                                    </el-select>
                                </div>
                                <div style="width: auto;">
                                    <el-button-icon-fa icon="el-icon-search"
                                        type="primary"
                                        @click="mxGetTableList"></el-button-icon-fa>
                                </div>
                            </div> -->
                        </div>

                        <div class="c-flex-auto">
                            <div class="c-content"
                                v-loading="loading">
                                <!-- :row-class-name="tableRowClassName" -->
                                <el-table :data="tableData"
                                    v-drag:[config]="tableData"
                                    ref="mainTable"
                                    size="small"
                                    id="medicineTable"
                                    :default-sort="{ prop: 'sDeviceTypeName', order: 'ascending' }"
                                    @row-click="onClickRow"
                                    border
                                    stripe
                                    height="100%"
                                    style="width: 100%">
                                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                                        show-overflow-tooltip
                                        :key="item.index"
                                        :prop="item.sProp"
                                        :label="item.sLabel"
                                        :fixed="item.sFixed"
                                        :align="item.sAlign"
                                        :width="item.sWidth"
                                        :min-width="item.sMinWidth"
                                        :sortable="!!item.iSort">
                                        <template v-slot="scope">
                                            <template v-if="item.sProp === 'action'">
                                                <el-button size="small"
                                                    link
                                                    type="primary"
                                                    @click="handleEdit(scope.row)">编辑
                                                    <template #icon>
                                                        <Icon name="el-icon-edit"
                                                            color="">
                                                        </Icon>
                                                    </template>
                                                </el-button>
                                                <el-divider direction="vertical"></el-divider>
                                                <el-button size="small"
                                                    link
                                                    class
                                                    @click="onClickDel(scope.row)">
                                                    删除
                                                    <template #icon>
                                                        <Icon name="el-icon-delete"
                                                            color="">
                                                        </Icon>
                                                    </template>
                                                </el-button>
                                                <el-divider direction="vertical"></el-divider>
                                                <el-button size="small"
                                                    link
                                                    class="i-sort">排序
                                                    <template #icon>
                                                        <Icon name="el-icon-rank"
                                                            color="">
                                                        </Icon>
                                                    </template>
                                                </el-button>
                                            </template>
                                            <template v-if="item.sProp === 'iIsInvariable'">
                                                {{scope.row[item.sProp] ? '是' : '否'}}
                                            </template>
                                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                                            </template>
                                            <template v-else-if="item.sProp === 'iIsEnable'">
                                                <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                                    v-model="scope.row.iIsEnable"
                                                    :active-value="1"
                                                    :inactive-value="0"></el-switch>
                                            </template>
                                            <template v-else>
                                                {{scope.row[`${item.sProp}`]}}
                                            </template>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </DragAdjust>

        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            top="10vh"
            :close-on-click-modal="false"
            @close="closeDialog">
            <div class="flex">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="130px">
                    <el-col :span="24">
                        <el-form-item prop="sDeviceTypeId"
                            label="设备类型：">
                            <el-select style="width:100%"
                                clearable=""
                                v-model="editLayer.form.sDeviceTypeId"
                                placeholder="设备类型">
                                <el-option v-for="(item, index) in optionsLoc.sDeviceTypeOptions"
                                    :key="index"
                                    :value="item.sId"
                                    :label="item.sDeviceTypeName"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <template v-if="editLayer.form.sId">
                        <el-col :span="24">
                            <el-form-item prop="sItemId"
                                label="检查项目：">
                                <el-select v-model="editLayer.form.sItemId"
                                    ref="sItemName"
                                    placeholder="检查项目"
                                    style="width:100%">
                                    <el-option v-for="item in editLayer?.form?.sDeviceTypeId ? optionsLoc.sItemOptions.filter(item =>item.sDeviceTypeId  === editLayer.form.sDeviceTypeId) : optionsLoc.sItemOptions"
                                        :key="item.sId"
                                        :label="item.sItemName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sItemName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px; padding-right:5px;">{{ item.sDeviceTypeName }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sNuclideId"
                                label="核  素：">
                                <el-select v-model="editLayer.form.sNuclideId"
                                    ref="sNuclideName"
                                    placeholder="核素"
                                    clearable
                                    style="width:100%">
                                    <el-option v-for="item in optionsLoc.sNuclideOptions"
                                        :key="item.sId"
                                        :label="item.sNuclideName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sNuclideName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px"
                                            v-html="item.sNuclideSupName"></span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sTracerId"
                                label="示踪剂：">
                                <el-select v-model="editLayer.form.sTracerId"
                                    ref="sTracerName"
                                    placeholder="示踪剂"
                                    clearable
                                    style="width:100%">
                                    <el-option v-for="item in optionsLoc.sTracerOptions"
                                        :key="item.sId"
                                        :label="item.sTracerName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sTracerName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px"
                                            v-html="item.sTracerSupName"></span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </template>
                    <template v-else>
                        <el-col :span="24">
                            <el-form-item prop="sItemIds"
                                label="检查项目：">
                                <el-select v-model="editLayer.form.sItemIds"
                                    ref="sItemIds"
                                    placeholder="检查项目"
                                    multiple
                                    collapse-tags
                                    collapse-tags-tooltip
                                    clearable
                                    class="i-select-choose"
                                    @focus="onFocusSelect('sItemIds')"
                                    @blur="onBlurSelect('sItemIds')"
                                    @change="() => onSelectChange('sItemIds' , optionsLoc.sItemOptions)">
                                    <template #prefix
                                        v-if="oShowFinishIcon['sItemIds']">
                                        <span @click.stop="onFinishIconClick('sItemIds')"
                                            class="i-finish">
                                            <i class="el-icon-finished"></i>
                                        </span>
                                    </template>
                                    <el-checkbox v-model="checkedKey['sItemIds']"
                                        class="my-checkbox"
                                        @change="(val) => selectAll('sItemIds' , optionsLoc.sItemOptions)">全选</el-checkbox>

                                    <el-option v-for="item in editLayer?.form?.sDeviceTypeId ? optionsLoc.sItemOptions.filter(item =>item.sDeviceTypeId  === editLayer.form.sDeviceTypeId) : optionsLoc.sItemOptions"
                                        :key="item.sId"
                                        :label="item.sItemName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sItemName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px; padding-right:5px;">{{ item.sDeviceTypeName }}</span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sNuclideIds"
                                label="核  素：">
                                <el-select v-model="editLayer.form.sNuclideIds"
                                    ref="sNuclideIds"
                                    placeholder="核素"
                                    multiple
                                    collapse-tags
                                    collapse-tags-tooltip
                                    clearable
                                    class="i-select-choose"
                                    @focus="onFocusSelect('sNuclideIds')"
                                    @blur="onBlurSelect('sNuclideIds')"
                                    @change="() => onSelectChange('sNuclideIds' , optionsLoc.sNuclideOptions)">
                                    <template #prefix
                                        v-if="oShowFinishIcon['sNuclideIds']">
                                        <span @click.stop="onFinishIconClick('sNuclideIds')"
                                            class="i-finish">
                                            <i class="el-icon-finished"></i>
                                        </span>
                                    </template>
                                    <el-checkbox v-model="checkedKey['sNuclideIds']"
                                        class="my-checkbox"
                                        @change="(val) => selectAll('sNuclideIds' , optionsLoc.sNuclideOptions)">全选</el-checkbox>
                                    <el-option v-for="item in optionsLoc.sNuclideOptions"
                                        :key="item.sId"
                                        :label="item.sNuclideName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sNuclideName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px; padding-right:5px;"
                                            v-html="item.sNuclideSupName"></span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sTracerIds"
                                label="示踪剂：">
                                <el-select v-model="editLayer.form.sTracerIds"
                                    ref="sTracerIds"
                                    placeholder="示踪剂"
                                    multiple
                                    collapse-tags
                                    collapse-tags-tooltip
                                    clearable
                                    class="i-select-choose"
                                    @focus="onFocusSelect('sTracerIds')"
                                    @blur="onBlurSelect('sTracerIds')"
                                    @change="() => onSelectChange('sTracerIds' , optionsLoc.sTracerOptions)">
                                    <template #prefix
                                        v-if="oShowFinishIcon['sTracerIds']">
                                        <span @click.stop="onFinishIconClick('sTracerIds')"
                                            class="i-finish">
                                            <i class="el-icon-finished"></i>
                                        </span>
                                    </template>
                                    <el-checkbox v-model="checkedKey['sTracerIds']"
                                        class="my-checkbox"
                                        @change="(val) => selectAll('sTracerIds' , optionsLoc.sTracerOptions)">全选</el-checkbox>
                                    <el-option v-for="item in optionsLoc.sTracerOptions"
                                        :key="item.sId"
                                        :label="item.sTracerName"
                                        :value="item.sId">
                                        <span style="float: left">{{ item.sTracerName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px; padding-right:5px;"
                                            v-html="item.sTracerSupName"></span>
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </template>
                    <el-col :span="24">
                        <el-form-item prop="sItemPositionId"
                            label="检查部位：">
                            <el-select v-model="editLayer.form.sItemPositionId"
                                v-select-name="{ formData: editLayer.form, fields: {sPropName: 'sItemPositionName'} }"
                                placeholder="检查部位"
                                clearable
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.sItemPositionOptions"
                                    :key="item.sId"
                                    :label="item.sItemPositionName"
                                    :value="item.sId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sTestModeId"
                            label="检查方式：">
                            <el-select v-model="editLayer.form.sTestModeId"
                                v-select-name="{ formData: editLayer.form, fields: {sPropName: 'sTestModeName'} }"
                                placeholder="检查方式"
                                clearable
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.sTestModeOptions"
                                    :key="item.sId"
                                    :label="item.sTestModeName"
                                    :value="item.sId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="iIsInvariable"
                            label="定 量：">
                            <el-select v-model="editLayer.form.iIsInvariable"
                                placeholder="定量"
                                style="width:100%"
                                @change="onChangeInvariable">
                                <el-option v-for="(item, index) in optionsLoc.iIsInvariableOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24"
                        v-if="!!editLayer.form.iIsInvariable">
                        <el-form-item prop="fDosage"
                            label="定量剂量：">
                            <!-- <el-input v-model="editLayer.form.fDosage"></el-input> -->
                            <el-input-number v-model="editLayer.form.fDosage"
                                controls-position="right"
                                :min="0"
                                :max="99999999"
                                style="width:100%"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24"
                        v-else>
                        <el-form-item prop="fCoefficient"
                            label="处方系数：">
                            <!-- <el-input v-model="editLayer.form.fCoefficient"></el-input> -->
                            <el-input-number v-model="editLayer.form.fCoefficient"
                                controls-position="right"
                                :min="0"
                                :max="99999999"
                                style="width:100%"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sPrice"
                            label="检查费用（元）：">
                            <!-- <el-input v-model="editLayer.form.sPrice"></el-input> -->
                            <el-input-number v-model="editLayer.form.sPrice"
                                controls-position="right"
                                :min="0"
                                :max="99999999"
                                style="width:100%"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sMatters"
                            label="费用说明：">
                            <el-input v-model="editLayer.form.sMatters"
                                type="textarea"
                                placeholder="费用说明"
                                :rows="2"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sMemo"
                            label="备  注：">
                            <el-input v-model="editLayer.form.sMemo"
                                placeholder="备注"
                                type="textarea"
                                :rows="2"></el-input>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button-icon-fa :loading="editLayer.loading"
                        icon="el-icon-check"
                        type="primary"
                        @click="handleSave">保存</el-button-icon-fa>
                    <el-button-icon-fa @click="closeDialog"
                        icon="el-icon-close">取消</el-button-icon-fa>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script>
/** 检查用药 */
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
import { deepClone } from '@/utils/function'
export default {
    name: 'CheckMedicine',
    mixins: [mixinTable, mixinTableDrag],
    props: {},
    data () {
        return {

            dialogTitle: '新增',
            dialogVisible: false,
            loading: false,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                    iIsInvariable: 0
                }
            },
            condition: {
                iType: '4',
            },
            configTable: [
                {
                    sProp: 'sDeviceTypeName',
                    sLabel: '设备类型',
                    sAlign: 'left',
                    sMinWidth: '100px',
                    // iSort: 1
                },
                {
                    sProp: 'sItemName',
                    sLabel: '检查项目',
                    sAlign: 'left',
                    sMinWidth: '200px',
                },
                {
                    sProp: 'sNuclideName',
                    sLabel: '核素',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sTracerName',
                    sLabel: '示踪剂名称 ',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sItemPositionName',
                    sLabel: '检查部位',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sTestModeName',
                    sLabel: '检查方式',
                    sAlign: 'left',
                    sWidth: '120px',
                },
                {
                    sProp: 'iIsInvariable',
                    sLabel: '定量',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'fDosage',
                    sLabel: '定量剂量 ',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'fCoefficient',
                    sLabel: '处方系数',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sPrice',
                    sLabel: '检查费用（元）',
                    sAlign: 'center',
                    sWidth: '120px',
                },
                {
                    sProp: 'sMatters',
                    sLabel: '收费说明',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sMemo',
                    sLabel: '备注',
                    sAlign: 'center',
                    sWidth: '120px',
                },
                {
                    sProp: 'action',
                    sLabel: '操作',
                    sAlign: 'center',
                    sWidth: '220px',
                    sFixed: 'right'
                }
            ],
            rules: {
                sItemIds: [{ required: true, message: '不能为空' }],
                // sNuclideIds: [{ required: true, message: '不能为空' }],
                // sTracerIds: [{ required: true, message: '不能为空' }],
                sItemId: [{ required: true, message: '不能为空' }],
                // sNuclideId: [{ required: true, message: '不能为空' }],
                // sTracerId: [{ required: true, message: '不能为空' }],
                // sItemPositionId: [{ required: true, message: '不能为空' }],
                // sTestModeId: [{ required: true, message: '不能为空' }],
            },
            optionsLoc: {
                iIsInvariableOptions: [{
                    sName: '是',
                    sValue: 1
                }, {
                    sName: '否',
                    sValue: 0
                }],
                sNuclideOptions: [],
                sTracerOptions: [],
                sItemPositionOptions: [],
                sTestModeOptions: [],
                sItemOptions: [],
                sDeviceTypeOptions: []
            },
            sortApi: Api.sortItemSet,
            oShowFinishIcon: {
                sItemIds: false,
                sNuclideIds: false,
                sTracerIds: false
            },
            checkedKey: {},
            treeLoading: false,
            defaultProps: {
                children: 'childs',
                label: 'sName'
            },
            treeData: [],
            selectNodeData: {},
            treeExpandIndex: [],
            oItemsCount: {},
            DA0: {
                type: 't-x',
                localStorageKey: '202312190940',
                panelConfig: [{
                        size: 300,
                        minSize: 180,
                        maxSize: 500,
                        name: "c1",
                        isFlexible: false
                    },
                    {
                        size: 0,
                        minSize: 45,
                        name: "c2",
                        isFlexible: true
                    }
                ]
            },
            isMixinDynamicGetTableHead: true
        }
    },
    methods: {
        // tableRowClassName ({ row, rowIndex }) {
        //     if (this.dragRow.sItemId && row.sItemId !== this.dragRow.sItemId) {
        //         return "row-filtered";
        //     }
        //     return "";
        // },
        onChangeDeviceType () {
            if (this.condition.sDeviceTypeId) {
                const options = this.optionsLoc.sItemOptions.filter(item => item.sDeviceTypeId === this.condition.sDeviceTypeId);
                const findItem = options.find(item => item.sId === this.condition.sItemId);
                if (!findItem) {
                    this.condition.sItemId = '';
                }
            }

            this.mxGetTableList();
        },
        // 全选
        selectAll (sProp, options) {
            this.editLayer.form[sProp] = [];
            const sDeviceTypeId = this.editLayer?.form?.sDeviceTypeId;
            if (sProp === 'sItemIds' && sDeviceTypeId) {
                options = options.filter(item => item.sDeviceTypeId === sDeviceTypeId);
            }
            if (this.checkedKey[sProp]) {
                options.map((item) => {
                    this.editLayer.form[sProp].push(item.sId);
                })
            }
            let timer = setTimeout(() => {
                this.$refs[sProp].focus();
                clearTimeout(timer)
            }, 100)
        },
        onFocusSelect (sProp) {
            this.oShowFinishIcon[sProp] = true;
        },
        onBlurSelect (sProp) {
            this.oShowFinishIcon[sProp] = false;
        },
        onSelectChange (sProp, options) {
            if (this.editLayer.form[sProp].length === options.length) {
                this.checkedKey[sProp] = true
            } else {
                this.checkedKey[sProp] = false
            }
        },
        onFinishIconClick (sProp) {
            this.$nextTick(() => {
                this.oShowFinishIcon[sProp] = false;
                this.$refs[sProp].blur();
            })
        },
        // 新增
        handleAdd () {
            this.mxOpenDialog(1, 'no-title');
            this.dialogTitle = '新增';
            Object.keys(this.checkedKey).map(key => {
                this.checkedKey[key] = false;
            })
            if (this.selectNodeData.sItemId) {
                this.editLayer.form.sDeviceTypeId = this.selectNodeData.sDeviceTypeId;
                this.editLayer.form.sItemIds = [this.selectNodeData.sItemId];
            }
            this.dialogVisible = true;
            setTimeout(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            }, 100)
        },
        closeDialog () {
            this.dialogVisible = false
        },
        onChangeInvariable (val) {
            if (val) {
                // 清空处方系数
                this.editLayer.form.fCoefficient = '';
            } else {
                // 清空定量剂量
                this.editLayer.form.fDosage = '';
            }
        },
        handleEdit (row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = { ...row };
            if (row.fCoefficient === null) {
                this.editLayer.form.fCoefficient = undefined;
            }
            if (row.fDosage === null) {
                this.editLayer.form.fDosage = undefined;
            }
            this.$nextTick(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            })

        },
        handleSave () {
            this.editLayer.loading = true
            let params = deepClone(this.editLayer.form);
            this.$refs['refEditLayer'].validate((valid) => {
                if (valid) {
                    this.saveData(params)
                    return
                }
                this.editLayer.loading = false
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            // if (params.iIsInvariable == 1 && (params.fDosage == '' || params.fDosage == undefined)) {
            //     this.$message.warning('请输入定量剂量！')
            //     this.editLayer.loading = false;
            //     return
            // }
            if (params.iIsInvariable == 0 && (params.fCoefficient == '' || params.fCoefficient == undefined)) {
                this.$message.warning('请输入处方系数！')
                this.editLayer.loading = false;
                return
            }
            if (!params.sId) {
                params.sItemIds = params.sItemIds.join(',');
                params.sNuclideIds = params.sNuclideIds.join(',');
                params.sTracerIds = params.sTracerIds.join(',');
                Api.itemSetBatchAdd(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        this.dialogVisible = false
                        this.mxSetSelected()
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return
            }
            params.sNuclideName = this.$refs.sNuclideName.selectedLabel;
            let selectedNuclide = this.optionsLoc.sNuclideOptions.find(item => item.sId === params.sNuclideId)
            if (selectedNuclide) params.sNuclideSupName = selectedNuclide.sNuclideSupName;

            params.sTracerName = this.$refs.sTracerName.selectedLabel;
            let selectedTracer = this.optionsLoc.sTracerOptions.find(item => item.sId === params.sTracerId)
            if (selectedTracer) params.sTracerSupName = selectedTracer.sTracerSupName;

            Api.editItemSet(params).then((res) => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.$message.success(res.msg);
                    this.mxOpenDialog(1, 'no-title');
                    this.mxGetTableList();
                    this.dialogVisible = false
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除这条记录吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                Api.delItemSet({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        // 如果编辑的是删除的，清空编辑内容
                        this.mxGetTableList();
                        return;
                    }
                    this.$message.error(res.msg);
                })
            })
        },
        // 获取表格数据
        getData () {
            this.loading = true;
            var jsonData = {};
            this.selectNodeData.sItemId && (jsonData.sItemId = this.selectNodeData.sItemId)
            Api.getItemSetData(jsonData).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data || [];
                    this.tableData.map(item => {
                        if (item.sPrice?.length) {
                            item.sPrice = Number(item.sPrice);
                        }
                    });
                    this.mxSetSelected();
                    return
                }

                this.tableData = [];
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.tableData = [];
                this.loading = false;
            })
        },
        getItemCount() {
            Api.getItemSetData({}).then((res) => {
                if (res.success) {
                    const tableData = res.data || [];
                    this.oItemsCount = {};
                    tableData.map(item => {
                        if (item.sPrice?.length) {
                            item.sPrice = Number(item.sPrice);
                        }
                        if (this.oItemsCount.hasOwnProperty(item.sItemId)) {
                            this.oItemsCount[item.sItemId]++
                        } else {
                            this.oItemsCount[item.sItemId] = 1;
                        }
                    });
                    if(this.selectNodeData.sItemId) {
                        this.tableData = tableData.filter(item => item.sItemId === this.selectNodeData.sItemId)
                    }
                    return
                }
                this.oItemsCount = {};
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.oItemsCount = {};
            })
        },
        
        // 获取下拉框数据
        getOptionsData (fnName, optionName) {
            const jsonData = {
                iIsEnable: 1,
            }
            Api[fnName](jsonData).then((res) => {
                if (res.success) {
                    this.optionsLoc[optionName] = res?.data || [];
                    if (fnName == 'getItemData') {
                        this.optionsLoc[optionName] = this.optionsLoc[optionName].sort((a, b) => (a.sDeviceTypeName + '').localeCompare(b.sDeviceTypeName + ''))
                    }
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => {
            })
        },
        // 点击树
        onNodeClick (obj , isNotToGetData) {
            this.selectNodeData = obj;
            isNotToGetData !== true && this.getData();
        },
        nodeExpand (data) {
            this.treeExpandIndex.push(data.sId); // 在节点展开是添加到默认展开数组
        },
        nodeCollapse (data) {
            this.treeExpandIndex.splice(this.treeExpandIndex.indexOf(data.sId), 1); // 收起时删除数组里对应选项
        },
        // 右键事件
        onContextmenu (event, obj) {
            // this.rightData = obj
            // this.contextmenuShow(event)
        },
        async refreshData() {
            await this.getTreeData();
            this.getItemCount();
        },
        // 获取树数据
        async getTreeData () {
            this.treeLoading = true
            await Api.getItemTreeData({}).then(res => {
                this.treeLoading = false
                if (res.success) {
                    this.treeData = res.data ? res.data : [];
                    if (!this.$refs.tree) return;
                    // 存在树，默认选中第一个节点的子节点
                    if (!Object.keys(this.selectNodeData).length) {

                        // 有选中
                        // this.treeExpandIndex = [this.treeData[0].childs[0].sId];
                        this.selectNodeData = this.treeData[0].childs[0];

                        this.$nextTick(() => {
                            this.$refs.tree.setCurrentKey(this.selectNodeData.sId);
                            this.onNodeClick(this.selectNodeData, true);
                        });
                    }
                    //如果选中节点，刷新树时，保持选中
                    if (Object.keys(this.selectNodeData).length) {
                        this.$nextTick(() => {
                            this.$refs.tree.setCurrentKey(this.selectNodeData.sId);
                            this.onNodeClick(this.selectNodeData, true);
                        });
                    }
                    return;
                }
                this.treeData = [];
            }).catch(e => {
                this.treeLoading = false;
                console.log(e);
            })
        },
    },
    async created () {
        let temp = [{
            fnName: 'getNuclideData',
            optionName: 'sNuclideOptions',
        }, {
            fnName: 'getTracerData',
            optionName: 'sTracerOptions',
        }, {
            fnName: 'getItemPositionData',
            optionName: 'sItemPositionOptions',
        }, {
            fnName: 'getTestModeData',
            optionName: 'sTestModeOptions',
        }, {
            fnName: 'getItemData',
            optionName: 'sItemOptions',
        }, {
            fnName: 'getDeviceTypeData',
            optionName: 'sDeviceTypeOptions',
        }];
        for (var i = 0; i < temp.length; i++) {
            this.getOptionsData(temp[i].fnName, temp[i].optionName);
        }
        await this.getTreeData();
        this.getItemCount();
    },
};
</script>
<style lang="scss" scoped>
.scope-CheckProjectTree {
    height: 100%;
    .c-left { 
        width: 100%;
        height: 100%;
        // overflow: hidden;
    }
    .c-right {
        height: 100%;
        overflow: hidden;
        .c-flex-context {
            height: 100%;
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            :deep(.c-search) {
                //display: flex;
                padding-bottom: 10px;
                // justify-content: space-between;
            }
            .c-search-right {
                > div {
                    width: 240px;
                    margin: 0 5px;
                }
            }
            :deep(.c-flex-auto) {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow: hidden;
                .c-content {
                    flex: 1;
                    overflow: hidden;
                    // height: 0px;
                }
            }
        }
    }
}

:deep(.i-select-choose) {
    width: 100%;
    .el-input__wrapper {
        position: relative;
        .el-input__prefix {
            position: absolute;
            right: 0;
            z-index: 3;
        }
    }
    .i-finish {
        background-color: #fff;
        border: 1px solid var(--el-color-primary);
        border-radius: 50%;
        color: var(--el-color-primary);
        width: 24px;
        height: 24px;
        line-height: 24px;
    }
}
/* element滚动条组件 隐藏水平滚动条 */
// .sidebar-wrapper .el-scrollbar__wrap {
//     overflow-x: hidden;
// }
// .is-horizontal {
//     display: none;
// }
// .el-scrollbar {
//     height: 100%;
// }
// :deep(::-webkit-scrollbar-track),
// :deep(::-webkit-scrollbar) {
//     background-color: transparent;
// }

.my-checkbox {
    padding: 0 20px 0 13px;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
    flex-direction: row-reverse;

    &:hover {
        background: var(--el-color-primary-light-9);
    }
}
</style>
