<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-button">
                <el-row :gutter="10">
                    <el-col :span="24">
                        <div>
                            <el-button-icon-fa type="primary" plain
                            icon="el-icon-refresh" :loading="loading" @click="mxDoRefresh">刷新
                            </el-button-icon-fa>
                            <!-- <el-button-icon-fa                                 size="small"
                                _icon="fa fa-export-01"
                                @click=""
                                type="primary"
                                plain>导出</el-button-icon-fa> -->
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content" v-loading="loading">
                <el-table :data="tableData" id="itemTable" ref="mainTable" size="small"
                    stripe height="100%" style="width: 100%">
                    <!-- <el-table-column align="center"
                        type="selection"
                        width="55">
                    </el-table-column> -->
                    <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                        <el-table-column show-overflow-tooltip  :prop="item.sProp" :label="item.sLabel"
                            :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth" :min-width="item.sMinWidth"
                            :sortable="!!item.iSort" >
                            <template v-slot="{row}">
                                <template
                                    v-if="['isGraveDept','deptConsult'].includes(item.sProp)">
                                    {{row[`${item.sProp}`] == 1 ? '是' : '否'}}
                                </template>
                                <template
                                    v-else-if="item.sProp === 'clinicAttr'">
                                    {{clinicAttrFilter( row[`${item.sProp}`])}}
                                </template>
                                <template
                                    v-else-if="item.sProp === 'outpOrInp'">
                                    {{outpOrInpFilter( row[`${item.sProp}`])}}
                                </template>
                                <template
                                    v-else-if="item.sProp === 'internalOrSergery'">
                                    {{internalOrSergeryFilter ( row[`${item.sProp}`])}}
                                </template>
                                <template v-else-if="item.sProp === 'deptType'">
                                    {{ row[`${item.sProp}`] == 1 ? '生殖科室' : '普通科室' }}
                                </template>
                                <template v-else>
                                    <span>{{row[`${item.sProp}`]}}</span>
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>


    </div>
</template>
<script>



import { transformDate } from '$supersetUtils/function.js'
import { getDepartDictionaryData } from '$supersetApi/projects/apricot/system/dictionary.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'ExportTemplate',
    mixins: [mixinTable],
    props: {},
    
    data() {
        return {
            loading: false,
            clientEditDiolog: false,
            clientRegistedDiolog: false,
            client: {},
            clientForm: {},
            configTable: [
                {
                    sProp: 'serialNo',
                    sLabel: '序号',
                    sAlign: 'center',
                    sWidth: '60px'
                },
                {
                    sProp: 'deptCode',
                    sLabel: '科室代码',
                    sAlign: 'center',
                    sWidth: '100px'
                },
                {
                    sProp: 'displayOrder',
                    sLabel: '显示顺序',
                    sAlign: 'center',
                    sWidth: '100'
                },
                {
                    sProp: 'deptName',
                    sLabel: '科室名称',
                    sAlign: 'left',
                    sMinWidth: '120'
                },
                {
                    sProp: 'deptAlias',
                    sLabel: '简称',
                    sAlign: 'left',
                    sMinWidth: '70'
                },
                {
                    sProp: 'deptType',
                    sLabel: '科室类型',
                    sAlign: 'center',
                    sWidth: '80'
                },
                {
                    sProp: 'clinicAttr',
                    sLabel: '临床科室属性',
                    sAlign: 'center',
                    sWidth: '80'
                },
                {
                    sProp: 'outpOrInp',
                    sLabel: '门诊住院科室标志',
                    sAlign: 'center',
                    sWidth: '94px'
                },
                {
                    sProp: 'internalOrSergery',
                    sLabel: '内外科标志',
                    sAlign: 'center',
                    sMinWidth: '100px'
                },
                {
                    sProp: 'isGraveDept ',
                    sLabel: '重症监护室',
                    sAlign: 'center',
                    sMinWidth: '100px'
                },

                {
                    sProp: 'deptConsult',
                    sLabel: '会诊科室 ',
                    sAlign: 'center',
                    sWidth: '80px'
                },
                
                {
                    sProp: 'totalBedNum',
                    sLabel: '开放床位数',
                    sAlign: 'center',
                    sWidth: '106px'
                },
                 {
                    sProp: 'typeCode',
                    sLabel: '类型码',
                    sAlign: 'left',
                    sWidth: '106px'
                },
                {
                    sProp: 'inputCode',
                    sLabel: '输入码',
                    sAlign: 'left',
                    sWidth: '106px'
                },
                {
                    sProp: 'pym',
                    sLabel: '拼音码',
                    sAlign: 'left',
                    sWidth: '106px'
                },
                {
                    sProp: 'wbm',
                    sLabel: '五笔码',
                    sAlign: 'left',
                    sWidth: '106px'
                },
                {
                    sProp: 'location',
                    sLabel: '位置',
                    sAlign: 'left',
                    sWidth: '106px'
                },
                {
                    sProp: 'hospitalId',
                    sLabel: '医院编码',
                    sAlign: 'left',
                    sWidth: '106px'
                },
            ],
            d_params_visiable: false,
            d_params_save: false,
            paramsData: [],
            clientInfo: {},
            isDefaultClient: false,
        }
    }, 
    methods: {
      setTime2yyyyMMDDHHmm(val) {
          return transformDate(val, false, 'yyyy-MM-dd HH:mm')
      },
      clinicAttrFilter(val) {
          let clinicAttr ={
              0:'临床',
              1:'辅诊',
              2:'护理单元',
              3:'机关',
              9:'其他',
          }
         // clinicAttr 临床科室属性 0 临床 1 辅诊 2 护理单元3 机关9 其他；非空
          return clinicAttr[val]
      },
      outpOrInpFilter(val) {
          let outpOrInp ={
              0:'门诊',
              1:'住院',
              2:'门诊住院',
              9:'其他',
          }
          // outpOrInp 门诊住院科室标志 0 门诊、1 住院、2 门诊住院、9 其他；非空
          return outpOrInp[val]
      },
      internalOrSergeryFilter(val) {
          let internalOrSergery ={
              0:'内科',
              1:'外科',
              9:'其他',
          }
          // internalOrSergery 内外科标志 0 内科、1 外科、9 其他；非空
          return internalOrSergery[val]
      },
        
        /**
         * 获取表格数据
         */
        getData() {
            this.loading = true
            getDepartDictionaryData().then((res) => {
                if (res.success) {
                    this.tableData = res.data
                    this.loading = false;
                    // 赋选中状态
                    this.setSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    mounted() {
    }
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 10px 0px 0px;
    background-color: #fff;
    :deep(.c-form) {
        display: flex;
        flex-direction: column;

        .c-form-button {
            padding: 0 10px 10px 10px;
            border-bottom: var(--el-border);
        }

       
    }

    :deep(.c-flex-auto ){
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        padding-bottom: 10px;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;

            >button {
                margin-top: 13px;
            }
        }

        .c-content {
            flex: 1;
            height: 0px;
        }
    }
}

// :deep(.drag-set) {
//     &.i-show {
//         .board-column-header {
//             background: #4c9ddf;
//         }
//     }

//     &.i-hide {
//         .board-column-header {
//             background: #ccc;
//         }

//         .board-column-content {
//             flex-wrap: wrap;
//         }
//     }
// }

.i-center {
    display: inline-block;
    height: 40px;
}

:deep(.my-dialog) {
    &.is-fullscreen {
        width: 80%;
        height: 90%;
        margin-top: 2.5%;

        .el-dialog__body {
            height: calc(100% - 95px);
            padding: 20px;

            .c-inner-content {
                height: 100%;
                overflow: auto;
            }
        }
        // .m-table {
        //     height: calc(100% - 45px)
        // }

    }
    &:not(.t-2) {
        .el-dialog__body {
            padding: 40px 30px 20px 20px;
        }
    }

    .el-dialog__body {
        padding: 20px;
    }
}
</style>
