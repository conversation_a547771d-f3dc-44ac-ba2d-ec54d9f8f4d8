import mixinTable from '../apricot/mixinTable'
import mixinCodeTable from '../apricot/mixinCodeTable'
import mixinElementConfigs from './mixinElementConfigs';
import mixinUpdatePatient from './mixinUpdatePatient'
import mixinAvatar from './mixinAvatar'
import mixinTableInner from './mixinTableInner'
import mixinPrintPreview from './mixinPrintPreview'
import mixinCaseType from './mixinCaseType'
import mixinExportExcel from './mixinExportExcel'
import mixinTableDrag from './mixinTableDrag'

import store from '$supersetStore'

export {
    mixinTable,
    mixinCodeTable as mixinDictionaryGroup,
    mixinElementConfigs,
    mixinAvatar,
    mixinUpdatePatient,
    mixinTableInner,
    mixinPrintPreview,
    mixinCaseType,
    mixinExportExcel,
    mixinTableDrag
}


// 获取模块的状态名通过路由
export function getStoreNameByRoute(routeName = 'apricot_Case') {
    let result = 'case_module'

    switch (routeName) {
        case 'apricot_Case':
            result = 'case_module';
            break;
        case 'apricot_Consult':
            result = 'consult_module';
            break;
        case 'apricot_Inject':
            result = 'inject_module';
            break;
        case 'apricot_Machine':
            result = 'machine_module';
            break;
        case 'apricot_Report':
            result = 'report_module';
            break;
        case 'apricot_TeachCase':
            result = 'teachCase_module';
            break;
        case 'apricot_TeachComment':
            result = 'teachComment_module';
            break;
        case 'apricot_FollowupVisits':
            result = 'followupVisits';
            break;
        case 'apricot_RemoteConsult':
            result = 'remoteConsult_module';
            break;
        case 'apricot_CollcetMng':
            result = 'collectMng_module';
            break;
        default:
            break;
    }
    return result;
}


export function openCallsetWindow() {
    let url = window.configs.urls.callSet;
    //获得窗口的垂直位置 
    let availHeight = window.screen.availHeight;
    let iTop = 100;
    let iHeight = availHeight - iTop * 2;
    //获得窗口的水平位置 
    let availWidth = window.screen.availWidth;
    let iLeft = 150;
    let iWidth = availWidth - iLeft * 2;
    iWidth = iWidth < 1200 ? 1200 : iWidth;
    window.open(url, 'callset', `width=${iWidth},height=${iHeight},top=${iTop},left=${iLeft},scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes,location=no`)
    // window.open(url + `?sCookie=${getToken()}`, 'callset', `width=1280,height=700,top=${iTop},left=${iLeft},scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes,location=no`)
}


export function openCallWindow(obj = {}) {
    let temp = ''
    if (Object.prototype.toString.call(obj).slice(8, -1) === 'Object') {
        for (let item in obj) {
            if (!['', undefined, null].includes(obj[item])) {
                temp += item + '=' + obj[item] + '&'
            }
        }
    }
    let url = window.configs.urls.call + '?' + (temp.slice(0, -1));
    //获得窗口的垂直位置 
    var iTop = (window.screen.availHeight - 30 - 600) / 2;
    //获得窗口的水平位置 
    var iLeft = (window.screen.availWidth - 10 - 1000) / 2;
    window.open(url, 'call', `width=1000,height=600,top=${iTop},left=${iLeft},scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes,location=no`)
}

let newWin = [null, null];
let imageWin = null;
// iIsRebuild:是否阅图，1:重建，0：阅图； userId:用户sId；info:用户信息；isImagePatient：是否图像患者，true=是；
export function openWebReadImgOrRebuild(iIsRebuild, userId, info, isImagePatient = false) {
    let baseUrl = iIsRebuild ? window.configs.urls.webReadUrl : window.configs.urls.webReBuildUrl;
    // 工作站id
    const stationId = store.state.user.workStation.stationId;

    if (!isImagePatient) {
        baseUrl = `${baseUrl}?module=${iIsRebuild}&userId=${userId}&sPatientId=${info.sPatientId}&sWorkStationId=${stationId}&deviceTypeId=${info.deviceTypeId}${info.past ? '&past=1' : ''}`
    } else {
        baseUrl = `${baseUrl}?module=${iIsRebuild}&userId=${userId}&accessNo=${info.accessNo}&studyDate=${info.studyDate}&patientId=${info.patientId}&sWorkStationId=${stationId}${info.past ? '&past=1' : ''}`
    }
    // 根据存储的配置，判断web重建是新开tab页还是覆盖原有tab页
    let oAutoSaveTime = window.localStorage.getItem('oAutoSaveTime')
    let isOpenImgTab = 0
    if(oAutoSaveTime) {
        isOpenImgTab = JSON.parse(oAutoSaveTime).isOpenImgTab?JSON.parse(oAutoSaveTime).isOpenImgTab : 0
    }
    // 重建对比用当前打开报告的患者id
    let title = isOpenImgTab == 1 && iIsRebuild?`viewer${info.sId || info.sPatientId}`:'viewer'
    const open = window.open(baseUrl, title + iIsRebuild);
    if (newWin[iIsRebuild] !== undefined) newWin[iIsRebuild] = open
}

export function closeWindow(){
    newWin.forEach(win => {
        if (win) {
            win.close();
            win = null
        }
    })
    if (imageWin) {
        imageWin.close();
        imageWin = null
    }
}

export function openCaseImageWin(params) {
    let { href } = this.$router.resolve({ path: '/NewImagePage', query: params })
    imageWin = window.open(href, 'caseImages')
}
