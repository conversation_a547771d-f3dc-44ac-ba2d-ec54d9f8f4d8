<template>
    <span></span>

    <el-dialog 
    :close-on-click-modal="false"
    append-to-body 
    :title="popTitle" 
    :modelValue="dialogVisible" 
    @close="handleCloseDialog"
    fullscreen
    class="my-dialog my-full-dialog my-padding-dialog">
        <div class="c-plan">
            <ul>
                <li>
                    <span>语言 </span>
                    <el-select v-model="form.sLang" size="small" style="width: 100px;">
                        <el-option v-for="item in optionsLang"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </li>
                <li>
                    <span>缩放倍数 </span>
                    <el-select v-model="form.sScale" size="small" style="width: 100px;">
                        <el-option v-for="item in optionsScale"
                            :key="item.value"
                            :label="item.name"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                </li>
                <li>
                    <el-checkbox v-model="form.iRemoveLines" :true-label="1" :false-label="0">移除线条</el-checkbox>
                </li>
                <li>
                    <el-checkbox v-model="form.iNoiseFilter" :true-label="1" :false-label="0">噪声滤波器</el-checkbox>
                </li>
                <li>
                    <el-checkbox v-model="form.iSkewAngle" :true-label="1" :false-label="0">图像倾斜修复</el-checkbox>
                </li>
                <li>
                    <el-checkbox v-model="form.iFmt" :true-label="1" :false-label="0">内容格式化</el-checkbox>
                </li>
            </ul>
            <div class="item-01">
                <div>
                    <el-button size="small" type="primary" @click="apiTranslate" :loading="loading">{{loading ? '正在提取' : '内容提取'}}</el-button>
                    <el-button size="small" type="primary" @click="onClickCopy">一键复制</el-button>
                </div>
            </div>
            <div class="c-body">
                <div class="c-content">
                    <slot></slot>
                    <!-- <img 
                    v-show="showImgUrl" 
                    :src="showImgUrl" 
                    @mousedown="onMove" 
                    @mousewheel="onWheel"
                    ref="img" 
                    class="s-img"/> -->
                    <transition name = "fade">
                        <div v-show="tip.show" class="f-tip">{{tip.msg}}</div>
                    </transition>  
                </div>
                <el-input
                type="textarea" autosize
                placeholder="暂无提取内容"
                v-model="textarea">
                </el-input>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import { getTranslate, getOcrContent } from '$supersetApi/projects/apricot/common/ocr.js'
export default {
    props: {
        popTitle: {
            type: String,
            default: 'ORC图文识别'
        },
        dialogVisible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['closeDialog'],

    data() {
        return {
            form: {
                sLang: 'zh',
                sScale: '0',
                iRemoveLines: 0,
                iNoiseFilter: 0,
                iSkewAngle: 0,
                iFmt: 0,
            },
            optionsScale: [
                { value: '0', name: '自动' },
                { value: '0.25', name: '0.25' },
                { value: '0.5', name: '0.5' },
                { value: '1.0', name: '1.0' },
                { value: '1.5', name: '1.5' },
                { value: '2.0', name: '2.0' },
                { value: '2.5', name: '2.5' },
                { value: '4.0', name: '4.0' },
            ],
            optionsLang: [
                { value: 'zh', name: '简体中文' },
                { value: 'en', name: '英语' }, 
            ],
            showImgUrl: '',
            textarea: '',
            loading: false,
            source: {},
            scaleSize: 1,    // 缩放比
            tip: {			 // 提示
				show: false,
				timeout: null,
				msg: '提示'
			}
        }
    },
    watch: {
        dialogVisible: {
            handler(later){
                if(later && this.data){
                    // this.center()
                    this.getOcrContent();
                }
            },
            immediate: true
        },
        
    },
    methods: {
        getOcrContent() {
            getOcrContent({sFilePath: `${this.data.sDiskSymbol}${this.data.sFilePath}`}).then(res => {
                if(res.success) {
                    this.form.sContent = res.data || '';
                    this.apiTranslate();
                }
            }).catch(err => {
                console.log(err)
            })
        },
        onClickCopy(){
            let oInput = document.createElement('textarea')
            oInput.value = this.textarea
            document.body.appendChild(oInput)
            oInput.select()
            document.execCommand("Copy")
            this.$message({
                message: '复制成功',
                type: 'success'
            })
            oInput.remove()
        },
        apiTranslate(){
            this.loading = true
            getTranslate(this.form).then(e => {
                this.loading = false
                if (e.success){
                    this.$message({
                        message: e.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.textarea = e.data
                    return;
                }
                this.$message({
                    message: e.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.loading = false
            })
        },
        // 关闭弹窗
        handleCloseDialog() {
            Object.keys(this.source).length && this.source.cancel("cancelRequest");
            this.textarea = ''
            this.$emit('closeDialog');
        },
        center(){
			this.$nextTick(() => {
                let content = this.$refs.content1;
                let img = this.$refs.img1;
                img.addEventListener('load', () => {
                    let left = (content.offsetWidth - img.naturalWidth) / 2;
                    let top = (content.offsetHeight - img.naturalHeight) / 2;
                    img.style.top = top + "px";
                    img.style.left = left + "px";
                    img.style.transform = "rotateZ(" + 0 + "deg)";
                    img.style.width = img.naturalWidth + "px";
                    img.style.height = img.naturalHeight + "px";
                    this.scaleSize = 1;
                })
            })
		},
        // 提示
		showTip(){
			this.tip.show = true
			if (this.tip.timeout !== null) {
				clearTimeout(this.tip.timeout)
			}
			this.tip.timeout = setTimeout(() => {
				this.tip.show = false
			}, 1000);
		}
    }
}
</script>
<style lang="scss" scoped>
    .c-plan{
        display: flex;
        flex-direction: column;
        height: 100%;
        > ul{
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin: 0px;
            padding: 10px 0px 10px 10px;
            li {
                list-style-type: none;
                padding-right: 18px;
            }
        }
        .item-01{
            display: flex;
            align-items: center;
            padding: 10px 40px;
            > div{
                flex: 1;
                display: flex;
                align-items: center;
                &:first-child{
                    .el-image{
                        width: 70px;
                        height: 70px;
                        margin-left: 20px;
                    }
                }
                &:last-child{
                    justify-content: flex-end;
                }
                > div{
                    display: inline-block;
                }
            }
        }
        .c-body{
            display: flex;
            margin: 10px;
            padding-bottom: 10px;
            flex: 1;
            > div{
                flex: 1;
            }
            .c-content{
                flex: 1;
                overflow: hidden;
                // overflow: auto;
                position: relative;
                border: 1px solid #eee;
                margin-right: 10px;
                border-radius: 4px;
                .f-tip{
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    margin: auto;
                    height: 40px;
                    line-height: 40px;
                    width: 80px;
                    background: black;
                    color: white;
                    opacity: 0.6;
                    border-radius: 10px;
                    box-shadow: 0 1px 10px 2px rgba(255, 255, 255, 0.3);
                    font-size: 14px;
                    text-align: center;
                }
            }
            .el-textarea{
                :deep( textarea) {
                    height: 100% !important;
                }
            }
            .s-img{
                cursor:pointer;
                position: relative;
                position: absolute;
            }
        }
    }
</style>
