<template>
    <DragAdjust :dragAdjustData="DA0">
        <template v-slot:c1>
            <DragAdjust :dragAdjustData="MngIndexPenalConfig.isShowApplyPanel ? DA2 : DA3">
                <template v-slot:c1>
                    <LayoutTable v-bind="$attrs">
                        <template v-slot:header>
                            <SearchList v-model:modelValue="condition" :list="searchStyle" :optionData="optionsLoc"
                                :configBtn="MngIndexPenalConfig.isShowConfigBtn" :iModuleId="iModuleId" storageKey="MachineMngIndexSearch"
                                @changeSearch="mxDoSearch">
                                <template v-slot:dAppointmentTimeSt>
                                    <el-date-picker v-model="condition.dAppointmentTimeSt" type="date"
                                        :disabled-date="pickerOptionsAppointDayStart" @change="changeAppointTime" style="height:100%;">
                                    </el-date-picker>
                                </template>
                                <template v-slot:dAppointmentTimeEd>
                                    <el-date-picker v-model="condition.dAppointmentTimeEd"
                                        :disabled-date="pickerOptionsAppointDayEnd" type="date" @change="changeAppointTime" style="height:100%;">
                                    </el-date-picker>
                                </template>
                                <template v-slot:sDistrictId>
                                    <el-select v-model="condition.sDistrictId" placeholder=" " clearable @change="useChangeHospital">
                                        <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                                            :key="index"
                                            :label="item.sDistrictPrefix"
                                            :value="item.sId">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:sMachineryRoomId>
                                    <el-select v-model="condition.sMachineryRoomId" placeholder=" " clearable @change="useChangeMachineRoom">
                                        <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                                            :key="index"
                                            :label="item.sRoomName"
                                            :value="item.sId">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:sProjectId>
                                    <el-select v-model="condition.sProjectId" placeholder=" " clearable filterable @change="mxDoSearch">
                                        <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                                            :key="index"
                                            :label="item.sItemName"
                                            :value="item.sId">
                                            <span style="float: left">{{ item.sItemName }}</span>
                                            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:iIsRegister>
                                    <el-select v-model="condition.iIsRegister" @change="onChangeAndSet2LocalStorage" placeholder=" "
                                        clearable>
                                        <el-option v-for="(item, index) in optionsLoc.iIsRegisterOptions" :key="index"
                                            :label="item.sName" :value="item.sValue">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:iIsInject>
                                    <el-select v-model="condition.iIsInject" @change="onChangeAndSet2LocalStorage" placeholder=" " clearable>
                                        <el-option v-for="(item, index) in optionsLoc.iIsInjectOptions" :key="index"
                                            :label="item.sName" :value="item.sValue">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:iIsMachine>
                                    <el-select v-model="condition.iIsMachine" @change="onChangeAndSet2LocalStorage" placeholder=" " clearable>
                                        <el-option v-for="(item, index) in optionsLoc.iIsMachineOptions" :key="index"
                                            :label="item.sName" :value="item.sValue">
                                        </el-option>
                                    </el-select>
                                </template>
                                <template v-slot:iIsHoldOn>
                                    <el-select v-model="condition.iIsHoldOn" @change="onChangeAndSet2LocalStorage" placeholder=" " clearable>
                                        <el-option v-for="(item, index) in optionsLoc.iIsHoldOnOptions" :key="index"
                                            :label="item.sName" :value="item.sValue">
                                        </el-option>
                                    </el-select>
                                </template>

                            </SearchList>
                        </template>
                        <template v-slot:action>
                            <div class="c-action">
                                <div>
                                    <el-dropdown >
                                        <el-button type="primary" plain>
                                            <template #icon><Icon name="el-icon-setting"></Icon></template>
                                            <span>设置</span>
                                            <Icon name="el-icon-arrow-down"></Icon>
                                        </el-button>
                                        <template #dropdown>
                                            <el-dropdown-menu> 
                                                <el-dropdown-item v-if="$auth['report:machine:callConfig']"
                                                     @click="onOpenCallSet">
                                                    <i class="fa fa-call-fill i-height"></i>
                                                    <span>呼叫设置</span>
                                                </el-dropdown-item>
                                                <el-dropdown-item v-if="$auth['report:machine:printConfig']"
                                                    @click="onOpenPrintSet">
                                                    <i class="fa fa-print-setup i-height"></i>
                                                    <span>打印设置</span>
                                                </el-dropdown-item>
                                                <el-dropdown-item v-if="$auth['report:machine:pageConfig']">
                                                    <PanelConfigDialog :iModuleId="iModuleId" :formData="MngIndexPenalConfig" :configKey="'MachineMngPenalConfig'" @updateData="(data)=>MngIndexPenalConfig=data"></PanelConfigDialog>
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                    <el-button-icon-fa v-if="$auth['report:machine:myVoice']" type="primary" plain icon="fa fa-call-fill" 
                                        @click="onOpenVoice" 
                                        style="margin-left: 10px;">我的语音</el-button-icon-fa>
                                </div>
                                <div style="display: flex; align-items: center;">

                                    <el-button @click="onClickReset">
                                        <template #icon>
                                            <Icon name="el-icon-refresh-left" >
                                            </Icon>
                                        </template>
                                        重置
                                    </el-button>
                                    
                                    <el-button type="primary" v-if="$auth['report:machine:query']"
                                        :loading="loading"
                                        @click="mxDoSearch">
                                        <template #icon>
                                            <Icon name="el-icon-search" color="white"></Icon>
                                        </template>
                                        查询
                                    </el-button>
                                </div>
                            </div>
                        </template>
                        <template v-slot:content>
                            <el-table-extend v-loading="loading" :data="tableData" ref="mainTable"  stripe
                                oncontextmenu='return false' v-contextmenu:contextmenuDiv  @row-contextmenu="handleRightClick"
                                :row-class-name="mxRowClassName" @row-click="handleRowClick"
                                @sort-change="mxOnSort" highlight-current-row height="100%" style="width: 100%"
                                :configBtn="MngIndexPenalConfig.isShowConfigBtn" :iModuleId="iModuleId" storageKey="MachineMngIndexTable">
                                <el-table-column type="index" label="序号" prop="_index" align="center" width="60"
                                    class-name="tableColOverflowVisible"></el-table-column>
                                <el-table-column v-for="item in patientTableConfig"
                                    :show-overflow-tooltip="item.sProp !== 'img'" :key="item.index" :prop="item.sProp"
                                    :label="item.sLabel" :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth"
                                    :min-width="item.sMinWidth" :sortable="(!!item.iSort) ? 'custom' : false"
                                    :isSortable="item.isSortable" :sOrder="item.sOrder" :iIsHide="item.iIsHide"
                                    :column-key="item.sSortField ? item.sSortField : null">
                                    <template v-slot="scope">
                                        <template v-if="FlowStateEnum.includes(item.sProp)">
                                            <!-- 0:未上机;1:上机准备;2:上机中;3:延迟中;4:上机完成 -->
                                            <span v-if="item.sProp === 'iIsMachine' && scope.row[`${item.sProp}`] == 1"
                                                class="icon-blue">准备</span>
                                            <span v-else-if="item.sProp==='iIsMachine'&& scope.row[`${item.sProp}`]==3"
                                                class="icon-blue">延迟</span>
                                            <i v-else-if="scope.row[`${item.sProp}`] == 2"
                                                class="icon-blue" :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                            <i v-else-if="scope.row[`${item.sProp}`]" class="icon-green"
                                                :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                        </template>
                                        <template v-else-if="item.sProp === 'dDelayTime'">
                                            {{ setTime2HHmm(scope.row.dDelayTime)}}
                                        </template>
                                        <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                            {{mxFormatterDate( scope.row[`${item.sProp}`])}}
                                        </template>
                                        <template v-else-if="['sNuclideSupName', 'sTracerSupName'].includes(item.sProp)">
                                            <span v-if="scope.row[item.sProp]" v-html="scope.row[item.sProp]"></span>
                                        </template>
                                        <template v-else-if="['fRecipeDose'].includes(item.sProp)">
                                            {{ setRecipeDose(scope.row[item.sProp], scope.row.sRecipeDoseUnit) }}
                                        </template>
                                        <template v-else-if="['fBloodSugar'].includes(item.sProp)">
                                            {{ setBloodSugar(scope.row[item.sProp]) }}
                                        </template>
                                        <template v-else-if="['iIsPregnant'].includes(item.sProp)">
                                            {{ setPregnantText(scope.row[item.sProp]) }}
                                        </template>
                                        <template v-else>
                                            {{scope.row[`${item.sProp}`]}}
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table-extend>

                            <!-- 鼠标右击 -->
                            <v-contextmenu 
                                v-if="$auth['report:machine:call']"
                                :disabled="!callConfigBtnShow || !tableData.length" 
                                ref="contextmenuDiv">
                                <div class="call-contextmenu-inner">
                                  <div class="name">{{rightClickRow.sName}}</div> 
                                    <template v-if="rightClickCallBtnArr?.length">
                                        <v-contextmenu-item v-for="(item, index) in rightClickCallBtnArr" :key="index">
                                            <div class="relative">
                                                <el-button 
                                                    :loading="item.loading"
                                                    :class="item.isCalled ? 'isCalled' : ''"
                                                    _icon="fa fa-volume-up"
                                                    @click.stop="handleQuickCall(rightClickRow, item, index)">
                                                        <span :class="item.isCalled?'icon-green':''">{{item.buttonName}}</span>
                                                </el-button>
                                                <i v-if="item.isCalled" class="el-icon-check icon-green i-position"></i>
                                            </div>
                                        </v-contextmenu-item>
                                    </template>
                                    <div v-else>
                                        <el-empty :image-size="80" description="未配置按钮" />
                                    </div>
                                </div>
                            </v-contextmenu>
                        </template>
                        <template v-slot:footer>
                            <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
                                :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5"
                                :page-size="page.pageSize" layout="total, sizes, prev, pager, next" :total="page.total">
                            </el-pagination>
                        </template>
                    </LayoutTable>
                    
                    <!-- 呼叫设置-->
                    <CallSet :dialogVisible="callSet_DialogVisible" :iModule="{iModuleId:iModuleId, iModuleName:'上机管理'}"
                        @closeDialog="onCloseCallSet"></CallSet>

                    <!-- 我的语音 -->
                    <MyVoice v-model:dialogVisible="d_myVoice_v"></MyVoice>

                        <!-- 打印设置 -->
                    <PrintSet :dialogVisible="d_printSet_v"
                        :iModuleId="iModuleId"
                        @closeDialog="closePrintSetDialog"></PrintSet>

                    <!-- 呼叫列表 -->
                    <CallList v-model:dialogVisible="d_callList_v"></CallList>

                    <!-- 上机呼叫 呼叫-->
                    <Call v-model:dialogVisible="call_DialogVisible" :patientInfo="rightClickRow" :iModule="{iModuleId:iModuleId, iModuleName:'上机管理'}"
                        @closeDialog="closeCallDialog"></Call>

                </template>
                <template v-slot:c2 v-if="MngIndexPenalConfig.isShowApplyPanel">
                    <div class="m-flexLaout-ty c-penal-text">
                        <div class="c-item-2 m-flexLaout-ty">
                            <div class="c-text-title">
                                <h3>电子申请单</h3>
                                <ApplyListInfo class="i-button" :patientInfo="editLayer.selectedItem" buttonName="详情" type="primary" link></ApplyListInfo>
                            </div>
                            
                            <div class="g-flexChild" :style="{ backgroundColor: MngIndexPenalConfig.applyBgColor}">
                                <el-scrollbar class="my-scrollbar">
                                    <TextList :list="ApplyTextListConfig" :data="applyInfo"
                                        :configBtn="MngIndexPenalConfig.isShowConfigBtn" :iModuleId="iModuleId" storageKey="MachineMngIndexTextApply"></TextList>
                                </el-scrollbar>
                            </div>
                        </div>
                    </div>  
                </template>  
            </DragAdjust>
        </template>
        <template v-slot:c2>
            <div class="m-flexLaout-ty c-penal-text">
                <div class="c-item-1">
                    <div class="c-text-title">
                        <h3>上机记录</h3>   
                        <el-button-icon-fa class="i-button" :disabled="!editLayer.selectedItem.sId" link type="primary" icon="el-icon-tickets"
                            @click="openPatientInfoModifi">患者详情</el-button-icon-fa>
                    </div>
                    <div :style="{ backgroundColor: MngIndexPenalConfig.patientInfoBgColor}">
                        <TextList :list="TextListConfig" :data="currentPatientInfo" :labelWidth="100"
                            :configBtn="MngIndexPenalConfig.isShowConfigBtn" :iModuleId="iModuleId" storageKey="MachineMngIndexTextPatient">
                            <template v-for="(item, index) in ['dAppointmentTime', 'dApplyDate']"
                                v-slot:[item]="{ style, row }" :key="index">
                                <span :style="style" :title="mxFormatterDate(currentPatientInfo[item])">
                                    {{ mxFormatterDate(currentPatientInfo[item]) || '（空）' }}
                                </span>
                            </template>
                            <template v-for="(item, index) in ['dInjectionSta', 'dInjectionTime', 'dInjectionEnd']"
                                v-slot:[item]="{ style, row }" :key="index">
                                <span :style="style" :title="setTime2MMddhhmm(currentPatientInfo[item])">
                                    {{ setTime2MMddhhmm(currentPatientInfo[item]) || '（空）' }}
                                </span>
                            </template>
                            <template #dBirthday="{ row, style }" :title="mxToDate(currentPatientInfo[row.prop]) || '（空）'">
                                {{ mxToDate(currentPatientInfo[row.prop]) || '（空）' }}
                            </template>
                            <template #sNuclideSupName="{ row, style }">
                                <span :style="style" v-html="currentPatientInfo.sNuclideSupName || '（空）'"> </span>
                            </template>
                            <template #sTracerSupName="{ row, style }">
                                <span :style="style" v-html="currentPatientInfo.sTracerSupName || '（空）'"> </span>
                            </template>
                            <template #fRecipeDose="{ row, style }">
                                <span :style="style" :title="setRecipeDose(currentPatientInfo[row.prop], currentPatientInfo.sRecipeDoseUnit)">
                                    {{ setRecipeDose(currentPatientInfo[row.prop], currentPatientInfo.sRecipeDoseUnit)  || '（空）' }}
                                </span>
                            </template>
                            <template #fBloodSugar="{ row, style }">
                                <span :style="style" :title="setBloodSugar(currentPatientInfo[row.prop])">
                                    {{ setBloodSugar(currentPatientInfo[row.prop]) || '（空）' }}
                                </span>
                            </template>
                            <template #iIsPregnant="{ row, style }">
                                <span :style="style" :title="setPregnantText(currentPatientInfo[row.prop])">
                                    {{ setPregnantText(currentPatientInfo[row.prop]) || '（空）' }}
                                </span>
                            </template>
                        </TextList>
                    </div>
                </div>
                <div class="c-item-2">
                    <OperateArea :callBtnArray="rightClickCallBtnArr" :callConfigBtnShow="callConfigBtnShow" @updateTableInfo="updateTableInfo"></OperateArea>
                </div>
            </div>
            <!-- 患者详情 -->
            <PatientInfoRead :dialogVisible="d_PatientInfoModifi_v" :patientSid="editLayer.selectedItem.sId" :iModuleId="iModuleId"
                @closeDialog="closePatientInfoModifi">
            </PatientInfoRead>

        </template> 
    </DragAdjust>
</template>

<script>
import { Tickets } from '@element-plus/icons-vue';
import OperateArea from './components/OperateArea.vue'
import PanelConfigDialog from './components/PanelConfigDialog.vue'

// 接口、外部函数、混入
import Configs from './config'
import { deepClone, transformDate } from '$supersetUtils/function'
import { iIsRegisterOptions, iIsMachineOptions, iIsInjectOptions } from '$supersetResource/js/projects/apricot/enum.js'
import { mixinTable, mixinTableInner } from '$supersetResource/js/projects/apricot/index.js';
import { useUserConfigQueryByKey } from '$supersetResource/js/projects/apricot/useUserConfig.js';

import { getOperateComputerData, getOperateComputerRow } from '$supersetApi/projects/apricot/appointment/patientInfo.js'
import { getPatientBriefById } from '$supersetApi/projects/apricot/case/index.js'
import { getMechineInit } from '$supersetApi/projects/apricot/case/machine.js'

import { getCallButtonSetOfModules,getCalledBtn, handleCallAction} from '$supersetResource/js/projects/apricot/call.js'
import { useGetHospitalData, useGetMachineRoomData, useGetItemData, useChangeHospital, useChangeMachineRoom } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
import scannerKeyCodeEvent from '$supersetResource/js/projects/apricot/scannerKeyCodeEvent.js';
export default {
    name: 'apricot_Machine',
    mixins: [ mixinTable, mixinTableInner],
    components: {
        OperateArea,
        PanelConfigDialog,
        Tickets,
        Call: defineAsyncComponent(() => import('$supersetViews/apricot/components/Call.vue')), // 上机呼叫
        CallSet: defineAsyncComponent(() => import('$supersetViews/apricot/components/CallSet.vue')), // 呼叫设置
        ReportPrintBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPrintBtn.vue')), // 打印按钮
        MyVoice: defineAsyncComponent(() => import('$supersetViews/apricot/components/MyVoice.vue')),    // 我的语音
        CallList: defineAsyncComponent(() => import('$supersetViews/apricot/components/CallList.vue')),    // 呼叫列表
        PrintSet: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintSet.vue')),
        PatientInfoRead: defineAsyncComponent(() => import('$supersetViews/apricot/common/PatientInfoRead.vue')),
        ApplyListInfo: defineAsyncComponent(() => import('$supersetViews/apricot/components/ApplyListInfo.vue')),
    },
    data () {
        return {
            iModuleId: 5, // 上机管理标识 ，eName: 'MACHINE'， 在mixinPrintPreview混合模块中调用
            isMixinDynamicGetTableHead: true,   // 是否动态获取表头
            DA0: Configs.DA0,
            DA2: Configs.DA2,
            DA3: Configs.DA3,
            condition: {
                dAppointmentTimeSt: new Date(),
                dAppointmentTimeEd: new Date(),
                iIsMachine: '',
                iIsInject: '',
                iIsRegister: '1',
                iIsHoldOn: ''
            },
            isAppointDate: true,
            iIsHoldOn: false,
            searchStyle: Configs.searchStyle,
            pickerOptionsAppointDayStart: time => {
                // if (this.condition.dAppointmentTimeEd) {
                //     return time.getTime() > new Date(this.condition.dAppointmentTimeEd).getTime()
                // }
                // return time.getTime()
            },
            pickerOptionsAppointDayEnd: time => {
                // if (this.condition.dAppointmentTimeSt) {
                //     return time.getTime() < new Date(this.condition.dAppointmentTimeSt).getTime()
                // }
                // return time.getTime()
            },
            optionsLoc: {
                deviceTypeOptions: [],
                iIsInjectOptions: iIsInjectOptions,
                iIsMachineOptions: iIsMachineOptions,
                iIsRegisterOptions:iIsRegisterOptions,
                districtArrOption: [], //院区
                machineRoomArrOption: [], // 机房
                itemsArrOption: [],    // 项目
                iIsHoldOnOptions: [{
                    sValue: '',
                    sName: '全部'
                }, {
                    sValue: '1',
                    sName: '已挂机'
                }, {
                    sValue: '0',
                    sName: '未挂机'
                }]
            },
            d_myVoice_v: false,
            d_callList_v: false,
            d_printSet_v: false,
            call_DialogVisible: false, // 呼叫弹框
            callSet_DialogVisible: false, // 呼叫设置弹框
            rightClickRow: {}, // 右键单机行,
            rightClickCallBtnArr: [], //右键按钮组
            callBtnArray: [],  // 模块按钮组
            currentPatientInfo: {},
            TextListConfig: [...Configs.textLableConfig],
            ApplyTextListConfig: [...Configs.applyTextLableConfig],
            patientTableConfig: [...Configs.patientTable],
            d_PatientInfoModifi_v: false,
            consultMngPenalConfig: {},
            applyInfo: {},
            MngIndexPenalConfig: {
                rightTabPanels: []
            },
            page: {  // 分页	
                pageCurrent: 1,
                pageSize: localStorage.machineMngIndexPageSize ? JSON.parse(localStorage.machineMngIndexPageSize) : 30,
                total: 0
            },
        }
    },
    provide () {
        return {
            patientInfo: computed(() => this.editLayer.selectedItem),
            updateTableInfo: this.updateTableInfo,
            configValue: computed(() => this.MngIndexPenalConfig)
        }
    },
    computed: {
        workStation () {
            let temp = this.$store.getters['user/workStation'];
            return temp
        },
        callConfigBtnShow () {
            return this.workStation.stationTypeCode === this.iModuleId.toString();
        }
    }, 
    watch: {
        'page.pageSize'(val) {
            localStorage.setItem('machineMngIndexPageSize', val);
        },
        'workStation': {
            async handler (val, oldVal) {
                if (val) {
                    // 赋值院区Id到查询条件
                    this.condition.sDistrictId = val.districtId;
                    // 清空机房、项目查询条件
                    this.condition.sMachineryRoomId = '';
                    this.condition.sProjectId = '';
                    // 获取患者表格数据
                    oldVal && this.mxDoSearch()
                    // 获取按钮类型
                    if(oldVal && this.callConfigBtnShow)  {
                        this.rightClickCallBtnArr = await getCallButtonSetOfModules(this.iModuleId,val.stationId)
                        this.callBtnArray = deepClone(this.rightClickCallBtnArr)
                    }
                    // 根据院区查询相应的机房数据
                    await this.useGetMachineRoomData(val.districtId);
                    if(val.roomId) {
                        // 赋值机房Id到查询条件
                        const aHasRoom = this.optionsLoc.machineRoomArrOption.filter(item => item.sId == this.workStation.roomId)
                        aHasRoom.length && (this.condition.sMachineryRoomId = this.workStation.roomId);
                    }
                    if(this.optionsLoc.machineRoomArrOption.length) {
                        // 判断工作站的机房id是存在于查询到的机房数据里
                        var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === this.condition.sMachineryRoomId);
                        // 院区匹配到机房，查询检查项目
                        this.useGetItemData(target?.sDeviceTypeId);
                    } else {
                        // 匹配不到机房，清空项目
                        this.optionsLoc.itemsArrOption = []
                    }
                }
            }
        },
        'editLayer.selectedItem': {
            handler (val, oldVal) {
                if(val && oldVal && val.sId === oldVal.sId) return;
                val.sId && this.getApplyInfo(val.sId)
                val.sId && this.getMechineInit(val.sId)
            }
        }
    },
    methods: {
        setTime2HHmm (val) {
            return transformDate(val, false, 'HH:mm')
        },
        setTime2MMddhhmm (val) {
            return transformDate(val, false, 'MM-dd HH:mm')
        },
        async onClickReset(){
            this.condition = this.$options.data().condition;
            let machineSearch  = localStorage.getItem('machineSearch') 
            if(machineSearch) {
                const maS = JSON.parse(machineSearch)
                this.condition.iIsMachine = maS['iIsMachine']? maS['iIsMachine'] + '' : '';
                this.condition.iIsInject = maS['iIsInject']? maS['iIsInject'] + '' : '';
                this.condition.iIsRegister = maS['iIsRegister']? maS['iIsRegister'] + '' : '';
                this.condition.iIsHoldOn = maS['iIsHoldOn']? maS['iIsHoldOn'] + '' : '';
            }
            // 赋值院区Id到查询条件
            this.condition.sDistrictId = this.workStation.districtId;
            // 清空机房、项目查询条件
            this.condition.sMachineryRoomId = '';
            this.condition.sProjectId = '';
            // 根据院区查询相应的机房数据
            await this.useGetMachineRoomData(this.workStation.districtId);
            if(this.workStation.roomId) {
                // 赋值机房Id到查询条件
                const aHasRoom = this.optionsLoc.machineRoomArrOption.filter(item => item.sId == this.workStation.roomId)
                aHasRoom.length && (this.condition.sMachineryRoomId = this.workStation.roomId);
            }
            if(this.optionsLoc.machineRoomArrOption.length) {
                // 判断工作站的机房id是存在于查询到的机房数据里
                var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === this.condition.sMachineryRoomId);
                // 院区匹配到机房，查询检查项目
                this.useGetItemData(target?.sDeviceTypeId);
            } else {
                // 匹配不到机房，清空项目
                this.optionsLoc.itemsArrOption = []
            }
            this.mxDoSearch();
        },
        //  打开打印设置
        onOpenPrintSet () {
            if(!this.callConfigBtnShow) {
                this.$message.warning('请切换到上机工作站！');
                return
            }
            this.d_printSet_v = true;
        },
        closePrintSetDialog () {
            this.d_printSet_v = false;
        },
        // 打开患者信息修改弹窗
        openPatientInfoModifi () {
            //打开弹窗
            this.d_PatientInfoModifi_v = true;
        },
        // 关闭患者信息修改弹窗
        closePatientInfoModifi () {
            this.d_PatientInfoModifi_v = false;
        },
        onChangeAndSet2LocalStorage() {
            let params = {
                iIsRegister: this.condition.iIsRegister,
                iIsInject: this.condition.iIsInject,
                iIsMachine: this.condition.iIsMachine,
                iIsHoldOn: this.condition.iIsHoldOn
            }
            localStorage.setItem('machineSearch',JSON.stringify(params));
            this.mxDoSearch();
        },
        updatePatientOfTable () {
            this.updateTableInfo();
        },
        // 多选框change事件
        handleCheckBox (val, key) {
            this[key] = val;
            this.mxDoSearch();
        },
        
        // 打开呼叫
        onOpenCall () {
            this.call_DialogVisible = true
            // openCallWindow(target)
        },
        // 关闭呼叫  
        closeCallDialog() {
            this.call_DialogVisible = false
        },
        //  打开呼叫设置
        onOpenCallSet () {
            if(!this.callConfigBtnShow) {
                this.$message.warning('请切换到上机工作站！');
                return
            }
            this.callSet_DialogVisible = true
        },
        // 关闭呼叫设置
        async onCloseCallSet(isEditBtn) {
            this.callSet_DialogVisible = false
            if(isEditBtn) {
                this.rightClickCallBtnArr = await getCallButtonSetOfModules(this.iModuleId, this.workStation.stationId) 
                this.callBtnArray = deepClone(this.rightClickCallBtnArr)
            }
        },
        // 右键单击行
        handleRightClick (row) {
            if(!this.callConfigBtnShow) {
                this.$message.warning('请切换到上机工作站！');
                return
            }
            this.rightClickRow = row;
            this.getRowCalled(row.sId);
        },
        async getRowCalled(id) {
            this.rightClickCallBtnArr.map( item=>{
                item.isCalled = false
            })
            const calledBtnData = await getCalledBtn(id)
            this.rightClickCallBtnArr.forEach(item =>{
                if (calledBtnData.includes(item.buttonCode)) {
                    item.isCalled = true
                }
            })
        },
        // 列表快捷呼叫
        async handleQuickCall (row, data, index) {
            if(data.buttonCode == 0) {
                this.onOpenCall()
                return
            }
            data.loading = true
            this.rightClickCallBtnArr[index] = data 
            let jsonData = {
                stationId: data.stationId,
                sysModuleCode:data.moduleId,
                patientInfoId: row.sId,
                callBtnCode: data.buttonCode,
                captionsId:''
            }
            const isCalled = await handleCallAction(jsonData)
            data.loading = false
            if(isCalled.isCalled) {
                data.isCalled = true
            }
            this.rightClickCallBtnArr[index] = data 
            this.updateTableInfo(row.sId, row.index)
        },

        // 打开我的语音弹窗
        onOpenVoice () {
            this.d_myVoice_v = true;
        },
        // 打开呼叫列表
        onOpenCallList () {
            this.d_callList_v = true;
        },
        handleRowClick (row) {
            if (row.sId === this.editLayer.selectedItem.sId) return;
            this.onClickRow(row);
        },
        // 选择注射状态触发查询
        changeiIsInject (val) {
            this.condition.iIsInject = val;
            this.mxDoSearch()
        },
        changeAppointTime () {
            this.mxDoSearch()
        },
        // 勾选工作站
        changsMyStationId() {
            if(this.isStationId) {
                this.mxDoSearch()
            }
        },
        // 获取申请单数据
        getApplyInfo (sId) {
            getPatientBriefById({ sId: sId }).then(res => {
                if (res.success) {
                    this.applyInfo = res.data || {};
                    return;
                }
                this.applyInfo = {};
                this.$message.error(res.msg)
            }).catch(() => {
                this.applyInfo = {};
            })
        },
        // 获取上机信息Text
        getMechineInit (sId) {
            // this.currentPatientInfo = {};
            getMechineInit({
                sPatientId: sId
            }).then(res => { 
                if (res.success) {
                    let data = res.data || {};
                    this.currentPatientInfo = { ...this.editLayer.selectedItem};
                    Object.keys(data).map(key => {
                        if(data[key] ?? false) {
                            this.currentPatientInfo[key] = data[key];
                        }
                    })
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
        // 查询数据
        getData (obj) {
            let params = deepClone(obj);
            let dAppointmentTimeSt = params.condition.dAppointmentTimeSt;
            params.condition.dAppointmentTimeSt = this.mxFormateOneDayStart(dAppointmentTimeSt);

            // 当选择了结束时间，转换成23：59：59
            let dAppointmentTimeEd = params.condition.dAppointmentTimeEd;
            params.condition.dAppointmentTimeEd = this.mxFormateOneDayEnd(dAppointmentTimeEd);

            if(this.MngIndexPenalConfig.isSearchFormCurrentWStation) {
                // 工作站id赋值
                params.condition.sWorkStationId = this.workStation.stationId
            }            
            params.condition.iIsCancel = 0;
            getOperateComputerData(params).then((res) => {
                this.tableData = [];
                this.loading = false;
                if (res.success) {
                    let data = res.data.recordList || [];
                    this.tableData = data;
                    this.page.total = res.data.countRow;
                    // 赋选中状态
                    this.mxSetSelected();
                    if(!this.tableData.length) {
                        this.currentPatientInfo = {};
                        this.applyInfo = {}
                    }
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        },
        updateTableInfo (id = null, idx = null) {
            const sId = id !== null ? id : this.editLayer.selectedItem.sId;
            const index = idx !== null ? idx : this.editLayer.selectedItem.index;
            getOperateComputerRow({ sId }).then(res => {
                if (res.success) {
                    let data = res.data;
                    data.index = index;
                    this.tableData.splice(index, 1, data);
                    if(sId !== this.editLayer.selectedItem.sId) return
                    this.editLayer.selectedItem = this.tableData[index]; // 更新选中值
                    this.$refs.mainTable && this.$refs.mainTable.setCurrentRow(this.tableData[index]) // 设置选中值
                    return;
                }
            })
        },
        scannerSearch(code) {
            const params = {
                condition: {
                    sNuclearNum: code 
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 30
                }
            }
            getOperateComputerData(params).then((res) => {
                this.tableData = [];
                this.loading = false;
                if (res.success) {
                    let data = res.data.recordList || [];
                    this.tableData = data;
                    this.page.pageCurrent = 1;
                    this.page.pageSize = 10;
                    this.page.total = res.data.countRow;
                    // 赋选中状态
                    this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        },
        scannerMonitor() {
            // 设备扫描
            scannerKeyCodeEvent.onMachineEvent = (res => {
                if (this.$route.name === 'apricot_Machine') {
                    this.scannerSearch(res);
                }
            })
        },
        // 获取已呼叫按钮
        getCalledBtn(patientInfoId) {
            let params = {
                patientInfoId: patientInfoId
            }
            isCalledBtns(params).then( res =>{
                if(res.success) {
                    let arr = res.data
                    this.rightClickCallBtnArr.map( (item, index) =>{
                        item.isCalled = false
                        this.rightClickCallBtnArr[index] = item
                        if (arr.includes(item.buttonCode)) {
                            item.isCalled = true
                            this.rightClickCallBtnArr[index] = item
                        }
                        
                    })
                    return
                }
                this.$message.error(res.msg)
            }).catch( err=>{
                console.log(err)
            })
        },
        useGetHospitalData, 
        useGetMachineRoomData, 
        useGetItemData,
        useChangeHospital,
        useChangeMachineRoom,
    },
    async mounted () {
        // 获取页面配置
        let useUserConfigQuery = useUserConfigQueryByKey()
        await useUserConfigQuery('MachineMngPenalConfig', this, Configs.MngIndexPenalConfig);
        
        // 查询院区、与院区匹配的机房数据
        await this.useGetHospitalData();
        await this.onClickReset()
       
        // 获取表格数据
        // this.isMixinDynamicGetTableHead && this.mxGetTableList();
        this.scannerMonitor();
        if(this.callConfigBtnShow) {
            this.rightClickCallBtnArr = await getCallButtonSetOfModules(this.iModuleId, this.workStation.stationId) 
            this.callBtnArray = deepClone(this.rightClickCallBtnArr)
        }
    },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.c-action {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .c-checkbox {
        margin-right: 10px;

        &:last-child {
            margin-right: none;
        }
    }
}
.c-penal-text {
    .c-item-1 {
        padding: 10px; 
        background-color: #fff;
    }
    .c-item-2 {
        flex: 1; 
        padding: 10px; 
        background-color: #fff; 
        overflow: hidden;
    }
    .c-text-title {
        position: relative;
        h3 {
            padding: 10px 0;
            text-align: center;
            margin: 0 ;
        }
        .i-button {
            position: absolute;
            top: 11px;
            right: 8px;
        }
    }
}

.i-height {
    height: 20px;
}
.i-position {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}


</style>
