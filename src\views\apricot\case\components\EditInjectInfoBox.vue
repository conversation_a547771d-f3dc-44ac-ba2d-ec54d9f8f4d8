<template>
  <div class="m-flexLaout-ty">
      <div class="c-panel-01 m-flexLaout-ty">
          <div class="g-flexChild m-flexLaout-tx c-item">
              <el-form ref="refEditLayer"
                  class="scope--rules"
                  :model="form"
                  :rules="rules"
                  inline>
                  <template v-for="(item, index) in  InjectInfoForm">
                      <form-style
                          :configData="item"
                          :formData="form"
                          :optionData="optionsLoc"
                          :isPickerOptions="true">
                          <template v-if="item.sProp === 'dInjectionSta'"
                              v-slot:custom>
                              <!-- 满针时间  -->
                              <el-time-picker placeholder=""
                                  :default-value="defaultValue.iFull"
                                  v-model="form[item.sProp]"
                                  @focus="onFocusFn('iFull')"
                                  :readonly="!!item.iReadonly">
                              </el-time-picker>
                          </template>
                          <template v-else-if="item.sProp === 'dInjectionTime'"
                              v-slot:custom>
                              <!-- 实际时间  -->
                              <el-time-picker placeholder=""
                                  :default-value="defaultValue.iActual"
                                  v-model="form[item.sProp]"
                                  @focus="onFocusFn('iActual')"
                                  :readonly="!!item.iReadonly">
                              </el-time-picker>
                          </template>
                          <template v-else-if="item.sProp === 'dInjectionEnd'"
                              v-slot:custom>
                              <!-- 空针时间  -->
                              <el-time-picker placeholder=""
                                  :default-value="defaultValue.iNull"
                                  v-model="form[item.sProp]"
                                  @focus="onFocusFn('iNull')"
                                  :readonly="!!item.iReadonly">
                              </el-time-picker>
                          </template>
                          <template v-else-if="item.sProp === 'fFullNeedle'"
                              v-slot:custom>
                              <!-- 满针剂量  -->
                              <el-input v-model="form[item.sProp]"
                                  type="number"
                                  step="0.1"
                                  min="0"
                                  @input="handleChangeFullCount">
                                  <el-select slot="append"
                                      v-model="form.sFullUnit"
                                      :readonly="!!item.iReadonly"
                                      class="i-suffix-value"
                                      clearable
                                      placeholder="单位">
                                      <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                          :key="item.sId"
                                          :label="item.sName"
                                          :value="item.sValue">
                                      </el-option>
                                  </el-select>
                              </el-input>
                          </template>
                          <template v-else-if="item.sProp === 'fFactDose'"
                              v-slot:custom>
                              <!-- 实际注射剂量  -->
                              <el-input v-model="form[item.sProp]"
                                  type="number"
                                  step="0.1"
                                  min="0">
                                  <el-select slot="append"
                                      v-model="form.sFullUnit"
                                      :readonly="!!item.iReadonly"
                                      class="i-suffix-value"
                                      clearable
                                      placeholder="单位">
                                      <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                          :key="item.sId"
                                          :label="item.sName"
                                          :value="item.sValue">
                                      </el-option>
                                  </el-select>
                              </el-input>
                          </template>
                          <template v-else-if="item.sProp === 'fEmptyNeedle'"
                              v-slot:custom>
                              <!-- 空针剂量  -->
                              <el-input v-model="form[item.sProp]"
                                  type="number"
                                  step="0.1"
                                  min="0"
                                  @input="handleChangeEmptyCount">
                                  <el-select slot="append"
                                      v-model="form.sFullUnit"
                                      :readonly="!!item.iReadonly"
                                      class="i-suffix-value"
                                      clearable
                                      placeholder="单位">
                                      <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                          :key="item.sId"
                                          :label="item.sName"
                                          :value="item.sValue">
                                      </el-option>
                                  </el-select>
                              </el-input>
                          </template>
                          <!-- 渗漏 -->
                          <template v-else-if="item.sProp === 'sStainPosition'"
                              v-slot:custom>
                              <el-select :ref="item.sProp"
                                  v-model="form[item.sProp]"
                                  :readonly="!!item.iReadonly"
                                  filterable
                                  allow-create
                                  default-first-option
                                  clearable>
                                  <el-option v-for="(item, index) in optionsLoc[item.sOptionProp]"
                                      :key="index"
                                      :label="item.sName"
                                      :value="item.sName">
                                  </el-option>
                              </el-select>
                          </template>
                      </form-style>
                  </template>
              </el-form>
          </div>
          <div class="flex items-center justify-end c-button ">
              <el-button-icon-fa type="primary"
                  icon="el-icon-document-checked"
                  :class="{'pull-right': !isFormDatafromParent}"
                  :loading="saveLoading"
                  @click="handleSaveInjectClick">保存</el-button-icon-fa>

                  <el-button-icon-fa 
                icon="fa fa-close"
                @click="$emit('close')"
                >关闭</el-button-icon-fa>   

              <el-button-icon-fa v-if="isFormDatafromParent"
                  size="small"
                  :class="{'pull-right': !isFormDatafromParent}"
                  icon="el-icon-refresh-right"
                  @click="initform">重置</el-button-icon-fa>

              <el-button-icon-fa size="small"
                  v-if="isFormDatafromParent"
                  icon="el-icon-tickets"
                  @click="openApplyListDialog">申请单</el-button-icon-fa>
          </div>
      </div>
      <ApplyListInfo v-if="isFormDatafromParent" v-model:dialogVisible="d_applyList_v"
          :patientInfo="patientInfo"></ApplyListInfo>

      <PrintTemDialog v-model:dialogVisible="visibleTemplate"></PrintTemDialog>
  </div>
</template>
<script>
import FormStyle from '$supersetViews/components/FormStyle.vue'
import ApplyListInfo from '$supersetViews/apricot/components/ApplyListInfo.vue'    // 申请单

import { InjectForm } from './InjectInfoConfig'
import { deepClone } from '$supersetUtils/function'
import {  mixinElementConfigs, mixinPrintPreview } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/case/inject.js'
let defaultForm = {};
export default {
  mixins: [mixinElementConfigs, mixinPrintPreview],
  props: {
      // 表单数据是否来自父组件
      isFormDatafromParent: {
          type: Boolean,
          default: true
      },
      patientInfo: {
          type: Object,
          default: () => new Object()
      },
      optionsLoc: {
          type: Object,
          default: () => new Object()
      },
      injectData: {
          type: Object,
          default: () => new Object()
      }
  },
  emits: ['close'],
  components: {
      FormStyle,
      ApplyListInfo,
      PrintTemDialog: defineAsyncComponent(()=>import('$supersetViews/apricot/components/PrintTemDialog.vue'))

  },
  data () {
      return {
          iModuleId: 4, // 注射管理标识 ，eName: 'INJECTION'， 在mixinPrintPreview混合模块中调用
          loading: false,
          injectForm: InjectForm,
          rules: {},
          rights: {},
          form: {},
          formDefaultVal: {},
          bloodSugarLoading: false,
          saveLoading: false,
          d_applyList_v: false,
          injectModuleSetData: {
              iFull: 2,
              iNull: 2,
              iActual: 2,
              readInterval: 3,
              isShowActivityMeterPage: false,
          },
          defaultValue: {
              iFull: '',
              iActual: '',
              iNull: ''
          },
      }
  },
  computed: {
      userInfo () {
          let temp = this.$store.getters["user/userSystemInfo"];
          if (temp.__proto__.constructor === Object) {
              return temp;
          } else {
              return {};
          }
      },
      //根据项目过滤表单输入框
      InjectInfoForm () {
          return this.elementConfigData.InjectInfoForm && this.elementConfigData.InjectInfoForm.filter(item => [undefined, '', null].includes(item.sDeviceId) || item.sDeviceId === this.patientInfo.sRoomId);
      }
  },
  watch: {
      InjectInfoForm(val) {
          if(val.length) {
              defaultForm = {};
              val.map(item => {
                  defaultForm[item.sProp] = undefined;
              });
          }
      },
      injectData: {
          handler(val) {
              if(!this.isFormDatafromParent) {
                  this.getInjectInit()
                  return
              }
              this.form = deepClone(val);
              this.setNull2Undefined(this.form);
              if(!this.form.sId) {
                  this.initform()
              }
          },
          immediate: true
      }
  },
  methods: {
      onFocusFn (keyword) {
          let target = window.localStorage.getItem('injectModuleSetData');
          if (!target) {
              this.defaultValue[keyword] = new Date(new Date().getTime() - 2 * 60 * 1000);
              return;
          }
          target = JSON.parse(target);
          target[keyword] = target[keyword] || 0;
          this.defaultValue[keyword] = new Date(new Date().getTime() - target[keyword] * 60 * 1000);
      },
      // 初始化表单
      initform () {
          this.form = deepClone(defaultForm);
          this.form.sPatientId = this.patientInfo.sId;
          this.form.sNurseNo = this.userInfo.sNo;
          this.form.sNurseName = this.userInfo.sName;
          this.form['sNurseId'] = this.userInfo.sId;
          this.form['dInjectDate'] = new Date();
          let ApricotReportDoseUnit = this.optionsLoc.ApricotReportDoseUnit || [];
          this.form['sFullUnit'] = ApricotReportDoseUnit[0].sValue;

          let ApricotReportDrugDelivery = this.optionsLoc.ApricotReportDrugDelivery;
          if (ApricotReportDrugDelivery.length) this.form['sDrugDeliveryCode'] = ApricotReportDrugDelivery[0].sValue;
          this.form['sStainPosition'] = '无';
          let ApricotReportMedicineSource = this.optionsLoc.ApricotReportMedicineSource;
          if (ApricotReportMedicineSource.length === 1) this.form['sMedicineSource'] = ApricotReportMedicineSource[0].sValue;
          // dInjectionSta,dInjectionTime,dInjectionEnd

          let injectModuleSetData = window.localStorage.getItem('injectModuleSetData');
          if(!injectModuleSetData) {
              this.form['dInjectionSta'] = new Date(new Date().getTime() - 2 * 60 * 1000 - 5 * 1000);
              this.form['dInjectionTime'] = new Date(new Date().getTime() - 2 * 60 * 1000);
              this.form['dInjectionEnd'] = new Date(new Date().getTime() - 2 * 60 * 1000);
          } else{
              injectModuleSetData = JSON.parse(injectModuleSetData);
              this.form['dInjectionSta'] = new Date(new Date().getTime() - injectModuleSetData.iFull * 60 * 1000 - 5 * 1000);
              this.form['dInjectionTime'] = new Date(new Date().getTime() - injectModuleSetData.iActual * 60 * 1000);
              this.form['dInjectionEnd'] = new Date(new Date().getTime() - injectModuleSetData.iNull * 60 * 1000);
          }
          this.$nextTick(() => {
              this.$refs['refEditLayer'].clearValidate();
          });
      },
      // 获取初始化数据
      getInjectInit () {
          this.loading = true;
          let sId = this.patientInfo.sId
          Api.getInjectInit({
              sPatientId: sId
          }).then(res => {
              this.loading = false;
              if (res.success) {
                  let data = res.data || {};
                  if (!data.sId) {
                      this.initform();
                      this.setNull2Undefined(this.form);
                      // 不存在数据
                      return
                  }
                  this.form = deepClone(data);
                  this.setNull2Undefined(this.form);
                  this.$nextTick(() => {
                      this.$refs['refEditLayer']&&this.$refs['refEditLayer'].clearValidate();
                  })
                  return
              }
              this.$message.error(res.msg);
          }).catch(err => {
              this.loading = false;
              console.log(err);
          })
      },
      setUnitDoseText () {
          let val = this.staticPatientInit
          let target = this.optionsLoc.ApricotReportDoseUnit.find(item => val.sRecipeDoseUnit == item.sValue);
          val.sRecipeDoseUnitText = target ? target.sName : '';
      },
      // element组件 数字输入框 当数据返回为null 界面显示为0，设置为undefined 则不显示
      setNull2Undefined (obj) {
          if (Object.prototype.toString.call(obj) !== '[object Object]') return;
          if (!Object.keys(obj).length) return;
          for (let k of Object.keys(obj)) {
              if (obj[k] === null) {
                  obj[k] = undefined;
              }
          }
      },
      // 申请单
      openApplyListDialog () {
          if (!this.patientInfo.sId) {
              this.$message.warning('请选择患者数据！')
              return
          }
          if (!this.patientInfo.sApplyNO) {
              this.$message.warning('没有申请单信息')
              return
          }
          this.d_applyList_v = true;
      },
      // 根据满针剂量值，计算实际注射量
      handleChangeFullCount (val) {
          if(val == ''|| [null, undefined,''].includes(this.form.fEmptyNeedle)) {
              this.form['fFactDose'] = '';
            return
          }
          val = Number(val);
          let fEmptyNeedle = Number(this.form.fEmptyNeedle)
          if(val >= fEmptyNeedle) {
              let acc = this.accSub(val, this.form.fEmptyNeedle)
              this.form['fFactDose'] = acc >= 0 ? acc : undefined;
              return
          }
          this.form['fFactDose'] = '';
      },
      // 根据空针剂量值，计算实际注射量
      handleChangeEmptyCount (val) {
          if(val == ''|| [null, undefined,''].includes(this.form.fFullNeedle)) {
              this.form['fFactDose'] = '';
            return
          }
          val = Number(val);
          let fFullNeedle = Number(this.form.fFullNeedle);
          if(fFullNeedle >= val) {
              let acc = this.accSub(this.form.fFullNeedle, val)
              this.form['fFactDose'] = acc >= 0 ? acc : undefined;
              return
          }
          this.form['fFactDose'] = '';
      },
      /**
      ** 减法函数，用来得到精确的减法结果
      ** 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
      ** 调用：accSub(arg1,arg2)
      ** 返回值：arg1加上arg2的精确结果
      **/
      accSub (arg1 = 0, arg2 = 0) {
          var r1, r2, m, n;
          try {
              r1 = arg1.toString().split(".")[1].length;
          } catch (e) {
              r1 = 0;
          }
          try {
              r2 = arg2.toString().split(".")[1].length;
          } catch (e) {
              r2 = 0;
          }
          m = Math.pow(10, Math.max(r1, r2)); //last modify by deeka //动态控制精度长度
          n = (r1 >= r2) ? r1 : r2;
          return ((arg1 * m - arg2 * m) / m).toFixed(n);
      },
      // 获取下拉框组件选中值的标签名
      getName (arr, val) {
          let item = arr.find(item => item.sValue == val);
          return item ? item.sName : null;
      },
      // 保存注射信息
      handleSaveInjectClick () {
          if (!this.patientInfo.sId) {
              this.$message.warning('请选择患者数据！')
              return
          }
          let form = deepClone(this.form);
          form.sDrugDeliveryText = this.getName(this.optionsLoc.ApricotReportDrugDelivery, form.sDrugDeliveryCode);
          form.sBefDrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, form.sBefDrinkTypeCode);
          form.sAft3DrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, form.sAft3DrinkTypeCode);
          form.sAft6DrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, form.sAft6DrinkTypeCode);
          form.sMedicineSourceText = this.getName(this.optionsLoc.ApricotReportMedicineSource, form.sMedicineSource);
          form.sInjectionPositionText = this.getName(this.optionsLoc.ApricotReportInjectSite, form.sInjectionPosition);
          form.sFullUnitText = this.getName(this.optionsLoc.ApricotReportDoseUnit, form.sFullUnit);
          //  注射人
          this.optionsLoc.DoctorOptions.find(item => {
              if (item.userId === this.form.sNurseId) {
                  form.sNurseNo = item.userNo;
                  form.sNurseName = item.userName;
              }
          })

          if (form.dInjectDate && form.dInjectionSta) {
              let sDate = new Date(form.dInjectDate).toLocaleDateString();
              let sTime = new Date(form.dInjectionSta).toTimeString().substring(0, 8)
              form.dInjectionSta = new Date(`${sDate} ${sTime}`)
          }
          if (form.dInjectDate && form.dInjectionTime) {
              let sDate = new Date(form.dInjectDate).toLocaleDateString();
              let sTime = new Date(form.dInjectionTime).toTimeString().substring(0, 8)
              form.dInjectionTime = new Date(`${sDate} ${sTime}`)
          }
          if (form.dInjectDate && form.dInjectionEnd) {
              let sDate = new Date(form.dInjectDate).toLocaleDateString();
              let sTime = new Date(form.dInjectionEnd).toTimeString().substring(0, 8)
              form.dInjectionEnd = new Date(`${sDate} ${sTime}`)
          }

          this.$refs['refEditLayer'].validate((valid) => {
              if (!valid) {
                  this.$message.warning('填写正确信息');
                  return false;
              }
              if (!this.form.sId) {
                  this.saveLoading = true;
                  Api.saveInject(form).then(res => {
                      this.saveLoading = false;
                      if (res.success) {
                          this.$message.success(res.msg);
                          this.updateData();
                          // 新增时，根据缓存设置判断是否需要打印；
                          this.isFormDatafromParent && this.mxOnPrintByCache('injectModuleSetData');
                          return;
                      }
                      this.$message.error(res.msg);
                  }).catch(() => {
                      this.saveLoading = false;
                  })
                  return
              }
              this.saveLoading = true;
              Api.editInject(form).then(res => {
                  this.saveLoading = false;
                  if (res.success) {
                      this.$message.success(res.msg);
                      this.updateData();
                      // 修改时，根据缓存设置判断是否需要打印；
                      this.isFormDatafromParent && this.mxOnPrintByCache('injectModuleSetData', false);
                      return;
                  }
                  this.$message.error(res.msg);
              }).catch(() => {
                  this.saveLoading = false;
              })
          });
      },
      updateData () {
          this.isFormDatafromParent && this.getInjectInit();
          this.$emit('updateTable');
      },
      // 删除注射记录
      handleDelInjectClick () {
          if (!this.form.sId) {
              this.$message.warning('没有数据可删除！');
              return
          }
          Api.delInject({
              iVersion: this.form.iVersion,
              sId: this.form.sId,
              sPatientId: this.patientInfo.sId
          }).then(res => {
              if (res.success) {
                  this.$message.success(res.msg);
                  this.initform();
                  return
              }
              this.$message.error(res.msg);
          })
      },
  },
  mounted () {
      this.mxGetElementConfigBykeyword(
          {
              containerLabels: [
                  'InjectInfoForm'
              ],
              nodeLabel: 'apricotInject',
              systemLabel: 'ApricotReport'
          },
          {
              InjectInfoForm: this.injectForm,
          }
      );
  }
}
</script>
<style lang="scss" scoped>
.c-panel-01 {
  > .c-item {
      padding: 10px 15px 5px 5px;
  }
  .c-button {
      padding: 10px 15px 5px;
      border-top: 1px solid #eee;
      box-sizing: border-box;
  }
}
.i-suffix-value {
  width: 78px;
  height: calc(100% - 2px);
}
</style>
