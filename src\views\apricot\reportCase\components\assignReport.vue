<template>
    <el-dialog v-model="visible"
        title="分配报告"
        :close-on-click-modal="false"
        width="900"
        top="10vh"
        class="t-default my-dialog">
            <el-tabs v-model="doctorLevel" >
                <el-tab-pane v-for="(item, index ) in doctorLevelOption" :key="index" :label="item.sName" :name="item.sValue">
                    <div style="height:60vh" v-loading="loading">
                        <div style="padding:10px;" >
                        <el-radio-group v-model="radio[index]" @change="handleAssignDoctor">
                            <span class="item-style" v-for="(item, index ) in item.doctorOptions" :key="index">
                            <el-radio :label="item.userId">{{ item.userName }}</el-radio>
                            </span>
                        </el-radio-group>
                        </div>
                        <div v-if="!item.doctorOptions.length">
                        <div class="no-data">暂无数据！</div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <template #footer>
                <el-button-icon-fa icon="fa fa-close-1" @click="$emit('update:modelValue', false)">关闭</el-button-icon-fa>
            </template>
    </el-dialog>
</template>
<script>
import { getReportAboveDrData  } from '$supersetResource/js/projects/apricot/useHandlerSelect.js' // 获取报告医生
import {
  toAssignDoctor
} from '$supersetApi/projects/apricot/case/report.js'
export default {
  name: 'assignReport',
  props: {
    multipleSelection: {
      type: Array,
      default: () => ({})
    },
    modelValue: {
        type: Boolean,
        default: false,
    }
  },
  computed: {
    visible: {
        get() {
            return this.modelValue
        },
        set(val) {
            this.$emit('update:modelValue', val) 
        }
    }
  },
  data() {
    return {
      loading: false,
      doctorOptions: [],
      radio: [],
      doctorLevel: '0',
      doctorLevelOption: [{
        sValue: '0',
        sName: '报告医生',
        doctorOptions:[]
      },
      {
        sValue: '1',
        sName: '审核医生',
        doctorOptions:[]
      },
      {
        sValue: '2',
        sName: '复审医生',
        doctorOptions:[]
      }
      ]
    }
  },
  watch: {
    visible: {
        async handler(val) {
            if (val) {
                this.radio = []
                this.doctorLevel= '0'; 
                const Doctors = await getReportAboveDrData()
                this.doctorLevelOption[1].doctorOptions = Doctors.auditDoctors //获取审核医生
                this.doctorLevelOption[0].doctorOptions = Doctors.reportDoctors // 获取报告医生
                this.doctorLevelOption[2].doctorOptions = Doctors.recheckDoctors // 复审医生

            }
        }
    },
  },
  methods: {
    dropDownClick() {
      //this.visible = !this.visible
    },

    handleAssignDoctor() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择需要分配的报告！')
        return
      }
        const userId = this.radio[Number(this.doctorLevel)]
      let params = {
        destDoctorId: userId,
        patientInfoIds: []
      }

      this.multipleSelection.forEach((element, index) => {
        if (!element.sReporterName) {
          params.patientInfoIds.push(element.sId)
        }
      });
      if (!params.patientInfoIds.length) {
        this.$message.error('报告已有报告医生，无需分配！')
        this.visible = false
        return
      }
      
      let target = this.doctorLevelOption[Number(this.doctorLevel)].doctorOptions.find(item => item.userId === userId)
      this.$confirm(`确定分配给【${target.userName}】医生吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        toAssignDoctor(params).then(res => {
          if (res.success) {
            this.visible = false
            this.$message.success(res.msg)
            this.$emit('reFreshTable')
          }
        }).catch(err => {
          console.log(err)
        })
      })
    }

  },
  async mounted() {
    
  },
}
</script>
<style lang="scss" scoped>
.item-style {
  display: inline-block;
  width: 160px;
  padding: 5px 10px;
}

.no-data {
  margin-left: 200px;
  margin-top: 80px;
  font-size: 18px;
  font-weight: 500;
  color: rgb(199, 199, 199);
}
</style>
