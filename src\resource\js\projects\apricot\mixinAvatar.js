//  混入头像相关操作
const mixinAvatar = {
  data() {
    return {
      d_CollectAvatar_v: false,
      refresh: Math.random()
    }
  },
  methods: {
    // 打开采集头像弹窗
    onClickFace() {
      this.d_CollectAvatar_v = true;
    },
    // 关闭采集头像弹窗
    closeCollectAvatarDialog() {
      this.d_CollectAvatar_v = false;
    },
    // 拍摄完成刷新头像
    refreshImg() {
      this.tableData[this.editLayer.selectedItem.index]['refresh'] = Math.random();
    }
  },
}
export default mixinAvatar;
