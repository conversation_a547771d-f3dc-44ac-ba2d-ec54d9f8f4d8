<template>
    <div>
        <div style="margin: 30px 0;">
            <el-input class="input-new-tag"
                v-model="keywordValue"
                ref="saveTagInput"
                placeholder="请输入关键字"
                @blur="handleInputBlur">
            </el-input>
            <el-button-icon-fa icon="el-icon-check"
                type="primary"
                @click="handleInputConfirm">添加</el-button-icon-fa>
        </div>
        <el-tag :key="tag"
            v-for="tag in dynamicTags"
            closable
            size="large"
            :disable-transitions="false"
            @close="handleClose(tag)">
            {{tag.sKeyword}}
        </el-tag>
    </div>
</template>
<script>
import { kipReportKeyList, kipReportKeyAdd, kipReportKeyDel } from '$supersetApi/projects/apricot/reportCase/kip.js';

export default {
    data () {
        return {
            dynamicTags: [],
            inputVisible: false,
            keywordValue: ''
        };
    },
    created () {
        this.getKeywords();
    },
    methods: {
        handleClose (tag) {
            this.$confirm(`确定要删除关键字【${tag.sKeyword}】吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                const { sId } = tag;
                kipReportKeyDel({ sId }).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err)
                })
            })
            // this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
        },

        showInput () {
            this.inputVisible = true;
            this.$nextTick(_ => {
                this.$refs.saveTagInput.$refs.input.focus();
            });
        },

        handleInputBlur () {
            setTimeout(() => {
                this.keywordValue = '';
                this.inputVisible = false;
            }, 1000)
        },

        handleInputConfirm () {
            let params = {
                keyword: this.keywordValue
            }
            kipReportKeyAdd(params).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    this.keywordValue = '';
                    this.getKeywords();
                    // this.dynamicTags.push(res.data);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err)
            })
        },
        getKeywords () {
            kipReportKeyList().then(res => {
                if (res.success) {
                    this.dynamicTags = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.el-tag {
    margin-left: 10px;
}

.button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
}

.input-new-tag {
    width: 300px;
    margin-right: 15px;
    vertical-align: bottom;
}
</style>