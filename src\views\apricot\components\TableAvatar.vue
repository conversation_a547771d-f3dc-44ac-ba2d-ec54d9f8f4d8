<template>
    <el-image class="my-image"
        :src="photoDownUrl + '&uniqueId=' + (patientInfo.sInnerIndex ? patientInfo.sInnerIndex : patientInfo.sId)  + '&refresh=' + patientInfo.refresh"
        fit="cover">
        <template #error>
        <div 
            class="my-img-error"
            :style="{'background-color':patientInfo.sSex == '2' ? '#f4a5e0' : '#52b2e8'}">
            <svg v-if="patientInfo.sSex == '2'"
                class="fa"
                aria-hidden="true">
                <use :xlink:href="'#' + 'fa-female-head'"></use>
            </svg>
            <svg v-else
                class="fa"
                aria-hidden="true">
                <use :xlink:href="'#' + 'fa-male-head'"></use>
            </svg>
        </div>
        </template>
    </el-image>
</template>
<script>
// 全局参数
import { photoDownUrl } from '$supersetUtils/base_config'
import { getToken } from "$supersetUtils/auth"

export default {
    name: 'TableAvatar',
    props: {
        patientInfo: {
            type: Object,
            default: ({})
        },
        refresh: {
            type: [Number],
        },
        index: {
            type: [Number],
        },
    },
    data () {
        return {
            photoDownUrl: photoDownUrl + '?' + getToken(),
        }
    },
}
</script>
<style lang="scss" scoped>
.my-image {
    width: 100%;
    height: 100%;
    border: 1px solid #eee;
    background-color: white;
    vertical-align: middle;
    margin-right: 20px;
    cursor: pointer;
    .my-img-error {
        svg {
            font-size: 100px;
            position: relative;
            bottom: 0;
            left: -16px;
        }
    }
}
</style>
