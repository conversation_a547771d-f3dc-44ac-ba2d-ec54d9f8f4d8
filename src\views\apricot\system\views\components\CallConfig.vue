<template>
    <div class="c-container">
        <div class="c-left m-flexLaout-ty">
            <!-- <h4 style="padding:10px; border-bottom: 1px solid #eee;">工作站</h4> -->
            <div class="g-flexChild">
                <el-tree ref="tree"
                    v-loading="treeLoading"
                    :data="treeData"
                    node-key="treeId"
                    highlight-current
                    default-expand-all
                    empty-text=""
                    :props="defaultProps"
                    @node-click="onNodeClick">
                </el-tree>
            </div>
        </div>
        <div class="c-right">
            <div class="c-flex-context">
                <el-tabs v-model="activeName" >
                    <el-tab-pane label="语音设置" name="VoiceSet"> 
                        <voice-set v-if="activeName == 'VoiceSet'" :seletedNode="seletedNode"></voice-set>
                    </el-tab-pane>
                    <el-tab-pane label="按钮设置" name="buttonSet">
                        <CallBtnSet v-if="activeName == 'buttonSet'" :seletedNode="seletedNode"></CallBtnSet>
                    </el-tab-pane>
                    <el-tab-pane label="推送屏设置" name="ToScreenSet">
                        <to-screen-set v-if="activeName == 'ToScreenSet'" :seletedNode="seletedNode"></to-screen-set>
                    </el-tab-pane>
                </el-tabs>    
            </div>
        </div>
    </div>

</template>
<script>
import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/common/callSet.js'
import { getScreen } from '$supersetApi/projects/apricot/common/call.js'

import VoiceSet from './VoiceSet.vue'
import CallBtnSet from './CallBtnSet.vue'
import ToScreenSet from './ToScreenSet.vue'


export default {
    name: 'CallConfig',
    //mixins: [mixinTable],
    components: {
        VoiceSet,
        CallBtnSet,
        ToScreenSet
    },
    data () {
        return {
            treeLoading: false,
            loading: false,
            activeName: 'VoiceSet',
            treeData: [],
            defaultProps: {
                children: 'workStationDtos',
                label: 'treeName'
            },
            seletedNode: {},
            text: 1,
            tableData:[],
            screenTableData:[],
            isEdit: true,
            reRender: true,
            copyData:{},
            currentRow: null,
            sStationId: this.$store.getters['user/workStation'].stationId,
            isSaveLoading: false,
        }
    },
    methods: {
        onNodeClick (node) {
            this.seletedNode = node
        }, 
        // 获取院区/工作站
        getWokeStationByDis(){
            this.treeLoading = true
            Api.getWorkStations().then( res=>{
                this.treeLoading = false
                if(res.success) {
                    this.treeData = res.data ? res.data:[]
                    let data = this.treeData[0]
                    if(data && data.workStationDtos.length > 0) {
                        this.seletedNode = data.workStationDtos[0]  
                        // this.seletedNode['stationId'] = data.workStationDtos[0].treeId
                        // this.seletedNode['stationName'] = data.workStationDtos[0].treeName
                        this.$nextTick( ()=>{
                            this.$refs.tree.setCurrentKey(data.workStationDtos[0].treeId)
                        })
                    }
                }
                
            }).catch(()=>{
                this.treeLoading = false
                console.log(err)
            })
        },
        
       
    },
    mounted () {
        this.getWokeStationByDis()
        this.$nextTick(() => {
            // this.rowDrop()
            // this.getModuleTemlateSelected(this.seletedNode.sValue)
        });

    },
    created () {

    }

};
</script>
<style lang="scss" scoped>
.c-container {
    height: 100%;
    display: flex;
    padding-left: 10px;
    padding-bottom: 10px;
    overflow: hidden;
    .c-left {
        padding: 10px 0 0 0;
        width: 260px;
        border-right: 1px solid #eee;
        overflow: hidden;
        overflow: auto;
        :deep(.el-tree-node__content ){
            height: 36px;
        }
    }
    .c-right {
        flex: 1;
        overflow: hidden;
    }
}
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 22px;
    padding-left: 20px;
    .c-form {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #eee;
        .modulesName {
            padding-left: 15px;
            font-size: 16px;
            height: 18px;
            span {
                font-weight: bold;
            }
        }
        .pull-right {
            margin-right: 15px;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 10px 15px 0;
        overflow: auto;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;
            > button {
                margin-top: 13px;
            }
        }
        .c-content {
            flex: 1;
            height: 0px;
        }
        .el-table td {
            padding: 3px 0;
        }
    }
    
}
.danger {
    color: rgb(245, 108, 108)
}
.action {
    margin-left: 10px;
    font-size: 15px;
    cursor: pointer;
}
:deep(.el-tabs--card) {
    >.el-tabs__header .el-tabs__nav {
        background-color: var(--el-color-primary-light-9);
        border-color: #e0e4ed;
        // border-left: none;
        border-radius:0px 4px 0px;
    }
    >.el-tabs__header .el-tabs__item {
        border-bottom: var(--el-border);
    }
    >.el-tabs__header .el-tabs__item.is-active {
        background-color: #fff;
        border-color: #fff;
        border-left: 1px solid var(--el-border-color-light)
    }
    >.el-tabs__header .el-tabs__item:first-child.is-active {
        border-left:none
    }
}
:deep(.el-tabs) {
    >.el-tabs__header {
        margin-left: 0;
    }
}
</style>
