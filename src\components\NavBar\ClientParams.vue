<template>
    <div v-loading="testLoading">
        <div class="c-title">
            <span>客户端设置</span>
        </div>
        <div>
            <h5 class="i-h5">运行状态检测</h5>
            <el-form :model="formClient" :rules="rulesClient" ref="refClient" class="client-form" label-width="120px">
                <el-form-item label="本机IP:" prop="ip" clearable>
                    <el-input v-model="formClient.ip" @keyup.native.enter="onTestClientClick"></el-input>
                </el-form-item>
                <el-form-item label="运行状态:">
                    <el-input v-model="linkTips" :class="{'i-text-primary':linkTips=='运行中','i-text-danger':linkTips=='未启动'}" readonly></el-input>
                </el-form-item>
                <el-form-item label="自动检测:">
                    <el-select v-model="isEnableTestLink" :teleported="false" @change="handleEnableChange">
                        <el-option :value="1" label="是"></el-option>
                        <el-option :value="0" label="否"></el-option>
                    </el-select>
                </el-form-item>

                <div style="text-align:right;">
                    <el-button-icon-fa :disabled="!isEnableTestLink" icon="el-icon-link" size="small" type="primary" plain
                        @click="onTestClientClick">测试</el-button-icon-fa>

                    <el-button-icon-fa :disabled="!isEnableTestLink" icon="el-icon-thumb" size="small" type="primary" plain
                        @click="onActiveClient">启动</el-button-icon-fa>
                </div>
            </el-form>
        </div>
        <div class="c-item-2">
            <h5 class="i-h5">参数设置</h5>
            <el-form :model="clientParams" ref="refClient" class="client-form" label-width="120px">
                <el-form-item label="高拍仪驱动程序:">
                    <el-input v-model="clientParams.cameraExe" clearable>
                        <template #append>
                            <el-button type="primary" link size="small" @click="onChooseFileClick('cameraExe')">选择</el-button>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="高拍仪图片目录:">
                    <el-input v-model="clientParams.cameraFilePath" clearable>
                        <template #append>
                            <el-button type="primary" link size="small" @click="onChooseFileClick('cameraFilePath')">选择</el-button>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="扫描仪驱动程序:">
                    <el-input v-model="clientParams.scannerExe" clearable>
                        <template #append>
                            <el-button type="primary" link size="small" @click="onChooseFileClick('scannerExe')">选择</el-button>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="扫描仪图片目录:">
                    <el-input v-model="clientParams.scannerFilePath" clearable>
                        <template #append>
                            <el-button type="primary" link size="small" @click="onChooseFileClick('scannerFilePath')">选择</el-button>
                        </template>
                    </el-input>
                </el-form-item>
                <div style="text-align:right;">
                    <el-button-icon-fa icon="fa fa-save" size="small" type="primary"
                        @click="onSaveLocalClick">保存</el-button-icon-fa>
                    <el-button-icon-fa icon="fa fa-close-1" size="small" @click="$emit('closePop')">关闭</el-button-icon-fa>
                </div>
            </el-form>
        </div>

        <el-dialog append-to-body :modelValue="d_showDir_v" class="t-default my-dialog" width="900px"
            :close-on-click-modal="false" title="目录" @close="d_showDir_v = false">
            <div v-loading="treeLoading" style="height: 60vh;overflow: auto;padding: 15px 30px;">
                <el-tree :data="treeData" :props="defaultProps" ref="dTree" node-key="absolutePath" highlight-current lazy
                    :load="loadNode" @node-expand="handleNodeExpand" @node-click="handleNodeClick">
                </el-tree>
            </div>
            <template #footer>
                <span style="margin-right: 30px;">当前选中路径：{{ selectedItem.absolutePath }}</span>
                <el-button-icon-fa type="primary" icon="el-icon-check" @click="onCheckedClick">选好了</el-button-icon-fa>
                <el-button-icon-fa _icon="fa fa-close-1" @click="d_showDir_v = false">关闭</el-button-icon-fa>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import { baseURL } from '$supersetUtils/request'
import { testLink, queryClientIp } from '$supersetApi/user'
import { getLocalConfig, saveLocalConfig } from '$supersetApi/projects/apricot/assistServe/index.js'
import { cdburnGetSubFile } from '$supersetApi/projects/apricot/case/report.js'
export default {
    name: "ClientParams",
    props: {
        isShow: {
            type: Boolean,
            default: false
        }
    },
    emits: ['updateIP', 'closePop'],
    data () {
        const validateIP = (rule, value, callback) => {
            var regexIP = /^((25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))\.){3}(25[0-5]|2[0-4]\d|((1\d{2})|([1-9]?\d)))$/g;
            if (!value) {
                return callback(new Error('IP不能为空！'));
            } else if (!regexIP.test(value)) {
                return callback(new Error('IP格式不正确！'))
            } else {
                callback();
            }
        }
        return {
            isLinkClient: null,
            formClient: {
                ip: ''
            },
            rulesClient: {
                ip: [{ required: true, message: 'IP地址不能为空！', trigger: 'blur' },
                { required: true, validator: validateIP, trigger: 'blur' },]
            },
            testLoading: false,
            isEnableTestLink: 1,
            linkTips: '',
            clientParams: {
                cameraExe: undefined,
                cameraFilePath: undefined,
                scannerExe: undefined,
                scannerFilePath: undefined
            },
            d_showDir_v: false,
            treeLoading: false,
            treeData: [],
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            selectedItem: {},
            chooseKey: ''
        }
    },
    watch: {
        async isShow (value) {
            if (value) {
                await this.testClientLink();
                if(this.linkTips == '未启动') return
                this.getLocalConfig();
            }
        }
    },
    methods: {
        // 测试按钮点击事件
        onTestClientClick () {
            this.isLinkClient = null;
            this.$refs['refClient'].validate(valid => {
                if (!valid) {
                    this.$message.warning('请填写正确IP地址！');
                    return false;
                }
                this.testClientLink();
            })
        },
        // 测试连接
        async testClientLink (isFirst) {
            if (this.formClient.ip) {
                let apricotAssist = baseURL.apricotAssist;
                let ulrAnalysis = apricotAssist.split(':');
                let protocol = ulrAnalysis[0];
                let port = ulrAnalysis[2];
                let newUrl = `${protocol}://${this.formClient.ip}:${port}`;
                window.configs.urls.apricotAssist = newUrl;
                baseURL.apricotAssist = newUrl;
            }
            let isEnableTestLink = sessionStorage.getItem('isEnableTestLink');
            this.isEnableTestLink = isEnableTestLink !== null ? Number(isEnableTestLink) : 1;
            if (!this.isEnableTestLink) {
                return
            }
            this.testLoading = true;
            this.linkTips = '';
            await testLink().then((res) => {
                this.testLoading = false;
                if (res === undefined) {
                    this.linkTips = '未启动';
                    return
                }
                if (Object.prototype.toString.call(res) === '[object Object]' && !res.success) {
                    this.linkTips = '未启动';
                    return
                }
                this.linkTips = '运行中';
            }).catch(err => {
                this.linkTips = '未启动';
                this.testLoading = false;
                this.$message.closeAll();
            })
        },
        //启动客户端
        onActiveClient () {
            let a = document.createElement('a');
            a.href = 'XingXiang://';
            // a.setAttribute('target', '_blank');
            document.body.append(a);
            a.click();
            a.remove();
        },
        // 获取客户端IP
        queryClientIp () {
            queryClientIp().then(res => {
                if (res.data) {
                    // ip 赋值
                    this.formClient.ip = res.data?.clientIp || undefined;
                    this.$emit('updateIP', res.data?.clientIp || undefined);
                    this.testClientLink(true);
                    return
                }
            }).catch(err => {
                console.log(err);
            })
        },
        // 是否开启客户端连接
        handleEnableChange (val) {
            this.isEnableTestLink = val;
            sessionStorage.setItem('isEnableTestLink', val);
            if (!val) {
                this.isLinkClient = null;
            }
        },
        // 展开节点
        handleNodeExpand (data, node) {
            if (!this.nodeLoading) {
                node.loaded = false;
                node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
            }
            this.$refs.dTree.setCurrentKey(node.data.absolutePath);
            this.selectedItem = data;
        },
        // 点击节点
        handleNodeClick (data, node) {
            this.selectedItem = data;
            if (node.expanded) {
                node.loaded = false;
                !data.isClick && node.expand(); // 主动调用展开节点方法，重新查询该节点下的所有子节点
            }
        },
        // 加载节点
        loadNode (node, resolve) {
            if (node.level === 0) {
                return
            }
            let params = {
                absolutePath: node.data.absolutePath
            }
            this.nodeLoading = true;
            cdburnGetSubFile(params).then(res => {
                this.nodeLoading = false;
                if (!res.success) {
                    this.$message.error(res.msg);
                    resolve([])
                    return
                }
                node.data.isClick = true;
                let arr = res.data || [];
                resolve(arr)
            }).catch(err => {
                console.log(err);
                this.nodeLoading = false;
                resolve([])
            })
        },
        // 获取根目录
        GetSubFile () {
            let params = {
                absolutePath: '/'
            }
            this.treeLoading = true;
            cdburnGetSubFile(params).then(res => {
                this.treeLoading = false;
                if (!res.success) {
                    this.$message.error(res.msg);
                    return
                }
                this.treeData = res.data || [];
            }).catch(err => {
                this.treeLoading = false;
                console.log(err);
            })
        },
        // 选择目录
        onChooseFileClick(key) {
            this.selectedItem = {};
            this.chooseKey = key;
            this.d_showDir_v = true;
            !this.treeData.length && this.GetSubFile();
        },
        // 选中目录
        onCheckedClick () {
            this.clientParams[this.chooseKey] = this.selectedItem.absolutePath;
            this.d_showDir_v = false;
        },
        // 保存客户端参数配置
        onSaveLocalClick () {
            saveLocalConfig(this.clientParams).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    // this.$emit('closePop')
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
        // 获取客户端参数配置
        getLocalConfig () {
            getLocalConfig().then(res => {
                if (res.success) {
                    this.clientParams = res.data || this.$options.data().clientParams;
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        }
    },
    mounted () {
        this.queryClientIp();
    }
}
</script>

<style lang="scss" scoped>
.c-title {
    border-left: 3px solid var(--el-color-primary);
    padding: 5px 10px;
    background: var(--el-color-primary-light-8);
    font-size:16px;
}

.i-h5 {
    margin: 10px;
}

.client-form {
    margin: 0 10px;
}

.c-item-2 {
    margin-top: 30px;
    margin-bottom: 10px;
    border-top: 1px solid var(--el-border-color);
    overflow: hidden;
}

:deep(.i-text-primary) {
    .el-input__inner {
        color:var(--el-color-primary)
    }
}
:deep(.i-text-danger) {
    .el-input__inner {
        color:var(--el-color-danger)
    }
}

:deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
    background-color: var(--table-highlight-row-bg);
}
</style>