<template>
    <!-- 接口管理 -->
    <div class="c-tamplte m-flexLaout-tx">
        <div class="g-flexChild c-content t-1 m-flexLaout-ty">
            <h3>
                <span>医院</span>
                <i class="el-icon-refresh"
                    title="刷新"
                    @click="getHospitalList"></i>
            </h3>
            <div class="g-flexChild c-item"
                v-loading="loading1">
                <el-table :data="tableData1"
                    ref="table1"
                    border
                    highlight-current-row
                    height="100%"
                    :header-cell-style="{ color: 'var(--el-color-primary)' }"
                    @row-click="onRowClick1">
                    <el-table-column prop="desp"
                        label="医院名称"
                        min-width="100">
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="g-flexChild c-content t-2 m-flexLaout-ty">
            <h3>
                <span>系统</span>
                <i class="el-icon-refresh"
                    title="刷新"
                    @click="getHospitalSysPage(selectedRow1.code)"></i>
            </h3>
            <div class="g-flexChild c-item"
                v-loading="loading2">
                <el-table :data="tableData2"
                    ref="table2"
                    border
                    highlight-current-row
                    height="100%"
                    :header-cell-style="{ color: 'var(--el-color-primary)' }"
                    @row-click="onRowClick2">
                    <el-table-column prop="hospitalSysName"
                        label="系统名称"
                        min-width="100">
                        <template v-slot="{row}">
                            <el-input v-if="row.isEdit"
                                v-model="row.hospitalSysName"
                                size="small" />
                            <span v-else>{{row.hospitalSysName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="hospitalSysCoding"
                        label="系统编码"
                        min-width="100">
                        <template v-slot="{row}">
                            <el-input v-if="row.isEdit"
                                v-model="row.hospitalSysCoding"
                                size="small" />
                            <span v-else>{{row.hospitalSysCoding}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="address"
                        align="center"
                        width="110px"
                        label="操作">
                        <template v-slot:header>
                            <span>操作</span>
                            <el-button-icon-fa class="icon-btn"
                                type="primary"
                                link
                                _icon="fa fa-plus"
                                title="添加"
                                :disabled="!selectedRow1.code"
                                @click="handleAddRow(2)"></el-button-icon-fa>
                        </template>
                        <template v-slot="{row, $index}">
                            <el-button-icon-fa v-if="!row.isEdit"
                                class="icon-btn"
                                type="primary"
                                _icon="el-icon-edit"
                                link
                                title="编辑"
                                @click.stop="handleEdit2($index)"></el-button-icon-fa>
                            <el-button-icon-fa v-else
                            class="icon-btn"
                                type="primary" 
                                link
                                _icon="fa fa-save"
                                title="保存"
                                @click.stop="handleSave2($index, row)"></el-button-icon-fa>
                            <el-button-icon-fa type="danger"
                            class="icon-btn"
                                _icon="el-icon-delete"
                                link
                                title="删除"
                                @click.stop="handleDelete2($index, row)"></el-button-icon-fa>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="g-flexChild c-content t-3 m-flexLaout-ty">
            <h3>
                <span>参数</span>
                <i class="el-icon-refresh"
                    title="刷新"
                    @click="getHospitalSysParamPage(selectedRow2.id)"></i>
            </h3>
            <div class="g-flexChild c-item"
                v-loading="loading3">
                <el-table :data="tableData3"
                    ref="table3"
                    border
                    highlight-current-row
                    height="100%"
                    :header-cell-style="{ color: 'var(--el-color-primary)' }">
                    <el-table-column prop="param"
                        label="参数名称"
                        min-width="100">
                        <template v-slot="{row}">
                            <el-input v-if="row.isEdit"
                                v-model="row.param"
                                size="small" />
                            <span v-else>{{row.param}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="paramValue"
                        label="参数值"
                        min-width="100">
                        <template v-slot="{row}">
                            <el-input v-if="row.isEdit"
                                v-model="row.paramValue"
                                size="small" />
                            <span v-else>{{row.paramValue}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="paramDesc"
                        label="参数描述"
                        min-width="180">
                        <template v-slot="{row}">
                            <el-input v-if="row.isEdit"
                                v-model="row.paramDesc"
                                size="small" />
                            <span v-else>{{row.paramDesc}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="address"
                        align="center"
                        width="110px"
                        label="操作">
                        <template v-slot:header>
                            <span>操作</span>
                            <el-button-icon-fa class="icon-btn"
                                type="primary"
                                link
                                _icon="fa fa-plus"
                                title="添加"
                                :disabled="!selectedRow2.id"
                                @click="handleAddRow(3)"></el-button-icon-fa>
                        </template>
                        <template v-slot="{row, $index}">
                            <el-button-icon-fa v-if="!row.isEdit"
                            class="icon-btn"
                                type="primary"
                                _icon="el-icon-edit"
                                link
                                title="编辑"
                                @click.stop="handleEdit3($index, row)"></el-button-icon-fa>
                            <el-button-icon-fa v-else
                            class="icon-btn"
                                type="primary"
                                link
                                _icon="fa fa-save"
                                title="保存"
                                @click.stop="handleSave3($index, row)"></el-button-icon-fa>
                            <el-button-icon-fa type="danger"
                            class="icon-btn"
                                _icon="el-icon-delete"
                                link
                                title="删除"
                                @click.stop="handleDelete3($index, row)"></el-button-icon-fa>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>
<script>
import API from '$supersetApi/projects/apricot/interface/index.js'
export default {
    name: 'apricot_Interface',
    data () {
        return {
            // tableData1: [{ desp: 'xx医院', code: '007' }, { desp: 'xx医院', code: '010' }],
            // tableData2: [{ hospitalSysName: 'xx系统', hospitalSysCoding: '007', id: '01' }, { hospitalSysName: 'xx系统', hospitalSysCoding: '010', id: '02' }],
            // tableData3: [{ param: 'xx参数', paramDesc: '007', id: '01' }, { param: 'xx参数', paramDesc: '010', id: '02' }],
            tableData1: [],
            tableData2: [],
            tableData3: [],
            selectedRow1: {},
            selectedRow2: {},
            loading1: false,
            loading2: false,
            loading3: false
        }
    },
    methods: {
        onRowClick1 (row) {
            this.selectedRow1 = row;
            this.getHospitalSysPage(row.code);
        },
        onRowClick2 (row) {
            let isNeedToGetData = true;
            if (this.selectedRow2.id === row.id) {
                isNeedToGetData = false
            }
            this.selectedRow2 = row;
            if (row.id) {
                isNeedToGetData && this.getHospitalSysParamPage(row.id);
            } else {
                this.tableData3 = [];
            }
        },
        handleEdit2 (index) {
            this.tableData2[index]['isEdit'] = true;
        },
        handleEdit3 (index) {
            this.tableData3[index]['isEdit'] = true;
        },
        // 添加行
        handleAddRow (suffix) {
            if (suffix == 2) {
                let row2 = {
                    hospitalCode: this.selectedRow1.code,
                    hospitalSysName: '',
                    hospitalSysCoding: '',
                    isEdit: true,
                };
                this.tableData2.unshift(row2);
                this.selectedRow2 = {};
                this.tableData3 = [];
                return
            }
            if (suffix == 3) {
                let row3 = {
                    hospitalSysId: this.selectedRow2.id,
                    param: '',
                    paramDesc: '',
                    isEdit: true,
                };
                this.tableData3.unshift(row3)
            }
        },
        handleSave2 (index, row) {
            if (!row.hospitalCode) {
                this.$message.warning('请选择医院！')
                return
            }
            if (!row.hospitalSysName) {
                this.$message.warning('请输入系统名称！')
                return
            }
            if (!row.hospitalSysCoding) {
                this.$message.warning('请输入系统编码！')
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在保存中，请稍等',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            if (row.id) {
                // 编辑
                let jsonData = Object.assign({}, row);
                delete jsonData.isEdit;
                API.editHospitalSys(jsonData).then(res => {
                    loading.close()
                    if (res.success) {
                        this.$message.success(res.msg);
                        if (res.data) {
                            this.tableData2[index] = res.data;
                        } else {
                            this.tableData2[index]['isEdit'] = false;
                        }
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(() => {
                    loading.close()
                })
                return
            }
            // 新增
            let jsonData = Object.assign({}, row);
            delete jsonData.isEdit;
            API.addHospitalSys(jsonData).then(res => {
                loading.close()
                if (res.success) {
                    this.$message.success(res.msg);
                    if (res.data) {
                        this.tableData2[index] = res.data;
                        this.selectedRow2 = res.data;
                    } else {
                        this.getHospitalSysPage(this.selectedRow1.code);
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                loading.close()
            })
        },
        handleSave3 (index, row) {
            if (!row.hospitalSysId) {
                this.$message.warning('请选择系统！')
                return
            }
            if (!row.param) {
                this.$message.warning('请输入参数名称！')
                return
            }
            if (!row.paramValue) {
                this.$message.warning('请输入参数值！')
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在保存中，请稍等',
                
                background: 'rgba(0, 0, 0, 0.2)'
            });
            if (row.id) {
                // 编辑
                let jsonData = Object.assign({}, row);
                delete jsonData.isEdit;
                API.editHospitalSysParam(jsonData).then(res => {
                    loading.close()
                    if (res.success) {
                        this.$message.success(res.msg)
                        if (res.data) {
                            this.tableData3[index] = res.data;
                        } else {
                            this.tableData3[index]['isEdit'] = false;
                        }
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(() => {
                    loading.close()
                })
                return
            }
            // 新增
            let jsonData = Object.assign({}, row);
            delete jsonData.isEdit;
            API.addHospitalSysParam(jsonData).then(res => {
                loading.close()
                if (res.success) {
                    this.$message.success(res.msg);
                    if (res.data) {
                        this.tableData3[index] = res.data;
                    } else {
                        this.getHospitalSysPage(this.selectedRow2.id);
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                loading.close()
            })
        },
        // 删除
        handleDelete2 (index, row) {
            if (!row.id) {
                this.tableData2.splice(index, 1);
                return
            }
            this.$confirm('确定删除选中数据吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                let loading = this.$loading({
                    lock: true,
                    text: '正在删除中，请稍等',
                    
                    background: 'rgba(0, 0, 0, 0.2)'
                });
                API.delHospitalSys({ id: row.id }).then(res => {
                    loading.close();
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.tableData2.splice(index, 1);
                        this.selectedRow2 = {};
                        this.tableData3 = [];
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(() => {
                    loading.close();
                })
            }).catch(() => { })
        },
        // 删除
        handleDelete3 (index, row) {
            if (!row.id) {
                this.tableData3.splice(index, 1);
                return
            }
            this.$confirm('确定删除选中数据吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                let loading = this.$loading({
                    lock: true,
                    text: '正在删除中，请稍等',
                    
                    background: 'rgba(0, 0, 0, 0.2)'
                });
                API.delHospitalSysParam({ id: row.id }).then(res => {
                    loading.close();
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.tableData3.splice(index, 1);
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(() => {
                    loading.close();
                })
            }).catch(() => { })
        },
        getHospitalList () {
            this.selectedRow1 = {};
            this.selectedRow2 = {};
            this.tableData1 = [];
            this.tableData2 = [];
            this.tableData3 = [];
            this.loading1 = true;
            API.getHospitalList().then(res => {
                this.loading1 = false;
                if (res.success) {
                    this.tableData1 = res.data || [];
                    if (this.tableData1.length) {
                        this.$nextTick(() => {
                            let temp = this.tableData1[0];
                            this.onRowClick1(temp);
                            this.$refs.table1.setCurrentRow(temp)
                        })
                        return
                    }
                    return
                }
                this.tableData1 = [];
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading1 = false;
            })
        },
        getHospitalSysPage (hospitalCode) {
            if (!hospitalCode) {
                this.$message.warning('请选择医院！')
                return
            }
            this.selectedRow2 = {};
            this.tableData2 = [];
            this.tableData3 = [];
            let jsonData = {
                condition: {
                    hospitalCode
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 100
                }
            }
            this.loading2 = true;
            API.getHospitalSysPage(jsonData).then(res => {
                this.loading2 = false;
                if (res.success) {
                    this.tableData2 = res.data.recordList || [];
                    if (this.tableData2.length) {
                        this.$nextTick(() => {
                            let temp = this.tableData2[0];
                            this.onRowClick2(temp);
                            this.$refs.table2.setCurrentRow(temp)
                        })
                        return
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading2 = false
            })
        },
        getHospitalSysParamPage (hospitalSysId) {
            if (!hospitalSysId) {
                this.$message.warning('请选择系统！')
                return
            }
            this.tableData3 = [];
            let jsonData = {
                condition: {
                    hospitalSysId
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 100
                }
            }
            this.loading3 = true;
            API.getHospitalSysParamPage(jsonData).then(res => {
                this.loading3 = false;
                if (res.success) {
                    this.tableData3 = res.data.recordList || [];
                    if (this.tableData3.length) {
                        this.$nextTick(() => {
                            let temp = this.tableData3[0];
                            this.$refs.table3.setCurrentRow(temp);
                        })
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading3 = false
            })
        },

    },
    created () {
        this.getHospitalList();
    }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.c-tamplte {
    height: 100%;
    .c-content {
        margin-right: 10px;
        height: auto;
        background-color: #fff;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
        &:last-child {
            margin-right: 0;
        }
        &.t-1 {
            flex: 2;
            max-width: 300px;
        }
        &.t-2 {
            flex: 3;
            max-width: 500px;
        }
        &.t-3 {
            flex: 4;
        }
        h3 {
            margin: 0;
            padding: 15px;
            border-bottom: 1px solid #eee;
            > i {
                float: right;
                cursor: pointer;
                color: var(--el-color-primary);
            }
        }
        .c-item {
            padding: 15px;
        }
        .icon-btn {
            position: relative;
            margin-left: 15px;
            justify-content: center;
            :deep(.el-icon) {
              position: absolute;
            }
        }
    }
}
</style>
