<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-button">
                <el-row :gutter="10">
                    <el-col :span="4">
                        <div class="m-labelInput">
                            <el-input v-model="condition.sOperationName"
                                clearable></el-input>
                            <label>操作名称</label>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="m-labelInput">
                            <el-input v-model="condition.sOperatorName"
                                clearable></el-input>
                            <label>操作人</label>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="m-labelInput">
                            <el-date-picker v-model="condition.dOperateTimeSt"
                                type="date">
                            </el-date-picker>
                            <label>开始时间</label>
                        </div>
                    </el-col>
                    <el-col :span="4">
                        <div class="m-labelInput">
                            <el-date-picker v-model="condition.dOperateTimeEd"
                                type="date"
                                @change="mxDoSearch()">
                            </el-date-picker>
                            <label>结束时间</label>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div>
                            <el-button-icon-fa _icon="fa fa-search"
                                type="primary"
                                size="small"
                                :loading="loading"
                                @click="mxDoSearch()">查询</el-button-icon-fa>
                            <el-button-icon-fa _icon="fa fa-shuaxin"
                                size="small"
                                @click="mxDoRefresh()">刷新</el-button-icon-fa>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table :data="tableData"
                    id="itemTable"
                    ref="mainTable"
                    size="small"
                    @row-click="onClickRow"
                    
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort"
                        >
                        <template v-slot="scope">
                            <template v-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate(true) }}
                            </template>
                            <template v-else-if="['sParamOut','sParamIn'].includes(scope.row[`${item.sProp}`])">
                                {{scope.row[`${item.sProp}`| filterString]}}
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="c-pagination scope-pagination-around">
            <el-pagination background @size-change="onSizeChange"
                @current-change="onCurrentChange"
                :current-page="page.pageCurrent"
                :page-sizes="mxPageSizes"
                :pager-count="5"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next"
                :total="page.total">
            </el-pagination>
        </div>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/system/systemLog.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SystemLog',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            loading: false,
            configTable: [{
                sProp: 'sOperationName',
                sLabel: '操作名称',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'dOperateTime',
                sLabel: '操作时间',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'sOperatorName',
                sLabel: '操作人',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'sOperateIp',
                sLabel: '操作IP',
                sAlign: 'left',
                sMinWidth: '100px',
                
            },
            {
                sProp: 'sOperatePath',
                sLabel: '操作路径',
                sAlign: 'left',
                sMinWidth: '180px',
                
            },
            {
                sProp: 'sParamOut',
                sLabel: '出参',
                sAlign: 'left',
                sMinWidth: '200px',
                
            },
            {
                sProp: 'sParamIn',
                sLabel: '入参',
                sAlign: 'left',
                sMinWidth: '200px',
                
            }
            ],
            condition: {}
        }
    }, 
    methods: {
      filterString (string) {
          return string.substring(0, 300)
      },
        // 获取表格数据
        getData (params) {
            const p = Object.assign({}, params);
            let dOperateTimeEd = p.condition.dOperateTimeEd;
            if(dOperateTimeEd) {
                p.condition.dOperateTimeEd =new Date(dOperateTimeEd.setTime(dOperateTimeEd.setHours(23, 59, 59, 999)))
            }
            Api.getData(p).then(res => {
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList;
                    this.page.total = res.data.countRow;
                    this.loading = false;
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        }
    },
    mounted () {
    },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 12px 0px 0px;
    :deep(.c-form) {
        display: flex;
        flex-direction: column;
        .c-form-button {
            padding: 0 10px 15px 15px;
            .m-labelInput {
                width: 100%;
                margin-bottom: 0;
                &:first-child {
                    margin-left: 0;
                }
            }
            .el-button {
                margin-top: 12px;
            }
        }
        .m-labelInput {
            width: calc(100% - 10px);
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;
            > button {
                margin-top: 13px;
            }
        }
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
}
</style>
