<template>
    <el-dialog
        title="宣教内容"
        :modelValue="dialogVisible"
        append-to-body
        class="t-default my-dialog"
        width="800px"
        :close-on-click-modal="false"
        @close="handleCloseDialog">
        <el-row style="margin-bottom: 15px;">
            <el-button-icon-fa icon="el-icon-refresh-right"
                class="btns"  
                @click="getTableData">刷 新</el-button-icon-fa>
            <el-upload v-if="rowData.iMediaPlay != 3"
                class="upload-demo"
                style="display: inline-block; margin-right: 10px"
                :action="rowData.iMediaPlay != 2 ? vedioSuffix : imageSuffix"
                :before-upload="beforeUpload"
                :http-request="customUpload"
                :on-change="handleProgress"
                :data="{sScreenId: rowData.sId}"
                multiple
                :show-file-list="false"
                :file-list="fileList">
                    <el-button-icon-fa type="primary" icon="el-icon-upload2" class="btns">上 传</el-button-icon-fa>
            </el-upload>
            <el-button-icon-fa v-else 
                type="primary" 
                icon="el-icon-circle-plus-outline"
                class="btns" 
                @click="onAddRowText">添 加</el-button-icon-fa>
        </el-row>
        <!-- tableData.filter(item =>item.iMediaType === rowData.iMediaPlay) -->
        <div>
            <el-table v-loading="isLoad"
                :data="tableData"  
                :row-class-name="rowClassName" 
                border 
                height="50vh" 
                style="width: 100%;"
                @row-click="rowClick">
                <el-table-column label="序号" type="index" width="60px" align="center">
                </el-table-column>
                <el-table-column v-if="rowData.iMediaPlay!=3" prop="sFileName" label="文件名" min-width="100" show-overflow-tooltip>
                </el-table-column>
                <el-table-column v-if="rowData.iMediaPlay!=3" prop="sPath" label="文件路径" min-width="100" show-overflow-tooltip>
                </el-table-column>
                <el-table-column v-if="rowData.iMediaPlay==3" prop="sFileName" label="文字描述" min-width="100">
                    <template v-slot="{row, $index}">
                        <el-input v-if="row.isEdit" type="textarea" v-model="row.sFileName"></el-input>
                        <span v-else> {{row.sFileName}}</span>
                    </template>
                </el-table-column>
                <el-table-column label="启用" align="center" width="80">
                    <template v-slot="{row, $index}">
                        <el-switch v-model="row.iIsEnable" :active-value="1" :inactive-value="0"
                        @click.stop.native="onChangeEnable($event, row, $index)"></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" width="230">
                    <template v-slot="{row, $index}">
                        <el-button-icon-fa v-if="rowData.iMediaPlay == 3 && row.isEdit"
                            icon="el-icon-finished"
                            type="primary"
                            link
                            @click.stop="onSaveText(row, $index)">
                            保存
                        </el-button-icon-fa>
                        <el-button-icon-fa v-if="rowData.iMediaPlay == 3 && !row.isEdit" 
                            icon="el-icon-edit"
                            link
                            @click.stop="onEditText(row, $index)">
                            编辑
                        </el-button-icon-fa>
                        <el-divider v-if="rowData.iMediaPlay==3" direction="vertical"></el-divider>

                        <el-button-icon-fa icon="el-icon-delete"
                            link
                            @click.stop="handleDel(row, $index)">
                            删除
                        </el-button-icon-fa>
                        <el-divider direction="vertical"></el-divider>
                        <el-popover
                            v-model:visible="row.isShow"
                            placement="left"
                            title="同步至选中屏幕"
                            trigger="click"
                            width="500">
                            <div style="padding: 5px 20px;">
                                <div class="i-box">
                                    <el-checkbox-group v-model="selectedScreen" class="i-checkbox">
                                        <el-checkbox  v-for="(item, index) in screenData" :key="index" :label="item.sId" >
                                            {{ item.sName }} 
                                            <span style="color: #8492a6;padding-left: 10px">{{ oDistrictsNames[item.sDistrictId] }}</span>
                                        </el-checkbox>
                                    </el-checkbox-group>
                                </div>
                                <div class="text-right">
                                    <el-button v-loading="loading" type="primary" 
                                        style="margin-left:10px;"
                                        @click="syncFileClick(row, $index)">
                                        <template #icon>
                                            <i class="el-icon-document-checked"> </i>
                                        </template>
                                        确 认
                                    </el-button>
                                    <el-button
                                        style="margin-left:10px;"
                                        @click="tableData[$index].isShow=false">
                                        <template #icon>
                                            <i class="el-icon-close"> </i>
                                        </template>
                                        取 消
                                    </el-button>
                                </div>
                            </div>
                            <template #reference>
                                <el-button-icon-fa :disabled="!row.sId"
                                    icon="el-icon-refresh"
                                    link
                                    @click.stop="showSyncFilePop(row, $index)">
                                    同步
                                </el-button-icon-fa>
                            </template>
                        </el-popover>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template #footer>
            <el-button-icon-fa @click="handleCloseDialog" icon="el-icon-close">关 闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import Api from '$supersetApi/projects/apricot/system/screenConfig.js'

export default {
    name: 'VideoUpload',
    props: {
        rowData: {
            type: Object,
            default: () => ({})
        },
        oDistrictsNames:{
            type: Object,
            default: () => ({})
        },
        dialogVisible: {
            type:Boolean,
            default: false
        }
    },
    emits: ['closeDialog'],
    data () {
        return {
            loading: false,
            form: {},
            condition: {},
            tableData: [],
            selectedItem: {},
            isLoad: false,
            isSave: false,
            fileList: [],
            screenData:[],
            imageInfo: {
                len: 0,
                currentIdx: 0,
                upload: false,
            },
            selectedScreen: [],
            vedioSuffix: '.mp4,.webm,.ogg',
            imageSuffix: '.gif,.jpg,.jpeg,.png,.GIF,.JPG,.JPEG,.PNG'
        }
    },
     watch: {
        dialogVisible(val) {
            if(val) {
                this.tableData = [];
                this.getTableData()
                this.getAllScreen()
            }
        }
        
    },
    methods: {
        onAddRowText() {
            let data = {
                sScreenId: this.rowData.sId,
                sFileName: undefined,
                isEdit: true,
                iIsEnable: 1
            };
            this.tableData.push(data)
        },
        onSaveText (row) {
            var jsonData = Object.assign({}, row);
            jsonData.text = row.sFileName;
            jsonData.sMediaId = row.sId;
            delete jsonData.isEdit;
            let loading = this.$loading({
                lock: true,
                text: '保存中...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            Api.saveText(jsonData).then( res=>{
                loading.close()
                if(res.success) {
                    this.$message.success(res.data)
                    row['isEdit'] = false;
                    this.getTableData()
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{
                loading.close();
                console.log(err)
            })
        },
        onEditText(row) {
            row['isEdit'] = true
        },
         // 给给每行数据添加index属性
        rowClassName ({
            row,
            rowIndex
        }) {
            row.index = rowIndex;
        },
        handleClose () {
            this.selectedScreen = [];
            this.$emit('closeDialog');
        },
        rowClick (row) {
            this.selectedItem = row;
        },
        beforeUpload (file) {
            var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1);
            let targetList = this.rowData.iMediaPlay != 2 ? ['mp4', 'webm', 'ogg'] : ['gif', 'jpg', 'jpeg', 'png', 'GIF', 'JPG', 'JPEG', 'PNG'];
            let isTrue = targetList.includes(testmsg);
            if (!isTrue) {
                this.$message({
                    message: this.rowData.iMediaPlay != 2 ? '上传文件仅支持mp4、webm、ogg格式！' :
                        '上传文件仅支持gif、jpg、jpeg、png格式！',
                    type: 'warning'

                });
            }
            return isTrue
        },
        // // 删除
        handleDel (row, index) {
            if(!row.sId) {
                this.tableData.splice(index, 1);
                return
            }
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                let jsonData = {
                    sId: row.sId
                }
                Api.delVideo(jsonData).then( res=>{
                    if(res.success) {
                        this.$message.success(res.msg)
                        this.getTableData()
                        return
                    }
                    this.$message.error(res.msg)
                })
            }).catch( ()=>{
                console.log(err)
            })
        },
        // 获取播放列表
        getTableData() {
            let jsonData = {
                sScreenId: this.rowData.sId,
                sScreenIp: this.rowData.sIp
            };
            this.isLoad = true;
            Api.getVideoList(jsonData).then(res =>{
                this.isLoad = false;
                if(res.success) {
                    this.tableData = res.data ? (res.data.callScreenVideoList || []) : [];
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch( ()=>{
                this.isLoad = false;
                console.log(err)
            } )

        },
        // 上传视频
        customUpload(file) {
            let loading = this.$loading({
                lock: true,
                text: '上传中...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            let FormDatas = new FormData();
            FormDatas.append('sScreenId', this.rowData.sId);
            FormDatas.append('sScreenIp', this.rowData.sId);
            FormDatas.append('iMediaType', this.rowData.iMediaPlay);
            FormDatas.append('file', file.file);
            Api.upLoadVideo(FormDatas).then( res=>{
                loading.close();
                if (res.success) {
                    this.$message.success(res.data)
                    this.getTableData();
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch( ()=>{
                loading.close();
            })
        },
        // 打开popover组件
        showSyncFilePop (row, index) {
            if (row.sId != this.selectedItem.sId) {
                this.selectedScreen = [];
                if (this.selectedItem.sId) this.tableData[this.selectedItem.index]['isShow'] = false;
            }
            this.selectedItem = this.tableData[index];
        },
        // 同步
        syncFileClick(row, index) {
            if (!this.selectedScreen.length) {
                this.$message({
                    message: '请选择需要同步的屏幕',
                    type: 'warning',
                    duration: 3000
                });
                return
            }
            let jsonData = {
                sId: row.sId,
                sScreenIds: this.selectedScreen.join(',')
            }
            this.loading = true;
            Api.asyncVideo(jsonData).then(res =>{
                this.loading = false;
                if(res.success) {
                    this.$message.success(res.msg)
                    row.isShow = false;
                    this.selectedScreen = [];
                    return
                }

            }).catch( ()=>{
                this.loading = false;
            })
        },
        handleProgress() {

        },
        // 启用
        onChangeEnable(val, row, index) {
            let jsonData = {
                sId: row.sId,
                iIsEnable: row.iIsEnable,
            }
            Api.saveOnOrOff(jsonData).then( res=>{
                if(res.success) {
                    this.$message.success(res.msg)
                    return
                }
                this.tableData[index]['iIsEnable'] = !val;
            }).catch( ()=>{
                this.tableData[index]['iIsEnable'] = !val;
                console.log(err)
            })
        },

        // 关闭弹窗
        handleCloseDialog () {
            this.$emit('closeDialog');
            // this.activeCarouselItem = 'carouselItem1';
            // this.$refs.carousel1 && this.$refs.carousel1.setActiveItem(this.activeCarouselItem);
            // this.isSpellShow = false; 
            // this.patientNameHistoryData = [];
        },
        // 获取所有屏幕
        getAllScreen() {
            let params = {
                sDistrictId: '',
            }
            Api.getScreenByDistricts(params).then( res=>{
                if(res.success) {
                    this.screenData = res.data || [];
                }
            }).catch( ()=>{
                console.log(err)
            })
        },
    },
}
</script>
<style lang="scss" scoped>
:deep(.el-table__body-wrapper) {
    height: 100% !important;
}
.i-box {
    min-height: 100px;
    max-height:500px;
    overflow: auto;
    margin-bottom:10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}


:deep(.el-table td) {
    padding: 1px 0;
}

.i-checkbox {
    display: flex;
    flex-direction: column;
}

.btns {
    margin-right: 10px;
}
</style>
