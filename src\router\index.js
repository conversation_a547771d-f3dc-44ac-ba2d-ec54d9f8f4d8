
import { createRouter, createWebHashHistory } from 'vue-router';


import Login from '$supersetViews/login/Index.vue'
import Main from '$supersetViews/Main.vue'
import Error_401 from '$supersetViews/Error_401.vue'
import NewImagePage from '$supersetViews/apricot/components/NewImagePage.vue'

import moduleList from './bridge.js'
import { getDefaultModuleRouterName } from '$supersetResource/js/common.js'

/*------------------------------路由引入*/
/*方法-----------------------------------------*/
import { getAccessToken, removeAccessToken } from '@/utils/accessToken';
import { setLoginCertId } from '$supersetUtils/auth'

//前端初始化的方法，包括权限获取、路由挂载等
import init from '$supersetUtils/init'
import store from '$supersetStore'

import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
NProgress.configure({ ease: 'linear', speed: 1000, showSpinner: false });

export const constantRoutes = [
  {
    path: '/',
    name: 'root',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/main',
    name: 'Main',
    component: Main,
    children: moduleList
  },
  {
    path: '/newImagePage',
    name: 'NewImagePage',
    component: NewImagePage,
  },
  {
    path: '/error_401',
    name: 'Error_401',
    component: Error_401,
  },
  // {
  //   path: '/login',
  //   name: 'Login',
  //   component: () => import('@/views/login/Index.vue'),
  //   meta: {
  //     title: '登录',
  //   },
  //   hidden: true,
  // },
  // {
  //   path: '/401',
  //   name: '401',
  //   component: () => import('@/views/Error_401.vue'),
  //   hidden: true,
  // },
  // {
  //   path: '/error',
  //   component: Layout,
  //   name: 'Error',
  //   meta: { title: '404页面', icon: 'icon-shouye' },
  //   hidden: true,
  //   children: [
  //     {
  //       path: '/404',
  //       name: '404',
  //       component: () => import('@/views/errorPage/404.vue'),
  //       hidden: true,
  //     }
  //   ]
  // }
  { path: '/:pathMatch(.*)*', name: 'NotFound', redirect: '/main/welcome_index', hidden: true }

];
 

const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
});

let firstVisitRouter = true;

router.beforeEach((to, from, next) => {
  // NProgress.start();
  if (to.name == "NewImagePage" && opener) {  // 图片预览页免鉴权
    next()
    firstVisitRouter = false;
    return
  }
  if (to.query.sso) {
    // 单点登录处理
    store.commit({ type: 'user/setAccessToken', accessToken: to.query.sessionId, expires: new Date(Number(to.query.expireTime)) })

    if(to.query.ukeyCertId) {
        setLoginCertId(to.query.ukeyCertId);
    }
    
    clearLocationHrefQuery();
    
    router.push({ path: '/main/welcome_jump', query: { redirect: 'auto' } })

    firstVisitRouter = false;

    return
  }
  
  const LOGIN_PAGE_NAME = 'Login';
  if(firstVisitRouter) {
    firstVisitRouter = false;
    // 当关闭过浏览器或标签页时（sessionStorage），重新登录
    let origin = window.location.origin;
    if(!sessionStorage[origin + '_' + 'username']){
        removeAccessToken()
        next({ name: LOGIN_PAGE_NAME}) // 跳转到登录页
        return
    }
  }

  /*所有的路由跳转都要经此判断*/
  let token = getAccessToken();
  if (token) {
    if (to.name === LOGIN_PAGE_NAME) {
      // 已登录且要跳转的页面是登录页
      next({ name: getDefaultModuleRouterName() })
      return 
      // if (from.name == 'Error_401' || from.name == null) {
      //   next({ name: 'welcome_Index' })
      // } else {
      //   next({ name: from.name })
      // }
    }

    let isInited = store.getters['user/isInited']
    //如果已经挂载路由 
    if (isInited) {
      next()
    } else {
      //未进行路由挂载，则进行初始化
      init().then(function () {
        // console.log('inited in routerBefore')
        if (to.matched.length) {

          if (to.name != 'Error_401') {
            //设置合法路由名称，使得出错时返回
            store.commit({
              type: 'user/setValidRouterName',
              validRouterName: to.name
            })
          }
    
          next()
    
        } else {
          next({ name: 'welcome_Index' })
        }
      })
    }
    /*debugger*/
    
  } else if (to.name !== LOGIN_PAGE_NAME) {
    // 未登录且要跳转的页面不是登录页
    // 转至登录页
    next({
      name: LOGIN_PAGE_NAME // 跳转到登录页
    })
  } else if (to.name === LOGIN_PAGE_NAME) {
    // 未登陆且要跳转的页面是登录页
    // 允许路由正常跳转到登录页
    next() // 跳转
  }
})

router.afterEach(() => {
  // NProgress.done()
})
router.onError(function (err) {
  /* debugger */
  console.log('router.onError:', err)
})


export default router;
// reset router
export function resetRouter() {
  router.getRoutes().forEach((route) => {
    const { name } = route;
    if (name) {
      router.hasRoute(name) && router.removeRoute(name);
    }
  });
}

function clearLocationHrefQuery() {
    setTimeout(() => {
        var url = window.location.href; //获取当前页面的url
        url = url.replace(/(\?)[^'"]*/, '');  //去除参数
        window.history.pushState({}, 0, url);
    }, 300)
}

