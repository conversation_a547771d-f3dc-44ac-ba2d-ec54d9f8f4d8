import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'

// 综合模块操作接口查询: Research Controller
// 分页数据
export function researchPage(data) {
    return request({
        url: baseURL.apricot + '/research/controller/page',
        method: 'POST',
        data
    })
}

// 获取申请科室
export function getApplyDept(data) {
    return request({
        url: baseURL.apricot + '/research/controller/getApplyDept',
        method: 'POST',
        data
    })
}
// 获取申请医生
export function getApplyDr(data) {
    return request({
        url: baseURL.apricot + '/research/controller/getApplyDr',
        method: 'POST',
        data
    })
}