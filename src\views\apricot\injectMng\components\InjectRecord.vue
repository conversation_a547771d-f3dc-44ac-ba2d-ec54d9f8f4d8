<template>
    <el-scrollbar class="my-scrollbar" v-if="isVisible" v-loading="loading">
        <el-empty
            v-if="!dataList.length"
            :image-size="80"
            description="暂无记录"
            style="height: 100%" />
        <el-card
            v-else
            class="box-card"
            shadow="never"
            v-for="(item, index) in dataList"
            :key="index"
            :class="{ 'c-active': selectedId === item.sId }">
            <div class="text-right p-2 i-title">
                <span class="float-left i-left" style="hei">{{ `第 ${index + 1} 条` }}</span>
                <el-button-icon-fa
                    type="primary"
                    size="small"
                    icon="el-icon-edit"
                    @click="onEditClick(item, index)">编辑</el-button-icon-fa>
                <el-button-icon-fa
                    size="small"
                    icon="el-icon-delete"
                    @click="onDeleteClick(item, index)">删除</el-button-icon-fa>
            </div>
            <TextList
                :list="textList"
                :data="item"
                :configBtn="false"
                :iModuleId="iModuleId"
                :storageKey="storageKeyList[textDeviceType]"
                labelWidth="120px">
                <template #dInjectionSta="{ row, style }">
                    <span :style="style" :title="mxFormatterTime(item[row.prop])">
                        {{ mxFormatterTime(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                <template #dInjectionTime="{ row, style }">
                    <span :style="style" :title="mxFormatterTime(item[row.prop])">
                        {{ mxFormatterTime(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                <template #dInjectionEnd="{ row, style }">
                    <span :style="style" :title="mxFormatterTime(item[row.prop])">
                        {{ mxFormatterTime(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                <template #dInjectDate="{ row, style }">
                    <span :style="style" :title="mxToDate(item[row.prop])">
                        {{ mxToDate(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                <template #dTableDetectTime="{ row, style }">
                    <span :style="style" :title="mxFormatterTime(item[row.prop])">
                        {{ mxFormatterTime(item[row.prop]) || '（空）' }}
                    </span>
                </template>
                <template #fFullNeedle="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + item['sFullUnitText']
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #fFactDose="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + item['sFullUnitText']
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #fEmptyNeedle="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + item['sFullUnitText']
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #fRecipeDose="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + item['sRecipeDoseUnit']
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #fBloodSugar="{ row, style }">
                    <span :style="style" :title="item[row.prop]">
                        {{
                            (item[row.prop] ?? false) && item[row.prop] >= 0
                                ? item[row.prop] + 'mmol/L'
                                : '（空）'
                        }}
                    </span>
                </template>
                <template #sDrugDeliveryCode="{ style }">
                    <span :style="style" :title="item['sDrugDeliveryText']">
                        {{ item['sDrugDeliveryText'] || '（空）' }}
                    </span>
                </template>
                <template #sInjectionPosition="{ style }">
                    <span :style="style" :title="item['sInjectionPositionText']">
                        {{ item['sInjectionPositionText'] || '（空）' }}
                    </span>
                </template>
                <template #sAft3DrinkTypeCode="{ style }">
                    <span :style="style" :title="item['sAft3DrinkTypeText']">
                        {{ item['sAft3DrinkTypeText'] || '（空）' }}
                    </span>
                </template>
                <template #sAft6DrinkTypeCode="{ style }">
                    <span :style="style" :title="item['sAft6DrinkTypeText']">
                        {{ item['sAft6DrinkTypeText'] || '（空）' }}
                    </span>
                </template>
                <template #sBefDrinkTypeCode="{ style }">
                    <span :style="style" :title="item['sBefDrinkTypeText']">
                        {{ item['sBefDrinkTypeText'] || '（空）' }}
                    </span>
                </template>
                <template #sNurseId="{ style }">
                    <span :style="style" :title="item['sNurseName']">
                        {{ item['sNurseName'] || '（空）' }}
                    </span>
                </template>
                <template #sMedicineSource="{ style }">
                    <span :style="style" :title="item['sMedicineSourceText']">
                        {{ item['sMedicineSourceText'] || '（空）' }}
                    </span>
                </template>
            </TextList>
        </el-card>
    </el-scrollbar>
</template>

<script setup>
    import { InjectList } from '../config';
    import { mxToDate, mxFormatterDate, mxFormatterTime } from '../config/famaterData';
    import {
        findInjectionListByPatientId,
        delInject,
    } from '$supersetApi/projects/apricot/injectMng/inject.js';
    import { ElMessage, ElMessageBox } from 'element-plus';

    const emits = defineEmits(['onToRecordTab']);

    const props = defineProps({ lastFormTabName: String });

    const { proxy } = getCurrentInstance();
    // 患者信息
    const patientInfo = inject('patientInfo', ref({}));
    // 模块ID
    const iModuleId = inject('iModuleId');

    const updateInjectRowData = inject('updateInjectRowData')

    // 枚举类
    const storageKeyList = ref({
        PET: 'PETInjectFormList',
        ECT: 'ECTInjectFormList',
        Others: 'OthersInjectFormList',
    });
    // 枚举类
    const eNumDeviceType = ref({
        '001': 'PET',
        '002': 'ECT',
        '003': 'PET',
    });

    // 文本组件配置
    var textList = reactive(InjectList);
    // 设备类型编码
    var sDeviceTypeCode = computed(() => patientInfo?.value?.sDeviceTypeCode);
    // 是否显示
    var isVisible = ref(true);
    // 设备类型
    var textDeviceType = ref(null);
    // 患者sId
    var sPatientId = computed(() => patientInfo?.value?.sId);
    // 记录数据
    const dataList = ref([]);
    // 加载状态
    var loading = ref(false);

    const selectedId = ref('');

    // 监听设备类型编码
    watch(sDeviceTypeCode, (val) => {
        textDeviceType.value = eNumDeviceType.value[val] || 'Others';
        isVisible.value = false;
        // 待处理，清除定时器
        setTimeout(() => {
            isVisible.value = true;
        }, 500);
    });
    // 监听患者ID
    watch(sPatientId, (val, oldVal) => {
        if (!val) {
            dataList.value = [];
            selectedId.value = [];
            return
        }
        if (val && val !== oldVal) {
            selectedId.value = '';
            getRecordData(val);
        }
    });

    // 删除
    const onDeleteClick = (item, index) => {
        ElMessageBox.confirm('确认删除该条注射记录，是否继续？', '提示', {
            type: 'warning',
        }).then(() => {
            // 删除注射记录
            delInject({
                iVersion: item.iVersion,
                sId: item.sId,
                sPatientId: sPatientId.value,
            }).then((res) => {
                if (res.success) {
                    ElMessage.success(res.msg);
                    dataList.value.splice(index, 1);
                    if (props.lastFormTabName === 'InjectForm') {
                        proxy.$eventbus.emit('injectInfoFormInit', item);
                    } else {
                        proxy.$eventbus.emit('injectActivityMeterInit', item);
                    }
                    updateInjectRowData()
                    return;
                }
                ElMessage.error(res.msg);
            });
        });
    };

    // 编辑
    const onEditClick = (item, index) => {
        selectedId.value = item.sId;
        if (props.lastFormTabName === 'InjectForm') {
            proxy.$eventbus.emit('injectInfoFormEdit', item);
        } else {
            proxy.$eventbus.emit('injectActivityMeterEdit', item);
        }
    };

    // 获取注射记录
    const getRecordData = (sPatientId) => {
        const params = {
            sPatientId,
        };
        loading.value = true;
        findInjectionListByPatientId(params)
            .then((res) => {
                loading.value = false;
                if (res.success) {
                    dataList.value = res?.data || [];
                    dataList.value.map(item => {
                        if(item.fError || item.fError === 0) {
                            item.fError = (item.fError * 100) + '%';
                        }
                    })
                    if(!selectedId.value) {
                        selectedId.value = dataList.value[0].sId;
                    }
                    return;
                }
                ElMessage.error(res.msg);
                dataList.value = [];
            })
            .catch((err) => {
                dataList.value = [];
                loading.value = false;
            });
    };

    onMounted(() => {
        proxy.$eventbus.on('onRefreshInjectRecordCard', async (res) => {
            if (res) {
                selectedId.value = res?.sId;
                console.log();
                getRecordData(sPatientId.value);
                emits('onToRecordTab');
            }
        });
    });
    onUnmounted(() => {
        proxy.$eventbus.off('onRefreshInjectRecordCard');
    });

    // 暴露组件属性或方法可供父组件使用
    defineExpose({
        dataList,
    });
</script>

<style scoped lang="scss">
    .el-card {
        margin-top: 15px;
        &:first-child {
            margin-top: 10px;
        }
        &.c-active {
            border: 1px solid var(--el-color-primary);
        }
        :deep(.container .grid-box) {
            border: none;
        }
        :deep(.el-card__body) {
            padding: 0;
        }
        .i-title {
            padding-left: 15px;
            padding-right: 15px;
            border-bottom: 1px solid #eee;
        }
        .i-left {
            line-height: 24px;
        }
    }
    :deep(.container .grid-box .cell-box) {
        align-items: center;
    }
</style>
