<template>
    <el-button v-bind="$attrs" :disabled="!patientInfo.sId" 
        :class="{
            'm-vertical-btn t2': isReportModule && buttonMsg.icon,
            'margin_l': isReportModule && !buttonMsg.icon,
            'm-vertical-text': isReportModule && buttonMsg.isFold,
            't-border': isReportModule && buttonMsg.isBorder
        }"
        @click="onShowClick">
        <svg class="fa" aria-hidden="true" v-if="isReportModule && buttonMsg.icon">
            <use :xlink:href="'#' + buttonMsg.icon"></use>
        </svg>
        <template v-if="!isReportModule" #icon><el-icon><Tickets/></el-icon></template>
        <label>{{ isReportModule ? buttonMsg.name :buttonName }}</label>

        <!-- 抽屉 -->
        <el-drawer title="电子申请单" :model-value="visible" :with-header="false" append-to-body destroy-on-close direction="ltr"
            size="850px" @close="closeDialog">
            <div class="m-flexLaout-ty">
                <h4>
                    <strong>电子申请单</strong>
                    <el-button class="i-btn"
                        type="primary"
                        link
                        @click="closeDialog">
                        <i class="el-icon-circle-close"
                            style="font-size: 28px;"></i>
                    </el-button>
                </h4>
                <ApplyDetail :info="info"></ApplyDetail>
            </div>
        </el-drawer>

    </el-button> 
</template>
<script>
import ApplyDetail from '$supersetViews/apricot/components/ApplyDetail.vue';
import { Tickets } from '@element-plus/icons-vue';
import { getPatientInfo } from '$supersetApi/projects/apricot/common'
export default {
    name: 'ApplyListInfo',
    components: {
        Tickets,
        ApplyDetail
    },
    props: {
        isReportModule: {
            type: Boolean,
            default: false
        },
        buttonMsg: {
            type: Object,
            default: () => ({})
        },
        buttonName: {
            type: String,
            default: ''
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        },
    },
    data () {
        return {
            visible: false,
            info: {}
        }
    },
    methods: {
        async onShowClick () {
            // if (!this.patientInfo.sApplyNO) {
            //     this.$message.warning('没有申请单信息');
            //     return
            // }
            await this.getData();
            this.visible = true;
        },
        closeDialog () {
            this.visible = false;
        },
        async getData () {
            let params = {
                // applyNo: this.patientInfo.sApplyNO,
                // patientName: this.patientInfo.sName,
                // execState: 2
                sId: this.patientInfo.sId
            }
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            await getPatientInfo(params).then(res => {
                loading.close();
                if (res.success) {
                    this.info = res.data || {}
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close();
                console.log(err)
            });
        }
    },
}
</script>
<style lang="scss" scoped>
h4 {
    position: relative;
    margin: 0 30px 0 0;
    padding: 8px;
    text-align: center;
    // border-bottom: 1px solid #eee;
    .i-btn {
        padding: 0;
        position: absolute;
        top: 50%;
        transform:translateY(-50%);
        right: -30px;
    }
}
.margin_l {
  margin-left: 10px;
}
</style>
