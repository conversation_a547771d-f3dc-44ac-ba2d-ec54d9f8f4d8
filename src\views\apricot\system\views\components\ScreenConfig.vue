<template>
  <div class="c-container">
    <div class="c-flex-context">
      <div class="c-flex">
        <div class="flex justify-between mb-4 ">
          <div class="w-1/2">
            <el-button-icon-fa type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button-icon-fa>
            <el-button-icon-fa plain type="primary" icon="el-icon-refresh" @click="getScreenData">刷新</el-button-icon-fa>
          </div>
          <div class="flex justify-end w-1/2">
            <div class="w-1/3 mr-2">
              <el-input v-model="searchForm.sScreenName" clearable placeholder="名称"
                @keyup.enter.native="getScreenData"></el-input>
            </div>
            <div class="w-1/3">
              <el-select v-model="searchForm.sDistrictId" clearable style="width:100%" placeholder="院区"
                @change="getScreenData">
                <el-option v-for="(item, index) in sDistrictIdData" v-show="item.iIsEnable" :key="index" :label="item.sHospitalDistrictName"
                  :value="item.sId">
                </el-option>
              </el-select>
            </div>
            <div class="ml-2">
              <el-button-icon-fa type="primary" icon="el-icon-search" @click="getScreenData"></el-button-icon-fa>
            </div>
          </div>
        </div>
      </div>
      <el-table :data="tableData" id="itemTable" ref="mainTable" size="small" border stripe height="100%"
        style="width: 100%" v-loading="loading">
        <el-table-column v-for="item in tableColumnConfig" show-overflow-tooltip :key="item.index" :prop="item.sProp"
          :label="item.sLabel" :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth" :min-width="item.sMinWidth"
          :sortable="!!item.iSort">
          <template v-slot="scope">
            <template v-if="item.sProp == 'sDistrictId'">
              {{ (sDistrictIdData.find(district => district.sId == scope.row.sDistrictId) || {}).sHospitalDistrictName }}
            </template>
            <template v-else-if="['iIsEnable', 'iMediaMute', 'iHideName'].includes(item.sProp)">
              {{ (booleanArray.find(j => j.sValue == scope.row[item.sProp]) || {}).sLabel }}
            </template>
            <template v-else-if="['iIsConnected'].includes(item.sProp)">
              <span :class="scope.row.iIsConnected == 1?'on-line': 'iline'">{{scope.row.iIsConnected == 1 ? '在线' : '离线' }}</span>
            </template>
            <template v-else-if="['sIp'].includes(item.sProp)">
              <el-button size="small" link class="copy-btn icon" data-clipboard-action="copy"
                :data-clipboard-text="url + '/app/call/screen.html?ip=' + scope.row.sIp" title="复制呼叫地址"
                @click="copyCallUrl()"><i class="fa fa-copy"></i></el-button>
                {{ scope.row[`${item.sProp}`] }}
            </template>
            <template v-else>
              {{ scope.row[`${item.sProp}`] }}
            </template>
          </template>
        </el-table-column>
        <el-table-column width="210" label="操作" align="center">
          <template v-slot="scope">
            <el-button size="small" link type="primary" @click="handleEdit(scope.row)">
              编辑
              <template #icon>
                <Icon name="el-icon-edit" color="">
                </Icon>
              </template>
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button size="small" link class @click="handleDelData(scope.row)">
              删除
              <template #icon>
                <Icon name="el-icon-delete" color="">
                </Icon>
              </template>
            </el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button size="small" link @click="openVideo(scope.row)">
              媒体
              <template #icon>
                <Icon name="el-icon-setting" color="">
                </Icon>
              </template>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" append-to-body top="5vh" class="t-default i-height-70" width="700"
      :close-on-click-modal="false" @close="closeDialog">
      <el-form :model="editLayer.form" ref="refEditLayer" label-width="100px" :rules="rules">
        <el-divider>基本信息</el-divider>
        <el-form-item prop="" label="启用">
          <el-switch v-model="editLayer.form.iIsEnable" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
        <el-form-item prop="sDistrictId" label="院区">
          <el-select v-model="editLayer.form.sDistrictId" clearable style="width:100%">
            <el-option v-for="(item, index) in sDistrictIdData" v-show="item.iIsEnable" :key="index" :label="item.sHospitalDistrictName"
              :value="item.sId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="editLayer.form.sId" prop="" label="工作站">
          {{ editLayer.form.workStation }}
        </el-form-item>
        <el-form-item prop="sName" label="名称">
          <el-input v-model="editLayer.form.sName" clearable></el-input>
        </el-form-item>
        <el-form-item prop="sIp" label="IP">
          <el-input v-model="editLayer.form.sIp" clearable></el-input>
        </el-form-item>

        <el-form-item prop="" label="全屏静音">
          <el-select v-model="editLayer.form.iMediaMute" style="width: 100%">
            <el-option v-for="(item, index) in booleanArray" :key="index" :label="item.sLabel"
              :value="item.sValue"></el-option>
          </el-select>
        </el-form-item>
        <div class="line"></div>
        <el-divider>患者列表</el-divider>
        <el-form-item prop="" label="匿名显示">
          <el-select v-model="editLayer.form.iHideName" style="width: 100%">
            <el-option v-for="item in booleanArray" :key="item.sValue" :label="item.sLabel"
              :value="item.sValue"></el-option>
          </el-select>

        </el-form-item>
        <el-form-item prop="" label="字体大小">
          <el-select v-model="editLayer.form.sListFontSize" filterable allow-create clearable style="width: 100%">
            <el-option v-for="(item, index) in fontSizes" :key="item.index" :label="item.label" :value="item.value">
              <span style="float: left">{{ item.label }}px</span>
              <span style="float: right; color: #8492a6;" :style="{ fontSize: item.label + 'px' }">{{ item.tips
              }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="" label="列表行高">
          <el-select v-model="editLayer.form.sRowHeight" filterable allow-create clearable style="width: 100%">
            <el-option v-for="(item, index) in  lineHeights" :key="item.index" :label="item.label" :value="item.value">
              <span>{{ item.label }}px</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="" label="列表宽度">
          <el-slider :min="0" :max="100" :marks="marks" v-model="editLayer.form.iPercent"></el-slider>
        </el-form-item>
        <el-form-item prop="" label="患者个数">
          <el-select v-model="editLayer.form.iRowsNum" filterable allow-create clearable style="width: 100%">
            <el-option v-for="(item, index) in patientNum" :key="item.index" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="" label="刷新间隔">
          <el-select v-model="editLayer.form.iRefreshTime" filterable allow-create clearable style="width: 100%">
            <el-option v-for="(item, index) in refreshArray" :key="item.index" :label="item.label" :value="item.value">
              <span>{{ item.label }}S</span>
            </el-option>
          </el-select>
        </el-form-item>

        <div class="line"></div>
        <!--  #宣教内容-->
        <el-divider>宣教内容</el-divider>
        <el-form-item prop="" label="显示">
          <el-select v-model="editLayer.form.iMediaShow" style="width: 100%">
            <el-option v-for="item in booleanArray" :key="item.sValue" :label="item.sLabel"
              :value="item.sValue"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="" label="媒体">
          <el-select v-model="editLayer.form.iMediaPlay" style="width: 100%">
            <el-option label="视频" :value="1"></el-option>
            <el-option label="图片" :value="2"></el-option>
            <el-option label="文字" :value="3"></el-option>
          </el-select>
          <!-- <el-button :disabled="!editLayer.form.sId" link @click="openVideo(editLayer.form)" style="padding: 0;margin-left: 10px;"> 
            <i class="el-icon-setting" style="font-size:24px; margin-top:0"></i>
          </el-button> -->
        </el-form-item>
        <el-form-item prop="" label="宣教宽度">
          <el-slider :min="0" :max="100" :step="1" :marks="marks" v-model="editLayer.form.iMediaPercent"></el-slider>
        </el-form-item>

        <el-form-item prop="" label="视频音量">
          <el-slider :min="0" :max="100" :step="5" :marks="marks" v-model="editLayer.form.iMediaVolume"></el-slider>
        </el-form-item>
        <div class="line"></div>

        <!-- #滚动文字 -->
        <el-divider>滚动文字</el-divider>
        <el-form-item prop="" label="显示">
          <el-select v-model="editLayer.form.iShowCaptions" style="width: 100%">
            <el-option v-for="(item, index) in booleanArray" :key="index" :label="item.sLabel" :value="item.sValue">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="" label="滚动内容">
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="editLayer.form.sMemo" clearable></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div>
          <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保
            存</el-button-icon-fa>
          <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取 消</el-button-icon-fa>
        </div>
      </template>
    </el-dialog>
    <VideoUpload :dialogVisible="d_upload_video" :rowData="rowData" :oDistrictsNames="oDistrictsNames"
      @closeDialog="closeVideo"></VideoUpload>
  </div>
</template>
<script>
import ClipboardJs from 'clipboard';
import Api from '$supersetApi/projects/apricot/system/screenConfig.js'
import { getHospitalData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'

import VideoUpload from './videoUpload.vue'
export default {
  name: 'ScreenConfig',
  mixins: [mixinTable],
  components: {
    VideoUpload
  },
  props: {},
  data() {
    return {
      loading: false,

      sDistrictIdData: [],
      sHospitalProps: {
        expandTrigger: 'hover',
        value: 'sId',
        label: 'sHospitalDistrictName',
        children: 'children',
      },
      searchForm: {},
      tableColumnConfig: [
        { sLabel: '院区', sProp: 'sDistrictId' },
        { sLabel: '名称', sProp: 'sName' },
        { sLabel: 'IP', sProp: 'sIp' },
        { sLabel: '工作站', sProp: 'workStation' },
        { sLabel: '启用', sProp: 'iIsEnable', sWidth: '50px' },
        { sLabel: '状态', sProp: 'iIsConnected', sWidth: '50px' },
        
        { sLabel: '滚动内容', sProp: 'sMemo' },

        { sLabel: '全屏静音', sProp: 'iMediaMute', sWidth: '80px' },
        { sLabel: '匿名显示', sProp: 'iHideName', sWidth: '80px' },
        // { sLabel: '字体大小', sProp: 'sListFontSize' },
        // { sLabel: '列表行高', sProp: 'sRowHeight' },
        // { sLabel: '列表宽度', sProp: 'iPercent' },
        // { sLabel: '患者个数', sProp: 'iRowsNum' },
        // { sLabel: '刷新间隔', sProp: 'iRefreshTime' },
        // { sLabel: '显示宣教内容', sProp: 'iMediaShow' },
        // { sLabel: '媒体', sProp: 'iMediaPlay' },
        // { sLabel: '宣教宽度', sProp: 'iMediaPercent' },
        // { sLabel: '视频音量', sProp: 'iMediaVolume' },
        // { sLabel: '显示滚动文字', sProp: 'iShowCaptions' },
      ],
      rules: {
        sDistrictId: [{ required: true, message: '' }],
        sName: [{ required: true, message: '' }],
        sIp: [{ required: true, message: '' }],
      },
      defualtVal: {

      },
      isSave: false,
      tableData: [],
      rowData: {},
      d_upload_video: false,
      oDistrictsNames: {},
      booleanArray: [{
        sValue: 1,
        sLabel: '是'
      }, {
        sValue: 0,
        sLabel: '否'
      }],
      actionState: 0,
      marks: { 
        20:'20%',
        40: '40%',
        60:'60%', 
        80: '80%'  
      },
      fontSizes: [
        {
          label: '40',
          value: 40,
          tips: '字体大小'
        },
        {
          label: '42',
          value: 42,
          tips: '字体大小'
        },
        {
          label: '44',
          value: 44,
          tips: '字体大小'

        },
        {
          label: '46',
          value: 46,
          tips: '字体大小'

        },
        {
          label: '48',
          value: 48,
          tips: '字体大小'

        }
      ],
      clipboard: null,
      url: window.location.origin,
      lineHeights: [
        { label: '112', value: 112 },
        { label: '116', value: 116 },
        { label: '120', value: 120 },
        { label: '124', value: 124 },
        { label: '128', value: 128 }
      ],
      patientNum: [
        { label: 1, value: 1 },
        { label: 3, value: 3 },
        { label: 5, value: 5 },
        { label: 6, value: 6 },
        { label: 7, value: 7 },
        { label: 8, value: 8 },
        { label: 9, value: 9 }
      ],
      refreshArray: [
        { label: '3', value: 3 },
        { label: '5', value: 5 },
        { label: '10', value: 10 },
        { label: '15', value: 15 },
        { label: '20', value: 20 },
        { label: '30', value: 30 },
        { label: '60', value: 60 },
        { label: '90', value: 90 },
        { label: '120', value: 120 },
        { label: '150', value: 150 },
        { label: '180', value: 180 }
      ],
      dialogVisible: false,
      dialogTitle: '',
    }
  },
  watch: {
  },
  methods: {
    copyCallUrl() {
      if (this.clipboard) {
        this.clipboard.destroy();
      }
      this.clipboard = new ClipboardJs(".copy-btn");
      this.clipboard.on('success', () => {
        this.$message({
          showClose: true,
          message: '复制成功',
          type: 'success',
        });
      });
      this.clipboard.on('error', () => {
        this.$message({
          showClose: true,
          message: '复制失败',
          type: 'error',
        });
      });
    },
    handleAdd() {
      this.actionState = 1
      this.dialogVisible = true
      this.dialogTitle = '新增呼叫屏'
      let params = {
        sDistrictId: this.sDistrictIdData[0].sId, // y院区id
        sName: '',// 名称
        sIp: '', // ip
        iIsConnected: 1,// 状态
        workStation: '',// 工作站
        iHideName: 1, // 匿名显示
        iIsEnable: 1, // 启用
        iMediaMute: 0, // 全屏静音
        iMediaPercent: 35, //媒体宽度
        iMediaShow: 0,  // 媒体显示
        iMediaVolume: 60, //媒体视频音量
        iPercent: 32,   // 列表宽度
        iRefreshTime: 5,// 列表刷新时间
        iRowsNum: 6,    //  列表患者个数 
        iShowCaptions: 0,// 是否显示提醒
        iSpeakRate: 8, //  呼叫语速
        iSpeakVolume: 85,// 呼叫音量
        sListFontSize: 40,// 列表字体大小
        sRowHeight: 120,//列表行高 
      }
      this.editLayer.form = Object.assign({}, params)
    
      let timeout = setTimeout(() => {
        this.$refs['refEditLayer'].clearValidate();
        clearTimeout(timeout)
      }, 100)
    },
    closeDialog() {
      this.dialogVisible = false
    },
    handleEdit(item, index) {
      this.actionState = 2
      this.dialogTitle = '编辑呼叫屏'
      this.dialogVisible = true
      this.editLayer.form = Object.assign({}, item)
      this.$nextTick(() => {
        this.$refs['refEditLayer'].clearValidate();
      })
    },
    handleSave() {
      this.editLayer.loading = true
      let params = Object.assign({}, this.editLayer.form)
      const sendReq = params.sId ? Api.editScreen : Api.addScreen
      this.$refs['refEditLayer'].validate((valid) => {
        if (valid) {
          sendReq(params).then((res) => {
            if (res.success) {
              this.$message({
                message: res.msg,
                type: 'success',
                duration: 3000
              });
              this.editLayer.loading = false
              this.actionState === 1 && this.closeDialog()
              this.mxGetTableList();
              return;
            }
            this.$message({
              message: res.msg,
              type: 'error',
              duration: 3000
            });
          }).catch(() => {
            this.editLayer.loading = false
          })

          return
        }
        this.editLayer.loading = false
      })
    },
    // 删除
    handleDelData(data, index) {
      let jsonData = {
        iVersion: data.iVersion,
        sId: data.sId
      }
      this.$confirm(`确定要删除【${data.sName}】显示屏吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      }).then(() => {
        Api.delScreen(jsonData).then(res => {
          if (res.success) {
            this.$message({
              message: res.msg,
              type: 'success',
              duration: 3000
            });
            // this.tableData.splice(index, 1)
            this.mxGetTableList();
            return
          }
          this.$message({
            message: res.msg,
            type: 'error',
            duration: 3000
          });
        }).catch(() => {
          console.log()
        })
      })

    },
    // 获取屏幕信息
    getScreenData() {
      this.loading = true
      let params = {
        sDistrictId: this.searchForm.sDistrictId,
        sScreenName: this.searchForm.sScreenName
      }
      Api.getScreenByDistricts(params).then(res => {
        this.loading = false
        if (res.success) {
          let data = res.data || [];
          data.map(item => {
            item.workStation = item.pushWorkStationNames.join('，')
          })
          this.tableData = data
          return
        }
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 3000
        });
      }).catch(() => {
        this.loading = false
        console.log(err)
      })
    },

    // 获取院区树数据
    async getTreeData() {
      await getHospitalData().then((res) => {
        if (res.success) {
          this.sDistrictIdData = res?.data || [];
          // this.searchForm.sDistrictId = this.sDistrictIdData[0].sId
          return
        }
      }).catch(() => {
      })
    },

    // 获取表格数据
    getData(params) {
      this.getScreenData()
    },

    // 启用禁用屏幕
    onChangeEnable(data, index) {
      if (!data.sId) {
        return
      }
      let jsonData = {
        sId: data.sId,
        iIsEnable: data.iIsEnable
      }
      Api.changeEnable(jsonData).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
          return
        }
        this.tableData[index]['iIsEnable'] = !val
        this.$message.error(res.msg)
      }).catch(() => {
        this.tableData[index]['iIsEnable'] = !val
      })
    },
    // 是否显示头像,字幕，匿名
    onChangeSwitch(val, row, index, sKey) {
      let jsonData = {
        sId: row.sId,
      }
      jsonData[sKey] = val;
      // 头像
      if (sKey == 'iPortrait') {
        Api.changeAvatar(jsonData).then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            return
          }
          this.tableData[index]['iPortrait'] = !val
          this.$message.error(res.msg)
        }).catch(() => {
          this.tableData[index]['iPortrait'] = !val
          console.log(err)
        })
        return
      }
      Api.changeShowCaptions(jsonData).then(res => {
        if (res.success) {
          this.$message.success(res.msg)
          return
        }
        this.tableData[index][sKey] = !val

        this.$message.error(res.msg)
      }).catch(() => {
        this.tableData[index][sKey] = !val
        console.log(err)
      })


    },
    // 设置媒体播放内容
    onChangeMediaSet(data, index) {
      if (data.sId) {
        let jsonData = {
          sId: data.sId,
          //iMediaMute: row.iMediaMute,
          iMediaPlay: data.iMediaPlay,
          // iMediaVolume: row.iMediaVolume
        }
        Api.videoSet(jsonData).then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            // console.log(this.tableData[index])
            // this.tableData[index]['iMediaPlay'] = val;
            // if (this.form.iVersion) this.form[propname] = val
            return
          }
          this.getScreenData()
          this.$message.error(res.msg)
        }).catch(() => {
          console.log(err)
        })
      }
    },
    // 清空
    handleClear() {
      for (let item in this.form) {
        this.form[item] = undefined;
      }
      this.form['iPercent'] = 50;
      this.form['sRowHeight'] = 80;
      this.form['iRowsNum'] = 5;
      this.$refs['mainForm'].clearValidate();
    },
    // 打开视频维护弹窗
    openVideo(data) {
      this.rowData = data;
      // this.setDistrictsName()
      // this.getTableData(true)
      this.d_upload_video = true;
    },
    // 关闭视频维护弹窗
    closeVideo() {
      this.d_upload_video = false
    },
    setDistrictsName() {
      this.sDistrictIdData.forEach(item => {
        this.oDistrictsNames[item.sId] = item.sHospitalDistrictName
      });
    },
  },
  async mounted() {
    // this.getScreenList()
    await this.getTreeData()
    this.getScreenData()
  },
};
</script>
<style lang="scss" scoped>
.c-container {
  width: 100%;
  height: 100%;
}


.c-flex-context {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15px 10px 10px 10px;
}

.item-title {
  font-size: 16px;
}

:deep(.el-input-number__decrease) {
  top: 8px
}

.action {
  margin-left: 10px;
  font-size: 15px;
  cursor: pointer;
}

// 
:deep(.el-collapse-item__header) {
  font-size: 14px;
  font-weight: 600;
  color: #555;
}

:deep(.el-slider) {
  margin-bottom: 4px;
}

:deep(.el-slider__marks) {
  width: auto;
}

.on-line {
  font-weight: 600;
  color: rgb(89, 220, 128);
}

.iline {
  font-weight: 600;
  color: rgb(167, 171, 177);
}

:deep(.el-tooltip__popper.is-dark) {
  width: 50px;
}

:deep(.el-slider__marks-text) {
  margin-top: 3px;
  transform: translateX(-100%);
  font-size: 13px;
}

:deep(.el-form-item.el-form-item) {
  margin-bottom: 5px;
  margin-right: 10px;
}

:deep(.el-form-item .el-form-item__error) {
  top: 30px;
  left: 2px;
}


.pull-right {
  float: right;

  span.icon {
    padding: 0 10px;
    cursor: pointer;
    color: var(--el-color-primary);
  }
}

:global(.i-height-70 .el-dialog__body) {
    height: 70vh;
    overflow: auto;
}
</style>
