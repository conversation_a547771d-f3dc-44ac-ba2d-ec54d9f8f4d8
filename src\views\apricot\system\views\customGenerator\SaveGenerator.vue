<template>
    <el-dialog append-to-body
        title="保存模板"
        v-model="dialogVisible"
        :close-on-click-modal="false"
        width="600px"
        class="my-dialog t-default">
        <el-form ref="form"
            :model="formData"
            :rules="rules"
            labelWidth="100px"
            class="form">
            <el-col :span="24">
                <el-form-item prop="sTemplateName" label="模板名称">
                    <el-input v-model="formData.sTemplateName" clearable></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item prop="sMemo" label="模板备注">
                    <el-input v-model="formData.sMemo" type="textarea" :rows="2"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="24">
                <el-form-item prop="iIsEnable" label="是否启用">
                    <el-switch
                        v-model="formData.iIsEnable"
                        size="large"
                        :active-value="1"
                        :inactive-value="0">
                    </el-switch>
                </el-form-item>
            </el-col>
        </el-form>

        <template #footer>
            <el-button-icon-fa  type="primary" icon="fa fa-save" :loading="loading" @click="onClickSave">保存</el-button-icon-fa>
            <el-button-icon-fa icon="fa fa-close-1" @click="dialogVisible = false">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import { addCustomTemplate, editCustomTemplate } from '$supersetApi/projects/apricot/case/consult.js'
export default {
    emits: [ 'update:modelValue', 'handleSave'],
    props: {
        modelValue: {
            type: Boolean,
            default: () => {return false }
        },
        editRow: {
            type: Object,
            default: () => ({})
        },
        jsonData: String
    },
    data() {
        return {
            formData: {
                sTemplateName: '',
                sMemo: '',
                iIsEnable: 1,
            },
            rules: {
                sTemplateName: [
                    { required: true, message: '请输入模板名称', trigger: 'blur' },
                ],
            },
            loading: false,
        }
    },
    computed: {
        dialogVisible: {
            get: function() {
                return this.modelValue
            },
            set: function(val) {
                this.$emit('update:modelValue', val);
                if(!val) {
                    this.$refs.form?.resetFields()
                }
            }
        }
    },
    watch: {
        editRow: {
            handler() {
                if (Object.keys(this.editRow).length) {
                    this.formData.sTemplateName = this.editRow.sTemplateName
                    this.formData.sMemo         = this.editRow.sMemo
                    this.formData.iIsEnable     = this.editRow.iIsEnable
                }else {
                    this.formData.sTemplateName = ''
                    this.formData.sMemo         = ''
                    this.formData.iIsEnable     = 1
                }
            },
            immediate: true,
            deep: true,
        }
    },
    methods: {
        onClickSave() {
            this.$refs['form'].validate((valid) => {
                if (!valid) {
                    return;
                } else {
                    this.loading = true;
                    if (Object.keys(this.editRow).length) {
                        // 编辑
                        const params = Object.assign({}, this.formData, {sTemplateJson: this.jsonData, sId: this.editRow.sId})
                        editCustomTemplate(params).then(res => {
                            this.loading = false;
                            if (res.success) {
                                this.$message.success('编辑成功！')
                                this.$emit('handleSave')
                                this.dialogVisible = false
                                return;
                            }
                            this.$message.error(res.msg);
                        }).catch(err => {
                            this.loading = false;
                            console.log(err)
                        })
                    }else {
                        // 新增
                        const params = Object.assign({}, this.formData, {sTemplateJson: this.jsonData})
                        addCustomTemplate(params).then(res => {
                            this.loading = false;
                            if (res.success) {
                                this.$message.success('保存成功！')
                                this.$emit('handleSave')
                                this.dialogVisible = false
                                return;
                            }
                            this.$message.error(res.msg);
                        }).catch(err => {
                            this.loading = false;
                            console.log(err)
                        })
                    }
                }
            })
            
        }
    }
}
</script>
<style lang="scss" scoped>
.form{
    padding: 10px;
    overflow: hidden;
    // :deep(.el-form-item__error){
    //     top: 80%;
    //     left: 10px;
    // }
}
</style>
