<template>
  <div class="content">
    <div class="box-left">
      <h5>组件库</h5>
      <h6>表单组件</h6>
      <draggable class="components-draggable" v-model="fromComponents"
        :group="{ name: 'componentsGroup', pull: 'clone', put: false }" :clone="cloneComponent"
        draggable=".components-item" :sort="false" item-key="id" @end="onEnd">
        <template #item="{ element: item }">
          <div class="components-item">
            <div class="components-body">
              <i class="fa" :class="item.tagIcon"></i>
              {{ item.label }}
            </div>
          </div>
        </template>
      </draggable>

      <h6>布局组件</h6>
      <draggable class="components-draggable" v-model="layoutComponents"
        :group="{ name: 'componentsGroup', pull: 'clone', put: false }" :clone="cloneComponent"
        draggable=".components-item" :sort="false" item-key="id" @end="onEnd">
        <template #item="{ element: item }">
          <div class="components-item">
            <div class="components-body">
              <i class="fa" :class="item.tagIcon"></i>
              {{ item.label }}
            </div>
          </div>
        </template>
      </draggable>

      <h6>修饰组件</h6>
      <draggable class="components-draggable" v-model="modifierComponents"
        :group="{ name: 'componentsGroup', pull: 'clone', put: false }" :clone="cloneComponent"
        draggable=".components-item" :sort="false" item-key="id" @end="onEnd">
        <template #item="{ element: item }">
          <div class="components-item">
            <div class="components-body">
              <i class="fa" :class="item.tagIcon"></i>
              {{ item.label }}
            </div>
          </div>
        </template>
      </draggable>
    </div>
    <div class="box-center">
      <div class="plane-header">
        <ul>
          <!-- <li>快捷布局1</li>
                    <li>快捷布局2</li>
                    <li>快捷布局3</li> -->
        </ul>
        <div>
          <el-button-icon-fa icon="el-icon-video-camera" @click="lookVisible = true"
            style="margin-right: 10px">预览</el-button-icon-fa>
          <el-popconfirm @confirm="onClickClear" confirm-button-text='好的' cancel-button-text='点错了' title="确定要清除吗?">
            <template #reference>
              <el-button-icon-fa type="warning" icon="el-icon-delete" slot="reference">清除</el-button-icon-fa>
            </template>
          </el-popconfirm>
          <el-button-icon-fa type="primary" icon="el-icon-check" :disabled="drawingList.length===0" @click="onClickSave" 
            style="margin-left: 10px">确定</el-button-icon-fa>
        </div>
      </div>
      <div class="plane-content" v-contextmenu:contextmenuDiv>
        <ul class="plane-nav"
          :style="{ height: navs.length ? '30px' : '0', borderBottom: navs.length ? '2px solid #eee' : '0' }">
          <li v-for="item in navs" @click="onClickToY(item.field)"> {{ item.label }} </li>
        </ul>
        <el-scrollbar class="scrollbar xx-el-scrollbar" ref="myScrollbar"
          :style="{ height: navs.length ? 'calc(100% - 38px)' : '100%' }">
          <el-form ref="refEditLayer" class="scope--rules" :inline="true">
            <draggable class="drawing-board" v-model="drawingList" item-key="id" :animation="340" :group="{ name: 'componentsGroup', type: 'transition-group' }">
              <template #item="{ element }">
                  <el-col :span="element.span">
                    <RenderDraggableUi @onClickRemove="onClickRemove" @onClickActiveItem="onClickActiveItem" :dataItem="element"
                      :activeId="activeId">
                    </RenderDraggableUi>
                  </el-col>
              </template>
            </draggable>
            <!-- 选中时，显示删除 -->
            <div v-if="!drawingList.length" class="tip">编辑区域</div>
          </el-form>
        </el-scrollbar>
      </div>
    </div>
    <div class="box-right">
      <h5>组件属性</h5>
      <div class="plane">
        <SetAttribute v-model:activeData="activeData" v-model:defaultAttribute="defaultAttribute"
          @changeNavigation="changeNavigation" @changeComponent="changeComponent"></SetAttribute>
      </div>
    </div>

    <LayerGeneratorLook v-model="lookVisible" :renderData="drawingList"></LayerGeneratorLook>

    <SaveGenerator v-model="saveVisible" :editRow="editRow" :jsonData="JSON.stringify(this.drawingList)"
      @handleSave="handleSave"></SaveGenerator>

    <!-- 空白处右键 -->
    <v-contextmenu ref="contextmenuDiv">
      <v-contextmenu-item @click="onClickCopy">复制模板</v-contextmenu-item>
      <v-contextmenu-item divider></v-contextmenu-item>
      <v-contextmenu-item @click="onClickPaste">粘贴模板</v-contextmenu-item>
    </v-contextmenu>
  </div>
</template>
<script>
import draggable from 'vuedraggable';
import { fromComponents, layoutComponents, defaultAttribute, modifierComponents } from '../config/generator.js';
import { deepClone } from "$supersetUtils/function";
import SetAttribute from './customGenerator/SetAttribute';
import RenderDraggableUi from './customGenerator/RenderDraggableUi';
import LayerGeneratorLook from './customGenerator/LayerGeneratorLook';
import SaveGenerator from './customGenerator/SaveGenerator';

export default {
  name: 'CustomGenerator',
  components: {
    draggable,
    SetAttribute,
    RenderDraggableUi,
    LayerGeneratorLook,
    SaveGenerator,
  },
  props: {
    editRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      fromComponents,
      layoutComponents,
      modifierComponents,
      defaultAttribute,
      drawingList: [], // 拖拽显示列表
      cloneData: {},   // 拖拽生成的数据
      activeData: {},  // 当前组件
      activeId: null,  // 当前选中 id
      idIndex: 1,      // id 累加  
      lookVisible: false, //  预览的弹窗
      saveVisible: false, // 保存显示
      navs: [],        // 导航
      activeNames: null
    }
  },
  watch: {
    editRow: {
      handler() {
        // 编辑的时候，赋值模板内容
        if (Object.keys(this.editRow).length && this.editRow.sTemplateJson) {
          const json = JSON.parse(this.editRow.sTemplateJson)
          this.drawingList.splice(0, this.drawingList.length, ...json)

          // 获取最大值 id 唯一值
          const getSpecialAttr = (list, last) => {
            list.forEach(item => {
              if (item.id && item.id > last.id) {
                last.id = item.id
              }
              if (item.children) {
                getSpecialAttr(item.children, last);
              }
            });
          }
          const lastItem = { id: 0 };
          getSpecialAttr(this.drawingList, lastItem);
          this.idIndex = lastItem.id;
        } else {
          this.drawingList.splice(0)
        }
        this.changeNavigation();
      },
      immediate: true,
      deep: true,
    }
  },
  methods: {
    handleChange() {
      console.log('changed');
    },
    inputChanged(value) {
      console.log('input-change')
      this.activeNames = value;
    },
    getComponentData() {
      return {
        onChange: this.handleChange,
        onInput: this.inputChanged,
        wrap: true,
        value: this.activeNames
      };
    },
    cloneComponent(origin) {
      const clone = deepClone(origin);
      clone.id = ++this.idIndex;
      clone.field = `field${clone.id}`;
      clone.span = this.defaultAttribute.span;

      if (origin.layout === 'formItem') {
        clone.required = this.defaultAttribute.required;
        clone.labelStyle = deepClone(this.defaultAttribute.labelStyle);
        clone.titleShow = this.defaultAttribute.titleShow;
        clone.labelWidth = this.defaultAttribute.labelWidth;

        const style = deepClone(this.defaultAttribute.style);
        // 多行文本，不要高度
        if (clone.tag === 'input-textarea') {
          delete style.height
        }
        clone.style = style;
      }

      this.cloneData = clone;
      return clone;
    },
    /**
     * 改变组件
     */
    changeComponent(component) {
      const newComponent = this.cloneComponent(component);
      newComponent.id = this.activeId;
      newComponent.field = this.activeData.field;

      this.activeData = newComponent;
      this.updateDrawingList(newComponent, this.drawingList);
    },
    /**
     * 替换新的组件
     */
    updateDrawingList(component, list) {
      const index = this.drawingList.findIndex(item => item.id === this.activeId);
      if (index != -1) {
        list.splice(index, 1, component);
      } else {
        list.forEach(item => {
          if (Array.isArray(item.children)) {
            this.updateDrawingList(component, item.children);
          }
        });
      }
    },
    onEnd(obj) {
      if (obj.from !== obj.to) {
        // 放置的位置不同
        this.activeData = this.cloneData;
        this.activeId = this.activeData.id;
      }
    },
    // 点击选中视图项
    onClickActiveItem(item) {
      this.activeData = item;
      this.activeId = item.id;
    },
    /**
     * 移除视图上的元素
     */
    onClickRemove(data) {
      const id = data.id;
      const treeDel = (list) => {
        if (!list) {
          return;
        }
        for (let index = 0; index < list.length; index++) {
          const item = list[index];
          if (item.id === id) {
            list.splice(index, 1);
            this.$nextTick(() => {
              const len = this.drawingList.length;
              if (len) {
                this.onClickActiveItem(this.drawingList[len - 1]);
              } else {
                this.activeData = {};
                this.activeId = null;
              }
            });
            return;
          } else {
            treeDel(item.children);
          }
        }
      }
      treeDel(this.drawingList);
      this.changeNavigation();
    },
    // 保存
    onClickSave() {
      this.saveVisible = true;
    },
    onClickClear() {
      this.activeData = {};
      this.drawingList = [];
      this.changeNavigation();
    },
    handleSave() {
      this.drawingList.splice(0);
      this.$emit('handleSave')
    },
    changeNavigation() {
      // 改变导航
      const navs = []
      const getSpecialAttr = (list) => {
        list.forEach(item => {
          if (item.navigation) {
            navs.push(item);
          }
          if (item.children) {
            getSpecialAttr(item.children);
          }
        });
      }
      getSpecialAttr(this.drawingList);
      this.navs = navs;
    },
    onClickToY(elementId) {
      let offsetTop = 0
      // document.getElementById('field35').parentNode
      const getOffsetTop = (dom) => {
        offsetTop += dom.offsetTop;
        const pNode = dom.parentNode;
        // 没有上一个节点，或者已到滚动条边界
        if (!pNode || (pNode && pNode.getAttribute('class') == 'drawing-board')) {
          return;
        }
        getOffsetTop(pNode);
      }
      getOffsetTop(document.getElementById(elementId));

      this.$refs['myScrollbar'].wrapRef.scrollTop = offsetTop //  this.$refs['' + elementId].offsetTop
    },
    onClickCopy() {
      localStorage.setItem('copyCustomTemplate', JSON.stringify(this.drawingList));
      this.$message.success('复制成功！');
    },
    onClickPaste() {
      const obj = localStorage.getItem('copyCustomTemplate');
      if (obj) {
        this.drawingList = JSON.parse(obj)
        this.changeNavigation();
        this.$message.success('粘贴成功！');
      } else {
        this.$message.info('没有粘贴内容');
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: row;
  height: 100%;
  width: 100%;

  .box-left {
    border-right: 1px solid #DCDFE6;
    width: 220px;
    // background: #24292E;
    color: #666;

    h5 {
      text-align: center;
      line-height: 41px;
      margin: 0px;
      border-bottom: 1px solid #DCDFE6;
      background: #f5f7fa;
      color: black;
      font-size: $base-font-size-default
    }

    h6 {
      margin: 20px 0px 4px 2%;
    }
  }

  .box-center {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .plane-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 41px;
      padding-right: 20px;
      border-bottom: 1px solid #DCDFE6;
      overflow: hidden;
      background: #f5f7fa;
      color: black;

      ul {
        padding: 0px;
        margin-bottom: 0px;
        list-style: none;
        height: 100%;

        li {
          float: left;
          line-height: 40px;
          padding-left: 20px;
          cursor: pointer;

          &:hover {
            font-weight: bold;
          }
        }
      }
    }

    .plane-content {
      flex: 1;
      overflow: hidden;

      .plane-nav {
        height: 0;
        line-height: 30px;
        padding: 0;
        margin: 4px 0px;
        display: flex;
        list-style-type: none;
        overflow: hidden;
        margin: 0;
        li {
          position: relative;
          min-width: 96px;
          text-align: center;
          cursor: pointer;

          &:hover {
            color: #23527c;
            text-decoration: underline;
          }

          &::before {
            content: "";
            width: 1px;
            height: 14px;
            background: #999;
            position: absolute;
            right: 0;
            top: 8px;
          }
        }
      }

      .scrollbar {
        height: 100%;
      }
    }
  }

  .box-right {
    display: flex;
    flex-direction: column;
    border-left: 1px solid #DCDFE6;
    width: 300px;

    h5 {
      text-align: center;
      line-height: 41px;
      margin: 0px;
      background: #f5f7fa;
      color: black;
      font-size: $base-font-size-default;
    }

    .plane {
      flex: 1;
      overflow: hidden;
    }

  }
}

.components-draggable {
  .components-item {
    display: inline-block;
    width: 46%;
    margin: 2%;

    .components-body {
      padding: 8px 10px;
      background: #f1f1f1;
      font-size: 12px;
      cursor: move;
      border-radius: 3px;
      text-align: center;
      color: black;

      i {
        width: 100%;
        height: initial;
        font-size: 26px;
        display: block;
      }
    }
  }
}

.el-form {
  height: 100%;

  .drawing-board {
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
}

.scrollbar {
  :deep(.el-scrollbar__view) {
    height: 100%;
  }
}

.tip {
  position: absolute;
  top: calc(50% - 20px);
  width: 100%;
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #8eb1cd;
  font-size: 32px;
  font-weight: bold;
}
</style>
