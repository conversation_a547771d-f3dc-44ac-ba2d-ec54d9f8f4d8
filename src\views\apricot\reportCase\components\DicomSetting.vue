<template>
    <el-dialog :title="'节点管理'"
        v-model="mainDialogVisible"
        append-to-body
        align-center
        width="90vw"
        class="t-default my-dialog"
        :close-on-click-modal="false"
        @close="mainDialogVisible = false;">
        <div class="c-flex-context c-container">
            <div class="c-form">
                <div class="c-form-btn">
                    <el-button-icon-fa type="primary"
                        icon="el-icon-plus"
                        @click="handleAdd">新增</el-button-icon-fa>
                    <el-button-icon-fa plain
                        type="primary"
                        :loading="loading"
                        icon="el-icon-refresh"
                        @click="mxDoRefresh()">刷新</el-button-icon-fa>
                </div>

                <!-- <div class="c-form-search">
                    <div class="flex">
                        <span class="label">节点名称：</span>
                        <el-input v-model="condition.sServerName"
                            placeholder="节点名称"
                            clearable=""
                            style="width:200px;"
                            @keyup.enter.native="mxDoSearch()"></el-input>
                    </div>
                    <div class="flex">
                        <span class="label">SERVER IP：</span>
                        <el-input v-model="condition.sIp"
                            placeholder="SERVER IP"
                            clearable=""
                            style="width:200px;"
                            @keyup.enter.native="mxDoSearch()"></el-input>
                    </div>
                    <div style="width: auto;">
                        <el-button-icon-fa icon="el-icon-search"
                            type="primary"
                            @click="mxDoSearch"
                            :loading="loading"></el-button-icon-fa>
                    </div>

                </div> -->
            </div>
            <div class="c-flex-auto">
                <div class="c-content"
                    v-loading="loading">
                    <!-- @row-click="handleRowClick" -->
                    <el-table :data="tableData"
                        id="itemTable"
                        ref="mainTable"
                        size="small"
                        @row-dblclick="handleRowClick"
                        border
                        stripe
                        height="100%"
                        style="width: 100%">
                        <el-table-column v-for="item in configTable.filter(_i => !_i.iIsHide)"
                            show-overflow-tooltip
                            :key="item.index"
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort">
                            <template v-slot="scope">
                                <template v-if="item.sProp === 'action'">
                                    <el-button size="small"
                                        link
                                        type="primary"
                                        @click="handleEdit(scope.row)">
                                        编辑
                                        <template #icon>
                                            <Icon name="el-icon-edit"
                                                color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button-icon-fa size="small"
                                        link
                                        _icon="fa fa-link1"
                                        @click="onLinkClick(scope.row)">测试连接</el-button-icon-fa>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button size="small"
                                        link
                                        @click="onClickDel(scope.row)">
                                        删除
                                        <template #icon>
                                            <Icon name="el-icon-delete"
                                                color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                </template>
                                <template v-else-if="['iAutoSend','iAllowResend','iSendScreenshot','iSendOriginalDicom'].includes(item.sProp)">
                                    <span v-if="scope.row[item.sProp]" class="icon-green">是</span>
                                    <span v-else>否</span>
                                </template>
                                <template v-else-if="['iImageType','iTriggerType','iSeriesChangeMode'].includes(item.sProp)">
                                    {{ setKeyName(scope.row[`${item.sProp}`], item.sProp) }}
                                </template>
                                <template v-else-if="item.sProp.slice(0, 1) === 'd'">
                                    {{ scope.row[`${item.sProp}`] | mxToDate() }}
                                </template>
                                <template v-else>
                                    {{ scope.row[`${item.sProp}`] }}
                                </template>
                            </template>
                            <!-- <template v-slot:header
                            v-slot="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <el-dialog :title="dialogTitle"
                v-model="dialogVisible"
                append-to-body
                class="t-default"
                width="700"
                :close-on-click-modal="false"
                @close="closeDialog">
                <div class="flex">
                    <el-form :model="editLayer.form"
                        ref="refEditLayer"
                        label-width="138px"
                        :rules="rules">
                        <el-col :span="24">
                            <el-form-item prop="sServerName"
                                label="节点名称：">
                                <el-input v-model="editLayer.form.sServerName"
                                    clearable
                                    placeholder="节点名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sAETitle"
                                label="AE Title：">
                                <el-input v-model="editLayer.form.sAETitle"
                                    clearable
                                    placeholder="AE Title"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sIp"
                                label="SERVER IP：">
                                <el-input v-model="editLayer.form.sIp"
                                    clearable
                                    placeholder="SERVER IP"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="iPort"
                                label="SERVER PORT：">
                                <el-input v-model="editLayer.form.iPort"
                                    clearable
                                    placeholder="SERVER PORT"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="自动发送：">
                                <el-select v-model="editLayer.form.iAutoSend"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iAutoOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>   
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="触发方式：">
                                <el-select v-model="editLayer.form.iTriggerType"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iTriggerTypeOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="发送截图：">
                                <el-select v-model="editLayer.form.iSendScreenshot"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iAutoOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>   
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="截图类型：">
                                <el-select v-model="editLayer.form.iImageType"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iImageTypeOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="允许截图重发：">
                                <el-select v-model="editLayer.form.iAllowResend"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iAutoOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>   
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="序列修改模式：">
                                <el-select v-model="editLayer.form.iSeriesChangeMode"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iSeriesChangeModeOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="发送原始图：">
                                <el-select v-model="editLayer.form.iSendOriginalDicom"
                                    placeholder=" "
                                    clearable
                                    style="width: 100%;">
                                    <el-option v-for="item in iAutoOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select>   
                            </el-form-item>
                        </el-col>
                    </el-form>
                </div>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button-icon-fa :loading="editLayer.loading"
                            icon="el-icon-check"
                            type="primary"
                            @click="handleSave">保存</el-button-icon-fa>
                        <el-button-icon-fa @click="closeDialog"
                            icon="el-icon-close">取消</el-button-icon-fa>

                    </div>
                </template>
            </el-dialog>
        </div>
        <template #footer>
            <el-button-icon-fa type="default"
                icon="fa fa-close-1"
                @click="mainDialogVisible = false;">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import { deepClone } from '$supersetUtils/function'
import Api from '$supersetApi/projects/apricot/system/dicomSet.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'DicomSetting',
    mixins: [mixinTable],
    components: {},
    props: {
        modelValue: {
            default: false
        }
    },
    data () {
        return {
            loading: false,
            dialogVisible: false,
            isMixinDynamicGetTableHead: true,
            dialogTitle: '',
            configTable: [{
                sProp: 'sServerName',
                sLabel: '节点名称',
                sAlign: 'left',
                sMinWidth: '100px',
            },
            {
                sProp: 'sAETitle',
                sLabel: 'AE Title',
                sAlign: 'left',
                sMinWidth: '110px',
            },
            {
                sProp: 'sIp',
                sLabel: 'SERVER IP ',
                sAlign: 'left',
                sMinWidth: '110px',

            },
            {
                sProp: 'iPort',
                sLabel: 'SERVER PORT',
                sAlign: 'left',
                sMinWidth: '125px',
            },
            {
                sProp: 'iAutoSend',
                sLabel: '自动发送',
                sAlign: 'center',
                sMinWidth: '80px',
            },
            // {
            //     sProp: 'iTriggerType',
            //     sLabel: '触发方式',
            //     sAlign: 'center',
            //     sMinWidth: '80px',
            // },
            // {
            //     sProp: 'iSendScreenshot',
            //     sLabel: '发送截图',
            //     sAlign: 'center',
            //     sMinWidth: '80px',
            // },
            // {
            //     sProp: 'iImageType',
            //     sLabel: '截图类型',
            //     sAlign: 'center',
            //     sMinWidth: '120px',
            // },
            // {
            //     sProp: 'iAllowResend',
            //     sLabel: '允许截图重发',
            //     sAlign: 'center',
            //     sMinWidth: '80px',
            // },
            // {
            //     sProp: 'iSendOriginalDicom',
            //     sLabel: '发送原始图',
            //     sAlign: 'center',
            //     sMinWidth: '80px',
            // },
            // {
            //     sProp: 'iSeriesChangeMode',
            //     sLabel: '序列修改模式',
            //     sAlign: 'center',
            //     sMinWidth: '120px',
            // },
            // {
            //     sProp: 'iIsEnable',
            //     sLabel: '启用',
            //     sAlign: 'center',
            //     sMinWidth: '80px',
            // },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '230px',
                sFixed: 'right'
            }],
            rules: {
                sServerName: [{ required: true, message: '节点名称不能为空' }],
                sAETitle: [{ required: true, message: 'AE Title不能为空' }],
                sIp: [{ required: true, message: 'SERVER IP不能为空' }],
                iPort: [{ required: true, message: 'SERVER PORT不能为空' }],
            },
            condition: {},
            defaultVal: {
                editLayer: {
                    sServerName: '',
                    sAETitle: '',
                    sIp: '',
                    iPort: '',
                    iAutoSend: 1,
                    iAllowResend: 1,
                    iImageType: 2,
                    iSendOriginalDicom: 0,
                    iSendScreenshot: 1,
                    iSeriesChangeMode: 1,
                    iTriggerType: 1,
                    iIsEnable: 1,
                },
            },
            iAutoOptions: [
                {
                    sName: '是',
                    sValue: 1
                },
                {
                    sName: '否',
                    sValue: 0
                },
            ],
            iImageTypeOptions: [
                {
                    sName: '所有截图',
                    sValue: 1
                },
                {
                    sName: '标记打印的截图',
                    sValue: 2
                },
            ],
            iTriggerTypeOptions: [
                {
                    sName: '审核后',
                    sValue: 1
                },
                {
                    sName: '复审后',
                    sValue: 2
                },
            ],
            iSeriesChangeModeOptions: [
                {
                    sName: '保持各截图的序列ID不变',
                    sValue: 1
                },
                {
                    sName: '病例下的所有截图归并到一个固定的序列ID',
                    sValue: 2
                },
                {
                    sName: '每次发送生成新的序列ID',
                    sValue: 3
                },
            ]
        }
    },
    computed: {
        mainDialogVisible: {
            get () {
                return this.modelValue
            },
            set (val) {
                this.$emit('update:modelValue')
            }
        },

    },
    watch: {
        mainDialogVisible (val) {
            if (val) {
                this.mxDoSearch()
            }
        },
    },
    methods: {
        setKeyName(value, prop) {
            var tempObj = {}
            this[prop + 'Options'].map(item => {
                tempObj[item.sValue] = item.sName
            })
            return tempObj?.[value]
        },
        onChangeConditionKey (val) {
            this.selectKey = val
        },
        // 新增
        handleAdd () {
            this.actionState = 1;
            this.dialogVisible = true;
            this.dialogTitle = '新增';
            this.editLayer.form = { ...this.defaultVal };
            var time = setTimeout(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
                clearTimeout(time);
            }, 100)
        },
        closeDialog () {
            this.dialogVisible = false;
        },
        handleEdit (row) {
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({}, row)
            this.$nextTick(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            })
        },
        handleSave () {
            this.editLayer.loading = true
            let params = Object.assign({}, this.editLayer.form)
            this.$refs['refEditLayer'].validate((valid) => {
                if (valid) {
                    this.saveData(params)
                    return
                }
                this.editLayer.loading = false
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            let pattern = /(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)\.(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)/;
            if (!pattern.test(params.sIp)) {
                this.$message.error('SERVER IP输入不正确');
                this.editLayer.loading = false
                return;
            }
            let pattern1 = /^\d+$/;
            if (!pattern1.test(params.iPort)) {
                this.$message.error('SERVER Port输入不正确');
                this.editLayer.loading = false
                return;
            }
            this.editLayer.loading = true;
            if (params.sId) {
                Api.editDicomNode(params).then(res => {
                    this.editLayer.loading = false
                    if (res.success) {
                        this.dialogVisible = false
                        this.$message.success(res.msg);
                        this.mxGetTableList();
                        return;
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    this.editLayer.loading = false
                    console.log(err)
                })
                return
            }
            params.iIsEnable = 1;
            Api.addDicomNode(params).then(res => {
                this.editLayer.loading = false
                if (res.success) {
                    this.dialogVisible = false
                    this.$message.success(res.msg);
                    this.mxGetTableList();
                    return;
                }
                this.$message.error(res.msg)
            }).catch(err => {
                this.editLayer.loading = false
                console.log(err)
            })
        },
        onLinkClick (row) {
            const loading = this.$loading({
                lock: true,
                text: '连接中...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            Api.dicomTestConnect({
                sAETitle: row.sAETitle,
                sIp: row.sIp,
                iPort: row.iPort
            }).then(res => {
                loading.close();
                if (res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close();
                console.log(err);
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【${row.sServerName}】吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning",
                customClass: 'my-message-box'
            }).then(() => {
                Api.delDicomNode({ sId: row.sId, iVersion: row.iVersion }).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.mxGetTableList();
                        return;
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    console.log(err);
                });
            }).catch((e) => {
                console.log(e)
            });
        },
        // 行点击事件
        handleRowClick (row, isCancel = false, id = 'sId') {
            this.onClickRow(row, isCancel, id);
        },
        // 获取表格数据
        getData (params) {
            this.$nextTick(() => {
                this.$refs.refEditLayer && this.$refs.refEditLayer.clearValidate()
            })
            Api.getDicomNode(params).then(res => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data || [];
                    this.page.total = res.data.countRow;
                    // 赋选中状态
                    // this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        },
    },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 80vh;
    display: flex;
    flex-direction: column;
    padding: 0;
    padding-top: 10px;
    .c-form {
        display: flex;
        padding-bottom: 10px;
        justify-content: space-between;

        .c-form-search {
            min-width: 400px;
            display: flex;
            .flex {
                align-items: center;
                span.label {
                    width: 120px;
                    text-align: right;
                }
            }
            > div:not(:last-child) {
                margin-right: 10px;
            }
        }
    }

    .c-flex-auto {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;

        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;

            > button {
                margin-top: 13px;
            }
        }

        .c-content {
            flex: 1;
            height: 0px;
        }
    }

    // .m-labelInput {
    //     width: 100%;
    //     margin-left: 0;
    // }
}
</style>
