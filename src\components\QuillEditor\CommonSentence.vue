<template>
  <el-dialog append-to-body align-center title="常用语设置" v-model="visible" width="1070px" :close-on-click-modal="false"
    @close="closeDialog" class=" ">
    <div class="g-content">
      <div class="" style="float: right; width: 150px;">
        <el-popover v-model:visible="isPopoverShow" trigger="manual" placement="bottom" width="300">
          <div class="">
            <el-input v-model="inputVal" :rows="2" type="textarea" placeholder="请输入内容"></el-input>
          </div>
          <div style="margin-top: 10px;">
            <el-button @click="onClickAdd">确定</el-button>
            <el-button @click="() => { inputVal = ''; isPopoverShow = false; }">取消</el-button>
          </div>
          <template #reference>
            <span></span>
          </template>
        </el-popover>
      </div>
      <el-table :data="CommonSentenceList" height="500" border stripe>
        <el-table-column label="常用语内容">
          <template v-slot="scope">
            <el-input v-if="editRowIndexMap[scope.$index]" v-model="editRowIndexMap[scope.$index]" />
            <span v-else>{{ scope.row.sCommonWords }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="210">
          <template v-slot:header="scope">
            <el-button-icon-fa title="新增" link size="small" icon="fa fa-plus" @click="isPopoverShow = true">
              新增
            </el-button-icon-fa>

          </template>
          <template v-slot="scope">
            <el-button-icon-fa v-if="!editRowIndexMap[scope.$index]" link title="编辑" icon="el-icon-edit" size="small"
              @click="handleEditTable(scope.row, scope.$index)">编辑</el-button-icon-fa>
            <el-button-icon-fa v-if="editRowIndexMap[scope.$index]" :loading="isSaveLoading" link title="保存"
              icon="fa fa-save" size="small" @click="handleSaveTable(scope.row, scope.$index)">保存</el-button-icon-fa>
            <el-button-icon-fa :loading="isSaveLoading" link title="删除" icon="el-icon-delete" size="small"
              @click="delData(scope.row, scope.$index)">删除</el-button-icon-fa>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <!-- <el-button-icon-fa type="primary" icon="el-icon-document-checked" @click="onClickSave"
        :loading="loading">保存</el-button-icon-fa> -->
      <el-button-icon-fa icon="el-icon-close" @click="closeDialog">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>
<script>

import Api from '$supersetApi/projects/apricot/case/report.js'
export default {
  name: 'CommonSentence',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      isSaveLoading: false,
      isPopoverShow: false,
      inputVal: '',

      editRowIndexMap: {}
    }
  },
  watch: {
    dialogVisible() {
      this.visible = this.dialogVisible;
      if (this.visible) {
        this.getData()
      }
    }
  },
  computed: { 
    CommonSentenceList() {
      return this.$store.getters['apricot/report_module/CommonSentence']
    }
  },
  mounted() { 
  },
  methods: {
    handleEditTable(row, index) {
      this.editRowIndexMap[index] = row.sCommonWords
    },
    delData(row, index) {
      var title = row.sCommonWords ? row.sCommonWords : ''
      this.$confirm(`确定要删除【${title}】吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: "warning"
      }).then(() => {
        Api.delCommonWords({sCommonWordsId: row.sCommonWordsId}).then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            this.getData()
            return
          }
          this.$message.error(res.msg)
        }).catch((err) => {
          console.log(err)
        })
      })

    },
    handleSaveTable(row, index) { 
      this.isSaveLoading = true
      const params = {
        sCommonWordsId: row.sCommonWordsId,
        sCommonWords: this.editRowIndexMap[index]
      }
        Api.editCommonWords(params).then(res => {
          this.isSaveLoading = false
          if (res.success) {
            this.editRowIndexMap[index] = false
            this.$message.success(res.msg)
            this.getData()
            return
          }
          this.$message.error(res.msg)
        }).catch(() => {
          this.isSaveLoading = false
          console.log(err)
        })
       

    },
    getData() {
      this.$store.dispatch('apricot/report_module/loadCommonSentence', true).then(list => {
        if (!list) return
        list.forEach((a, i) => {
          this.editRowIndexMap[i] = false
          // this.editRowIndexMap[i] = 0
        })
      })

    }, 
    // 点击新增
    onClickAdd() {
      if (!(this.inputVal)) {
        this.$message.warning('请填写内容！')
        return
      }
      this.loading = true
      Api.addCommonWords({ sCommonWords: this.inputVal }).then(res => {
        this.loading = false
        if (res.success) {
          this.getData()
          this.$message.success(res.msg)
          this.isPopoverShow = false
          this.inputVal = ''
          return;
        }
        this.$message.error(res.msg)
      }).catch(() => {
        this.loading = false
      })
    },
    openDialog() {
      // this.mxDoRefresh()
    },
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
  }
}
</script>
<style lang="scss" scoped></style>
