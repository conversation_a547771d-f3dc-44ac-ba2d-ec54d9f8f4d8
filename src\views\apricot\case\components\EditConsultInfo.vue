<template>
    <!-- 问诊信弹窗 -->
    <el-dialog v-model="visible" 
        class="t-default my-dialog"
        title="问诊信息" 
        width="1080px" 
        top="10vh"
        destroy-on-close 
        append-to-body
        :close-on-click-modal="false" 
        @close="handleCloseDialog">
        <div class="c-dialog-body" v-if="visible" style="height:70vh">
            <ConsultTemplate :deviceTypeKey="'sRoomId'" :isConsultModule="false" @onClose="onClose"></ConsultTemplate>
        </div>
        <template #footer> 
            <div class="my-flex">
                <div class="my-consult-doctors fl"></div>
                <div>
                    <!-- 问诊信息保存 -->
                    <span class="my-consult-save mr"></span>

                    <el-button-icon-fa icon="fa fa-close-1" @click="onClose">关闭</el-button-icon-fa>
                </div>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import ConsultTemplate from '$supersetViews/apricot/consultMng/components/ConsultTemplate.vue'
export default {
    name: 'EditConsultInfo',
    emits: ['update:dialogVisible','closeDialog'],
    components: {
        ConsultTemplate
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            visible: false,
            delayPatientInfo: {}
        }
    },
    provide () {
        return {
            patientInfo: computed(() => this.delayPatientInfo),
            mxGetTableList: () => {},
            configValue: {}
        }
    },
    watch: {
        dialogVisible(val) {
            this.visible = val;
            this.delayPatientInfo = {};
            if(val) {
                setTimeout(() => {
                    this.delayPatientInfo = this.patientInfo
                }, 300);
            }
        },
    },
    methods: {
        handleCloseDialog() {
            this.$emit('closeDialog');
        },
        onClose() {
            this.$emit('update:dialogVisible', false)
        }
    }
}
</script>

<style lang="scss" scoped>
.fl {
    float: left;
    text-align: left;
}
.mr {
    margin-right: 10px;
}
.my-flex{
    display:flex;
    justify-content: space-between;
}

.my-consult-doctors {
    width: calc(100% - 200px);
    :deep(.container .main-form-list .grid-box) {
        padding-top: 0;
    }
    :deep(.container .main-form-list .grid-box .cell-box) {
        padding: 0;
    }
    :deep(.el-form--inline .el-form-item) {
        margin-bottom: 0;
    }
    :deep(.el-form-item--default .el-form-item__content) {
        padding-right: 10px;
    }
}

</style>
