import request from '$supersetUtils/request'
import {
    baseURL
  } from '$supersetUtils/request'
// 报告模块
export default {
    getReportInit(params){
        return request({
            url: baseURL.apricot + '/intern/report/find/init',
            method: 'POST',
            params
        })
    },
    // 保存报告
    addReport(data) {
        return request({
            url: baseURL.apricot + '/intern/report/add',
            method: 'POST',
            data
        })
    },
    // 编辑报告
    editReport(data) {
        return request({
            url: baseURL.apricot + '/intern/report/edit',
            method: 'POST',
            data
        }) 
    },

    // 获取报告分页数据
    getInternPage(data) {
        return request({
            url: baseURL.apricot + '/intern/report/find/page',
            method: 'POST',
            data
        })
    },
    
}