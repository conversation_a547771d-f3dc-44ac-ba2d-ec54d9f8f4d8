<template>
    <DragAdjust :dragAdjustData="DA0" >
        <template v-slot:c-left >
            <LayoutTable>
                <template v-slot:header>
                    <SearchList ref="searchRef"
                        v-model:modelValue="condition" 
                        :list="searchStyle" 
                        :iModuleId="iModuleId"
                        :optionData="optionsLoc" 
                        :configBtn="MngIndexPenalConfig.isShowConfigBtn"
                        storageKey="visitSearchLsit" 
                        @changeSearch="mxDoSearch"
                        @reset="handleResetCondition">
                        <template v-slot:dAppointmentTimeSt>
                            <el-date-picker v-model="condition.dAppointmentTimeSt"
                                type="date"
                                :picker-options="pickerOptionsAppointDayStart"
                                @change="mxDoSearch()" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:dAppointmentTimeEd>
                            <el-date-picker type="date"
                                v-model="condition.dAppointmentTimeEd"
                                @change="mxDoSearch()"
                                :picker-options="pickerOptionsAppointDayEnd" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:dReportCommitTimeSta>
                            <el-date-picker v-model="condition.dReportCommitTimeSta"
                                type="date"
                                @change="mxDoSearch()" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:dReportCommitTimeEnd>
                            <el-date-picker type="date"
                                v-model="condition.dReportCommitTimeEnd"
                                @change="mxDoSearch()" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:sRecentDays>
                            <el-select v-model="condition.sRecentDays"
                                @change="changeTimes"
                                placeholder=""
                                clearable>
                                <el-option v-for="(item, index) in optionsLoc.recentDayOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.keyWord">
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:sDistrictId>
                            <el-select v-model="condition.sDistrictId"
                                placeholder=""
                                @change="useChangeHospital"
                                clearable>
                                <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                                    :key="index"
                                    :label="item.sDistrictPrefix"
                                    :value="item.sId">
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:sMachineryRoomId>
                            <el-select v-model="condition.sMachineryRoomId"
                                @change="useChangeMachineRoom"
                                placeholder=""
                                clearable>
                                <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                                    :key="index"
                                    :label="item.sRoomName"
                                    :value="item.sId">
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:sProjectId>
                            <el-select v-model="condition.sProjectId"
                                placeholder=""
                                @change="mxDoSearch"
                                clearable>
                                <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                                    :key="index"
                                    :label="item.sItemName"
                                    :value="item.sId">
                                    <span style="float: left">{{ item.sItemName }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:dFollowUpTime>
                            <el-date-picker v-model="condition.dFollowUpTime"
                                @change="changeFollowUpTime"
                                type="date">
                                <!-- :picker-options="pickerOptionsAppointDayEnd"> -->
                            </el-date-picker>
                        </template>
                        <template v-slot:sApplyDepartText>
                            <el-select v-model="condition.sApplyDepartText"
                                placeholder=""
                                clearable
                                filterable
                                allow-create
                                default-first-option
                                @change="mxDoSearch">
                                <el-option v-for="(item, index) in optionsLoc['sApplyDepartTextOptions']"
                                    :key="index"
                                    :label="item.sApplyDeptName"
                                    :value="item.sApplyDeptName">
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:sReporterName>
                            <el-select v-model="condition.sReporterName"
                                placeholder=""
                                clearable
                                filterable
                                allow-create
                                default-first-option
                                style="width:100%"
                                @change="mxDoSearch">
                                <el-option v-for="item in optionsLoc[`sReporterNameOptions`]"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sName">
                                </el-option>
                            </el-select>
                        </template>
                    </SearchList>
                </template>
                <template #action>
                    <div class="action-box">
                        <div class="item-box">
                            <el-dropdown >
                                <el-button type="primary" plain>
                                    <template #icon><Icon name="el-icon-setting"></Icon></template>
                                    更多操作&nbsp; <Icon name="el-icon-arrow-down"></Icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item v-if="$auth['report:followup:export']" @click="doExportTable(false)"> 
                                            <i class="fa fa-file-excel-o" style="height: 20px;top: -1px;"></i> 
                                            批量导出病历
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="$auth['report:followup:export']" @click="doExportTable(true)"> 
                                            <i class="fa fa-file-excel-o" style="height: 20px;top: -1px;"></i> 
                                            匿名导出病历
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="$auth['report:followup:export']" @click="mxExportSelectionTable"> 
                                            <i class="fa fa-file-excel-o" style="height: 20px;top: -1px;"></i> 
                                            勾选导出病历
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="$auth['report:followup:export']" @click="doSeparateExportTable(false)"> 
                                            <i class="fa fa-file-excel-o" style="height: 20px;top: -1px;"></i> 
                                            批量导出随访
                                        </el-dropdown-item>
                                        <el-dropdown-item v-if="$auth['report:followup:export']" @click="doSeparateExportTable(true)"> 
                                            <i class="fa fa-file-excel-o" style="height: 20px;top: -1px;"></i> 
                                            匿名导出随访
                                        </el-dropdown-item>
                                        <!-- <el-dropdown-item v-if="$auth['report:followup:export']" @click="mxExportSelectionTable"> 
                                            <i class="fa fa-file-excel-o" style="height: 20px;top: -1px;"></i> 
                                            勾选导出随访
                                        </el-dropdown-item> -->
                                        <el-dropdown-item v-if="$auth['report:followup:pageConfig']">
                                            <PanelConfigDialog :iModuleId="iModuleId" 
                                                :formData="MngIndexPenalConfig" 
                                                :configKey="'VisitedMngPenalConfig'" 
                                                @updateData="(data)=>MngIndexPenalConfig=data"></PanelConfigDialog>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>

                            <!-- 页面设置 PanelConfigDialog自定义组件放置在 button组件里需要添加class:u-button, dropdown组件不需要 -->
                            <!-- <el-button>
                                <PanelConfigDialog class="u-button" :iModuleId="iModuleId" 
                                :formData="MngIndexPenalConfig" 
                                :configKey="'VisitedMngPenalConfig'" 
                                @updateData="(data)=>MngIndexPenalConfig=data"></PanelConfigDialog>
                            </el-button> -->

                            <span class="ml-4 i-text">病历数：{{ page.total ?? 0 }}</span>
                            <span class="ml-4 i-text">随访数：{{ followupCount ?? 0 }}</span>
                        </div>
                        <div class="item-box">
                            <el-button @click="handleResetCondition()">
                                    <template #icon>
                                    <Icon name="el-icon-refresh-left" >
                                    </Icon>
                                </template>
                                重置</el-button>
                            
                        
                            <el-button v-auth="'report:followup:query'" type="primary" @click="mxDoSearch()">
                                <template #icon><Icon name="el-icon-search" color="white"></Icon></template>
                                查询</el-button>
                        
                        </div>
                    </div>
                </template>
                <template #content>
                    <el-table-extend v-loading="loading"
                        ref="mainTable"
                        :data="tableData"
                        storageKey="VisitsMngIndexTable"
                        :iModuleId="iModuleId"
                        :configBtn="MngIndexPenalConfig.isShowConfigBtn"
                        :row-class-name="mxRowClassName"
                        @row-click="handleOnClickRow"
                        @sort-change="handleSortChange"
                        @selection-change="handleSelectionChange"
                        highlight-current-row
                        height="100%">
                        <el-table-column fixed type="selection" label="选择" prop="_select" align="center" width="50">
                        </el-table-column>
                        <el-table-column type="index" label="序号" prop="_index" align="center" width="60">
                            <template v-slot="scope">
                                <span>{{ scope.$index + 1 }}</span>
                            </template>
                        </el-table-column>
                        <template v-for="item in patientTable">
                            <el-table-column :show-overflow-tooltip="item.sProp !== 'img'"
                                :prop="item.sProp"
                                :label="item.sLabel" 
                                :fixed="item.sFixed" 
                                :align="item.sAlign" 
                                :width="item.sWidth"
                                :min-width="item.sMinWidth" 
                                :sortable="(!!item.iSort) ? 'custom': false"
                                :column-key="item.sSortField?item.sSortField:null">
                                <template v-slot="scope">
                                    <template v-if="FlowStateEnum.includes(item.sProp)">
                                        <!-- 0:未上机;1:上机准备;2:上机中;3:延迟中;4:上机完成 -->
                                        <span v-if="item.sProp === 'iIsMachine' && scope.row[`${item.sProp}`] == 1"
                                            class="icon-blue">准备</span>
                                        <span v-else-if="item.sProp==='iIsMachine'&& scope.row[`${item.sProp}`]==3"
                                            class="icon-blue">延迟</span>
                                        <i v-else-if="scope.row[`${item.sProp}`] == 2"
                                            class="icon-blue" :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                        <i v-else-if="scope.row[`${item.sProp}`]" class="icon-green"
                                            :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                    </template>
                                    <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                        {{mxFormatterDate( scope.row[`${item.sProp}`])}}
                                    </template>
                                    <template v-else>
                                        {{scope.row[`${item.sProp}`]}}
                                    </template>
                                </template>
                            </el-table-column>
                        </template>
                    </el-table-extend>
                </template>
                <template #footer>
                    <el-pagination background @size-change="onSizeChange"
                        @current-change="onCurrentChange"
                        :current-page="page.pageCurrent"
                        :page-sizes="mxPageSizes"
                        :pager-count="5"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next"
                        :total="page.total">
                    </el-pagination>
                </template>
            </LayoutTable>
        </template>
        <template v-slot:c-right >
            <div class="m-flexLaout-ty">
                <div class="c-right-item">
                    <div class="c-text-title">
                        <h3>个人信息</h3>
                        <el-button class="i-button" :disabled="!editLayer.selectedItem.sId" link type="primary" @click="openPatientInfoModifi">
                            <el-icon size="14"><Tickets/></el-icon>
                            患者详情
                        </el-button>
                    </div>
                    <div :style="{ backgroundColor: MngIndexPenalConfig.patientInfoBgColor}">
                        <TextList :list="textList" 
                            :data="initPatientInfo" 
                            :iModuleId="iModuleId" 
                            :title="'个人信息'" 
                            :configBtn="MngIndexPenalConfig.isShowConfigBtn"
                            storageKey="upVisitIndexInfoList">
                            <!-- <template #customBtn>
                                <el-button type="primary" link @click="openPatientInfoModifi"><el-icon size="18" ><Edit /></el-icon></el-button>
                            </template> -->
                        </TextList>
                    </div>
                    
                </div>
                <div class="c-right-item g-flexChild">
                    <OperateArea 
                        :configValue="MngIndexPenalConfig"
                        @updateRowData="updateTableInfo"
                    ></OperateArea>
                </div>
                
            </div>
        </template>
    </DragAdjust>
    <!-- 采集头像 -->
    <!-- <CollectAvatar :dialogVisible="d_CollectAvatar_v"
        :patientInfo="editLayer.selectedItem"
        :index="editLayer.selectedItem.index"
        @refreshImg="refreshImg"
        @closeDialog="closeCollectAvatarDialog"></CollectAvatar> -->

   
     <!-- 患者详情 -->
    <PatientInfoRead :dialogVisible="dialog_PatientInfoModifi_v" 
        :patientSid="editLayer.selectedItem.sId" 
        :iModuleId="iModuleId"
        @closeDialog="closePatientInfoModifi">
    </PatientInfoRead>
</template>

<script>
// 模块辅助样式
import { Search, RefreshRight, Upload , Setting, ArrowDown,Tickets} from '@element-plus/icons-vue';
// 组件

import ScrollPane from '$supersetViews/components/ScrollPane.vue'
import TableAvatar from '$supersetViews/apricot/components/TableAvatar.vue'
import CollectAvatar from '$supersetViews/apricot/components/CollectAvatar.vue'
import OperateArea from './components/OperateArea.vue'
import PanelConfigDialog from './components/PanelConfigDialog.vue' // 页面配置
import PatientInfoRead from '$supersetViews/apricot/common/PatientInfoRead.vue'
//js
import Configs from './config'
import  visiteFormConfigs from './config/visiteForm'
import { deepClone } from '$supersetUtils/function'
import { recentDayOptions } from '$supersetResource/js/projects/apricot/enum.js'
// 混入
import {
    mixinTable, mixinTableInner, mixinExportExcel
} from '$supersetResource/js/projects/apricot/index.js'
import { useUserConfigQueryByKey } from '$supersetResource/js/projects/apricot/useUserConfig.js'; // 获取页面配置数据

import { getFollowUpData, getFollowUpRow, getFollowUpCount } from '$supersetApi/projects/apricot/appointment/patientInfo.js'

import { getApplyDept } from '$supersetApi/projects/apricot/retrieve'

import { getPatientInfo } from '$supersetApi/projects/apricot/common' // 获取个人信息
import { useGetHospitalData, useGetMachineRoomData, useGetItemData, useChangeHospital, useChangeMachineRoom, 
    getReportAboveDrData} from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
//状态管理
import { createNamespacedHelpers } from 'vuex'
const { mapMutations } = createNamespacedHelpers('apricot/followupVisits')

export default {
    name: 'apricot_FollowUpVisitMng',
    mixins: [ mixinTable, mixinTableInner, mixinExportExcel],
    components: {
        ScrollPane,
        TableAvatar,
        CollectAvatar,
        PatientInfoRead,
        OperateArea,
        PanelConfigDialog,
        Search, RefreshRight,Upload, Setting, ArrowDown,Tickets
    },
    
    data () {
        return {
            DA0: Configs.DA0,
            textList: Configs.textListConfig,
            isMixinDynamicGetTableHead: true,   // 是否动态获取表头
            condition: {
                dAppointmentTimeSt: new Date(),
                dAppointmentTimeEd: new Date(),
                // sVisitState: ''
            },
            pickerOptionsAppointDayStart: {
                // disabledDate: time => {
                //     if (this.condition.dAppointmentTimeEd) {
                //         return time.getTime() > new Date(this.condition.dAppointmentTimeEd).getTime()
                //     }
                //     return time.getTime()
                // }
            },
            pickerOptionsAppointDayEnd: {
                // disabledDate: time => {
                //     if (this.condition.dAppointmentTimeSt) {
                //         return time.getTime() < new Date(this.condition.dAppointmentTimeSt).getTime()
                //     }
                //     return time.getTime()
                // }
            },
            patientTable: Configs.patientTable,
            searchStyle: Configs.searchStyle,
            DA1: Configs.DA1,
            currentProcess: '',
            selectedMenu: {}, // 选择右键
            layerSearch: {
                visible: false,
                loadDone: false,
            },
            dialog: { // 弹窗显示状态
                editInfo: false
            },
            optionsLoc: {
                recentDayOptions: recentDayOptions,
                deviceItemOptions: [],
                districtMachineRoomOptions: [],
                sApplyDepartTextOptions: [],
                sReporterNameOptions: [],
                sVisitStateOptions: [],
                districtArrOption: [], //院区
                machineRoomArrOption: [], // 机房
                itemsArrOption: [],    // 项目
            },
            multipleSelection: [],
            initPatientInfo:{},
            MngIndexPenalConfig:{
                rightTabPanels:[],
            },
            iModuleId: 9,
            dialog_PatientInfoModifi_v: false,
            followupCount: 0,
            page: {  // 分页	
                pageCurrent: 1,
                pageSize: localStorage.followUpVisitMngIndexPageSize ? JSON.parse(localStorage.followUpVisitMngIndexPageSize) : 30,
                total: 0
            },
        }
    },
    
    provide () {
        return {
            patientInfo: computed(() => this.editLayer.selectedItem),
            mxGetTableList: this.mxGetTableList,
            configValue: computed(() => this.MngIndexPenalConfig),
            iModuleId: this.iModuleId
        }
    },
    watch: {
        'page.pageSize'(val) {
            localStorage.setItem('followUpVisitMngIndexPageSize', val);
        },
        'workStation': {
            async handler (val, oldVal) {
                if (val) {
                     // 赋值院区Id到查询条件
                    this.condition.sDistrictId = val.districtId;
                    // 清空机房、项目查询条件
                    this.condition.sMachineryRoomId = '';
                    // 获取患者表格数据
                    oldVal && this.mxDoSearch()
                    await this.useGetMachineRoomData(val.districtId);
                    if(val.roomId) {
                        // 赋值机房Id到查询条件
                        const aHasRoom = this.optionsLoc.machineRoomArrOption.filter(item => item.sId == this.workStation.roomId)
                        aHasRoom.length && (this.condition.sMachineryRoomId = this.workStation.roomId);
                    }
                    if(this.optionsLoc.machineRoomArrOption.length) {
                        // 判断工作站的机房id是存在于查询到的机房数据里
                        var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === this.condition.sMachineryRoomId);
                        // 院区匹配到机房，查询检查项目
                        this.useGetItemData(target?.sDeviceTypeId);
                    } else {
                        // 匹配不到机房，清空项目
                        this.optionsLoc.itemsArrOption = []
                    }
                }
            },
            
        }
    },

    computed:{
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];         
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
        
        workStation () {
            let temp = this.$store.getters['user/workStation'];
            return temp
        },
    },
    methods: {
        ...mapMutations([
            'setPatientInfo',
            'setCurrentModule'
        ]),
        // 导出, 处理数据并发起导出请求
        doExportTable(isAnonymous = false) {
            // 复制查询条件
            let condition = this.handleTransCondition(this.condition);

            const sModuleName = 'followup';

            const filename = '随访管理';
            // 调用统一混入导出方法   
            this.mxDoExportTableByModule(condition, sModuleName, isAnonymous, filename);
        },
        // 导出随访, 处理数据并发起导出请求
        doSeparateExportTable(isAnonymous = false) {
            // 复制查询条件
            let condition = this.handleTransCondition(this.condition);

            const sModuleName = 'followup';

            const filename = '随访管理';

            let otherList = this.getExportOtherFileds();
            
            // 调用统一混入导出方法   
            this.mxDoExportTableByModule(condition, sModuleName, isAnonymous, filename, otherList);
        },
        // 获取表单配置数据，并重组；
        getExportOtherFileds () {
            let name = 'visiteForm';
            let oStorageData = this.$store.getters['user/personalOnlineStorage'][this.iModuleId][name];
            let formSetList =  oStorageData?.list || visiteFormConfigs.followRecordInput;
            let targetList = [];
            let oKeyToName = {
                sDoctorId: 'sDoctorName',
                sFollowMode: 'sFollowModeText',
                iIsAccord: 'sIsAccordText',
                sVisitState: 'sVisitStateText',
            }
            formSetList.map(item => {
                if(item.isShow || item.isShow === undefined) {
                    let prop_ = oKeyToName[item.prop || item.sProp] || item.prop || item.sProp
                    let tempObject = {
                        prop: prop_,
                        label: item.label || item.sLabel,
                        width: '120px',
                        isExport: true
                    }
                    targetList.push(tempObject)
                }
            });
            return targetList
        },

        handleSelectionChange(rows) {
            this.multipleSelection = rows;
        },
        handleSortChange( ) {

        },
        handleOnClickRow(row) {
            this.onClickRow(row)
            this.getPatientInfoData(row.sId)
        },
        // 联级选择并失去焦点
        onCascaderChange (ref) {
            this.mxDoSearch();
            this.$refs[ref][0].dropDownVisible = false;
        },
        openProcess (componentName, patientInfo) {
            // 把选中的患者存入到 store 中 patientInfo 对象中
            this.setPatientInfo({
                patientInfo
            })
            this.setCurrentModule({
                name: componentName
            })
            this.currentProcess = componentName
        },
        closeProcess () {
            this.currentProcess = ''
        },
        changeTimes (val) {
            let target = this.optionsLoc.recentDayOptions.find(item => item.keyWord == val);
            if(target) {
                this.condition.dAppointmentTimeSt = moment().add(target.dates[0], 'days').toDate();
                this.condition.dAppointmentTimeEd = moment().add(target.dates[1], 'days').toDate();
                localStorage.setItem('visitSearchKey', val);
            } else {
                this.condition.dAppointmentTimeSt = '';
                this.condition.dAppointmentTimeEd = '';
                localStorage.setItem('visitSearchKey','')
            }
            this.mxDoSearch()
        },
        changeFollowUpTime (val) {
            this.mxDoSearch()
        },
        getFollowUpCount(data) {
            getFollowUpCount(data).then(res => {
                if(res.success) {
                    this.followupCount = res.data;
                    return
                }
                this.followupCount = 0;
                this.$message.error(res.msg);
            }).catch(err => {
                console.log('getFollowUpCount:', err);
                this.followupCount = 0;
            })
        },
        // 处理condition入参
        handleTransCondition(data) {
            let condition = deepClone(data) || {};
            let dAppointmentTimeSt = condition.dAppointmentTimeSt;
            condition.dAppointmentTimeSt = this.mxFormateOneDayStart(dAppointmentTimeSt);

            // 当选择了结束时间，转换成23：59：59
            let dAppointmentTimeEd = condition.dAppointmentTimeEd;
            condition.dAppointmentTimeEd = this.mxFormateOneDayEnd(dAppointmentTimeEd);

            let dReportCommitTimeSta = condition.dReportCommitTimeSta;
            condition.dReportCommitTimeSta = this.mxFormateOneDayStart(dReportCommitTimeSta);

            let dReportCommitTimeEnd = condition.dReportCommitTimeEnd;
            condition.dReportCommitTimeEnd = this.mxFormateOneDayEnd(dReportCommitTimeEnd);

            let dFollowUpTime = condition.dFollowUpTime;
            if (dFollowUpTime) {
                condition.dFollowUpTimeSt = this.mxFormateOneDayStart(dFollowUpTime);
                condition.dFollowUpTimeEd = this.mxFormateOneDayEnd(dFollowUpTime);
                delete condition.dFollowUpTime;
            }
            delete condition.itemTree;
            delete condition.sRecentDays;
            condition.iIsCancel = 0;
            
            Object.keys(condition).map(key => {
                if(['', undefined, null].includes(condition[key])) {
                    delete condition[key]
                }
            })
            return condition
        },
        // 查询数据
        async getData (obj) {
            let params = deepClone(obj);
            params.condition = this.handleTransCondition(params.condition);
            await getFollowUpData(params).then((res) => {
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.page.total = res.data.countRow;
                    this.loading = false; 
                    if(!this.editLayer.selectedItem.sId && this.tableData.length){
                        this.getPatientInfoData(this.tableData[0].sId)
                    }
                    // 赋选中状态
                    this.mxSetSelected()
                    if(!this.tableData.length) {
                        this.initPatientInfo = {}
                    }
                    return
                }
                this.tableData = [];
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
            this.getFollowUpCount(params.condition);
        },
        updateTableInfo (id = null, idx = null) {
            const sId = id !== null ? id : this.editLayer.selectedItem.sId
            const index = idx !== null ? idx : this.editLayer.selectedItem.index
            getFollowUpRow({ sId }).then(res => {
                if (res.success) {
                    let data = res.data;
                    data.index = index;
                    this.tableData.splice(index, 1, data);
                    this.editLayer.selectedItem = this.tableData[index]; // 更新选中值
                    this.$refs.mainTable && this.$refs.mainTable.setCurrentRow(this.tableData[index]) // 设置选中值
                    return;
                }
            });

            let condition = this.handleTransCondition(this.condition);
            this.getFollowUpCount(condition);
        },
        getApplyDept () {
            if (this.optionsLoc.sApplyDepartTextOptions.length) {
                return
            }
            getApplyDept().then(res => {
                if (res.success) {
                    this.optionsLoc.sApplyDepartTextOptions = res.data || [];
                    return
                }
                this.$message.error(res.msg);
            })
        },
        
        // 获取个人信息
        getPatientInfoData(sId) {
            const params = {
                sId: sId
            }
            getPatientInfo(params).then( res=>{
                if(res.success) {
                    this.initPatientInfo = res.data
                }
            }).catch( err=>{
                console.log(err)
            })
        },
        handleResetCondition() {
            this.resetCondition()
            this.mxDoSearch()
        },
        resetCondition() {
            let visitSearchKey = localStorage.getItem('visitSearchKey')
            this.condition.sDistrictId = this.workStation.districtId;
            this.useGetMachineRoomData(this.workStation.districtId)
            let cacheConfigList = this.$refs.searchRef.react.tableData;
            let hasStartTimeInput = cacheConfigList.filter(item => item.isShow && item.sProp === 'dAppointmentTimeSt').length;
            let hasEndTimeInput = cacheConfigList.filter(item => item.isShow && item.sProp === 'dAppointmentTimeEd').length;
            if(visitSearchKey) {
                let target = this.optionsLoc.recentDayOptions.find( item => item.keyWord == visitSearchKey)
                this.condition.sRecentDays = target.sValue
                this.condition.dAppointmentTimeSt = hasStartTimeInput ? moment().add(target.dates[0], 'days').toDate() : '';
                this.condition.dAppointmentTimeEd = hasEndTimeInput ? moment().add(target.dates[1], 'days').toDate() : '';
            } else {
                this.condition.dAppointmentTimeSt = hasStartTimeInput ? new Date() : '';
                this.condition.dAppointmentTimeEd = hasEndTimeInput ? new Date() : '';
            }
        },
          // 打开患者信息弹窗
        openPatientInfoModifi () {
            //打开弹窗
            this.dialog_PatientInfoModifi_v = true
        },
        // 关闭个人信息弹框
        closePatientInfoModifi() {
            this.dialog_PatientInfoModifi_v = false
        },
        useGetHospitalData, 
        useGetMachineRoomData, 
        useGetItemData,
        useChangeHospital,
        useChangeMachineRoom,
    },
    created () {
        this.getApplyDept()
    },
    async mounted () {
        let useUserConfigQuery = useUserConfigQueryByKey()
        // reportDoctors
        const doctors =  await getReportAboveDrData()
        this.optionsLoc['sReporterNameOptions'] = doctors['reportDoctors']
        await useUserConfigQuery('VisitedMngPenalConfig', this, Configs.VisitedMngPenalConfig);

        // 查询院区、与院区匹配的机房数据
        await this.useGetHospitalData();
        this.useGetMachineRoomData(this.workStation.districtId);
        // 查询项目
        this.useGetItemData()
        // 初始化查询条件
        this.resetCondition()
        this.mxGetTableList();
        this.optionsLoc['sVisitStateOptions'] = this.$store.getters['dict/map'].ApricotReportFollowUp || [];

    },
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
$borderColor: #cdcecf;
$borderColor2: #eeeeee;
$bgColor1: #7ecef4;
.c-template {
    position: relative;
}
.c-star {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .i-star {
        font-size: 24px;
        color: #ffc43e;
    }
}

.plan-item-01 {
    display: flex;
    align-items: center;
    border-top: 1px solid #eee;
    .c-checkbox {
        display: inline-block;
        margin-right: 15px;
    }
}
.el-cascader {
    line-height: inherit;
}
.action-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item-box {
        display: flex;
        align-items: center;
    }
}

.c-right-item {
    padding: 10px;
    background-color: #fff;
    .c-text-title {
        position: relative;
        h3 {
            padding: 10px 0;
            margin: 0;
            text-align: center;
           
        }
        .i-button {
            position: absolute;
            top: 11px;
            right: 8px;
        }
    }
}
.i-text {
    color: var(--el-color-primary);
    font-size: 16px;
}
</style>
