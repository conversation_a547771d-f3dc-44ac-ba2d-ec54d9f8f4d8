import request, {
    baseURL,
    stringify
}  from '$supersetUtils/request'
// 注射模块
export default {
    getInjectInit,
    saveInject,
    editInject,
    delInject,
    saveGlucoseMeasure,
    getInjectData(data) {
        return request({
            url: baseURL.apricot + '/injection/info/find/key',
            method: 'POST',
            data
        })
    },

    

    // 血糖给药信息操作接口: Drug Delivery Controller
    // 获取血糖给药信息
    drugDeliveryPage(data) {
        return request({
            url: baseURL.apricot + '/drug/delivery/find/page',
            method: 'POST',
            data
        })
    },
    // 保存血糖给药信息
    saveDrugDelivery(data) {
        return request({
            url: baseURL.apricot + '/drug/delivery/add',
            method: 'POST',
            data
        })
    },
    // 批量保存血糖给药信息
    saveBatchDrugDelivery(data) {
        return request({
            url: baseURL.apricot + '/drug/delivery/addBatch',
            method: 'POST',
            data
        })
    },
    // 编辑血糖给药信息
    editDrugDelivery(data) {
        return request({
            url: baseURL.apricot + '/drug/delivery/edit',
            method: 'POST',
            data
        })
    },
    // 删除血糖给药信息
    delDrugDelivery(params) {
        return request({
            url: baseURL.apricot + '/drug/delivery/del',
            method: 'POST',
            params
        })
    },

    
    // 平板操作患者列表查询 : Pad Patient Info Controller
    getInjectPadData(data) {
        return request({
            url: baseURL.apricot + '/pad/patient/info/find/pad/patients',
            method: 'POST',
            data
        })
    },

    // 平板端获取一条注射行信息
    getRowInjectPad(params) {
        return request({
            url: baseURL.apricot + '/pad/patient/info/find/pad/patients/row',
            method: 'POST',
            params
        })
    },

    // 标记已注射
    setSignInject(params) {
        return request({
            url: baseURL.apricot + '/pad/patient/info/sign/inject',
            method: 'POST',
            params
        })
    },

    callOneTouch,
    machineCallReady,

}

export function getInjectInit(params) {
    return request({
        url: baseURL.apricot + '/injection/info/find/init',
        method: 'POST',
        params
    })
}
// 快速呼叫
export function callOneTouch(data) {
    return request({
        url: baseURL.apricot + '/call/route/one/touch',
        method: 'POST',
        data
    })
}

// 上机呼叫准备
export function machineCallReady(data) {
    return request({
        url: baseURL.apricot + '/operate/computer/getReady',
        method: 'POST',
        data
    })
}

// 活度仪数据读取
export function activityMeterRead(data) {
    return request({
        url: baseURL.apricotAssist + '/calibrator/read',
        method: 'GET',
        data
    })
}

// 保存注射信息
export function saveInject(data) {
    return request({
        url: baseURL.apricot + '/injection/info/add',
        method: 'POST',
        data
    })
}

// 编辑注射
export function editInject(data) {
    return request({
        url: baseURL.apricot + '/injection/info/edit',
        method: 'POST',
        data
    })
}

// 删除注射
export function delInject(params) {
    return request({
        url: baseURL.apricot + '/injection/info/del',
        method: 'POST',
        params
    })
}

// 血糖测量
export function saveGlucoseMeasure(data) {
    return request({
        url: baseURL.apricot + '/patient/info/glucose/measure',
        method: 'POST',
        data
    })
}

// 注射记录
export function findInjectionListByPatientId(params) {
    return request({
        url: baseURL.apricot + '/injection/info/findInjectionListByPatientId',
        method: 'POST',
        data: stringify(params)
    })
}
