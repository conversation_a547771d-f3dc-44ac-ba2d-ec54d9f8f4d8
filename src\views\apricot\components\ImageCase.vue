<template>
    <el-dialog :close-on-click-modal="false"
        title="图像管理"
        v-model="visible"
        append-to-body
        align-center
        width="99vw"
        destroy-on-close
        @open="openDialog"
        @close="closeDialog"
        class="my-dialog image-case-dialog">
            <DragAdjust :dragAdjustData="DA0" class="g-content">
                <template v-slot:c1>
                    <LayoutTable v-bind="$attrs">
                        <template v-slot:header>
                            <SearchList v-model:modelValue="condition" :list="searchStyle" :optionData="optionsLoc"
                                :iModuleId="iModuleId" storageKey="ImageCaseSearch" labelWidth="130px"
                                @changeSearch="mxDoSearch">
                                <template v-slot:studyDate="{style}">
                                    <el-date-picker v-model="condition.studyDate"
                                        value-format="YYYYMMDD" @change="mxDoSearch" :style="style"></el-date-picker>
                                </template>
                                <template v-slot:createTime="{style}">
                                    <el-date-picker v-model="condition.createTime"
                                        value-format="YYYY-MM-DD" @change="mxDoSearch" :style="style"></el-date-picker>
                                </template>
                            </SearchList>
                        </template>
                        <template v-slot:action>
                            <div class="text-right" style="margin: 0 -10px;">
                                <div class="float-left">
                                    <el-popover
                                        :visible="isShowPop"
                                        ref="popoverRef"
                                        placement="bottom-end"
                                        title="节点列表"
                                        :width="300">
                                        <div class="i-checkBox">
                                            <el-checkbox-group v-if="nodeOptions.length"
                                                v-model="selectNodes">
                                                <div v-for="(item, index) in nodeOptions" :key="index">
                                                    <el-checkbox
                                                        :label="item.sId"
                                                        :key="item.sId">
                                                            <span>{{item.sServerName}}</span>  
                                                            <span v-if="item.sIp" class="i-text">{{ `(${item.sIp})`}}</span>  
                                                        </el-checkbox>
                                                </div>
                                            </el-checkbox-group>
                                           <el-empty v-else :image-size="80" description=" " />

                                           <div class="text-right mt-2">
                                                <el-button-icon-fa type="primary"
                                                    icon="el-icon-check"
                                                    :disabled="nodeOptions.length === 0"
                                                    @click="onSend">确定</el-button-icon-fa>
                                                <el-button-icon-fa 
                                                    icon="el-icon-close"
                                                    @click="isShowPop = false">关闭</el-button-icon-fa>
                                           </div>
                                        </div>
                                        <template #reference>
                                            <el-button-icon-fa icon="el-icon-position" @click="onSendPopClick">
                                                发送图像
                                            </el-button-icon-fa>
                                        </template>
                                    </el-popover>
                                   
                                    <el-button-icon-fa icon="el-icon-delete"  @click="onDelete">
                                        删除图像
                                    </el-button-icon-fa>
                                </div>
                                <el-button type="primary"
                                    :loading="loading"
                                    @click="mxDoSearch">
                                    <template #icon>
                                        <Icon name="el-icon-search" color="white"></Icon>
                                    </template>
                                    查询
                                </el-button>
                            </div>
                        </template>
                        <template v-slot:content>
                            <el-table-extend v-loading="loading" :data="tableData" ref="mainTable"  stripe border 
                                :row-class-name="mxRowClassName" @row-click="onClickRow"
                                @sort-change="mxOnSort" highlight-current-row height="100%" style="width: 100%"
                                :iModuleId="iModuleId" storageKey="ImageCaseTable">
                                <el-table-column fixed type="selection" label="选择" prop="_select" align="center" width="50">
                                </el-table-column>
                                <!-- <el-table-column type="selection"
                                    align="center"
                                    width="50">
                                </el-table-column> -->
                                <el-table-column align="center" label="序号" type="index" width="60">
                                </el-table-column>
                                <el-table-column v-for="(item,index) in tableHeaderProps"
                                    :key="index"
                                    :prop="item.sProp"
                                    :label="item.sLabel"
                                    :min-width="item.sMinWidth"
                                    :align="item.sAlign"
                                    show-overflow-tooltip>
                                    <template v-slot="scope">
                                        <template v-if="item.sProp.slice(0,1) === 'd'">
                                            {{ scope.row[`${item.sProp}`] | mxToDate() }}
                                        </template>
                                        <template v-else>
                                            {{ scope.row[`${item.sProp}`] }}
                                        </template>
                                    </template>
                                </el-table-column>
                            </el-table-extend>
                        </template>
                        <template v-slot:footer>
                            <el-pagination class="mt-2" background @size-change="onSizeChange" @current-change="onCurrentChange"
                                :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5"
                                :page-size="page.pageSize" layout="total, sizes, prev, pager, next" :total="page.total">
                            </el-pagination>
                        </template>
                    </LayoutTable>
                </template>
                <template v-slot:c2>
                    <div class="m-flexLaout-ty c-item-02">
                        <div class="g-flexChild">
                            <ImageStudy :params="editLayer.selectedItem"
                                :iModuleId="iModuleId"
                                :modelValue="visible" 
                                @closeDialog="closeDialog"
                                @isUpdateRowData="isUpdateRowData"></ImageStudy>
                        </div>
                    </div>
                </template>
            </DragAdjust>
    </el-dialog>
</template>
<script>
import ImageStudy from '$supersetViews/apricot/components/ImageStudy.vue'

import { transformDate, deepClone } from '$supersetUtils/function'

import { mixinTable, openWebReadImgOrRebuild } from '$supersetResource/js/projects/apricot/index.js'
import Api, { delByStudyUid, sendByStudyUid } from '$supersetApi/projects/apricot/case/report.js'
import { getDicomNode } from '$supersetApi/projects/apricot/system/dicomSet.js'
export default {
    name: 'ImageCase',
    mixins: [mixinTable],
    components: {
        ImageStudy,
    },
    props: {
   
        dialogVisible: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
            DA0: {
                type: 't-x',
                localStorageKey: '************',
                panelConfig: [{
                    size: 0,
                    minSize: 50,
                    name: 'c1',
                    isFlexible: true
                },
                {
                    size: window.innerWidth / 2,
                    minSize: 588,
                    maxSize: window.innerWidth / 4 * 3,
                    name: 'c2',
                    isFlexible: false

                }]
            },
            visible: false,
            tablePartData: [],
            searchStyle:[
                {
                    sProp: 'patientName',
                    sLabel: '姓名拼音',
                    iLayourValue: 4
                },
                {
                    sProp: 'patientId',
                    sLabel: 'PatientId',
                    iLayourValue: 4
                },
                {
                    sProp: 'accessionNumber',
                    sLabel: 'AccessionNumber',
                    iLayourValue: 4
                },
                {
                    sProp: 'studyInstanceUid',
                    sLabel: 'StudyUid',
                    iLayourValue: 4
                },
                {
                    sProp: 'studyDate',
                    sLabel: '检查日期',
                    iLayourValue: 4,
                    iCustom: 1,
                },
                {
                    sProp: 'relatedMark',
                    sLabel: '是否关联', 
                    sOptionProp: 'relatedMark',
                    sInputType: 'option',
                    iLayourValue: 4,
                },
                {
                    sProp: 'createTime',
                    sLabel: '导入日期',
                    iLayourValue: 4,
                    iCustom: 1,
                },
                {
                    sProp: 'patientInfoName',
                    sLabel: '病例姓名',
                    iLayourValue: 4
                },
                {
                    sProp: 'nuclearNum',
                    sLabel: '核医学号',
                    iLayourValue: 4
                },
            ],
            tableHeaderProps: [
                {
                    sProp: 'patientName',
                    sLabel: '姓名拼音',
                    sMinWidth: '130px',
                },
                {
                    sProp: 'patientSex',
                    sLabel: '性别',
                    sMinWidth: '60px',
                    sAlign: 'center'
                },
                {
                    sProp: 'patientBirthDate',
                    sLabel: '生日',
                    sMinWidth: '120px',
                    sAlign: 'center'
                },
                {
                    sProp: 'patientAge',
                    sLabel: '年龄',
                    sMinWidth: '80px',
                    sAlign: 'center'
                },
                {
                    sProp: 'patientWeight',
                    sLabel: '体重',
                    sMinWidth: '80px',
                    sAlign: 'center'
                },
                {
                    sProp: 'patientId',
                    sLabel: 'PatientId',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'accessionNumber',
                    sLabel: 'AccessionNumber',
                    sMinWidth: '150px',
                },
                {
                    sProp: 'studyInstanceUid',
                    sLabel: 'StudyUid',
                    sMinWidth: '150px',
                },
                {
                    sProp: 'studyDate',
                    sLabel: '检查日期',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'studyTime',
                    sLabel: '检查时间',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'modality',
                    sLabel: 'Modality',
                    sMinWidth: '100px',
                    sAlign: 'center'
                },
                {
                    sProp: 'studyDesc',
                    sLabel: 'StudyDesc',
                    sMinWidth: '100px',
                    sAlign: 'center'
                },
                {
                    sProp: 'studyId',
                    sLabel: 'StudyId',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'createTime',
                    sLabel: '导入日期',
                    sMinWidth: '170px',
                },
                {
                    sProp: 'related',
                    sLabel: '是否关联',
                    sMinWidth: '90px',
                    sAlign:'center'
                },
                {
                    sProp: 'memo',
                    sLabel: '关联描述',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'patientInfoName',
                    sLabel: '病例姓名',
                    sMinWidth: '130px',
                },
                {
                    sProp: 'nuclearNum',
                    sLabel: '核医学号',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'appointmentDate',
                    sLabel: '预约日期',
                    sMinWidth: '170px',
                },
                {
                    sProp: 'deviceTypeName',
                    sLabel: '设备类型',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'itemName',
                    sLabel: '检查项目',
                    sMinWidth: '120px',
                },
            ],
            optionsLoc: {
                relatedMark: [{
                    sName: '全部',
                    sValue: ''
                }, {
                    sName: '是',
                    sValue: 1
                }, {
                    sName: '否',
                    sValue: 0
                }]
            },
            condition: {
                createTime: moment().format('YYYY-MM-DD'),
                relatedMark: '',
            },
            clientParams: {
                needVerifyLicence: null,//阅图是否需要授权
                verificationStatus: null, // 授权状态
                clientId: null,
            },
            nodeOptions: [],
            selectNodes: [],
            nodeLoading: false,
            isShowPop: false
            
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    watch: {
        dialogVisible () {
            this.visible = this.dialogVisible;
        }
    },
    methods: {
        openDialog () {
            this.mxDoSearch()
        },
        closeDialog () {
            this.isShowPop = false;
            this.$emit('update:dialogVisible', false);
        },
        onSendPopClick() {
            const selections = this.$refs.mainTable?.getSelectionRows();
            if(!selections.length) {
                this.$message.warning('请勾选表格数据！');
                this.$nextTick(() => {
                    this.isShowPop = false;
                })
                return
            }
            this.isShowPop = true;
            this.getDicomNode();
        },
        async onSend() {
            const selections = this.$refs.mainTable?.getSelectionRows();
            if(!selections.length) {
                this.$message.warning('请勾选表格数据！');
                return
            }
            const selectNodes = this.nodeOptions.filter(item => this.selectNodes.includes(item.sId)) ;
            if(!selectNodes.length) {
                this.$message.warning('请勾选发送节点！');
                return
            }
            var isStopSend = false;
            var loading = this.loadFindTip('发送中...');
            var count = 0;
            for(var i = 0; i < selections.length; i++){
                if (isStopSend) {
                    // 停止循环；
                    break;
                }
                const item = selections[i]
                for(var j = 0; j < selectNodes.length; j++){
                    if (isStopSend) {
                        // 停止循环；
                        break;
                    }
                    const node = selectNodes[j];
                    const jsonData = {
                        iPort: node.iPort,
                        sAETitle: node.sAETitle,
                        sIp: node.sIp,
                        studyInstanceUid: item.studyInstanceUid
                    }
                    await sendByStudyUid(jsonData).then(res => {
                        if(res.success)  {
                            count++
                        } else {
                            isStopSend = true;
                            loading.close();
                        }
                    }).catch(err => {
                        isStopSend = true;
                        loading.close();
                    })
                }
            }
            if(count === selections.length * selectNodes.length) {
                this.$message.success('完成发送！')
                loading.close();
                this.isShowPop = false;
            }
        },
        onDelete() {
            const selections = this.$refs.mainTable?.getSelectionRows();
            if(!selections.length) {
                this.$message.warning('请勾选表格数据！');
                return
            }
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(async () => {
                let isStopSend = false;
                let loading = this.loadFindTip('删除中...');
                let count = 0;
                for(let j = 0; j < selections.length; j++){
                    if (isStopSend) {
                        // 停止循环；
                        break;
                    }
                    const item = selections[j];
                    const jsonData = {
                        studyDate: item.studyDate,
                        studyUid: item.studyInstanceUid
                    }
                    await delByStudyUid(jsonData).then(res => {
                        if(res.success)  {
                            count++;
                        } else {
                            isStopSend = true;
                            loading.close();
                        }
                    }).catch(err => {
                        isStopSend = true;
                        loading.close();
                    })
                    
                }
                if(count === selections.length ) {
                    this.$message.success('完成删除！');
                    loading.close();
                    this.mxDoRefresh();
                }
            })
        },
        // 加载状态
        loadFindTip(text) {
            return this.$loading({
                lock: true,
                text: text || '文件生成中...',
                background: 'rgba(255, 255, 255, 0.5)',
                customClass: 'my-loading',
            });
        },
        isUpdateRowData () {
            const params = {
                condition: {
                    studyInstanceUid: this.editLayer.selectedItem.studyInstanceUid
                },
                orders: {
                    orderInfoList: []
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 1
                }
            }
            const uId = this.editLayer.selectedItem.studyInstanceUid
            const index = this.editLayer.selectedItem.index
            Api.getImgPatientData(params).then((res) => {
                this.loading = false;
                if (res.success) {
                    const data = res.data?.recordList?.[0] || this.editLayer.selectedItem;
                    data.index = index;
                    this.tableData.splice(index, 1, data);
                    if(uId !== this.editLayer.selectedItem.studyInstanceUid) return
                    this.editLayer.selectedItem = this.tableData[index]; // 更新选中值
                    this.$refs.mainTable && this.$refs.mainTable.setCurrentRow(this.tableData[index]) // 设置选中值
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
            })
        },
        // 获取所有检查数据
        getData (data) {
            if (!this.dialogVisible) {
                return;
            }
            let params = deepClone(data);
            Object.keys(params.condition).map(item => {
                if(params.condition[item] === '') {
                    delete params.condition[item]
                }
            })
            Api.getImgPatientData(params).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.page.total = res.data.countRow;
                    // 赋选中状态
                    this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg);
                this.tableData = [];
            }).catch(() => {
                this.loading = false;
                this.tableData = [];
            })
        },
        getDicomNode () {
            if(this.nodeOptions.length) return;
            this.nodeLoading = true;
                getDicomNode().then(res => {
                this.nodeLoading = false;
                if (res.success) {
                    this.nodeOptions = res.data || [];
                    return
                }
            }).catch(err => {
                this.nodeLoading = false;
                console.log(err)
            })
        },
    },
}
</script>
<style lang="scss" scoped>
// :global(.my-dialog.image-case-dialog) {
//     height: 99vh;
// }

.g-content {
    width: 100%;
    height: 91vh;
    :deep(.table-box .action) {
        border: none;
    }

    .g-flexChild {
        overflow: hidden;
    }
    
    .c-item-01, .c-item-02 {
        overflow: hidden;
    }
}
.i-text {
    color: #666;
    padding-left: 10px;
}

:deep(.el-pagination) {
    padding-right: 0;
}
</style>
