<template>
    <div class="c-btn" style="" v-if="btnName && btnUrl" @click="openUTD">
        <img :src="`${url}/img/utd.png?v=${version}`" />
        <span>{{btnName}}</span>
    </div>
</template>
<script>
import { getBPMSetFindKeys } from '$supersetApi/projects/apricot/system/bpmSet.js'
export default {
    name: "UTD",
    props: {
    },
    data () {
        return {
            url: window.location.origin + window.location.pathname,
            version: new Date().getTime(),
            btnName: '',
            btnUrl: ''
        }
    },
    methods: {
        openUTD() {
            window.open(this.btnUrl, '_blank');
        },
        // 获取按钮信息
        getButtonMsg() {
            const params = ['UTDButton']
            getBPMSetFindKeys(params).then(res => {
                if(res.success) {
                    const data = res?.data?.[0] || {};
                    let arr = (data.sValue || '').split(';');
                    this.btnName = arr[0];
                    this.btnUrl = arr[1];
                }
            }).catch(err => {
                console.log(err);
            })
        }
    },
    mounted () {
        this.getButtonMsg();
    }
}
</script>

<style lang="scss" scoped>
.c-btn {
    display: flex;
    align-items: center; 
    height: 34px;
    padding: 0 2px;
    cursor: pointer;
    >img {
        height: 22px;
    }
    >span {
        line-height: 22px;
    }
    &:hover{
        background-color: rgba(0, 0, 0, 0.1);
    } 
}
</style>