import request, {  baseURL, stringify } from '$supersetUtils/request'
// 报告模块
export default {
    // 报告查询
    queryCaseReport(params) {
        return request({
            url: baseURL.apricot + '/process/queryCaseReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 带锁报告
    queryAndLockCaseReport(params) {
        return request({
            url: baseURL.apricot + '/process/queryAndLockCaseReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 报告锁定
    lockReport(params) {
        return request({
            url: baseURL.apricot + '/process/lockReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 解锁报告
    unlockReport(params) {
        return request({
            url: baseURL.apricot + '/process/unlockReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 刷新锁时间
    updateLockTime(params) {
        return request({
            url: baseURL.apricot + '/process/updateLockTime',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 审核报告
    auditReport(params) {
        return request({
            url: baseURL.apricot + '/process/auditReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 撤审报告
    cancelAuditReport(params) {
        return request({
            url: baseURL.apricot + '/process/cancelAuditReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 撤销提交报告
    cancelCommitReport(params) {
        return request({
            url: baseURL.apricot + '/process/cancelCommitReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 撤销复审报告
    cancelRecheckReport(params) {
        return request({
            url: baseURL.apricot + '/process/cancelRecheckReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 提交报告
    commitReport(params) {
        return request({
            url: baseURL.apricot + '/process/commitReport',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 编辑报告
    editReport(data) {
        return request({
            url: baseURL.apricot + '/process/editReport',
            method: 'POST',
            data
        })
    },
    // 复审报告
    finalAuditReport(params) {
        return request({
            url: baseURL.apricot + '/process/finalAuditReport',
            method: 'POST',
            data: stringify(params)
        })
    },

    // 发布
    reportRelease(params) {
        return request({
            url: baseURL.apricot + '/project/and/patient/report/release',
            method: 'post',
            data: stringify(params)
        })
    },
    // 取消发布
    reportReleaseCancel(params) {
        return request({
            url: baseURL.apricot + '/project/and/patient/report/release/cancel',
            method: 'post',
            data: stringify(params)
        })
    },

    // 病灶描述表操作接口 : Lesion Describe Controller

    getLesionDescribeData(data) {
        return request({
            url: baseURL.apricot + '/lesion/describe/find/page',
            method: 'POST',
            data
        })
    },

    addLesionDescribe(data) {
        return request({
            url: baseURL.apricot + '/lesion/describe/addBatch',
            method: 'POST',
            data
        })
    },

    editLesionDescribe(data) {
        return request({
            url: baseURL.apricot + '/lesion/describe/edit',
            method: 'POST',
            data
        })
    },

    delLesionDescribe(params) {
        return request({
            url: baseURL.apricot + '/lesion/describe/del',
            method: 'POST',
            data: stringify(params)
        })
    },

    sortLesionDescribe(params) {
        return request({
            url: baseURL.apricot + '/lesion/describe/sort',
            method: 'POST',
            data: stringify(params)
        })
    },

    autoSortLesionDescribe(params) {
        return request({
            url: baseURL.apricot + '/lesion/describe/autoSort',
            method: 'POST',
            data: stringify(params)
        })
    },

    // 历史患者信息操作接口 : Past Patient Info Controller
    getSimilarPatient(data) {
        return request({
            url: baseURL.apricot + '/past/patient/info/similar/patient',
            method: 'post',
            data
        })
    },
    // 获取患者历史报告内容
    getPatientResultById,

    // 教学病例 
    setTeachcase(params) {
        return request({
            url: baseURL.apricot + '/project/and/patient/report/teachcase/switch/',
            method: 'POST',
            data: stringify(params)
        })
    },

    // 修改痕迹操作接口 : Modify Trace Controller
    getModifyTraceData(data) {
        return request({
            url: baseURL.apricot + '/modify/trace/find/page',
            method: 'post',
            data
        })
    },

    // 查询选中记录与上一次修改的对比
    getModifyTraceCompare(params) {
        return request({
            url: baseURL.apricot + '/modify/trace/find/compare',
            method: 'post',
            data: stringify(params)
        })
    },

    // 查询任意两份修改记录
    getModifyTraceContrast(params) {
        return request({
            url: baseURL.apricot + '/modify/trace/find/contrast',
            method: 'post',
            data: stringify(params)
        })
    },


    // 报告锁
    reportLock(params) {
        return request({
            url: baseURL.apricot + '/patient/info/report/lock',
            method: 'post',
            data: stringify(params)
        })
    },

    reportUnlock(params) {
        return request({
            url: baseURL.apricot + '/patient/info/report/unlock',
            method: 'post',
            data: stringify(params)
        })
    },
    // 强制解锁病例
    reportUnlockForce(params) {
        return request({
            url: baseURL.apricot + '/patient/info/report/unlock/force',
            method: 'post',
            data: stringify(params)
        })
    },

    // 患者检查关联操作接口 : Patient Related Study Controller 
    getPatientStudy(data) {
        return request({
            url: baseURL.image + '/patient/related/study/find/key',
            method: 'post',
            data
        })
    },

    addPatientStudy(params) {
        return request({
            url: baseURL.image + '/ris/manual/related',
            method: 'post',
            data: stringify(params)
        })
    },

    delPatientStudy(params) {
        return request({
            url: baseURL.image + '/ris/manual/unrelated',
            method: 'post',
            data: stringify(params)
        })
    },

    // 图像关联操作接口 : Img Patient Controller

    getImgPatientData(data) {
        return request({
            url: baseURL.image + '/ris/findStudyByPage',
            method: 'post',
            data
        })
    },

    // 图像序列
    getPatientStudyInfo(params) {
        return request({
            url: baseURL.image + '/ris/find/study',
            method: 'post',
            data: stringify(params)
        })
    },
    // 图像序列
    findStudySeries(params) {
        return request({
            url: baseURL.image + '/ris/findStudySeries',
            method: 'post',
            data: stringify(params)
        })
    },

    // 图像序列
    findStudyByPatientInfoId,

    getImgPatientInfo(params) {
        return request({
            url: baseURL.image + '/img/patient/register/info',
            method: 'post',
            data: stringify(params)
        })
    },

    // 修改阴阳性，质控
    changeQualitativeOrMore(data) {
        return request({
            url: baseURL.apricot + '/process/description',
            method: 'post',
            data
        })
    },
    // 获取二维码 
    getCloudCode(data) {
        return request({
            url: baseURL.broken + '/sign/qrcode',
            method: 'post',
            data
        })
    },
    // 获取扫码结果
    getScanResult(data) {
        return request({
            url: baseURL.broken + '/sign/qrcode/status',
            method: 'post',
            data
        })
    },
    // 签名
    toSignAutograph(data) {
        return request({
            url: baseURL.broken + '/sign/sign',
            method: 'post',
            data
        })
    },
    // 禾正获取签名结果(data)
    getSignStatus(data) {
        return request({
            url: baseURL.broken + '/sign/sign/status',
            method: 'post',
            data
        })
    },
    getPictureStatus(data) {
        return request({
            url: baseURL.broken + '/sign/sign/picture/status',
            method: 'post',
            data
        })
    },
    // 发送会诊
    sendConsultation(params) {
        return request({
            url: baseURL.apricot + '/consultation/sendConsultation',
            method: 'post',
            data: stringify(params)
        })
    },
    // 取消会诊
    cancelConsultation(params) {
        return request({
            url: baseURL.apricot + '/consultation/cancelSendConsultation',
            method: 'post',
            data: stringify(params)
        })
    },
    
   
    BPMMsgCreate,
    imgPatientDicomSend,
    delSeriesUid,
    queryClientConfig,
    readClientId,
    verificationCode, 
    getModelEssayTree,
    getPrimaryModelEssayTree,
    addModelEssayTemplate,
    editModelEssayTemplate,
    delModelEssayTemplate,
    reportHighlight,
    queryCommonWords,
    addCommonWords,
    delCommonWords,
    editCommonWords,
    queryAllBookmark,
    createBookmark,
    editBookmark,
    deleteBookmark,
    
    queryModelEssayTree,
    createModelEssayTreeNode,
    saveModelEssayContent,
    deleteModelEssayTreeNode,
    editModelEssayTreeNode,
    updateSortBatch,
    bakReport
}

// 获取高亮字样
export function reportHighlight(params) {
    return request({
        url: baseURL.apricot + '/b/p/m/set/report/highlight',
        method: 'post',
        data: stringify(params)
    })
}

// 范文模板管理操作接口(新) : Model Essay Controller
export function getModelEssayTree(params) {
    return request({
        url: baseURL.apricot + '/model/essay/template/find/tree/secondary',
        method: 'POST',
        data: stringify(params)
    })
}
export function getPrimaryModelEssayTree(params) {
    return request({
        url: baseURL.apricot + '/model/essay/template/find/tree/primary',
        method: 'POST',
        data: stringify(params)
    })
}

export function addModelEssayTemplate(data) {
    return request({
        url: baseURL.apricot + '/model/essay/template/add',
        method: 'POST',
        data
    })
}

export function editModelEssayTemplate(data) {
    return request({
        url: baseURL.apricot + '/model/essay/template/update',
        method: 'POST',
        data
    })
}

export function delModelEssayTemplate(params) {
    return request({
        url: baseURL.apricot + '/model/essay/template/remove',
        method: 'POST',
        data: stringify(params)
    })
}


// 范文模板管理操作接口(2.5) 范文模板树
export function queryModelEssayTree(params) {
    return request({
        url: baseURL.apricot + '/modelEssayTree/queryModelEssayTree',
        method: 'POST',
        data: stringify(params)
    })
}
// 范文模板管理操作接口(2.5) 新增节点
export function createModelEssayTreeNode(data) {
    return request({
        url: baseURL.apricot + '/modelEssayTree/createModelEssayTreeNode',
        method: 'POST',
        data
    })
}

// 范文模板管理操作接口(2.5) 新增节点
export function editModelEssayTreeNode(data) {
    return request({
        url: baseURL.apricot + '/modelEssayTree/editModelEssayTreeNode',
        method: 'POST',
        data
    })
}

// 范文模板管理操作接口(2.5) 保存范文内容
export function saveModelEssayContent(data) {
    return request({
        url: baseURL.apricot + '/modelEssayTree/saveModelEssayContent',
        method: 'POST',
        data
    })
}
// 范文模板管理操作接口(2.5) 删除节点
export function deleteModelEssayTreeNode(params) {
    return request({
        url: baseURL.apricot + '/modelEssayTree/deleteModelEssayTreeNode',
        method: 'POST',
        data: stringify(params)
    })
}
// 范文模板管理操作接口(2.5) 删除节点
export function updateSortBatch(data) {
    return request({
        url: baseURL.apricot + '/modelEssayTree/updateSortBatch',
        method: 'POST',
        data
    })
}


// 获取患者历史报告内容
export function getPatientResultById(params) {
    return request({
        url: baseURL.apricot + '/past/patient/info/past/patient/result',
        method: 'POST',
        data: stringify(params)
    })
}

// 修改报告医生
export function reportChangeWriter(params) {
    return request({
        url: baseURL.apricot + '/process/changeWriter',
        method: 'post',
        data: stringify(params)
    })
}


// 修改审核医生
export function reportChangeAuditor(params) {
    return request({
        url: baseURL.apricot + '/process/changeAuditor',
        method: 'post',
        data: stringify(params)
    })
}

// 修改实习医生（书写医生）
export function reportChangePractice(params) {
    return request({
        url: baseURL.apricot + '/process/changePractice',
        method: 'post',
        data: stringify(params)
    })
}





// 刻录序列
export function cdburnSeries(data) {
    return request({
        url: baseURL.image + '/ris/record/series',
        method: 'post',
        data
    })
}


// 刻录
export function cdburnRecord(data) {
    return request({
        url: baseURL.apricotAssist + '/cdburn/burn',
        method: 'post',
        data
    })
}

// 导出
export function cdExportRecord(data) {
    return request({
        url: baseURL.apricotAssist + '/cdburn/export',
        method: 'post',
        data
    })
}

// 获取指定目录下的所有文件
export function cdburnGetSubFile(params) {
    return request({
        url: baseURL.apricotAssist + '/cdburn/getSubFile',
        method: 'post',
        data: stringify(params)
    })
}

// 下载.doc文件
export function fileDownandopen(params) {
    return request({
        url: baseURL.apricotAssist + '/file/downandopen',
        method: 'post',
        data: stringify(params)
    })
}
// 调用打印机打印的接口
export function downAndPrintBatch(data) {
    return request({
        url: baseURL.apricotAssist + '/file/downandprintbatch',
        method: 'post',
        data
    })
}

// 消息发送操作接口: BPM Msg Controller
export function BPMMsgCreate(data) {
    return request({
        url: baseURL.apricot + '/b/p/m/msg/create',
        method: 'post',
        data: stringify(data)
    })
}

// 消息发送操作接口: BPM Msg Controller
export function imgPatientDicomSend(data) {
    return request({
        url: baseURL.image + '/ris/dicom/send',
        method: 'post',
        data
    })
}

// 删除序列接口
export function delSeriesUid(params) {
    return request({
        url: baseURL.image + '/web/view/del/seriesUid',
        method: 'post',
        data: stringify(params)
    })
}

// 按检查ID删除图像
export function delByStudyUid(params) {
    return request({
        url: baseURL.image + '/web/view/delByStudyUid',
        method: 'post',
        data: stringify(params)
    })
}

// 按studyUid发送图像
export function sendByStudyUid(data) {
    return request({
        url: baseURL.image + '/ris/dicom/sendByStudyUid',
        method: 'post',
        data
    })
}

// 按studyUid拆分图像
export function splitStudyBySeriesDate(params) {
    return request({
        url: baseURL.image + '/ris/splitStudyBySeriesDate',
        method: 'post',
        data: stringify(params)
    })
}
// 按PatientInfoId查找图像
export function findStudyByPatientInfoId(params) {
    return request({
        url: baseURL.image + '/ris/findStudyByPatientInfoId',
        method: 'post',
        data: stringify(params)
    })
}

// 获取检验数据
export function getInspectionByPatientId(params) {
    return request({
        url: baseURL.apricot + '/inspection/set/find/sPatientId',
        method: 'post',
        data: stringify(params)
    })
}
// 填写检验数据逐行保存
export function saveInspection(data) {
    return request({
        url: baseURL.apricot + '/inspection/result/save',
        method: 'post',
        data
    })
}
// 获取客户端配置
export function queryClientConfig(data) {
    return request({
        url: baseURL.broken + '/client/queryClientConfig',
        method: 'post',
        data
    })
}
// 获取本机客户端本机主板序列号
export function readClientId(data) {
    return request({
        url: baseURL.apricotAssist + '/client/readClientId',
        method: 'get',
        data
    })
}
// 获取本机授权状态
export function verificationCode(params) {
    return request({
        url: baseURL.broken + '/client/verifyLicence',
        method: 'post',
        data: stringify(params)
    })
}

// 获取危急值表格数据
export function emergencyqueryList(data) {
    return request({
        url: baseURL.apricot + '/emergency/queryList',
        method: 'post',
        data
    })
}

// 危急值:标记撤销
export function emergencyCancel(params) {
    return request({
        url: baseURL.apricot + '/emergency/cancel',
        method: 'post',
        data: stringify(params)
    })
}
// 标记电话通知
export function emergencyPhoneUp(data) {
    return request({
        url: baseURL.apricot + '/emergency/phoneUp',
        method: 'post',
        data
    })
}

// 标记忽略
export function emergencyMarkIgnored(data) {
    return request({
        url: baseURL.apricot + '/emergency/markIgnored',
        method: 'post',
        data
    })
}

// 危急值发送
export function registerEmergency(data) {
    return request({
        url: baseURL.apricot + '/emergency/save',
        method: 'post',
        data
    })
}
// 危急值回复
export function emergencyReply(data) {
    return request({
        url: baseURL.broken + '/register/emergencyReply',
        method: 'post',
        data: stringify(data)
    })
}
// 危急值重发
export function emergencyRetry(data) {
    return request({
        url: baseURL.broken + '/register/emergencyRetry',
        method: 'post',
        data: stringify(data)
    })
}
// 旧版-废弃-图像导入-图像上传
export function importScreenshot(data) {
  return request({
      url: baseURL.image + '/web/view/importScreenshot', // baseURL.image
      method: 'post',
      headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: stringify(data)
  })
}
// 新版-图像导入-图像上传
export function importFile(data) {
  return request({
      url: baseURL.image + '/web/view/importFile', // baseURL.image
      method: 'post',
      data
  })
}
// 获取指定目录下的所有文件
export function getFilterFile(data) {
    return request({
        url: baseURL.apricotAssist + '/import/getFilterFile',
        method: 'post',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(data)
    })
}

export function getLocalFileBase64(data) {
    return request({
        url: baseURL.apricotAssist + '/import/getLocalFileBase64',
        method: 'post',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(data)
    })
}

// 病区
export function getComboInPatient(params) {
    return request({
        url: baseURL.apricot + '/patient/info/combo/inpatient', 
        method: 'post',
        data: stringify(params)
    })
}

// 分配医生

export function toAssignDoctor(data) {
    return request({
        url: baseURL.apricot + '/process/distributeReport', 
        method: 'post',
        data
    })
}
// 签名记录列表
export function elecSignatureRecordList(data) {
    return request({
        url: baseURL.apricot + '/elec/signature/record/find/list', 
        method: 'post',
        data
    })
}

// 签名验证
export function elecSignatureValidate(params) {
    return request({
        url: baseURL.apricot + '/elec/signature/record/sign/validate', 
        method: 'post',
        data: stringify(params)
    })
}

// 签名验证的统一接口
export function signVerifySignData(data) {
    return request({
        url: baseURL.broken + '/sign/verifySignData', 
        method: 'post',
        data
    })
}


// 查询报告书写常用语列表
export function queryCommonWords(params) {
  return request({
      url: baseURL.apricot + '/commonWords/queryCommonWords',
      method: 'post',
      data: stringify(params)
  })
}

// 常用语列表
export function addCommonWords(params) {
  return request({
      url: baseURL.apricot + '/commonWords/addCommonWords',
      method: 'post',
      data: (params)
  })
}

// 常用语列表
export function editCommonWords(params) {
  return request({
      url: baseURL.apricot + '/commonWords/editCommonWords',
      method: 'post',
      data: (params)
  })
}

// 常用语列表
export function delCommonWords(params) {
  return request({
      url: baseURL.apricot + '/commonWords/delCommonWords',
      method: 'post',
      data: stringify(params)
  })
}

// 结构化文字
export function queryAllBookmark(params) {
  return request({
      url: baseURL.apricot + '/structBookmark/queryAllBookmark',
      method: 'post',
      data: stringify(params)
  })
}

// 结构化文字
export function createBookmark(params) {
  return request({
      url: baseURL.apricot + '/structBookmark/createBookmark',
      method: 'post',
      data: (params)
  })
}

// 结构化文字
export function editBookmark(params) {
  return request({
      url: baseURL.apricot + '/structBookmark/editBookmark',
      method: 'post',
      data: (params)
  })
}

// 结构化文字
export function deleteBookmark(params) {
  return request({
      url: baseURL.apricot + '/structBookmark/deleteBookmark',
      method: 'post',
      data: stringify(params)
  })
}

// 备份记录
export function getReportBak(params) {
  return request({
      url: baseURL.apricot + '/report/bak/getReportBak',
      method: 'post',
      data: stringify(params)
  })
}

// 备份报告
export function bakReport(data) {
  return request({
      url: baseURL.apricot + '/report/bak/bakReport',
      method: 'post',
      data
  })
}

// 质控模板查询
export function qualityTemplateQueryAll(data) {
  return request({
      url: baseURL.apricot + '/qualityTemplate/queryAll',
      method: 'post',
      data
  })
}

// 质控模板添加
export function addQualityTemplate(data) {
  return request({
      url: baseURL.apricot + '/qualityTemplate/add',
      method: 'post',
      data
  })
}

// 质控模板修改
export function editQualityTemplate(data) {
  return request({
      url: baseURL.apricot + '/qualityTemplate/edit',
      method: 'post',
      data
  })
}

// 质控模板删除
export function delQualityTemplate(params) {
  return request({
      url: baseURL.apricot + '/qualityTemplate/del',
      method: 'post',
      data: stringify(params)
  })
}

// 质控模板内容项查询
export function qualityTemplateItemFindById(params) {
  return request({
      url: baseURL.apricot + '/qualityTemplateItem/findBysTemplateId',
      method: 'post',
      data: stringify(params)
  })
}

// 质控模板内容项删除
export function delQualityTemplateItem(params) {
  return request({
      url: baseURL.apricot + '/qualityTemplateItem/delBysItemId',
      method: 'post',
      data: stringify(params)
  })
}

// 质控模板内容项保存
export function saveQualityTemplateItem(data) {
  return request({
      url: baseURL.apricot + '/qualityTemplateItem/saveAll',
      method: 'post',
      data
  })
}

// 通过报告Id获取质量评级的内容
export function qualityReportFindByPatientId(data) {
  return request({
      url: baseURL.apricot + '/qualityReport/findBysPatientId',
      method: 'post',
      transformRequest: stringify,
      data
  })
}

// 按报告id保存质量评级的内容
export function saveQualityReport(data) {
  return request({
      url: baseURL.apricot + '/qualityReport/save',
      method: 'post',
      data
  })
}

// UKey 从后端获取报告原文
export function guangxiqurenminUKeyGetPlain(data) {
  return request({
      url: baseURL.broken + '/guangxiqurenmin/ukey/getPlain',
      method: 'post',
      data
  })
}

// UKey 从后端获取报告原文
export function guangxiqurenminUKeyVerify(data) {
  return request({
      url: baseURL.broken + '/guangxiqurenmin/ukey/verify',
      method: 'post',
      data
  })
}





// 江门 密码登录获取令牌接口
export function jiangmenzhongxinCAPinToken(data) {
  return request({
      url: baseURL.broken + '/jiangmenzhongxin/ca/pin/token/get',
      method: 'post',
      data
  })
}
// 江门 密码登录签名接口
export function jiangmenzhongxinCAPinSign(data) {
  return request({
      url: baseURL.broken + '/jiangmenzhongxin/ca/pin/sign',
      method: 'post',
      data
  })
}





