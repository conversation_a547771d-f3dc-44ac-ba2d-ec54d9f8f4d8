<template>
    <div class="c-template">
        <div class="g-header">
            <img v-if="isShowLogo"
                :src="hospitalLogoUrl"
                alt=""
                style="top:10px;left: 150px;width:auto;height: 40px;">
        </div>
        <div class="g-content">
            <div class="c-content">
                <div class="c-pic">
                    <a href="http://www.ivena.cn"
                        target="_blank"
                        rel="noopener noreferrer">www.ivena.cn</a>
                    <div class="login-left hidden-sm-and-down">
                        <div class="login-left-wrap">
                            <el-carousel class="img"
                                indicator-position="outside"
                                arrow="never">
                                <el-carousel-item>
                                    <div class="c-img-container">
                                        <img src="/img/login/01.jpg">
                                    </div>
                                </el-carousel-item>
                                <el-carousel-item>
                                    <div class="c-img-container">
                                        <img src="/img/login/02.jpg">
                                    </div>
                                </el-carousel-item>
                                <el-carousel-item>
                                    <div class="c-img-container">
                                        <img src="/img/login/03.jpg">
                                    </div>
                                </el-carousel-item>
                                <el-carousel-item>
                                    <div class="c-img-container">
                                        <img src="/img/login/04.jpg">
                                    </div>
                                </el-carousel-item>
                            </el-carousel>

                        </div>
                    </div>
                </div>

                <div class="twoLogin"
                    :class="{'i-height': hasQrcodeLogin !== '1'&& hasUKeyLogin !== '1'}">
                    <!-- 系统名称 -->
                    <div class="c-description">
                        <h4 v-text="systemName || '信息管理系统'"></h4>
                        <p v-text="systemNameEN || 'Information management system'"></p>
                    </div>
                    <div class="login">
                        <div class="login-type"
                            v-if="hasQrcodeLogin === '1' || hasUKeyLogin === '1'">
                            <span :class="currentLoginType == 1 ? 'active' : ''"
                                @click="onChangeLoginType(1)">账号登录</span>
                            <span v-if="hasQrcodeLogin ==='1'"
                                :class="currentLoginType == 2 ? 'active' : ''"
                                @click="onChangeLoginType(2)">扫码登录</span>
                            <span v-if="hasUKeyLogin ==='1'"
                                :class="currentLoginType == 3 ? 'active' : ''"
                                @click="onChangeLoginType(3)">UKey</span>
                        </div>
                        <!-- 账号密码登录 -->
                        <form class="block passwordLogin"
                            v-if="currentLoginType == 1">
                            <div class="c-input">
                                <!-- 用户名 -->
                                <div class="c-input-item">
                                    <el-input v-model="loginData.sNameOrNo"
                                        name="username"
                                        ref="nameInput"
                                        clearable
                                        size="large"
                                        @keyup.native.enter="handleNameInputEnter">
                                        <template #prefix>
                                            <div class="inline-block px-0">
                                                <Icon name="el-icon-user"
                                                    :strokeWidth="4"
                                                    size="20"
                                                    color="#999" />
                                            </div>
                                        </template>
                                    </el-input>
                                </div>
                                <!-- 密码 -->
                                <div class="c-input-item">
                                    <el-input v-model="loginData.sPass"
                                        name="password"
                                        ref="pwdInput"
                                        show-password
                                        size="large"
                                        @keyup.native.enter="handleLogin"
                                        type="password">
                                        <template #prefix>
                                            <div class="inline-block px-0">
                                                <Icon name="el-icon-lock"
                                                    :strokeWidth="4"
                                                    size="20"
                                                    color="#999" />
                                            </div>
                                        </template>
                                    </el-input>
                                </div>
                            </div>
                            <div class="c-btn">
                                <el-button size="large"
                                    type="primary"
                                    @click="handleLogin"
                                    @keyup.enter="handleLogin">登录</el-button>
                            </div>
                        </form>
                        <!-- 二维码登录 -->
                        <div class="qrlogin"
                            v-if="currentLoginType == 2">
                            <div class="code">
                                <el-image :src="base64QrCode">
                                    <template #error>
                                        <div class="image-slot">
                                            <Icon name="el-icon-picture"
                                                size="30"></Icon>
                                        </div>
                                    </template>
                                </el-image>
                                <div class="refresh"
                                    v-if="isQrOverTime">
                                    <p @click="getQRCode()">
                                        <i v-if="!qrCodeLoading"
                                            class="fa fa-refresh"
                                            style="font-size:20px">刷新</i>
                                        <i v-if="qrCodeLoading"
                                            class="el-icon-loading"
                                            style="font-size: 30px"></i>
                                    </p>

                                </div>
                            </div>
                            <p class="app">
                                <i class="fa fa-scanning"></i>
                                请打开手机
                                <span>{{ signatureAppName }}APP</span>
                                扫一扫登录
                            </p>
                        </div>
                        <!-- UKey密码登录 -->
                        <form class="block passwordLogin"
                            v-if="currentLoginType == 3">
                            <div class="c-input">
                                <!-- 用户名 -->
                                <el-select v-model="uKeyForm.userCertId"
                                    class="c-input-item"
                                    ref="uNameInput"
                                    placeholder=" "
                                    clearable
                                    size="large">
                                    <template #prefix>
                                        <div class="inline-block px-0">
                                            <Icon name="el-icon-user"
                                                :strokeWidth="4"
                                                size="20"
                                                color="#999" />
                                        </div>
                                    </template>
                                    <el-option v-for="(item, index) in uKeyUsersList"
                                        :key="index"
                                        :label="item.label"
                                        :value="item.value" />
                                </el-select>
                                <!-- 密码 -->
                                <div class="c-input-item">
                                    <el-input v-model="uKeyForm.userPwd"
                                        name="u_password"
                                        show-password
                                        size="large"
                                        @keyup.native.enter="onClickUKeyLogin"
                                        type="password">
                                        <template #prefix>
                                            <div class="inline-block px-0">
                                                <Icon name="el-icon-lock"
                                                    :strokeWidth="4"
                                                    size="20"
                                                    color="#999" />
                                            </div>
                                        </template>
                                    </el-input>
                                </div>
                            </div>
                            <div class="c-btn">
                                <el-button size="large"
                                    type="primary"
                                    @click="onClickUKeyLogin"
                                    @keyup.enter="onClickUKeyLogin">登录</el-button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
        <div class="g-footer">
            <pre v-text="copyright"></pre>
        </div>
    </div>
</template>

<script>
import { checkFileExists } from '$supersetResource/js/tools'
import { getLoginQRCode, getScanResult, qrcodeLogin } from '$supersetApi/user'
import { getBPMSetFindKeys } from '$supersetApi/projects/apricot/system/bpmSet.js'
import { setLoginUserOptions, getLoginUserOptions, removeLoginCertId, setQRCodeAuthId, removeQRCodeAuthId } from '$supersetUtils/auth'
import mixinUKeyLogin from './mixinUKeyLogin.js'

export default {
    name: 'Login',
    mixins: [mixinUKeyLogin],
    data: function () {
        return {

            copyright: 'Copyright © 2002 - 2023 杏翔科技 版权所有',
            loginData: {
                sNameOrNo: '',
                sPass: ''
            },
            systemName: window.configs.names.systemName,
            systemNameEN: window.configs.names.systemNameEN,
            loginInUserOptions: [],
            base64QrCode: '',   // 二维码路径
            sAuthId: '',        // 二维码扫码登录授权码
            loginTimer: null,   // 定时器
            isQrOverTime: false,    // 二维码是否过期
            qrCodeLoading: true,    // 二维码加载状态
            hasQrcodeLogin: '0',   // 是否配置二维码登录
            signatureAppName: '...',// 签章APP名称
            currentLoginType: 1, // 当前登录模式：1=用户名密码登录；2=二维码登录；3=UKey登录；
            isLoadingResult: false,
            hospitalLogoUrl: '',
            isShowLogo: false, // 显示医院logo
        }
    },
    methods: {
        onChangeLoginType (val) {
            this.currentLoginType = val;
            localStorage.setItem('loginIndexMode', val);
            clearInterval(this.loginTimer)
            if (this.currentLoginType == 1) {
                // 默认显示最后登录者
                this.setLastUserMessage()
            } else if (this.currentLoginType == 2) {
                this.getQRCode()
            } else if (this.currentLoginType == 3) {
                // 开启
                this.mxInitUKeyMode()
            }
        },
        // 回车键聚焦密码输入框
        handleNameInputEnter () {
            if (!this.loginData.sNameOrNo.length) {
                return
            }
            this.$refs.pwdInput.focus();
        },
        handleLogin () {
            let loginData = this.loginData;
            //表单校验
            if (!loginData.sNameOrNo) {
                this.$message.warning('请输入用户名！');
                this.$refs.nameInput.focus();
                return
            }
            if (!loginData.sPass) {
                this.$message.warning('请输入登录密码！');
                this.$refs.pwdInput.focus();
                return
            }
            //表单校验完成后
            this.sendLogin(loginData)
        },
        sendLogin (loginData) {
            let loading = this.loginLoading()
            const newParams = {
                "loginType": "PC",
                subject: loginData.sNameOrNo,
                password: loginData.sPass
            }
            this.$store.dispatch('user/login', newParams).then((res) => {
                loading.close()
                setLoginUserOptions(JSON.stringify([{ 'sNameOrNo': loginData.sNameOrNo }]))
                this.$store.commit({
                    type: "user/setSignatureAppName",
                    signatureAppName: this.signatureAppName
                });

                this.$router.push({ path: '/main/welcome_jump', query: { redirect: 'auto' } })
            }).catch((res) => {
                loading.close()
            });
        },
        loginLoading () {
            /*登录loading*/
            return this.$loading({
                lock: true,
                text: '登录请求已提交，请等待......',
                background: 'rgba(0, 0, 0, 0.35)'
            });
        },
        //获取登录二维码
        getQRCode () {
            this.qrCodeLoading = true
            let params = {}
            getLoginQRCode(params).then(res => {
                this.qrCodeLoading = false
                if (res.success) {
                    const sQrCode = res.data?.sQrCode ?? '';
                    this.base64QrCode = sQrCode.substring(0, 21).includes('data:image') ? sQrCode : `data:image/jpg;base64,${sQrCode}`;

                    this.sAuthId = res.data.sAuthId
                    this.isQrOverTime = false
                    setTimeout(() => { this.setIntervalLoginTimer() }, 1000)
                    return
                }
                this.$message.error(res.msg)
            }).finally(() => {
                this.qrCodeLoading = false
            })
        },
        // 定时获取结果
        setIntervalLoginTimer () {
            clearInterval(this.loginTimer)
            this.loginTimer = setInterval(() => {
                !this.isLoadingResult && this.getScanningResult()
            }, 3000)
        },
        // 获取扫码结果
        getScanningResult () {
            let params = {
                sAuthId: this.sAuthId
            }
            this.isLoadingResult = true;
            getScanResult(params).then(res => {
                if (res.success) {
                    let iOnceAgain = res.data.iOnceAgain
                    if (iOnceAgain == 0) {
                        // 已使用App扫码，清除定时器，请求登录
                        clearInterval(this.loginTimer)
                        this.toQrcodeLogin(res.data)
                        return
                    }
                    return
                }
                this.isQrOverTime = true
                clearInterval(this.loginTimer)
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
                this.isQrOverTime = true;
                clearInterval(this.loginTimer);
            }).finally(() => {
                this.isLoadingResult = false;
            })
        },
        // 二维码扫码后，执行登录
        toQrcodeLogin (data) {
            //提示登录提交
            let loading = this.loginLoading()
            let params = {
                userCode: data.sUserCode,
                authId: data.sAuthId
            }
            qrcodeLogin(params).then(res => {
                loading.close()
                if (res.success) {
                    this.$message.success('登录成功');
                    setLoginUserOptions(JSON.stringify([{ 'sNameOrNo': data.sUserCode }]))
                    setQRCodeAuthId(data.sAuthId); //缓存sAuthId
                    this.$store.commit({
                        type: "user/setSignatureAppName",
                        signatureAppName: this.signatureAppName
                    });
                    this.$store.commit({
                        type: 'user/setAccessToken',
                        accessToken: res.data.sessionId,
                        expires: new Date(Number(res.data.expireTime))
                    })
                    this.$router.push({ path: '/main/welcome_jump', query: { redirect: 'auto' } })
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                loading.close();
                this.isQrOverTime = true;
            })
        },
        // 获取系统配置  是否开启二维码登录；CA软件名称；是否开启UKEY登录；
        async handleBPMSetFindKeys () {
            let params = ['LoginInQrcode', 'CaAppName', 'LoginInUkey']
            this.currentLoginType = localStorage.getItem('loginIndexMode') || 1;
            this.hasQrcodeLogin = '0'
            await getBPMSetFindKeys(params).then(res => {
                if (res.success) {
                    let arr = res.data;
                    // 扫码登录开关设置
                    let codeValue = arr.find(item => item.sKey === 'LoginInQrcode')
                    this.hasQrcodeLogin = codeValue?.sValue
                    if (this.hasQrcodeLogin === '1' && this.currentLoginType == 2) {
                        //  开启  
                        this.getQRCode()
                    }
                    // CaAPP名称赋值
                    let appValue = arr.find(item => item.sKey === 'CaAppName')
                    this.signatureAppName = appValue?.sValue;

                    // UKEY登录开关设置
                    let tempValue = arr.find(item => item.sKey === 'LoginInUkey')
                    this.hasUKeyLogin = tempValue?.sValue
                    if (this.hasUKeyLogin === '1' && this.currentLoginType == 3) {
                        // 开启
                        this.mxInitUKeyMode()
                    }
                    return
                }
            }).catch(err => {
                console.log(err)
            }).finally(() => {
                // 若未开启配置，重置登录模式
                if (this.hasQrcodeLogin !== '1' && this.currentLoginType == 2) {
                    this.currentLoginType = 1
                }
                if (this.hasUKeyLogin !== '1' && this.currentLoginType == 3) {
                    this.currentLoginType = 1
                }
            })
        },
        setLastUserMessage () {
            if (this.currentLoginType == 1) {
                // 默认显示最后登录者
                let usersOption = getLoginUserOptions();
                this.loginInUserOptions = usersOption ? JSON.parse(usersOption) : [];
                if (this.loginInUserOptions.length) {
                    this.loginData.sNameOrNo = this.loginInUserOptions[0].sNameOrNo;
                    this.$nextTick(() => {
                        this.loginData.sNameOrNo && this.$refs.pwdInput.focus();
                    });
                }
            }
        },
    },
    beforeUnmount () {
        clearInterval(this.loginTimer)
    },
    async created () {
        // 设置底部时间
        let year = new Date().getFullYear()
        this.copyright = 'Copyright © 2002 - ' + year + ' 杏翔科技 版权所有';

        await this.handleBPMSetFindKeys() // todo
        this.setLastUserMessage();
        removeLoginCertId()  // 清除签章UKEY的缓存
        removeQRCodeAuthId() // 清除二维码sAuthId的缓存
        localStorage.removeItem('reportCloudSignPinAuthId')  // 清除签章PIN的缓存

        // 读取医院logo文件
        this.hospitalLogoUrl = window.location.origin + window.location.pathname + `img/hospital/logo.jpg`;
        checkFileExists(this.hospitalLogoUrl, (res) => {
            this.isShowLogo = res;
        })

    }
}
</script>

<style lang="scss" scoped>
$mainColor: var(--theme-header-bg);

.c-template {
    position: relative;
    width: 100%;
    height: 100%;
}

.g-header {
    height: 60px;
    background: url('/img/logo.png') no-repeat;
    background-position: 20px center;
    background-color: $mainColor;
}

.g-content {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(100% - 120px);

    .c-content {
        display: flex;
        justify-content: center;
        align-items: center;

        .c-pic {
            width: 405px;
            height: 344px;
            padding-right: 70px;

            img {
                width: 100%;
                margin-top: 26px;
                height: auto;
                opacity: 1;
            }

            > a {
                color: $mainColor;
            }
        }
    }
}

.c-img-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.g-footer {
    height: 60px;
    display: inline-block;
    width: 100%;

    // background-color: #f9f9f9;
    & > pre {
        margin: 10px 0 0 0;
        border: none;
        background: none;
        text-align: center;
        font-size: 14px;
        font-family: 'Microsoft YaHei';
    }
}

.twoLogin {
    width: 400px;
    height: 430px;
    padding: 8px 0px;
    box-shadow: 0px -1px 6px rgba(0, 0, 0, 0.15);
    margin-left: 120px;
    &.i-height {
        height: 380px;
    }

    .login-type {
        text-align: center;
        font-size: 16px;
        padding: 10px 0 5px;

        span {
            margin: 0 15px;
            cursor: pointer;
        }

        span.active {
            color: var(--el-color-primary);
        }
    }

    .login {
        position: relative;
        padding: 20px;

        .icon {
            width: 40px;
            height: 40px;
            position: absolute;
            right: 10px;
            top: 10px;
            cursor: pointer;
        }
    }

    .c-description {
        color: $mainColor;
        padding: 0 20px 15px;
        border-bottom: 1px solid #e4e7ed;

        h4 {
            text-align: left;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 1px;
        }

        p {
            text-align: left;
            font-size: 14px;
            margin-bottom: 4px;
        }
    }
}

.qrlogin {
    padding: 20px 0 0 0;
    text-align: center;
    font-size: 16px;

    .scan {
        font-size: 18px;
        font-weight: 600;
    }

    .app {
        padding: 5px 0 0 0;

        span {
            color: var(--el-color-primary);
            padding: 0 3px;
        }
    }

    .code {
        width: 180px;
        position: relative;
        height: 180px;
        border: 1px solid rgb(211, 211, 211);
        padding: 4px;
        border-radius: 4px;
        margin: 0 auto;
        .el-image {
            width: 100%;
            height: 100%;
            .image-slot {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
            }
        }

        .refresh {
            position: absolute;
            top: 0;
            left: 0;
            background-color: rgba(0, 0, 0, 0.5);
            width: 100%;
            height: 100%;
            text-align: center;
            color: #fff;

            p {
                margin-top: calc(50% - 10px);
                cursor: pointer;
            }
        }
    }
}

.passwordLogin {
    padding: 10px 15px 0 15px;

    .c-input-item {
        padding: 15px 0px;
    }

    .c-btn {
        padding: 20px 0 0 0;

        :deep(.el-button) {
            width: 100%;
            height: 38px;
            font-size: 18px;
        }

        button {
            background-color: $mainColor;
            border-color: $mainColor;
        }
    }
}
</style>

