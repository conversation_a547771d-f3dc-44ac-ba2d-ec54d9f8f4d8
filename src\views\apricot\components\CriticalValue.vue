<template>
    <span></span>

    <el-dialog
        title="危急值"
        append-to-body
        v-model="visible"
        fullscreen
        @close="closeDialog"
        :close-on-click-modal="false"
        class="my-dialog t-default my-full-dialog my-padding-dialog"> 
        <div class="g-main">
            <div class="c-main">
                <div class="c-head-right">
                    <strong>超时回复提醒：</strong>
                    <el-input v-model="iTimeout" 
                        type="number"
                        :min="0" 
                        size="small"
                        @change="handleTimeoutDelay"
                        style="width: 100px;margin-right: 5px;">
                        <template #suffix>min</template>
                    </el-input>
                    <!-- <span>min</span> -->
                    <!-- <span style="display:inline-block;margin-left:30px;">自动查询回复：</span>
                    <el-switch v-model="formInline.value"></el-switch> -->
                </div>

                <el-tabs v-model="activeTab" @tab-click="onTabClick">
                    <el-tab-pane label="当前患者" name="current">
                        <div class="">
                            <div class="c-info">
                            <span>核医学号：<strong>{{patientInfo.sNuclearNum}}</strong></span> 
                            <span>姓名：<strong>{{patientInfo.sName}}</strong></span>
                            <span>性别：<strong>{{patientInfo.sSexText}}</strong></span>
                            <span>年龄：<strong>{{patientInfo.sAge}}</strong></span>
                            <span>检查项目：<strong>{{patientInfo.sProjectName}}</strong></span>
                        </div>
                        <div class="c-form">
                            <el-form :model="form" class="demo-form-inline" inline label-position="right">
                                <el-form-item label="危急标志：">
                                    <el-select v-model="form.iEmergencyFlag" 
                                        placeholder="标志"
                                        style="width: 100%;">
                                        <el-option v-for="(item, index) in optionsLoc.EmergencyFlag" 
                                            :key="index" 
                                            :label="item.sName" 
                                            :value="item.sValue"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="危急值内容：">
                                    <el-autocomplete
                                        popper-class="my-autocomplete"
                                        v-model="form.sEmergency"
                                        :fetch-suggestions="querySearch"
                                        placeholder=" "
                                        @select="handleSelect"
                                        style="width: 450px;">
                                        <template v-slot="{ item }">
                                            <div>{{ item.sName }}</div>
                                        </template>
                                    </el-autocomplete>
                                </el-form-item>
                            <el-form-item>
                                <el-button-icon-fa icon="el-icon-position" type="primary" @click="onSubmit">发送</el-button-icon-fa>
                                <el-button-icon-fa icon="el-icon-refresh-left" @click="onTrash">清空</el-button-icon-fa>
                            </el-form-item>
                            </el-form>
                        </div>
                        </div>
                        <div class="flex flex-between"> 
                            <div style="font-size: 16px;"><strong>历史危急值</strong></div>
                            <div  class="flex  items-center ">
                                <el-button @click="queryList" style="margin-right: 15px;">
                                    <template #icon>
                                        <Icon name="el-icon-refresh" >
                                        </Icon>
                                    </template>
                                    刷新
                                </el-button>
                                <el-radio-group v-model="condition.iDealState" 
                                    @change="onSearch"
                                    >
                                    <el-radio-button label="">全部</el-radio-button>
                                    <el-radio-button label="1">未回复</el-radio-button>
                                    <el-radio-button label="2">已回复</el-radio-button>
                                    <el-radio-button label="3">上报失败</el-radio-button>
                                    <el-radio-button label="4">标记忽略</el-radio-button>
                                    <el-radio-button label="5">电话上报</el-radio-button>
                                </el-radio-group>
                                <!-- <span>核医学号：</span>
                                <el-input v-model="condition.sNuclearNum"
                                    clearable
                                    style="width: 200px;margin-right: 30px;"></el-input>
                                <el-button type="primary"  @click="onSearch">查 询</el-button>
                                <el-button  @click="onReset">重 置</el-button> -->
                            </div>
                        </div>

                        <div class="c-table">
                            <!-- @row-click="handleRowClick" -->
                            <el-table :data="tableData"
                                v-loading="loading"
                                id="itemTable"
                                ref="mainTable"
                                highlight-current-row
                                @row-dblclick="onRowDblclick"
                                border
                                stripe
                                height="100%"
                                style="width: 100%">
                                <el-table-column label="序号"
                                    type="index"
                                    width="60">
                                </el-table-column>
                                <template  v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                                    <el-table-column
                                    show-overflow-tooltip
                                    
                                    :prop="item.sProp"
                                    :label="item.sLabel"
                                    :fixed="item.sFixed"
                                    :align="item.sAlign"
                                    :width="item.sWidth"
                                    :min-width="item.sMinWidth"
                                    :sortable="!!item.iSort"
                                    >
                                    <template v-slot="{row, $index}">
                                        <template v-if="item.sProp === 'action'">
                                            <el-button-icon-fa type="primary" link icon="el-icon-paperclip"
                                                @click="onPhoneUp(row, $index)">标记电话通知</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-view"
                                                @click="onReply(row, $index)">查看回复</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-position"
                                                @click="onRetry(row, $index)">重发</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-close-notification"
                                                @click="onMarkIgnored(row, $index)">标记忽略</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-refresh-left" 
                                                @click="onMarkCancel(row, $index)">撤销</el-button-icon-fa>
                                        </template>
                                        <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                            {{  toDateString(row[`${item.sProp}`]) }}
                                        </template>
                                        <template v-else-if="item.sProp === 'iTimeout'">
                                            <i v-if="row[`${item.sProp}`]" class="fa fa-timeout i-highlight" style="font-size: 18px; color:red;"></i>
                                        </template>
                                        <template v-else>
                                            {{ row[`${item.sProp}`] }}
                                        </template>
                                    </template>
                                </el-table-column>
                                </template>
                            </el-table>
                        </div>
                        <div style="text-align: right;">
                            <el-pagination
                                @size-change="handleSizeChange"
                                @current-change="handleCurrentChange"
                                :current-page="page.pageCurrent"
                                :page-sizes="[10, 20, 30, 50, 100]"
                                :page-size="page.pageSize"
                                layout="total, sizes, prev, pager, next"
                                :total="page.total">
                                </el-pagination>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="全部患者" name="all">
                      <div class="flex flex-col w-full h-full">
                        <div class="flex flex-between" style="">
                            
                            <div>
                                <span>核医学号：</span>
                                <el-input v-model="condition1.sNuclearNum" 
                                    clearable
                                    style="width: 200px;margin-right: 30px;"></el-input>
                                <span>姓 名：</span>
                                <el-input v-model="condition1.sName" 
                                    clearable
                                    style="width: 200px;margin-right: 10px;"></el-input>
                                <el-button type="primary" @click="onSearch1">
                                    <template #icon>
                                        <Icon name="el-icon-search" >
                                        </Icon>
                                    </template>
                                    查询</el-button>
                                <el-button-icon-fa @click="onReset1">
                                    <template #icon>
                                        <Icon name="el-icon-refresh-left" >
                                        </Icon>
                                    </template>
                                    重置
                                </el-button-icon-fa>
                            </div>
                            <el-radio-group v-model="condition1.iDealState" 
                                @change="onSearch1"
                                style="">
                                <el-radio-button label="">全部</el-radio-button>
                                <el-radio-button label="1">未回复</el-radio-button>
                                <el-radio-button label="2">已回复</el-radio-button>
                                <el-radio-button label="3">上报失败</el-radio-button>
                                <el-radio-button label="4">标记忽略</el-radio-button>
                                <el-radio-button label="5">电话上报</el-radio-button>
                            </el-radio-group>
                        </div>
                        <div class="c-table">
                            <el-table :data="tableData1"
                                v-loading="loading1"
                                id="itemTable"
                                ref="mainTable"
                                
                                highlight-current-row
                                border
                                stripe
                                height="100%"
                                style="width: 100%">
                                <el-table-column label="序号"
                                    type="index"
                                    width="60">
                                </el-table-column>
                                <template  v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                                    <el-table-column
                                    show-overflow-tooltip
                                    
                                    :prop="item.sProp"
                                    :label="item.sLabel"
                                    :fixed="item.sFixed"
                                    :align="item.sAlign"
                                    :width="item.sWidth"
                                    :min-width="item.sMinWidth"
                                    :sortable="!!item.iSort"
                                    >
                                    <template v-slot="{row, $index}">
                                        <template v-if="item.sProp === 'action'">
                                            <el-button-icon-fa type="primary" link icon="el-icon-paperclip"
                                                @click="onPhoneUp(row, $index)">标记电话通知</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-view"
                                                @click="onReply(row, $index)">查看回复</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-position"
                                                @click="onRetry(row, $index)">重发</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-close-notification"
                                                @click="onMarkIgnored(row, $index)">标记忽略</el-button-icon-fa>
                                            <el-button-icon-fa type="primary" link icon="el-icon-refresh-left" 
                                                @click="onMarkCancel(row, $index)">撤销</el-button-icon-fa>
                                        </template>
                                        <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                            {{  toDateString(row[`${item.sProp}`]) }}
                                        </template>
                                        <template v-else-if="item.sProp === 'iTimeout'">
                                            <i v-if="row[`${item.sProp}`]" class="fa fa-timeout i-highlight" style="font-size: 18px; color:red;"></i>
                                        </template>
                                        <template v-else>
                                            {{ row[`${item.sProp}`] }}
                                        </template>
                                    </template>
                                </el-table-column>
                                </template>
                            </el-table>
                        </div>
                        <div class="" style="text-align: right;">
                            <el-pagination
                                @size-change="handleSizeChange1"
                                @current-change="handleCurrentChange1"
                                :current-page="page1.pageCurrent"
                                :page-sizes="[10, 20, 30, 50, 100]"
                                :page-size="page1.pageSize"
                                layout="total, sizes, prev, pager, next"
                                :total="page1.total">
                                </el-pagination>
                        </div>
                      </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <el-dialog
            :title="isPhoneMark ? '标记电话通知' : '标记忽略'"
            v-model="isShowTip"
            append-to-body
            :close-on-click-modal="false"
            class="my-dialog t-default"
            width="750px">
            <div>
                <el-row style="margin-bottom: 30px;border-bottom: 1px solid #eee;">
                    <el-col :span="6" class="c-col" :title="selectedRow.sNuclearNum">核医学号：<strong>{{selectedRow.sNuclearNum}}</strong></el-col>
                    <el-col :span="6" class="c-col" :title="selectedRow.sName">姓名： <strong>{{selectedRow.sName}}</strong></el-col>
                    <el-col :span="6" class="c-col">性别： <strong>{{selectedRow.sSexText}}</strong></el-col>
                    <el-col :span="6" class="c-col">年龄： <strong>{{selectedRow.sAge}}</strong></el-col>
                    <el-col :span="12" class="c-col" :title="selectedRow.sItemName">项目名称： <strong>{{selectedRow.sItemName}}</strong></el-col>
                    <el-col :span="12" class="c-col">危急标志： <strong>{{selectedRow.sEmergencyFlag}}</strong></el-col>
                    <el-col :span="12" class="c-col">申请科室： <strong>{{selectedRow.sApplyDept}}</strong></el-col>
                </el-row>
                <el-form v-if="isPhoneMark" :model="tipValues" label-position="right" label-width="130px">
                    <el-form-item label="申请科室/医生电话">
                        <el-input v-model="tipValues.phone"
                            clearable style="width: 50%;"></el-input>
                    </el-form-item>
                    <el-form-item label="接收方已处理：">
                        <el-checkbox v-model="tipValues.iReply" :true-label="1" :false-label="0"></el-checkbox>
                    </el-form-item>
                    <el-form-item v-if="tipValues.iReply" label="回复内容:">
                        <el-input v-model="tipValues.sReply" 
                            clearable></el-input>
                    </el-form-item>
                    <el-form-item label="备注：">
                        <el-input v-model="tipValues.sMeno" 
                            type="textarea"
                            clearable></el-input>
                    </el-form-item>
                </el-form>

                <el-form v-else :model="tipValues"  label-position="right" label-width="130px">
                    <el-row style="color:red;margin-bottom: 20px;">注意：标记后，系统会忽略处理，如已上报，请电话通知患者所在科室。</el-row>
                    <el-form-item label="备注：">
                        <el-input v-model="tipValues.sMeno" 
                            type="textarea"
                            clearable></el-input>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <el-button-icon-fa v-if="isPhoneMark" type="primary" icon="fa fa-save"  @click="onSubmitPhoneUp">确定</el-button-icon-fa>
                <el-button-icon-fa v-else type="primary" icon="fa fa-save" @click="onSubmitMarkIgnored">确定</el-button-icon-fa>
                <el-button-icon-fa icon="fa fa-close-1" @click="isShowTip = false">关闭</el-button-icon-fa>
            </template>
        </el-dialog>
        <el-dialog
            title="查看回复"
            v-model="isShowReply"
            append-to-body
            :close-on-click-modal="false"
            class="my-dialog t-default"
            width="750px">
            <div>
                <el-row style="margin-bottom: 30px;border-bottom: 1px solid #eee;">
                    <el-col :span="6" class="c-col" :title="selectedRow.sNuclearNum">核医学号：<strong>{{selectedRow.sNuclearNum}}</strong></el-col>
                    <el-col :span="6" class="c-col" :title="selectedRow.sName">姓名： <strong>{{selectedRow.sName}}</strong></el-col>
                    <el-col :span="6" class="c-col">性别： <strong>{{selectedRow.sSexText}}</strong></el-col>
                    <el-col :span="6" class="c-col">年龄： <strong>{{selectedRow.sAge}}</strong></el-col>
                    <el-col :span="12" class="c-col" :title="selectedRow.sItemName">项目名称： <strong>{{selectedRow.sItemName}}</strong></el-col>
                    <el-col :span="12" class="c-col">危急标志： <strong>{{selectedRow.sEmergencyFlag}}</strong></el-col>
                    <el-col :span="12" class="c-col">申请科室： <strong>{{selectedRow.sApplyDept}}</strong></el-col>
                </el-row>
                <el-row class="c-col">状态：<strong>{{replyContents.sDealState}}</strong></el-row>
                <el-row class="c-col">回复人：{{replyContents.sReplyName}}</el-row>
                <el-row >回复内容：{{replyContents.sReply}}</el-row>
            </div>
            <template #footer>
                <el-button-icon-fa icon="fa fa-close-1" @click="isShowReply = false;replyContents = {}">关闭</el-button-icon-fa>
            </template>
        </el-dialog>
        
        <template #footer>
            <el-button-icon-fa icon="fa fa-close-1" @click="$emit('update:dialogVisible', false)">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import { transformDate, getOptionName } from '$supersetResource/js/tools'
import { registerEmergency, emergencyReply, emergencyRetry, emergencyqueryList,
emergencyPhoneUp, emergencyMarkIgnored, emergencyCancel  } from '$supersetApi/projects/apricot/case/report.js'
export default {
    name: "CriticalValue",
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
        patientInfo: {
            type: Object,
            default: ({}),
        },
    },
    emits: ['update:dialogVisible', 'emergencyqueryList'],
    data() {
        return {
            visible: false,
            activeTab: 'current',
            loading: false,
            condition: {
                iDealState: ''
            },
            page: {
                pageSize: 30,
                pageCurrent: 1,
                total: 0
            },
            iTimeout: localStorage.getItem('iTimeoutCriticalValue') || 5,
            optionsLoc: {
                EmergencyFlag: [],
                Emergency: []
            },
            form: {},
            tableData: [],
            configTable: [
                {
                    sProp: 'iTimeout',
                    sLabel: '超时未回复',
                    sAlign: 'center',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sName',
                    sLabel: '姓名',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'sSexText',
                    sLabel: '性别',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sAge',
                    sLabel: '年龄',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sNuclearNum',
                    sLabel: '核医学号',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'sImgStudyDate',
                    sLabel: '检查日期',
                    sMinWidth: '110px',
                },
                {
                    sProp: 'sItemName',
                    sLabel: '检查项目',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'sEmergencyFlag',
                    sLabel: '危急标志',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sEmergency',
                    sLabel: '危急值内容',
                    sMinWidth: '150px',
                },
                {
                    sProp: 'sDealState',
                    sLabel: '状态',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'dUpTime',
                    sLabel: '上报时间',
                    sMinWidth: '155px',
                },
                {
                    sProp: 'sUpUserName',
                    sLabel: '上报人',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sApplyDept',
                    sLabel: '申请科室',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sUpResult',
                    sLabel: '发送返回结果',
                    sMinWidth: '150px',
                },
                {
                    sProp: 'sDrPhone',
                    sLabel: '医生电话',
                    sMinWidth: '110px',
                },
                {
                    sProp: 'sReplyName',
                    sLabel: '回复人',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'dReplyTime',
                    sLabel: '回复时间',
                    sMinWidth: '155px',
                },
                {
                    sProp: 'sReply',
                    sLabel: '回复内容',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sMeno', 
                    sLabel: '备注',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'action',
                    sLabel: '操作',
                    sAlign: 'center',
                    sFixed: 'right',
                    sMinWidth: '430px',
                },
            ],
            isShowTip: false,
            tipValues: {},
            selectedRow: {},
            loading1: false,
            condition1: {
                iDealState: ''
            },
            page1: {
                pageSize: 30,
                pageCurrent: 1,
                total: 0
            },
            tableData1: [],
            isPhoneMark: true,
            replyContents: {},
            isShowReply: false,
            delayFresh: null,
            refreshReport: false
        };
    }, 
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                // let rights = this.rights;
                // let trueArray = [rights.PhoneUp, rights.Reply, rights.Resend, rights.Ignored, rights.Cancle].filter(item => item == true);
                // this.configTable[this.configTable.length - 1].sMinWidth = trueArray.length ? 150 + (trueArray.length - 1) * 70 : 0;
                this.visible = this.dialogVisible;
                this.activeTab = 'current';
                this.form = this.$options.data().form;
                this.condition = this.$options.data().condition;
                this.condition1 = this.$options.data().condition1;
                this.selectedRow = this.$options.data().selectedRow;
                this.tipValues = this.$options.data().tipValues;
                this.tableData = [];
                this.tableData1 = [];
                let iTimeout = localStorage.getItem('iTimeoutCriticalValue');
                this.iTimeout = iTimeout === null || isNaN(Number(iTimeout)) ? 5 : iTimeout;
                localStorage.setItem('iTimeoutCriticalValue', this.iTimeout);
                clearInterval(this.delayFresh);
                if(this.iTimeout > 0) {
                    this.delayFresh = setInterval(() => {
                        this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                    }, this.iTimeout * 60 * 1000)
                }
                this.onSearch();
            } else {
                this.visible = false
                clearInterval(this.delayFresh);
            }
        },
    },
    methods: {
      toDateString (val) {
            return transformDate(val, true)
        },
        closeDialog () {
            this.$emit('update:dialogVisible', false);
            if(this.refreshReport) {
                this.$emit('emergencyqueryList')
            }
        },
        querySearch(queryString, cb) {
            var emergency = this.optionsLoc.Emergency || [];
            var results = queryString ? emergency.filter(this.createFilter(queryString)) : emergency;
            // 调用 callback 返回建议列表的数据
            cb(results);
        },
        createFilter(queryString) {
            return (emergency) => {
                return (emergency.sName.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
            };
        },
        handleSelect(item) {
            this.form.sEmergency = item.sName;
        },
        // 超时查询
        handleTimeoutDelay(currentValue) {
            clearInterval(this.delayFresh); 
            if(currentValue !== undefined) {
                localStorage.setItem('iTimeoutCriticalValue', currentValue)
            }
            if(currentValue) {
                this.delayFresh = setInterval(() => {
                    this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                }, this.iTimeout * 60 * 1000);
            }
        },
        // 切换Tab
        onTabClick() {
            if(this.activeTab=== 'all') {
                if(!this.tableData1.length) {
                    this.onSearch1();
                }
            }
        },
        // 表格双击
        onRowDblclick(row) {
            this.form = {
                iId: row.iId,
                iEmergencyFlag: row.iEmergencyFlag,
                sEmergency: row.sEmergency,
            };
        },
        // 发送危急值 
        onSubmit () {
            let isFalse = ['', undefined, null]
            if(isFalse.includes(this.form.iEmergencyFlag) || isFalse.includes(this.form.sEmergency)) {
                this.$message.warning('请录入危急值标志或危急值内容！')
                return
            }
            let jsonData = {
                sPatientInfoId: this.patientInfo.sId,
                sNuclearNum: this.patientInfo.sNuclearNum,
                sName: this.patientInfo.sName,
                sItemName: this.patientInfo.sProjectName,
                sUpUserId: this.userInfo.sId,
                sUpUserNo: this.userInfo.sNo,
                sUpUserName: this.userInfo.sName,
            }
            Object.assign(jsonData, this.form);
            jsonData.sEmergencyFlag = getOptionName(jsonData.iEmergencyFlag + '', this.optionsLoc.EmergencyFlag);
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
            registerEmergency(jsonData).then(res => {
                loading.close();
                this.queryList();
                this.refreshReport = true
                if(res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg)
            }).catch (err => {
                if(err?.success === false) {
                    this.queryList();
                    this.refreshReport = true
                }
                loading.close()
            })
        },
        // 打开电话通知弹窗
        onPhoneUp (row, index) { 
            this.isPhoneMark = true;
            this.tipValues = this.$options.data().tipValues;
            this.tipValues['phone'] = row.sDrPhone;
            this.tipValues['iReply'] = 0;
            this.selectedRow = row;
            this.isShowTip = true;
        },
        // 标记电话通知
        onSubmitPhoneUp() {
            let jsonData = Object.assign({}, this.tipValues);
            jsonData.iId = this.selectedRow.iId;
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
			jsonData.iReply = jsonData.iReply || 0;
            emergencyPhoneUp(jsonData).then(res => {
                loading.close();
                if(res.success) {
                    this.$message.success(res.msg);
                    this.isShowTip = false;
                    this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                loading.close()
            })
        },
        // 打开取消弹窗
        onMarkIgnored(row, index) { 
            this.isPhoneMark = false;
            this.tipValues = this.$options.data().tipValues;
            this.selectedRow = row;
            this.isShowTip = true;
        },
        // 标记忽略
        onSubmitMarkIgnored() {
            let jsonData = Object.assign({}, this.tipValues);
            jsonData.iId = this.selectedRow.iId;
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
            emergencyMarkIgnored(jsonData).then(res => {
                loading.close()
                if(res.success) {
                    this.$message.success(res.msg);
                    this.isShowTip = false;
                    this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                loading.close()
            })
        },
        // 查看回复
        onReply (row, index) {
            let jsonData = {
                iId: row.iId
            }
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
            this.selectedRow = {};
            emergencyReply(jsonData).then(res => {
                loading.close()
                if(res.success) {
                    this.selectedRow = Object.assign({}, row);
                    this.isShowReply = true;
                    this.replyContents = res.data || {};
                    this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                loading.close()
            })
        },
        // 重发
        onRetry (row, index) {
            let jsonData = {
                iId: row.iId
            }
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
            emergencyRetry(jsonData).then(res => {
                loading.close()
                if(res.success) {
                    this.$message.success(res.msg);
                    this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                loading.close()
            })
        },
        onMarkCancel(row, index) {
            let loading = this.$loading({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.1)',
            })
            emergencyCancel({emergencyId: row.iId}).then(res => {
                loading.close()
                if(res.success) {
                    this.$message.success(res.msg);
                    this.activeTab === 'current' ? this.queryList() : this.queryAllList();
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                loading.close()
            })
        },
        // 清空
        onTrash () {
            Object.keys(this.form).map(item => {
                this.form[item] = undefined
            });
        },
        // 重置
        onReset () {
            Object.keys(this.condition).map(item => {
                this.condition[item] = ''
            });
        },
        // 查询
        onSearch () {
            this.page.pageCurrent = 1;
            this.queryList()
        },
        // 获取数据
        queryList() {
            let jsonData = Object.assign({}, this.condition);
            jsonData.pageSize = this.page.pageSize;
            jsonData.pageCurrent = this.page.pageCurrent;
            jsonData.iTimeout = this.iTimeout || 0;
            if(this.activeTab === 'current') {
                let patientInfo = this.patientInfo
                jsonData.sImgPatientId = patientInfo.sImgPatientId;
                jsonData.sInHospitalNO = patientInfo.sInHospitalNO;
                jsonData.sMedicalRecordNO = patientInfo.sMedicalRecordNO;
                jsonData.sName = patientInfo.sName;
                jsonData.sOutpatientNO = patientInfo.sOutpatientNO;
                jsonData.sNuclearNum = patientInfo.sNuclearNum;
            }
            this.loading = true;
            emergencyqueryList(jsonData).then(res => {
                this.loading = false;
                if(res.success) {
                    this.tableData = res.data.records || [];
                    this.page.total = res.data.total;
                    return
                }
                this.$message.error(res.msg)
            }).catch(err =>{
                this.loading = false;
            })
        },
        // 翻页大小
        handleSizeChange(val) {
            this.page.pageSize = val;
            this.onSearch();
        },
        // 翻页当前页面
        handleCurrentChange(val) {
            this.page.pageCurrent= val;
            this.onSearch();
        },
        // 
        onSearch1 () {
            this.page1.pageCurrent = 1;
            this.queryAllList()
        },
        onReset1 () {
            Object.keys(this.condition1).map(item => {
                this.condition1[item] = ''
            });
        },
        queryAllList() {
            let jsonData = Object.assign({}, this.condition1);
            jsonData.pageSize = this.page1.pageSize;
            jsonData.pageCurrent = this.page1.pageCurrent;
            jsonData.iTimeout = this.iTimeout || 0;
            if (jsonData.iDealState === "") {
                delete jsonData.iDealState
            }
            this.loading1 = true ;
            emergencyqueryList(jsonData).then(res => {
                this.loading1 = false
                if(res.success) {
                    this.tableData1= res.data.records || [];
                    this.page1.total = res.data.total;
                    return
                }
                this.$message.error(res.msg);
            }).catch(err =>{
                this.loading1 = false
            })
        },
        handleSizeChange1(val) {
            this.page1.pageSize = val;
            this.onSearch1();
        },
        handleCurrentChange1(val) {
            this.page1.pageCurrent= val;
            this.onSearch1();
        },
    },
    beforeUnmount() {
        clearInterval(this.delayFresh);
    },
    created() {
        this.optionsLoc.EmergencyFlag = this.$store.getters['dict/map'].EmergencyFlag || [];
        this.optionsLoc.Emergency = this.$store.getters['dict/map'].Emergency || [];
    }
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs) {
    .el-tabs__content {
        padding: 15px 10px 0 10px;
    }
}
.g-main {
    display: flex;
    background-color: #f0f0f0;
    overflow: hidden;
    padding: 0;

    .c-main {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        flex: 1;
        background: #fff;
        padding: 12px 20px 0px 20px;
        overflow: hidden;
        box-sizing: border-box;
        // -webkit-user-select: none;
        // -moz-user-select: none;
        // -ms-user-select: none;
        // user-select: none;
        position: relative;

        .c-head-right {
            padding: 14px 20px 5px 0;
            position: absolute;
            right: 0;
            top: 0;
            z-index: 1;
        }
    }

    .c-info {
        padding: 15px 0 15px;
        >span {
            display: inline-block;
            margin-right: 50px;
            line-height: 30px;
        }
    }
    .c-form {
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;
    }
    .c-table {
        flex: 1; 
        padding-top: 10px;
        overflow: auto;
    }
}

:deep(.el-tabs .el-tabs__header) {
    margin: 0;
}
.flex-between {
    justify-content: space-between;
    padding: 10px 10px 4px 10px;
    place-items: center;
}
:deep(.el-tabs__item) {
    height: 40px;
    line-height: 40px;
}
.c-col {
    margin-bottom: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.i-highlight {
    animation: animate 2s;
    animation-iteration-count: infinite;
}

:deep(.el-autocomplete) {
    .el-input {
        width: 100%;
    }
}

@keyframes animate { 
    0% { color: red; } 
    50% { color: goldenrod; } 
    100% { color: red}
}
</style>
