import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'
// 表格数据
export function getClientData(data) {
    return request({
        url: baseURL.broken + '/client/queryAllClient',
        method: 'POST',
        data
    })
}
// 编辑客户端
export function editClient(data) {
    return request({
        url: baseURL.broken + '/client/editClient',
        method: 'POST',
        data
    })
}
// 注册客户端
export function registerClient(data) {
    return request({
        url: baseURL.broken + '/client/registerClient',
        method: 'POST',
        data
    })
}
// 按客户端主键查询配置列表数据(如果该主板的客户端未配置，则返回主键为0的默认模板配置)
export function clientFindClientNo(params) {
    return request({
        url: baseURL.broken + '/client/set/find/sClientNo',
        method: 'POST',
        data: stringify(params)
    })
}

// 新增数据
export function clientSetSave(data) {
    return request({
        url: baseURL.broken + '/client/set/save',
        method: 'POST',
        data
    })
}