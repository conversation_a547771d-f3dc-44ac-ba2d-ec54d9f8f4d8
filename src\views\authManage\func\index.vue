<template>
    <LayoutTable>
		<template v-slot:header>
			<SearchList v-model="searchParams.condition" v-model:list="searchList" :loading="loadingSearch" :isLang="false"
			storageKey="AuthManageFuncIndex"  @changeSearch="onClickSearch" @reset="onClickReset">
			</SearchList>
		</template>
        <template #action>
            <div style="text-align: right">
                <el-button @click="onClickReset">
                    <template #icon>
                        <Icon name="el-icon-refresh-left" >
                        </Icon>
                    </template>
                    重置</el-button>
                <el-button type="primary" @click="onClickSearch" :loading="loadingSearch">
                    <template #icon>
                        <Icon name="el-icon-search" color="white">
                        </Icon>
                    </template>
                    查询</el-button>
            </div>
        </template>
        <template v-slot:content>
            <el-table :data="tableData" style="width: 100%" height="100%" v-loading="loadingSearch">
                <xxTableColumn v-model="tableList" storageKey="AuthManageFuncIndex" :isLang="false">
                </xxTableColumn>
            </el-table>
        </template>
        <template #footer>
            <!-- 分页 -->
            <el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :current-page="page.pageNum"
                :page-sizes="pageSizes" :pager-count="5" :page-size="page.pageSize" background
                layout="total, sizes, prev, pager, next" :total="page.total">
            </el-pagination>
        </template>
    </LayoutTable>
</template>
<script>
	export default {
		name: 'AuthManageFuncIndex'
	}
</script>
<script setup>
	import Api from "@/api/auth/auth"

	const tableData = ref([])
	const loadingSearch = ref(false)
    const tableList = ref([
        { prop: "systemCode", label: "系统编码", align: 'center', width: 100 },
		{ prop: "systemName", label: "系统名称", align: 'center', width: 100 },
        { prop: "moduleCode", label: "模块编码", align: 'center', width: 120 },
        { prop: "moduleName", label: "模块名称", align: 'center', width: null },
        { prop: "rightName", label: "按钮名称", align: 'center', width: null },
        { prop: "resourceName", label: "资源名称", align: 'center', width: null },
        { prop: "rightCode", label: "接口URL", align: 'left', setting: true, width: null },
        { prop: "createTime", label: "创建时间", align: 'center', width: 180 },

    ])
    const searchParams = reactive({
        condition: {}
    })

	const searchList = [
        { label: '模块名称', prop: 'moduleName', componentType: 'el-input', width: '40%' },
    ]

    const pageSizes = [10, 20, 30, 40, 60]
    const page = reactive({ // 分页	
        pageNum: 1,
        pageSize: 20,
        total: 0
    })
    // 翻页
    function onSizeChange(val) {
        page.pageSize = val
        onClickSearch()
    }
    // 切页
    function onCurrentChange(val) {
        page.pageNum = val
        onClickSearch()
    }

	onMounted(() => {
       onClickSearch()
    })

	// 点击搜索
    const onClickSearch = () => {
        loadingSearch.value = true
        const params = Object.assign({}, searchParams, page)
        Api.queryRightPage(params).then(({ data }) => {
            tableData.value = data.list || []
            loadingSearch.value = false
            page.total = data.total
        }).catch(() => {
            loadingSearch.value = false
        })
    }
    function onClickReset() {
      for (const key in searchParams.condition) {
        if (searchParams.condition.hasOwnProperty(key)) {
          searchParams.condition[key] = '' 
        }
      }
      onClickSearch()
    }
</script>
