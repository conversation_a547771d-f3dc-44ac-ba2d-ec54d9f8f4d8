<template>
    <div>
        <template v-for="item in list">
            <div v-if="item.type === 'checkbox'" class="i-item" :class="[item.styleClass != undefined ? item.styleClass : '']">
                <el-checkbox style="margin: 2px 0;" v-model="item.sValue" @change="onChange(item.sValue, item.pid, item)">{{ item.sName }}</el-checkbox>
                <template v-if="item.child">
                    <template v-if="item.child.type === 'option'">
                        <el-select size="small" style="width: 90px; margin: 2px 0 0 8px;" v-model="item.child.sValue">
                            <el-option
                                v-for="op in item.child.option"
                                :key="op.sValue"
                                :label="op.sName"
                                :value="op.sName">
                            </el-option>
                        </el-select>
                    </template>
                    <template v-if="item.child.type === 'text'">
                        <p class="c-input">
                            <span v-if="item.child.sName">{{ item.child.sName }}</span>&nbsp;<el-input size="small" style="width: 100px;" v-model="item.child.sValue"></el-input>
                        </p>
                    </template>
                </template>
                <div v-if="item.children"  class="children-item">
                    <CheckboxTree1 :list="item.children" :item="item"></CheckboxTree1>
                </div>
            </div>


            <div v-else-if="item.type === 'input'" class="i-item" :class="[item.styleClass != undefined ? item.styleClass : '']">
                <span>{{ item.sName }}</span><el-input size="small" style="width: 81px; margin: 2px 0 0 8px;" v-model="item.sValue"></el-input>
                <template v-if="item.child">
                    <template v-if="item.child.type === 'option'">
                        <el-select size="small" style="width: 90px; margin: 2px 0 0 8px;" v-model="item.child.sValue">
                            <el-option
                                v-for="op in item.child.option"
                                :key="op.sValue"
                                :label="op.sName"
                                :value="op.sName">
                            </el-option>
                        </el-select>
                    </template>
                    <template v-if="item.child.type === 'text'">
                        <p class="c-input">
                            <span v-if="item.child.sName">{{ item.child.sName }}</span>&nbsp;<el-input size="small" style="width: 100px;" v-model="item.child.sValue"></el-input>
                        </p>
                    </template>
                </template>
                <div v-if="item.children"  class="children-item">
                    <CheckboxTree1 :list="item.children" :item="item"></CheckboxTree1>
                </div>
            </div>

            <div v-else-if="item.type === 'radio'" class="i-item" :class="[item.styleClass != undefined ? item.styleClass : '']">
                <el-radio-group v-model="item.sValue" @change="onChange(item.sValue, item.pid, item)" style="margin: 2px 0;line-height: 35px;min-height: 35px;">
                    <template v-for="radio in item.option">
                        <el-radio :label="radio.sValue">{{ radio.sName }}</el-radio>
                        <el-button v-if="radio.isMore && radio.sValue === item.sValue" @click="onClickShowDialog(radio.mores)" type="primary" icon="el-icon-edit" circle size="small"></el-button>
                        <div v-if="radio.isMore && radio.sValue === item.sValue" class="c-text-area" v-html="radio.html"></div>
                    
                        <el-dialog v-if="radio.isMore && radio.sValue === item.sValue" :close-on-click-modal="false" append-to-body title="有/可疑残留-选择部位" v-model="visible" width="740px" class="my-dialog">
                            <div class="c-content">
                                <CheckboxTree1 :list="layerData" />
                            </div>
                            <template #footer><div  class="my-dialog-footer">
                                <el-button size="small" icon="el-icon-success" @click="onClickSureBack" type="primary">确 定</el-button>
                                <el-button size="small" icon="el-icon-circle-close" @click="visible = false">关 闭</el-button>
                            </div></template>
                        </el-dialog>
                    </template>

                </el-radio-group>
                <template v-if="item.child">
                    <template v-if="item.child.type === 'option'">
                        <el-select size="small" style="width: 90px; margin: 2px 0 0 8px;" v-model="item.child.sValue">
                            <el-option
                                v-for="op in item.child.option"
                                :key="op.sValue"
                                :label="op.sName"
                                :value="op.sName">
                            </el-option>
                        </el-select>
                    </template>
                    <template v-if="item.child.type === 'text'">
                        <p class="c-input">
                            <span v-if="item.child.sName">{{ item.child.sName }}</span>&nbsp;<el-input size="small" style="width: 100px;" v-model="item.child.sValue"></el-input>
                        </p>
                    </template>
                </template>
                <div v-if="item.children"  class="children-item">
                    <CheckboxTree1 :list="item.children" :item="item"></CheckboxTree1>
                </div>
            </div>

        </template>
    </div>
</template>

<script>
export default {
    name: "CheckboxTree1",
    props: {
        list: {
            type: [Object, Array],
        },
        item: {
            type: [Object, Array],
        }
    },
    components: {
    },
    data() {
        return {
            layerData: [],
            visible: false,
        }
    },
    methods: {
        // 向上选中
        onChange(val, pid, item){
            if(!val) {
                this.treeFloorClear(item.id)
                return
            }
            let p = pid
            let parent = this.$parent
            while (parent && !parent.isTreeRoot) {
                if (parent.list === undefined) return;
                parent.list.forEach(element => {
                    if (element.id === p){
                        if (element.type === 'checkbox') element.sValue = true
                        if (element.pid != '') p = element.pid
                    }
                });
                parent = parent.$parent
            }
        },
        // 向下清空值
        treeFloorClear(id){
            let parent = this.$parent
            const filterTree = (tree, id, clearArr) => {
                if (tree === undefined) return;
                tree.forEach(item => {
                    if (item.pid === id || clearArr.includes(item.pid)){
                        clearArr.push(item.id)    // 把当前的 id 加入到清空数组中
                        if (item.type === 'radio'){
                            item.sValue = null
                        }
                        if (item.type === 'checkbox'){
                            item.sValue = false
                        }
                    }
                    if (item.children){
                        filterTree(item.children, id, clearArr)
                    }
                })
            }
            // parent.list 空的时候是最顶级的就使用 自身
            filterTree(parent.list === undefined ? this.list : parent.list, id, [])
            
        },
        // 打开弹窗
        onClickShowDialog(value){
            this.visible = true
            this.layerData = value
        },
        // 弹窗点击返回数据
        onClickSureBack(){
            this.list.forEach(element => {
                element.option.forEach(el => {
                    if (el.isMore){
                        el.html = this.recursiveData(this.layerData)
                    }
                });
            });
            this.visible = false
        },
        recursiveData(data){
          var result = '<ul class="">'
          for (let index = 0; index < data.length; index++) {
              const el = data[index];
              if (el.sValue){
                  // result += el.sName
                  let cls = el.child ? "style='display: inline-block;'" : ''
                  result += "<li " + cls + " >" + el.sName + "</li>"

                  if (el.child){
                    result += "<span style='padding: 0px 5px;'>：" + el.child.sValue + "</span> </br>"
                    //  console.log(el.child.sValue) 
                  }
              }
              if (el.children){
                result += this.recursiveData(el.children)
              }
          }
          result += "</ul>";
          return result
        }
    }
};
</script>
<style>
.c-text-area ul{
    list-style-type: none;
    /* padding-left: 20px; */
}
.c-text-area ul li{
    /* padding: 6px 0px; */
    height: 18px;
    line-height: 18px;
    padding: 0px;
}
</style>
<style scoped lang="scss">
.children-item{
    padding-left: 20px;
}
.i-item{
    margin: 5px 0px;
}
.i-inline-block{
    display: inline-block;
    margin-right: 15px;
}
.i-align-top{
    vertical-align: top;
}
.c-input{
    display: inline-block;
    margin: 2px 0 0 8px;
}
.c-text-area{
    overflow: auto;
    height: 120px;
    margin: 10px 10px 10px 0px;
    padding: 10px 0px;
    border: 1px solid #eee;
    font-size: 14px;
}
.c-box-group{
    .el-radio-group{
        display: block;
    }
}
</style>
