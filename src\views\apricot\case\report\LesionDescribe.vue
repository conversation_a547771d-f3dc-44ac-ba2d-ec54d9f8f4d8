<template>
      <el-dialog
        append-to-body
        title="病灶信息"
        v-model="visible"
        width="1070px"
        :close-on-click-modal="false"
        @close="closeDialog"
        @open="openDialog"
        class="my-dialog my-padding-dialog">
        <div class="g-content">
            <header>
                <el-button-icon-fa type="primary" plain icon="el-icon-refresh-right" :loading="loading" @click="mxDoRefresh" >刷 新</el-button-icon-fa>
                <el-button-icon-fa type="primary" plain icon="el-icon-plus" @click="onClickAdd">新 增</el-button-icon-fa>
                <!-- <el-button-icon-fa v-if="rights.Del"  icon="el-icon-delete" @click="onClickDel">删 除</el-button-icon-fa> -->
            </header>
            <section class="lesion-describe-table">
                <el-table v-if="reRender" :data="tableData" ref="table" height="400" border stripe highlight-current-row @row-click="onClickRow" :row-class-name="mxRowClassName" >
                    <el-table-column label="">
                        <el-table-column label="部位" prop="sLesionPart" align="center" min-width="150">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.sLesionPart" clearable></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="大小(cm)" align="center">
                        <el-table-column label="X" prop="fSizeX" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fSizeX"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Y" prop="fSizeY" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fSizeY"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="Z" prop="fSizeZ" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fSizeZ"></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="常规相" align="center">
                        <el-table-column label="SUVmax" prop="fNPSUVmax" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fNPSUVmax"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="SUVmin" prop="fNPSUVmin" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fNPSUVmin"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="SUVavg" prop="fNPSUVavg" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fNPSUVavg"></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="延迟相" align="center">
                        <el-table-column label="SUVmax" prop="fDPSUVmax" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fDPSUVmax"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="SUVmin" prop="fDPSUVmin" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fDPSUVmin"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="SUVavg" prop="fDPSUVavg" align="center" width="90">
                            <template v-slot="scope">
                                <el-input style="text-align: center;"  v-model="scope.row.fDPSUVavg"></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                    <el-table-column label="操作" align="center">
                        <el-table-column align="center">
                            <template v-slot:header="scope">
                                <i class="el-icon-rank i-sort" style="cursor: pointer;font-size: 14px; font-size: 20px;" title="首次或无法排序时，点击初始化排序" @click="autoSort"></i>
                            </template>
                            <template v-slot="scope">
                                <i class="el-icon-delete i-sort" style="cursor: pointer;font-size: 14px; font-size: 20px; margin-right:5px" title="删除" @click="onClickDel(scope.row,scope.$index)"></i>
                                <i v-if="scope.row.sId" class="el-icon-rank i-sort" style="cursor: pointer; font-size: 20px;"></i>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </section>
        </div>
        <template #footer>
          <div  class="my-dialog-footer">
              <div class="g-page-footer">
                  <div style="float: left;">
                      <!-- <el-button-icon-fa v-if="rights.MoveUp"  _icon="fa fa-direction-up" @click="onClickMove('top')">上 移</el-button-icon-fa>
                      <el-button-icon-fa v-if="rights.MoveDown"  _icon="fa fa-direction-down" @click="onClickMove('down')">下 移</el-button-icon-fa> -->
                  </div>
                  <el-button-icon-fa type="primary"  icon="el-icon-document-checked" @click="onClickSave" :loading="loading">保存</el-button-icon-fa>
                  <el-button-icon-fa icon="el-icon-close" @click="closeDialog"  >关闭</el-button-icon-fa>
              </div>
          </div>
        </template>
      </el-dialog>
</template>
<script>

import { mixinTable, getStoreNameByRoute } from '$supersetResource/js/projects/apricot/index.js'
import Sortable from 'sortablejs'
// 接口
import Api from '$supersetApi/projects/apricot/case/report.js'
export default {
    mixins: [mixinTable],
	props: {
		dialogVisible: {
			type: Boolean,
			default: false
		}
    },
    data() {
        return {
            visible: false,
            loading: false,
            firstLoad: true,
            page: { // 分页	
                pageCurrent: 1,
                pageSize: 9999,
            },
            reRender: true
        }
    },
	watch: {
		dialogVisible(){
			this.visible = this.dialogVisible;
		}
    },
    computed: {
        patientInfo() {
            const module = getStoreNameByRoute(this.$route.name);
            if (this.$store.state.apricot[module]){
                return this.$store.state.apricot[module].patientInfo || {};
            }
            return {};
        },
    },
    mounted(){
        this.firstLoad = false;
    },
    methods: {
        autoSort() {
            if (!this.tableData.length) {
                return;
            }
            Api.autoSortLesionDescribe({
                sPatientId: this.patientInfo.sId
            }).then(res => {
                if (res.success) {
                    this.$message.success(res.msg);
                    this.mxGetTableList();
                    return;
                }
                this.$message.error(res.msg);
            })
        },
        // 元素页面表格 行拖拽 
        rowDrop() {
            const tbody = document.querySelector('.lesion-describe-table .el-table__body-wrapper tbody');
            const _this = this;
            let updateFnc = ({
                newIndex,
                oldIndex
            }) => {
                if (newIndex === oldIndex) return;
                Api.sortLesionDescribe({
                    // iIndexOld: oldIndex,
                    // iIndexNew: newIndex,
                    iIndexOld: _this.tableData[oldIndex].iIndex,
                    iIndexNew: _this.tableData[newIndex].iIndex,
                    sId: _this.tableData[oldIndex].sId,
                }).then(res => {
                    if (res.success) {
                        const currRow = _this.tableData.splice(oldIndex, 1)[0]
                        _this.tableData.splice(newIndex, 0, currRow)
                        _this.reRender = false
                        _this.$nextTick(() => {
                            _this.reRender = true
                            _this.$nextTick(() => {
                                // this.mxGetTableList();
                                new Sortable(document.querySelector('.lesion-describe-table .el-table__body-wrapper tbody'), {
                                    animation: 200,
                                    onEnd: updateFnc,
                                })
                            })
                        })
                    }
                })
            }
            Sortable.create(tbody, {
                handle: ".i-sort",
                animation: 200,
                onEnd: updateFnc,
            })
        },
        // mxDoRefresh 会回调这个方法--获取表格数据
        getData(params){
            if (this.firstLoad) return;
            params.condition.sPatientId = this.patientInfo.sId;
            delete params.orders;
            Api.getLesionDescribeData(params).then((res) =>{
                this.loading = false;
                if(res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                }
                this.$nextTick(() => {
                    this.rowDrop()
                })
            }).catch(() => {
                this.loading = false;
            })
        },
        // 点击保存
        onClickSave(){
            if (!this.tableData.length){
                this.$message.info('无保存的数据')
                return;
            }
            this.loading = true
            Api.addLesionDescribe(this.tableData).then(res => {
                this.loading = false
                if (res.success){
                    this.tableData = res.data
                    this.$message.success(res.msg)
                    return;
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false
            })
        },
        // 点击新增
        onClickAdd(){
            let len = this.tableData.length;
            let iIndex = len ? this.tableData[len - 1].iIndex + 1 : len + 1 ;
            this.tableData.push({
                fDPSUVavg: '',
                fDPSUVmax: '',
                fDPSUVmin: '',
                fNPSUVavg: '',
                fNPSUVmax: '',
                fNPSUVmin: '',
                fSizeX: '',
                fSizeY: '',
                fSizeZ: '',
                sPatientId: this.patientInfo.sId,
                iIndex: iIndex
            })
        },
        // 点击删除
        onClickDel(row, index){
            // if (!Object.keys(this.editLayer.selectedItem).length){
            //     this.$message.info('请先点选某行数据')
            //     return;
            // }
            if (row.sId){

                this.$confirm('您确定要删除吗？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'error'
                }).then(() => {
                    Api.delLesionDescribe({sId: row.sId, iVersion: row.iVersion}).then(res => {
                        if (res.success){
                            this.$message.success(res.msg)
                            this.tableData.splice(index, 1)
                            // this.editLayer.selectedItem = {}
                            return;
                        }
                        this.$message.error(res.msg)
                    })
                }).catch(() => {});
                return;
            }
            this.tableData.splice(index, 1)
            // this.editLayer.selectedItem = {}
        },
        // 点击上下移动
        onClickMove(direction = 'top'){
            if (!Object.keys(this.editLayer.selectedItem).length){
                this.$message.info('请先点选某行数据')
                return;
            }
            const len = this.tableData.length;
            let index = this.editLayer.selectedItem.index;
            if (direction === 'top'){
                // 向上移动
                if (index === 0){
                    return;
                }
                // 交互位置
                const temp = Object.assign({}, this.tableData[index - 1], {index: index})
                this.tableData[index - 1] = Object.assign({}, this.tableData[index], {index: index - 1})
                this.tableData[index] = temp
                // 更新选中值、效果
                this.$refs.table && this.$refs.table.setCurrentRow(this.tableData[index - 1])
                this.editLayer.selectedItem = this.tableData[index - 1];
            }else {
                // 向下移动
                if (index + 1 === len){
                    return;
                }
                const temp = Object.assign({}, this.tableData[index + 1], {index: index})
                this.tableData[index + 1] = Object.assign({}, this.tableData[index], {index: index + 1})
                this.tableData[index] = temp
                this.$refs.table && this.$refs.table.setCurrentRow(this.tableData[index + 1])
                this.editLayer.selectedItem = this.tableData[index + 1];
            }
        },
        openDialog(){
            this.mxDoRefresh()
        },
        closeDialog(){
			this.$emit('update:dialogVisible', false);
		},
    }
}
</script>
<style lang="scss" scoped>
.g-content{
    header{
        padding: 10px 10px;
    }
    > section{
        padding: 0px 10px;
        margin-bottom: 20px;
    }
}
</style>
