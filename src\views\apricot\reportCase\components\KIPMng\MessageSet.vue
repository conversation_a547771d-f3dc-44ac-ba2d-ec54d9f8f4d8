<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa type="primary"
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain
                    type="primary"
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content">
                <!-- @row-click="handleRowClick" -->
                <el-table :data="tableData"
                    id="itemTable"
                    ref="mainTable"
                    size="small"
                    border
                    stripe
                    height="100%"
                    style="width: 100%"
                    v-loading="loading">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort">
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'iReceiverType'">
                                {{ optionsLoc.receiverTypeOptions.filter(item => item.sValue == scope.row.iReceiverType)[0]?.sName  }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                {{ scope.row.iIsEnable == 1 ? '启用': '禁用' }}
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'action'">
                                <el-button size="small"
                                    link
                                    type="primary"
                                    @click="handleEdit(scope.row)">
                                    编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit"
                                            color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small"
                                    link
                                    class
                                    @click="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete"
                                            color="">
                                        </Icon>
                                    </template>
                                </el-button>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            v-model="dialogVisible"
            append-to-body
            class="t-default my-dialog"
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog">
            <div class="flex">
                <el-form :model="editLayer.form"
                    ref="refEditLayer"
                    label-width="100px"
                    :rules="rules">
                    <el-col :span="24">
                        <el-form-item prop="sName"
                            label="模板名称：">
                            <el-input v-model="editLayer.form.sName"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="iReceiverType"
                            label="接收角色：">
                            <el-select v-model="editLayer.form.iReceiverType"
                                style="width: 100%;"
                                placeholder=" "
                                clearable>
                                <el-option v-for="item in optionsLoc.receiverTypeOptions"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="iIsEnable"
                            label="状 态：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sContent"
                            label="内 容：">
                            <el-input v-model="editLayer.form.sContent"
                                type="textarea"
                                :rows="8"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer>
                <el-button-icon-fa :loading="editLayer.loading"
                    icon="el-icon-check"
                    type="primary"
                    @click="handleSave">保 存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog"
                    icon="el-icon-close">取 消</el-button-icon-fa>
            </template>
        </el-dialog>
    </div>
</template>
<script>
import { receiverTypeOptions } from '$supersetResource/js/projects/apricot/enum.js';
import { kipSmsTemplateList, kipSmsTemplateSave, kipSmsTemplateDel } from '$supersetApi/projects/apricot/reportCase/kip.js';
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js';
export default {
    // name: 'ThirdLink',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            dialogVisible: false,
            dialogTitle: '',
            loading: false,
            configTable: [{
                sProp: 'sName',
                sLabel: '模板名称',
                sAlign: 'left',
                sMinWidth: '100px'
            }, {
                sProp: 'iReceiverType',
                sLabel: '接收角色',
                sAlign: 'left',
                sMinWidth: '100px'
            }, {
                sProp: 'iIsEnable',
                sLabel: '状态',
                sAlign: 'left',
                sMinWidth: '100px'
            }, {
                sProp: 'sContent',
                sLabel: '内容',
                sAlign: 'left',
                sMinWidth: '300px'
            }, {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '170px'
            }],
            rules: {
                sName: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
                iReceiverType: [{ required: true, message: '接收角色不能为空', trigger: 'blur' }],
                sContent: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
            },
            condition: {},
            optionsLoc: {
                receiverTypeOptions,
            },
            defualtVal: {
                editLayer: {
                    sName: '',
                    iReceiverType: '',
                    iIsEnable: 1,
                    sContent: ''
                },
            },

        }
    },
    methods: {
        // 新增
        handleAdd () {
            this.actionState = 1
            this.dialogVisible = true
            this.dialogTitle = '新增'
            this.mxOpenDialog(1, 'no-title');
        },
        closeDialog () {
            this.dialogVisible = false
        },
        handleEdit (row) {
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true;
            this.editLayer.form = Object.assign({}, row);
            this.$nextTick(() => {
                this.$refs['refEditLayer'].clearValidate();
            })
        },
        handleSave () {
            this.editLayer.loading = true
            let params = Object.assign({}, this.editLayer.form)
            this.$refs['refEditLayer'].validate((valid) => {
                if (valid) {
                    this.saveData(params)
                    return
                }
                this.editLayer.loading = false
            })
        },
        // 保存数据
        saveData (params) {
            this.editLayer.loading = false;
            let loading = this.$loading({
                lock: true,
                text: '保存中,请稍等...',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            kipSmsTemplateSave(params).then(res => {
                loading.close();
                if (res.success) {
                    this.dialogVisible = false
                    this.mxOpenDialog(1, 'no-title');
                    this.mxGetTableList();
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(err => {
                console.log(err);
                loading.close()
            })
        },
        // 删除数据
        onClickDel (row) {
            this.$confirm(`确定要删除模板【${row.sName}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                const { sId } = row;
                kipSmsTemplateDel({ sId }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },

        // 获取表格数据
        getData (params) {
            // const p = Object.assign({}, params, { page: { pageSize: 500 } })
            kipSmsTemplateList().then((res) => {
                if (res.success) {
                    this.tableData = res.data || [];
                    this.loading = false;
                    // 赋选中状态
                    // this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        }
    },
    mounted () { },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px;
    :deep(.c-form) {
        display: flex;
        padding-bottom: 10px;
        justify-content: space-between;

        .c-form-search {
            min-width: 400px;
            width: 36%;
            display: flex;
            > div {
                width: 50%;
                margin: 0 5px;
            }
        }
        .el-textarea__inner {
            border-color: #dcdfe6;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
}

.text-color {
    color: var(--el-color-primary) !important;
    &:active {
        color: white !important;
    }
}
</style>

