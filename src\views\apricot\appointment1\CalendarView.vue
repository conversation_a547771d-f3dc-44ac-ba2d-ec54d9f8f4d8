<template>
    <div class="container-top">
        <el-tabs v-model="hospitalId"
            @tab-click="onTabClick" >
            <el-tab-pane v-for="(item, index) in districtData"
                :ref="`tabItemOne${index}`"
                :key="index"
                :label="item.sDistrictPrefix"
                :name="item.sHospitalDistrictId">
            </el-tab-pane>
        </el-tabs>
        <ul class="device-type-list">
            <li v-for="(item, index) in roomData" 
            :class="{'active': item.sRoomId == roomId}"
            :ref="`tabItemTwo${index}`"
            @click="onClickRoom(item)"
            :key="item.sId">{{ item.sRoomName }}</li>
        </ul>
        <div class="month-view">
            <el-calendar v-model="date" ref="calendar">
                <template #header="{ date }">
                    <span style="line-height: 28px;">{{ date }}</span>
                    <el-button-group>
                        <el-button size="small" @click="selectDate('prev-month')" :icon="ArrowLeft"></el-button>
                        <el-button size="small" @click="selectDate('today')">今</el-button>
                        <el-button size="small" @click="selectDate('next-month')" :icon="ArrowRight"></el-button>
                    </el-button-group>
                    <el-button size="small" @click="()=> refresh = !refresh" :icon="Refresh">刷新</el-button>
                </template>
                <template v-slot:date-cell="{ data: { date } }">
                    <div :class="{'i-color': [0, 6].includes(new Date(date).getDay())}">
                        <strong v-if="date" class="date">{{  date.getDate() }} </strong> <br />
                        <span class="number-value"
                            v-if="summaryData[transformDate(date)]">
                            {{summaryData[transformDate(date)].registered}}/{{summaryData[transformDate(date)].limit}}
                        </span>
                        <span v-else>&nbsp;</span>
                    </div>
                </template>
            </el-calendar>
        </div>
    </div>
</template>
<script setup>
    /**
     * 预约登记-左上角-病区-机房-日历选择模块
     */
    import { getHospitalAndRoomData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
    import { queryCalendar } from '$supersetApi/projects/apricot/appointment/index.js'
    import { transformDate } from '$supersetResource/js/tools'
    import { ElMessage } from 'element-plus'
    import { ArrowLeft, ArrowRight, Refresh } from '@element-plus/icons-vue'
    import { useStore } from 'vuex'

    const props = defineProps({
        modelValue: Object,
    })
    const emits = defineEmits(['update:modelValue', 'getHospitalData'])
    const store = useStore()
    // 工作站
    const workStation = computed(() => {
        let temp = store.getters['user/workStation']
        return temp
    })
    // 院区
    const hospitalId = computed({
        get: function () {
            return props.modelValue.hospitalId
        },
        set: function (val) {
            props.modelValue.hospitalId = val
        }
    })
    // 获取院区
    const districtData = ref([])
    const getHospital = () => {
        getHospitalAndRoomData().then((res) => {

            if (res.success) {
                let data = res.data || []
                // 过滤未开启的
                data = data.filter(district => {
                    if (district.enable) {
                        const rooms = district.rooms.filter(room => room.enable)
                        district.rooms = rooms || []
                        return true
                    }
                    return false
                })
                districtData.value = data
                emits('getHospitalData', data)
                // 查找工作站，有工作站设置为默认选择
                const findItem = districtData.value.find(item => item.sHospitalDistrictId == workStation.value.districtId)
                if (localCache.hospitalId) {
                    hospitalId.value = localCache.hospitalId
                    localCache.hospitalId = null
                }else if (findItem) {
                    hospitalId.value = findItem.sHospitalDistrictId
                }else if (districtData.value[0]) {
                    hospitalId.value = districtData.value[0].sHospitalDistrictId
                }
            }
        })
    }
    // 点击院区
    const onTabClick = () => {
        nextTick(() => {
            setCache()
        })
        // emits('update:hospitalId', districtData.value[item.index].sId)
    }
    // 机房
    const roomId = computed({
        get: function () {
            return props.modelValue.roomId
        },
        set: function (val) {
            props.modelValue.roomId = val
        }
    })
    // 获取设备类型
    const roomData = ref([])

    // 监听院区变化, 获取机房数据
    watch(() => hospitalId.value, (later) => {
        if (later) {
            const findData = districtData.value.find(item => item.sHospitalDistrictId == later)
            if (findData) {
                roomData.value = findData.rooms
                if (localCache.roomId) {
                    roomId.value = localCache.roomId
                    props.modelValue.roomName = localCache.roomName
                    props.modelValue.sDeviceTypeId = localCache.sDeviceTypeId
                    localCache.roomId = null
                }else if (findData.rooms[0]) {
                    roomId.value = findData.rooms[0].sRoomId
                    props.modelValue.roomName = findData.rooms[0].sRoomName
                    props.modelValue.sDeviceTypeId = findData.rooms[0].sDeviceTypeId
                }else {
                    roomId.value = ''
                    props.modelValue.roomName = ''
                    props.modelValue.sDeviceTypeId = ''
                }
            }
        }
    }, { immediate: true, deep: true })
    
    // 点击机房
    const onClickRoom = (item) => {
        if (item.sRoomId === roomId.value) {
            return
        }
        props.modelValue.roomName = item.sRoomName
        props.modelValue.sDeviceTypeId = item.sDeviceTypeId
        roomId.value = item.sRoomId
        setCache()
    }
    // 监听机房变化，获取日历数据
    watch(() => roomId.value, (later) => {
        if (later) {
            if (!date.value) {
                date.value = new Date()
                nextTick(() => {
                    queryCalendarData()
                })
            }else {
                queryCalendarData()
            }
        }
    }, { immediate: false, deep: true })

    // 刷新计算属性
    const refresh = computed({
        get: function () {
            return props.modelValue.refresh
        },
        set: function (val) {
            props.modelValue.refresh = val
        }
    })
    // 监听刷新
    watch(() => refresh.value, (later) => {
        queryCalendarData()
    }, { immediate: false, deep: true })

    // 日历
    const prevDate = ref(new Date())
    const summaryData = ref({})

    const date = computed({
        get: function () {
            return props.modelValue.date
        },
        set: function (val) {
            props.modelValue.date = val
        }
    })

    watch(() => date.value, (later) => {
        if (later) {
            handleGetDate(later)
        }
    })
    
    const calendar = ref()
    const selectDate = (val) => {
        calendar.value.selectDate(val)
    }
    // 获取数据
    const handleGetDate = (date) => {
        if(prevDate.value.getMonth() != date.getMonth()) {
            queryCalendarData(date)
        }
        prevDate.value = date
    }

    // 获取日历数据
    const queryCalendarData = (params) => {
        let dateItem = params || date.value
        let year = dateItem.getFullYear()
        let month = dateItem.getMonth() + 1
        month = month < 10 ? '0' + month : month
        queryCalendar({
            hospitalDistrictId: hospitalId.value,
            roomId: roomId.value,
            yearMonth: year + '' + month
        }).then((res) => {
            if (res.success) {
                let data = res.data || [];
                summaryData.value = {};
                if (data.length) {
                    data.map(item => {
                        let iDate = item.iDate + '';
                        item.registered = item.registered || 0;
                        summaryData.value[`${iDate.substring(0, 4)}-${iDate.substring(4, 6)}-${iDate.substring(6, 8)}`] = item;
                    })
                }
                return
            }
            ElMessage.error(res.msg)
        })
    }
    
    let localCache = {}
    const localKey = 'CalendarViewCache'
    function getCache() {
        const data = localStorage.getItem(localKey)
        localCache = data ? JSON.parse(data) : {}
    }
    function setCache() {
        nextTick(() => {
            const data = {
                hospitalId: hospitalId.value,
                roomId: roomId.value,
                roomName: props.modelValue.roomName,
                sDeviceTypeId: props.modelValue.sDeviceTypeId,
            }
            localStorage.setItem(localKey, JSON.stringify(data))
        })

    }
    // 一开始加载院区数据
    onMounted(() => {
        getCache()
        getHospital()
    })
</script>
<style lang="scss" scoped>
.container-top {
    padding: 2px 12px 12px;
    user-select: none;
    .device-type-list {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        margin: 13px 0 6px;
        li {
            font-weight: 400;
            list-style: none;
            width: 31%;
            height: 28px;
            line-height: 22px;
            padding: 3px 14px;
            border-radius: 2px;
            border: 1px solid rgba(220, 223, 230, 1);
            margin-bottom: 7px;
            margin-right: 7px;
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            cursor: pointer;
            &:hover {
                border-color: var(--el-color-info-dark-2);
            }
            &.active {
                border-color: var(--el-color-primary);
                color: var(--el-color-primary);
                background: rgba(240, 244, 255, 1);
            }
        }
    }
    .month-view {
        // height: 345px;
        border: 1px solid #eee;
        :deep(.el-calendar) {
            height: 100%;
            .el-calendar__header {
                padding: 5px 10px;
                .el-calendar__title {
                    font-size: 14px;
                    font-weight: 600;
                    color: #333;
                }
            }
            .el-calendar__body {
                height: calc(100% - 50px);
                padding: 5px 10px;
            }
            .el-calendar-table {
                height: calc(100% - 0px);
                thead th {
                    padding: 4px 0;
                    color: #555;
                    text-align: center;
                }
                tbody {
                    height: calc(100% - 30px);
                    .el-calendar-day {
                        // min-height: 40px;
                        height: 100%;
                        padding: 2px;
                        text-align: center;
                        > div {
                            position: relative;
                            padding: 6px;
                            height: 46px;
                            box-sizing: border-box;
                        }
                        .number-value {
                            font-size: 12px;
                        }
                    }
                    .current:not(.is-today) .el-calendar-day span.number-value {
                        color: #555;
                    }
                    .el-calendar-day {
                        .date {
                            display: inline-block;
                            padding: 1px 2px;
                        }
                        .date, .number-value {
                            z-index: 1;
                            position: inherit;
                        }
                    }
                    .el-calendar-day:hover {
                        background-color: white;
                        > div {
                            &:before {
                                content: "";
                                position: absolute;
                                left: 0px;
                                top: 0px;
                                width: 100%;
                                height: 100%;
                                border-radius: 50%;
                                background: #f2f2f2;
                                z-index: 0;
                            }
                        }
                    }
                    td.is-selected {
                        background-color: white;
                        .el-calendar-day > div {
                            &:before {
                                content: "";
                                position: absolute;
                                left: 0px;
                                top: 0px;
                                width: calc(100% - 2px);
                                height: calc(100% - 2px);
                                border: 2px solid var(--el-color-primary);
                                border-radius: 50%;
                            }
                        }
                        // &.is-today .el-calendar-day > div:before{
                        //     border: 1px solid var(--el-color-primary);
                        // }
                    }
                    .is-today {
                        .el-calendar-day {
                            position: relative;
                            &:before {
                                content: "今";
                                position: absolute;
                                left: 0px;
                                top: 0px;
                                text-align: center;
                                color: #000;
                                z-index: 1;
                                background: #ebeef5;
                                font-size: 28px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                padding-top: 2px;
                                width: 100%;
                                height: 100%;
                                font-weight: bold;
                                opacity: 0.1;
                            }
                            .date {
                                // background: #0d6de5;
                                // border-radius: 2px;
                                // color: white;
                                // background: var(--el-color-primary);
                                // color: var(--el-color-white);
                                // border-radius: 50%;
                            }
                            > div {
                                // background: rgba(42, 130, 228, 1);
                                // color: white;
                                border-radius: 50%;
                                min-width: 20px;
                                &:before {
                                    // background: #4387bd;
                                }
                            }
                        }

                    }
                }
            }
        }
    }
    // :deep(.el-tabs) {
    //     .el-tabs__nav-next, .el-tabs__nav-prev {
    //         line-height: 30px;
    //     }
    // }
}
.i-color {
    background-color: #e8eff3;
}
</style>
