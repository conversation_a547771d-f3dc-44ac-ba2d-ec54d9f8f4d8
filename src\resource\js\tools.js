/*为路由添加属性----------------------------------------*/

export function moduleListInit(moduleList, initData) {

    for (let i = 0; i < moduleList.length; i++) {
        let item = moduleList[i]

        let prefix = initData.projectName ? initData.projectName + '_' : ''
        /*权限列表设置--------------------------------*/
        //如果不存在此属性，则先补充
        if (!item['meta']) {
            item['meta'] = {}
        }

        /*debugger*/
        item['meta']['componentName'] = initData.projectName + '_' + item.name
        /*--------------------------------权限列表设置*/
        /*name/path添加前缀-------------------------------*/
        if (item['name']) {
            item['name'] = prefix + item['name']
        }
        if (item['path']) {
            item['path'] = prefix + item['path']
        }
        /*-------------------------------name/path添加前缀*/
    }


}
/*----------------------------------------为路由添加属性*/

/*菜单分组------------------------------------------------*/
export function menuGrouping(menuList) {
    let arr = menuList;
    /*arr.sort(function(a, b){
    	return a.group - b.group
    });*/
    let len = arr.length - 1;
    for (let i = 0; i < len; i++) {
        if (arr[i].group != arr[i + 1].group) {
            arr[i]["isBorder"] = true;
        } 
        else{
            arr[i]["isBorder"] = false;
        }
        if(arr[i].group == '操作组4'){
            arr[i]["isBorder"] = true;
        }
    }
    for(let  j=len;j >=0; j-- ){
        if (arr[j].iIsHide === 0 ) {
            arr[j]["isBorder"] = true;
            break;
        }
    }
    return arr;
}
/*------------------------------------------------菜单分组*/

/*菜单分组2------------------------------------------------*/
export function menuPartitioning(menuList) {
    let arr = []
    // let len = menuList.length - 1
    let len = menuList.length
    if (len === 0) {
        return []
    }
    let j = 0
    arr[j] = []

    for (let i = 0; i < len; i++) {

        arr[j].push(menuList[i])

        if (menuList[i + 1] && menuList[i].group != menuList[i + 1].group) {
            //arr下标加1
            ++j
            //添加一位二维数组
            arr[j] = []
        }
    }
    /*debugger*/
    return arr;
}
/*------------------------------------------------菜单分组2*/



/**
 * 时间戳转换时间
 * @param {*} t 时间
 * @param {*} isHMS 是否要时分秒
 * @param {String} format 年月日间隔符合
 */
export function transformDate(t, isHMS, format = '-') {
    if (t === undefined || t === null || t === '') {
        return;
    }
    let dd = new Date(t);
    let y = dd.getFullYear();
    if (isNaN(y) || t == null) {
        return;
    }
    let m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
    let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
    if (isHMS) {
        let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
        let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
        let s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
        return y + format + m + format + d + ' ' + h + ':' + mm + ':' + s;
    } else {
        return y + format + m + format + d;
    }

}


/**
 * 日期对象取得的星期数 转文字
 * @param {*} day 日期对象取得的星期数 
 */

export function transformDay(day) {
    let list = {
        '0': '星期天',
        '1': '星期一',
        '2': '星期二',
        '3': '星期三',
        '4': '星期四',
        '5': '星期五',
        '6': '星期六',
    }
    // debugger
    return list[day]
}

/**
 * 数组变树
 * @param {*} list 
 * @param {*} pid 
 */
export function arrToTree(list, pid) {
    var fun = function (pid) {
        var pid = pid ? pid : 0;
        var b = [];
        for (var i in list) {
            var item = list[i];
            if (item.pid === pid) {
                item.children = arguments.callee(item.id);
                b.push(item);
            }
        }
        return b;
    }
    return fun(pid);
}


//根据值获取名称（下拉选项）
export function getOptionName(value, options, attr = { find: 'sValue', get: 'sName' }) {
    if (['', undefined].includes(value) || Object.prototype.toString.call(options) !== '[object Array]') return null;
    let item = options.find(item => item[attr.find] === value);
    return item ? item[attr.get] : null
}


export function checkFileExists(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.open('HEAD', url);
    xhr.onload = function() {
        callback(xhr.status !== 404);
    };
    xhr.send();
}
