<template>
    <section v-if="patient.sOpenerId"
        style="display: inline-block;">
        <el-popover placement="top"
            width="160"
            v-model="visible">
            <p>确定解锁报告吗？</p>
            <div style="text-align: right; margin: 0">
                <el-button size="small"
                    link
                    @click="visible = false">取消</el-button>
                <el-button type="primary"
                    size="small"
                    @click="onClickSure">确定</el-button>
            </div>
            <template #reference>
            <el-button-icon-fa size="small"
                round
                _icon="fa fa-lock-fill"
                class="i-lock"
                title="解锁"
                slot="reference"><span class="i-name">{{patient.sOpener}}</span></el-button-icon-fa>
            </template>
        </el-popover>
    </section>
</template>
<script>
import Api from '$supersetApi/projects/apricot/case/report.js'
export default {
    props: {
        patient: {
            type: Object,
            default: () => ({})
        }
    },
    data () {
        return {
            visible: false
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    methods: {
        onClickSure () {
            if (this.patient.sOpenerId != this.userInfo.sId) {
                Api.reportUnlockForce({ patientId: this.patient.sId }).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg)
                        this.$emit('callBack', null)
                        return;
                    }
                    this.$message.error(res.msg)
                })
                this.visible = false
                return
            }
            Api.reportUnlock({ sId: this.patient.sId }).then((res) => {
                if (res.success) {
                    this.$message.success(res.msg)
                    this.$emit('callBack', null)
                    return;
                }
                this.$message.error(res.msg)
            })
            this.visible = false
        }
    },
}
</script>
<style lang="scss" scoped>
.i-lock {
    margin-left: 10px;
    color: #d0966c;
    &:hover {
        border-color: #d0966c;
    }
}
.i-name {
    position: relative;
    top: -2px;
}
</style>
