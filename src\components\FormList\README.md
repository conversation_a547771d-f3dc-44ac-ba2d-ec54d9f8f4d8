# FormList 通用表单列表组件

示例：
```
  <el-form 
    ...
    ref="elForm"  :model="appointmentForm"  label-position="top"     :rules="rules">
    <FormList
      storageKey="patientInfoGroup-1"
       iModuleId="6"
      :list="elementConfigData.patientInfoInputList0"
        v-model:formData="appointmentForm"
        :optionData="optionsLoc"> 
        <template v-slot:sDistrictMachine="{ style }">
            <el-cascader ...
                ...
                :style="style"></el-cascader>
        </template>
    </FormList> 
  </el-form>
  <el-button type="primary" :ref="(el) => buttonRef = el">替换按钮</el-button>
```

属性: 
| 属性名 | 说明 | 格式 | 默认值 | 
|----|--------------|---|---| 
| storageKey  | 保存到localstorage时的唯一标识，不填则不保存   | string | 无  |  
| iModuleId  | 保存到服务器时的moduleId字段，与storageKey同时存在时启用数据上传   | string | 无  |  
| formData  | 包含了表单数据的对象  | object | {}  | 
| list  | 表单的配置数据，格式见示例   | array | []  | 
| optionData  | 包含所有选择项的选项数据的对象   | object | {}  |  
| rules |  校验规则，需要校验时需要在外层用`<el-form>`元素包裹，并传递同样的rules参数   | object | {}  |   
| configBtn  | 配置按钮是否显示，或用于替换默认按钮的ref对象   | boolean,Ref | true  |   
| labelWidth  | 统一设置每个表单项的label-width   | string | 无  |  


插槽:
|  插槽名称 | 说明  |  参数  | 
|-----------|-------------------------|
| “查询条件的prop字段” | 配置了iCustom: true的表单项会使用插槽名字为其prop字段的插槽内容作为渲染组件，附带的style参数需要加到插槽中的element-plus组件中，用于变更自定义样式  | { style }


外层`<el-form>`元素属性：
| 属性名 | 说明 | 格式 | 默认值 |
|----|--------------|---|---| 
| label-position  |  label显示位置，为left或right时左右结构，为top时变为上下结构   | string | 'right'  |  
| rules  | 校验规则，与FormList组件上传递的rules是同一个  | object | {}  | 


配置数据示例：
``` 
formData: {
  sDistrictMachine: 1
},
list: [

      {
          sProp: 'sDistrictMachine',
          sLabel: '院区/机房',
          iIsHide: false,  // 为true时排除掉该配置项
          prop: '',  // 与sProp任选一个
          label: '',  // 与sLabel任选一个

          isShow: true, // 不为false时默认显示该项（可设置）
          required: false, // 是否必填，校验功能需要配合外层的<el-form>组件实现

          iCustom: 1, // 是否使用插槽
          sInputType: 'text', // 组件类型， 支持： 'text', 'number', 'color', 'date-picker', 'dateTime-picker', 'time-picker', 'option', 'text-unit', 'number-unit', 'textarea', 'input-01', 'input-02', 'option-01',

            
          iReadonly: false,            // 内置组件 只读
          sOptionProp: 'districtMachineRoomOptions', // 使用下拉选择项时，在options中找到这个字段对应的数据作为可选项数据
          optionLabel: 'sName',            //  options中的label
          optionValue: 'sValue',            //  options中的value 

          iLimitLength: 0, //  填写数字时，给该字段加入一条字数限制的校验规则
          sRegEx: '',  //  填写正则时，给该字段加入一条需要符合该正则的校验规则

          // 配置了样式属性时，会改变该配置项的默认样式
          align: 'center',
          width: '25%',
          iLayourValue: 3, // 没有传width时默认按 1/24 转化为宽度%

          height: '36px',
          labelWidth: '100px',
          color: '',
          isBold: false,
          fontSize: '14px',
          bgColor: '',


      },
      //  必填：
      {
          sProp: 'sAge',
          sLabel: '年龄', 
      },
],
optionData: {
  districtMachineRoomOptions: [
        {
            sName:'全部',
            sValue: ''
        },
  ],
}
 ```



