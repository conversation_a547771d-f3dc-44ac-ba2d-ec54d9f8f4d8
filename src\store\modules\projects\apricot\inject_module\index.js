const state = {
    patientInfo: {},
    // currentModule: '',
    IsUpdatePatientInfo: false
}

const getters = {

}

const mutations = {
    setPatientInfo(state, payload) {
        state.patientInfo = payload.patientInfo
    },
    setIsUpdatePatientInfo(state, payload) {
        state.IsUpdatePatientInfo = payload.IsUpdatePatientInfo
    },
}

const actions = {}

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
    modules: {}
}