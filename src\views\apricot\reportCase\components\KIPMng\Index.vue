<template>
    <el-dialog v-model="visible"
        title="KIP管理"
        draggable
        fullscreen
        :close-on-click-modal="false"
        class="t-default my-dialog dialog-kip"
        width="1150px">
        <div class="dialog-body">
            <el-tabs v-model="activeName"
                @tab-click="handleClick">
                <el-tab-pane label="KIP日志"
                    name="first">
                    <Log></Log>
                </el-tab-pane>
                <el-tab-pane label="KIP短信模板设置"
                    name="second">
                    <MessageSet></MessageSet>
                </el-tab-pane>
                <el-tab-pane label="KIP报告关键字设置"
                    name="third">
                    <KeywordSet></KeywordSet>
                </el-tab-pane>
            </el-tabs>
        </div>
        <template #footer>
            <el-button-icon-fa plain
                icon="fa fa-close-1"
                @click="visible = false">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
export default {
    components: {
        Log: defineAsyncComponent(()=> import('./Log.vue')),
        MessageSet: defineAsyncComponent(()=> import('./MessageSet.vue')),
        KeywordSet: defineAsyncComponent(()=> import('./KeywordSet.vue')),
    },
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
    },
    emits: ['update:modelValue'],
    data () {
        return {
            activeName: 'first',
        }
    },
    computed: {
        visible: {
            get: function () {
                return this.modelValue
            },
            set: function (val) {
                this.$emit('update:modelValue', val)
            }
        },
    },
    methods: {
        handleClick (tab, event) {
            // console.log(tab, event);
        }
    },
    created () { }
}
</script>
<style>
.dialog-kip {
    display: flex;
    flex-direction: column;
    width: 98%;
    height: 96% !important;
    top: 2%;
}
.dialog-kip .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
}
</style>
<style lang="scss" scoped>
.dialog-body {
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    // background: var(--theme-menu-background-color);
}
.btns {
    display: flex;
    justify-content: end;
    align-items: center;
    .btn-item {
        margin-left: 10px;
    }
}
</style>