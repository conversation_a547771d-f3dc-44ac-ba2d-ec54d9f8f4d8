import { createStore } from 'vuex';

import getters from './getters.js';

/*一级----------------------------------------------*/
// import system from './modules/system'
import user from './modules/user'
import module_router from './modules/module_router'
import apricot from './modules/projects/apricot'
import setting from './modules/setting'
import dict from './modules/dict'
/*----------------------------------------------一级*/


const store = new createStore({
  modules: {
    user,
    module_router,
    apricot,
    setting,
    dict
  },
  state: {
    // appId: 4,  // appId 跟 app 对象中的 id 是分开的
    // app: {
    //   appId: 4,
    //   id: 4
    // },
    // isAdmin: true,
    // funcList: [],
    // funcAuthMap: {},
    // funcGroupList: [],


    // get_token: '',
    // monitorFullscreen: false,
    // language: localStorage.getItem('language') || "zhCh",
    // dictData: null
  },
  getters,
  actions: {

    setFuncList({ state }, list) {
      state.funcList = list
      const authCodeMap = {}
      list.forEach(item => {
        authCodeMap[item.code] = true
      })
      state.funcAuthMap = authCodeMap
    },
    setFuncGroupList({ state }, list) {
      state.funcGroupList = list
    },
  }
});

export default store;
