export default {
    // 搜索条件
    searchStyle0: [
        {
            sProp: 'dMachineTimeSt',
            sLabel: '预约日期从',
            iCustom: 1,
            iLayourValue: 3
        },
        {
            sProp: 'dMachineTimeEd',
            sLabel: '到',
            iCustom: 1,
            iLayourValue: 3
        },
        // {
        //     sProp: 'itemTree',
        //     sLabel: '设备类型/项目',
        //     sInputType: 'text',
        //     iCustom: 1,
        //     iLayourValue: 3
        // },
       
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sInputType: 'text',
            iLayourValue: 3
        },

        {
            sProp: 'sInspectSee',
            sLabel: '检查所见',
            sInputType: 'text',
            iLayourValue: 3
        },
        {
            sProp: 'sDiagnosticOpinion',
            sLabel: '诊断意见',
            sInputType: 'text',
            iLayourValue: 3
        }
    ],
    searchStyle1: [ {
            sProp: 'sApplyDeptName',
            sLabel: '申请科室',
            sInputType: 'option',
            sOptionProp: 'sApplyDeptNameOptions',
            iCustom: 1,
            iLayourValue: 3
        },
        {
            sProp: 'sDistrictId',
            sLabel: '院区',
            sInputType: 'option',
            sOptionProp: 'sDistrictOptions',
            iCustom: 1,
            iLayourValue: 3,
        }, {
            sProp: 'sMachineryRoomId',
            sLabel: '机房',
            sInputType: 'option',
            sOptionProp: "machineRoomArrOption",
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sProjectIds',
            sLabel: '项目',
            sInputType: 'option',
            sOptionProp: "itemsArrOption",
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sInputType: 'text',
            iLayourValue: 3
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sInputType: 'text',
            iLayourValue: 3
        },
        {
            sProp: 'sName',
            sLabel: '患者姓名',
            sInputType: 'text',
            iLayourValue: 3
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sInputType: 'text',
            iLayourValue: 3
        },
        // {
        //     sProp: 'sSex',
        //     sLabel: '性别',
        //     sInputType: 'option',
        //     sOptionProp: 'sSexOptions',
        //     iLayourValue: 3,
        //     iCustom: 1
        // },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            sInputType: 'text',
            iLayourValue: 3
        },
        {
            sProp: 'sSource',
            sLabel: '就诊类型',
            sInputType: 'option',
            sOptionProp: 'visitTypeOptions',
            iLayourValue: 3,
            iCustom: 1
        },
        // {
        //     sProp: 'iAgeMin',
        //     sLabel: '年龄（min）',
        //     sInputType: 'text',
        //     iLayourValue: 3
        // },
        // {
        //     sProp: 'iAgeMax',
        //     sLabel: '年龄（max）',
        //     sInputType: 'text',
        //     iLayourValue: 3
        // },
        // {
        //     sProp: 'sAgeUnit',
        //     sLabel: '年龄单位',
        //     sInputType: 'option',
        //     sOptionProp: 'sAgeUnitOptions',
        //     iLayourValue: 3
        // },
        // {
        //     sProp: 'fHeightMin',
        //     sLabel: '身高（min）',
        //     sInputType: 'text',
        //     iLayourValue: 3
        // },
        // {
        //     sProp: 'fHeightMax',
        //     sLabel: '身高（max）',
        //     sInputType: 'text',
        //     iLayourValue: 3
        // },
        // {
        //     sProp: 'fWeightMin',
        //     sLabel: '体重（min）',
        //     sInputType: 'text',
        //     iLayourValue: 3
        // },
        // {
        //     sProp: 'fWeightMax',
        //     sLabel: '体重（max）',
        //     sInputType: 'text',
        //     iLayourValue: 3
        // },
       
        {
            sProp: 'sPositionText',
            sLabel: '检查部位',
            sInputType: 'text',
            iCustom: 1,
            iLayourValue: 3
        },
        // {
        //     sProp: 'sTestModeText',
        //     sLabel: '检查方式',
        //     sInputType: 'text',
        //     iCustom: 1,
        //     iLayourValue: 3
        // },
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            sInputType: 'text',
            iCustom: 1,
            iLayourValue: 3
        },
        // {
        //     sProp: 'sTracerText',
        //     sLabel: '药物',
        //     sInputType: 'text',
        //     iCustom: 1,
        //     iLayourValue: 3
        // },
        {
            sProp: 'caseTree',
            sLabel: '病例类型',
            sInputType: 'text',
            iCustom: 1,
            iLayourValue: 3
        },
        {
            sProp: 'sInjectName',
            sLabel: '注射医生',
            sInputType: 'text',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sMachineName',
            sLabel: '上机医生',
            sInputType: 'text',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sReporterName',
            sLabel: '报告医生',
            sInputType: 'text',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sExamineName',
            sLabel: '审核医生',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sApplyDrName',
            sLabel: '申请医生',
            sInputType: 'option',
            sOptionProp: 'sApplyDrNameOptions',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sQualitative',
            sLabel: '阴阳性',
            sInputType: 'option',
            sOptionProp: 'sQualitativeOptions',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sDiagnosticAccord',
            sLabel: '诊断符合',
            sInputType: 'option',
            sOptionProp: 'sDiagnosticAccordOptions',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sImgQuality',
            sLabel: '图像质量',
            sInputType: 'option',
            sOptionProp: 'sImgQualityOptions',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sReportQuality',
            sLabel: '报告质量',
            sInputType: 'option',
            sOptionProp: 'sReportQualityOptions',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'iIsPregnant',
            sLabel: '怀孕',
            sInputType: 'option',
            sOptionProp: 'iIsPregnantOptions',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'iIsReportCommit',
            sLabel: '报告提交',
            sOptionProp: 'iIsReportCommitOptions',
            sInputType: 'option',
            iLayourValue: 3
        },
        {
            sProp: 'iIsApprove',
            sLabel: '报告审核',
            sOptionProp: 'iIsApproveOptions',
            sInputType: 'option',
            iLayourValue: 3
        },
        {
            sProp: 'iIsFinalApprove',
            sLabel: '报告复审',
            sOptionProp: 'iIsFinalApproveOptions',
            sInputType: 'option',
            iLayourValue: 3
        },
        {
            sProp: 'sCollectionMemo',
            sLabel: '收藏标签',
            sInputType: 'option',
            sOptionProp: 'sCollectionMemoOptions',
            iCustom: 1,
            iLayourValue: 3
        },
        {
            sProp: 'sFollowUpResult',
            sLabel: '随访记录',
            sInputType: 'text',
            iLayourValue: 3
        },
        {
            sProp: 'sReportQualityDoc',
            sLabel: '报告评级医生',
            sInputType: 'text',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sImgQualityDoc',
            sLabel: '影像评级医生',
            iLayourValue: 3,
            iCustom: 1,
        },
        {
            sProp: 'sReportQualityMemo',
            sLabel: '报告评级备注',
            sMinWidth: '200px',
            iLayourValue: 3,
            sInputType: 'text',
        },
        // {
        //     sProp: 'sImgQualityMemo',
        //     sLabel: '影像评级备注',
        //     sMinWidth: '200px', 
        //     iLayourValue: 3,
        //     sInputType: 'text',
        // },
       
    ],
    // 患者 table 表
    patientTable: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNameSpell',
            sLabel: '姓名拼音',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'fHeight',
            sLabel: '身高（cm）',
            sAlign: 'center',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'fWeight',
            sLabel: '体重（kg）',
            sAlign: 'center',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'sApplyDeptName',
            sLabel: '申请科室',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sRoomText',
            sLabel: '设备类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sMinWidth: '110px'
        },
        {
            sProp: 'dMachineTime',
            sLabel: '预约日期',
            sMinWidth: '110px'
        },
        {
            sProp: 'sDistrictName',
            sLabel: '院区',
            sMinWidth: '150px'
        },
        {
            sProp: 'sRecordText',
            sLabel: '病例类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sQualitativeText',
            sLabel: '阴阳性',
            sMinWidth: '110px'
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sMinWidth: '150px'
        },
        {
            sProp: 'sDiagnosticOpinionText',
            sLabel: '诊断意见',         
            sMinWidth: '150px'
        },
        {
            sProp: 'sInspectSeeText',
            sLabel: '检查所见',
            sMinWidth: '150px'
        },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            sMinWidth: '150px'
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sPositionText',
            sLabel: '检查部位',
            sMinWidth: '110px'
        },
        {
            sProp: 'sTestModeText',
            sLabel: '检查方式',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            sMinWidth: '110px'
        },
        {
            sProp: 'sTracerText',
            sLabel: '示踪剂',
            sMinWidth: '100px'
        },
        {
            sProp: 'sDiagnosticAccordText',
            sLabel: '诊断符合',
            sMinWidth: '110px'
        },
        {
            sProp: 'sImgQualityText',
            sLabel: '图像质量',
            sMinWidth: '110px'
        },
        {
            sProp: 'sReportQualityText',
            sLabel: '报告质量',
            sMinWidth: '110px'
        },
        {
            sProp: 'sSubscribePerson',
            sLabel: '登记人',
            sMinWidth: '110px'
        },
        {
            sProp: 'dSubscribeTime',
            sLabel: '登记时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'sInjectName',
            sLabel: '注射医生',
            sMinWidth: '110px'
        },
        {
            sProp: 'dInjectTime',
            sLabel: '注射时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'sMachineName',
            sLabel: '上机医生',
            sMinWidth: '110px'
        },
        {
            sProp: 'dFactCheckTime',
            sLabel: '上机时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'sReporterName',
            sLabel: '报告医生',
            sMinWidth: '110px',
            sOrderTable: 'Report',
            sOrderField: 'sReporterName'
        },
        {
            sProp: 'sExamineName',
            sLabel: '审核医生',
            sMinWidth: '110px'
        },
        {
            sProp: 'dReporterTime',
            sLabel: '报告时间',
            sMinWidth: '130px'
        },
        {
            sProp: 'dReportTime',
            sLabel: '提交时间',
            sMinWidth: '130px'
        },
        {
            sProp: 'dExamineDate',
            sLabel: '审核时间',
            sMinWidth: '130px'
        },
        {
            sProp: 'dFinalExamineDate',
            sLabel: '复审时间',
            sMinWidth: '130px'
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'iIsReportCommit',
            sLabel: '报告提交',
            sMinWidth: '110px'
        },
        {
            sProp: 'iIsApprove',
            sLabel: '报告审核',
            sMinWidth: '110px'
        },
        {
            sProp: 'sReportQualityDoc',
            sLabel: '报告评级医生',
            sMinWidth: '110px',
            // iIsHide: true
        },
        {
            sProp: 'sImgQualityDoc',
            sLabel: '影像评级医生',
            sMinWidth: '110px', 
            // iIsHide: true
        },
        {
            sProp: 'sReportQualityMemo',
            sLabel: '报告评级备注',
            sMinWidth: '200px',
            // iIsHide: true
        },
        {
            sProp: 'sImgQualityMemo',
            sLabel: '影像评级备注',
            sMinWidth: '200px', 
            // iIsHide: true
        },
        {
            sProp: 'sFollowUpRecord',
            sLabel: '随访记录',
            sAlign: 'left',
            sMinWidth: '200px',
        },
        {
            sProp: 'sIdNum',
            sLabel: '身份证号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dBirthday',
            sLabel: '出生日期',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sMachineryRoomText',
            sLabel: '机房',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineStationName',
            sLabel: '工作站',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sVitisNo',
            sLabel: '就诊号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sEncounter',
            sLabel: '就诊次数',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideSupName',
            sLabel: '核素全称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTracerSupName',
            sLabel: '示踪剂全称',
            sMinWidth: '120px',
            iIsHide: true
        },
        {
            sProp: 'fRecipeDose',
            sLabel: '处方剂量',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'iIsPregnant',
            sLabel: '怀孕',
            sAlign: 'center',
            sMinWidth: '80px',
            iIsHide: true
        },
        {
            sProp: 'sPhone',
            sLabel: '联系电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sAddress',
            sLabel: '住址',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMaritalStatusName',
            sLabel: '婚姻状况',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sImageNo',
            sLabel: '影像号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientAreaText',
            sLabel: '病区名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientWardText',
            sLabel: '病房名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sBedNum',
            sLabel: '床号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sCardNum',
            sLabel: '社保卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fBloodSugar',
            sLabel: '检查空腹血糖',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPresentHistory',
            sLabel: '现病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sPastHistory',
            sLabel: '既往史疾病',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalSymptoms',
            sLabel: '临床症状',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefComplaint',
            sLabel: '主诉',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInvoiceNum',
            sLabel: '发票号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sFeeTypeText',
            sLabel: '费用类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fFees',
            sLabel: '费用',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChargeStateText',
            sLabel: '收费状态',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dApplyDate',
            sLabel: '申请时间',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianName',
            sLabel: '主治医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPhysicalExamNo',
            sLabel: '体检号',
            sMinWidth: '110px',
            iIsHide: true
        }
    ],

    DA1: {
        type: 't-y',
        localStorageKey: '202101061113',
        panelConfig: [{
                size: 180,
                minSize: 180,
                maxSize: 500,
                name: "c1",
                isFlexible: false
            },
            {
                size: 0,
                minSize: 45,
                name: "c2",
                isFlexible: true
            }
        ]
    }
}
