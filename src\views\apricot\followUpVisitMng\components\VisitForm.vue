<template>

    <el-form ref="formRef"
        label-position="right"
        :model="followRecordForm">
        <FormList storageKey="visiteForm"
            :iModuleId="iModuleId"
            :list="Configs.followRecordInput"
            v-model:formData="followRecordForm"
            :optionData="optionsLoc"
            :configBtn="configValue.isShowConfigBtn">
        </FormList>
    </el-form>

</template>
<script setup>
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import { getEventbus } from '@/utils'
import Configs from '../config/visiteForm'

import { getOptionName, transformDate } from '$supersetResource/js/tools'
import { getAllUsersData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
import CommonApi from '$supersetApi/projects/common'
import {
    addFollowupRecord, editFollowupRecord, delFollowupRecord, getFollowList
} from '$supersetApi/projects/apricot/followupVisits/index.js'

//获取
const _store = useStore();
const $eventbus = getEventbus()
const userInfo = computed(() => {
    let userInfo = _store.getters["user/userSystemInfo"] || {};
    return userInfo
})

const iModuleId = inject('iModuleId', null)
const patientInfo = inject('patientInfo', {});
const configValue = inject('configValue', {})

watch(patientInfo, (val, oldVal) => {
    if (val && oldVal && val.sId === oldVal.sId) return;
    if (val.sId) {
        resetForm();
    }
})
const emits = defineEmits(['updateLoading', 'updateRowData', 'onChangeActiveTab'])

var optionsLoc = reactive({
    iIsAccordOptions: [
        {
            sName: '符合',
            sValue: 1
        },
        {
            sName: '不符合',
            sValue: 2
        }
    ],
    sFollowModeOptions: [],
    sDoctorIdOptions: [],
    sVisitStateOptions: []
})
optionsLoc.sFollowModeOptions = computed(() => {
    const temp = _store.getters['dict/map'].ApricotReportFollowMode || [];
    return temp
})

optionsLoc.sVisitStateOptions = computed(() => {
    const temp = _store.getters['dict/map'].ApricotReportFollowUp || [];
    return temp
})

var followRecordForm = ref({})
const formRef = ref(null)
// 保存数据
function saveVisiteData () {
    formRef.value.validate(valid => {
        if (!valid) {
            this.$message({
                message: '填写正确信息',
                type: 'warning'
            });
            emits('updateLoading')
            return false
        }
        let data = followRecordForm.value;
        data.sPatientId = patientInfo.value.sId;     //id赋值
        data.sIsAccordText = getOptionName(data['iIsAccord'], optionsLoc.iIsAccordOptions);
        data.sFollowModeText = getOptionName(data['sFollowMode'], optionsLoc.sFollowModeOptions);
        data.sVisitStateText = getOptionName(data['sVisitState'], optionsLoc.sVisitStateOptions);
        let target = optionsLoc.sDoctorIdOptions.find(item => item.sValue == followRecordForm.value.sDoctorId)
        data.sDoctorName = target?.userName
        data.sDoctorNo = target?.userNo;
        data.sDoctorId = target?.userId
        const currentFn = data.sId ? editFollowupRecord : addFollowupRecord;
        currentFn(data).then(res => {
            emits('updateLoading')
            if (res.success) {
                ElMessage({
                    message: res.msg,
                    type: 'success'
                });
                emits('updateRowData')
                //新增成功后刷新表格
                // this.getFollowList();
                return
            }
            ElMessage.error(res.msg);
        }).catch(err => {
            emits('updateLoading')
            console.log(err)
        })
    });
};

function resetForm () {
    let formName = 'visiteForm';
    let oStorageData = _store.getters['user/personalOnlineStorage']?.[iModuleId]?.[formName];
    let formSetList = oStorageData?.list || [];
    let params = {
        dPlanTime: new Date(),
        dFollowDate: new Date(),
        sFollowMode: '2',
        sVisitState: '1',
        iIsAccord: 1
    }
    if(formSetList.length) {
        formSetList.map(item => {
            if(Object.keys(params).includes(item.prop) && !item.isShow) {
                delete params[item.prop]
            }
        })
    }
    params.sDoctorId = _store.getters['user/userSystemInfo'].userId;
    followRecordForm.value = params
    nextTick(() => {
        formRef.value.clearValidate();
    })
};
function resetFormNull () {
    followRecordForm.value = {}
}
defineExpose({ saveVisiteData: saveVisiteData, resetFormNull: resetFormNull })
onMounted(async () => {
    optionsLoc.sDoctorIdOptions = await getAllUsersData()
    // nextTick(() => {
    //     resetForm()
    // })
    // 兄弟组件 信息修改
    $eventbus.on('visitedInfoEdit', res => {
        emits('onChangeActiveTab');
        resetForm()
        nextTick(() => {
            followRecordForm.value = res;
        })
    });
    $eventbus.on('visitedFormInit', res => {
        res.sId === followRecordForm.value.sId && resetForm()
    });

})
onBeforeUnmount(() => {
    $eventbus.off('visitedInfoEdit');
    $eventbus.off('visitedFormInit');
})

</script>