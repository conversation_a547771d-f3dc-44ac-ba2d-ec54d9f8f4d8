<template>
  <LayoutTable>
    <template v-slot:header>
      <SearchList v-model="searchParams.condition" v-model:list="searchList" :loading="loadingSearch"
        storageKey="AuthManageRolesIndex"  @changeSearch="onClickSearch" @reset="onClickReset">
      </SearchList>
    </template>
    <template #action>

       <div class="roles-flex-x">
            <div>
                <el-button  @click="onClickAdd" type="primary">
                    <template #icon>
                        <Icon name="el-icon-plus" color="white">
                        </Icon>
                    </template>
                    新增</el-button>
            </div>
            <div>
                <el-button @click="onClickReset">
                    <template #icon>
                        <Icon name="el-icon-refresh-left" >
                        </Icon>
                    </template>
                    重置</el-button>
                <el-button type="primary" @click="onClickSearch" :loading="loadingSearch">
                    <template #icon>
                        <Icon name="el-icon-search" color="white">
                        </Icon>
                    </template>
                    查询</el-button>
            </div>
        </div>
    </template>

    <template v-slot:content>
      <el-table :data="tableData" style="width: 100%" height="100%" v-loading="loadingSearch">
        <xxTableColumn v-model="tableList" storageKey="rolesSet" :isLang="false">

          <template #action="scope">
            <el-button test-auth="'auth:role:assign'" type="primary" link size="small" plain @click="getRoleRight(scope.row)">
              分配权限
            </el-button>
            <el-button  type="primary" link size="small" plain @click.prevent="onClickEdit(scope.row, scope.$index)">
              编辑
              <template #icon>
                <Icon name="el-icon-edit" class="">
                </Icon>
              </template>
            </el-button>
            <el-popconfirm confirm-button-text="是" cancel-button-text="否" icon-color="#FF0000" title="确定删除?"
              @confirm="onClickDel(scope.row)">
              <template #reference>
                <el-button  link size="small" plain>
                  删除
                  <template #icon>
                    <Icon name="el-icon-delete"  class="">
                    </Icon>
                  </template>
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </xxTableColumn>
      </el-table>

      <el-dialog v-model="dialogVisible" class="dialog-rolesSet" title="角色操作" width="520px" draggable>
        <el-form ref="editFormRef" :model="dataForm" :rules="dataRules" label-width="110px">
          <el-row style="margin-right: 40px;">
            <el-col :span="24">
              <el-form-item label="角色名称" prop="roleName">
                <el-input v-model="dataForm.roleName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="onClickEditSave" type="primary" :loading="saveLoading">
              <template #icon>
                <Icon name="el-icon-folder-add" color="white">
                </Icon>
              </template>
              保 存
            </el-button>
            <el-button @click="dialogVisible = false">
              <template #icon>
                <Icon name="el-icon-close" color="">
                </Icon>
              </template>
              关 闭
            </el-button>
          </span>
        </template>
      </el-dialog>


    </template>
  </LayoutTable>
  <LayerSlide v-model="roleRight.visible" @close="roleRight.visible = false" :title="roleRight.title">
    <div class="layer-container">
      <div class="layer-body" v-loading="roleRight.loadingGet">
        <el-scrollbar height="100%">
          <TreeRight :list="roleRightList"></TreeRight>
        </el-scrollbar>
      </div>
      <div class="layer-footer">
        <el-button type="primary" @click="onClickSaveRight" :loading="roleRight.loadingSave">
          <template #icon>
            <Icon name="el-icon-folder-add" color="white">
            </Icon>
          </template>
          保 存
        </el-button>
      </div>
    </div>
  </LayerSlide>
</template>
<script>
export default {
  name: 'AuthManageRolesIndex'
}
</script>
<script setup>
import rolesApi from "@/api/auth/roles"
import authApi from "@/api/auth/auth"

import TreeRight from './TreeRight.vue'
import { ElMessage } from 'element-plus'



const tableData = ref([])
const loadingSearch = ref(false)
const tableList = ref([
  { prop: "roleName", label: "角色名称", align: 'left', width: 140, align: "center" },
  { prop: "creator", label: "创建人", align: 'center', width: 120 },
  {
    prop: "createDate", label: "创建日期", width: 140, sortable: true,
    formatter: (row) => moment(row.createDate).format("YYYY-MM-DD HH:mm")
  },
  { prop: "modifieder", label: "修改人", align: 'center', width: 120 },
  {
    prop: "modifiedDate", label: "修改日期", width: 140, align: 'left', sortable: true,
    formatter: (row) => moment(row.modifiedDate).format("YYYY-MM-DD HH:mm")
  },
  { prop: 'action', label: 'action', width: null, fixed: 'right' },
])
const searchParams = reactive({
  condition: {
    roleName: ''
  }
})

const searchList = ref([
  { label: '角色名称', prop: 'roleName', componentType: 'el-input', width: '30%' },
])

const dataForm = ref({
  "roleCode": "",
  "roleName": ""
})
const dataRules = {
  roleName: [{ required: true, message: "必须", trigger: "blur" }],
}
const editFormRef = ref(null)
const dialogVisible = ref(false)
const saveLoading = ref(false)

onMounted(() => {
  onClickSearch()
})

// 点击搜索
const onClickSearch = () => {
  loadingSearch.value = true
  rolesApi.pageList(searchParams).then(({ data }) => {
    tableData.value = data.filter(i => String(i.roleName).indexOf(searchParams.condition.roleName.trim()) > -1)
    loadingSearch.value = false
  }).catch(() => {
    loadingSearch.value = false
  })
}
function onClickAdd() {
  dataForm.value = {}
  dialogVisible.value = true
}
// 编辑
function onClickEdit(row) {
  dataForm.value = Object.assign({}, row)
  dialogVisible.value = true
}
// 删除
function onClickDel(row) {
  rolesApi.del({ roleCode: row.roleCode }).then(res => {
    ElMessage.success(res.msg)
    onClickSearch()
  })
}
// 保存
function onClickEditSave() {
  editFormRef.value.validate((valid) => {
    if (valid) {
      saveLoading.value = true
      const params = Object.assign({}, dataForm.value)

      const submitApi = params.roleCode ? rolesApi.update : rolesApi.add

      // 编辑
      submitApi(params).then(res => {
        onClickSearch()
        ElMessage.success(res.msg)
        dialogVisible.value = false
      }).finally(() => {
        saveLoading.value = false
      })
    }
  })
}

const roleRight = reactive({
  title: '分配权限',
  visible: false,
  roleCode: '',
  loadingSave: false,
  loadingGet: false
})
const roleRightList = ref([])

// 点击获取权限
function getRoleRight({ roleCode, roleName }) {
  roleRight.visible = true
  roleRight.loadingGet = true
  authApi.queryRoleRightTree({ roleCode }).then(({ data }) => {

    roleRight.roleCode = roleCode
    roleRight.title = `分配权限 (${roleName})`
    roleRightList.value = data.menus

  }).finally(() => {
    roleRight.loadingGet = false
  })
}

// 分配权限
async function onClickSaveRight() {
  roleRight.loadingSave = true
  let rightCodes = ''

  const list = roleRightList.value
  list.forEach(item => {
    if (item.modules) {
      item.modules.forEach(moduleList => {
        if (moduleList.buttons) {
          moduleList.buttons.forEach(button => {
            if (button.assign) {
              rightCodes += button.rightCode + ','
            }
          })
        }
      })
    }
  })
  // 去掉最后一个 ,
  if (rightCodes.length) {
    rightCodes = rightCodes.substring(0, rightCodes.length - 1)
  }

  const res = await rolesApi.assignRight({ roleCode: roleRight.roleCode, rightCodes })

  roleRight.loadingSave = false

  if (res.success) {
    ElMessage.success(res.msg)
    return
  } else {
    ElMessage.error(res.msg)
  }
}
function onClickReset() {
      for (const key in searchParams.condition) {
        if (searchParams.condition.hasOwnProperty(key)) {
          searchParams.condition[key] = '' 
        }
      }
      onClickSearch()
    }
</script>
<style scoped lang="scss">
.layer-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  .layer-body {
    flex: 1;
    overflow: hidden;
  }

  .layer-footer {
    height: 50px;
    border-top: 1px solid #eee;
    padding-top: 10px;
    float: right;
    display: flex;
    justify-content: center;
  }
  
}
.roles-flex-x {
        display: flex;
        justify-content: space-between;
    }
</style>
