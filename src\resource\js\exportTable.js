// 引入导出Excel表格依赖
import FileSaver from 'file-saver';
import XLSX from 'xlsx';
// excelName:导出excel的名字,  elementName:被导出的元素名
export function exportExcelFn(excelName, elementName) {
    /* 从表生成工作簿对象 */
    var xlsxParam = {
        raw: true
    }; //转换成excel时，使用原始的格式
    var wb = XLSX.utils.table_to_book(document.querySelector(`${elementName}`), xlsxParam);
    /* 获取二进制字符串作为输出 */
    var wbOut = XLSX.write(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array',
    });
    try {
        FileSaver.saveAs(
            //Blob 对象表示一个不可变、原始数据的类文件对象。
            //Blob 表示的不一定是JavaScript原生格式的数据。
            //File 接口基于Blob，继承了 blob 的功能并将其扩展使其支持用户系统上的文件。
            //返回一个新创建的 Blob 对象，其内容由参数中给定的数组串联组成。
            new Blob([wbOut], {
                type: 'application/octet-stream;charset=utf-8'
            }),
            //设置导出文件名称
            `${excelName}.xlsx`
        );
    } catch (e) {
        if (typeof console !== 'undefined') console.log(e, wbOut);
    }
    return wbOut;
}