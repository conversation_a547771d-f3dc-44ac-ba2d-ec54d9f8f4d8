export default {
    MngIndexPenalConfig: {
        leftLayoutWidth: '50%',
        tabMachineInfo: true,
        tabMachineRecord: true,
        tabScannerData: true,
        tabPatientImage: true,
        tabInjectRecord: true,
        // rightTabPanels: ['MachineInfo', 'MachineRecord', 'ScannerData', 'PatientImage', 'InjectRecord'],
        isShowAltimeterPanel: false,
        autoPrintType: 0,
        autoPrintClassifys: [],
        isShowConfigBtn: true,
        isSearchFormCurrentWStation: false,
        isShowApplyPanel: true,
        isSaveAndAllowLeave: false,
        patientInfoBgColor: 'rgba(243, 250, 232, 1)',
        tabBgColor: '#fff',
        applyBgColor: '#fff',
        fPickingRate: 1.5
    },
    // 搜索条件
    searchStyle: [{
            sProp: 'dAppointmentTimeSt',
            sLabel: '预约开始',
            sInputType: 'date-picker',
            iCustom: 1,
            iLayourValue: 4
        },
        {
            sProp: 'dAppointmentTimeEd',
            sLabel: '预约结束',
            sInputType: 'date-picker',
            iCustom: 1,
            iLayourValue: 4,
        },
        {
            sProp: 'sDistrictId',
            sLabel: '院区',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sName',
            sLabel: '姓名',
            sInputType: 'text',
            iLayourValue: 4
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sInputType: 'text',
            iLayourValue: 4
        }, 
        {
            sProp: 'sMachineryRoomId',
            sLabel: '机房',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sProjectId',
            sLabel: '项目',
            iLayourValue: 4,
            iCustom: 1,
        },
        { 
            prop: 'sImageNo', 
            label: '影像号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sOutpatientNO', 
            label: '门诊号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sPhysicalExamNo', 
            label: '体检号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sVitisNo', 
            label: '就诊号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sCardNum', 
            label: '社保卡号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sHealthCardNO', 
            label: '健康卡号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        // {
        //     sProp: 'sMedicalRecordNO',
        //     sLabel: '病历号',
        //     sInputType: 'text',
        //     iLayourValue: 4
        // },
        // {
        //     sProp: 'sApplyNO',
        //     sLabel: '申请单号',
        //     sInputType: 'text',
        //     iLayourValue: 4
        // },
        // {
        //     sProp: 'sOrderNO',
        //     sLabel: '医嘱号',
        //     sInputType: 'text',
        //     iLayourValue: 4
        // },
        // {
        //     sProp: 'sInHospitalNO',
        //     sLabel: '住院号',
        //     sInputType: 'text',
        //     iLayourValue: 4
        // },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sOptionProp: 'iIsRegisterOptions',
            sInputType: 'option',
            iCustom: 1,
            iLayourValue: 4
        },
        {
            sProp: 'iIsInject',
            sLabel: '注射',
            sOptionProp: 'iIsInjectOptions',
            iCustom: 1,
            sInputType: 'option',
            iLayourValue: 4
        },
        // {
        //     sProp: 'dInjectTime',
        //     sLabel: '注射日期',
        //     sInputType: 'date-picker',
        //     iLayourValue: 4
        // },
        {
            sProp: 'iIsMachine',
            sLabel: '上机',
            sOptionProp: 'iIsMachineOptions',
            sInputType: 'option',
            iCustom: 1,
            iLayourValue: 4
        },
        {
            sProp: 'iIsHoldOn',
            sLabel: '挂起',
            sOptionProp: 'iIsHoldOnOptions',
            sInputType: 'option',
            iCustom: 1,
            iLayourValue: 4
        },
    ],
    // 患者信息
    textLableConfig: [
        {
            prop: 'sName',
            label: '姓名'
        },
        {
            prop: 'sSexText',
            label: '性别'
        },
        {
            prop: 'sAge',
            label: '年龄'
        },
        {
            prop: 'sNuclearNum',
            label: '核医学号'
        },
        {
            prop: 'sMedicalRecordNO',
            label: '病历号'
        },
        {
            prop: 'sProjectName',
            label: '检查项目',
            width: '75%'
        },
        {
            prop: 'fBloodSugar',
            label: '空腹血糖',
            sUnit: 'mmol/L'
        },
        {
            prop: 'fFullNeedle',
            label: '满针剂量'
        },
        {
            prop: 'fFactDose',
            label: '注射剂量'
        },
        {
            prop: 'fEmptyNeedle',
            label: '空针剂量'
        },
        {
            prop: 'dInjectionSta',
            label: '满针时间'
        },
        {
            prop: 'dInjectionTime',
            label: '注射时间'
        },
        {
            prop: 'dInjectionEnd',
            label: '空针时间'
        },
        {
            prop: 'sStainPosition',
            label: '渗漏'
        },
        {
            prop: 'sInspect',
            label: '特殊检查要求',
            width: '100%'
        },
        {
            prop: 'sNameSpell',
            label: '姓名拼音',
            isShow: false
        },
        {
            prop: 'fHeight',
            label: '身高（cm）',
            isShow: false
        },
        {
            prop: 'fWeight',
            label: '体重（kg）',
            isShow: false
        },
        {
            prop: 'sIdNum',
            label: '身份证号',
            isShow: false
        },
        {
            prop: 'dBirthday',
            label: '出生日期',
            isShow: false
        },
        {
            prop: 'sDistrictName',
            label: '院区',
            isShow: false
        },
        {
            prop: 'sRoomText',
            label: '设备类型',
            isShow: false
        },
        {
            prop: 'sMachineryRoomText',
            label: '机房',
            isShow: false
        },
        {
            prop: 'sMachineStationName',
            label: '工作站',
            isShow: false
        },
        {
            prop: 'dAppointmentTime',
            label: '预约日期',
            isShow: false
        },
        {
            prop: 'sOutpatientNO',
            label: '门诊号',
            isShow: false
        },
        {
            prop: 'sInHospitalNO',
            label: '住院号',
            isShow: false
        },
        {
            prop: 'sApplyNO',
            label: '申请单号',
            isShow: false
        },
        {
            prop: 'sOrderNO',
            label: '医嘱号',
            isShow: false
        },
        {
            prop: 'sVitisNo',
            label: '就诊号',
            isShow: false
        },
        {
            prop: 'sClinicalDiagnosis',
            label: '临床诊断',
            isShow: false
        },
        {
            prop: 'sMedicalHistory',
            label: '简要病史',
            isShow: false
        },
        {
            prop: 'sSourceText',
            label: '就诊类型',
            isShow: false
        },
        {
            prop: 'sEncounter',
            label: '就诊次数',
            isShow: false
        },
        {
            prop: 'sPositionText',
            label: '检查部位',
            isShow: false
        },
        {
            prop: 'sTestModeText',
            label: '检查方式',
            isShow: false
        },
        {
            prop: 'sNuclideText',
            label: '核素',
            isShow: false
        },
        {
            prop: 'sNuclideSupName',
            label: '核素全称',
            isShow: false
        },
        {
            prop: 'sTracerText',
            label: '示踪剂',
            isShow: false
        },
        {
            prop: 'sTracerSupName',
            label: '示踪剂全称',
            isShow: false
        },
        {
            prop: 'fRecipeDose',
            label: '处方剂量',
            isShow: false
        },
        {
            prop: 'iIsPregnant',
            label: '怀孕',
            isShow: false
        },
        {
            prop: 'sPhone',
            label: '联系电话',
            isShow: false
        },
        {
            prop: 'sAddress',
            label: '住址',
            isShow: false
        },
        {
            prop: 'sMaritalStatusName',
            label: '婚姻状况',
            isShow: false
        },
        {
            prop: 'sMedicalCaseNO',
            label: '病案号',
            isShow: false
        },
        {
            prop: 'sImageNo',
            label: '影像号',
            isShow: false
        },
        {
            prop: 'sInpatientAreaText',
            label: '病区名称',
            isShow: false
        },
        {
            prop: 'sInpatientWardText',
            label: '病房名称',
            isShow: false
        },
        {
            prop: 'sBedNum',
            label: '床号',
            isShow: false
        },
        {
            prop: 'sVisitCard',
            label: '就诊卡号',
            isShow: false
        },
        {
            prop: 'sCardNum',
            label: '社保卡号',
            isShow: false
        },
        {
            prop: 'sHealthCardNO',
            label: '健康卡号',
            isShow: false
        },
        {
            prop: 'sPresentHistory',
            label: '现病史',
            isShow: false
        },
        {
            prop: 'sPastHistory',
            label: '既往史疾病',
            isShow: false
        },
        {
            prop: 'sCheckIntent',
            label: '检查目的',
            isShow: false
        },
        {
            prop: 'sClinicalSymptoms',
            label: '临床症状',
            isShow: false
        },
        {
            prop: 'sChiefComplaint',
            label: '主诉',
            isShow: false
        },
        {
            prop: 'sInvoiceNum',
            label: '发票号',
            isShow: false
        },
        {
            prop: 'sFeeTypeText',
            label: '费用类型',
            isShow: false
        },
        {
            prop: 'fFees',
            label: '费用',
            isShow: false
        },
        {
            prop: 'sChargeStateText',
            label: '收费状态',
            isShow: false
        },
        {
            prop: 'dApplyDate',
            label: '申请时间',
            isShow: false
        },
        {
            prop: 'sApplyPersonName',
            label: '申请医生',
            isShow: false
        },
        {
            prop: 'sApplyDepartText',
            label: '申请科室',
            isShow: false
        },
        {
            prop: 'sChiefPhysicianName',
            label: '主治医生',
            isShow: false
        },
        {
            prop: 'sChiefPhysicianPhone',
            label: '医生电话',
            isShow: false
        },
        {
            prop: 'sPhysicalExamNo',
            label: '体检号',
            isShow: false
        }
    ],
    // 申请单信息
    applyTextLableConfig: [
        {
            sProp: 'sName',
            sLabel: '姓名',
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
        },
        {
            sProp: 'sTargetSiteName',
            sLabel: '申请单部位',
            width: '100%',
        },
        {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            width: '100%',
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            width: '100%',
        },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            width: '100%',
        },
        {
            sProp: 'sApplyMemo',
            sLabel: '申请单备注',
            width: '100%',
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            width: '20%',
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            width: '20%',
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            width: '20%',
        },
        {
            sProp: 'dApplyDate',
            sLabel: '申请日期',
            width: '20%',
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            width: '20%',
        },
    ],
    // 患者 table 表
    patientTable: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sMinWidth: '100px'
        },
        {
            sProp: 'sNameSpell',
            sLabel: '姓名拼音',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'fHeight',
            sLabel: '身高（cm）',
            sAlign: 'center',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'fWeight',
            sLabel: '体重（kg）',
            sAlign: 'center',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsInject',
            sLabel: '注射',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsMachine',
            sLabel: '上机',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsImaging',
            sLabel: '收图',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsReport',
            sLabel: '报告',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'sRoomText',
            sLabel: '设备类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sMinWidth: '110px'
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约日期',
            sMinWidth: '110px'
        },
        {
            sProp: 'dInjectTime',
            sLabel: '注射时间',
            sMinWidth: '110px',
            isSortable: true,
            sOrder: 'ascending'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sVitisNo',
            sLabel: '就诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sDelayBody',
            sLabel: '延迟部位',
            sMinWidth: '110px'
        },
        {
            sProp: 'dDelayTime',
            sLabel: '延迟时间',
            sMinWidth: '110px'
        },
        {
            sProp: 'dBirthday',
            sLabel: '出生日期',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sMachineStationName',
            sLabel: '工作站',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sDistrictName',
            sLabel: '院区',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineryRoomText',
            sLabel: '机房',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sDiagnosticOpinionText',
            sLabel: '诊断意见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sInspectSeeText',
            sLabel: '检查所见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sEncounter',
            sLabel: '就诊次数',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPositionText',
            sLabel: '检查部位',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTestModeText',
            sLabel: '检查方式',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideSupName',
            sLabel: '核素全称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTracerText',
            sLabel: '示踪剂',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'sTracerSupName',
            sLabel: '示踪剂全称',
            sMinWidth: '120px',
            iIsHide: true
        },
        {
            sProp: 'fRecipeDose',
            sLabel: '处方剂量',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'iIsPregnant',
            sLabel: '怀孕',
            sAlign: 'center',
            sMinWidth: '80px',
            iIsHide: true
        },
        {
            sProp: 'sPhone',
            sLabel: '联系电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sAddress',
            sLabel: '住址',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMaritalStatusName',
            sLabel: '婚姻状况',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sImageNo',
            sLabel: '影像号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientAreaText',
            sLabel: '病区名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientWardText',
            sLabel: '病房名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sBedNum',
            sLabel: '床号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sCardNum',
            sLabel: '社保卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fBloodSugar',
            sLabel: '检查空腹血糖',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPresentHistory',
            sLabel: '现病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sPastHistory',
            sLabel: '既往史疾病',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalSymptoms',
            sLabel: '临床症状',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefComplaint',
            sLabel: '主诉',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInvoiceNum',
            sLabel: '发票号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sFeeTypeText',
            sLabel: '费用类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fFees',
            sLabel: '费用',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChargeStateText',
            sLabel: '收费状态',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dApplyDate',
            sLabel: '申请时间',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianName',
            sLabel: '主治医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPhysicalExamNo',
            sLabel: '体检号',
            sMinWidth: '110px',
            iIsHide: true
        }
    ],
    // 上机表单
    machineInfoFormConfig: [{
        sProp: 'sImgTypeCode',
        sLabel: '显像类型',
        sInputType: 'option',
        sOptionProp: 'ApricotReportImagingType',
        iLayourValue: 8,
        disabledSetDefault: 1,
    }, {
        sProp: 'sTargetSiteCode',
        sLabel: '采集范围',
        sInputType: 'option',
        sOptionProp: 'ApricotReportTargetSite',
        iLayourValue: 8,
        iCustom: 1
    }, {
        sProp: 'sStain',
        sLabel: '污染情况',
        sInputType: 'option',
        sOptionProp: 'ApricotReportStain',
        iLayourValue: 8,
        iCustom: 1
    }, {
        sProp: 'sMachineryRoomId',
        sLabel: '机房',
        sInputType: 'option',
        sOptionProp: 'sMachineryRoomOptions',
        iLayourValue: 8,
        disabledSetDefault: 1,
    }, {
        sProp: 'dOperateEnd',
        sLabel: '采集时间',
        iRequired: 1,
        sInputType: 'dateTime-picker',
        iLayourValue: 8, 
        iCustom: 1,
        defaultValue: '1'
    }, {
        sProp: 'sDrinkTypeCode',
        sLabel: '饮水类型',
        sInputType: 'option',
        sOptionProp: 'ApricotReportDrinkType',
        iLayourValue: 8
    }, {
        sProp: 'iIntake', 
        sLabel: '饮水量',
        sInputType: 'number',
        iLayourValue: 8,
        iCustom: 1
    }, {
        sProp: 'fPickingRate',
        sLabel: '采集速度',
        sInputType: 'number',
        iLayourValue: 8,
        iCustom: 1,
        defaultValue: '1.5'
    }, {
        sProp: 'iNumber',
        sLabel: '采集数量',
        sInputType: 'number',
        iLayourValue: 8,
        iCustom: 1
    }, {
        sProp: 'iCountingRate',
        sLabel: '计数率',
        sInputType: 'number',
        iLayourValue: 8
    }, {
        sProp: 'sOperatorId',
        sLabel: '操作人',
        sInputType: 'option',
        sOptionProp: 'DoctorOptions',
        iLayourValue: 8,
        disabledSetDefault: 1,
    }, {
        sProp: 'sMemo',
        sLabel: '备注',
        sHeight: '60px',
        sInputType: 'textarea',
        iLayourValue: 24
    }],
    //采集数据表格
    collectionTable: [{
        sProp: 'sImgTypeName',
        sLabel: '显像类型',
        sMinWidth: '100px',
    }, {
        sProp: 'sTargetSiteName',
        sLabel: '采集范围',
        sMinWidth: '100px',
    }, {
        sProp: 'sStain',
        sLabel: '污染情况',
        sMinWidth: '100px',
    }, {
        sProp: 'sMachineryRoomText',
        sLabel: '机房',
        sMinWidth: '100px',
    }, {
        sProp: 'dOperateEnd',
        sLabel: '采集时间',
        sMinWidth: '180px',
    }, {
        sProp: 'iIntake',
        sLabel: '饮水量',
        sMinWidth: '100px',
    }, {
        sProp: 'sDrinkTypeText',
        sLabel: '饮水类型',
        sMinWidth: '100px',
    }, {
        sProp: 'fPickingRate',
        sLabel: '采集速度',
        sMinWidth: '100px',
    }, {
        sProp: 'iNumber',
        sLabel: '采集数量',
        sMinWidth: '100px',
    }, {
        sProp: 'iCountingRate',
        sLabel: '计数率',
        sMinWidth: '100px',
    }, {
        sProp: 'sOperator',
        sLabel: '操作人',
        sMinWidth: '100px',
    }, {
        sProp: 'sMemo',
        sLabel: '备注',
        sMinWidth: '180px',
    }, {
        sProp: 'Actions',
        sLabel: '操作',
        sAlign: 'center',
        sFixed: 'right',
        sMinWidth: '170px',
    }],


    injectTextList: [
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            width: '33.3%',
        },
        {
            sProp: 'sTracerText',
            sLabel: '示踪剂',
            width: '33.3%',
        },
        {
            sProp: 'fRecipeDose',
            sLabel: '处方剂量',
            width: '33.3%',
        },
        {
            sProp: 'sStainPosition',
            sLabel: '渗漏',
            width: '33.3%',
        },
        {
            sProp: 'sInjectionPosition',
            sLabel: '注射部位',
            width: '33.3%',
        },
        {
            sProp: 'dInjectionTime',
            sLabel: '注射时间',
            width: '33.3%',
        },
        {
            sProp: 'fFactDose',
            sLabel: '注射剂量',
            width: '33.3%',
        },
        {
            sProp: 'dInjectDate',
            sLabel: '注射日期',
            width: '33.3%',
        },
        {
            sProp: 'fBloodSugar',
            sLabel: '空腹血糖',
            width: '33.3%',
        },
    ],

    DA1: {
        type: 't-y',
        localStorageKey: '201911072238',
        panelConfig: [{
                size: 250,
                minSize: 180,
                maxSize: 500,
                name: "c1",
                isFlexible: false
            },
            {
                size: 0,
                minSize: 45,
                name: "c2",
                isFlexible: true
            }
        ]
    },
    DA0: {
        type: 't-x',
        localStorageKey: '202306211515',
        panelConfig: [{
            size: 0,
            minSize: 50,
            name: 'c1',
            isFlexible: true
        },
        {
            size: window.innerWidth / 2,
            minSize: 588,
            maxSize: window.innerWidth / 4 * 3,
            name: 'c2',
            isFlexible: false

        }]
    },
    DA2: {
        type: 't-y',
        localStorageKey: '202307071623',
        panelConfig: [{
            size: 0,
            minSize: 380,
            name: 'c1',
            isFlexible: true
        },
        {
            size: window.innerHeight / 5 * 2,
            minSize: 0,
            name: 'c2',
            isFlexible: false
        }]
    },
    DA3: {
        type: 't-y',
        localStorageKey: '202307071710',
        panelConfig: [{
            size: '100%',
            minSize: 380,
            name: 'c1',
            isFlexible: true
        }]
    },
}
