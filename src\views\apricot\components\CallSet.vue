<template>
    <el-dialog
        title="呼叫设置"
        :modelValue="dialogVisible"
        :close-on-click-modal="false"
        append-to-body
        class="t-default my-dialog dialog-height"
        width="65%"
        @close="handleCloseDialog">
            <template #header>
                <span class="titles">呼叫设置</span><span>【{{workStation.stationName}}】</span>
            </template>
            <el-tabs v-model="tabName">
                <!-- 语音设置 -->
                <el-tab-pane label="语音设置" name="callSet">
                    <div v-if="tabName == 'callSet' && dialogVisible" class="u-table">
                        <VoiceSet :seletedNode="workStation" :stationName="false" ></VoiceSet>
                    </div>
                </el-tab-pane>
                <!-- 按钮设置 -->
                <el-tab-pane label="按钮设置" name="btnSet">
                    <div v-if="tabName == 'btnSet'" class="u-table btn-call-table">
                        <CallBtnSet :seletedNode="workStation" :iModuleId='moduleId'  :stationName="false" @isEditBtn="handleIsEditBtn"></CallBtnSet>
                    </div>
                </el-tab-pane>
                <!-- 推送屏设置 -->
                <el-tab-pane label="推送屏设置" name="toScreenSet">
                    <div v-if="tabName == 'toScreenSet'" class="u-table">
                        <ToScreenSet :seletedNode="workStation" :stationName="false"></ToScreenSet>
                    </div>
                </el-tab-pane>
            </el-tabs>
            <template #footer>
                <el-button-icon-fa _icon="fa fa-close-1"
                    @click="handleCloseDialog">关闭</el-button-icon-fa>
            </template>
    </el-dialog>
</template>
<script>
// import Api from '$supersetApi/projects/apricot/common/callSet.js'

import VoiceSet from '$supersetViews/apricot/system/views/components/VoiceSet.vue'
import CallBtnSet from '$supersetViews/apricot/system/views/components/CallBtnSet.vue'
import ToScreenSet from '$supersetViews/apricot/system/views/components/ToScreenSet.vue'
import Sortable from 'sortablejs'

export default {
    name: 'CallSet',
    props: {
        dialogVisible: {
            type:Boolean,
            default: false,  
        },
        iModule: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['closeDialog'],
    components: {
        VoiceSet,
        CallBtnSet,
        ToScreenSet
    },

    data () {
        return {
            tabName: 'callSet',
            visible: false,
            editBtn: false,// 呼叫按钮是否编辑过
        }
    },
    computed:{
        workStation() {
            // console.log(this.$store.getters['user/workStation'])
            return this.$store.getters['user/workStation']
        },
        moduleId() {
            return this.iModule['iModuleId']
        },
    },
    methods: {
        // 关闭弹窗
        handleCloseDialog () {
            this.$emit('closeDialog', this.editBtn);
            this.tabName = 'callSet'
        },
        // t通知患者列表要重新获取呼叫按钮
        handleIsEditBtn(val) {
           this.editBtn = val 
        }
    }
}
</script>
<style lang="scss" scoped>
:global(.dialog-height) {
    min-width: 700px;
    height: 70%;
}
:global(.dialog-height .el-dialog__body) {
    height: calc(100% - 110px);
}
.u-table {
    height: 100%;
    
    :deep(.c-flex-auto) {
        padding: 0;
        .el-table__body-wrapper {
            height: calc(100% - 38px);
        }
    }
    

}
.btn-table {
    padding-top: 10px;
}
:deep(.el-table td) {
    padding: 2px 0;    
}

.el-tabs .el-tabs__content .el-tab-pane {
    padding: 5px 10px;
}
.titles {
        color: var(--theme-header-bg);
        margin-right: 10px;
    }
</style>
