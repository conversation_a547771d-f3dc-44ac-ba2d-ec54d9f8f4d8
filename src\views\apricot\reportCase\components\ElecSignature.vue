<template>
  <el-dialog :title="'签章管理'" v-model="mainDialogVisible" append-to-body align-center width="90vw"
    class="t-default my-dialog" :close-on-click-modal="false" @close="mainDialogVisible = false;">
    <div class="c-flex-context c-container">
      <div class="c-form">
        <div class="c-form-btn">
          <el-button-icon-fa type="primary" icon="el-icon-plus"
            @click="handleAdd">新增</el-button-icon-fa>
          <el-button-icon-fa plain type="primary" :loading="loading" icon="el-icon-refresh"
            @click="mxDoRefresh()">刷新</el-button-icon-fa>
        </div>

        <div class="c-form-search">
          <div class="flex">
            <span class="label">工号：</span>
            <el-input v-model="condition.sUserCode" placeholder="工号" clearable=""
              @keyup.enter.native="mxDoSearch()"></el-input>
          </div>
          <div class="flex">
            <span class="label">医生姓名：</span>
            <el-input v-model="condition.sUserName" placeholder="医生姓名" clearable=""
              @keyup.enter.native="mxDoSearch()"></el-input>
          </div>
          <div style="width: auto;">
            <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch"
              :loading="loading"></el-button-icon-fa>
          </div>
        </div>
        <el-dialog :title="dialogTitle" v-model="dialogVisible" append-to-body class="t-default" width="700"
          :close-on-click-modal="false" @close="closeDialog">
          <div class="flex">
            <el-form ref="refEditLayer" :model="editLayer.form" :rules="rules" label-width="120px">
              <el-col :span="24">
                <el-form-item prop="sUserCode" label="医生工号：">
                  <el-input v-model="editLayer.form.sUserCode" @blur="onblursNoInput" placeholder="医生工号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="医生姓名：">
                  <el-input v-model="editLayer.form.sUserName" disabled placeholder="医生姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item prop="sFilePath" label="上传文件：">
                  <el-input v-model="editLayer.form.sFilePath" disabled placeholder="上传文件">
                    <template #append>
                        <el-upload class="upload-demo" ref="upload" action="#" :http-request="handleUploadFile"
                            :show-file-list="false">
                            <el-button-icon-fa class="text-color" type="primary" icon="el-icon-upload">上传</el-button-icon-fa>
                        </el-upload>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="UKey序列码：">
                  <el-input v-model="editLayer.form.sUkeySerial" disabled placeholder="UKey序列码">
                    <template #append>
                        <el-button-icon-fa class="text-color" type="primary" icon="el-icon-search" @click="getUKeyData">获取</el-button-icon-fa>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="UKey姓名：">
                  <el-input v-model="editLayer.form.sUkeyName" disabled placeholder="UKey姓名"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="图片宽度：">
                  <el-input v-model="editLayer.form.sWidth" placeholder="图片宽度"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="图片高度：">
                  <el-input v-model="editLayer.form.sHeight" placeholder="图片高度"></el-input>
                </el-form-item>
              </el-col>
            </el-form>
          </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary"
                @click="mxDoSaveData('refEditLayer')">保存</el-button-icon-fa>
              <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>

            </div>
          </template>
        </el-dialog>
      </div>
      <div class="c-flex-auto">
        <div class="c-content" v-loading="loading">
          <el-table :data="tableData" id="itemTable" ref="mainTable" size="small" @row-dblclick="handleRowClick" border
            stripe height="100%" style="width: 100%">
            <el-table-column v-for="item in configTable.filter(_i => !_i.iIsHide)" show-overflow-tooltip :key="item.index"
              :prop="item.sProp" :label="item.sLabel" :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth"
              :min-width="item.sMinWidth" :sortable="!!item.iSort">
              <template v-slot="{row, $index}">
                <template v-if="item.sProp === 'action'">
                  <el-button-icon-fa icon="el-icon-edit" 
                    size="small" 
                    link 
                    type="primary" 
                    @click.stop="handleEdit(row)">编辑</el-button-icon-fa>
                  <el-divider direction="vertical"></el-divider>
                  <el-button-icon-fa icon="el-icon-delete" 
                    link 
                    size="small" 
                    @click="handleDelClick(row)">删除</el-button-icon-fa>
                </template>
                <template v-else-if="item.sProp.slice(0, 1) === 'd'">
                  {{ mxToDate(row[`${item.sProp}`]) }}
                </template>
                <template v-else-if="item.sProp === 'sFilePath'">
                  <el-image fit="contain" style="min-width: 30px; min-height: 30px"
                    :src="`${apricotUrl}/elec/signature/img/${row['sUserCode']}.${row['sFileType']}?&time=${random}`">
                    <template #error>
                      <div class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </template>
                  </el-image>
                </template>
                <template v-else>
                  {{ row[`${item.sProp}`] }}
                </template>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="c-pagination scope-pagination-around">
          <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
            :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next" :total="page.total">
          </el-pagination>
        </div>
      </div>
    </div>
    <template #footer>
        <el-button-icon-fa type="default" icon="fa fa-close-1" @click="mainDialogVisible = false;">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/elecSignature.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import { deepClone } from '$supersetUtils/function'
import { getAccessToken } from '@/utils/accessToken';
import { tokenName } from '@/utils/base_config';
export default {
  name: 'ElecSignature',
  mixins: [mixinTable],
  components: {},
  props: {
    modelValue: {
      default: false
    }
  },
  data() {
    return {
      isMixinDynamicGetTableHead: true, 
      tokenName: tokenName,
      dialogVisible: false,
      dialogTitle: '',
      configTable: [{
        sProp: 'sUserName',
        sLabel: ' 医生姓名',
        sMinWidth: '100px',

      },
      {
        sProp: 'sUserCode',
        sLabel: '工号 ',
        sMinWidth: '100px',

      },
      {
        sProp: 'sUkeySerial',
        sLabel: 'UKey序列码',
        sMinWidth: '100px',

      },
      {
        sProp: 'dCreateDate',
        sLabel: '上传时间',
        sAlign: 'center',
        sMinWidth: '120px',

      },
      {
        sProp: 'sFilePath',
        sLabel: '电子签章',
        sAlign: 'center',
        sMinWidth: '100px',

      },
      {
        sProp: 'action',
        sLabel: '操作',
        sAlign: 'center',
        sWidth: '180px',

      }],
      rules: {
        sUserCode: [{ required: true, message: '必填' }],
        sFilePath: [{ required: true, message: '请上传签章图片' }],
      },
      apricotUrl: window.configs.urls.apricot,
      random: Math.random()
    }
  },
  computed: {
    mainDialogVisible: {
      get() {
        return this.modelValue
      },
      set(val) {
        this.$emit('update:modelValue')
      }
    },

  },
  watch: {
    mainDialogVisible(val) {
        if(!val) return 
        this.mxDoSearch()
    }
  },
  methods: {
    getAccessToken,
    // 新增
    handleAdd() {
      this.actionState = 1
      this.dialogVisible = true
      this.dialogTitle = '新增'
      this.mxOpenDialog(1, 'no-title')
    },
    closeDialog() {
      this.dialogVisible = false
    },
    handleEdit(row) {
      this.actionState = 2
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      this.editLayer.form = Object.assign({}, row)
      this.$nextTick(() => {
        this.$refs['refEditLayer'].clearValidate();
      })
    },
    handleSave() {
      this.editLayer.loading = true
      let params = Object.assign({}, this.editLayer.form)
      this.$refs['refEditLayer'].validate((valid) => {
        if (valid) {
          this.saveData(params)
          return
        }
        this.editLayer.loading = false
      })
    },
    onblursNoInput() {
      let userNo = this.editLayer.form.sUserCode;
      if (!userNo) {
        return
      }
      Api.getUserByNo({ userNo }).then(res => {
        if (res.success) {
          let data = res.data || [];
          this.editLayer.form['sUserName'] = data.userName;
          this.editLayer.form['sUserId'] = data.userId;
          return
        }
        this.$message.error(res.msg);
      });
    },
    handleUploadFile(file) {
      const fileObj = file.file;
      this.getBase64(fileObj).then(res => {
        this.editLayer.form['sFilePath'] = fileObj.name;
        let suffix = fileObj.name.split('.');
        this.editLayer.form['sFileType'] = suffix.length > 1 ? suffix[suffix.length - 1] : 'jpg';
        let data = res.split(';base64,')[1];
        this.editLayer.form['sPathTemp'] = data;
      });
    },
    // 然后自定义一个方法，用来把图片内容转为base64格式，
    // imgResult就是base64格式的内容了。
    // 转为base64字符串要调用h5特性中的FileReader这个api,
    // 但是这个api不能return，所以用promise封装一下。
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      })
    },
    // 获取UKey序列码
    getUKeyData() {
      this.$message.warning('暂时没有接口')
    },
    onClearForm() {
      this.editLayer.form = {};
      this.$nextTick(() => {
        this.$refs.refEditLayer.clearValidate();
      })
    },
    /**
     * 保存数据
     */
    saveData(data) {
      let params = deepClone(data);
      if (params.iVersion) {
        Api.editSignature(params).then(res => {
          this.editLayer.loading = false;
          if (res.success) {
            this.dialogVisible = false
            this.$message.success(res.msg);
            this.mxGetTableList();
            this.onClearForm()
            return
          }
          this.$message.error(res.msg);
        }).catch(err => {
          console.log(err)
          this.editLayer.loading = false;
        })
        return
      }
      Api.addSignature(params).then(res => {
        this.editLayer.loading = false;
        if (res.success) {
          this.dialogVisible = false
          this.$message.success(res.msg);
          this.mxGetTableList();
          this.onClearForm()
          return
        }
        this.$message.error(res.msg);
      }).catch(err => {
        console.log(err)
        this.editLayer.loading = false;
      })
    },
    // 删除
    handleDelClick(item) {
      this.$confirm(`确定要删除【${item.sUserName}】吗？`, '提示', {
        confirmButtonClass: 'i-device-primary',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: "warning"
      }).then(() => {
        Api.delSignature({ iVersion: item.iVersion, sUserId: item.sUserId }).then((res) => {
          if (res.success) {
            this.$message.success('删除成功！');
            this.mxGetTableList();
            return;
          }
          this.$message.error(res.msg);
        }).catch(err => {
          console.log(err);
        });
      })
    },
    handleRowClick(row, isCancel = false, id = 'sId') {
      this.onClickRow(row, isCancel, id);
      this.mxOpenDialog(4, '111')
    },
    // 获取表格数据
    getData(params) {
      this.random = Math.random();
      this.editLayer.form = {};
      Api.getData(params).then((res) => {
        if (res.success) {
          this.tableData = res.data.recordList || []
          this.page.total = res.data.countRow;
          this.loading = false;
          return
        }
        this.loading = false;
        this.tableData = [];
        this.$message.error(res.msg)
      }).catch(() => {
        this.loading = false;
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
  height: 80vh;
  display: flex;
  flex-direction: column;
  padding: 0;
    padding-top:10px;
  .c-form {
    display: flex;
    padding-bottom: 10px;
    justify-content: space-between;

    .c-form-search {
      min-width: 400px;
      display: flex;
      .flex {
        align-items: center;
        span.label {
            width: 100px;
            text-align: right;
        }
      }
      >div:not(:last-child) {
        margin-right: 10px;
      }
    }
  }

  .c-flex-auto {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;

    .c-search {
      display: flex;
      align-items: flex-end;
      flex-wrap: wrap;
      padding: 10px;
      margin-left: -10px;

      >button {
        margin-top: 13px;
      }
    }

    .c-content {
      flex: 1;
      height: 0px;
    }
  }

  // .m-labelInput {
  //     width: 100%;
  //     margin-left: 0;
  // }
  :deep(.el-image) {
    width: 50px;
    height: 50px;
    max-width: 100px;
    max-height: 100px;

    .image-slot {
      height: 100%;
      // border: 1px solid #fafafa;
      text-align: center;
      line-height: 4;
    }
  }
}
.text-color {
    color: var(--el-color-primary) !important;
    &:active {
        color: white !important;
    }
}
</style>
