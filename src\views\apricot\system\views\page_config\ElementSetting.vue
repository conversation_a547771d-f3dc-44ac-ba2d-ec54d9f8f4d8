<template>
    <!-- 表格或者表单页面配置 -->
    <div class="m-flexLaout-ty">
        <div class="u-border-bottom m-flexLaout-tx u-autoHeight"
            style="padding:10px 15px 0 0">
            <div class="g-flexChild">
                <el-form inline
                    :model="condition"
                    size="small"
                    class="c-form t-1">
                    <template v-for="(item, index) in searchElementConfig" :key="index">
                        <FormStyle
                            :configData="item"
                            :formData="condition"
                            :optionData="optionsLoc">
                            <template v-if="item.sProp == 'iSetClass'"
                                v-slot:custom>
                                <el-select clearable
                                    v-model="condition[item.sProp]"
                                    placeholder=""
                                    @change="mxDoSearch">
                                    <el-option v-for="item in optionsLoc.SetClassArray"
                                        :key="item.sValue"
                                        :value="item.sValue"
                                        :label="item.sName">
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-else-if="item.sProp == 'sDeviceId'"
                                v-slot:custom>
                                <el-select v-model="condition[item.sProp]"
                                    clearable
                                    placeholder="">
                                    <el-option v-for="item in deviceTypeOptions"
                                        :key="item.sId"
                                        :label="item.sDeviceTypeName"
                                        :value="item.sId">
                                    </el-option>
                                </el-select>
                                <!-- <el-cascader clearable
                                    placeholder=""
                                    :props="config202101221346"
                                    v-model="condition[item.sProp]"
                                    :options="cascader1Options"
                                    @change="mxDoSearch"></el-cascader> -->
                            </template>
                        </FormStyle>
                    </template>
                    <el-button-icon-fa class="f-elementEditTableSearchFormSubmit"
                        _icon="fa fa-search"
                        style="margin-left:25px;margin-top:2px"
                        type="primary"
                        size="small"
                        @click="mxDoSearch">查询</el-button-icon-fa>
                </el-form>
            </div>
            <div style="margin-top:10px;">
                <el-button-icon-fa _icon="fa fa-plus"
                    type="primary"
                    size="small"
                    @click="openElementDialog(1)">新增</el-button-icon-fa>
                <el-button-icon-fa _icon="fa fa-copy"
                    type="primary"
                    plain
                    size="small"
                    @click="openElementDialog(3)">复制</el-button-icon-fa>
                <el-button-icon-fa _icon="fa fa-pencil"
                    type="primary"
                    plain
                    size="small"
                    @click="openElementDialog(2)">修改</el-button-icon-fa>
                <el-button-icon-fa _icon="fa fa-trash"
                    type="danger"
                    plain
                    size="small"
                    @click="handleElementDeleteClick">删除</el-button-icon-fa>
            </div>
        </div>
        <div class="g-flexChild"
            v-loading="loading">
            <div class="m-flexLaout-ty">
                <!-- 表格数据 -->
                <!-- :key="tableType" -->
                <div class="g-flexChild">
                    <el-table ref="mainTable"
                        id="pageConfigTable"
                        :key="nodeData.sModelType?nodeData.sModelType:tableType"
                        :data="tableData"
                        :row-class-name="mxRowClassName"
                        size="small"
                        stripe
                        height="100%"
                        @row-click="onClickRow"
                        v-if="reRender"
                        >
                        <template v-for="(item,index) in tableColumnConfig" :key="index">
                            <el-table-column 
                                v-if="item.className.includes(nodeData.sModelType?nodeData.sModelType:tableType)&&!item.iIsHide"
                                :prop="item.sProp"
                                :label="item.sLabel"
                                :fixed="item.sFixed"
                                :align="item.sAlign"
                                :width="item.sWidth"
                                :min-width="item.sMinWidth"
                                :sortable="!!item.iSort"
                                show-overflow-tooltip>
                                <template v-slot="scope">
                                    <template v-if="item.sProp === 'action'">
                                        <i class="el-icon-rank i-sort"
                                            style="cursor: pointer; font-size: 18px;"></i>
                                    </template>
                                    <template v-if="item.sProp === 'sLabel'">
                                        <span style="font-weight: bold;color:#333">{{scope.row[item.sProp]}}</span>
                                    </template>
                                    <template v-else-if="['iIsHide', 'iReadonly','iRequired'].includes(item.sProp)">
                                        <el-switch v-model="scope.row[item.sProp]"
                                            :active-value='1'
                                            :inactive-value='0'
                                            @click.stop.native="onChangeEnable($event, scope.row, scope.$index, item.sProp)"
                                             ></el-switch>
                                        <!-- {{scope.row[`${item.sProp}`]==1? '是':'否'}} -->
                                    </template>
                                    <template v-else-if="['iCustom'].includes(item.sProp)">
                                        {{scope.row[`${item.sProp}`]==1? '是':'否'}}
                                    </template>
                                    <template v-else-if="['iSort'].includes(item.sProp)">
                                        {{scope.row[`${item.sProp}`]==1? '是':'否'}}
                                    </template>
                                    <template v-else-if="item.sProp=='iSetClass'">
                                        {{SetClassText(scope.row[`${item.sProp}`])}}
                                    </template>
                                    <template v-else-if="item.sProp=='sFixed'">
                                        {{SetFixedText(scope.row[`${item.sProp}`])}}
                                    </template>
                                    <template v-else-if="item.sProp=='sAlign'">
                                        {{SetAlignText(scope.row[`${item.sProp}`])}}
                                    </template>
                                    <template v-else-if="['iFontWeight','iLabelFontWeight'].includes(item.sProp)">
                                        {{setFontWeight(scope.row[`${item.sProp}`])}}
                                    </template>
                                    <template v-else>
                                        {{scope.row[`${item.sProp}`]}}
                                    </template>
                                </template>
                                <!-- <template v-slot:header>
                                    <span>{{item.sLabel}}</span>
                                    <i v-if="item.sProp === 'action'"
                                        class="el-icon-rank i-sort"
                                        style="cursor: pointer;font-size: 14px;padding-left: 5px; font-size: 18px; position:relative; top: 2px"
                                        title="首次或无法排序时，点击初始化排序"
                                        @click="autoSort"></i>
                                </template> -->
                            </el-table-column>
                        </template>
                    </el-table>
                </div>
            </div>
        </div>
        <!-- "元素弹窗 -->
        <el-dialog append-to-body
            title="元素编辑"
            v-model="d_element_v"
            @close="handleElementCloseDialog"
            class="my-dialog"
            :close-on-click-modal="false">
            <div class="c-look-content c-inner-content scope-input-look">
                <el-form inline
                    class="scope--rules"
                    ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules">
                    <template v-for="(item, index) in inputElementConfig" :key="index">
                        <FormStyle v-if="item.sConfigType.includes(nodeData.sModelType)"
                            
                            :configData="item"
                            :formData="editLayer.form"
                            :optionData="optionsLoc">
                            <template v-if="item.sProp == 'sDeviceId'"
                                v-slot:custom>
                                <el-select v-model="editLayer.form[item.sProp]"
                                    clearable
                                    placeholder="">
                                    <el-option v-for="item in deviceTypeOptions"
                                        :key="item.sId"
                                        :label="item.sDeviceTypeName"
                                        :value="item.sId">
                                    </el-option>
                                </el-select>
                                <!-- <el-cascader :props="config202101221346"
                                    v-model="editLayer.form[item.sProp]"
                                    :options="cascader1Options"
                                    size="small"
                                    clearable></el-cascader> -->
                            </template>
                            <template v-else-if="item.sProp == 'sFontSize' || item.sProp =='sLabelFontSize'"
                                v-slot:custom>
                                <el-select v-model="editLayer.form[item.sProp]"
                                    placeholder="">
                                    <el-option v-for="item in fontSizes"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6;"
                                            :style="{fontSize:item.value}">{{item.tips}}</span>
                                    </el-option>
                                </el-select>
                            </template>
                            <template v-else-if="item.sProp == 'iLabelFontWeight' || item.sProp =='iFontWeight'"
                                v-slot:custom>
                                <el-select v-model="editLayer.form[item.sProp]"
                                    placeholder="">
                                    <el-option v-for="item in sFontWeight"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                        <span style="float: left">{{ item.label }}</span>
                                        <span style="float: right; color: #8492a6;"
                                            :style="{fontWeight:item.value}">{{item.tips}}</span>
                                    </el-option>
                                </el-select>
                            </template>
                        </FormStyle>
                    </template>
                </el-form>
            </div>
            <template #footer><div 
                class="my-dialog-footer">
                <div class="g-page-footer">
                    <el-button-icon-fa _icon="fa fa-iconclose"
                        @click="handleElementCloseDialog"
                        size="small">关 闭</el-button-icon-fa>
                    <el-button-icon-fa type="primary"
                        _icon="fa fa-save"
                        size="small"
                        :loading="sendloading"
                        @click="handleElementSaveClick">保 存</el-button-icon-fa>
                </div>
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Sortable from 'sortablejs'
import FormStyle from '$supersetViews/components/FormStyle.vue'

import config from "../../config/index"
// 混入
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import { getItemTree } from '$supersetApi/projects/apricot/appointment/index.js'
import { getDeviceTypeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import {
    adjustSetFindByPage, adjustSetAdd, adjustSetEdit, adjustSetDel, adjustSetAutoSort,
    adjustSetSort, adjustSetHide, adjustSetReadonly, adjustSetRequired
} from "$supersetApi/projects/apricot/system/pageConfig"
export default {
    name: 'ElementSetting',
    mixins: [mixinTable],
    components: {
        FormStyle
    },
    props: {
        nodeData: {
            type: Object,
            default: () => ({})
        },
        refresh: {
            type: Number,
            default: Math.random()
        },
    },
    data () {
        return {
            d_element_v: false,
            searchElementConfig: config.searchElementConfig,
            tableColumnConfig: config.tableColumnConfig,
            tableData: [],
            inputElementConfig: config.inputElementConfig,
            optionsLoc: config.optionsLoc,
            rules: {},
            modleFormData: {},
            condition: {
                iSetClass: ''
            },
            tableType: 'form',
            editType: 1,
            reRender: true,
            cascader1Options: [],
            deviceTypeOptions: [],
            config202101221346: {
                value: 'sId',
                label: 'sName',
                children: 'childs'
            },
            sendloading: false,
            fontSizes: [
                {
                    label: '14px',
                    value: '14px',
                    tips: '系统默认'
                },
                {
                    label: '16px',
                    value: '16px',
                    tips: '字体大小'
                },
                {
                    label: '18px',
                    value: '18px',
                    tips: '字体大小'

                }
            ],
            sFontWeight: [
                {
                    label: '正常',
                    value: 400,
                    tips: '系统默认'
                },
                {
                    label: '加粗',
                    value: 600,
                    tips: '加粗'
                }
            ]
        }
    }, 
    computed: {
        tableSearchFormConfig () {
            let targetArr = this.inputElementConfig.filter(item => ['iSetClass', 'sXXXX'].includes(item.sProp));
            return targetArr || []
        },
    },
    watch: {
        refresh () {
            this.$nextTick(() => {
                this.rowDrop()
            })
            this.mxDoSearch();
        },
        nodeData (val) {
            if (val.sModelType) {
                this.tableType = val.sModelType
            }
        }
    },
    methods: {
      SetClassText (val) {
            let target = config.optionsLoc.SetClassArray.find(item => val == item.sValue);
            return target ? target.sName : null
        },
        SetFixedText (val) {
            let target = config.optionsLoc.FixedArray.find(item => val == item.sValue);
            return target ? target.sName : null
        },
        SetAlignText (val) {
            let target = config.optionsLoc.AlignArray.find(item => val == item.sValue);
            return target ? target.sName : null
        },
        setFontWeight (val) {
            let target = config.optionsLoc.sFontWeight.find(item => val == item.sValue);
            return target ? target.sName : null
        },
        doAction () {
            return {
                iIsHide: (row) => {
                    let jsonData = {
                        sId: row.sId,
                        iIsHide: row.iIsHide
                    }
                    adjustSetHide(jsonData).then(res => {
                        if (!res.success) {
                            this.$message.error(res.msg);
                            row.iIsHide = Number(!row.iIsHide)
                            return
                        }
                        // this.tableData[index]['iIsHide'] = row.iIsHide;
                        this.$message.success(res.msg);
                    }).catch(err => {
                        console.log(err)
                    })
                },
                iReadonly: (row) => {
                    let jsonData = {
                        sId: row.sId,
                        iReadonly: row.iReadonly
                    }
                    adjustSetReadonly(jsonData).then(res => {
                        if (!res.success) {
                            this.$message.error(res.msg);
                            row.iReadonly = Number(!row.iReadonly)
                            return
                        }
                        // this.tableData[index]['iReadonly'] = row.iReadonly;
                        this.$message.success(res.msg);
                    }).catch(err => {
                        console.log(err)
                    })
                },
                iRequired: (row) => {
                    let jsonData = {
                        sId: row.sId,
                        iRequired: row.iRequired
                    }
                    adjustSetRequired(jsonData).then(res => {
                        if (!res.success) {
                            this.$message.error(res.msg);
                            row.iRequired = Number(!row.iRequired)
                            return
                        }
                        // this.tableData[index]['iRequired'] = row.iRequired;
                        this.$message.success(res.msg);
                    }).catch(err => {
                        console.log(err)
                    })
                },
            }
        },
        // 修改状态
        onChangeEnable (e, row, index, key) {
            let action = this.doAction();
            action[key](row, index);
        },
        // 关闭元素编辑弹窗
        handleElementCloseDialog () {
            this.$refs.refEditLayer && this.$refs.refEditLayer.clearValidate();
            this.d_element_v = false;
        },
        // 打开元素编辑弹窗
        openElementDialog (type) {
            this.editType = type;
            this.editLayer.form = {}
            if (type == 1) {
                //新增
                if (!Object.keys(this.nodeData).length || this.nodeData.iType != 3) {
                    this.$message.warning('请选择一条模块数据！');
                    return;
                }
                this.editLayer.form['iSetClass'] = this.nodeData.iSetClass;
                this.d_element_v = true;
                return
            }
            if (type == 2) {
                // 修改 
                if (!Object.keys(this.editLayer.selectedItem).length) {
                    this.$message.warning('请选择一条配置数据！');
                    return;
                }
                this.editLayer.form = Object.assign({}, this.editLayer.selectedItem);
                this.editLayer.form['sXXXX'] = this.editLayer.form.sProjectId || this.editLayer.form.sDeviceId;
                this.d_element_v = true;
                return
            }
            // 复制
            if (!Object.keys(this.editLayer.selectedItem).length) {
                this.$message.warning('请选择一条配置数据！');
                return;
            }
            this.editLayer.form = Object.assign({}, this.editLayer.selectedItem);
            this.editLayer.form['sXXXX'] = this.editLayer.form.sProjectId || this.editLayer.form.sDeviceId;
            delete this.editLayer.form.sId;
            this.d_element_v = true;
        },
        // 保存元素编辑数据
        handleElementSaveClick () {
            // TODO
            this.$refs.refEditLayer.validate(valid => {
                if (!valid) {
                    this.$message.warning('请正确填写必填项');
                    return;
                }
                let temp = Object.assign({}, this.editLayer.form);
                // if (temp.iSort && !temp.sSortField) {
                //     this.$message.warning('请填写排序字段（格式为：表名.字段名）！');
                //     return;
                // }

                temp.sModelId = this.nodeData.sId;
                temp.sSystemLabel = this.nodeData.sSystemLabel;
                temp.iSetClass = temp.iSetClass || this.nodeData.iSetClass;
                temp.sNodeLabel = this.nodeData.sNodeLabel;

                // let data = this.editLayer.form['sXXXX']

                // if (data && typeof (data) == 'object') {
                //     temp.sDeviceId = data[0] || ''
                //     temp.sProjectId = data[1] || ''
                // }

                if (this.editType == 1 || this.editType == 3) {
                    // this.tableData.push(temp);
                    this.sendloading = true;
                    adjustSetAdd(temp).then(res => {
                        this.sendloading = false;
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.handleElementCloseDialog();
                            this.mxGetTableList();
                            return
                        }
                        this.$message.error(res.msg)
                    }).catch(err => {
                        console.log(err)
                        this.sendloading = false;
                    })
                    return
                }
                this.sendloading = true;
                adjustSetEdit(temp).then(res => {
                    this.sendloading = false;
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.handleElementCloseDialog();
                        this.mxGetTableList();
                        return;
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    console.log(err)
                    this.sendloading = false;
                })

            });
        },
        // 删除页面元素配置数据
        handleElementDeleteClick () {
            if (!Object.keys(this.editLayer.selectedItem).length) {
                this.$message.warning('请选择一条配置数据！');
                return;
            }
            this.$confirm("确定要删除该条数据吗，是否继续？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                adjustSetDel({
                    iVersion: this.editLayer.selectedItem.iVersion,
                    sId: this.editLayer.selectedItem.sId
                }).then(res => {
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.mxDoSearch();
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err)
                })
            }).catch(() => { });
        },
        // 元素页面表格 自动排序
        autoSort () {
            if (!this.tableData.length) {
                return;
            }
            adjustSetAutoSort({
                sModelId: this.nodeData.sId
            }).then(res => {
                if (res.success) {
                    this.mxGetTableList();
                    this.$message.success(res.msg);
                    return;
                }
                this.$message.error(res.msg);
            })
        },
        // 元素页面表格 行拖拽 
        rowDrop () {
            const tbaleEle = document.getElementById("pageConfigTable");
            if (!tbaleEle) {
                return
            }
            const tbody = tbaleEle.querySelector('.el-table__body-wrapper tbody');
            const _this = this;
            let updateFnc = ({
                newIndex,
                oldIndex
            }) => {
                if (newIndex === oldIndex) return;
                adjustSetSort({
                    iIndexOld: _this.tableData[oldIndex].iIndex,
                    iIndexNew: _this.tableData[newIndex].iIndex,
                    sId: _this.tableData[oldIndex].sId,
                    sModelId: _this.tableData[oldIndex].sModelId,
                    iVersion: _this.tableData[oldIndex].iVersion,
                }).then(res => {
                    if (res.success) {
                        _this.tableData[oldIndex].iVersion += 1
                        _this.tableData[newIndex].iVersion += 1
                        const currRow = _this.tableData.splice(oldIndex, 1)[0]
                        _this.tableData.splice(newIndex, 0, currRow)
                        _this.reRender = false
                        _this.$nextTick(() => {
                            _this.reRender = true
                            if (_this.editLayer.selectedItem) _this.editLayer.selectedItem.iVersion += 1
                            _this.$nextTick(() => {
                                this.mxGetTableList();
                                new Sortable(document.getElementById("pageConfigTable").querySelector('.el-table__body-wrapper tbody'), {
                                    animation: 200,
                                    onEnd: updateFnc,
                                })
                            })
                        })
                    }
                })
            }
            Sortable.create(tbody, {
                handle: ".i-sort",
                animation: 200,
                onEnd: updateFnc,
            })
        },
        // 获取表格数据
        getData (data) {
            let nodeData = this.nodeData || {};
            if (!Object.keys(nodeData).length) {
                this.loading = false;
                this.tableData = [];
                return
            }
            let params = Object.assign({}, data);
            params.condition.sModelId = nodeData.sId;
            params.condition.iSetClass = this.condition.iSetClass;
            // let param = this.switchData(this.condition['sXXXX']);
            // if (param) {
            //     for (let key in param) {
            //         params.condition[key] = param[key]
            //     }
            // }
            // delete params.condition.sXXXX;

            params.page.pageSize = 500;
            adjustSetFindByPage(params).then(res => {
                if (res.success) {
                    this.loading = false;
                    this.tableData = res.data.recordList || [];
                    if (!this.tableData.length) {
                        return
                    }
                    // 赋选中状态
                    this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading = false;
                console.log(err)
            })
        },
        switchData (data) {
            let obj = {}
            if (data && typeof (data) == 'object') {
                obj = {
                    sDeviceId: data[0],
                    sProjectId: data[1]
                }
            }
            return obj
        },
        // 获取设备类型/项目 联级数据
        getCascader1Options () {
            getItemTree({}).then(res => {
                if (res.success) {
                    this.cascader1Options = res.data;
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err)
            });
        },
        // 获取设备下拉
        getDeviceOptions () {
            getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.deviceTypeOptions = res?.data || [];
                    return;
                }
                this.$message.error(res.msg);
            })
        },
    },
    created () {
        // this.getCascader1Options();
        this.getDeviceOptions();
    },
};
</script>
<style lang="scss" scoped>
:deep(.my-dialog) {
    width: 70%;
    max-width: 1100px;
}
.f-elementEditTableSearchFormSubmit {
    position: relative;
    top: 10px;
    right: 15px;
}
.c-form.t-1 {
    margin-left: 5px;
}
.el-form {
    overflow: hidden;
    padding-bottom: 5px;
    :deep(.el-form-item__error ) {
        bottom: -3px;
    }
    .el-select,
    .el-cascader {
        width: 100%;
    }
}
</style>
