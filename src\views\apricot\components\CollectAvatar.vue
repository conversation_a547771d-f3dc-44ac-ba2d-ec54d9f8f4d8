<template>
    <!-- 头像采集 -->
    <el-dialog title="头像采集"
        :close-on-click-modal="false"
        append-to-body
        :modelValue="dialogVisible"
        class="my-dialog t-default"
        width="550px"
        @close="handleCloseDialog">
        <div class="c-dialog-body">
            <div class="c-item t-2">
                <div class="i-item">
                    <div>
                        <p>
                            <span>姓名：</span>
                            <span>{{patientInfo.sName}}</span>
                        </p>
                        <p>
                            <span>性别：</span>
                            <span>{{patientInfo.sSexText}}</span>
                        </p>
                        <p>
                            <span>出生日期：</span>
                            <span v-text="transformDate(patientInfo.dBirthday)"></span>
                        </p>
                        <el-button-icon-fa 
                            plain
                            type="primary"
                            _icon="fa fa-camera"
                            @click="onClickMedia">拍 摄</el-button-icon-fa>
                    </div>
                    <div class="i-img">
                        <el-image class="my-image"
                            :src="photoDownUrl + '&uniqueId=' + (patientInfo.sInnerIndex ? patientInfo.sInnerIndex: patientInfo.sId) + '&random=' + random"
                            fit="cover">
                            <template #error><div  class="image-slot" style="margin-top: 60%;text-align: center;">
                                <!-- <i class="el-icon-picture-outline"></i> -->
                                暂无头像
                            </div></template>
                        </el-image>
                    </div>
                </div>
                <p style="margin: 10px 0;">
                    <span>身份证号：</span>
                    <span v-text="patientInfo.sIdNum"></span>
                </p>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa 
                _icon="fa fa-close-1"
                @click="handleCloseDialog">关 闭</el-button-icon-fa>
        </template>

        <el-dialog :close-on-click-modal="false"
            append-to-body
            title="摄像"
            v-model="innerVisible"
            @close="closeInnerDialog"
            width="400px"
            class="my-dialog">
            <div class="c-dialog-body t-1">
                <el-button-icon-fa icon="el-icon-success" @click="handleCompleteClick">拍摄结束</el-button-icon-fa>
            </div>
        </el-dialog>

    </el-dialog>

</template>

<script>
import { transformDate } from '$supersetResource/js/tools.js'
import { photoDownUrl, photoUpUrl } from '$supersetUtils/base_config'
export default {
    name: 'CollectAvatar',
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        },
        // refresh: null,
        index: null
    },
    emits: ['closeDialog', 'refreshImg'],

    data () {
        return {
            form: {
                sIdNum: null,
            },
            photoDownUrl: photoDownUrl + '?',
            photoUpUrl: photoUpUrl,
            random: Math.random(),
            innerVisible: false,
            rules: {
                sIdNum: [
                    {
                        validator: (rule, value, callback) => {
                            if (value == undefined || value == null || value == '') {
                                callback()
                            }
                            let reg = /^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/;
                            if (reg.test(value)) {
                                callback()
                            }
                            callback(new Error('请填写正确身份证信息'))
                        }
                    },
                ],
            }
        }
    },
    methods: {
        // 时间转换
        transformDate: transformDate,
        // 关闭弹窗
        handleCloseDialog () {
            this.$emit('closeDialog');
            this.form.sIdNum = null;
            // this.$emit('refreshImg', this.index);
        },
        // 关闭内置弹窗
        closeInnerDialog () {
            this.$emit('refreshImg');
        },
        onClickMedia () {
            let href = this.photoUpUrl + '?uniqueId=' + (this.patientInfo.sInnerIndex ? this.patientInfo.sInnerIndex : this.patientInfo.sId) + '&name=' + this.patientInfo.sName
            // let newWnd = window.open(u, '_blank')
            // newWnd.opener = null;
            // let a = document.createElement('a');
            // a.setAttribute('href', href);
            // a.setAttribute('target', '_blank');
            // a.setAttribute('rel', 'noopener noreferrer');
            // a.setAttribute('id', 'startTelMedicine');
            window.open(href, "_blank", "scrollbars=yes,resizable=1,modal=false,alwaysRaised=yes,width=850,height=750");
            // 防止反复添加
            if (document.getElementById('startTelMedicine')) {
                document.body.removeChild(document.getElementById('startTelMedicine'));
            }
            // document.body.appendChild(a);
            // a.click();
            this.innerVisible = true;
        },
        handleCompleteClick () {
            this.innerVisible = false;
            this.random = Math.random();
        }
    },
}
</script>

<style lang="scss" scoped>
.c-dialog-body {
    &.t-1 {
        padding: 30px;
        text-align: center;
    }
    .c-item.t-1 {
        padding: 20px;
    }
    .c-item.t-2 {
        margin: 10px;
        padding: 10px 30px;
        // border: 1px solid #d2d2d2;
        .i-item {
            display: flex;
            justify-content: space-between;
            .i-img {
                width: 140px;
                height: 202px;
                border: 1px solid #c4cfd5;
                .my-image {
                    width: 100%;
                    max-width: 100%;
                    max-height: 100%;
                }
            }
        }
    }
}
:deep(.el-input--mini .el-input__inner ) {
    color: inherit;
}
</style>
