import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'

// 分页查询病例
export function getAirReportPage(data) {
    return request({
        url: baseURL.apricot + '/airreport/find/patients',
        method: 'POST',
        data
    })
}

// 发送报告给平台
export function airReportSendHis(params) {
    return request({
        url: baseURL.apricot + '/airreport/send/his',
        method: 'POST',
        data: stringify(params)
    })
}