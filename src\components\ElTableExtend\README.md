# ElTableExtend 拓展表格组件

替代`<el-table>`组件并在其基础上拓展功能。其传参、方法、插槽、事件都与`<el-table>`组件一致。
拓展的功能包括可控制表格中列数据的显示、顺序等，并将配置数据暂存在localstorage中。

使用示例：
```
    <el-table-extend ... iModuleId="6" storageKey="ConsultCaseIndexTable" :configBtn="buttonRef">
      <el-table-column ...>
        <template v-slot="scope"> 
        ...
        </template>
      </el-table-column>
      ...
    </el-table-extend>
    <el-button type="primary" :ref="(el) => buttonRef = el">替换按钮</el-button>
```

属性: 
| 属性名 | 说明 | 格式 | 默认值 | 
|----|--------------|---|---| 
| storageKey  | 保存到localstorage时的唯一标识，不填则不保存   | string | 无  |  
| iModuleId  | 保存到服务器时的moduleId字段，与storageKey同时存在时启用数据上传   | string | 无  |  
| configBtn  | 配置按钮是否显示，或用于替换默认按钮的ref对象   | boolean,Ref | true  |   


`<el-table-column >`的附加属性：
| 属性名 | 说明 | 格式 | 默认值 | 
|----|--------------|---|---| 
| isSortable  | 是否显示排序按钮（默认值），当为true时且配置了sortable="custom"时，点击按钮触发事件（后端排序），否则进行前端排序   | boolean | false  |  
| sortable  | el-table-column的自带属性，决定了对应列是否可以排序。设置为'custom'时为后端排序，需要监听 Table 的 sort-change 事件   | string/boolean | false  |  
| sOrder  | 默认排序类型，'ascending' 表示升序，'descending' 表示降序 | string | - |  



*Ref属性传递： 需要使用`<el-table>`组件的ref对象的时候，使用`<el-table-extend>`的ref对象下的`tableRef`作为代替，例如：

```
    <el-table-extend ... ref="mainTable">
      ...
    </el-table-extend> 

    ...
    this.$refs.mainTable.tableRef

```

 

 


