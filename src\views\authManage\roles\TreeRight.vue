<template>
    <div v-for="item in list" class="menu-container">
        <div class="menu" :class="{ 'i-menu': item.menuName }">
            <p v-if="item.menuName || item.moduleName">
                <span @click="onClickMenu(item)" class="flex items-center">
                    {{ item.menuName || item.moduleName }}
                    <el-icon title="全选" :size="20"><Pointer /></el-icon>
                </span>
            </p>
            <ul v-if="item.buttons" class="buttons">
                <li v-for="button in item.buttons"
                    :class="{ success: button.assign }"
                    @click="onClickSelect(button)" >
                    <span class="check"></span>
                    <span>{{ button.buttonName }}</span>
                </li>
            </ul>
            <div v-if="item.modules">
                <TreeRight :list="item.modules"></TreeRight>
            </div>
        </div>
    </div>
</template>
<script>
import { Pointer } from '@element-plus/icons-vue';
export default {
    name: 'TreeRight',
    props: {
        list: {
            type: [Object, Array],
        },
    },
    components: {
        Pointer,
    },
    methods: {
        onClickSelect(item) {
            item.assign = !item.assign;
        },
        onClickMenu(list) {
            // 点目录，快速选择
            let yes = false;
            if (list.modules) {
                list.modules.forEach((item) => {
                    if (item.buttons) {
                        const obj = item.buttons.find((button) => button.assign);
                        if (obj) {
                            yes = true;
                        }
                    }
                });
            } else {
                const obj = list.buttons.find((button) => button.assign);
                if (obj) {
                    yes = true;
                }
            }

            if (list.modules) {
                list.modules.forEach((item) => {
                    if (item.buttons) {
                        item.buttons.forEach((button) => {
                            button.assign = !yes;
                        });
                    }
                });
            } else {
                list.buttons.forEach((button) => {
                    button.assign = !yes;
                });
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.menu-container {
    &:last-child {
        .menu {
            border: none;
        }
    }
    .menu {
        display: flex;
        padding-left: 40px;
        border-bottom: 1px solid #eee;

        &.i-menu {
            display: block;
            margin-top: 10px;
            padding-left: 15px;

            > p {
                display: block;
                // border-bottom: 1px solid #eee;
                line-height: 40px;
                margin: 0;
            }
        }

        > p {
            display: flex;
            align-items: center;
            line-height: 57px;
            min-width: 100px;
            font-weight: 700;
            user-select: none;

            > span {
                cursor: pointer;
            }
        }
    }
}

.buttons {
    display: flex;
    margin-left: 30px;
    margin-top: 12px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    overflow: hidden;

    li {
        display: flex;
        align-items: center;
        min-width: 130px;
        height: 30px;
        padding: 0 10px;
        border: 1px solid #e4e4e4;
        margin-right: 10px;
        margin-bottom: 10px;
        cursor: pointer;

        // transition: all 0.3s linear;
        &.success {
            // border-color: var(--theme-header-bg);
            color: var(--theme-header-bg);

            background: var(--el-color-primary-light-9);

            .check {
                background: var(--theme-header-bg);
            }
        }

        .check {
            background: #ccc;
            width: 10px;
            height: 10px;
            margin-right: 6px;
            border-radius: 50%;
        }

        &:hover {
            border-color: var(--theme-header-bg);
        }
    }
}
</style>
