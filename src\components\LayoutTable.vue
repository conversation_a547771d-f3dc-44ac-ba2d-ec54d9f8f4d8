<template>
  <div class="table-content" :class="types[theme]">
    <div class="search " v-show="slots.header">
      <slot name="header"></slot>
    </div>
    <div class="table-box">
      <div class="action" v-show="slots.action">
        <slot name="action"></slot>
      </div>
      <div class="content">
        <slot name="content"></slot>
      </div>
      <div class="pagination" v-show="slots.footer">
        <slot name="footer"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { useSlots } from "vue";
export default {
  name: "LayoutTable",
  props: {
    theme: String,
  },
  data() {
    return {
      types: {
        'border': 'layout-theme1'
      },
      slots: useSlots()
    }
  }
};
</script>
<style lang="scss" scoped>
.table-content {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all .2s;
  overflow: hidden;
  // width: 0;
  .search {
    display: flex;
    // padding: 0;
    margin-bottom: 5px;
    // margin: 5px ;
    // border-radius: var(--theme-table-radius);
    // color: #3c4353;

    // font-size: 13px;
    // font-weight: bold;
    // border-top: var(--theme-search-border);
    // border-bottom: var(--theme-search-border);
    // box-shadow: $base-box-shadow;
    :deep(.container) {
      background-color: #fafafa;
    }
    :deep(.search-left){
      flex: 1;
      align-items: center;
    }
    :deep(.padding-rb-10){
      padding: 5px 16px 5px 0px;
    }
    :deep(.search-right ){
      padding: 0 10px 0 0;
      display: flex;
      align-items: center;
    }
  }
  .table-box {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    // border-radius: var(--theme-table-radius);
    // border: var(--theme-table-border);
    padding: 0px 1px;
    background: var(--theme-bg);
  }
  
  .action{
    padding: 10px;
    border-bottom: var(--el-border);
    // background: #fafafa;
  }
  .content{
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
  .pagination {
    background: var(--theme-bg);
  }
}
</style>
