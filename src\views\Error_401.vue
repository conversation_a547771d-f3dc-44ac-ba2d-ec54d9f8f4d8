<template>
  <div class="absolute flex w-full h-full  align-center">
    <div class="w-full h-screen flex flex-col items-center justify-center">
    <img src="/img/error/401.svg" class="w-1/2 md:1/3 lg:w-1/4 text-blue-600" :alt="type" />
      <!-- <img src="/img/error/404.svg" class="img" :alt="type" /> -->
    <div class="flex flex-col items-center justify-center">
        <p class="text-3xl md:text-4xl lg:text-5xl text-gray-800 mt-12">无访问权限</p>
        <p class="md:text-lg lg:text-xl text-gray-600 mt-8">请检查您的用户权限 {{ getRouteName() }}</p>
        <a @click="fnReturn" href="#" class="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-gray-100 px-4 py-2 mt-12 rounded transition duration-150" title="返回">
          返回
        </a>
    </div>
</div>
  </div>
</template>

<script>
export default {
  name: 'Error_401',
  data() {
    return {
    }
  },
  methods: {
    getRouteName() {
      return this.$router.currentRoute.value.query.name || ''  
    },
    fnReturn() {
      let backName = this.$store.getters['user/validRouterName'] || 'Login'

      this.$router.replace({ name: backName })
    }
  }
}
</script>

<style scoped lang='scss'></style>
