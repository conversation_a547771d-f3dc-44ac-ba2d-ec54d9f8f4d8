<template>
    <template v-if="isBatch">
        <el-dropdown-item v-if="$auth['report:report:batchPublish']"
            @click="onBatchSendImage">
            <div class="inline-flex w-24 items-center p-0.5 leading-5">
                <i class="fa fa-send-o-2 pr-1"
                    style="height: 20px"></i>
                发送图像
            </div>
        </el-dropdown-item>
    </template>
    <template v-else>
        <el-button :disabled="buttonMsg.isReadOnly"
            class="m-vertical-btn t2"
            :class="{
                'm-vertical-btn t2': buttonMsg.icon,
                'margin_l': !buttonMsg.icon,
                'm-vertical-text': buttonMsg.isFold,
                't-border': buttonMsg.isBorder
            }"
            @click="onBatchSendImage">
            <svg class="fa"
                aria-hidden="true">
                <use :xlink:href="'#' + buttonMsg.icon"></use>
            </svg>
            <label>{{ buttonMsg.name }}</label>
        </el-button>
    </template>

    <el-dialog title="节点列表"
        v-model:modelValue="dialogVisible"
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        align-center
        width="500px"
        class="my-dialog t-default">
        <div class="c-flex-context">
            <el-checkbox-group v-if="nodeOptions.length"
                v-model="selectNodes">
                <div v-for="(item, index) in nodeOptions"
                    :key="index">
                    <el-checkbox :label="item.sId"
                        :key="item.sId">
                        <span>{{ item.sServerName }}</span>
                        <span v-if="item.sIp"
                            class="i-text">{{ `(${item.sIp})` }}</span>
                    </el-checkbox>
                </div>
            </el-checkbox-group>
            <el-empty v-else
                :image-size="80"
                description=" " />
        </div>
        <template #footer>
            <el-button-icon-fa _icon="fa fa-tick-line"
                type="primary"
                @click="onSendClick">确认</el-button-icon-fa>
            <el-button-icon-fa _icon="fa fa-close-1"
                @click="closeDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>
import { findStudyByPatientInfoId, sendByStudyUid } from '$supersetApi/projects/apricot/case/report.js';
import { getDicomNode } from '$supersetApi/projects/apricot/system/dicomSet.js'
export default {
    name: 'SendImage',
    props: {
        multipleSelection: {
            type: Array,
            default: () => [],
        },
        isBatch: {
            type: Boolean,
            default: true
        },
        // patientInfo: {
        //     type: Object,
        //     default: () => ({})
        // },
        buttonMsg: {
            type: Object,
            default: () => ({})
        },
    },
    data () {
        return {
            dialogVisible: false,
            nodeOptions: [],
            selectNodes: [],
        };
    },
    methods: {
        async onSendClick () {
            const selectNodes = this.nodeOptions.filter((item) => this.selectNodes.includes(item.sId));
            if (!selectNodes.length) {
                this.$message.warning('请勾选发送节点！');
                return;
            }
            const selections = this.multipleSelection;
            var isStopSend = false;
            var loading = this.loadFindTip('发送中...');
            var count = 0;
            var innerCount = 0;
            var idx = 0; 
            for (var i = 0; i < selections.length; i++) {
                if (isStopSend) {
                    // 停止循环；
                    break;
                }
                const item = selections[i];
                var studyList = [];
                const params = {
                    iIsRebuid: null,
                    sPatientInfoId: item.sId
                }
                // 查找study
                await findStudyByPatientInfoId(params).then(res => {
                    if (res.success) {
                        studyList = res.data || [];
                        if (studyList.length) {
                            idx++;
                        } else {
                            isStopSend = true;
                            loading.close();
                            this.$message.error(`中断发送： ${item.sName} 未查到图像！`);
                        }
                    } else {
                        isStopSend = true;
                        loading.close();
                        this.$message.error(res.msg)
                    }
                }).catch(err => {
                    isStopSend = true;
                    loading.close();
                })
                if (isStopSend) {
                    // 停止循环；
                    break;
                }
                innerCount += studyList.length * selectNodes.length;
                for (var k = 0; k < studyList.length; k++) {
                    const studyItem = studyList[k];
                    for (var j = 0; j < selectNodes.length; j++) {
                        if (isStopSend) {
                            // 停止循环；
                            break;
                        }
                        const node = selectNodes[j];
                        const jsonData = {
                            iPort: node.iPort,
                            sAETitle: node.sAETitle,
                            sIp: node.sIp,
                            studyInstanceUid: studyItem.studyInstanceUid,
                        };
                        await sendByStudyUid(jsonData).then((res) => {
                            if (res.success) {
                                count++;
                            } else {
                                isStopSend = true;
                                loading.close();
                            }
                        }).catch((err) => {
                            isStopSend = true;
                            loading.close();
                        });
                    }
                }
            }
            if (idx >= selections.length && count === innerCount) {
                this.$message.success('完成发送！');
                loading.close();
                this.closeDialog();
            }
        },
        // 加载状态
        loadFindTip (text) {
            return this.$loading({
                lock: true,
                text: text || '文件生成中...',
                background: 'rgba(255, 255, 255, 0.5)',
                customClass: 'my-loading',
            });
        },
        onBatchSendImage () {
            if (!this.multipleSelection.length) {
                this.$message.warning('请勾选患者数据！');
                return;
            }
            this.dialogVisible = true;
            this.selectNodes =  [];
            this.getDicomNode();
        },
        // 获取节点数据
        getDicomNode () {
            this.nodeLoading = true;
            getDicomNode()
                .then((res) => {
                    this.nodeLoading = false;
                    if (res.success) {
                        this.nodeOptions = res.data || [];
                        return;
                    }
                })
                .catch((err) => {
                    this.nodeLoading = false;
                    console.log(err);
                });
        },
        closeDialog () {
            this.dialogVisible = false;
        },
    },
    mounted () { },
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px 20px 10px 20px;
    box-sizing: border-box;
    .i-text {
        color: #666;
        padding-left: 10px;
    }
}
</style>
