<template>
    <div class="plane-content">
        <ul class="plane-nav" :style="{height: navs.length ? '30px' : '0', borderBottom: navs.length ? '2px solid #eee' : '0'}">
            <li v-for="item in navs" @click="onClickToY(item.field)"> {{ item.label }} </li>
        </ul>
        <div v-if="!renderData.length"
            style="display: flex;height: 100%;justify-content: center;align-items: center;">
            <img src="./svg/empty.svg">
        </div>
        <el-scrollbar class="scrollbar xx-el-scrollbar" ref="myScrollbar" wrap-class="smooth-scrollbar" :style="{height: navs.length ? 'calc(100% - 38px)' : '100%'}">
            <el-form ref="formData"
                board="board"
                class="scope--rules scrollbar-form"
                :model="formData"
                :rules="rules"
                :show-message="false"
                :inline="true">
                    <RenderGeneratorUi
                        v-for="(item, index) in renderData"
                        :dataItem="item"
                        :formData="formData" 
                        :key="item.id">
                    </RenderGeneratorUi>
                </el-form>
        </el-scrollbar>
    </div>
</template>
<script>
import RenderGeneratorUi from './RenderGeneratorUi'
export default {
    name: 'RenderMainGenerator',
    components: {
        RenderGeneratorUi
    },
    props: {
        renderData: {
            default: () => []
        },
        fCoefficient: undefined,
        fDosage: undefined
    },
    data() {
        return {
            formData: {},
            rules: {},
            hasfRecipeDosetKey: false,
            navs: []
        }
    },
    watch: {
        renderData: {
            handler(val) {
                if (this.renderData && this.renderData.length) {
                    this.formData = {};
                    this.rules    = {};
                    const treeTraverse = (list) => {
                        list.forEach(item => {
                            if (item.layout === 'formItem') {
                                // 字段
                                this.formData[item.field] = ['null','undefined'].includes(item.value) ? undefined : item.value;
                                // 验证
                                this.rules[item.field] = [];
                                // 必填
                                if (item.required) {
                                    this.rules[item.field].push({ 
                                        required: true, 
                                        message: `${item.titleShow ? item.label : ''} 必填`, 
                                        trigger: 'blur' 
                                    },)
                                }
                                // 正则

                            }else if (item.children) {
                                treeTraverse(item.children);
                            }
                        });
                    }
                    treeTraverse(this.renderData);
                    setTimeout(() => {
                        if (this.$refs['formData']) {
                            this.$refs['formData'].clearValidate();
                        }
                        if(this.formData.hasOwnProperty('fRecipeDose')) {
                            this.hasfRecipeDosetKey = true;
                        }
                    }, 100);
                    
                    this.getNavigation();
                }
            },
            immediate: true
        },
        'formData.fWeight'(val) {
            if(!val) {
                this.formData.fRecipeDose = undefined;
                return
            }
            val = Number(val);
            if(this.hasfRecipeDosetKey && this.fCoefficient !== null && this.fCoefficient >= 0) {
                this.formData.fRecipeDose = val ? (val * this.fCoefficient).toFixed(2) : undefined;
                return
            }
            if(this.hasfRecipeDosetKey && this.fDosage !== null && this.fDosage >= 0) {
                this.formData.fRecipeDose = val ? val * this.fDosage : undefined;
                return
            }
        }
    },
    methods: {
        updateData() {
            const treeTraverse = (list) => {
                list.forEach(item => {
                    if (item.layout === 'formItem' && this.formData[item.field] !== undefined) {
                        item.value = this.formData[item.field];
                    }else if (item.children) {
                        treeTraverse(item.children);
                    } 
                })
            }
            treeTraverse(this.renderData);
        },
        // 获取导航
        getNavigation() {
            const navs = []
            const getSpecialAttr = (list) => {
                list.forEach(item => {
                    if (item.navigation) {
                        navs.push(item);
                    }
                    if (item.children) {
                        getSpecialAttr(item.children);
                    }
                });
            }
            getSpecialAttr(this.renderData);
            this.navs = navs;
        },
        // 点击导航去某个位置
        onClickToY(elementId) {
            let offsetTop = 0
            const getOffsetTop = (dom) => {
                offsetTop += dom.offsetTop;
                const pNode = dom.parentNode;
                // 没有上一个节点，或者已到滚动条边界
                if (!pNode || (pNode && pNode.getAttribute('board') == 'board')) {
                    return;
                }
                getOffsetTop(pNode);
            }
            getOffsetTop(document.getElementById(elementId));

            this.$refs['myScrollbar'].setScrollTop(offsetTop);
        },
        // 数据验证
        validate() {
            return new Promise(resolve => {
                this.$refs['formData'].validate(valid => {
                    if (!valid) {
                        this.$message.closeAll();
                        this.$message({
                            message: '请完成必填信息的录入！',
                            type: 'info',
                            duration: 3000
                        });
                        resolve({
                            code: 201,
                            success: true,
                            msg: '请完成必填信息的录入！',
                        })
                    }
                    this.updateData();
                    // 返回数据
                    resolve({
                        code: 200,
                        success: true,
                        msg: '成功！',
                        data: JSON.stringify(this.renderData)
                    })
                })
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.plane-content{
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .plane-nav{
        height: 0;
        line-height: 30px;
        padding: 0;
        margin: 0px; // 0px 8px;
        display: flex;
        list-style-type: none;
        overflow: hidden;
        li {
            position: relative;
            min-width: 96px;
            text-align: center;
            cursor: pointer;
            &:hover{
                color: #23527c;
                text-decoration: underline;
            }
            &::before{
                content: "";
                width: 1px;
                height: 14px;
                background: #999;
                position: absolute;
                right: 0;
                top: 8px;
            }
        }
    }
    .scrollbar{
        flex: 1;
        height: 0;
        .scrollbar-form{
            padding: 0;
            box-sizing: border-box;
        }
    }
    .el-form{
        height: 100%;
    }
}
:deep(.smooth-scrollbar) {
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
}
</style>
