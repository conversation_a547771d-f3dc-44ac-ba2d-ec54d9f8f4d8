<template>
    <!-- 相似病历 -->
    <div class="g-content">
        <div class="c-info">
            <el-row :gutter="10">
                <el-col :span="3">
                    <div class="i-title">
                        <span>姓名：</span>
                        <span style="font-weight: bold;">{{info.sName}}</span>
                    </div>
                </el-col>
                <el-col :span="3">
                    <div class="i-title">
                        <span>性别：</span>
                        <span>{{info.sSexText}}</span>
                    </div>
                </el-col>
                <el-col :span="3">
                    <div class="i-title">
                        <span>年龄：</span>
                        <span>{{info.sAge}}</span>
                    </div>
                </el-col>
                <el-col :span="5">
                    <div class="i-title">
                        <span>核医学号：</span>
                        <span>{{info.sNuclearNum}}</span>
                    </div>
                </el-col>
                <el-col :span="5">
                    <div class="i-title">
                        <span>检查类型：</span>
                        <span>{{info.sRoomText}}</span>
                    </div>
                </el-col>
                <el-col :span="5">
                    <div class="i-title">
                        <span>检查项目：</span>
                        <span>{{info.sProjectName}}</span>
                    </div>
                </el-col>
            </el-row>
        </div>
        <div class="c-search">
            <el-row :gutter="10">
                <span class="i-search-title">匹配条件:</span>
                <el-col :span="24"
                    class="c-line">
                    <div class="m-labelInput">
                        <label>检查项目</label>
                        <el-autocomplete v-model="condition.sRecord"
                            :fetch-suggestions="autocompleteSearch"
                            popper-class="global-autocomplete"
                            placeholder="请输入内容"
                            value-key="sItemName"
                            clearable>
                            <!-- <i class="el-icon-edit el-input__icon" slot="suffix" @click="handleIconClick"></i> -->
                            <template v-slot="{ item }">
                                <div class="name">{{ item.sItemName || '(名称)' }}&nbsp;&nbsp;<span style="color: #999;float: right;">{{ item.sItemCode || '(编码)' }}</span></div>
                                <div></div>
                            </template>
                        </el-autocomplete>
                    </div>
                    <div class="m-labelInput">
                        <label>临床诊断</label>
                        <el-input v-model="condition.sDiagnosis"
                            clearable></el-input>
                    </div>
                    <div class="m-labelInput">
                        <label>检查所见</label>
                        <el-input v-model="condition.sInspectSee"
                            clearable></el-input>
                    </div>
                    <div class="m-labelInput">
                        <label>诊断意见</label>
                        <el-input v-model="condition.sDiagnosticOpinion"
                            clearable></el-input>
                    </div>
                    <el-button-icon-fa @click="mxDoSearch"
                        :loading="loading"
                        class="i-button"
                        type="primary"
                        size="small"
                        _icon="fa fa-search">查询</el-button-icon-fa>
                </el-col>
            </el-row>
        </div>
        <div class="c-table"
            v-loading="loading">
            <el-table :data="tableData"
                @sort-change="mxOnSort"
                ref="mainTable"
                @row-click="onClickRow"
                highlight-current-row
                border
                stripe
                height="100%"
                style="width: 100%">
                <template v-for="(item, index) in similarCaseConfig">
                    <el-table-column show-overflow-tooltip
                        :key="index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort"
                        v-if="!item.iIsHide">
                        <template v-slot="scope">
                            <template v-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                    </el-table-column>
                </template>
            </el-table>
        </div>
        <div class="c-action">
            <el-button-icon-fa size="small"
                @click="onReduilbOpen(0)"
                :loading="menuDisabled0"
                _icon="fa fa-reading-image-computer-browse">阅 图</el-button-icon-fa>
            <el-button-icon-fa size="small"
                @click="onReduilbOpen(1)"
                :loading="menuDisabled1"
                _icon="fa fa-zhongjian">重 建</el-button-icon-fa>
            <el-button-icon-fa size="small"
                @click="openWebReadViewer()"
                :loading="menuWebDisabled0"
                _icon="fa fa-reading-image-computer-browse">web阅图</el-button-icon-fa>
            <el-button-icon-fa size="small"
                @click="openWebReBuildViewer()"
                :loading="menuWebDisabled1"
                _icon="fa fa-poly-rebuildding-1">web重建</el-button-icon-fa>
            <el-button-icon-fa size="small"
                _icon="fa fa-scanning-data"
                @click="onOpenLookImage">扫描资料</el-button-icon-fa>
            <!-- 报告预览 -->
            <ReportPreviewBtn :propParams="{ patient: editLayer.selectedItem,  isBatch: false, idKey:'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"></ReportPreviewBtn>

        </div>
        <div class="c-info-more">
            <!-- <el-scrollbar class=" xx-el-scrollbar"> -->
            <div class="c-box-1">
                <DragAdjust class="c-template c-patientInfo"
                    :dragAdjustData="DA1">
                    <template v-slot:c1>
                        <ul class="c-ul c-left">
                            <li>
                                <h4>临床诊断</h4>
                                <div v-html="currentItem.sClinicalDiagnosis"></div>
                            </li>
                            <li>
                                <h4>简要病史</h4>
                                <div v-html="currentItem.sMedicalHistory"></div>
                            </li>
                            <li>
                                <h4>既往病史</h4>
                                <div v-html="currentItem.sInquiryOtherMessage"></div>
                            </li>
                        </ul>
                    </template>
                    <template v-slot:c2>
                        <ul class="c-ul c-right">
                            <li>
                                <h4>检查所见</h4>
                                <div v-html="currentItem.sInspectSee"></div>
                            </li>
                            <li>
                                <h4>诊断意见</h4>
                                <div v-html="currentItem.sDiagnosticOpinion">
                                </div>
                            </li>
                        </ul>
                    </template>
                </DragAdjust>
            </div>
            <!-- </el-scrollbar> -->
        </div>

        <!-- 扫描件 -->
        <LookImage v-model:dialogVisible="d_lookImage_v"
            :patientInfo="this.editLayer.selectedItem" 
            :isOnlyShowUpload="true"></LookImage>
    </div>
</template>
<script>
import LookImage from "$supersetViews/apricot/components/LookImage.vue"    // 扫描件
import ReportPreviewBtn from '$supersetViews/apricot/components/ReportPreviewBtn.vue' // 报告预览打印按钮

import Configs from "./configs/similarCase.js"
import { mixinTable, openWebReadImgOrRebuild } from '$supersetResource/js/projects/apricot/index.js'
// 接口
import Api from '$supersetApi/projects/apricot/case/report.js'
import ApiProjectSet from '$supersetApi/projects/apricot/appointment/projectSet.js'
import ApiAssist from '$supersetApi/projects/apricot/assistServe/index.js'
import { queryClientConfig, readClientId, verificationCode } from '$supersetApi/projects/apricot/case/report.js'
import { queryClientIp } from '$supersetApi/user'
import { baseURL } from '$supersetUtils/request'
import { getPatientInfo } from '$supersetApi/projects/apricot/common'
export default {
    mixins: [mixinTable],
    components: {
        LookImage,
        ReportPreviewBtn
    },
    data () {
        return {
            iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
            similarCaseConfig: Configs.table,
            loading: false,
            currentItem: {},
            info: {},
            DA1: Configs.DA1,
            menuDisabled: false, // 加载状态
            menuDisabled0: false,
            menuDisabled1: false,
            menuWebDisabled0: false,
            menuWebDisabled1: false,
            d_lookImage_v: false,
            first: true,
            clientParams: {
                needVerifyLicence: null,//阅图是否需要授权
                verificationStatus: null, // 授权状态
                clientId: null,
            }
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
        moduleName () {
            let temp = this.$route.query.module
            if (temp) {
                return temp
            } else {
                return ''
            }
        }
    },
    mounted () {
        document.title = '相似病历'
        if (this.$route.query.id) {
            this.getPatientInfo(this.$route.query.id)
        }
    },
    methods: {
        // 打开web重建
        async openWebReBuildViewer (iIsRebuild = 1) {
            if (!Object.keys(this.editLayer.selectedItem).length) {
                this.$message.warning('请选择一条患者数据');
                return
            }
            if (iIsRebuild == 0) {
                this.menuWebDisabled0 = true
            }
            if (iIsRebuild == 1) {
                this.menuWebDisabled1 = true
            }
            if (this.moduleName != 'remoteConsultMng') {
                await this.getClientConfig()
                const needVerifyLicence = this.clientParams.needVerifyLicence
                // 若接口链接超时，默认值为null,
                if (needVerifyLicence == null) {
                    this.menuWebDisabled0 = false
                    this.menuWebDisabled1 = false
                    return
                }
                if (needVerifyLicence) {
                    await this.getClientId()
                    const clientId = this.clientParams.clientId
                    // 没有获取到id，默认值为null,
                    if (!clientId) {
                        this.menuWebDisabled0 = false
                        this.menuWebDisabled1 = false
                        return
                    }
                    await this.getVerificationCode()
                    if (!this.clientParams.verificationStatus) {
                        this.menuWebDisabled0 = false
                        this.menuWebDisabled1 = false
                        return
                    }
                }
            }
            this.menuWebDisabled0 = false
            this.menuWebDisabled1 = false
            let info = {
                sPatientId: this.editLayer.selectedItem.sId,
                deviceTypeId: this.editLayer.selectedItem.sRoomId
            }
            openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info)
        },
        // 打开web阅图
        openWebReadViewer () {
            this.openWebReBuildViewer(0)
        },
        async onReduilbOpen (iIsRebuid = 0) {
            let selectedItem = this.editLayer.selectedItem;
            if (!Object.keys(selectedItem).length) {
                this.$message.warning('请选择一条患者数据');
                return
            }
            if (iIsRebuid == 0) {
                this.menuDisabled0 = true
            }
            if (iIsRebuid == 1) {
                this.menuDisabled1 = true
            }
            if (this.moduleName != 'remoteConsultMng') {
                await this.getClientConfig()
                const needVerifyLicence = this.clientParams.needVerifyLicence
                // 若接口链接超时，默认值为null,
                if (needVerifyLicence == null) {
                    this.menuDisabled0 = false
                    this.menuDisabled1 = false
                    return
                }
                if (needVerifyLicence) {
                    await this.getClientId()
                    const clientId = this.clientParams.clientId
                    // 没有获取到id，默认值为null,
                    if (!clientId) {
                        this.menuDisabled0 = false
                        this.menuDisabled1 = false
                        return
                    }
                    await this.getVerificationCode()
                    if (!this.clientParams.verificationStatus) {
                        this.menuDisabled0 = false
                        this.menuDisabled1 = false
                        return
                    }
                }
            }
            await this.queryClientIp()
            const params = {
                sImgPatientId: selectedItem.sImgPatientId,
                sImgStudyDate: selectedItem.sImgStudyDate,
                sImgAccessionNumber: selectedItem.sImgAccessionNumber,
                iIsRebuid: iIsRebuid
            }
            ApiAssist.rebuildOpenView(params).then((res) => {
                this.menuDisabled0 = false
                this.menuDisabled1 = false
                if (res && !res.success) {
                    this.$message.error(res.msg)
                    return
                }
            }).catch(() => {
                this.menuDisabled0 = false
                this.menuDisabled1 = false
            })
        },
        // 获取客户端IP
        async queryClientIp () {
            await queryClientIp().then(res => {
                if (res.data) {
                    if(res.data.clientIp) {
                        let apricotAssist = baseURL.apricotAssist;
                        let ulrAnalysis = apricotAssist.split(':');
                        let protocol = ulrAnalysis[0];
                        let port = ulrAnalysis[2];
                        let newUrl= `${protocol}://${res.data.clientIp}:${port}`;
                        window.configs.urls.apricotAssist = newUrl;
                        baseURL.apricotAssist = newUrl;
                    }
                    return
                }
                // 错误处理
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err);
            })
        },
        // 打开扫描资料
        onOpenLookImage () {
            let selectedItem = this.editLayer.selectedItem;
            if (!Object.keys(selectedItem).length) {
                this.$message.warning('请选择一条患者数据');
                return
            }
            this.d_lookImage_v = true
        },
        // 查看报告
        openReport () {
            this.mxOpenReportPreviewWindow()
        },
        // mxDoRefresh 会回调这个方法--获取表格数据
        getData (params) {
            if (this.first) {
                this.first = false;
                this.loading = false;
                return
            }
            Api.getSimilarPatient(params.condition).then((res) => {
                this.loading = false;
                this.tableData = [];
                this.currentItem = {}
                if (res.success) {
                    this.tableData = [];
                    this.tableData = res.data == null ? [] : res.data
                    // 赋选中状态
                    // this.mxSetSelected()
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        },
        // 获取患者结果通过id
        getPatientResultById (id) {
            Api.getPatientResultById({ sPatientId: id }).then(res => {
                if (res.success) {
                    this.currentItem = res.data
                    return;
                }
                this.currentItem = {}
                this.$message.error(res.msg)
            })
        },
        getPatientInfo (id) {
            getPatientInfo({ sId: id }).then(res => {
                if (res.success) {
                    this.info = res.data
                    return;
                }
                this.info = {}
                this.$message.error(res.msg)
            })
        },
        onClickRow (row) {
            this.getPatientResultById(row.sId);
            this.editLayer.selectedItem = row;
        },
        autocompleteSearch (queryString, callback) {

            const params = {
                condition: { sItemName: queryString, iIsEnable: "1" },
                page: { pageSize: 9999, pageCurrent: 1 }
            }
            // element 失去焦点报错它的问题
            //向后端发送查询字符串
            ApiProjectSet.getItemData(params).then((res) => {
                if (res.success) {
                    callback(res.data|| [])
                } else {
                    callback([])
                }
            }).catch((err) => {
                console.log('err:', err)
            })
            // 调用 callback 返回建议列表的数据

        },
        // 查询客户端配置
        async getClientConfig () {
            await queryClientConfig().then(res => {
                if (res.success) {
                    this.clientParams.needVerifyLicence = res.data.needVerifyLicence
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 查询本机主板序列号
        async getClientId () {
            await readClientId().then(res => {
                if (res.success) {
                    this.clientParams.clientId = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },
        // 获取授权状态
        async getVerificationCode () {
            let params = {
                clientId: this.clientParams.clientId
            }
            await verificationCode(params).then(res => {
                if (res.success) {
                    this.clientParams.verificationStatus = res.success
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        }
    }
}
</script>
<style lang="scss" scoped>
.g-content {
    height: 100%;
    padding: 0px 10px 10px;
    display: flex;
    flex-direction: column;
    .c-action {
        padding-top: 10px;
        text-align: left;
        // border-bottom: 1px solid #eee;
    }
    .c-table {
        height: 200px;
    }
    .c-info {
        padding-top: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }
    .c-search {
        margin: 10px 0 0;
        .i-search-title {
            width: 80px;
            display: inline-block;
            padding-left: 10px;
        }
        .c-line {
            display: flex;
            align-items: center;
        }
        > .el-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .i-button {
            margin: 0px 10px;
        }
    }
    .c-info-more {
        height: 0px;
        display: flex;
        flex-direction: column;
        margin-top: 10px;
        flex: 1;
        overflow: auto;
        border: 1px solid #eee;
        .c-box-1 {
            flex: 1;
            height: 450px;
        }
        .c-box-2 {
            height: 150px;
            width: 100%;
            border: 1px solid #eee;
            border-top: none;
        }
        .c-ul {
            padding: 0px;
            margin: 0;
            text-align: left;
            &.c-left {
                li {
                    height: 150px;
                }
            }
            &.c-right {
                li {
                    height: 225px;
                    > div {
                        padding: 2px 6px;
                    }
                }
            }
            li {
                list-style: none;
                width: 100%;
                h4 {
                    height: 29px;
                    padding-left: 10px;
                    margin: 0px;
                    font-weight: bold;
                    font-size: 14px;
                    line-height: 28px;
                    background: #f5f9fc;
                    border-bottom: 1px solid #eee;
                }
                > div {
                    padding: 10px;
                    overflow: auto;
                    height: calc(100% - 29px);
                }
            }
        }
    }
}

.g-content1 {
    .c-box1 {
        height: 43px;
        line-height: 43px;
    }
    .c-box2 {
        border-top: 1px solid #eee;
        .i-title {
            padding: 15px 0px;
            color: #606266;
            font-size: 14px;
            word-break: break-all;
            > span:first-child {
                font-weight: bold;
            }
        }
        .g-info {
            .g-reportbrowse-midea {
                > span {
                    font-weight: bold;
                    display: block;
                    padding: 11px 0px 8px 0px;
                }
            }
            .g-footer {
                margin-top: 10px;
                .c-item {
                    display: flex;
                    justify-content: space-between;
                    > div {
                        width: 190px;
                        height: 30px;
                        text-align: left;
                    }
                }
            }
        }
    }
}
.el-table--border,
.el-table--group {
    border: 1px solid #eee;
}
.el-table--border::after,
.el-table--group::after,
.el-table::before {
    background-color: #eee;
}
.el-table--border {
    border-right: none;
    border-bottom: none;
}
</style>
