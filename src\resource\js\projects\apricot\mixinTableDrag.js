import { deepClone } from '@/utils/function'
//  混入 表格排序
const mixinTableDrag = {
    data () {
        return {
            dragRow: {},
            config: {
                elementSeletor: 'tbody',
                filter: '.row-filtered',
                handle: '.i-sort',
                onEnd: (evt) => {
                    this.dragRow = {};
                    const { oldIndex, newIndex } = evt;
                    if (newIndex === oldIndex) return;
                    this.rowDropEnd(oldIndex, newIndex);
                },
                onStart: (evt) => {
                    this.dragRow = this.tableData[evt.oldIndex]
                }
            },
            sortApi: '',  // 排序接口
            sortKey: 'sId', // 排序字段
        }
    },
    methods: {
        // tableRowClassName ({ row, rowIndex }) {
        //     if (this.dragRow.sDistrictId && row.sDistrictId !== this.dragRow.sDistrictId) {
        //         return "row-filtered";
        //     }
        //     return "";
        // },
        //行拖拽 
        rowDropEnd (oldIndex, newIndex, newFormTable) {
            newFormTable = deepClone(this.tableData);
            const changeData = newFormTable.splice(oldIndex || 0, 1)
            newFormTable.splice(newIndex || 0, 0, changeData[0]);
            let newArr = [];
            let sortKey = this.sortKey || 'sId';
            newFormTable.map((item, index) => {
                const params = {
                    iIndex: index + 1,
                    sId: item[sortKey],
                }
                newArr.push(params)
            })
            const nowScroll = this.computeTableScrollHeight();
            this.sortApi(newArr).then(res => {
                if (res.success) {
                    // this.reRender = false;
                    this.tableData = [];
                    this.$nextTick(() => {
                        // this.reRender = true;
                        this.tableData = deepClone(newFormTable);
                        // this.mxGetTableList();
                        // // 赋选中状态
                        // this.mxSetSelected();

                        //页面数据更新后调用以下代码， 滚动到计算好的位置
                        this.$nextTick(() => {
                           this.$refs.mainTable?.setScrollTop(nowScroll);
                        })
                    })
                }
            })
        },
        //计算列表滚动高度
        computeTableScrollHeight () {
            let vmEl = this.$refs.mainTable;
            const scrollParent = vmEl?.scrollBarRef?.wrapRef;
            const nowScroll = scrollParent?.scrollTop; //table内部的滚动条的当前位置距离table表头的高度
            return nowScroll;
        },
    }
}
export default mixinTableDrag;
