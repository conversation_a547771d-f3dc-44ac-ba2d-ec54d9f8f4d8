<template>
    <!-- <el-form-item v-if="!configData.iIsHide"
        :prop="configData.sProp"
        :class="formItemClass"
        :style="{ 
            width: configData.iLayourValue ? '100%' : (configData.sWidth && configData.sWidth.includes('%') ? configData.sWidth : '')}"> -->

    <div class="m-labelInput"
        v-if="!configData.iIsHide"
        :style="{
                width: configData.iLayourValue ? 'calc(100% - 10px)' : (configData.sWidth ? (configData.sWidth.includes('%') ? 'calc(100% - 10px)' : configData.sWidth) : defaultV.width),
                height: configData.sHeight ? ( configData.sInputType !== 'textarea' ? configData.sHeight : '') : defaultV.height,
                background: configData.sBgColor ? configData.sBgColor : defaultV.bg,
                fontSize: configData.sFontSize ? configData.sFontSize : defaultV.fontSize,
                fontWeight: configData.iFontWeight ? 'bold' : defaultV.fontWeight,
                color: configData.sFontColor ? configData.sFontColor : defaultV.fontColor }">

        <label :style="{
                '--labelbgColor': configData.sLabelBgColor ? configData.sLabelBgColor : defaultV.labelBgColor,
                marginLeft: marginLefts.includes(configData.sInputType) ? '40px' : '',
                background: configData.sLabelBgColor ? configData.sLabelBgColor : defaultV.labelBgColor,
                fontWeight: configData.iLabelFontWeight ? 'bold' : defaultV.labelFontWeight,
                fontSize: configData.sLabelFontSize ? configData.sLabelFontSize : defaultV.labelFontSize,
                color: configData.sLabelFontColor ? configData.sLabelFontColor : defaultV.labelFontColor}">{{configData.sLabel}}</label>

        <slot v-if="!configData.iCustom"
            name="default">
            <!-- 文本 -->
            <el-input v-if="configData.sInputType === 'text'"
                v-model="formData[configData.sProp]"
                :readonly="!!configData.iReadonly"
                clearable></el-input>
            <!-- 数字 -->
            <!-- :precision="2" -->
            <el-input-number v-else-if="configData.sInputType === 'number'"
                v-model="formData[configData.sProp]"
                :controls="false"
                :min="0"
                :max="99999999"
                :readonly="!!configData.iReadonly"></el-input-number>
            <!-- 颜色 -->
            <el-color-picker v-else-if="configData.sInputType === 'color'"
                type="color"
                :predefine="predefineColors"
                v-model="formData[configData.sProp]"
                :readonly="!!configData.iReadonly"
                @change="(val)=> {changeColorPicker(val, configData.sProp)} "></el-color-picker>
            <!-- 日期 -->
            <el-date-picker v-else-if="configData.sInputType === 'date-picker'"
                v-model="formData[configData.sProp]"
                type="date"
                
                :readonly="!!configData.iReadonly"
                :picker-options="isPickerOptions ? pickerOptions : {}">
            </el-date-picker>
            <!-- 日期时间 -->
            <el-date-picker v-else-if="configData.sInputType === 'dateTime-picker'"
                v-model="formData[configData.sProp]"
                type="datetime"
                
                :readonly="!!configData.iReadonly"
                :picker-options="isPickerOptions ? pickerOptions : {}">
            </el-date-picker>
            <!-- 时间 -->
            <el-time-picker v-else-if="configData.sInputType === 'time-picker'"
                v-model="formData[configData.sProp]"
                :readonly="!!configData.iReadonly">
            </el-time-picker>

            <!-- 下拉选择 -->
            <el-select v-else-if="configData.sInputType === 'option'"
                v-model="formData[configData.sProp]"
                :disabled="!!configData.iReadonly"
                placeholder=""
                clearable
                @change="$forceUpdate()">
                <el-option v-for="item in optionData[configData.sOptionProp]"
                    v-show="item.iIsAuxiliary != 1"
                    :key="item.sValue"
                    :label="item.sName"
                    :value="typeof formData[configData.sProp] === 'number' ? Number(item.sValue):item.sValue">
                </el-option>
            </el-select>

            <!-- 文本-单位 -->
            <div style="width: 100%; height: 100%; display: flex;"
                v-else-if="configData.sInputType === 'text-unit'">
                <el-input style="flex: 1;"
                    
                    v-model="formData[configData.sProp]"
                    :readonly="!!configData.iReadonly"></el-input>
                <span class="i-suffix-span"
                    :style="{
                    height: configData.sHeight ? configData.sHeight : defaultV.height,
                    fontSize: 'inherit',}"><i>{{configData.sUnit}}</i></span>
            </div>
            <!-- 数字-单位 -->
            <div style="width: 100%; height: 100%; display: flex;"
                v-else-if="configData.sInputType === 'number-unit'">
                <el-input-number style="flex: 1;"
                    
                    v-model="formData[configData.sProp]"
                    :controls="false"
                    :precision="2"
                    :min="0"
                    :max="99999999"
                    :readonly="!!configData.iReadonly"></el-input-number>
                <span class="i-suffix-span"
                    :style="{
                        height: configData.sHeight ? configData.sHeight : defaultV.height,
                        fontSize: 'inherit',}"><i>{{configData.sUnit}}</i></span>
            </div>
            <!-- 文本域 -->
            <el-input v-else-if="configData.sInputType === 'textarea'"
                type="textarea"
                :rows="configData.sHeight ? Math.round(parseInt(configData.sHeight) / 34) : 2"
                v-model="formData[configData.sProp]"
                :readonly="!!configData.iReadonly"></el-input>
            <!-- 输入值后自动标记- 文本1 -->
            <div v-else-if="configData.sInputType === 'input-01'"
                class="c-check-input"
                :class="[(formData[configData.sProp] && formData[configData.sProp].length) ? 'i-start' : 'i-disable']">
                <div class="c-i-innner">
                    <i class="i-check"></i>
                </div>
                <el-input v-model="formData[configData.sProp]"
                    :readonly="!!configData.iReadonly"
                    style="flex: 1;"></el-input>
            </div>
            <!-- 输入值后自动标记- 文本2 -->
            <div v-else-if="configData.sInputType === 'input-02'"
                class="c-check-input"
                :class="[(formData[configData.sProp] && formData[configData.sProp].length) ? 'i-start' : 'i-disable']">
                <div class="c-i-innner">
                    <i class="i-check"></i>
                </div>
                <el-input v-model="formData[configData.sProp]"
                    :readonly="!!configData.iReadonly"
                    style="flex: 1;"></el-input>
                <span class="i-suffix-span i-no-border"
                    :style="{
                        height: configData.sHeight ? configData.sHeight : defaultV.height,
                        fontSize: 'inherit',}"><i>{{configData.sUnit}}</i></span>
            </div>
            <!-- 选中值后自动标记-下拉框 -->
            <div v-else-if="configData.sInputType === 'option-01'"
                class="c-check-input"
                :class="[(formData[configData.sProp] && formData[configData.sProp].length) ? 'i-start' : 'i-disable']">
                <div class="c-i-innner">
                    <i class="i-check"></i>
                </div>
                <el-select class="i-set-padding"
                    v-model="formData[configData.sProp]"
                    :readonly="!!configData.iReadonly"
                    style="flex: 1;">
                    <el-option v-for="item in optionData[configData.sOptionProp]"
                        v-show="item.iIsAuxiliary != 1"    
                        :key="item.sValue"
                        :label="item.sName"
                        :value="item.sValue">
                    </el-option>
                </el-select>
            </div>
        </slot>
        <slot name="custom"
            v-else></slot>

    </div>
    <!-- </el-form-item> -->
</template>

<script>
export default {
    props: {
        configData: { // 自定义数据
            type: Object,
            default: () => ({})
        },
        formData: { // 绑定的表单数据对象
            type: Object,
            default: () => ({})
        },
        optionData: { // 下拉选项
            type: Object,
            default: () => ({})
        },
        formItemClass: {
            type: String,
            default: ''
        },
        verifyObjName: { // 验证对象名称
            type: String,
            default: 'rules'
        },
        isPickerOptions: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            defaultV: {
                width: '145px',
                height: '30px',
                bg: 'white',
                fontSize: '14px',
                fontColor: '#606266',
                labelFontColor: '#828284',
                labelFontSize: '14px',
                fontWeight: '400',
                labelFontWeight: '400'
            },
            marginLefts: ['input-01', 'option-01', 'input-02'],
            predefineColors: window.configs.colors.elementConfig,
            pickerOptions: {
                disabledDate: time => {
                    return time.getTime() > new Date().getTime()
                }
            }
        }
    },
    methods: {
        changeColorPicker (val, key) {
            // 当清空color时 值会被置为null , 但是后台接口不修改提交为null的值，但是会修改空字符空（如：修改=>sBgColor:'', 不修改=>sBgColor:null）
            if (!val) {
                this.formData[key] = '';
            }
        }
    },
    created () {
        if (this.configData.sValue) {
            this.formData[this.configData.sProp] = this.configData.sValue
            // this.$emit('setDefualt', this.configData.sValue)
        }
        if (this.configData.sInputType === 'option' && !this.optionData[this.configData.sOptionProp]) {
            this.optionData[this.configData.sOptionProp] = [];
        }
    }
}
</script>

<style lang="scss" scoped>
.m-labelInput {
    label {
        user-select: none;
    }
}

.el-form-item {
    margin-bottom: 0px;
    margin-right: 0px;
}

.i-suffix-span {
    display: inline-block;
    height: 100%;
    padding: 0 10px;
    border: 1px solid #d2d2d2;
    border-left: 1px solid rgba(0, 0, 0, 0);
    vertical-align: top;
    white-space: nowrap;

    &.i-no-border {
        border: none;
        padding-left: 2px;
    }

    i {
        font-style: inherit;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        width: 100%;
        text-align: center;
    }
}

:deep(.el-select) {
    width: 100%;
    height: inherit;
    background: inherit;

    .el-input {
        height: inherit;
        font-size: inherit;
        color: inherit;

        input {
            height: inherit;
            font-size: inherit;
            color: inherit;
        }
    }
    &.i-set-padding {
        .el-input .el-input__inner {
            padding-left: 40px;
        }
    }
}

.m-labelInput {
    :deep(.el-input-number) {
        height: inherit;
        width: 100%;
        background: inherit;

        .el-input {
            height: inherit;
            font-size: inherit;
            color: inherit;

            input {
                height: inherit;
                font-size: inherit;
                color: inherit;
            }
        }

        .el-input-number__decrease {
            height: 50% !important;
            top: initial !important;
            line-height: initial !important;

            .el-icon-arrow-down {
                top: calc((100% - 11px) / 2);
                position: relative;
            }
        }

        .el-input-number__increase {
            height: 50% !important;
            line-height: 0px !important;

            .el-icon-arrow-up {
                top: calc((100% - 11px) / 2);
                position: relative;
            }
        }
    }

    :deep(.el-input) {
        // height: inherit;
        font-size: inherit;
        color: inherit;

        .el-input__inner {
            height: inherit;
            // background: inherit;
            // color: inherit;  // 打开会跟着标题颜色一样
        }
    }

    :deep(.el-cascader) {
        height: inherit;
        font-size: inherit;
        color: inherit;

        .el-input__inner {
            height: inherit;
            background: inherit;
            // color: inherit;  // 打开会跟着标题颜色一样
        }
    }

    :deep(.el-textarea) {
        height: inherit;
        font-size: inherit;

        .el-textarea__inner {
            height: inherit;
            background: inherit;
        }
    }

    :deep(.el-date-editor) {
        width: 100%;
        height: inherit;
        .el-icon-date,
        .el-icon-time {
            line-height: initial;
            padding-top: 6px;
        }
    }

    :deep(.el-autocomplete) {
        height: inherit;
        width: inherit;
    }

    :deep(.el-color-picker) {
        height: 100%;
        width: 100%;

        .el-color-picker__trigger {
            height: 100%;
            width: 100%;
        }
    }
}

.i-suffix-value {
    width: 80px;
    height: inherit;
    display: inline-block;

    :deep( input ) {
        border-left: 0px;
    }
}

</style>
