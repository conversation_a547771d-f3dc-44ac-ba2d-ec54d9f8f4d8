<template>
    <div class="m-flexLaout-ty pos-rea">
        <el-tabs ref="tabsRef" 
            v-model="activeName" 
            class="g-flexChild demo-tabs" 
            @tab-click="handleTabClick" 
            :style="styleVar">
            <el-tab-pane label="随访录入" name="visitForm" >
                <div class="item-tab">
                    <VisitForm ref="visitForm" @updateLoading="updateLoading" 
                        @updateRowData="updateRowData"
                        @onChangeActiveTab="onChangeActiveTab"></VisitForm>
                </div>
            </el-tab-pane>
            <el-tab-pane name="VisitedList" >
                <template v-slot:label>
                    <span>随访记录</span>
                    <span class="i-count">{{`(${followList.length})`}}</span>
                </template>
                <div class="item-tab">
                    <VisitedList :followList="followList" :isShowConfigBtn="configValue.isShowConfigBtn" @updateList="getFollowListData"></VisitedList>
                </div>
            </el-tab-pane>
            <el-tab-pane name="HistoryReport" 
                v-if="configValue.tabHistoryReport">
                <template v-slot:label>
                    <span>历史报告</span>
                    <span class="i-count">({{ historyRef?.tableData.length }})</span>
                </template>
                <div class="item-tab">
                    <HistoryReport ref="historyRef" :iModuleId="iModuleId" :historyRelatedCheck="configValue.historyReportMatchConditions"
                    :isShowConfigBtn="configValue.isShowConfigBtn" ></HistoryReport>
                </div>
            </el-tab-pane>
            <el-tab-pane name="Pathological"
                v-if="configValue.tabPathological">
                <template v-slot:label>
                    <span>病理报告</span>
                    <span class="i-count">({{ PathologicalNum }})</span>
                </template>
                <div class="item-tab">
                    <Pathological 
                        ref="Pathological"
                        @updateLoading="updateLoading"
                        @getPathologicalNum="getPathologicalNum"></Pathological>
                </div>
            </el-tab-pane>
            <!-- <el-tab-pane v-if="configValue.tabClinicData" name="ClinicData">
                <template #label>
                    <span>临床资料</span>
                    <span class="i-count">({{ thirdLinkCount }})</span>
                </template>
                <ThirdLinkList ref="thirdLinkListRef"></ThirdLinkList>
            </el-tab-pane> -->
        </el-tabs>

        <ThirdLinkList v-if="configValue.tabClinicData"  ref="thirdLinkListRef" 
            style="position: absolute; top: 11px; left: 325px; cursor: pointer;"
            :style="{left: thirdLinkDomLeft}"></ThirdLinkList>

        <div class="c-bottom">
            <div>
                <ApplyListInfo v-if="$auth['report:followup:applyOrder']"  :patientInfo="patientInfo" buttonName="电子申请单" type="primary" plain 
                    class="mr-2.5"></ApplyListInfo>
            </div>
            <div>
                <el-button @click="onResetForm">
                    <template #icon><el-icon><RefreshLeft /></el-icon></template>
                清空</el-button>
                <span v-auth="'report:followup:save'" style="margin-left: 10px">
                    <el-button-icon-fa
                        type="primary"
                        _icon="fa fa-save"
                        :loading="saveLoading"
                        :disabled="!patientInfo.sId"
                        @click="handleSaveData"
                        >保存</el-button-icon-fa>
                </span>
                
            </div>
        </div>
    </div>
   
</template>

<script>
    import { ArrowDown,Setting, ArrowUp, Refresh,RefreshLeft } from '@element-plus/icons-vue';
    import { ElMessage } from 'element-plus';
    import { getFollowList } from '$supersetApi/projects/apricot/followupVisits/index.js'

    import VisitForm from './VisitForm.vue';
    import VisitedList from './VisitedList.vue';
    import HistoryReport from '$supersetViews/apricot/consultMng/components/HistoryReport.vue';
    import Pathological from './Pathological.vue'
    import ThirdLinkList from '$supersetViews/apricot/components/ThirdLinkList.vue';
    import ApplyListInfo from '$supersetViews/apricot/components/ApplyListInfo.vue'    // 申请单

    export default {
        components:{
            ArrowDown,
            Setting,
            ArrowUp,
            RefreshLeft,
            VisitForm,
            VisitedList,
            HistoryReport,
            Pathological,
            Refresh,
            ThirdLinkList,
            ApplyListInfo
        },
        props:{
            configValue:{
                type: Object,
                default: {},
            }
        },
        inject:{
            patientInfo:{
                from:'patientInfo',
                default: () => ({})
            },
            iModuleId: {
                from:'iModuleId',
                default: '9'
            }
        },
        emits:[ 'updateRowData' ],
        setup(props) {
            var saveLoading = ref(false)
            var activeName = ref('visitForm')
            var followList = ref([])
            var PathologicalNum = ref(0)
            const historyRef = ref(null)
            return {
                saveLoading,
                activeName,
                followList,
                PathologicalNum,
                historyRef
            }
        },
        computed:{
           styleVar() {
                return {
                "--ativeTabColor": this.configValue.tabBgColor 
                }
            },
            // thirdLinkCount() {
            //     return this.$refs?.thirdLinkListRef?.linkList.length 
            // },
            thirdLinkDomLeft() {
                const tempValue = this.configValue;
                const tabList = [tempValue.tabHistoryReport, tempValue.tabPathological]
                let len = tabList.filter(item => item === true).length + 2;
                return (len * 107) + 'px';
            }
        },
        watch: {
            patientInfo: {
                handler(val) {
                    if(val.sId) {
                        this.getFollowListData()
                    }
                },
                deep: true
            }
        },
        methods:{
            handleTabClick() {

            },
            onChangeActiveTab() {
                this.activeName = 'visitForm'
            },
            handleSaveData() {
                this.saveLoading = true;
                if (this.activeName == 'visitForm') {
                    this.$refs.visitForm.saveVisiteData()
                    return
                }
                this.$refs.Pathological.saveData()
            },
            onResetForm() {
                if (this.activeName == 'visitForm') {
                    this.$refs.visitForm.resetFormNull()
                    return
                }
                this.$refs.Pathological.resetFormNull()
            },
            getPathologicalNum(val) {
                this.PathologicalNum = val
            },
            // getHistoryReportNum(val) {
            //     this.HistoryReportNum = val
            // },
            updateLoading() {
                this.saveLoading = false
            },
            updateRowData() {
                this.$emit('updateRowData')
            },
            getFollowListData () {
                const sId = this.patientInfo.sId
                if(!sId) {
                    return
                }
                getFollowList({
                    condition: {
                        sPatientId: sId
                    },
                    page: {
                        pageCurrent: 1,
                        pageSize: 999
                    }
                }).then(res => {
                    if (res.success) {
                        this.followList = res.data.recordList || [];
                        // let temp = this.followList.find(item => this.followRecordForm.sId == item.sId);
                        // if (temp) {
                        //     this.$refs.mainTable.setCurrentRow(temp)
                        // }
                        return
                    }
                    ElMessage.error(res.msg);
                }).catch(err => {
                    console.log(err)
                })
            },
        },
        created() {
            // this.getFollowListData()
        },
        mounted() {
          
        }
    }
</script>
<style lang="scss" scoped>
.pos-rea {
    position: relative;
    box-sizing: border-box;
}
.c-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 10px 5px 10px;
    border-top: 1px solid #ddd;
    box-sizing: border-box;
}

:deep(.el-tabs--card) {
    >.el-tabs__header .el-tabs__nav {
        background-color: var(--el-color-primary-light-9);
        border-color: #e0e4ed;
    }
    >.el-tabs__header .el-tabs__item {
        border-bottom: var(--el-border);
    }
    >.el-tabs__header .el-tabs__item.is-active {
        background-color: var(--ativeTabColor);
        border-color: var(--ativeTabColor);
        border-left: 1px solid var(--el-border-color-light)
    }
    >.el-tabs__header .el-tabs__item:first-child.is-active {
        border-left:none
    }
}
:deep(.el-tabs) {
    
    >.el-tabs__header {
        margin: 0 0 0 0px
    }
    >.el-tabs__content {
        .item-tab {
            padding: 10px;
            height: 100%;
            box-sizing: border-box;
            background-color: var(--ativeTabColor);
        }
        // padding: 10px;
    }
}
.setting {
    position: absolute;
    top: 4px;
    right: 10px;
    color: var(--el-color-primary);
    cursor: pointer;
}
.c-contain {
    padding: 10px 15px;
}
.c-item {
    h4 {
        margin: 0 0 8px 0;
        
    }
    h4.print-title {
        margin: 0;
    }
    .c-padding {
        padding: 5px 15px;    
    }
    .padding-lr {
        padding: 0 15px; 
    }
    .c-setting {
        font-weight: bold;
        span {
            color: #868080;
            font-size: 13px;
        }
        .sub-title {
            padding: 15px 0px;
            font-weight: bold;
        }
    }
}
.c-item.t-2 {
    padding-bottom: 20px;

}
.paddingTop {
    padding: 20px 10px 0 5px;
    display: flex;
    height: 100%;
    width: 100%;
    box-sizing: border-box;
}
.i-count {
    width: 18px;
}
</style>
