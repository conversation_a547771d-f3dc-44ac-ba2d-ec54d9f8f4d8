<template>
    <div>
        <!-- 鼠标右击 -->
        <div  class="cutMenu"
            ref="rightMenus"
            :style="{left:style.left + 'px',top:style.top + 'px', minHeight: style.minHeight + 'px'}">
            <!-- {{ rightClickRow }} -->
            <div class="name">{{rightClickRow.sName}}</div> 
            <div class="btns" v-for="(item,index) in showCalledBtn"
                :key="index">
                <el-button 
                    :type="item.isCalled?'success':'primary'"
                    plain
                    :loading="item.loading"
                    :class="item.isCalled?'isCalled':''"
                    _icon="fa fa-volume-up"
                    size="small"
                    @click.stop="handleQuickCall(item, index)">{{item.buttonName}}<i v-if="item.isCalled" class="el-icon-check green"></i></el-button>
            </div>
        </div>
        <Call v-model:dialogVisible="call_DialogVisible" 
            :patientInfo="rightClickRow" 
            :iModule="{iModuleId:iModule.moduleId, iModuleName:iModule.name}"
            @closeDialog="closeCallDialog"></Call>
    </div>
</template>
<script setup>
    import { deepClone } from '$supersetUtils/function'
    import { ElMessage } from 'element-plus';
    import { useStore } from 'vuex';    
    import Call from '$supersetViews/apricot/components/Call.vue' // 呼叫设置
    // import { getCallButtonSetOfModule } from '$supersetApi/projects/apricot/common/callSet.js' // 获取列表呼叫按钮
    import { callAction, isCalledBtns } from '$supersetApi/projects/apricot/common/call.js' // 呼叫接口
    import { getCalledBtn } from '../call.js'


    const props = defineProps({
        style:{
            type: Object,
            default: {
                left: 320,
                top: 0
            }
        },
        rightClickRow:{
           type: Object,
            default: {
               
            } 
        },
        iModule: {
            type: Object,
            default: null,
        },
        callBtnArray: {
            type: Array,
            default: ()=>{
                return []
            }
        }
    })
    const emits = defineEmits(['updateRowData', 'hideRightCalls',])
    //const props=defineProps(['style','show','rightClickRow'])
    // var callBtnArray = ref([]); // m模块呼叫按钮组

    // var calledBtnArr = ref([]) // 带已呼叫
    var call_DialogVisible = ref(false)
    var showCalledBtn = ref([])
    
    var modulesCallsBtn = computed({
        // getter
        get() {
            return props.callBtnArray
        }})

    watch(() => props.rightClickRow, (val,oldVal)=> {
        if(val.sId) {
            showCalledBtn.value = []
            getRowCalled()
        }
    });

  
    function handleQuickCall(data, index) {
        if(data.buttonCode == 0) {
            onOpenCall()
            return
        }
        const row = props.rightClickRow
        data.loading = true
        showCalledBtn.value[index] = data 
        let jsonData = {
            callBtnCode: data.buttonCode,
            captionsId: "",
            patientInfoId: row.sId,
            stationId: data.stationId,
            sysModuleCode: data.moduleId,
        }
        callAction(jsonData).then(res =>{
            data.loading = false
            showCalledBtn.value[index] = data  
            emits('updateRowData',row.sId, row.index)
            if(res.success)  {
                data.isCalled = true
                showCalledBtn.value[index] = data 
                ElMessage.success(res.msg)
                // this.updateTableInfo(row.sId,row.index)
                //callBtnArray[index] = data
                return
            }
            // this.getCalledBtn(patientInfoId)
            ElMessage.error(res.msg)
        }).catch((err)=>{
            data.loading = false
            showCalledBtn.value[index] = data
            console.log(err)
        })

    };
    async function getRowCalled() {
        const arr = modulesCallsBtn.value
        const id = props.rightClickRow.sId
        showCalledBtn.value = await getCalledBtn(id, arr)
    }
    // function getCalledBtn (patientInfoId) {
    //     const params = {
    //         patientInfoId: props.rightClickRow.sId
    //     }
    //     isCalledBtns(params).then(res => {
    //         if (res.success) {
    //             let arr = res.data
    //             modulesCallsBtn.value.forEach((item, index) => {
    //                 item.isCalled = false
    //                 if (arr.includes(item.buttonCode)) {
    //                     item.isCalled = true
    //                 }
    //                 showCalledBtn.value.push(item)
    //             })
    //             return
    //         }
    //         ElMessage.error(res.msg)
    //     }).catch(err => {
    //         console.log(err)
    //     })
    // };
    const onOpenCall =() =>{
        call_DialogVisible.value = true
        emits('hideRightCalls')
    }
     // 关闭呼叫  
    const closeCallDialog = () => {
        call_DialogVisible.value = false
    };
    // const updatePatientInfo = ()=>{
    //     emits('updateTableInfo',row.sId, row.index)
    // }
</script>
<style lang="scss" scoped>
.cutMenu {
    padding: 10px 10px;
    border-radius: 4px;
    border: 1px solid #dcdcdc;
    // box-shadow: 0 2px 12px rgba(0 0 0, 0.1);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    z-index: 4000;
    position: fixed;
    box-sizing: border-box;
    width: 140px;
    display: -webkit-box;
    display: -ms-flexbox;
    background: #fbf5e9;
    top: 50%;
    left: 350px;
    min-height:200px;
    display: block;
    text-align: left;
    .btns {
        margin-bottom: 10px;
        .el-button {
            width: 100%;
        }
    }
    .name {
        padding-bottom: 10px;
        margin-bottom: 10px;
        color: #333;
        font-size: 16px;
        font-weight: 600;
        // line-height: 2;
        border-bottom: 2px dotted #aeaeae;
    }
    .icon {
        position: absolute;
        top: 18px;
        left: -6px;
        width: 0;
        height: 0;
        border-top: 6px solid transparent;
        border-right: 12px solid #fbf5e9;
        border-bottom: 6px solid transparent;
    }
}

</style>