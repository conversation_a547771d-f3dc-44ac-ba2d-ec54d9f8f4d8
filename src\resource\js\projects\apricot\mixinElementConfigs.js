import { getElementConfigBykeyword } from '$supersetApi/projects/apricot/appointment/index.js'
import store from '$supersetStore'
import { createNamespacedHelpers } from 'vuex'
const { mapGetters } = createNamespacedHelpers('user')


const mixinElementConfigs = {
    data() {
        return {
            configLoad: false
        }
    },
    computed: {
        ...mapGetters([
            'elementConfigData',
        ]),
        // // 容器配置数据对象
        // elementConfigData(){
        //     return store.state.user.elementConfigData;
        // }
    },
    methods: {
        // 排序及 null 值的时候赋初值
        // data 容器标识 []
        configDataResize(data, assign = {}) {
            for (let key in data) {
                
                let item = data[key]
                // 某个容器没有值的时候，赋初值
                if (!item){
                    data[key] = assign[key]
                    continue;
                }
                // 排序
                item.sort(function (a, b) {
                    return a.iIndex - b.iIndex
                })
                // 重置sFixed属性值为null
                item.map(_ => {
                    if(_.sFixed === '') {
                        _.sFixed = null
                    }
                })
                data[key] = item;
            }
            return data
        },
        // 主要设置 container 为 null 
        // container 容器标识 []
        configDataDefault(container, assign){
            let data ={}
            container.map(item => {
                data[item] = null
            })
            this.$store.commit('user/setElementConfigData', {
                elementConfigData: this.configDataResize(data, assign)
            });
        },
        // 获取配置 (弃用，全部取固定值
        mxGetElementConfigBykeyword(params, assign = {}){
            if (!params){
                return;
            }
            // console.log(6666,params, assign)
            // // 遍历该标签是否都有
            // const status = params.containerLabels.some((label) => {
            //     return store.state.user.elementConfigData[label]
            // })
            // // store 都有结束
            // if (status) return; 

            // 过滤状态管理器存在的配置数据
            // const temp = params.containerLabels.filter((label) => {
            //     return !store.state.user.elementConfigData[label]
            // });
            // // store 都有结束
            // if (!temp.length) return;

            this.configDataDefault(params.containerLabels, assign);

            // this.configLoad = true;
            // 请求获取配置
            // await getElementConfigBykeyword({
            //     // 'sContainerLabels': params.containerLabels,
            //     'sContainerLabels': temp,
            //     'sNodeLabel': params.nodeLabel,
            //     'sSystemLabel': params.systemLabel
            // }).then((res) => {
            //     if (res.success) {
            //         // 把后端返回的配置，存入到 store 中
            //         this.$store.commit('user/setElementConfigData', {
            //             elementConfigData: this.configDataResize(res.data, assign)
            //         });
            //         this.$nextTick(() => {
            //             this.configLoad = false;
            //         })
            //         return
            //     }
            //     this.configDataDefault(params.containerLabels, assign);
            // }).catch(err => {
            //     this.configDataDefault(params.containerLabels, assign);
            //     this.configLoad = false;
            // })
        }
    },
}
export default mixinElementConfigs;
