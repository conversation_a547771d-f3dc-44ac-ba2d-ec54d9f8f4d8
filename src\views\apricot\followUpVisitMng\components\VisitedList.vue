<template>
    <div class="c-content">
        <el-table-extend ref="mainTable"
            :data="followList"
            :configBtn="isShowConfigBtn"
            :iModuleId="iModuleId"
            storageKey="visitedTable"
            highlight-current-row
            height="100%">
            <el-table-column type="index"
                label="序号"
                prop="_index"
                align="center"
                width="60">
                <template v-slot="scope">
                    <span>{{ scope.$index + 1 }}</span>
                </template>
            </el-table-column>
            <!-- <el-table-column 
                label="操作"
                align="center"
                width="100px">
                <template v-slot="scope">
                    <el-button title="编辑" @click="onEdit(scope.row)" type="primary" plain size="small">
                            编辑
                            <template #icon>
                                <Icon name="el-icon-edit" color="">
                                </Icon>
                            </template>
                        </el-button>
                    <i class="el-icon-delete i-delete"
                        type="danger"
                        @click.stop="onClickDelete(scope.row, scope.$index)"></i>
                </template>
            </el-table-column> -->
            <template v-for="(item,index) in configList"
                :key="index">
                <el-table-column :prop="item.sProp"
                    :label="item.sLabel"
                    :fixed="item.sFixed"
                    :align="item.sAlign"
                    :width="item.sWidth"
                    :show-overflow-tooltip="true"
                    :min-width="item.sMinWidth">
                    <template v-slot="scope">
                        <template v-if="item.sProp==='dFollowDate'">
                            {{ transformDate(scope.row.dFollowDate) }}
                        </template>
                        <template v-else>
                            {{ scope.row[item.sProp]}}
                        </template>
                        <template v-if="item.sProp === 'Actions'">
                            <el-button v-auth="'report:followup:edit'"
                                title="编辑"
                                @click="onEdit(scope.row)"
                                type="primary"
                                link
                                size="small">
                                编辑
                                <template #icon>
                                    <Icon name="el-icon-edit"
                                        color="">
                                    </Icon>
                                </template>
                            </el-button>
                            <el-button v-auth="'report:followup:delete'"
                                title="删除"
                                @click="onClickDelete(scope.row, scope.$index)"
                                type="default"
                                link
                                size="small">
                                删除
                                <template #icon>
                                    <Icon name="el-icon-delete"
                                        color="">
                                    </Icon>
                                </template>
                            </el-button>
                        </template>
                    </template>
                </el-table-column>
            </template>
        </el-table-extend>
    </div>
</template>
<script setup>
import { getEventbus } from '@/utils'
import { ElMessage, ElMessageBox } from 'element-plus';
import { transformDate } from '$supersetResource/js/tools'
import { delFollowupRecord, getFollowList } from '$supersetApi/projects/apricot/followupVisits/index.js'

const $eventbus = getEventbus()
const { followList, isShowConfigBtn } = defineProps({
    followList: {
        type: Array,
        default: [],
    },
    isShowConfigBtn: {
        type: Boolean,
        default: true
    }
})
const iModuleId = inject('iModuleId', null)
const configList = ref(
    [{
        sProp: 'dFollowDate',
        sLabel: '随访日期',

    }, {
        sProp: 'sDoctorName',
        sLabel: '随访医生',
    }, {
        sProp: 'sFollowModeText',
        sLabel: '随访方式',
        sAlign: 'center'
    }, {
        sProp: 'sVisitStateText',
        sLabel: '随访状态',
        sAlign: 'center'
    },
    {
        sProp: 'sIsAccordText',
        sLabel: '随访符合',
        sAlign: 'center'
    },
    {
        sProp: 'Actions',
        sLabel: '操作',
        sWidth: '140px',
        sAlign: 'center'
    }]
)
const tableData = computed(() => {
    return followList
})
const emits = defineEmits(['updateList'])
const onClickDelete = (row, index) => {
    ElMessageBox.confirm('您确定要删除该行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        let jsonData = {
            iVersion: row.iVersion,
            sId: row.sId
        }
        delFollowupRecord(jsonData).then(res => {
            let { success, msg } = res;
            if (success) {
                ElMessage.success(msg);
                emits('updateList');
                $eventbus.emit('visitedFormInit', row);
                return
            }
            ElMessage.error(msg);
        }).catch(err => {
            console.log(err)
        })
    }).catch(() => { });
}
function onEdit (row) {
    // 兄弟组件 发布信息修改
    $eventbus.emit('visitedInfoEdit', row);
};
onMounted(() => {
})
</script>
<style scoped lang="scss">
.c-content {
    width: 100%;
}
</style>