import { getHospitalData, getMachineRoomData, getItemData, getAllUsers, queryUserListByUserType,
    queryAllReportDoctor } from '$supersetApi/projects/apricot/common/baseData.js'
import { ElMessage } from 'element-plus'
import store from '@/store';

// 获取院区数据
export async function useGetHospitalData(arrKey='districtArrOption') {
    // arrKey  数组名称
    await getHospitalData()
        .then((res) => {
            if (res.success) {
                this.optionsLoc[arrKey] = res?.data || [];
                this.optionsLoc[arrKey].length && this.optionsLoc[arrKey].map(item => {
                    item.sName = item.sHospitalDistrictName;
                    item.sValue = item.sId
                })
                return;
            }
            this.$message.error(res.msg);
        })
        .catch((err) => {
            console.log(err);
        });
}

// 获取机房数据
export async function useGetMachineRoomData(sDistrictId = '', arrKey='machineRoomArrOption') {
    // arrKey  数组名称
    let params = {
        sDistrictId: sDistrictId,
        iIsEnable: 1,
    }
    !params.sDistrictId && delete params.sDistrictId;
    await getMachineRoomData(params)
        .then((res) => {
            if (res.success) {
                this.optionsLoc[arrKey] = res?.data|| [];
                this.optionsLoc[arrKey].length && this.optionsLoc[arrKey].map(item => {
                    item.sName = item.sRoomName;
                    item.sValue = item.sId
                })
                return;
            }
            this.$message.error(res.msg);
        })
        .catch((err) => {
            console.log(err);
        });
}
// 获取项目
export async function useGetItemData(sDeviceTypeId = '', arrKey='itemsArrOption') {
    // arrKey  数组名称
    // sDeviceTypeId    设备类型Id
    let params = {
        sDeviceTypeId: sDeviceTypeId,
        iIsEnable: 1,
    }
    await getItemData(params)
        .then((res) => {
            if (res.success) {
                this.optionsLoc[arrKey] = res?.data || [];
                this.optionsLoc[arrKey].length && this.optionsLoc[arrKey].map(item => {
                    item.sName = item.sItemName;
                    item.sValue = item.sId
                })
                return;
            }
            this.$message.error(res.msg);
        })
        .catch((err) => {
            console.log(err);
        });
}

// 改变院区查询条件
export async function useChangeHospital() {
    await this.useGetMachineRoomData(this.condition.sDistrictId);
    if (this.condition.sMachineryRoomId) {
        this.condition.sMachineryRoomId = ''
        await this.useGetItemData()
    }
    this.mxDoSearch();
}

// 改变机房查询条件
export async function useChangeMachineRoom(val) {
    var target = this.optionsLoc.machineRoomArrOption.find(element => element.sId === val)
    if(target || !val.includes(',')) {
        // 机房单选
        await this.useGetItemData(target?.sDeviceTypeId);
        let filterLen = this.optionsLoc.itemsArrOption.filter(item => this.condition.sProjectId === item.sId).length
        if(!this.optionsLoc.itemsArrOption.length || !filterLen) {
            this.condition.sProjectId = ''
        }
    } 
    if(val.includes(',')) {
        // 机房多选
        await this.useGetItemData();
        let sDeviceTypeIds = this.optionsLoc.machineRoomArrOption.filter(element => val.includes(element.sId)).map(item => item.sDeviceTypeId);
        this.optionsLoc.itemsArrOption = this.optionsLoc.itemsArrOption.filter(item => sDeviceTypeIds.includes(item.sDeviceTypeId));
        let filterLen = this.optionsLoc.itemsArrOption.filter(item => this.condition.sProjectId === item.sId).length
        if(!this.optionsLoc.itemsArrOption.length || !filterLen) {
            this.condition.sProjectId = ''
        }
    }
    this.mxDoSearch();
}

// 获取全部用户数据
export async function getAllUsersData() {
    let allUsersOptions = []
    const res = await getAllUsers()
    if (!res) {
        ElMessage.error(res.msg)
    }
    if (res && res.success) {
        const data = res.data ? res.data : []
        data.map( item=>{
            item.sName = item.userName
            item.sValue = item.userId
        })
        allUsersOptions = data
    }

    return allUsersOptions
}

// 按用户类型查询用户
export async function queryUserListByType(userType) {
    const userInfo = store.getters["user/userSystemInfo"] || {};
    let list = []
    const res = await queryUserListByUserType({userType})
    if (!res) {
        ElMessage.error(res.msg)
    }
    if (res && res.success) {
        let data = res.data ? res.data : [];
        const targetItem = data.find(item => item.userId === userInfo.userId);
        if(!targetItem) {
            let tempItem = {
                ...userInfo,
                iIsAuxiliary: 1,
            }
            data.push(tempItem);
        }
        data.map(item => {
            item.sName = item.userName
            item.sValue = item.userId
        })
        list = data
    }
    return list
}




// 获取报告医生
export async function getReportAboveDrData() {
    // reportDoctors = []//报告医生 
    // auditDoctors = [] // 审核医生
    // practiceDoctors = [] 	/// 实习医生
    // recheckDoctors = []  // 复审医生
    let doctors = {}
    const res = await queryAllReportDoctor()
    if(!res) {
        ElMessage.error(res.msg)
    }
    if (res && res.success) {
        var data = res.data ? res.data : []
        for (const key in data){
            if (data[key] && Array.isArray(data[key])) {
                data[key].map(item =>{
                    item.sName = item.userName
                    item.sValue = item.userId
                })
            } else { data[key] = [] }
        }
        doctors = data
    }
    return doctors
}