
<template>
    <el-dialog v-model="visible" destroy-on-close append-to-body class="t-default my-dialog" width="50%"
        :close-on-click-modal="false" @close="handleCloseDialog">
        <template #header>
            <div class="flex-div ">
                <div class="dialog-title">呼叫：{{ sPatientName }}</div>
                <div class="c-screen">
                    <div class="c-title">呼叫屏: </div>
                    <el-checkbox-group v-model="checkScreens" :min="1">
                        <el-checkbox :disabled="!screen.iIsConnected" v-for="(screen, index) in screenArray"
                            :label="screen.sIp" :key="index" :class="screen.iIsConnected === 1 ? '' : 'iNotConStyle'">
                            <span>{{ screen.sName }} {{ screen.iIsConnected === 1 ? '（在线）' : '（离线）' }}</span>
                        </el-checkbox>
                    </el-checkbox-group>
                </div>
            </div>
        </template>
        <div class="c-context">
            <el-table v-loading="loading" id="able" border highlight-current-row :data="tableData" height="50vh">
                <el-table-column label="序号" type="index" width="60px" align="center">
                </el-table-column>
                <!-- <el-table-column label="序号" width="80px" align="center"
                    :cell-style="{'background-color': 'yellow',color: 'grey'}">
                    <template v-slot="scope">
                        <div :class="scope.row.isCalled?'isCalled':'isDefault'">{{scope.row.index}}
                        </div>
                    </template>
                </el-table-column> -->
                <el-table-column label="语音分类" align="center" width="100px">
                    <template v-slot="scope">
                        <div :class="scope.row.isCalled ? 'isCalled' : 'isDefault'">
                            {{ scope.row.sCaptionsTypeName }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="语音内容">
                    <template v-slot="scope">
                        <div :class="scope.row.isCalled ? 'isCalled' : 'isDefault'">
                            {{ scope.row.sCaptions }}
                            <span class="isCall"><i v-if="scope.row.isCalled" class="el-icon-check"></i></span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="90">
                    <template v-slot="scope">
                        <el-button type="primary" link size="small" title="呼叫"
                            @click="hanldCall(scope.row, scope.$index)">
                            <svg t="1616996759696" width="22" height="16" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="2484">
                                <path
                                    d="M541.2 132.3c-7.2-3.3-15.2-5-23.3-5-13.9 0-26.8 5.1-36.5 14.4L280.6 312.4h-158c-11.5 0-23.9 3.9-33.6 10.4h-4l-8.7 9.2c-7.5 7.9-12.2 20.8-12.2 33.8v290.7c0 15 6.7 31.4 16.9 41.6 10.2 10.3 26.6 16.9 41.6 16.9h158.2l200.7 165.6c8.4 7.9 23.3 16 35.6 16 5.6 0 16.5 0 27.1-8 10-4.2 17.8-10.4 23.1-18.6 5.7-8.6 8.4-19.1 8.4-32V184c0-12.9-2.8-23.4-8.4-32-5.8-9-14.6-15.6-26.1-19.7z m-24.8 57.4v642.9L310.2 662.5l-8.2-6.8v0.1H123.3V371.7h179l214.1-182zM899.3 300c-20.9-32.7-46.5-61.1-75.9-84.2l-0.1-0.1c-1.3-1.1-2.2-1.8-2.9-2.4-4.5-2.8-9.6-4.3-15.1-4.3-10.3 0-21.6 5.3-31.1 14.5l-0.1 0.1c-8.4 13.5-3.9 35.9 9 45.2l0.1 0.1c6.1 2.5 14.7 9.7 20.8 15.4 9.4 8.7 23.6 23.7 38.1 45.6 24.3 36.8 53.3 99.7 53.3 190.8 0 91-27.4 153.9-50.3 190.6-13.7 22-27.2 36.9-36.1 45.6-8.6 8.3-15.3 13.5-20.1 15.5l-0.1 0.1c-13.9 9.8-17.2 28.6-8.1 46.7 3.8 7.7 17.2 12.9 27.3 12.9 3.8 0 13.9 0 17.4-3.5 0.7-0.7 1.5-1.4 3.3-2.8 28.1-22.9 52.6-51 72.7-83.8 38.8-63.2 58.5-137.6 58.5-221.4 0-83.4-20.4-157.6-60.6-220.6z"
                                    fill="var(--el-color-primary)" p-id="2485"></path>
                                <path
                                    d="M752.7 376.7c-23.8-27.4-48.4-40.2-53.7-42.1h-1.6l-1.9-1.3c-3.2-2.2-7.4-3.3-12-3.3-11.9 0-24.9 7.6-27.8 16.4l-0.3 1-0.6 0.9c-3.8 5.8-4.3 14.2-1.4 22.7 2.9 8.4 8.6 15.1 14.6 17.1l0.6 0.2 0.6 0.3c0.7 0.4 17.2 9.5 33.6 29.7 15.1 18.5 33.1 50.3 33.1 96.7 0 96.6-54.4 128.5-60.7 131.9-13.8 9.4-23 27.9-14.6 40.4l0.3 0.4 0.2 0.5c4.3 8.7 18.9 18.6 27.3 18.6 5.5 0 8.4-0.1 11.7-3.8l2.2-2.2H704.7c6.2-2.3 28.7-15.6 50.6-44.2 20.6-26.9 45.1-74.4 45.1-147.4 0-64.2-25.9-107.5-47.7-132.5z"
                                    fill="var(--el-color-primary)" p-id="2486"></path>
                            </svg>
                            <span>呼叫</span>
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <template #footer>
            <el-button-icon-fa icon="fa fa-close-1" @click="$emit('update:dialogVisible', false)">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import Api from '$supersetApi/projects/apricot/common/call.js'
import { getOptionName } from '$supersetResource/js/tools'
import { callTypeOptions } from '$supersetResource/js/projects/apricot/enum.js'

export default {
    props: {
        patientInfo: {
            type: Object,
            default: () => ({})
        },
        dialogVisible: {
            type: Boolean,
            default: false
        },
        iModule: {
            type: Object,
            default: () => ({})
        }
    },
    emits: ['closeDialog', 'update:dialogVisible'],
    data () {
        return {
            loading: false,
            tableData: [],
            checkScreens: [],
            checked: false,
            screenArray: [],
            visible: false
        }
    },
    computed: {
        sPatientName () {
            return this.patientInfo.sName
        },
        stationId () {
            return this.$store.getters['user/workStation'].stationId
        }
    },
    watch: {
        async dialogVisible (val) {
            this.visible = val;
            if (val) {
                await this.getTableData()
                this.getCalledList()
                this.getScreenList()
            }
        }

    },
    methods: {
        onClose() {
            this.$emit('update:dialogVisible', false)
        },
        setsCaptionsTypeText (val) {
            let target = captionsTypeOptions.find(item => item.sValue == val)
            return target ? target.sName : ''
        },
        //  sStationId: this.$store.getters['user/workStation'].stationId,
        //  sPatientId: this.patientInfo.sId,
        // 获取播放列表
        async getTableData () {
            this.loading = true
            let params = {
                stationId: this.stationId,
                iIsEnable: 1,
                // iCaptionsType: 1
            }
            await Api.getCallCaptionsData(params).then(res => {
                this.loading = false
                if (res.success) {
                    this.tableData = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
                console.log(err)

            })
        },

        // 获取已呼叫列表 
        getCalledList () {
            // 获取呼叫记录
            let jsonData = {
                patientInfoId: this.patientInfo.sId,
                stationId: this.stationId,
            }
            Api.getCalledData(jsonData).then(res => {
                if (res.success) {
                    let data = res.data || [];
                    let tempStr = '';
                    data.map(item => {
                        if (item) {
                            tempStr = tempStr ? tempStr + ',' + item : item
                        }
                    })
                    this.tableData.map((item, index) => {
                        if (tempStr.includes(item.sId)) {
                            this.tableData[index]['isCalled'] = 1
                        }
                    })
                }
            }).catch(() => {
                console.log(err)
            })

        },
        // 获取屏幕信息
        getScreenList () {
            let params = {
                stationId: this.stationId,
            }
            Api.getScreen(params).then(res => {
                if (res.success) {
                    let data = res.data || [];
                    this.screenArray = data.map(item => {
                        return {
                            sName: item.sScreenName,
                            sIp: item.sIp,
                            iIsConnected: item.iIsConnected
                        }
                    });
                    this.checkScreens = this.screenArray.map(item => item.sIp);
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });

            }).catch(() => {
                console.log(err)
            })
        },

        // 关闭弹窗
        handleCloseDialog () {
            this.$emit('closeDialog');
            // this.activeCarouselItem = 'carouselItem1';
            // this.$refs.carousel1 && this.$refs.carousel1.setActiveItem(this.activeCarouselItem);
            // this.isSpellShow = false; 
            // this.patientNameHistoryData = [];
        },
        hanldCall (row, index) {
            if (!row.sCaptions) {
                this.$message.warning('呼叫内容不能为空！');
                return
            }
            let jsonData = {
                stationId: row.sStationId,
                sysModuleCode: this.iModule.iModuleId,
                patientInfoId: this.patientInfo.sId,
                callBtnCode: row.iCaptionsType,
                captionsId: row.sId
                // sPatientId: this.patientInfo.sId,
                // sScreenIps: this.checkScreens.join(','),
                // sState: getOptionName('ApricotReportMachine', callTypeOptions),
                // sStationId: this.$store.getters['user/workStation'].stationIds
            }
            let loading = this.$loading({
                lock: true,
                text: '正在加载中，请稍等',
                background: 'rgba(0, 0, 0, 0.1)'
            });
            Api.callAction(jsonData).then(res => {
                loading.close();
                if (res.success) {
                    this.$message.success(res.msg)
                    this.tableData[index]['isCalled'] = 1;
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
                this.tableData[index]['isCalled'] = 0;
            }).catch((err) => {
                loading.close();
                console.log(err)
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.flex-div {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .dialog-title {
        font-size: 16px;
        color: var(--el-color-primary);
    }
}
.c-screen {
    text-align: left;
    margin-right: 15px;

    .c-title {
        display: inline-block;
        margin-right: 15px;
        font-size: 16px;
        position: relative;
        top: -5px;
    }

    .el-checkbox-group {
        display: inline-block;
    }
}
.screen-items {
    padding: 0 10px;
}

.isCalled {
    color: #67C23A;
}

.isCall {
    margin-left: 5px;
    font-size: 16px;
    font-weight: 600;
}

.el-checkbox {
    margin-right: 15px;
}
</style>
