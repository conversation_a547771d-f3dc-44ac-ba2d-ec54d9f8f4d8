<template>
    <div class="c-template">
        <DrawerAside v-if="activeKey" :asideWidth="220" :spillage="22">
            <template v-slot:aside>
                <Menu :default-active="menus.findIndex(i => i.key === activeKey)" class="el-menu-vertical" :menuList="menus"
                    @select="handleSelect" ></Menu>
            </template>
            <template v-slot:content>
                <component :is="activeKey"></component>
            </template>

        </DrawerAside>
        
        <div class="flex w-full h-full items-center justify-center">
           <div class="text-xl">无访问权限，请检查您的用户权限</div> 
        </div>
    </div>
</template>

<script>
// 模块辅助样式
import { defineAsyncComponent } from 'vue'

import CallManagement from '$supersetViews/apricot/system/views/CallManagement.vue'
export default {
    name: 'apricot_System',
    components: {
        // PageConfig,
        CallManagement,
        BpmSet: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/BpmSet.vue')),
        TemplateSet: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/printManagement.vue')),
        ThirdLink: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/ThirdLink.vue')),
        // UserSet: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/UserSet.vue')),
        // SystemLog: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/SystemLog.vue')),
        // ExportTemplate: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/ExportTemplate.vue')),
        ProjectSet: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/ProjectSet.vue')),
        ClientManagement: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/clientManagement.vue')),
        // DepartmentDictionary: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/DepartmentDictionary.vue')),
        // UserDictionary: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/UserDictionary.vue')),
        DictionarySet: defineAsyncComponent(() => import('$supersetViews/apricot/system/views/DictionarySet.vue')),
    },
    data () {
        return {
            menus: [
                {
                    title: '基础数据',
                    key: 'ProjectSet',
                    icon: 'el-icon-s-operation',
                    buttonCode: 'report:system:baseData'
                },
                {
                    title: '系统参数',
                    key: 'BpmSet',
                    icon: 'el-icon-set-up',
                    buttonCode: 'report:system:systemParameter'
                },
                {
                    title: '打印管理',
                    key: 'TemplateSet',
                    icon: 'el-icon-printer',
                    buttonCode: 'report:system:printMng'
                },
                {
                    title: '呼叫管理',
                    key: 'CallManagement',
                    icon: 'el-icon-connection',
                    buttonCode: 'report:system:callMng'
                },

                {
                    title: '资源管理',
                    key: 'ThirdLink',
                    icon: 'el-icon-tickets',
                    buttonCode: 'report:system:resourceMng'
                },
                // {
                //     title: '导出管理',
                //     key: 'ExportTemplate',
                //     icon: 'el-icon-download',
                //     buttonCode: 'report:system:exportMng'
                // },
                {
                    title: '客户端管理',
                    key: 'ClientManagement',
                    icon: 'el-icon-monitor',
                    buttonCode: 'report:system:clientMng'
                },
                {
                    title: '字典管理',
                    key: 'DictionarySet',
                    icon: 'el-icon-collection',
                    buttonCode: 'report:system:dictMng'
                },

           
            ],
            activeKey: '',
        }
    },
    methods: {
        handleSelect (item) {
            this.activeKey = item.key
        }
    },
    created () {
        setTimeout(() => {
            const target = this.menus.filter(item => this.$auth[item.buttonCode]);
            if (target[0]) {
              this.activeKey = target[0].key
            }
        }, 0);
    }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->

<style lang="scss" scoped>
.el-menu-vertical {
    background: transparent;
}

:deep(.el-menu-vertical .el-menu-item i) {
    margin-right: 0;
}
</style>
