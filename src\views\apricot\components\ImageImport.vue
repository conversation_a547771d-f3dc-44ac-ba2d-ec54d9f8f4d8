<template>
    <el-dialog
        append-to-body
        title="图像导入"
        v-model="innerVisible"
        :close-on-click-modal="false"
        @open="openDialog"
        destroy-on-close
        fullscreen
        class="my-dialog t-default my-full-dialog import-image-dialog">
        <div class="header">
            <div class="left">
                <el-checkbox-group v-model="condition.fileTypes" @change="onChangeCondition" class="check-group">
                    <el-checkbox v-for="item in suffixs" :value="item.value" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
                    <el-date-picker
                    v-model="condition.lastModifierDate"
                    type="date"
                    placeholder="筛选日期"
                    format="YYYY 年 MM 月 DD 日"
                    style="width: 170px; margin-left: 88px;"
                    @change="onChangeCondition"
                    value-format="YYYY-MM-DD">
                    </el-date-picker>
                </el-checkbox-group>

                <div style="display: flex;" class="check-group">
                    <el-checkbox  @change="onChangeCondition" v-for="item in matching" v-model="condition[item.value]" :key="item.value">{{ item.label }}</el-checkbox>
                    <el-checkbox class="check-box" v-model="autoClose" @change="onChangeCheckBox(true)">添加后自动关闭</el-checkbox>
                </div>
            </div>
            <div class="right">
                <div class="upload-url">
                    <ComputerDirectory @changeDirectory="changeDirectory">
                        <template v-slot="slotProps">
                            <el-input v-model="condition.scanDir" @blur="onChangeCondition" placeholder="文件夹路径" style="width: calc(100% - 61px);"/>
                            <div class="upload-url-button" @click="slotProps.openDialog">选择</div>
                        </template>
                    </ComputerDirectory>

                </div>
                <!-- <div class="upload-action" @click="onClickAdd"><el-icon><Loading /></el-icon>添加</div> -->
            </div>
        </div>
        <div class="container">
            <div class="left">
                <el-table ref="multipleTable" :data="dataList"
                    @row-click="handleRowClick"
                    @selection-change="handleSelectionChange"
                    border
                    height="100%"
                    highlight-current-row>
                    <el-table-column
                    type="selection"
                    align="center"
                    width="50">
                    </el-table-column>
                    <el-table-column
                        prop="fileName" show-overflow-tooltip
                        label="文件名">
                    </el-table-column> 
                    <el-table-column
                        prop="lastModifierDate" show-overflow-tooltip width="134"
                        label="最后修改时间">
                        <template #default="scope">
                            {{ setTime2yyyyMMDD(scope.row.lastModifierDate) }}
                        </template>
                    </el-table-column> 
                </el-table>
            </div>
            <div class="right">
                <ul class="plane-box">
                    <li>
                        <span>影像号：</span>
                        <span>{{ patientInfo.sNuclearNum }}</span>
                    </li>
                    <li>
                        <span>检查日期：</span>
                        <span>{{setTime2yyyyMMDDHHmm( patientInfo.dAppointmentTime)}}</span>
                    </li>
                    <li>
                        <span>姓名：</span>
                        <span>{{ patientInfo.sName }}</span>
                    </li>
                    <li>
                        <span>拼音：</span>
                        <span>{{ patientInfo.sNameSpell }}</span>
                    </li>
                    <li>
                        <span>性别：</span>
                        <span>{{ patientInfo.sSexText }}</span>
                    </li>
                    <li>
                        <span>年龄：</span>
                        <span>{{ patientInfo.sAge }}</span>
                    </li>
                </ul>
                <div class="plane-image">
                    <embed v-if="showFile.fileType === 'pdf'" :src="showFile.base64" type="application/pdf" width="100%" height="100%" />
                    <el-image v-else
                    v-show="showFile.base64"
                    style="width: 100%; height: 100%"
                    :src="showFile.base64"
                    :preview-src-list="[showFile.base64]"
                    fit="contain">
                    <div slot="error" class="image-slot">
                        <span>pdf 不支持预览</span>
                    </div>
                    </el-image>
                </div>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa
            :loading="loading" 
            class="btn-item"    
            type="primary" 
            icon="fa fa-save"
            @click="onClickAdd">导入勾选数据</el-button-icon-fa>

            <el-button-icon-fa plain icon="fa fa-close-1"
                @click="innerVisible = false">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import ComputerDirectory from './ComputerDirectory.vue'
import { transformDate } from '$supersetUtils/function.js'
import { importFile, getLocalFileBase64, getFilterFile } from '$supersetApi/projects/apricot/case/report.js'
export default {
    name: 'ImageImport',
    emits: ['update:modelValue'],
    components: {
        ComputerDirectory
    },
    props: {
        modelValue: {
            type: Boolean,
            default: () => {
                return false;
            }
        },
        patientInfo: {
            type: Object,
            default: ({})
        }
    },
    computed: {
        innerVisible: {
            get: function() {
                return this.modelValue;
            },
            set: function(val) {
                this.$emit('update:modelValue', val);
            }
        },
    },
    data() {
        return {
            condition: {
                fileTypes: ['bmp', 'jpg', 'png', 'pdf'],
                sName: true,
                sNameSpell: true,
                sNuclearNum: true,
                maxFileNumber: 100,
                lastModifierDate: '',
            },
            suffixs: [ 
                { value: 'bmp', label: '*.bmp' }, 
                { value: 'jpg', label: '*.jpg' }, 
                { value: 'png', label: '*.png' },
                { value: 'pdf', label: '*.pdf' },
                // { value: '*', label: '*.*' },
            ],
            matching: [
                { value: 'sName', label: '姓名匹配' }, 
                { value: 'sNameSpell', label: '拼音匹配' }, 
                { value: 'sNuclearNum', label: '影像号匹配' },
            ],
            autoClose: true,
            dataList: [],
            storageKey: 'configs-imageImport-20230926',
            multipleSelection: [],
            loading: false,
            showFile: {
                fileType: '',
                base64: ''
            },
            uploadState: {
                count: 0,
                step: 0,
                success: 0,
                err: 0,
                errMsg: ''
            },
        }
    },
    methods: {
        openDialog() {
            this.onChangeCondition()
        },
        setTime2yyyyMMDDHHmm (val) {
            return transformDate(val, false, 'yyyy-MM-dd HH:mm')
        },
        setTime2yyyyMMDD (val) {
            return transformDate(val, false, 'yyyy年MM月dd日')
        },
        selectAll() {
            this.$nextTick(() => {
                this.$refs['multipleTable'].toggleAllSelection()
            })
        },
        onChangeCondition() {
            if (!this.condition.scanDir) {
                return
            }
            this.$nextTick(() => {
                const params = Object.assign({}, this.condition)

                if (params.sName) {
                    params.patientName = this.patientInfo.sName
                }
                if (params.sNameSpell) {
                    params.patientNameSpell = this.patientInfo.sNameSpell
                }
                if (params.sNuclearNum) {
                    params.imageNo = this.patientInfo.sNuclearNum
                }

                getFilterFile(params).then(res => {
                    if (res.success) {
                        res.data.map(item => {
                            const idx = item.fileName.lastIndexOf('.')
                            const fileType = item.fileName.substr(idx + 1).toLocaleLowerCase()
                            const suffix = item.fileName.substr(idx).toLocaleLowerCase()
                            const name  = item.fileName.substr(0, idx)

                            item.name = name
                            item.suffix = suffix
                            item.fileType = fileType
                        })
                        this.dataList = res.data
                        this.onChangeCheckBox()
                    }
                })
            })
        },
        getLocalFile(path) {
            getLocalFileBase64({absolutePath: path})
        },
        // 多选改变事件
        handleSelectionChange (rows) {
            this.multipleSelection = rows;
        },
        // 点击添加，上传到后端
        onClickAdd() {
            if (this.loading) {
                this.$message.info('加载中');
                return;
            }
            if (!this.multipleSelection.length) {
                this.$message.warning('请勾选添加数据');
                return;
            }
            let loadHander =  this.$message.info('正在上传中,请勿操作');

            this.loading = true;

            this.uploadState.count = this.multipleSelection.length;
            this.uploadState.step = 0;
            this.uploadState.success = 0;
            this.uploadState.err = 0;
            this.uploadState.errMsg = '';

            const uploadFiles = async () => {
                for (const item of this.multipleSelection) {
                    try {
                        const res = await getLocalFileBase64({ absolutePath: item.absolutePath });
                        if (res.success) {
                        const fileRes = await importFile({
                            patientInfoId: this.patientInfo.sId,
                            filename: item.name,
                            fileType: item.fileType,
                            base64: res.data
                        });
                        this.uploadState.step += 1;
                        if (fileRes.success) {
                            this.uploadState.success += 1;
                        } else {
                            this.uploadState.err += 1;
                            this.uploadState.errMsg += `文件名:${item.name} ;  `;
                        }
                        if (this.uploadState.step === this.uploadState.count) {
                            const msgSuccess = `上传成功：${this.uploadState.success} 张`;
                            const msgErr = `上传失败：${this.uploadState.err} 张 -- ${this.uploadState.errMsg}`;

                            if (this.uploadState.err && this.uploadState.success) {
                            this.$message.info(`成功：${this.uploadState.success} 张。失败：${this.uploadState.err} 张`);
                            setTimeout(() => {
                                this.$message.info(`失败：${this.uploadState.errMsg}`);
                            }, 3000);
                            } else if (this.uploadState.err) {
                            this.$message.error(msgErr);
                            } else if (this.uploadState.success) {
                            this.$message.success(msgSuccess);
                            }
                            // 关闭弹窗
                            if (this.autoClose) {
                            this.innerVisible = false;
                            }
                        }
                        }
                    } catch (err) {
                        this.uploadState.step += 1;
                        this.uploadState.err += 1;
                        this.uploadState.errMsg += `文件名:${item.name} ;  `;
                    }
                }
            };

            uploadFiles().finally(() => {
                this.loading = false;
                loadHander.close();
            })
        },
        // 点击行，显示图像
        handleRowClick(row) {
            getLocalFileBase64({absolutePath: row.absolutePath}).then(res => {
                if (res.success) {
                    this.showFile.fileType = row.fileType
                    this.showFile.base64 = `data:${row.fileType == 'pdf' ? 'application/pdf' : 'image/png'};base64,${res.data}`
                }
            })
        },
        changeDirectory(path) {
            this.condition.scanDir = path
            this.onChangeCondition()
        },
        setStorageItem() {
            let obj = localStorage.getItem(this.storageKey);
            if (!obj) {
                return
            }else {
                obj = JSON.parse(obj);
            }
            this.condition = obj.condition || { maxFileNumber: 100, fileTypes: ['bmp', 'jpg', 'png', 'pdf'] }
            this.autoClose = obj.autoClose || true
        },
        onChangeCheckBox(notAll = false) {
            const data = {
                condition: this.condition,
                autoClose: this.autoClose
            }
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            if (notAll) {
                return
            }
            this.selectAll();
        },
    },
    mounted() {
        this.setStorageItem()
    }
}
</script>
<style lang="scss" scoped>
:global(.import-image-dialog) {
    min-width: 1200px;
    max-width: 80%;
    height: 90%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    overflow: hidden;
}
.header {
    height: 80px;
    display: flex;
    background: #fafafa;
    margin-bottom: 10px;
    border: 1px solid #eee;
    .left {
        width: 690px;
        overflow: hidden;
        padding-left: 14px;
        .check-group{
            display: flex;
            align-items: center;
            height: 40px;
        }
        .check-box{
            margin-left: 77px;
        }
    }
    .right {
        flex: 1;
        display: flex;
        justify-content: flex-end;
        align-items: center;
    }
}
.container {
    height: calc(100% - 100px);
    display: flex;
    .left {
        width: 460px;
    }
    .right {
        flex: 1;
        overflow: hidden;
        padding-left: 10px;
        .plane-box{
            height: 100px;
            padding: 5px 0 0 10px;
            background: #fafafa;
            border: 1px solid #eee;
            list-style-type: none;
            margin: 0 0 10px;
            li {
                display: flex;
                height: 30px;
                line-height: 30px;
                width: 50%;
                float: left;
                padding-right: 10px;
                box-sizing: border-box;
                span {
                    display: inline-block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    &:nth-child(1) {
                        width: 70px;
                        text-align: justify;
                        display: inline-block;
                        text-align-last: justify;
                    }
                }
            }
        }
    }
    .plane-image{
        height: calc(100% - 120px);
        border: 1px solid #eee;
        :deep(.el-image-viewer__close){
            color: #fff;
            background-color: #606266;
        }
        :deep(.image-slot) {
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #999;
        }
    }
}
.upload-url {
    display: flex;
    width: calc(100% - 80px);
    .upload-url-button {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin-right: 10px;
        padding: 0 10px;
        background: #f4f4f5;
        border: 1px solid #d3d4d6;
        border-left: none;
        text-align: center;
        color: #666;
        cursor: pointer;
    }
}
.upload-reveal{
    display: inline-block;
    position: relative;
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    padding: 0 10px;
    background: #f4f4f5;
    border: 1px solid #d3d4d6;
    text-align: center;
    color: #666;
    cursor: default;
    .uplad-file{
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /*透明度为0*/
        opacity: 0;
        cursor: default;
    }
}
.upload-action{
    display: inline-block;
    position: relative;
    width: 60px;
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    padding: 0 10px;
    background: #f4f4f5;
    border: 1px solid #d3d4d6;
    text-align: center;
    color: #666;
    cursor: pointer;
}
</style>