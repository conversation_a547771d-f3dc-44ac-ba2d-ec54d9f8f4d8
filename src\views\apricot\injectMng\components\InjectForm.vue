<template>
    <div v-loading="loading">
        <el-form ref="refEditLayer"
            label-position="right"
            :model="form"
            :show-message="false">
            <FormList ref="formList"
                :storageKey="storageKeyList[sDeviceTypeCode]"
                :iModuleId="iModuleId"
                :list="defaultInjectList"
                v-model:formData="form"
                :optionData="optionsLoc"
                labelWidth="120px"
                :configBtn="injectModuleSetData.isShowConfigBtn">

                <!-- 核素特殊情况 ['sNuclideText','sTracerText','sPositionText','sTestModeText'] -->
                <!-- 核素特殊情况 ['核素','示踪剂','检查部位','用药'] -->
                <template v-for="(item, index) in defaultInjectList.filter(i => ['sNuclideText','sTracerText'].includes(i.sProp))"
                    v-slot:[item.sProp]="{ style, row }"
                    :key="index">
                    <el-select v-if="configValue?.medicineChooseType == 2"
                        v-model="form[medicineConfigParams[row.sProp].prop]"
                        filterable
                        clearable
                        placeholder=" "
                        :style="style"
                        @change="onMedicineChange">
                        <el-option v-for="item in optionsLoc[medicineConfigParams[row.sProp].optionListName]"
                            :key="item[medicineConfigParams[row.sProp].optionValue]"
                            :label="item[medicineConfigParams[row.sProp].optionLabel]"
                            :value="item[medicineConfigParams[row.sProp].optionValue]" />
                    </el-select>
                    <el-input v-else
                        v-model="form[row.sProp]"
                        readonly
                        @click="(e) => showLogin(e, row.sProp)"
                        :style="style"></el-input>
                </template>

                <template v-slot:fRecipeDose="{ row, style }">
                    <el-input v-model="form[row.sProp]"
                        type="number"
                        :style="style">
                        <template #append>
                            <el-select v-model="form.sRecipeDoseUnit"
                                placeholder=" "
                                class="i-suffix-value">
                                <el-option v-for="item in optionsLoc.sRecipeDoseUnitOptions"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sName"></el-option>
                            </el-select>
                        </template>
                    </el-input>
                </template>

                <template v-slot:dInjectionSta="{ row, style }">
                    <!-- 满针时间  -->
                    <el-time-picker placeholder=""
                        v-model="form[row.sProp]"
                        :default-value="defaultValue.iFull"
                        format="HH:mm"
                        @visible-change="onFocusFn('iFull')"
                        @change="computedInjectDose"
                        :style="style">
                    </el-time-picker>
                </template>
                <template v-slot:dInjectionTime="{ row, style }">
                    <!-- 实际时间  -->
                    <el-time-picker placeholder=""
                        v-model="form[row.sProp]"
                        :default-value="defaultValue.iActual"
                        format="HH:mm"
                        @visible-change="onFocusFn('iActual')"
                        @change="onInjectTimeInputChange"
                        :style="style">
                    </el-time-picker>
                </template>
                <template v-slot:dInjectionEnd="{ row, style }">
                    <!-- 空针时间  -->
                    <el-time-picker placeholder=""
                        v-model="form['dInjectionEnd']"
                        format="HH:mm"
                        :style="style"
                        @change="computedInjectDose">
                    </el-time-picker>
                </template>
                <template v-slot:fFullNeedle="{ row, style }">
                    <!-- 满针剂量  -->
                    <el-input v-model="form[row.sProp]"
                        type="number"
                        step="0.1"
                        min="0"
                        :style="style"
                        @input="computedInjectDose">
                        <template #append>
                            <el-select v-model="form.sFullUnit"
                                class="i-suffix-value"
                                clearable
                                placeholder="单位">
                                <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </template>
                    </el-input>
                </template>
                <template v-slot:fFactDose="{ row, style }">
                    <!-- 实际注射剂量  -->
                    <el-input v-model="form[row.sProp]"
                        type="number"
                        step="0.1"
                        min="0"
                        :style="style"
                        @input="onInputFact">
                        <template #append>
                            <el-select v-model="form.sFullUnit"
                                class="i-suffix-value"
                                clearable
                                placeholder="单位">
                                <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </template>
                    </el-input>
                </template>
                <template v-slot:fEmptyNeedle="{ row, style }">
                    <!-- 空针剂量  -->
                    <el-input v-model="form[row.sProp]"
                        type="number"
                        step="0.1"
                        min="0"
                        :style="style"
                        @input="computedInjectDose">
                        <template #append>
                            <el-select v-model="form.sFullUnit"
                                class="i-suffix-value"
                                clearable
                                placeholder="单位">
                                <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </template>
                    </el-input>
                </template>
                <!-- 渗漏 -->
                <template v-slot:sStainPosition="{ row, style }">
                    <el-select ref="sStainPosition"
                        v-model="form[row.sProp]"
                        filterable
                        clearable
                        placeholder=" "
                        :style="style">
                        <el-option v-for="(item, index) in optionsLoc['ApricotReportStain']"
                            :key="index"
                            :label="item.sName"
                            :value="item.sName">
                        </el-option>
                    </el-select>
                </template>
                <template v-slot:sInjectionPosition="{ row, style }">
                    <!-- 注射部位 -->
                    <el-select v-model="form[row.sProp]"
                        filterable
                        clearable
                        placeholder=" "
                        allow-create
                        :style="style">
                        <el-option v-for="(item,index) in optionsLoc['ApricotReportInjectSite']"
                            :key="index"
                            :label="item.sName"
                            :value="item.sValue"></el-option>
                    </el-select>
                </template>
                <template v-slot:fBloodSugar="{ row, style }">
                    <el-input type="number"
                        v-model="form[row.sProp]"
                        :style="style">
                        <template #suffix>
                            <span>mmol/L</span>
                        </template>
                    </el-input>
                </template>
                <template v-slot:fHeight="{ row, style }">
                    <el-input type="number"
                        v-model="form[row.sProp]"
                        :style="style"
                        :min="0"
                        @input="inputHeight">
                        <template #suffix>
                            <span>cm</span>
                        </template>
                    </el-input>
                </template>
                <template v-slot:fWeight="{ row, style }">
                    <el-input type="number"
                        v-model="form[row.sProp]"
                        :style="style"
                        :min="0"
                        @input="inputWeight">
                        <template #suffix>
                            <span>kg</span>
                        </template>
                    </el-input>
                </template>
                <!-- 误差率 -->
                <template v-slot:fError="{ row, style }">
                    <el-input v-model="form[row.sProp]"
                        :style="style"
                        readonly
                        :min="0">
                    </el-input>
                </template>
                <!-- 注射台监测数值 -->
                <template v-slot:fTableDetectDose="{ row, style }">
                    <el-input type="number"
                        v-model="form[row.sProp]"
                        :style="style"
                        :min="0">
                        <template #suffix>
                            <span>{{ row.sUnit }}</span>
                        </template>
                    </el-input>
                </template>
                <template v-slot:dTableDetectTime="{ row, style }">
                    <!-- 监测时间  -->
                    <el-time-picker placeholder=""
                        v-model="form[row.sProp]"
                        :style="style"
                        :clearable="false">
                    </el-time-picker>
                    <el-button type="primary"
                        link
                        style="position:absolute;right: 5px;"
                        @click="form[row.sProp] = new Date()">now</el-button>
                </template>
            </FormList>
        </el-form>
    </div>

    <el-popover v-if="popVisible"
        :width="420"
        trigger="click"
        v-model:visible="popVisible"
        virtual-triggering
        :virtual-ref="tempRef"
        popper-class="pop-class"
        @show="onPopoverShow">
        <div style="margin-bottom: 10px;">
            <el-input v-model="medicationCondition.nuclearName"
                placeholder="核素"
                clearable
                style="width: 148px;margin-right: 4px"></el-input>
            <el-input v-model="medicationCondition.tracerName"
                placeholder="示踪剂"
                clearable
                style="width: 148px"></el-input>
            <el-input ref="sTextInput"
                style="width: 0;opacity: 0;"></el-input>
            <!-- <i class="el-icon-s-tools float-right"
                    @click="handlerSetCheckMedicine"
                    style="float: right;font-size: 22px;position: relative;top: 6px;cursor: pointer;"></i> -->
        </div>
        <el-table
            :data="medicationTableData.filter(item => item.sNuclideName.toLowerCase().includes(medicationCondition.nuclearName.toLowerCase())).filter(item =>item.sTracerName.toLowerCase().includes(medicationCondition.tracerName.toLowerCase()))"
            ref="medicineRef"
            highlight-current-row
            row-key="sId"
            border
            max-height="300"
            @row-click="(o) => nuclearTableRowClick(o)">
            <el-table-column width="150"
                property="sNuclideName"
                label="核素"
                show-overflow-tooltip></el-table-column>
            <el-table-column min-width="150"
                property="sTracerName"
                label="示踪剂"
                show-overflow-tooltip></el-table-column>
            <el-table-column width="80"
                property="sTestModeName"
                label="衰变周期"
                show-overflow-tooltip>
                <template v-slot="{ row }">
                    {{row.fDecayPeriod }}
                    {{row.fDecayPeriod && row.sDecayUnitName ? row.sDecayUnitName : '' }}
                </template>
            </el-table-column>
        </el-table>
        <div style="margin-top: 10px;text-align: right;">
            <el-button-icon-fa icon="fa fa-close-1"
                @click="popoverClickOutside">关闭</el-button-icon-fa>
        </div>
    </el-popover>

</template>
<script>
import { queryByKey } from '$supersetApi/userConfig.js';
import { InjectList } from '../config/index.js'
import { deepClone } from '$supersetUtils/function'
import { mixinPrintPreview } from '$supersetResource/js/projects/apricot/index.js'
import { getBPMSetFindKeys } from '$supersetApi/projects/apricot/system/bpmSet.js'
import Api from '$supersetApi/projects/apricot/injectMng/inject.js'
export default {
    mixins: [mixinPrintPreview],
    components: {

    },
    props: {
        // 表单数据是否来自父组件
        isFormDatafromParent: {
            type: Boolean,
            default: true
        },
        doctorOptions: {
            type: Array,
            default: []
        }
    },
    inject: {
        providePatient: {
            from: 'patientInfo',
            default: {}
        },
        injectModuleSetData: {
            from: 'configValue',
            default: {}
        },
        iModuleId: {
            from: 'iModuleId',
            default: null
        },
        injectPatientInfo: {
            from: 'injectPatientInfo',
            default: {}
        },
        updateRowData: {
            from: 'updateInjectRowData'
        },
        getInitInJectData: {
            from: 'getInitInJectData'
        },
        medicationTableData: {
            from: 'medicationTableData',
        },
        getItemSetData: {
            from: 'getItemSetData',
        },
        configValue: {
            from: 'configValue',
            default: {}
        },
        nuclideOptions: {
            from: 'nuclideOptions',
            default: []
        },
        tracerOptions: {
            from: 'tracerOptions',
            default: []
        }
    },
    emits: ['upDateLoading', 'onChangeActiveTab'],
    data () {
        return {
            defaultInjectList: InjectList,
            rights: {},
            bloodSugarLoading: false,
            d_applyList_v: false,
            defaultValue: {
                iFull: undefined,
                iActual: undefined,
                iNull: undefined
            },
            loading: false,
            form: {},
            defaultData: {
                sNuclideText: undefined,
                sTracerText: undefined,
                fRecipeDose: undefined,
                sStainPosition: undefined,
                sDrugDeliveryCode: undefined,
                sInjectionPosition: undefined,
                dInjectionSta: undefined,
                dInjectionTime: undefined,
                dInjectionEnd: undefined,
                fFullNeedle: undefined,
                fFactDose: undefined,
                fEmptyNeedle: undefined,
                iIntake: undefined,
                iAft3Intake: undefined,
                iAft6Intake: undefined,
                sBefDrinkTypeCode: undefined,
                sAft3DrinkTypeCode: undefined,
                sAft6DrinkTypeCode: undefined,
                sMedicineSource: undefined,
                sNurseId: undefined,
                dInjectDate: undefined,
                fBloodSugar: undefined,
                fHeight: undefined,
                fWeight: undefined,
            },
            optionsLoc: {
                ApricotReportDoseUnit: this.$store.getters['dict/map'].ApricotReportDoseUnit || [],
                ApricotReportDrugDelivery: this.$store.getters['dict/map'].ApricotReportDrugDelivery || [],
                ApricotReportMedicineSource: this.$store.getters['dict/map'].ApricotReportMedicineSource || [],
                ApricotReportStain: this.$store.getters['dict/map'].ApricotReportStain || [],
                ApricotReportInjectSite: this.$store.getters['dict/map'].ApricotReportInjectSite || [],
                ApricotReportDrinkType: this.$store.getters['dict/map'].ApricotReportDrinkType || [],
                DoctorOptions: this.doctorOptions,
                waterOptions: [
                    { sName: "100ml", sValue: 100 },
                    { sName: "200ml", sValue: 200 },
                    { sName: "300ml", sValue: 300 },
                    { sName: "400ml", sValue: 400 },
                    { sName: "500ml", sValue: 500 },
                    { sName: "600ml", sValue: 600 },
                    { sName: "700ml", sValue: 700 },
                    { sName: "800ml", sValue: 800 },
                    { sName: "900ml", sValue: 900 },
                    { sName: "1000ml", sValue: 1000 },
                ],
                sRecipeDoseUnitOptions: this.$store.getters['dict/map'].ApricotReportDoseUnit || [],
                nuclideOptions: [],
                tracerOptions: []
            },
            //patientInfo:{},
            storageKeyList: {
                PET: 'PETInjectFormList',
                ECT: 'ECTInjectFormList',
                Others: 'OthersInjectFormList',
            },
            sDeviceTypeCode: '',
            injectData: {},
            nuclearParams: {},  // 用药参数
            medicationCondition: {
                nuclearName: '',
                tracerName: ''
            },
            tempRef: null,
            popVisible: false,
            keyWordProp: '',
            medicineConfigParams: {
                sNuclideText: {
                    prop: 'sNuclide',
                    optionListName: 'nuclideOptions',
                    optionLabel: 'sNuclideName',
                    optionValue: 'sId'
                },
                sTracerText: {
                    prop: 'sTracer',
                    optionListName: 'tracerOptions',
                    optionLabel: 'sTracerName',
                    optionValue: 'sId',
                }
            },
            isSyncRecipeDose: false,  // 注射剂量值同步到处方剂量值
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
        patientInfo () {
            return this.providePatient
        }
    },
    watch: {
        'providePatient.sDeviceTypeCode': {
            handler (val, oldVal) {
                if (val == '001' || val == '003') {
                    this.sDeviceTypeCode = 'PET'
                    return
                }
                if (val == '002') {
                    this.sDeviceTypeCode = 'ECT'
                    return
                }
                if (val == '004' || val == '005' || val == '006') {
                    this.sDeviceTypeCode = 'Others'
                    return
                }

                // 如果设备编码为空或者其他类型，默认是Others
                this.sDeviceTypeCode = 'Others'
            },
            immediate: true
        },
        injectPatientInfo: {
            async handler (val, oldVal) {
                if (!val.sId) {
                    this.initForm();
                    return
                }
                if (val?.sPatientId === oldVal?.sPatientId) true;
                this.initForm();
                if (this.configValue?.medicineChooseType == 2) {
                    this.optionsLoc.nuclideOptions = this.nuclideOptions;
                    this.optionsLoc.tracerOptions = this.tracerOptions;
                    this.setMedicineData();
                } else {
                    this.findNuclearRow(this.medicationTableData)
                }
            },
            immediate: true
        }
    },
    methods: {
        setMedicineData () {
            if (!this.form.sId && Object.keys(this.injectPatientInfo).length) {
                const tempNuclideItem = this.optionsLoc.nuclideOptions.find(item => item.sNuclideName === this.injectPatientInfo.sNuclideText);
                if (tempNuclideItem?.sId) {
                    this.form.sNuclide = tempNuclideItem.sId;
                    this.form.sNuclideSupName = tempNuclideItem.sNuclideSupName;
                }
                const tempTracerItem = this.optionsLoc.tracerOptions.find(item => item.sTracerName === this.injectPatientInfo.sTracerText);
                if (tempTracerItem?.sId) {
                    this.form.sTracer = tempTracerItem.sId
                    this.form.sTracerSupName = tempTracerItem.sTracerSupName
                }
            }
        },
        onMedicineChange () {
            if (this.configValue.medicineChooseType == 1) return;
            const { sNuclide, sTracer } = this.form;
            this.setParamsOfNuclideToForm(sNuclide);
            this.setParamsOfTracerToForm(sTracer);
            this.computedInjectDose();
        },
        // 选中核素相关参数赋值给表单对象
        setParamsOfNuclideToForm (id) {
            const tempItem = this.optionsLoc.nuclideOptions.find(item => item.sId === id);
            if (!tempItem) return
            this.form.sNuclideText = tempItem.sNuclideName;
            this.form.sNuclideSupName = tempItem.sNuclideSupName;
        },
        // 选中示踪剂相关参数赋值给表单对象
        setParamsOfTracerToForm (id) {
            const tempItem = this.optionsLoc.tracerOptions.find(item => item.sId === id);
            if (!tempItem) return
            this.form.sTracerText = tempItem.sTracerName;
            this.form.sTracerSupName = tempItem.sTracerSupName;
        },
        inputHeight (value) {
            this.form.fHeight = this.saveTwoDecimal(value);
        },
        inputWeight (value) {
            this.form.fWeight = this.saveTwoDecimal(value);
            let nuclearParams = this.nuclearParams || {};
            if (this.configValue?.medicineChooseType == 2) {
                this.form['fRecipeDose'] = value > 0 ? (value * 0.1).toFixed(2) : undefined;
                return
            }
            if (!Object.keys(nuclearParams).length || nuclearParams.iIsInvariable) {
                return
            }
            let bool = (this.form.fWeight >= 0 && nuclearParams.fCoefficient);
            this.form['fRecipeDose'] = bool ? (this.form.fWeight * nuclearParams.fCoefficient).toFixed(2) : undefined;
        },
        saveTwoDecimal (value) {
            if (value.indexOf(".") < 0 && value !== "") {
                // 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
                value = parseFloat(value) + "";
            } else if (value.indexOf(".") >= 0) {
                value = value.replace(/^()*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两个小数
            }
            return value
        },
        showLogin (e, prop) {
            const evt = e || window.e || window.event;
            if (this.keyWordProp === prop && this.popVisible) {
                this.popVisible = false;
                return
            }
            if (this.tempRef) this.popVisible = false;
            this.medicationCondition.nuclearName = '';
            this.medicationCondition.tracerName = '';
            nextTick(() => {
                this.tempRef = evt.currentTarget
                this.popVisible = true;
                this.keyWordProp = prop;
            })
        },
        async onPopoverShow () {
            await this.getItemSetData();
            const tableRef = this.$refs['medicineRef'];
            tableRef && tableRef.setCurrentRow(this.nuclearParams);
            this.$refs['sTextInput'] && this.$refs['sTextInput'].focus();
        },
        popoverClickOutside () {
            this.popVisible = false;
        },
        // 核素相关表格行点击事件
        nuclearTableRowClick (row) {
            this.nuclearParams = row;
            this.popoverClickOutside()
            // 核素、示踪剂、检查部位、检查方式相关字段的赋值
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sMedicineSource', 'sMedicineSourceText'];
            let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sMedicineSource', 'sMedicineSourceText'];
            formKeys.map((item, index) => {
                this.form[item] = row[oKeys[index]];
            })
            this.doComputeDosageBySetting();
            this.computedInjectDose();
        },
        findNuclearRow (arr) {
            this.nuclearParams = {};
            if (!arr.length) {
                return
            }
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName'];
            let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName'];
            let finalItem = arr.find(item => {
                let isEq = true;
                oKeys.map((key, index) => {
                    if (this.form[formKeys[index]] && item[key] !== this.form[formKeys[index]]) {
                        isEq = false;
                    }
                })
                let targetItem = null;
                if (isEq) {
                    targetItem = item;
                }
                return targetItem;
            })
            if (finalItem) {
                this.nuclearParams = finalItem;
                this.form.sMedicineSource = finalItem.sMedicineSource;
                this.form.sMedicineSourceText = finalItem.sMedicineSourceText;
            }
        },
        // 根据核素设置计算处方剂量
        doComputeDosageBySetting () {
            let val = this.nuclearParams
            if (!Object.keys(val).length) return;
            if (val.iIsInvariable) {
                this.form['fRecipeDose'] = Number(val.fDosage);
                return
            }
            let fWeight = this.injectPatientInfo.fWeight;
            this.form['fRecipeDose'] = (fWeight && val.fCoefficient) ? Number((fWeight * val.fCoefficient).toFixed(2)) : undefined;
        },
        // 清除核素相关属性数据
        resetFormNuclearData () {
            let temp = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode', 'fRecipeDose'];
            for (let item in temp) {
                if (this.form[temp[item]]) {
                    this.form[temp[item]] = null;
                }
            }
        },
        // 判断是否包含mdp
        judgeIncludesSpecial (text) {
            text = text ?? ''
            return text.toLowerCase().includes('mdp')
        },
        onInputFact (value) {
            if (value === '') {
                this.form['fError'] = '';
                return
            }
            let A0 = this.form.fFullNeedle;     // 满针剂量
            let A1 = value;
            // 误差率计算 区分F18和mdp
            if (this.judgeIncludesSpecial(this.form.sNuclideText)) {
                // mdp 计算方式
                const fError = (Math.abs(A1 - 20) / 20) * 100;
                this.form['fError'] = fError.toFixed(0) + '%';
                return
            }
            // F18 (包括其他) 计算方式
            if (A0) {
                const fError = (A0 && (Math.abs(A1 - A0) / A0) * 100);
                this.form['fError'] = fError.toFixed(0) + '%';
            }
            this.handleSyncRecipeDose()
        },
        // 获取衰变周期和衰变单位
        getDecayParams () {
            let fDecayPeriod = undefined;
            let iDecayUnit = undefined;
            if (this.configValue?.medicineChooseType == 2) {
                console.log('随机选择模式')
                // 随机选择模式
                this.optionsLoc.nuclideOptions.find(item => {
                    if (item.sId === this.form.sNuclide) {
                        fDecayPeriod = item.fDecayPeriod;
                        iDecayUnit = item.iDecayUnit;
                    }
                })
                return { fDecayPeriod, iDecayUnit }
            }
            // 关联选择模式
            fDecayPeriod = this.nuclearParams?.fDecayPeriod;
            iDecayUnit = this.nuclearParams?.iDecayUnit;
            return { fDecayPeriod, iDecayUnit }
        },
        onInjectTimeInputChange () {
            if (this.injectModuleSetData.injectTimeSetType == 2) {
                let aTimeHas = this.getFullAndNullInput();
                aTimeHas[0] && (this.form['dInjectionSta'] = this.setComputedTime(this.injectModuleSetData.iTimeInterval, this.form['dInjectionTime']));
                aTimeHas[1] && (this.form['dInjectionEnd'] = this.setComputedTime(-this.injectModuleSetData.iTimeInterval, this.form['dInjectionTime']));
            }
            this.$nextTick(() => {
                this.computedInjectDose();
            })
        },
        // 同步处方剂量
        handleSyncRecipeDose (value) {
            if (this.isSyncRecipeDose) {
                //  注射剂量值同步到处方剂量值
                this.form.fRecipeDose = value;
            }
        },
        computedInjectDose () {
            let A0 = this.form.fFullNeedle;     // 满针剂量
            let A2 = this.form.fEmptyNeedle;    // 空针剂量
            let t0 = this.form.dInjectionSta;   // 满针时间
            let t1 = this.form.dInjectionTime;  // 注射时间
            let t2 = this.form.dInjectionEnd;   // 空针时间
            const isNumeric = this.isNumeric;
            const isFalseArr = [null, undefined, ''];
            if (!isNumeric(A0) || !isNumeric(A2)) {
                // if(!isNumeric(A0) || !isNumeric(A2)  || isFalseArr.includes(t0) || isFalseArr.includes(t1) || isFalseArr.includes(t2)) {
                // 实际注射量
                this.form['fFactDose'] = '';
                this.form['fError'] = ''
                console.log('（满针剂量、空针剂量）有空值，无法计算！');
                return
            }
            // 衰变周期 , 衰变单位
            const { fDecayPeriod, iDecayUnit } = this.getDecayParams();
            if (!isNumeric(fDecayPeriod) || !isNumeric(iDecayUnit)) {
                // 两者有一个不是数字的时候，简单计算：A1 = A0 - A2
                const A1 = this.form['fFactDose'] = (A0 - A2).toFixed(2);
                this.handleSyncRecipeDose(A1);
                console.log('衰变周期未设置, 计算公式：A1 = A0 - A2', this.form['fFactDose']);
                const fError = this.judgeIncludesSpecial(this.form.sNuclideText) ? (Math.abs(A1 - 20) / 20) * 100 : (Math.abs(A1 - A0) / A0) * 100;
                this.form['fError'] = fError.toFixed(0) + '%';
                return
            }

            // let period = 109.8 * 60 * 1000;
            // 衰变单位：1=秒；2=分；3=时；4=天；5=月；6=年；
            // 单位对应秒转换
            const oToSeconds = {
                1: 1000,
                2: 60 * 1000,
                3: 60 * 60 * 1000,
                4: 24 * 60 * 60 * 1000,
                5: 30 * 24 * 60 * 60 * 1000,
                6: 365 * 24 * 60 * 60 * 1000,
            }
            // 衰变周期
            const period = fDecayPeriod * oToSeconds[iDecayUnit];
            // 转化为时间戳
            t0 = new Date(this.form.dInjectionSta).getTime();
            t1 = new Date(this.form.dInjectionTime).getTime();
            t2 = new Date(this.form.dInjectionEnd).getTime();
            if (isFalseArr.includes(t0) || isFalseArr.includes(t1) || isFalseArr.includes(t2)) {
                this.$message.warning('（满针时间、实际注射时间、空针时间）有空值！');
                this.form['fFactDose'] = '';
                return
            }
            if (t0 > t2) {
                this.$message.warning('满针时间不能大于空针时间！');
                this.form['fFactDose'] = '';
                return
            }
            let A1 = A0 * Math.exp(-Math.LN2 / period * Math.abs(t1 - t0)) - A2 * Math.exp(-Math.LN2 / period * Math.abs(t2 - t1));
            console.log('半衰期公式参数：fDecayPeriod，iDecayUnit，A1', fDecayPeriod, iDecayUnit, A1);
            A1 = A1.toFixed(2);
            console.log('使用半衰期公式计算，A1=', A1);
            this.form['fFactDose'] = A1;
            this.handleSyncRecipeDose(A1);
            const fError = this.judgeIncludesSpecial(this.form.sNuclideText) ? (Math.abs(A1 - 20) / 20) * 100 : (Math.abs(A1 - A0) / A0) * 100;
            this.form['fError'] = fError.toFixed(0) + '%';
        },
        // 判断是一个变量是否为数字或字符串数字
        isNumeric (value) {
            // 使用 typeof 来判断变量是否为数字类型
            if (typeof value === 'number') {
                return true;
            }
            // 使用正则表达式来检查字符串是否表示数字
            if (typeof value === 'string' && value.trim() !== '') {
                // 去除字符串两端的空格，并检查是否为空字符串
                return /^\d+(\.\d+)?$/.test(value.trim());
            }
            return false;
        },
        onFocusFn (keyword) {
            this.defaultValue[keyword] = new Date(new Date().getTime() - this.injectModuleSetData[keyword] * 60 * 1000);
        },
        // 获取下拉框组件选中值的标签名
        getName (arr, val) {
            let item = arr.find(item => item.sValue == val);
            return item ? item.sName : null;
        },
        // 保存注射信息
        handleSaveInjectClick () {
            if (!this.patientInfo.sId) {
                this.$message.warning('请选择患者数据！');
                this.$emit('upDateLoading', 'saveLoading')
                return
            }
            let form = deepClone(this.form);

            form.sDrugDeliveryText = this.getName(this.optionsLoc.ApricotReportDrugDelivery, form.sDrugDeliveryCode);
            form.sBefDrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, form.sBefDrinkTypeCode);
            form.sAft3DrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, form.sAft3DrinkTypeCode);
            form.sAft6DrinkTypeText = this.getName(this.optionsLoc.ApricotReportDrinkType, form.sAft6DrinkTypeCode);
            form.sMedicineSourceText = this.getName(this.optionsLoc.ApricotReportMedicineSource, form.sMedicineSource);
            form.sInjectionPositionText = this.getName(this.optionsLoc.ApricotReportInjectSite, form.sInjectionPosition);
            form.sFullUnitText = this.getName(this.optionsLoc.ApricotReportDoseUnit, form.sFullUnit);

            // 误差率百分比转小数
            if (form.fError) {
                form.fError = form.fError.replace('%', '') / 100;
                // console.log(form.fError);
            }

            //  注射人
            this.optionsLoc.DoctorOptions.find(item => {
                if (item.userId === form.sNurseId) {
                    form.sNurseNo = item.userNo;
                    form.sNurseName = item.userName;
                }
            })

            if (form.dInjectDate && form.dInjectionSta) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dInjectionSta).toTimeString().substring(0, 8)
                form.dInjectionSta = new Date(`${sDate} ${sTime}`)
            }
            if (form.dInjectDate && form.dInjectionTime) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dInjectionTime).toTimeString().substring(0, 8)
                form.dInjectionTime = new Date(`${sDate} ${sTime}`)
            }
            if (form.dInjectDate && form.dInjectionEnd) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dInjectionEnd).toTimeString().substring(0, 8)
                form.dInjectionEnd = new Date(`${sDate} ${sTime}`)
            }
            if (form.dInjectDate && form.dTableDetectTime) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dTableDetectTime).toTimeString().substring(0, 8)
                form.dTableDetectTime = new Date(`${sDate} ${sTime}`)
            }
            Object.keys(form).map(item => {
                if ([null, undefined, ''].includes(form[item])) {
                    delete form[item]
                }
            })
            this.$refs['refEditLayer'].validate((valid) => {
                if (!valid) {
                    this.$message.warning('请正确填写信息!');
                    this.loading = false
                    this.$emit('upDateLoading', 'saveLoading')
                    return false;
                }
                this.loading = true
                if (!this.form.sId) {
                    Api.saveInject(form).then(res => {
                        this.loading = false
                        this.$emit('upDateLoading', 'saveLoading')
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.updateData();
                            // 新增时，根据缓存设置判断是否需要打印；
                            if (this.$auth['report:injection:print']) {
                                this.mxOnPrintByCache(this.injectModuleSetData);
                            }
                            this.initForm();
                            this.$eventbus.emit('onRefreshInjectRecordCard', res.data);
                            return;
                        }
                        this.$message.error(res.msg);
                    }).catch(() => {
                        this.loading = false
                        this.$emit('upDateLoading', 'saveLoading')
                    })
                    return
                }
                Api.editInject(form).then(res => {
                    this.loading = false
                    this.$emit('upDateLoading', 'saveLoading')
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.updateData();
                        // 修改时，根据缓存设置判断是否需要打印；
                        if (this.$auth['report:injection:print']) {
                            this.mxOnPrintByCache(this.injectModuleSetData, false);
                        }
                        this.initForm();
                        this.$eventbus.emit('onRefreshInjectRecordCard', form);
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(() => {
                    this.loading = false
                    this.$emit('upDateLoading', 'saveLoading')
                })
            });
        },
        updateData () {
            // this.$emit('isUpdateRow')
            this.updateRowData()
            this.getInitInJectData(this.patientInfo.sId)
        },
        // 删除注射记录
        handleDelInjectClick () {
            if (!this.form.sId) {
                this.$message.warning('没有数据可删除！');
                this.$emit('upDateLoading', 'delLoading');
                return
            }
            Api.delInject({
                iVersion: this.form.iVersion,
                sId: this.form.sId,
                sPatientId: this.patientInfo.sId
            }).then(res => {
                this.$emit('upDateLoading', 'delLoading')
                if (res.success) {
                    this.$message.success(res.msg);
                    this.updateData()
                    this.initForm();
                    return
                }
                this.$message.error(res.msg);
            }).catch((err) => {
                this.$emit('upDateLoading', 'delLoading')
                console.log(err)
            })
        },
        // 读取并设置表单的默认值（非日期相关输入框）
        setFormDefaultValue () {
            // 默認值，字段輸入框類型是select，配置Index;
            // const oDefaultValues = {
            //     sDrugDeliveryCode: 0,
            //     sStainPosition: 0,
            //     sInjectionPosition: 0,
            //     sMedicineSource: 0
            // }
            if (!this.$refs.formList) {
                return
            }
            // console.log(this.$refs.formList.react.tableData);
            // 排除日期
            const cacheConfigList = this.$refs.formList.react.tableData.filter(item => item.isShow && item.sProp.slice(0, 1) !== 'd');
            // console.log(cacheConfigList);
            cacheConfigList.forEach(item => {
                // 请检查本地defaultInjectList 是否配置sOptionProp，若配置不完全，则手动完善；
                let tempItem = this.defaultInjectList.find(o => item.sProp === o.sProp && o.sInputType === 'option');
                const defaultValue = item.defaultValue;
                if (tempItem) {
                    let options = this.optionsLoc[tempItem.sOptionProp] || [];
                    if (defaultValue.length) {
                        let targetItem = options[defaultValue];
                        this.form[item.sProp] = targetItem?.sValue;
                        if (item.sProp === 'sStainPosition') {
                            this.form[item.sProp] = targetItem?.sName;
                        }
                    }
                    // if (!item.required) {
                    //     if (defaultValue.length) {
                    //         let targetItem = options[defaultValue];
                    //         this.form[item.sProp] = targetItem?.sValue;
                    //         if (tempItem.sProp === 'sStainPosition') {
                    //             this.form[item.sProp] = targetItem?.sName;
                    //         }
                    //     }
                    // } else {
                    //     let targetItem = options[defaultValue.length ? defaultValue : oDefaultValues[item.sProp]];
                    //     this.form[item.sProp] = targetItem?.sValue;
                    //     if (tempItem.sProp === 'sStainPosition') {
                    //         this.form[item.sProp] = targetItem?.sName;
                    //     }
                    // }
                } else {
                    if (defaultValue.length) {
                        this.form[item.sProp] = defaultValue;
                    }
                    if (item.sProp === 'fRecipeDose' && item.isShow && !this.form['sRecipeDoseUnit']) {
                        // 处方剂量单位
                        if (!this.form['sRecipeDoseUnit']) {
                            const ApricotReportDoseUnit = this.optionsLoc.ApricotReportDoseUnit || [];
                            this.form['sRecipeDoseUnit'] = ApricotReportDoseUnit[0]?.sValue;
                        }
                    }
                    if (['fEmptyNeedle', 'fFullNeedle', 'fFactDose'].includes(item.sProp) && item.isShow) {
                        // 注射剂量单位
                        const ApricotReportDoseUnit = this.optionsLoc.ApricotReportDoseUnit || [];
                        this.form['sFullUnit'] = ApricotReportDoseUnit[0]?.sValue;
                    }
                    // if (!item.required) {
                    //     this.form[item.sProp] = defaultValue;
                    // } else {
                    //     this.form[item.sProp] = defaultValue.length ? defaultValue : oDefaultValues[item.sProp];
                    // }
                }
            })
        },
        getFullAndNullInput () {
            // 判断表单是否显示注射时间相关的录入框
            let list = this.$refs.formList?.showingContent || [];
            let hasFull = false;
            let hasNull = false;
            let hasFact = false;
            let hasDate = false;
            list.map(item => {
                if (item.prop === 'dInjectionSta' && item.defaultValue.length) {
                    hasFull = true
                }
                if (item.prop === 'dInjectionEnd' && item.defaultValue.length) {
                    hasNull = true
                }
                if (item.prop === 'dInjectionTime' && item.defaultValue.length) {
                    hasFact = true
                }
                if (item.prop === 'dInjectDate' && item.defaultValue.length) {
                    hasDate = true
                }
            })
            return { hasFull, hasNull, hasFact, hasDate }
        },
        // 初始化表单
        initForm () {
            this.form = deepClone(this.defaultData);
            this.setFormDefaultValue()
            this.form.sPatientId = this.patientInfo.sId;
            this.form.sNurseNo = this.userInfo.sNo;
            this.form.sNurseName = this.userInfo.sName;
            this.form['sNurseId'] = this.userInfo.sId;
            this.form['fHeight'] = this.patientInfo.fHeight;
            this.form['fWeight'] = this.patientInfo.fWeight;
            // 注射时间赋值
            let aTimeHas = this.getFullAndNullInput();
            this.form['dInjectDate'] = aTimeHas.hasDate ? new Date() : '';
            if (this.injectModuleSetData.injectTimeSetType == 2) {
                // 其他方式
                this.form['dInjectionSta'] = aTimeHas.hasFull ? this.setComputedTime(this.injectModuleSetData.iTimeInterval) : '';
                this.form['dInjectionTime'] = aTimeHas.hasFact ? this.setComputedTime(0) : '';
                this.form['dInjectionEnd'] = aTimeHas.hasNull ? this.setComputedTime(-this.injectModuleSetData.iTimeInterval) : '';
            } else {
                // 邵逸夫使用方式 
                this.form['dInjectionSta'] = aTimeHas.hasFull ? this.setComputedTime(this.injectModuleSetData.iFull) : '';
                this.form['dInjectionTime'] = aTimeHas.hasFact ? this.setComputedTime(this.injectModuleSetData.iActual) : '';
                this.form['dInjectionEnd'] = aTimeHas.hasNull ? this.setComputedTime(this.injectModuleSetData.iNull) : '';
            }
            // 注射信息
            if (this.injectPatientInfo?.sPatientId === this.providePatient?.sId) {
                const fieldList = ['sNuclide', 'sTracer', 'sNuclideText', 'sTracerText', 'fRecipeDose', 'sRecipeDoseUnit', 'fBloodSugar'];
                fieldList.map(item => {
                    this.form[item] = this.injectPatientInfo[item];
                })
            }

            this.$nextTick(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            });
        },
        // 计算注射时间
        setComputedTime (num, time) {
            num = isNaN(+num) ? 0 : num;
            time = time ? new Date(time).getTime() : new Date().getTime()
            return new Date(time - num * 60 * 1000);
        },
        setUnitDoseText () {
            let val = this.staticPatientInit
            let target = this.optionsLoc.ApricotReportDoseUnit.find(item => val.sRecipeDoseUnit == item.sValue);
            val.sRecipeDoseUnitText = target ? target.sName : '';
        },
        // element组件 数字输入框 当数据返回为null 界面显示为0，设置为undefined 则不显示
        setNull2Undefined (obj) {
            if (Object.prototype.toString.call(obj) !== '[object Object]') return;
            if (!Object.keys(obj).length) return;
            for (let k of Object.keys(obj)) {
                if (obj[k] === null) {
                    obj[k] = undefined;
                }
            }
        },
        // 显示显示非该用户类型的医生
        AddDocOptionToShow (params) {
            const targetItem = this.optionsLoc.DoctorOptions.find(item => item.userId === params.sNurseId);
            if (!targetItem) {
                this.optionsLoc.DoctorOptions.push({
                    userId: params.sNurseId,
                    userNo: params.sNurseNo,
                    userName: params.sNurseName,
                    sValue: params.sNurseId,
                    sName: params.sNurseName,
                    iIsAuxiliary: 1,
                })
            }
        },
        // 注射剂量值同步到处方剂量值(1:开;非1:关)
        async judgeSyncRecipeDoseSwitch () {
            let open = false;
            this.isSyncRecipeDose = false;
            const params = ['SyncRecipeDose']
            await getBPMSetFindKeys(params).then(res => {
                if (res.success) {
                    const data = res?.data?.[0] || {};
                    open = (data?.sValue || 0) == 1;
                    this.isSyncRecipeDose = open
                }
            }).catch(err => {
                console.log(err);
            })
        },
    },
    mounted () {
        this.judgeSyncRecipeDoseSwitch()
        // 兄弟组件 接收Machine.vue组件上机信息修改
        this.$eventbus.on('injectInfoFormEdit', res => {
            if (res.sNurseId) {
                this.AddDocOptionToShow(res)
            }
            this.$emit('onChangeActiveTab');
            this.$nextTick(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].resetFields();
                this.initForm();
                this.form = { ...res };
            })
        })
        this.$eventbus.on('injectInfoFormInit', row => {
            if (row.sId === this.form.sId) {
                this.initForm();
            }
        })
    },
    destroyed () {
        this.$eventbus.off('injectInfoFormEdit');
        this.$eventbus.off('injectInfoFormInit');
    }
}
</script>
<style lang="scss" scoped>
.c-panel-01 {
    > .c-item {
        padding: 10px 15px 5px 5px;
    }
    .c-button {
        padding: 5px 15px;
        border-top: 1px solid #eee;
        box-sizing: border-box;
    }
}
:deep(.el-input-group__append) {
    padding: 0;
}
</style>