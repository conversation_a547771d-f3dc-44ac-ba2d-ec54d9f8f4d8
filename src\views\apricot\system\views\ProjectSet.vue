<template>
    <SchemeProjectConfig :dialogVisible="projectSet.visible"
        @closeDialog="closeDialog"
        :isSetCheckMedicine="isSetCheckMedicine"></SchemeProjectConfig>
</template>
<script>
import SchemeProjectConfig from '$supersetViews/apricot/system/projectSet/index.vue'
export default {
    name: 'ProjectSet',
    components: {
        SchemeProjectConfig,
    },
    data () {
        return {
            projectSet: {
                visible: true
            },
            isSetCheckMedicine: false
        }
    },
    methods: {
        closeDialog () {
            this.projectSet.visible = false;
        }
    }
};
</script>
<style lang="scss" scoped>
</style>
