<template>
    <div class="c-tabs-02 m-flexLaout-ty">
        <div class="g-flexChild g-content m-flexLaout-ty">
            <div class="g-flexChild">
                <el-table :data="drugData.data1"
                    border
                    stripe
                    size="small"
                    height="100%"
                    class="c-first"
                    :row-class-name="tableRowClassName">
                    <el-table-column prop="dMeterTime"
                        width="165"
                        label="时间">
                        <template v-slot="scope">
                            <el-date-picker v-model="scope.row.dMeterTime"
                                align="right"
                                type="datetime"
                                size="small">
                            </el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column prop="sBlood"
                        min-width="100"
                        label="血糖测量（mmol/L）">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.sBlood"
                                type="number"
                                class="mark1"
                                size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fDose"
                        min-width="100"
                        label="注射胰岛素(IU)">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.fDose"
                                size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="sDoctorId"
                        min-width="100"
                        label="注射医生">
                        <template v-slot="scope">
                            <el-select size="small" 
                                v-model="scope.row.sDoctorId"
                                @change="changeDocdor(scope.$index)" >
                                <el-option v-for="(item,index) in doctorOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.sValue"
                                    ></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作"
                        width="140"
                        align="center">
                        <template v-slot:header>
                            <el-button-icon-fa size="small"
                                _icon="fa fa-plus"
                                title="添加"
                                link
                                @click="onClickAdd(1)"></el-button-icon-fa>
                        </template>
                        <template v-slot="scope">
                            <el-button-icon-fa
                                title="保存"
                                link
                                size="small"
                                _icon="fa fa-save"
                                :loading="saveLoading1"
                                type="primary"
                                @click="handleSaveDrugClick(1)">保存
                            </el-button-icon-fa>
                            <el-button
                                title="删除"
                                link
                                size="small"
                                @click="onClickDelete(scope.$index, 1, scope.row)">
                                    删除
                                    <template #icon>
                                        <el-icon><Delete /></el-icon>
                                    </template>
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <h3 class="c-item-title">其他药物给药情况</h3>
            <div class="g-flexChild">
                <el-table :data="drugData.data2"
                    border
                    stripe
                    size="small"
                    height="100%"
                    :row-class-name="tableRowClassName">
                    <el-table-column prop="dMeterTime"
                        width="165"
                        label="时间">
                        <template v-slot="scope">
                            <el-date-picker v-model="scope.row.dMeterTime"
                                align="right"
                                type="datetime"
                                size="small">
                            </el-date-picker>
                        </template>
                    </el-table-column>
                    <el-table-column prop="sDrugName"
                        min-width="100"
                        label="药物名称">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.sDrugName"
                                class="mark2"
                                size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fDose"
                        min-width="100"
                        label="药物剂量">
                        <template v-slot="scope">
                            <el-input v-model="scope.row.fDose"
                                size="small"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="sDoctorId"
                        min-width="100"
                        label="注射医生">
                        <template v-slot="scope">
                            <el-select size="small" 
                                v-model="scope.row.sDoctorId" 
                                @change="changesDoctor(scope.$index)"
                                >
                                <el-option v-for="(item,index) in doctorOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作"
                        width="140"
                        align="center">
                        <template v-slot:header>
                            <el-button-icon-fa
                                _icon="fa fa-plus"
                                link
                                @click="onClickAdd(2)"></el-button-icon-fa>
                        </template>
                        <template v-slot="scope">
                            <el-button-icon-fa
                                title="保存"
                                link
                                size="small"
                                _icon="fa fa-save"
                                :loading="saveLoading2"
                                type="primary"
                                @click="handleSaveDrugClick(1)"> 保存
                            </el-button-icon-fa>
                            <el-button title="删除"
                                link
                                size="small"
                                @click="onClickDelete(scope.$index, 2, scope.row)">
                                删除
                                <template #icon>
                                    <el-icon><Delete /></el-icon>
                                </template>
                            </el-button>
                                
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <!-- <div class="c-buttons">
            <el-button-icon-fa type="primary"
                v-if="rights.DrugDel"
                icon="el-icon-document-checked"
                size="small"
                @click="handleSaveDrugClick">保存</el-button-icon-fa>
        </div> -->
    </div>
</template>
<script>
import { Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus';
// import { getStoreNameByRoute } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/injectMng/inject.js' // 接口


export default {
    name: 'BloodAndDose',
    components:{
        Delete
    },
    inject:{
        providePatient :{
            from: 'patientInfo',
            default: {}
        },
    },
    props: {
        doctorOptions: {
            type: Object,
            default: {}
        }
    },
    emits:['upDateLoading'],
    data () {
        return {
            drugData: {
                data1: [],
                data2: [],
            },
            rights: {},
           
            patientInfo: {},
            saveLoading1: false,
            saveLoading2: false,
            addType: null,
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
        // patientInfo () {
        //     let modules = getStoreNameByRoute(this.$route.name);
        //     return this.$store.state.apricot[modules].patientInfo || {};
        // },
    },
    watch: {
        providePatient: {
            handler (val) {
                if (val.sId) {
                    this.patientInfo = val
                    this.getData()
                }
            },
            immediate: true,
            // deep: true,
        },
    },
    methods: {
       
        // 保存血糖及给药信息
        handleSaveDrugClick (type) {
            const arr = [null,undefined,'']
            let target1 = this.drugData.data1.find( item=> arr.includes(item.dMeterTime) || arr.includes(item.sBlood) || arr.includes(item.fDose))
            let target2 = this.drugData.data2.find( item=> arr.includes(item.dMeterTime) || arr.includes(item.sDrugName) || arr.includes(item.fDose) )
            if(target1 || target2) {
                this.$message.warning('请完善信息填写再保存！')
                this.$emit('upDateLoading', 'saveLoading')
                return
            }
            if(type && this.addType == 1) {
                this.saveLoading1 = true
            }
            if(type && this.addType == 2) {
                this.saveLoading2 = true
            }
            let jsonData = [...this.drugData.data1, ...this.drugData.data2];
            if (!jsonData.length) return

            Api.saveBatchDrugDelivery(jsonData).then(res => {
                if(!type) {
                    this.$emit('upDateLoading', 'saveLoading')
                }else {
                    this.saveLoading1 = false
                    this.saveLoading2 = false
                }
                
                if (!res.success) {
                    this.$message.error(res.msg);
                    return;
                }
                if (res.data) {
                    let recordList = res.data;
                    this.drugData.data1 = recordList.filter(item => item.iType == 1);
                    this.drugData.data2 = recordList.filter(item => item.iType == 2);
                }
                if( !type ) {
                    this.$message.success(res.msg);
                } else {
                    this.$message.success('新增保存成功！')
                }
                
            }).catch(err => {
                this.saveLoading1 = false
                this.saveLoading2 = false
                this.$emit('upDateLoading', 'saveLoading')
                console.log(err)
            })
        },
        tableRowClassName ({ row, rowIndex }) {
            row.index = rowIndex;
        },
        onClickAdd (type) {
            if(!this.patientInfo.sId) {
                this.$message.warning('请选择患者数据！')
                return
            }
            this.addType = type
            // if(!this.doctorOptions.length) {
            //     this.getDoctorsData()
            // }
            this.drugData[`data${type}`].unshift({
                "iType": type + '',
                "sPatientId": this.patientInfo.sId,
                "dMeterTime": new Date(),
                "sBlood": '',
                "sDrugName": '',
                "fDose": '',
                "sDoctorName": this.userInfo.sName,
                "sDoctorNo": this.userInfo.sNo,
                "sDoctorId": this.userInfo.sId
            });
            this.$nextTick(() => {
                document.querySelector(`.mark${type} input`).focus()
            })
        },
        changeDocdor(index){
            let target = this.doctorOptions.find(item =>
                item.sValue === this.drugData.data1[index].sDoctorId 
            )
            this.drugData.data1[index].sDoctorName = target.sName
            this.drugData.data1[index].sDoctorNo = target.userNo
            
        },
        //给药
        changesDoctor(index) {
            let target = this.doctorOptions.find(item =>
                item.sValue === this.drugData.data2[index].sDoctorId
            )
            this.drugData.data2[index].sDoctorName = target.sName
            this.drugData.data2[index].sDoctorNo = target.userNo
        },
        onClickDelete (index, type, row) {
            if (!row.sId) {
                this.drugData[`data${type}`].splice(index, 1)
                return;
            }
            // 提示删除 todo 本地的可以不提示
            ElMessageBox.confirm('您确定要删除该行吗？', '提示', {
                type: 'warning'
            }).then(() => {
                Api.delDrugDelivery({
                    iVersion: this.drugData[`data${type}`][index].iVersion,
                    sId: this.drugData[`data${type}`][index].sId
                }).then(res => {
                    if (res.success) {
                        this.drugData[`data${type}`].splice(index, 1);
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(err => {
                    console.log(err);
                })
            }).catch(() => {
            });
        },
       
        getData () {
            Api.drugDeliveryPage({
                condition: {
                    sPatientId: this.patientInfo.sId
                },
                page: {
                    pageSize: 500,
                    pageCurrent: 1
                }
            }).then(res => {
                if (!res.success) {
                    return
                }
                let recordList = res.data.recordList || []
                if (recordList) {
                    this.drugData.data1 = recordList.filter(item => item.iType == 1);
                    this.drugData.data2 = recordList.filter(item => item.iType == 2);
                }
            }).catch(err => {
                console.log(err)
            })
        }
    },
    mounted () {
        // window.winFn.setRightsData({
        //     DrugSave: '/ApricotReportInjectBloodDrugSave/',
        //     DrugDel: '/ApricotReportInjectBloodDrugDel/',
        // }, this, 'rights');
    }
}
</script>
<style lang="scss" scoped>
.g-content {
    .el-date-editor.el-input {
        width: 185px;
    }
    .c-item-title {
        height: 32px;
        line-height: 36px;
        text-align: center;
        margin: 0px;
        font-size: 15px;
        color: var(--el-color-primary);
    }

    .c-first {
        &.el-table--border {
            border-top: none;
        }
    }
}
.c-buttons {
    padding: 5px 15px;
}
</style>
