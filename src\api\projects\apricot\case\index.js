import request, {
    baseURL, stringify
} from '$supersetUtils/request'

export default {
    getCaseMngPatients(data) {
        return request({
            url: baseURL.apricot + '/case/mng/find/patients',
            method: 'post',
            data
        })
    },

    getCaseMngPatientsRow(params) {
        return request({
            url: baseURL.apricot + '/case/mng/find/patients/row',
            method: 'post',
            params
        })
    },

    getPatientInfo(data) {
        return request({
            url: baseURL.apricot + '/patient/info/find/patients',
            method: 'post',
            data
        })
    },

    getPatientInfoById,

    getPatientBriefById,
    // 病例收藏表操作接口 : Personal Collection Controller
    addCollection(data) {
        return request({
            url: baseURL.apricot + '/personal/collection/add',
            method: 'post',
            data
        })
    },
    editCollection(data) {
        return request({
            url: baseURL.apricot + '/personal/collection/edit',
            method: 'post',
            data
        })
    },
    delCollection(params) {
        return request({
            url: baseURL.apricot + '/personal/collection/del',
            method: 'post',
            params
        })
    },
    // 通过id查找标签 
    getsCollectionById(params) {
        return request({
            url: baseURL.apricot + '/personal/collection/findById',
            method: 'post',
            params
        })
    }
}

export function getPatientBriefById(params) {
    return request({
        url: baseURL.apricot + '/patient/info/brief',
        method: 'post',
        params
    })
}
// 
export function getPatientReportById(params) {
    return request({
        url: baseURL.apricot + '/process/quickBrowse',
        method: 'post',
        params
    })
}
export function getPatientInfoById(params) {
    return request({
        url: baseURL.apricot + '/patient/info/find/patients/row',
        method: 'post',
        data: stringify(params)
    })
}