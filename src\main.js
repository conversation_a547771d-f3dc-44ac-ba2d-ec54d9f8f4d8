import { computed, createApp } from 'vue';
import { ElLoading } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus';

// import 'virtual:windi.css'
import 'virtual:windi-utilities.css'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
// 引入样式
import '@/resource/style/reset.scss'
import '@/resource/style/element-reset.scss'


/*  旧系统样式 */
import '$supersetResource/style/old/basic.scss'
import '$supersetResource/style/old/frame_work_reset.scss'
import '$supersetResource/style/old/global.scss'
import '$supersetResource/style/old/vue_components.scss'
import '$supersetResource/style/old/el-icon/icon.css'
import '$supersetResource/style/old/projects/apricot/case/public.scss'

import '$supersetResource/style/old/quill.core.css'
import '$supersetResource/style/old/quill.snow.css'
import '$supersetResource/style/old/editor.scss'

import '$supersetIcon/superset/iconfont.css'
import '$supersetIcon/superset/iconfont.js'

import App from './App.vue';
const app = createApp(App);

import 'dayjs/locale/zh-cn';
import dayjs from "dayjs";

dayjs.locale("zh-cn");

import ElementPlus from 'element-plus';
app.use(ElementPlus)


import contextmenu from "v-contextmenu";
import "v-contextmenu/dist/themes/default.css";

app.use(contextmenu)

import baseComp from './components/export';
baseComp(app); 

import { initDirectives } from '@/utils'
initDirectives(app)

import moment from 'moment'
import momentLocal from '$plugins/moment-local-zhcn'
window.moment = moment
momentLocal()

// router
import router from './router/index';
app.use(router);

// vuex
import store from '@/store';
app.use(store);

import { getEventbus } from '@/utils'
const $eventbus = getEventbus()

app.config.globalProperties.$eventbus = $eventbus


// app.use(ElementPlus)

app.config.globalProperties.$loading = ElLoading.service
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$confirm = ElMessageBox.confirm

app.config.globalProperties.$auth = new Proxy(
  computed(() => {
    return store.getters['user/buttonRightMap']
  }), {
  get: function (obj, prop) {
    return prop in obj.value ? obj.value[prop] : false;
  }
})

/*全局组件注册-----------------------------------------------------------*/

// /*引入*/
// import DragAdjust from '$plugins/layout/DragAdjust.vue'
// import DrawerAside from '$plugins/layout/DrawerAside.vue'
// import MenuPanel from '$plugins/layout/MenuPanel.vue'
// import ElMenuLevel from '$plugins/layout/ElMenuLevel.vue'
// import TransitionSlide from '$plugins/layout/TransitionSlide.vue'
// import VueQuillEditor from '$plugins/other/quillEditor'



// /*注册*/
// app.component('DragAdjust', DragAdjust)
// app.component('DrawerAside', DrawerAside)
// app.component('MenuPanel', MenuPanel)
// app.component('ElMenuLevel', ElMenuLevel)
// app.component('TransitionSlide', TransitionSlide)
// app.component('QuillEditor', VueQuillEditor) 

import '$supersetResource/js/plugins' //js插件
import '$supersetResource/js/common.js'

// 保证注入会自动解包这个计算属性。这将会在 Vue 3.3 后成为一个默认行为，而我们暂时在此告知此项配置以避免后续升级对代码的破坏性。
app.config.unwrapInjectedRef = true;

app.mount('#app');
