import request, { stringify, baseURL } from '$supersetUtils/request'

let _base =  '/report'
_base = baseURL.apricot

export function queryByKey(params) {
  return request({
      url: _base + '/system/userConfig/queryByKey',
      method: 'post',
      params
  })
}

export function queryGlobalConfigByModuleIdAndKey(params) {
  return request({
      url: _base + '/system/userConfig/queryGlobalConfigByModuleIdAndKey',
      method: 'post',
      params
  })
}

export function queryByModuleId(params) {
  return request({
      url: _base + '/system/userConfig/queryByModuleId',
      method: 'post',
      params
  })
}

export function saveConfig(data) {
  return request({
      url: _base + '/system/userConfig/saveConfig',
      method: 'post',
      data
  })
}
