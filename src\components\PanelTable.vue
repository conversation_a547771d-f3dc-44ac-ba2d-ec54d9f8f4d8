<template>
    <div class="c-table-content">
        <div class="c-table">
            <slot name="content"></slot>
        </div>
        <div class="c-pagination scope-device-page pagination">
            <slot name="footer"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "PanelTable",
};
</script>
<style lang="scss" scoped>
.scope-device-page {

  :deep(.el-pagination__total),
  :deep(.el-pagination__sizes){
    float: left;

    }
}

.c-table-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .c-table{
        flex: 1;
        overflow: hidden;
    }
    .c-pagination{
        background-color: white;
        border-left: 1px solid #edf0f6;
        border-right: 1px solid #edf0f6;
        border-bottom: 1px solid #edf0f6;
        text-align: right;
    }
}
.c-custom-home .c-pagination{
    border: 1px solid white;
    border-radius: 0px 0px 6px 6px;
}
</style>
