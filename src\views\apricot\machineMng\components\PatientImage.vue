<template>
    <div class="m-flexLaout-ty">
        <!-- <div class="c-checkGroup">
            <el-checkbox v-model="enableRebuild" :true-label="1" :false-label="0">可重建</el-checkbox>
            <el-checkbox v-model="disableRebuild" :true-label="1" :false-label="0">不可重建</el-checkbox>
        </div> -->
        <div class="g-flexChild">
            <PatientStudy :params="patientInfo" :enableRebuild="enableRebuild" :disableRebuild="disableRebuild"
                :isHideElement="true" :rights="rights" @setImageSum="setImageSum" class="c-inner"></PatientStudy>
        </div>
    </div>
</template>

<script setup>
import PatientStudy  from '$supersetViews/apricot/case/report/PatientStudy.vue';

const props = defineProps({
    patientInfo: {
        type: Object,
        default: new Object()
    }
}) 

// 图像权限对象 传值
const {appContext: {config: {globalProperties: { $auth }}}} = getCurrentInstance();
const rights = ref({
    deleteSeries: !!$auth['report:machine:deleteSeries'],
})



const enableRebuild = ref(1);

const disableRebuild = ref(1);

const imageSum = ref(0);

const setImageSum = (sum) => {
    imageSum.value = sum;
}

// 暴露组件属性或方法可供父组件使用
defineExpose({
    imageSum
})

</script>

<style lang="scss" scoped>
:deep(.c-inner .c-box-01) {
    padding: 0;
}
</style>