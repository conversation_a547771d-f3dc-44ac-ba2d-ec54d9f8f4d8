<template>
    <div class="container">
        <div class="echarts" ref="echartDom" :id="`echarts${onlyValue}`" :style="style"></div>
        <div class="tip" v-if="isNotData">
            <svg class="iconfont item-icon" aria-hidden="true">
                <use xlink:href="#icon-tongjifenxi"></use>
            </svg>
            <span>暂无</span>
        </div>
    </div>
</template>
<script>
export default {
  name: 'Echart'
}
</script>
<script setup>
    import { debounce } from 'lodash-es';
    import * as echarts from "echarts"
import { computed } from 'vue';
    const props = defineProps({
        style: {
            type: Object,
            default: () => {
                return {
                    width: '100%',
                    height: '380px'
                }
            }
        },
        options: {
            type: Object,
            default: () => {
                return {}
            }
        },
        grid: {
            type: Object,
            default: () => {
                return {
                    top: 10,
                    left: 2,
                    right: 2,
                    bottom: 0,
                    containLabel: true,
                };
            },
        },
    })

    let chart = reactive({})
    let timer = ref(null)

    const store = useStore()

    const isCollapse = computed(() => {
        return store.getters.collapse
    })

    const isNotData = computed(() => {
        if (props.options.xAxis) {
            if (props.options.xAxis.data.length == 0) {
                return true
            }
        }
        return false
    })

    watch(
        () => isCollapse,
        () => {
            timer.value = setTimeout(() => {
                chart.resize(); //页面大小变化后Echarts也更改大小
                clearTimeout(timer.value);
                timer.value = null;
            }, 300);
        },
        {
            deep: true,
        }
    )

    watch(
        () => props.options,
        () => {
            nextTick(() => {
                updateChart()
            })

        }, { immediate: true, deep: true }
    )

    onMounted(
        () => {
        initChart();
        window.addEventListener(
            'resize',
            debounce(() => {
                chart.resize()  //页面大小变化后Echarts也更改大小
            }, 200)
        );
    })

    onBeforeUnmount(() => {
        clearTimeout(timer.value);
        timer.value = null;
    })

    const echartDom = ref(null)

    const initChart = () => {
        chart = echarts.init(echartDom.value)
        echartDom.value?.removeAttribute('_echarts_instance_')
    }

    const updateChart = () => {
        nextTick(() => {
            chart.setOption({
                grid: props.grid,
                ...props.options,
            })
        })
    }
</script>
<style lang="scss" scoped>
.container {
    position: relative;
    width: 100%;
    height: 100%;
    .tip {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        .item-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 10px;
        }
        > span {
            font-size: 12px;
        }
    }
}
</style>
