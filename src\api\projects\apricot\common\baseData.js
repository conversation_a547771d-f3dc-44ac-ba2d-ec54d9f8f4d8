import request, { baseURL, stringify} from '$supersetUtils/request'

export default {
    getHospitalData,
    getMachineRoomData,
    getItemData
}
// 获取院区数据
export function getHospitalData(data) {
    return request({
        url: baseURL.apricot + '/hospital/district/findAllHospital',
        method: 'POST',
        data
    })
};
// 获取机房数据
export function  getMachineRoomData(data) {
    return request({
        url: baseURL.apricot + '/machine/room/findMachineRoomList',
        method: 'POST',
        data
    })
}
// 获取项目数据
export function  getItemData(data) {
    return request({
        url: baseURL.apricot + '/item/findItemList',
        method: 'POST',
        data
    })
}

// 获取全部用户
export function getAllUsers(data) {
    return request({
        url: baseURL.system + '/user/query/queryAllUser',
        method: 'POST',
        data
    })
}

// 按用户类型查询用户
export function queryUserListByUserType(params) {
    return request({
        url: baseURL.system + '/user/queryUserListByUserType',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取报告医生
export function queryAllReportDoctor (data) {
    return request({
        url: baseURL.system + '/user/query/queryAllReportDoctor',
        method: 'POST',
        data
    })
}