<template>
    <el-cascader ref="myCascader"
        v-model="bindValue"
        :props="defaultProps"
        :options="options"
        size="small"
        clearable
        placeholder=""
        @change="changeSelect"></el-cascader>
</template>
<script>
import ApiProjectSet from '$supersetApi/projects/apricot/appointment/projectSet.js'
export default {
    props: {
        value: {
            type: [Array, Object, String],
            default: function () {
                return ''
            }
        },
        props: {
            type: Object,
            default: function () {
                return {}
            }
        },
        workStation: {
            type: Object,
            default: function () {
                return {}
            }
        },
        isMachimeModule: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            defaultProps: {
                children: 'childs',
                label: 'sName',
                value: 'sId',
                checkStrictly: true,
                expandTrigger: 'hover'
            },
            options: [],
            ids: {
                sHospitalDistrictId: '',
                sDeviceTypeId: '',
                sRoomId: '',
                sItemId: ''
            },
            idNames: ['sHospitalDistrictId', 'sDeviceTypeId', 'sRoomId', 'sItemId'],
            // conditionIds: [],  // 查询条件需要的sId数据
            // inputVal: []  // input的值
        }
    },
    mounted () {
        this.getItemTree()
        this.setDefaultProps()
    },
    computed: {
        bindValue: {  // 去掉了data里面的on,使用计算属性
            get () {  // get时返回 value值;
                return this.value;
            },
            set (val) { // set时发送 input 事件
                this.$emit('input', val);
                this.$nextTick(() => {
                    this.getTreeId(this.$refs.myCascader.getCheckedNodes()[0])
                })
            }
        }
    },
    watch: {
        workStation: {
            handler (val) {
                this.setIdsValue(val)
            },
            immediate: true
        }
    },
    methods: {
        setIdsValue (val) {
            let temp = [val.districtId, val.deviceTypeId, val.roomId].filter(item => item);
            let target = [];
            if (this.options.length && this.isMachimeModule) {
                this.options.forEach(item => {
                    if (temp[0] && item[this.idNames[0]] === temp[0]) {
                        target.push(item.sId);
                        if (temp[1] && item.childs) {
                            item.childs.forEach(item1 => {
                                if (temp[1] === item1[this.idNames[1]]) {
                                    target.push(item1.sId)
                                    if (temp[2] && item1.childs) {
                                        item1.childs.forEach(item2 => {
                                            if (temp[2] === item2[this.idNames[2]]) {
                                                target.push(item2.sId)
                                            }
                                        })
                                    }
                                }
                            })
                        }
                    }
                })
            }
            if (!this.isMachimeModule && temp[0]) {
                target.push(temp[0])
            }
            this.$emit('input', target);
            this.$nextTick(() => {
                this.$refs.myCascader && this.getTreeId(this.$refs.myCascader.getCheckedNodes()[0])
            })
            // this.bindValue.set(target)
        },
        // 获取 data 里面的项目、机房、设备、院区的id，非树id
        getTreeId (node = {}) {
            if(!node) {
                return
            }
            this.ids = {
                sHospitalDistrictId: '',
                sDeviceTypeId: '',
                sRoomId: '',
                sItemId: ''
            }
            // this.conditionIds = [];
            // this.inputVal = [];
            this.recursionTree(node);
            this.$emit('getIds', this.ids);
        },
        // 获取ids
        recursionTree (node = {}) {
            if (!node || !Object.keys(node).length) return;
            const idx = node.data.iType - 1;
            this.ids[this.idNames[idx]] = node.data[this.idNames[idx]];
            // this.conditionIds.unshift(node.data[this.idNames[idx]]);
            // this.inputVal.unshift(node.data.sId);
            // 退出
            if (!node.parent) {
                return;
            }
            this.recursionTree(node.parent)
        },
        // 获取项目树
        getItemTree () {
            ApiProjectSet.getItemTreeData({}).then(res => {
                if (res.success) {
                    this.options = res.data || [];
                    this.setIdsValue(this.workStation)
                    this.$emit('emitOptions', this.options);
                }
            })
        },
        setDefaultProps () {
            let _this = this
            Object.assign(_this.defaultProps, _this.props)
        },
        changeSelect(){
            this.$refs.myCascader.dropDownVisible = false  //选完关闭下拉框
            this.$nextTick(() =>{
                 this.$emit('change')
            })
        }
    }
}
</script>
<style rel="stylesheet/less">
    .el-cascader-panel .el-radio{
        width: 100%;
        height: 100%;
        z-index: 10;
        position: absolute;
        top: 10px;
        right: 10px;
    }
    .el-cascader-panel .el-radio__input{
        visibility: hidden;
    }
    .el-cascader-panel .el-cascader-node__postfix {
        top: 10px;
    }
</style>
