<template>
    <div class="c-content">
        <div class="c-action-01 c-box">
            <slot name="header"></slot>
        </div>
        <div class="c-action-02 c-box">
            <slot name="content"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "Panel",
};
</script>

<style lang="scss" scoped>
.c-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-box{
        background-color: white;
        border-radius: 4px;
    }
    .c-action-01, .c-action-02{
        // box-shadow: #0000001a 0px 1px 2px 0px;
    }
    .c-action-01 {
        padding: 10px;
        margin-bottom: 10px;
    }
    .c-action-02 {
        flex: 1;
        padding: 0 1px;
        display: flex;
        flex-direction: column;
        overflow: auto;
    }
}
</style>
