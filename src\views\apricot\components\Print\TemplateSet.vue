<template>
    <div class="c-flex-context c-container">
        <!-- 新增、编辑 -->
        <el-dialog :title="dialogTitle"
            v-model="dialogVisible"
            append-to-body
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog">
            <div class="flex">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="100px">
                    <el-col :span="24">
                        <el-form-item label="模板类型：" prop="iClassify">
                            <el-select v-model="editLayer.form.iClassify"
                                ref="sDeviceTypeId"
                                clearable
                                :disabled="isDisable"
                                placeholder="请选择"
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.classifyOptions"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="设备类型：" prop="sDeviceTypeId">
                            <el-select v-model="editLayer.form.sDeviceTypeId"
                                ref="sDeviceTypeId"
                                clearable
                                :disabled="isDisable"
                                placeholder="请选择"
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.deviceTypeOptions"
                                    :key="item.sId"
                                    :label="item.sDeviceTypeName"
                                    :value="item.sId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="内容类型：" prop="iType">
                            <el-select v-model="editLayer.form.iType"
                                ref="sDeviceTypeId"
                                :disabled="isDisable"
                                clearable
                                placeholder="请选择"
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.typeOptions"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="模板名称：" prop="sName">
                            <el-input v-model="editLayer.form.sName"
                                clearable
                                placeholder="模板名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="fileName" label="上传文件">
                            <el-input v-model="editLayer.form.fileName" placeholder="上传文件">
                                <template #append>
                                    <el-upload
                                        ref="upload"
                                        action="#"
                                        :on-progress="handleUploadFile"
                                        :show-file-list="false">
                                        <el-button-icon-fa class="text-color" type="primary" icon="el-icon-upload">上传</el-button-icon-fa>
                                    </el-upload>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="默认模版：" prop="iIsDefaulted">
                            <el-select v-model="editLayer.form.iIsDefaulted"
                                clearable
                                placeholder="请选择"
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.defaultedOptions"
                                    :key="item.sValue"
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer>
                <div  class="dialog-footer">
                    <el-button-icon-fa icon="el-icon-check" type="primary" @click="mxDoSaveData('refEditLayer')" :loading="editLayer.loading">保存</el-button-icon-fa>
                    <el-button-icon-fa  @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                </div>
            </template>
        </el-dialog>
        <div class="c-search">
            <div>
                <el-button-icon-fa type="primary" 
                icon="el-icon-plus"
                @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxGetTableList">刷新</el-button-icon-fa> 
            </div>
            <div class="search">
                
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <!-- @row-click="onClickRow" -->
                <el-table :data="tableData"
                    v-if="reRender"
                    id="itemTable"
                    ref="mainTable"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                        <el-table-column show-overflow-tooltip
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort">
                            <template v-slot="scope">
                                <template v-if="item.sProp === 'action'">
                                    <el-button link
                                        size="small" 
                                        type="primary"
                                        @click="handleEdit(scope.row)">编辑
                                        <template #icon>
                                            <Icon name="el-icon-edit" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button link
                                        size="small"
                                        type="primary"
                                        @click="handleDownLoad(scope.row)">下载
                                        <template #icon>
                                            <Icon name="el-icon-download" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                    
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button-icon-fa size="small" link icon="el-icon-set-up" @click="onClickSet(scope.row)">
                                        书签
                                    </el-button-icon-fa>
                                    <!-- <i class="el-icon-set-up i-icon"
                                        title="书签设置"
                                        @click="onClickSet(scope.row)">&nbsp;书签</i> -->
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button link size="small" 
                                        @click="onClickDel(scope.row)"
                                        >删除
                                        <template #icon>
                                            <Icon name="el-icon-delete" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                   
                                </template>
                                <template v-if="item.sProp === 'iIsDefaulted'">
                                    <span :class="{'c-enable':scope.row.iIsDefaulted }">{{scope.row.iIsDefaulted ? '是' : '否'}}</span>
                                </template>
                                <template v-else>
                                    {{scope.row[`${item.sProp}`]}}
                                </template>
                            </template>
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>
        <!-- <el-dialog title="书签设置"
            v-model="d_imageParams_v"> -->
        <el-dialog append-to-body
            title="书签设置"
            v-model="d_imageParams_v"
            :close-on-click-modal="false"
            class="my-dialog"
            size="small"
            top="10vh"
            width="1000">
            <h4 style="padding: 0 10px 5px;">
                <span>模板名称：</span>
                <span>{{templateName}}</span>
            </h4>
            <el-table :data="imageParamsData"
                border height="65vh">
                <el-table-column type="index"
                    label="序号"
                    align="center"
                    width="60">
                </el-table-column>
                <el-table-column property="sTagName"
                    label="书签名称"
                    width="150"
                    show-overflow-tooltip></el-table-column>
                <el-table-column property="iImgWidth"
                    label="区域宽度(cm)"
                    width="150"
                    show-overflow-tooltip>
                    <template v-slot="{row}">
                        <el-input-number v-if="row.isEdit"
                            v-model="row.iImgWidth"
                            controls-position="right"
                            size="small"
                            :min="0"
                            style="width:100%;"></el-input-number>
                        <span v-else>{{row.iImgWidth}}</span>
                    </template>
                </el-table-column>
                <el-table-column property="iImgHeight"
                    label="区域高度(cm)"
                    width="150"
                    show-overflow-tooltip>
                    <template v-slot="{row}">
                        <el-input-number v-if="row.isEdit"
                            v-model="row.iImgHeight"
                            controls-position="right"
                            size="small"
                            :min="0"
                            style="width:100%;"></el-input-number>
                        <span v-else>{{row.iImgHeight}}</span>
                    </template>
                </el-table-column>
                <el-table-column property="iValueType"
                    label="值类型"
                    width="150"
                    show-overflow-tooltip>
                    <template v-slot="{row}">
                        <el-select v-if="row.isEdit"
                            v-model="row.iValueType"
                            size="small"
                            clearable
                            style="width: 100%;">
                            <el-option v-for="item in optionsLoc.paramsTypeOptions"
                                :key="item.sValue"
                                :label="item.sName"
                                :value="item.sValue"></el-option>
                        </el-select>
                        <span v-else>{{sValueTypeText(row.iValueType)}}</span>
                    </template>
                </el-table-column>
                <el-table-column property="sValue"
                    label="值"
                    show-overflow-tooltip>
                    <template v-slot="{row}">
                        <el-input v-if="row.isEdit"
                            v-model="row.sValue"
                            size="small"></el-input>
                        <span v-else>{{row.sValue}}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column property="sJsonParams"
                    label="附加参数(json)"
                    show-overflow-tooltip>
                    <template v-slot="{row}">
                        <el-input v-if="row.isEdit"
                            v-model="row.sJsonParams"
                            size="small"></el-input>
                        <span v-else>{{row.sJsonParams}}</span>
                    </template>
                </el-table-column> -->
                <el-table-column property="actions"
                    label="操作"
                    align="center"
                    width="90">
                    <template v-slot="{row, $index}">
                        <el-button v-if="!row.isEdit"
                            type="primary"
                            link
                            size="small"
                            @click="onEditParams(row, $index)">编辑
                            <template #icon>
                                <Icon name="el-icon-edit" color="">
                                </Icon>
                            </template>
                        </el-button>
                        <el-button-icon-fa v-else
                            type="primary"
                            link
                            icon="fa fa-save"
                            @click="onSaveParams(row, $index)"
                            style="padding:0;">保存</el-button-icon-fa>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>
    </div>
</template>
<script>
import { getOptionName } from '$supersetResource/js/tools.js'
import { deepClone } from '$supersetUtils/function'
import Api from '$supersetApi/projects/apricot/system/templateSet.js'
import projectSetApi from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import { templateEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { getToken } from '$supersetUtils/auth'
export default {
    name: 'TemplateSet',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            loading: false,
            dialogTitle:'新增',
            dialogVisible: false,
            configTable: [
                {
                    sProp: 'sName',
                    sLabel: '模板名称',
                    sAlign: 'left',
                    sMinWidth: '100px',
                },{
                    sProp: 'sClassify',
                    sLabel: '模板类型',
                    sAlign: 'left',
                    sWidth: '100px',
                },
                {
                    sProp: 'sDeviceTypeName',
                    sLabel: '设备类型 ',
                    sAlign: 'center',
                    sWidth: '100px',
                },
                {
                    sProp: 'sType',
                    sLabel: '内容类型',
                    sAlign: 'center',
                    sWidth: '100px',
                },
            
                {
                    sProp: 'sFilePath',
                    sLabel: '模板文件',
                    sAlign: 'left',
                    sMinWidth: '150px',
                },
                {
                    sProp: 'iIsDefaulted',
                    sLabel: '默认模板',
                    sAlign: 'center',
                    sWidth: '80px',
                },
                {
                    sProp: 'action',
                    sLabel: '操作',
                    sAlign: 'center',
                    sWidth: '280px',
                    sFixed: 'right'
                }],
            rules: {
                sName: [{ required: true, message: '不能为空' },
                {
                    validator: (rule, value, callback) => {
                        let illegalStrings = ['/', '\\', '.', '<', '>', '|', '?', '*', ' '];
                        let splitArr = value.split('') || [];
                        let temp = splitArr.find(item => illegalStrings.includes(item));
                        if (temp) {
                            // callback(new Error(`名称不支持'/', '\\', '.', '<', '>', '|', '?', '*'以及空格！`))
                            callback(new Error(`名称不支持 '/ \\ . < > | ? *' 以及空格字符！`))
                        }
                        callback()
                    },
                    trigger: 'change'
                }],
                iClassify: [{ required: true, message: '不能为空' }],
                iType: [{ required: true, message: '不能为空' }],
                sDeviceTypeId: [{ required: true, message: '不能为空' }],
                sFileType: [{ required: true, message: '不能为空' }],
            },
            optionsLoc: {
                classifyOptions: templateEnum.classifyOptions,
                typeOptions: templateEnum.typeOptions,
                defaultedOptions: templateEnum.defaultedOptions,
                deviceTypeOptions: [],
                sFileTypeOptions: [
                    {
                        sValue: 'pdf'
                    },
                    {
                        sValue: 'docx'
                    }
                ],
                paramsTypeOptions: [
                    {
                        sName: '字符串',
                        sValue: 1
                    },
                    {
                        sName: '超链接',
                        sValue: 2
                    },
                    {
                        sName: '接口',
                        sValue: 3
                    },
                    {
                        sName: '图像',
                        sValue: 4
                    },
                    {
                        sName: '属性解析',
                        sValue: 5
                    },
                ]
            },
            isDisable: false,
            reRender: true,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                    iIsDefaulted: 0
                }
            },
            isUpdateFile: false,
            d_imageParams_v: false,
            imageParamsData: [],
            fileBase64: '',
            templateName: ''
        }
    },
    watch: {
        'editLayer.form.iVersion': {
            handler () {
                this.isDisable = this.editLayer.form.iVersion ? true : false;
            }
        }
    },
    methods: {
         // 新增
        handleAdd() {
            this.dialogTitle = '新增'
            this.dialogVisible = true
            // this.mxOpenDialog(1, 'no-title');
            this.resetForm()
        },
        // 关闭弹框
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.actionState = 2
            this.editLayer.form = Object.assign({},row)
        },
        // handleSave() {
        //     this.editLayer.loading = true
        //     let params = Object.assign({},this.editLayer.form)
        //     this.$refs['refEditLayer'].validate( (valid) =>{
        //         if(valid) {
        //           this.saveData(params)  
        //           return
        //         }
        //         this.editLayer.loading = false
        //     })
        // },
        sValueTypeText (val) {
            let text = getOptionName(val, this.optionsLoc.paramsTypeOptions)
            return text
        },
        onEditParams (row, index) {
            this.imageParamsData[index]['isEdit'] = true;
            row.iImgWidth = row.iImgWidth || undefined;
            row.iImgHeight = row.iImgHeight || undefined;
        },
        onSaveParams (row, index) {
            let jsonData = { ...row };
            // console.log(row);
            Api.editImageTag(jsonData).then(res => {
                if (res.success) {
                    this.$message.success('保存成功！');
                    this.imageParamsData[index]['isEdit'] = false;
                    return
                }
                this.$message.error(res.msg);
            }).catch(() => {

            })
        },
        // 重置表单数据
        resetForm () {
            this.mxOpenDialog(1, 'no-title');
            this.fileBase64 = ''
            let timeout = setTimeout(() => {
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        // 重置查询条件
        DoReset () {
            this.condition = {};
        },
        inputFocus (e) {
            e.srcElement.blur();
            setTimeout(() => {
                this.$refs.myTextarea.focus();
            }, 300)
        },
        // 上传文件
        handleUploadFile (e, file) {
            // console.log(file)
            const fileObj = file.raw;
            this.getBase64(fileObj).then(res => {
                this.editLayer.form['fileName'] = file.name;
                let data = res.split(';base64,')[1];
                this.fileBase64 = data;
                // console.log(res);
            });
        },
        // 然后自定义一个方法，用来把图片内容转为base64格式，
        // imgResult就是base64格式的内容了。
        // 转为base64字符串要调用h5特性中的FileReader这个api,
        // 但是这个api不能return，所以用promise封装一下。
        getBase64 (file) {
            return new Promise(function (resolve, reject) {
                let reader = new FileReader();
                let imgResult = "";
                reader.readAsDataURL(file);
                reader.onload = function () {
                    imgResult = reader.result;
                };
                reader.onerror = function (error) {
                    reject(error);
                };
                reader.onloadend = function () {
                    resolve(imgResult);
                };
            })
        },
        // 获取sValue
        getName (arr, val) {
            let temp = arr.find(item => val == item.sValue);
            return temp ? temp.sName : null
        },
        // 获取设备名称
        getDeviceName (val) {
            let temp = this.optionsLoc.deviceTypeOptions.find(item => val == item.sId);
            return temp ? temp.sDeviceTypeName : null
        },
        /**
         * 保存数据
         */
        saveData (data) {
            let params = deepClone(data);
            params.sClassify = this.getName(this.optionsLoc.classifyOptions, params.iClassify);
            params.sType = this.getName(this.optionsLoc.typeOptions, params.iType);
            params.sDeviceTypeName = this.getDeviceName(params.sDeviceTypeId);
            params.fileBase64 = this.fileBase64;
            params.iIsDefaulted = params.iIsDefaulted ? params.iIsDefaulted : 0;
            if (this.actionState == 1) {
                // 新增
                if (!this.fileBase64) {
                    this.$message.warning('请上传模板文件');
                    this.editLayer.loading = false;
                    return;
                }
                Api.addTemplate(params).then(res => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.dialogVisible = false
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        this.fileBase64 = '';
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return;
            }
            // 修改
            let jsonData = { ...params };
            Api.editTemplate(jsonData).then(res => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.dialogVisible = false
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.mxOpenDialog(1, 'no-title');
                    this.mxGetTableList();
                    this.fileBase64 = '';
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },
        // 点击行
        onClickRow (row, isCancel = false, id = 'sId') {
            this.fileBase64 = ''
            this.editLayer.selectedItem = { ...row };
            if (isCancel === true && this.editLayer.selectedItem[id] === row[id]) {
                this.$refs.mainTable.setCurrentRow()
                this.editLayer.selectedItem = {}
            }
            this.mxOpenDialog(4, '111');
            Object.keys(this.editLayer.form).map(key => {
                if ([null, 0, false, ''].includes(this.editLayer.form[key])) {
                    this.editLayer.form[key] = undefined;
                }
            })
            this.isUpdateFile = false;
        },
        // 下载
        handleDownLoad (row) {
            // let a = document.createElement('a');
            // // a.download = this.items[index].sUploadName;
            // // console.log(row.file)
            // let file = row.sFilePath;
            // let downURL = `${window.configs.urls.apricot}/template/download?path=${file}`
            // a.href = downURL;
            // a.setAttribute('target', '_blank');
            // document.body.append(a);
            // a.click();
            // a.remove();
            let file = row.sFilePath;
            let downURL = `${window.configs.urls.apricot}/template/download?path=${file}`
            window.location.href = downURL;
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【${row.sName}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                Api.delTemplate({ sId: row.sId, iVersion: row.iVersion, sFilePath: row.sFilePath }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        if (this.editLayer.form.sKey && this.editLayer.form.sKey === row.sKey) {
                            this.mxOpenDialog(1, 'no-title')
                        }
                        this.mxGetTableList();
                        this.mxOpenDialog(1, 'no-title');
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        onClickSet (row) {
            // console.log(row);
            this.d_imageParams_v = true;
            this.templateName = row.sName;
            this.queryTagByTemplateId(row.sId);
        },
        // 获取表格数据
        getData (params) {
            const p = Object.assign(params);
            delete p.orders;
            Api.getTemplateData(p).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data || [];
                    this.isUpdateFile = false;
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        },
        // 查询模板图像书签
        queryTagByTemplateId (sTemplateId) {
            this.imageParamsData = [];
            Api.queryTagByTemplateId({ sTemplateId }).then(res => {
                // console.log(res);
                if (res.success) {
                    this.imageParamsData = res.data || [];
                    return;
                }
                this.$message.error(res.msg);
            })
        },
        // 获取设备下拉
        getDeviceOptions () {
            projectSetApi.getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.optionsLoc.deviceTypeOptions = res?.data || [];
                    return;
                }
                this.$message.error(res.msg);
            })
        },
    },
    mounted () {
        this.mxOpenDialog(1, 'no-title');
        this.getDeviceOptions();
    },
};
</script>
<style lang="scss" scoped>

.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px 10px 0;
    .c-search {
        padding-bottom: 10px;
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
    .c-enable {
        color: #67c23a;
        font-weight: bold;
    }
}
.i-icon {
    cursor: pointer;
    font-size: 12px;
}
.text-color {
    color: var(--el-color-primary) !important;
    &:active {
        color: white !important;
    }
}
</style>
