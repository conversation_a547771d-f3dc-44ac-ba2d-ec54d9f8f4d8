<template>
  <div class="content panel-view">
    <div class="left-box">
      <div class="header">
        <div class="relative flex gap-3 h-full items-center">
          <div class="inline-block flex-1">
            <SearchList v-model="condition" :isLang="false" v-model:list="keyOptions" :loading="loading"
              storageKey="SystemManageDictIndex" @clickSearch="mxDoSearch" @changeSearch="mxDoSearch">
            </SearchList>
          </div>
          <div class="action-button-group">
            <el-button-icon-fa type="primary" icon="el-icon-plus" @click="mxOpenDialog(1, '新增字典')">新增</el-button-icon-fa>
          </div>
        </div>
      </div>
      <div class="body">
        <PanelTable>
          <template v-slot:content>
            <div class="table-layout-fixed">
              <!-- <div class="table-title">
                              <el-button-icon-fa size="small" :disabled="0 && !auth['/bsuser/dict/pageList']" ticon="el-icon-refresh" type="primary" class="i-primary" :loading="loading" @click="mxDoRefresh">刷新</el-button>
                              <el-button size="small" :disabled="0 && !auth['/bsuser/dict/add']" ticon="el-icon-plus" @click="mxOpenDialog(1, '新增字典')">新增</el-button-icon-fa>
                          </div> -->
              <div class="table-container">
                <el-table v-loading="loading" @row-click="onClickRow" :data="tableData" highlight-current-row
                  ref="mainTable" class="dict-table"  border stripe height="100%"
                  style="width: 100%">
                  <el-table-column prop="dictName" sortable show-overflow-tooltip label="字典名称"
                    align="center"></el-table-column>
                  <el-table-column prop="dictCode" sortable show-overflow-tooltip label="字典代码"
                    align="center"></el-table-column>
                  <el-table-column fixed="right" label="操作" align="center" >
                    <template v-slot="scope">
                      <el-button title="编辑" @click="mxOnClickRowAction(scope.row, 2, '编辑')" type="primary" plain
                        size="small" link>
                        编辑
                        <template #icon>
                          <Icon name="el-icon-edit" size="14"></Icon>
                        </template>
                      </el-button>
                      <el-divider direction="vertical"></el-divider>
                      <el-button title="删除" @click="mxOnClickRowDel(scope.row, `确定要删除【 ${scope.row.dictName} 】吗？`)" size="small" link
                        plain class="color-red">
                        删除
                        <template #icon>
                          <Icon name="el-icon-delete" size="14"></Icon>
                        </template>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </template>
          <template v-slot:footer>
            <el-pagination class="pagination" @size-change="onSizeChange" @current-change="onCurrentChange"
              :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
              layout="total, prev, pager, next" :total="page.total">
            </el-pagination>
          </template>
        </PanelTable>
      </div>
    </div>
    <div class="right-box">

      <DictValue :dictId="editLayer.selectedItem.dictId" :dictName="editLayer.selectedItem.dictName"></DictValue>
      <!-- 新增-编辑弹窗  -->
      <el-dialog :close-on-click-modal="false" append-to-body :title="editLayer.playerText" v-model="editLayer.visible"
        :destroy-on-close="true" width="670px" class="t-default">
        <el-form ref="refEditLayer" :model="editLayer.form" :rules="rules">
          <el-row  >
            <el-col :span="24">
              <el-form-item prop="dictCode" label="字典代码" class="item-block" labelWidth="90px">
                <el-input v-model="editLayer.form.dictCode"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="dictName" label="字典名称" class="item-block" labelWidth="90px">
                <el-input v-model="editLayer.form.dictName"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div>
            <el-button @click="mxDoSaveData('refEditLayer')" :loading="editLayer.loading" type="primary"
              class="i-primary">
              <template #icon>
                <Icon name="el-icon-document-checked" size="14"></Icon>
              </template>
              保存
            </el-button>
            <el-button ticon="el-icon-close" class="i-warning" @click="editLayer.visible = false">
              <template #icon>
                <Icon name="el-icon-close" size="14"></Icon>
              </template>
              关闭
            </el-button>
            
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { ElMessage } from 'element-plus'
import DictValue from './components/DictValue.vue'

import ApiDictionary from '@/api/projects/apricot/system/dictionary';
import apiRoles from "@/api/auth/roles";

import TableCURDMixin from "@/resource/js/common/mixin/TableCURDMixin";
import TableAPIMixin from "@/resource/js/common/mixin/TableAPIMixin";

export default {
  name: 'DictionarySet',
  components: {
    DictValue
  },
  mixins: [TableCURDMixin, TableAPIMixin],
  data() {
    return {
      condition: {
        // "dictCode": "",
        // // "dictId": "",
        "dictName": "",
        // remark: '',
        // "items": [
        //   // {
        //   //   "dictId": "",
        //   //   "itemCode": "",
        //   //   "itemId": "",
        //   //   "itemName": "",
        //   //   "remark": ""
        //   // }
        // ]
      },
      keyOptions: [
        { label: '字典名称', prop: 'dictName', componentType: 'el-input', width: '50%' },
        { label: '字典代码', prop: 'dictCode', componentType: 'el-input', width: '50%' },
      ],
      ApiTable: {
        pageList: ApiDictionary.queryDictPage,
        add: ApiDictionary.createDict,
        update: ApiDictionary.editDict,
        del: ApiDictionary.deleteDict,
      },
      rules: {
        dictName: [{ required: true, message: '必填' }],
        dictCode: [{ required: true, message: '必填' }],
      },
    }
  },
  mounted() {

  },
  methods: {
    callBackSave(params) {
      if (this.actionState == 1) {
        // 新增
        this.ApiTable.add(params).then((res) => {
          this.editLayer.loading = false;
          if (res.success) {
            ElMessage.success(res.msg);
            this.editLayer.visible = false;
            this.mxGetTableList();
            return;
          }
          ElMessage.error(res.msg);
        }).catch(() => {
          this.editLayer.loading = false;
        })
      } else {
        if (params.dictValueList === null) {
          delete params.dictValueList;
        }
        // 编辑
        this.ApiTable.update(params).then((res) => {
          this.editLayer.loading = false;
          if (res.success) {
            ElMessage.success(res.msg);
            this.editLayer.visible = false;
            this.mxGetTableList();
            return;
          }
          ElMessage.error(res.msg);
        }).catch(() => {
          this.editLayer.loading = false;
        })
      }
    },
    callBackDel(item) {
        this.ApiTable.del({dictId: item.dictId}).then((res) =>{
            if(res.success) {
                ElMessage.success(res.msg);
                this.mxGetTableList();
                return;
            }
            ElMessage.error(res.msg);
        }).catch((e) => {
            console.log(e)
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  height: 100%;
  overflow: hidden;

  .left-box {
    display: flex;
    flex-direction: column;
    background: white;
    height: 100%;
    width: 50%;
    margin-right: 10px;

    .header {
      padding: 5px 10px;
    }

    .body {
      flex: 1;
      display: flex;
      height: 100%;
      overflow: hidden;
    }
  }

  .right-box {
    position: relative;
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}
</style>
