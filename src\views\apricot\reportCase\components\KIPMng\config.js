export default {
    logSearchSetList: [
        {
            sProp: 'dCommitTimeSt',
            sLabel: '提交日期从',
            iLayourValue: 3,
            sInputType: 'date-picker',
            iCustom: 1
        },
        {
            sProp: 'dCommitTimeEd',
            sLabel: '到',
            iLayourValue: 3,
            sInputType: 'date-picker',
            iCustom: 1
        },
        {
            sProp: 'sCommitDocName',
            sLabel: '提交人',
            sInputType: 'text',
            iCustom: 1
        },
        {
            sProp: 'sReceiverName',
            sLabel: '接收人',
            sInputType: 'text',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sInputType: 'text',
        },
    ],
    
    logTableSetList: [
        {
            sProp: 'sCommitDocName',
            sLabel: '提交人',
            sMinWidth: '100px'
        },
        {
            sProp: 'sReceiverName',
            sLabel: '接收人',
            sMinWidth: '100px'
        },
        {
            sProp: 'sSmsContent',
            sLabel: '短信内容',
            sMinWidth: '200px'
        },
        {
            sProp: 'iSendState',
            sLabel: '发送状态',
            sMinWidth: '100px'
        },
        {
            sProp: 'iReturnResult',
            sLabel: '发送结果',
            sMinWidth: '100px'
        },
        {
            sProp: 'sUnsendReason',
            sLabel: '不发送原因',
            sMinWidth: '100px'
        },
        {
            sProp: 'sReportKeywords',
            sLabel: '报告关键字',
            sMinWidth: '100px'
        },
        {
            sProp: 'sReturnMsg',
            sLabel: '平台返回信息',
            sMinWidth: '120px'
        },
        {
            sProp: 'dCommitTime',
            sLabel: '提交时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'dSendTime',
            sLabel: '发送时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'dReturnTime',
            sLabel: '返回时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'iReceiverType',
            sLabel: '接收角色',
            sMinWidth: '100px'
        },
    ]
}