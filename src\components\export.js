const modulesFiles = import.meta.globEager('./*.vue');
const folderModulesFiles = import.meta.globEager('./*/*.vue');
const pluginsModulesFiles = import.meta.globEager('./plugins/*/*.vue');
// console.log(folderModulesFiles)
// 注册
export default (app) => {
  for (const path in folderModulesFiles) {
    const componentName = folderModulesFiles[path].default.name;
    app.component(componentName, folderModulesFiles[path].default);
  }
  for (const path in modulesFiles) {
    const componentName = modulesFiles[path].default.name;
    app.component(componentName, modulesFiles[path].default);
  }
  for (const path in pluginsModulesFiles) {
    const componentName = pluginsModulesFiles[path].default.name;
    app.component(componentName, pluginsModulesFiles[path].default);
  }
};
