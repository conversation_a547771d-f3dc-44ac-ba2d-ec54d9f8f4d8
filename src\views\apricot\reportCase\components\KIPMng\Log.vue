<template>
    <LayoutTable v-bind="$attrs">
        <template v-slot:header>
            <SearchList v-model:modelValue="condition"
                :list="searchListConfig"
                :optionData="optionsLoc"
                :iModuleId="iModuleId"
                storageKey="KIPLogSearch"
                @changeSearch="mxDoSearch">
                <template v-slot:dCommitTimeSt>
                    <el-date-picker v-model="condition.dCommitTimeSt"
                        type="date"
                        @change="changeCommitTime"
                        style="height:100%;">
                    </el-date-picker>
                </template>
                <template v-slot:dCommitTimeEd>
                    <el-date-picker v-model="condition.dCommitTimeEd"
                        type="date"
                        @change="changeCommitTime"
                        style="height:100%;">
                    </el-date-picker>
                </template>
                <template v-slot:sCommitDocName>
                    <el-select v-model="condition.sCommitDocName"
                        placeholder=" "
                        clearable
                        filterable
                        allow-create
                        default-first-option
                        style="width:100%"
                        @change="mxDoSearch">
                        <el-option v-for="(item, index) in optionsLoc[`sCommitDocNameOptions`]"
                            :key="index"
                            :label="item.userName"
                            :value="item.userName">
                        </el-option>
                    </el-select>
                </template>
            </SearchList>
        </template>
        <template v-slot:action>
            <div class="c-action flex space-between">
                <div>
                    <el-button @click="onSendClick">
                        <template #icon>
                            <Icon name="el-icon-position">
                            </Icon>
                        </template>
                        发送短信
                    </el-button>
                </div>
                <div style="display: flex; align-items: center;">
                    <el-button @click="onClickReset">
                        <template #icon>
                            <Icon name="el-icon-refresh-left">
                            </Icon>
                        </template>
                        重置
                    </el-button>

                    <el-button v-auth="'report:inquisition:save'"
                        type="primary"
                        :loading="loading"
                        @click="mxDoSearch">
                        <template #icon>
                            <Icon name="el-icon-search"
                                color="white"></Icon>
                        </template>
                        查询
                    </el-button>
                </div>
            </div>
        </template>
        <template v-slot:content>
            <el-table-extend v-loading="loading"
                :data="tableData"
                ref="mainTable"
                stripe
                :row-class-name="mxRowClassName"
                @row-click="handleRowClick"
                @sort-change="mxOnSort"
                @selection-change="handleSelectionChange"
                highlight-current-row
                height="100%"
                style="width: 100%"
                :iModuleId="iModuleId"
                storageKey="KIPLogTable">
                <el-table-column fixed
                    type="selection"
                    label="选择"
                    prop="_select"
                    align="center"
                    width="50"
                    :selectable="checkSelectable">
                </el-table-column>
                <el-table-column type="index"
                    label="序号"
                    prop="_index"
                    align="center"
                    width="60"
                    class-name="tableColOverflowVisible"></el-table-column>
                <el-table-column v-for="item in patientTableConfig"
                    :show-overflow-tooltip="item.sProp !== 'img'"
                    :key="item.index"
                    :prop="item.sProp"
                    :label="item.sLabel"
                    :fixed="item.sFixed"
                    :align="item.sAlign"
                    :width="item.sWidth"
                    :min-width="item.sMinWidth"
                    :sortable="(!!item.iSort) ? 'custom' : false"
                    :isSortable="item.isSortable"
                    :sOrder="item.sOrder"
                    :iIsHide="item.iIsHide"
                    :column-key="item.sSortField ? item.sSortField : null">
                    <template v-slot="{ row, $index }">
                        <template v-if="item.sProp.slice(0, 1) === 'd'">
                            {{ mxFormatterDate(row[item.sProp]) }}
                        </template>
                        <template v-else-if="item.sProp === 'iSendState'">
                            <span :class="{'i-green': row[item.sProp] == 2}">{{ iSendStateObj[row[item.sProp]] }}</span>
                        </template>
                        <template v-else-if="item.sProp === 'iReturnResult'">
                            {{ iReturnResultObj[row[item.sProp]] }}
                        </template>
                        <template v-else-if="item.sProp === 'iReceiverType'">
                            {{ receiverTypeOptions.filter(option => row[item.sProp] == option.sValue)[0]?.sName }}
                        </template>
                        <template v-else>
                            {{ row[item.sProp] }}
                        </template>
                    </template>
                </el-table-column>
            </el-table-extend>
        </template>
        <template v-slot:footer>
            <el-pagination background
                @size-change="onSizeChange"
                @current-change="onCurrentChange"
                :current-page="page.pageCurrent"
                :page-sizes="mxPageSizes"
                :pager-count="5"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next"
                :total="page.total">
            </el-pagination>
        </template>
    </LayoutTable>
</template>

<script>
// 接口、外部函数、混入
import { mixinTable, mixinTableInner } from '$supersetResource/js/projects/apricot/index.js'

import configs from './config.js'
import { receiverTypeOptions } from '$supersetResource/js/projects/apricot/enum.js';
import { deepClone } from '$supersetUtils/function'
import { getReportAboveDrData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'

import { kipLogSaveSearch, kipLogSendSms } from '$supersetApi/projects/apricot/reportCase/kip.js';

export default {
    mixins: [mixinTable, mixinTableInner],
    data () {
        return {
            iModuleId: 6, // 报告管理标识 ，eName: 'REPORT'， 在mixinPrintPreview混合模块中调用
            isMixinDynamicGetTableHead: true,   // 是否动态获取表头
            condition: {
                dCommitTimeSt: new Date(),
                dCommitTimeEd: new Date(),
            },
            searchListConfig: configs.logSearchSetList,
            optionsLoc: {
                sCommitDocNameOptions: []
            },
            patientTableConfig: configs.logTableSetList,
            receiverTypeOptions: receiverTypeOptions,
            iSendStateObj: {
                0: '不发送',
                1: '未发送',
                2: '已发送'
            },
            iReturnResultObj: {
                0: '失败',
                1: '成功'
            },
            multipleSelection: []
        }
    },
    async mounted () {
        const doctorsData = await getReportAboveDrData()
        this.optionsLoc.sCommitDocNameOptions = doctorsData['reportDoctors'] // 获取报告
        this.mxDoSearch();
    },
    methods: {
        changeCommitTime () {
            this.mxDoSearch();
        },
        onClickReset () {
            this.condition = this.$options.data().condition;
            this.mxDoSearch();
        },
        checkSelectable (row) {
            return row.iSendState == 0 ? false : true;
        },
        // 表格多选
        handleSelectionChange (rows) {
            this.multipleSelection = rows;
        },
        handleRowClick () { },
        // 加载状态
        loadFindTip (text) {
            return this.$loading({
                lock: true,
                text: text || '文件生成中...',
                background: 'rgba(255, 255, 255, 0.5)',
                customClass: 'my-loading',
            });
        },
        // 发送短信
        async onSendClick () {
            const len = this.multipleSelection.length;
            if (!len) {
                this.$message.warning('请至少勾选一个患者数据！');
                return;
            }
            let loading = this.loadFindTip('发送中...');
            var tipMsg = '发送成功！';
            let isStopSend = false;
            for (let i = 0; i < len; i++) {
                if (isStopSend) {
                    // 停止循环；
                    loading.close();
                    return;
                }
                let jsonData = this.multipleSelection[i];
                await kipLogSendSms(jsonData).then((res) => {
                    if (res.success) {
                        tipMsg = res.msg;
                        return;
                    }
                    this.$message({
                        message: `患者：${this.multipleSelection[i].sName}；${res.msg}，停止发送`,
                        type: 'error',
                        showClose: true,
                        duration: 5000,
                    });
                    isStopSend = true;
                }).catch((err) => {
                    console.log(err);
                    isStopSend = true;
                });
            }
            loading.close();
            this.$message.success(tipMsg);
            this.mxGetTableList();
        },
        getData (obj) {
            const params = deepClone(obj);
            let dCommitTimeSt = params.condition.dCommitTimeSt;
            params.condition.dCommitTimeSt = this.mxFormateOneDayStart(dCommitTimeSt);

            // 当选择了结束时间，转换成23：59：59
            let dCommitTimeEd = params.condition.dCommitTimeEd;
            params.condition.dCommitTimeEd = this.mxFormateOneDayEnd(dCommitTimeEd);
            kipLogSaveSearch(params).then((res) => {
                this.tableData = [];
                if (res.success) {
                    this.tableData = res.data?.recordList || [];
                    this.page.total = res.data.countRow;
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                    return;
                }
                this.loading = false;
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.space-between {
    justify-content: space-between;
}
.i-green {
    color: var(--el-color-primary);
}
:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
    background-color: var(--el-color-info-light-8);
}
</style>