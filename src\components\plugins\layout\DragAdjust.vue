<template>
    <div class="My-dragAdiust"
        :class="dragAdjustData.type"
        @dragAdjustDragen="updateSize">
        <div class="g-adjust-item"
            v-for="(item, index) in panelConfig"
            :key="index"
            :class="{'g-flexChild': item.isFlexible, 'item-border': hasDragBorder}"
            :style="sizeStyles[index]"
            :data-index="index"
            :data-minSize="item.minSize || minSize"
            :data-maxSize="item.maxSize || 999999">
            <div class="c-content"
                ref="c-content">
                <slot :name="item.name"></slot>
            </div>
            <button v-if="dragAdjustData.type==='t-x'" class="g-line">
              <!-- <div class="DCaret-box x-dir">
                <DCaret class="DCaret"/>
              </div> -->
                <div class="i-x"
                    v-for="(item, index) in 2"
                    :key="index"></div>
            </button>
            <button v-else class="g-line">
              <!-- <div class="DCaret-box ">
                <DCaret class="DCaret"/>
              </div> -->

                <div v-for="(item, index) in 2"
                    :key="index"
                    class="i-y"></div>

            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DragAdjust',
    props: {
        dragAdjustData: {
            type: Object,
            default: () => ({})
        },
        hasDragBorder: {
            type: Boolean,
            default: false
        }
    },
    components: {
    },
    data () {
        return {
            msg: 'heheda',
            minSize: 5,
            panelConfig: null //调整后的值，存储到localStorage
        }
    },
    computed: {
        sizeStyles () {
            let panelConfig = this.panelConfig  
            if (!panelConfig) return []
            let sideDes = this.dragAdjustData.type == 't-x' ? 'width' : 'height'
            return panelConfig.map(item => {
              let sizeVal = String(item["size"])
              let sizeStr = /%/.test(sizeVal) ? sizeVal : ( sizeVal.replace(/px/g, '') + 'px' )
              const style = {}
              if (!item.isFlexible) {
                style[sideDes] = sizeStr 
              }
              if (item._hidden) {
                style['display'] = 'none'
              }
              return style
            })
        },
        localStorageKey () {
            return window.localStorageRootKey["dragAdjustData"] + '_' + this.dragAdjustData.localStorageKey
        }
    },
    watch: {
        dragAdjustData: {
            handler () {
                // // 调用组件改变了 size 重新获取新 size
                // this.dragAdjustData.panelConfig.forEach((item, index) => {
                //     this.panelConfig[index]['size'] = item.size
                // });
                // this.setLocalPanelConfig(this.panelConfig)
                this.init();
            },
            // immediate: true
        }
    },
    
    methods: {
        updateSize () {
            let dragenData = window.dragAdjustDragenData
            //避免数值为0
            let size = Math.max(dragenData["value"], this.minSize)
            //改变dragSizes
            this.panelConfig[dragenData.index]["size"] = size
            //将尺寸（dragSizes）存储到localStorage
            this.setLocalPanelConfig(this.panelConfig)
        },
        getLocalPanelConfig () {
            let localStorageKey = this.localStorageKey
            let data = localStorage[localStorageKey]

            if (data) {
                return JSON.parse(data)
            } else {
                return null
            }

        },
        setLocalPanelConfig (val) {
            let localStorageKey = this.localStorageKey

            localStorage[localStorageKey] = JSON.stringify(val)
        },
        init () {
            /*创建时，访问一下本地数据看看有没有存储，如果有，则赋值给this.panelConfig, 否则使用prop传入的数据进行赋值*/
            //props传入的配置数据
            let panelConfig = this.dragAdjustData.panelConfig || []
            // //本地存储key
            // let localStorageKey = this.localStorageKey
            //本地数据
            let localPanelConfig = this.getLocalPanelConfig()
            /*debugger*/
            if (!localPanelConfig) {
                this.setLocalPanelConfig(panelConfig)
                this.panelConfig = panelConfig
            } else {
                // 合并配置项
                this.panelConfig = panelConfig.map(item => {
                  const target = localPanelConfig.find(i => i.name === item.name)
                  return {...item, ...target, _hidden: item._hidden}
                })
            }
        }
    },
    created () {
        this.init()
    }
}
</script>

<style lang="scss">
.My-dragAdiust {
    height: 100%;
    overflow: hidden;
    display: flex;
    // background-color: #ddd;

    &.t-x {
        flex-direction: row;
    }
    &.t-y {
        flex-direction: column;
    }
    > .g-adjust-item {
        position: relative;

        > .c-content {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            // background-color: #fff;
            overflow: auto;
        }
        &.i-full {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 100% !important;
            height: 100% !important;
            z-index: 2;
            overflow: hidden;
            .g-line {
                display: none !important;
            }
            .c-content {
                margin-top: 0 !important;
                margin-right: 0 !important;
            }
        }
    }
    > div:not(.g-flexChild) {
        flex-shrink: 0;
    }
    > .g-flexChild {
        flex: 1;
    }
    &.t-x {
        > .g-adjust-item:not(.g-flexChild) > .c-content {
            margin-right: 10px;
        }
        > .g-adjust-item.g-flexChild ~ .g-adjust-item > .c-content {
            margin-right: auto;
            margin-left: 10px;
        }
    }
    &.t-y {
        > .g-adjust-item:not(.g-flexChild) > .c-content {
            margin-bottom: 10px;
        }
        > .g-adjust-item.g-flexChild ~ .g-adjust-item > .c-content {
            margin-bottom: auto;
            margin-top: 10px;
        }
    }
    /*button---------------------------------------------------------*/
    > .g-adjust-item.g-flexChild > button {
        display: none;
        

    }
    > .g-adjust-item:not(.g-flexChild) > button {
        position: absolute;
        padding: 0;
        border: none;
        background-color: transparent;
        box-sizing: border-box;
        overflow: hidden;
        z-index: 1;
    }
    > .g-adjust-item:not(.g-flexChild) > button:active {
        background-color: #eee;
        box-shadow: none;
    }
    &.t-x {
        > .g-adjust-item:not(.g-flexChild) > button {
            height: 100%;
            top: 0;
            right: 0;
            width: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: visible;
            .i-x {
                height: 30px;
                width: 1px;
                background: #aaa;
                &:last-child {
                    margin-left: 2px;
                }
            }
        }
        > .g-adjust-item.g-flexChild ~ .g-adjust-item > button {
            left: 0;
        }
        > .g-adjust-item:not(.g-flexChild) > button:hover {
            cursor: col-resize;
        }
        > .g-adjust-item:not(.g-flexChild) > button:before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            margin-left: 0;
            width: 10px;
            z-index: -1;

            
        }
        >.g-adjust-item:not(.g-flexChild).item-border > button:before{
            border-left: 1px solid #eee;
            border-right: 1px solid #eee;
            box-sizing: border-box;
        }
    }
    &.t-y {
        > .g-adjust-item:not(.g-flexChild) > button {
            left: 0;
            bottom: 0;
            width: 100%;
            height: 10px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: visible;

            .i-y {
                height: 1px;
                width: 30px;
                background: #aaaaaa;
                &:last-child {
                    margin-top: 2px;
                }
            }
        }
        > .g-adjust-item.g-flexChild ~ .g-adjust-item > button {
            top: 0;
        }
        > .g-adjust-item:not(.g-flexChild) > button:hover {
            cursor: row-resize;
        }
        > .g-adjust-item:not(.g-flexChild) > button:before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            margin-top: 0;
            width: 100%;
            height: 8px;
            z-index: -1;
        }
        >.g-adjust-item:not(.g-flexChild).item-border > button:before{
            border-top: 1px solid #eee;
            border-bottom: 1px solid #eee;
            box-sizing: border-box;
        }
    }
    > .g-adjust-item:not(.g-flexChild) > button.s-dragging {
        z-index: 2;
    }
    /*---------------------------------------------------------button*/
    .DCaret-box {
      width: 50px;
      height: 14px;
      display: flex;
      background: var(--el-color-primary);
      border: 1px solid #eaeaea;
      border-radius: 39px;
      box-sizing: border-box;
      justify-content: center;
      .DCaret {
        color: var(--theme-bg);
      }
      &.x-dir {
        width: 14px;
        height: 50px;
        .DCaret {
          transform: rotate(90deg);
        }
      }
    }
}
</style>
