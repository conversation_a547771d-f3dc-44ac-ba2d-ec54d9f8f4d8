<template>
    <div class="m-flexLaout-ty">
        <el-tabs v-model="activeName" class="g-flexChild demo-tabs" :style="styleVar">
            <el-tab-pane v-if="configValue.tabConsultForm" name="first">
                <template #label>
                    <span>问诊信息</span>
                </template>
                <ConsultTemplate :deviceTypeKey="'sRoomId'" :isConsultModule="true"></ConsultTemplate>
            </el-tab-pane>
            <el-tab-pane v-if="configValue.tabHistoryReport" name="second">
                <template #label>
                    <span>历史报告</span>
                    <span class="i-count">({{ historyRef?.tableData.length }})</span>
                </template>
                <HistoryReport ref="historyRef" :iModuleId="iModuleId" :historyRelatedCheck="configValue.historyReportMatchConditions" 
                    :isShowConfigBtn="configValue.isShowConfigBtn" @changeActiveTab="changeActiveTabName"></HistoryReport>
            </el-tab-pane>
            <el-tab-pane v-if="configValue.tabScannerData" name="third">
                <template #label>
                    <span>扫描资料</span>
                    <span class="i-count">({{ scanImageRef?.serverImages.length }})</span>
                </template>
                <ScanImage :isVisible="activeName === 'third'" ref="scanImageRef" :rights="rights"></ScanImage>
            </el-tab-pane>
            <!-- <el-tab-pane v-if="configValue.tabClinicData" name="fourth">
                <template #label>
                    <span>临床资料</span>
                    <span class="i-count">({{ thirdLinkListRef?.linkList.length }})</span>
                </template>
                <ThirdLinkList ref="thirdLinkListRef"></ThirdLinkList>
            </el-tab-pane> -->
        </el-tabs>

        <ThirdLinkList v-if="configValue.tabClinicData"  ref="thirdLinkListRef" 
            style="position: absolute; top: 11px; left: 325px; cursor: pointer;"
            :style="{left: thirdLinkDomLeft}"></ThirdLinkList>

        <div v-show="activeName === 'first'" class="my-consult-doctors"></div>
        <div class="c-bottom">
            <div>
                <ApplyListInfo v-if="$auth['report:inquisition:applyOrder']"  :patientInfo="patientInfo" buttonName="电子申请单" type="primary" plain 
                    class="mr-2.5"></ApplyListInfo>
                <!-- 呼叫 -->
                <el-dropdown v-if="$auth['report:inquisition:call']"
                    ref="callDropdownRef" 
                    @visible-change="onCallDropdownClick">
                    <el-button-icon-fa type="primary" plain icon="fa fa-call-fill">
                        <span style="margin-right: 5px;">呼叫</span>
                        <Icon name="el-icon-arrow-up"></Icon>
                    </el-button-icon-fa>
                    <template #dropdown>
                        <el-dropdown-menu v-if="showCalledBtn?.length">
                            <template v-for="(item, index) in showCalledBtn" :key="index">
                                <el-dropdown-item :type="item.isCalled ? 'success' : 'primary'" plain 
                                    :loading="item.loading"
                                     _icon="fa fa-volume-up"
                                    @click="handleQuickCall(item, index)">
                                        <span :class="item.isCalled?'icon-green':''"> <i class="fa fa-call-fill"></i>
                                        {{ item.buttonName }}</span>
                                        <i v-if="item.isCalled" class="el-icon-check icon-green" style="margin-left: 5px;"></i>
                                </el-dropdown-item>
                            </template>
                        </el-dropdown-menu>
                        <div v-else style="width: 100px;">
                            <el-empty :image-size="80" description="未配置按钮" />
                        </div>
                    </template>
                </el-dropdown>
                <!-- 打印按钮集合 -->
                <ReportPrintBtn v-if="$auth['report:inquisition:print']" :isPopup="true"
                    :propParams="{ patient: patientInfo, isBatch: false, idKey: 'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }"
                    :buttonMsg="{ plain:true, type: 'primary' }"
                    style="margin-right: 10px;" ></ReportPrintBtn>

                <el-button-icon-fa v-if="!patientInfo.iIsRegister"
                    class="btn-item" 
                    type="primary" 
                    icon="fa fa-register"
                    plain
                    @click="onSignIn">签到</el-button-icon-fa>
                <!--  :disabled="patientInfo.iFlowState > 6" -->
                <el-button-icon-fa v-else
                    class="btn-item"
                    icon="fa fa-calendar-fork"
                    @click="onSignInCancel">取消签到</el-button-icon-fa>
            </div>
            <div>
                <!-- 问诊信息保存 -->
                <span class="my-consult-save" :disabled="activeName !== 'first'"></span>
            </div>
        </div>

        <Call v-model:dialogVisible="isDialogVisible" :patientInfo="patientInfo"
            :iModule="{ iModuleId: iModuleId, iModuleName: '问诊管理' }" @closeDialog="closeCallDialog"></Call>
    </div>
</template>

<script setup>
// 组件
import { ElMessage, ElLoading, ElMessageBox  } from 'element-plus';
import ConsultTemplate from './ConsultTemplate.vue';
import HistoryReport from './HistoryReport.vue';
import ScanImage from '$supersetViews/apricot/components/ScanImage.vue';
import ThirdLinkList from '$supersetViews/apricot/components/ThirdLinkList.vue';
import ReportPrintBtn from '$supersetViews/apricot/components/ReportPrintBtn.vue';
import ApplyListInfo from '$supersetViews/apricot/components/ApplyListInfo.vue'    // 申请单
import Call from '$supersetViews/apricot/components/Call.vue' // 呼叫设置

import { appointmentSignIn, appointmentSignInCancel } from '$supersetApi/projects/apricot/appointment/index.js'    
import { getCalledBtn, handleCallAction } from '$supersetResource/js/projects/apricot/call.js'

const props = defineProps({
    callBtnArray: {
        type: Object,
        default: new Object()
    },
    callConfigBtnShow: {
        type: Boolean,
        default: false
    }
})
const emits = defineEmits(['updateTableInfo'])
// 扫描资料权限对象 传值
const {appContext: {config: {globalProperties: { $auth }}}} = getCurrentInstance();
const rights = ref({
    deleteFile: !!$auth['report:inquisition:deleteFile'],
    uploadFile: !!$auth['report:inquisition:uploadFile'],
    manualUpload: !!$auth['report:inquisition:manualUpload'],
})

const showCalledBtn = computed(() => props.callBtnArray)

const iModuleId = inject('iModuleId', '3'); // 3  文字管理标识 ，eName: 'INQUISITION'， 在mixinPrintPreview混合模块中调用


// 患者信息
const patientInfo = inject('patientInfo', {});

// 配置
const configValue = inject('configValue', {});

const isDialogVisible = ref(false);

// 当前激活tab
const activeName = ref('first');
const styleVar = computed(()=> {
    return {
        "--ativeTabColor": configValue.value.tabBgColor
    }
})
// 获取组件实例
const historyRef = ref(null);
const scanImageRef = ref(null);
// const thirdLinkListRef = ref(null);
const callDropdownRef = ref(null);

const thirdLinkDomLeft = computed(() => {
    const tempValue = configValue.value;
    const tabList = [tempValue.tabConsultForm, tempValue.tabHistoryReport, tempValue.tabScannerData]
    let len = tabList.filter(item => item === true).length;
    return (len * 107) + 'px';
})

// 根据配置是否优先显示报告系统修改tab activeName
const changeActiveTabName = (len) => {
    const isHistoryPanelFirst = configValue.value.isHistoryPanelFirst;
    if (!isHistoryPanelFirst) {
        return
    }
    // 设置历史报告优先
    if (len) {
        activeName.value = 'second';
        return
    }
    activeName.value = 'first';
}

const onCallDropdownClick= (val) => {
    if(val && !props.callConfigBtnShow) {
        ElMessage.warning('请切换到问诊工作站！');
        callDropdownRef.value.handleClose();
        return
    }
    if(val && !patientInfo.value.sId) {
        ElMessage.warning('请选择患者数据');
        callDropdownRef.value.handleClose();
        return
    }
    if(val) {
        getRowCalled()
    }
    
}

async function getRowCalled() {
    showCalledBtn.value.map( item=>{
        item.isCalled = false
    })
    const calledBtnData = await getCalledBtn(patientInfo.value.sId)
    showCalledBtn.value.forEach(item =>{
        if (calledBtnData.includes(item.buttonCode)) {
            item.isCalled = true
        }
    })
}
// 关闭呼叫弹窗
const closeCallDialog = () => {
    isDialogVisible.value = false
}
// 打开呼叫弹窗
const onOpenCall = () => {
    isDialogVisible.value = true
}

// 点击呼叫按钮
 async function handleQuickCall(data, index) {
    if(!patientInfo.value.sId){
        ElMessage.warning('请选择患者数据！');
        return
    }
    if (data.buttonCode == 0) {
        onOpenCall()
        return
    }
    const row = patientInfo.value
    data.loading = true
    showCalledBtn.value[index] = data
    let jsonData = {
        callBtnCode: data.buttonCode,
        captionsId: "",
        patientInfoId: row.sId,
        stationId: data.stationId,
        sysModuleCode: data.moduleId,
    }
    const isCalled = await handleCallAction(jsonData)
    data.loading = false
    if(isCalled.isCalled) {
        data.isCalled = true
    }
    emits('updateTableInfo',row.sId, row.index)
    showCalledBtn.value[index] = data
}
// 签到
function onSignIn () {
    const row = patientInfo.value
    let jsonData = {
        patientInfoId: row.sId,
        autoSign: 0
    }
    let loading = ElLoading.service({
        lock: true,
        text: '正在加载中，请稍等',
        background: 'rgba(0, 0, 0, 0.1)'
    });
    appointmentSignIn(jsonData).then(res => {
        loading.close()
        if (res.success) {
            ElMessage.success(res.msg);
            emits('updateTableInfo', row.sId, row.index);
            return
        }
        ElMessage.error(res.msg);
    }).catch(err => {
        loading.close()
        console.log(err)
    })
}
// 取消签到
function onSignInCancel () {
    ElMessageBox.confirm('确认取消签到，是否继续？', '提示', { type: 'warning' }).then(() => {
        const row = patientInfo.value
        let jsonData = {
            patientInfoId: row.sId,
        }
        let loading = ElLoading.service({
            lock: true,
            text: '正在加载中，请稍等',
            background: 'rgba(0, 0, 0, 0.1)'
        });
        appointmentSignInCancel(jsonData).then(res => {
            loading.close()
            if (res.success) {
                ElMessage.success(res.msg);
                emits('updateTableInfo', row.sId, row.index);
                return
            }
            ElMessage.error(res.msg);
        }).catch(err => {
            loading.close()
            console.log(err)
        })
    }).catch()
}


</script>
<style lang="scss" scoped >
.c-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    padding: 15px 10px 5px 5px;
    border-top: 1px solid #ddd;
    box-sizing: border-box;
}

:deep(.demo-tabs) {
    >.el-tabs__header {
        margin: 0 0 0 0px
    }
    >.el-tabs__content {
        padding: 10px;
        background-color: var(--ativeTabColor);
    }
    
}

.i-count {
    display: inline-block;
    width: 18px;
}

:deep(.my-consult-doctors) {
    padding: 0 10px;
    .container .main-form-list .grid-box {
        padding-top: 10px;
    }
    .container .main-form-list .grid-box .cell-box {
        padding: 0;
    }
    .el-form--inline .el-form-item {
        margin-bottom: 10px;
    }
    .el-form-item--default .el-form-item__content {
        padding-right: 10px;
    }
}
</style>
