import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'
export default {
    // 部门组织操作接口 : Department Controller
    // 部门数据
    getDepartmentData() {
        return request({
            url: baseURL.system + '/department/find/tree',
            method: 'POST'
        })
    },
    // 人员信息操作接口 : Person Controller
    getPersonData(data) {
        return request({
            url: baseURL.system + '/person/find/page',
            method: 'POST',
            data
        })
    },
    // 根据部门编号查询员工
    getPersonByDepartmentData(params) {
        return request({
            url: baseURL.system + '/person/find/departmentid',
            method: 'POST',
            params
        })
    },
    // 用户业务权限配置: User Set Controller
    // 获取设备数据
    getFindSet(params) {
        return request({
            url: baseURL.apricot + '/user/set/find/set',
            method: 'POST',
            params
        })
    },

    // 保存可查询设备类型
    saveDeviceType(data) {
        return request({
            url: baseURL.apricot + '/user/set/device/type/save',
            method: 'POST',
            data
        })
    },

    // 删除可查询设备类型
    delDeviceType(params) {
        return request({
            url: baseURL.apricot + '/user/set/device/type/del',
            method: 'POST',
            params
        })
    },

    // 保存业务角色
    saveBussinessRole(data) {
        return request({
            url: baseURL.apricot + '/user/set/bus/role/save',
            method: 'POST',
            data
        })
    },

    // 删除业务角色
    delBussinessRole(params) {
        return request({
            url: baseURL.apricot + '/user/set/bus/role/del',
            method: 'POST',
            params
        })
    },

    // 保存报告书写角色
    saveReportWriter(data) {
        return request({
            url: baseURL.apricot + '/user/set/report/writer/save',
            method: 'POST',
            data
        })
    },

    // 删除报告书写角色
    delReportWriter(params) {
        return request({
            url: baseURL.apricot + '/user/set/report/writer/del',
            method: 'POST',
            params
        })
    },

    // 保存打印控制
    savePrint(data) {
        return request({
            url: baseURL.apricot + '/user/set/print/save',
            method: 'POST',
            data
        })
    },

    // 删除打印控制
    delPrint(params) {
        return request({
            url: baseURL.apricot + '/user/set/print/del',
            method: 'POST',
            params
        })
    },
}

// 按钮自定义显示配置存储
export function setButtonSave(data) {
    return request({
        url: baseURL.apricot + '/user/set/button/set/save',
        method: 'POST',
        data
    })
}
// 按钮自定义显示配置查询
export function setButtonFind(params) {
    return request({
        url: baseURL.apricot + '/user/set/button/set/find',
        method: 'POST',
        params
    })
}