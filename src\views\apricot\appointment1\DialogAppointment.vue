<template>
    <el-dialog v-model="visible"
    draggable
    fullscreen
    :close-on-click-modal="false"
    @open="dialogOpen"
    @closed="dialogClosed"
    class="dialog-appointment"
    width="1150px">
        <template #header>
            <el-tabs v-model="moduleId">
              <el-tab-pane v-for="item in tabPanelList.filter(item => item.id !=='scanner' || formData.sId)" :label="item.label" :name="item.id">
                <template v-if="item.id =='scanner'" #label>
                    <span>扫描资料</span>
                    <span class="i-count">({{ scanCount || 0 }})</span>
                </template>
              </el-tab-pane>
            </el-tabs>
        </template>
        <div class="dialog-body">
            <el-form 
            :model="formData"
            :rules="rules"
            label-position="left"
            ref="refEditLayer"
            v-show="moduleId == 'yyd'" 
            class="item-01 yyd">
                <div class="left box">
                    <div class="form-container">
                        <el-skeleton style="width: 100%;padding: 20px 3px 10px 10px;box-sizing: border-box;" :loading="loadingSkeleton" :rows="15" animated>
                            <template #default>
                                <ReservationPatient 
                                :visible="watchLoadData"
                                :rules="rules"
                                v-model:formData="formData"
                                :defaultCheckInfo="defaultCheckInfo"
                                :refEditLayer="refEditLayer"
                                :optionData="optionData"
                                :nuclearParams="nuclearParams"></ReservationPatient>
                            </template>
                        </el-skeleton>
                    </div>
                </div>
                <div class="right">
                    <div class="head box">
                        <div class="title title-01">
                            <h4>检查信息</h4>
                        </div>
                        <div class="form-container" style="padding: 0 10px;">
                            <el-skeleton style="width: 100%;padding: 0 3px 0 10px;box-sizing: border-box;" :loading="loadingSkeleton" animated>
                                <template #template>
                                    <div style="height: 210px;">
                                        <el-skeleton-item variant="text" style="margin-top: 16px;" />
                                        <el-skeleton-item variant="text" style="width: 70%; margin-top: 16px;" />
                                        <el-skeleton-item variant="text" style="margin-top: 16px" />
                                        <el-skeleton-item variant="text" style="width: 70%; margin-top: 16px" />
                                        <el-skeleton-item variant="text" style="margin-top: 16px" />
                                        <el-skeleton-item variant="text" style="width: 70%; margin-top: 16px" />
                                    </div>
                                </template>
                                <template #default>
                                    <ReservationCheckInfo
                                    :visible="watchLoadData"
                                    :rules="rules"
                                    v-model:formData="formData"
                                    :defaultCheckInfo="defaultCheckInfo"
                                    :districtData="districtData"
                                    :refEditLayer="refEditLayer"
                                    :optionData="optionData"
                                    @getNuclearParams="getNuclearParams"></ReservationCheckInfo>
                                </template>
                            </el-skeleton>
                        </div>
                    </div>
                    <div class="content box">
                        <SelectNumberSource 
                        v-model:formData="formData"
                        :optionData="optionData"
                        :currentSelectInfo="appointmentResult" 
                        @changeSource="changeSource"
                        ref="selectNumberSource"></SelectNumberSource>
                        <div class="info-result">
                            <!-- <h5>预约结果</h5> -->
                            <div class="item">
                                <span style="font-weight: bold;">状态：</span>
                                <p style="font-weight: bold;" :style="{'color': appointmentResult.statusName == '未预约' ? '#f56c6c' : '#67c23a'}">{{ appointmentResult.statusName }}</p>
                            </div>
                            <div class="item" style="cursor: pointer;" @click="onClickActive" v-show="appointmentResult.dAppointmentTime">
                                <span>预约时间：</span>
                                <p>{{ appointmentResult.dAppointmentTime }} {{ appointmentResult.sTimeText }}</p>
                                <div class="t-location">
                                    <el-icon>
                                        <Location />
                                    </el-icon>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-form>
            <div v-show="moduleId == 'sqd'" class="item-02 sqd">
                <ApplyFormInfo :applyForm="appointInfo.applyItem"></ApplyFormInfo>
            </div>
            <div v-show="moduleId == 'scanner'" class="item-02 scanner flex">
                <ScanImage :isVisible="moduleId === 'scanner'" ref="scanImageRef" :rights="rights"></ScanImage>
            </div>
        </div>
        <template #footer>
            <div class="btns">
            <template v-if="moduleId == 'yyd'">
                <SupplyApplication v-if="formData.sId" :formData="formData"></SupplyApplication>
                <AccurateFee v-if="formData.sId" :formData="formData"></AccurateFee>
                <span v-auth="'report:appoint:print'">
                    <ReportPrintBtn v-if="formData.sId" :propParams="{ patient: formData, isBatch: false, idKey:'sId', deviceTypeIdKey: 'sDeviceTypeId', iModuleId: iModuleId }"></ReportPrintBtn>
                </span>
                <span v-auth="'report:appoint:sign'">
                    <el-button-icon-fa v-if="formData.sId && !iIsRegister"
                        class="btn-item" 
                        type="primary" 
                        icon="fa fa-register"
                        :disabled="handleLoading"
                        plain
                        @click="onSignIn()">签到</el-button-icon-fa>
                </span>
                <span v-auth="'report:appoint:cancelSign'">
                    <el-button-icon-fa v-if="formData.sId && iIsRegister"
                        class="btn-item"
                        type="primary" 
                        icon="fa fa-calendar-fork"
                        :disabled="handleLoading"
                        plain
                        @click="onSignInCancel">取消签到</el-button-icon-fa>
                </span>
                <span v-auth="'report:appoint:cancelCheck'">
                    <el-button-icon-fa v-if="formData.sId"
                        class="btn-item"
                        type="primary" 
                        icon="fa fa-cancel"
                        :disabled="handleLoading"
                        plain
                        @click="onCancel">取消检查</el-button-icon-fa>
                </span>
                <span v-auth="'report:appoint:save'">
                    <el-button-icon-fa
                        :loading="saveLoading" 
                        class="btn-item"    
                        type="primary" 
                        icon="fa fa-save"
                        @click="onSaveClick">保存</el-button-icon-fa>
                </span>
            </template>
            <span class="btn-item">
                <el-button-icon-fa plain icon="fa fa-close-1"
                @click="visible = false">关闭</el-button-icon-fa>
            </span>
            </div>
        </template>
    </el-dialog>
    <el-dialog append-to-body
        title="选择模板"
        v-model="visibleTemplate"
        @close="closeTemplateDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="my-dialog"
        width="500px">
        <div style="margin: 10px 10px 15px;">
            <el-radio-group v-model="selectedTemplateId"
                style="display: flex; flex-direction: column;align-items: baseline;padding-left: 160px;">
                <el-radio v-for="(item, index) in pocketTemplateList"
                    :key="index"
                    :label="item.sTemplateId"
                    style="margin-top: 18px;">
                    <span style="font-size: 16px;">{{item.sTemplateName}}</span>
                </el-radio>
            </el-radio-group>
        </div>
        <template #footer>
            <div class="footer-item"
                style="text-align: right; padding: 10px;">
                <el-button-icon-fa
                    type="primary"
                    icon="fa fa-save"
                    @click="onPrintPocket">确认</el-button-icon-fa>
                <el-button-icon-fa
                    icon="fa fa-close-1"
                    @click="closeTemplateDialog">关闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
</template>
<script>
    import { MoreFilled, Location } from '@element-plus/icons-vue'
    // import { ElMessage } from 'element-plus'

    import { deepClone } from '$supersetUtils/function'
    import { getOptionName } from '$supersetResource/js/tools'  

    import SelectNumberSource from './components/SelectNumberSource.vue'
    import ApplyFormInfo from './components/ApplyFormInfo.vue'
    import ReservationPatient from './components/ReservationPatient.vue'
    import ReservationCheckInfo from './components/ReservationCheckInfo.vue'

    import { mixinDictionaryGroup, mixinPrintPreview, mixinElementConfigs } from '$supersetResource/js/projects/apricot/index.js'
    import { recommendNumSource, appointmentSignIn, appointmentSignInCancel, appointmentCancel, appointmentBookSave } from '$supersetApi/projects/apricot/appointment/index.js'
    import { getPatientInfo } from '$supersetApi/projects/apricot/common'
    import { getPatientInfoById } from '$supersetApi/projects/apricot/case/index.js'

    import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
    import ConfigsItems from './configs/configsItems.js'
    import useUserConfig from './useUserConfig.js'
    import { useSetNumberSource } from './useHighlightNumSource.js'
        
    export default {
        mixins: [mixinDictionaryGroup, mixinPrintPreview, mixinElementConfigs],
        components: {
            ReservationPatient,
            ReservationCheckInfo,
            SelectNumberSource,
            ApplyFormInfo,
            MoreFilled,
            Location,
            ReportPrintBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPrintBtn.vue')), // 报告打印按钮
            ScanImage: defineAsyncComponent(() => import('$supersetViews/apricot/components/ScanImage.vue')), // 扫描资料
            SupplyApplication: defineAsyncComponent(() => import('./SupplyApplication.vue')),
            AccurateFee: defineAsyncComponent(() => import('./components/AccurateFee.vue'))
        },
        props: {
            modelValue: Boolean,
            appointInfo: {
                type: Object,
                default: () => { applyItem: {} }
            },  // 申请单信息
            iModuleId: 2,
            districtData: Array, // 存储院区、机房数据集
            modelParams: Object,  // 存储选中的院区、机房
        },
        emits: ['update:modelValue', 'emitRfresh'],
        data() {
            return {
                moduleId: 'yyd',
                tabPanelList: [
                    {label: '预约单', id: 'yyd'}, 
                    {label: '电子申请单', id: 'sqd'},
                    {label: '扫描资料', id: 'scanner'},
                ],
                formData: {},
                watchLoadData: false,
                rules: {},
                handleLoading: false,
                pocketTemplateList: [],
                requestPatientData: {},
                visibleTemplate: false,
                selectedTemplateId: '', // 模板id
                saveLoading: false,   // 保存加载
                nuclearParams: {},
                defaultCheckInfo: {}, // 默认检查信息，存储病区、机房
                appointmentResult: { dAppointmentTime: '', sTimeText: '', statusName: ''},
                curNumberSorceInfo: {},  // 选择号源时，传递的参数
                closeRefreshStatus: {
                    numSource: false,  // 刷新号源
                    applyForm: false,  // 刷新申请单
                },
                optionData: {
                    iSexList: appointmentEnum.sexOptions,
                    sSourceOptions: appointmentEnum.visitTypeOptions,
                    sAgeUnitOptions: appointmentEnum.ageUnitOptions,
                    pinyinOptions: [],
                    pregnantOptions: [
                        { sName: '否', sValue: 0 },
                        { sName: '是', sValue: 1 },
                        { sName: '未知', sValue: 2 },
                        { sName: '不适用', sValue: 3 },
                    ],
                    ChargeStateOptions: [{
                        sName: '已收费',
                        sValue: '1'
                    },
                    {
                        sName: '未收费',
                        sValue: '0'
                    }],
                    ApricotReportFeeType: [],
                    ApricotReportDoseUnit: [],
                    machineRoomOptions: [], // 机房
                    projectOptions: [], // 项目
                    injectionRoomOption: [], //注射室
                    consultRoomOption: [], // 问诊室
                    ethnicGroupCodeOption: this.$store.getters['dict/map'].ethnic_group || []
                },
                loadingSkeleton: true, // 骨架
                rights: {
                    deleteFile: true,
                    uploadFile: true,
                    manualUpload: true,
                },
                iIsRegister: false
            }
        },
        computed: {
            visible: {
                get: function() {
                    return this.modelValue
                },
                set: function(val) {
                    this.$emit('update:modelValue', val)
                }
            },
            userInfo () {
                const temp = this.$store.getters['user/userSystemInfo'] || {};
                return temp;
            },
            scanCount() {
                return  this.$refs.scanImageRef?.serverImages.length
            }
        },
        methods: {
            getNuclearParams(params) {
                this.nuclearParams = params
            },
            onSignIn (patientInfoId = null, isAutoSign = 0) {
                let jsonData = {
                    patientInfoId: patientInfoId ? patientInfoId : this.appointInfo.sPatientId,
                    autoSign: patientInfoId ? 1 : 0
                }
                this.handleLoading = true;
                let loading = this.$loading({
                    lock: true,
                    text: '正在加载中，请稍等',
                    
                    background: 'rgba(0, 0, 0, 0.1)'
                });
                appointmentSignIn(jsonData).then(res => {
                    this.handleLoading = false;
                    loading.close();
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.iIsRegister = true;
                        this.closeRefreshStatus.numSource = true
                        this.formData.iFlowState = 2
                        if(!isAutoSign && res.data && res.data.iPrintSticker) {
                            const sId = res.data.sPatientInfoId;
                            this.requestPatientData = { ...this.formData, sId };
                            this.autoPrintPocket(this.requestPatientData);
                        }
                        return
                    }
                    this.$message.error(res.msg);
                }).catch(() => {
                    this.handleLoading = false;
                    loading.close();
                })
            },
            // 打印袋贴
            async autoPrintPocket (patient) {
                this.btnsOption = [{
                    iClassify: 2,
                    sClassify: '袋贴',
                    templateList: []
                }]
                // let deviceTypeIdKey = this.propParams.deviceTypeIdKey || 'sRoomId';
                let params = {
                    iClassify: this.btnsOption[0].iClassify,
                    iModuleId: this.iModuleId,
                    sDeviceTypeId: patient.sDeviceTypeId
                }
                await this.mxGetWorkStationPrintShow(this.iModuleId);
                await this.mxGetTemplateOfPrintClassify(params);
                let targetArr = this.btnsOption[0].templateList;

                if (targetArr.length === 0) {
                    setTimeout(() => {
                        this.$message.error('未配置打印模板！');
                    }, 100);
                    return
                }
                if (targetArr.length === 1) {
                    // 执行打印步骤
                    let params = {
                        patient: patient,
                        isBatch: false,
                        idKey: 'sId',
                        // deviceTypeIdKey: 'sDeviceTypeId',
                        template: targetArr[0],
                        iPrintType: this.sPrintShow.iPrintType || 2,  // 2 = 打开文件
                    }
                    this.mxHandlePrint(params);
                    return
                }
                // 显示模板弹窗
                this.visibleTemplate = true;
                this.pocketTemplateList = targetArr;
            },
            // 确认打印
            async onPrintPocket () {
                let targetObj = this.pocketTemplateList.find(item => item.sTemplateId === this.selectedTemplateId);
                if (targetObj) {
                    let params = {
                        patient: this.requestPatientData,
                        isBatch: false,
                        idKey: 'sId',
                        template: targetObj,
                        iPrintType: this.sPrintShow.iPrintType || 2,  // 2 = 打开文件
                    }
                    this.mxHandlePrint(params);
                    this.selectedTemplateId = '';
                    this.visibleTemplate = false;
                }
            },
            // 取消签到
            onSignInCancel () {
                this.$confirm('确认取消签到，是否继续？', '提示', { type: 'warning' }).then(() => {
                    let jsonData = {
                        patientInfoId: this.appointInfo.sPatientId,
                    }
                    let loading = this.$loading({
                        lock: true,
                        text: '正在加载中，请稍等',
                        background: 'rgba(0, 0, 0, 0.1)'
                    });
                    appointmentSignInCancel(jsonData).then(res => {
                        loading.close();
                        if (res.success) {
                            this.$message.success(res.msg);
                            this.iIsRegister = false;
                            this.closeRefreshStatus.numSource = true
                            // 刷新申请单
                            this.closeRefreshStatus.applyForm  = true

                            this.formData.iFlowState = 1 // 取消签到的状态

                            return
                        }
                        this.$message.error(res.msg);
                    }).catch(err => {
                        console.log(err);
                        loading.close();
                    })
                }).catch()
            },
            // 取消检查
            onCancel () {
                this.$confirm('确认取消检查吗，是否继续？', '提示', { type: 'warning' }).then(() => {
                    let jsonData = {
                        sPatientInfoId: this.appointInfo.sPatientId,
                    }

                    this.handleLoading = true;
                    let loading = this.$loading({
                        lock: true,
                        text: '正在加载中，请稍等',
                        
                        background: 'rgba(0, 0, 0, 0.1)'
                    });
                    appointmentCancel(jsonData).then(res => {
                        this.handleLoading = false;
                        loading.close();
                        if (res.success) {
                            this.$message.success(res.msg)
                            this.closeRefreshStatus.numSource = true
                            // 刷新申请单
                            this.closeRefreshStatus.applyForm  = true
                            this.visible = false
                            return
                        }
                        this.$message.error(res.msg);
                    }).catch(err => {
                        console.log(err)
                        this.handleLoading = false;
                        loading.close();
                    })
                }).catch()
            },
            // 关闭打印弹窗
            closeTemplateDialog () {
                this.selectedTemplateId = '';
                this.visibleTemplate = false;
            },
            // 保存
            onSaveClick () {
                this.$refs['refEditLayer'].validate((valid) => {
                    if (!valid) {
                        this.$message.closeAll();
                        this.$message({
                            message: '请完成必填信息录入！',
                            type: 'warning',
                        });

                        return;
                    }

                    let jsonData = deepClone(this.formData)

                    // 点击别的项目，又取消。还原回去
                    // if (!jsonData.numSourceId && jsonData.sId) {
                    //     console.log('进入？')
                    //     // dAppointmentTime 各式被转变为 0000-00-00 各式
                    //     jsonData.sAppointmentTime = null // this.appointmentResult.dAppointmentTime.replace(/-/g,'')
                    //     jsonData.numSourceId = this.appointmentResult.numSourceId
                    //     jsonData.sTimeText = this.appointmentResult.sTimeText
                    // }
                    
                    // return

                    if (!jsonData.numSourceId) {
                        this.$message({
                            message: '请选择号源',
                            type: 'warning',
                        })
                        return
                    }

                    if(!jsonData.sId) {
                        let subscribeObj = {
                            sSubscribeNo: this.userInfo.userNo,         //当前操作用户工号
                            sSubscribePerson: this.userInfo.userName,     //当前操作用户姓名
                            sSubscribePersonId: this.userInfo.userId,   //当前操作用户ID
                            dSubscribeTime: new Date(),       //当前操作时间
                        }
                        Object.assign(jsonData, subscribeObj);
                    }

                    // 患者信息 女性 且在12到70岁之间，提示用户录入是否怀孕信息
                    if (jsonData.iIsPregnant === undefined || jsonData.iIsPregnant === null) {
                        let targetArray = ConfigsItems.appointmentInputList1.filter(item => item.sProp ==='iIsPregnant' )
                        let bool = jsonData.sSex == 2 && jsonData.sAgeUnit === 'Y' && jsonData.iAge && jsonData.iAge >= 16 && jsonData.iAge <= 60
                        if (targetArray.length &&targetArray[0].iRequired && bool) {
                            this.$message({
                                message: '请给年龄在[16-60]岁女性患者完成 “是否怀孕” 信息的录入！',
                                type: 'warning',
                            });
                            return
                        }
                    }

                    // jsonData.sSexText = getOptionName(jsonData.sSex, this.optionData.iSexList); // 性别单位
                    // jsonData.sAgeUnitText = getOptionName(jsonData.sAgeUnit, this.optionData.sAgeUnitOptions); // 年龄单位
                    // jsonData.sSourceText = getOptionName(jsonData.sSource, this.optionData.sSourceOptions); // 就诊类型
                    // jsonData.sChargeStateText = getOptionName(jsonData.sChargeState, this.optionData.ChargeStateOptions); // 收费状态
                    // jsonData.sMachineryRoomText = getOptionName(jsonData.sMachineryRoomId, this.optionData.machineRoomOptions, { find: 'sRoomId', get: 'sRoomName' }); // 机房名称
                    // jsonData.sProjectName = getOptionName(jsonData.sProjectId, this.optionData.projectOptions); // 检查项目名称
                    jsonData.sItemCode = getOptionName(jsonData.sProjectId, this.optionData.projectOptions, { find: 'sValue', get: 'sItemCode' }); // 检查项目编号
                    // jsonData.sFeeTypeText = getOptionName(jsonData.sFeeType, this.optionData.ApricotReportFeeType); // 收费类型
                    // jsonData.sInjectionRoomName = getOptionName(jsonData.sInjectionRoomId, this.optionData.injectionRoomOption); // 注射室
                    // jsonData.sConsultRoomName = getOptionName(jsonData.sConsultRoomId, this.optionData.consultRoomOption); // 问诊室

                    // jsonData.sDistrictName = getOptionName(jsonData.sDistrictId, this.districtData, { find: 'sHospitalDistrictId', get: 'sHospitalDistrictName' })
                    
                    const roomObj = this.districtData.find( district => district.sHospitalDistrictId == jsonData.sDistrictId)?.rooms?.find(room => room.sRoomId == jsonData.sMachineryRoomId)
                    if (roomObj) {
                        jsonData.sDeviceTypeId = roomObj.sDeviceTypeId;  // 设备类型逻辑主键
                        jsonData.sDeviceTypeName = roomObj.sDeviceTypeName;  // 设备类型名称
                    }

                    if (jsonData.sRegister == 3) {
                        // 手工登记  映射申请单编号和项目名称
                        jsonData.sOrderItemCode = jsonData.sOrderItemCode ? jsonData.sOrderItemCode : jsonData.sItemCode
                        jsonData.sOrderItemName = jsonData.sOrderItemName ? jsonData.sOrderItemName : jsonData.sProjectName
                    }

                    if (jsonData.sId && jsonData.sAppointmentTime) {
                        const timestamp = moment(jsonData.sAppointmentTime + jsonData.sTimeText + '00', 'YYYYMMDDHH:mmss').valueOf();
                        jsonData.dAppointmentTime = timestamp
                        // moment('2023-02-02 00:00:00').valueOf();
                    }

                    const isTransformNumSource = jsonData.isTransformNumSource;
                    if(isTransformNumSource) {
                        const timeString = moment(jsonData.dAppointmentTime).format('YYYY-MM-DD HH:mm');
                        this.$confirm(`确认改约至${timeString}，是否继续？`, '提示', { type: 'warning' }).then(() => {
                            this.sendSaveRequest(jsonData);
                        }).catch(() => {
                            console.log('取消改约');
                        })
                        return
                    }

                    delete jsonData.isCheckNumberSource;
                    delete jsonData.isTransformNumSource;

                    this.sendSaveRequest(jsonData);
                })
            },
            // 发送保存请求
            sendSaveRequest(jsonData) {
                this.saveLoading = true
                appointmentBookSave(jsonData).then(res => {
                    this.saveLoading = false
                    if (res.success) {
                        // 第一次预约,没有 dAppointmentTime , 只有sAppointmentTime 需要转换一下
                        if (!jsonData.dAppointmentTime && jsonData.sAppointmentTime) {
                            this.formData.dAppointmentTime = moment(jsonData.sAppointmentTime + jsonData.sTimeText + '00', 'YYYYMMDDHH:mmss').valueOf()
                        }
                        this.$message.success(res.msg)

                        this.closeRefreshStatus.numSource = true
                        this.closeRefreshStatus.applyForm = true

                        if (jsonData.sId) {
                            this.formData.iVersion = res.data.iVersion
                        } else {
                            // 第一次保存。要刷新申请单，要打印，要签到
                            this.formData.sId = res.data.sPatientInfoId

                            if (this.configData.isSaveAutoPrintPocket && this.$auth['report:appoint:print']) {
                                // 调用打印
                                const sId = res.data.sPatientInfoId;
                                this.requestPatientData = { ...jsonData, sId}
                                this.autoPrintPocket(this.requestPatientData);
                            }
                            if (this.configData.isCallSignIn && this.$auth['report:appoint:sign']) {
                                // 调用签到
                                this.onSignIn(res.data.sPatientInfoId, 1)
                            }
                        }
                        // 关闭弹窗
                        if (this.configData.isSaveAndCloseAppointment) {
                            this.visible = false
                        }else {
                            // 不关闭弹窗，需要刷新一下
                            if (!jsonData.sId) {
                                this.appointInfo.sPatientId = res.data.sPatientInfoId
                                this.initLoadData()
                            }

                            this.$refs.selectNumberSource.refreshNumSource(jsonData.sId)

                        }
                        delete this.formData.isTransformNumSource;
                        return
                    }
                    this.$message.error(res.msg)
                }).catch(err => {
                    console.log(err)
                    this.saveLoading = false
                })
            },
            // 获取检查项目编号
            // getOptionItemCode (value, options) {
            //     if (['', undefined].includes(value) || Object.prototype.toString.call(options) !== '[object Array]') return null;
            //     let item = options.find(item => item.sValue === value);
            //     return item ? item.sItemCode : null
            // },
            dialogOpen() {
                this.initLoadData()
            },
            async initLoadData() {
                // 打开弹窗，赋值给表单数据
                // 存储选择的号源
                const curNumberSorceInfo = {
                    dAppointmentTime: new Date(),
                    sTimeText: '',
                    numSourceId: '',
                }
                // 表单默认参数
                const defaultParams = {
                    sRegister: 3,
                    sSource: '1',
                    sAgeUnit: 'Y',
                    sRecipeDoseUnit: 'mci'
                }
                this.iIsRegister = false;
                // 请求后端，获取患者信息
                if (this.appointInfo.sPatientId) {
                    const data = await this.getPatientInfo(this.appointInfo.sPatientId)
                    // console.log(data)
                    this.defaultCheckInfo = {
                        hospitalId: data.sDistrictId,
                        roomId: data.sMachineryRoomId,
                        sDeviceTypeId: data.sRoomId,
                        isHistory: true, // 历史数据
                    }

                    curNumberSorceInfo.dAppointmentTime = data.dAppointmentTime
                    curNumberSorceInfo.numSourceId = data.numSourceId
                    curNumberSorceInfo.sTimeText = data.sTimeText

                    const oResultTxt = {
                        '3_4_5_6': '已检查',
                        '7_8': '已报告',
                        '9': '已审核',
                        '10': '已复审',
                        'null_undefined': ''
                    }
                    let filter = Object.keys(oResultTxt).filter(key => key.includes(data.iFlowState + ''));
                    const statusName = data.iFlowState === 1 ||  data.iFlowState === 2 ? '已预约' : oResultTxt[filter[0]];
                    
                    // 存储预约结果
                    this.appointmentResult = {
                        dAppointmentTime: moment(data.dAppointmentTime).format("YYYY-MM-DD"),
                        sTimeText: data.sTimeText,
                        statusName: statusName,
                        numSourceId: data.numSourceId
                    }
                    data.sDeviceTypeId = data.sRoomId;
                    this.formData = Object.assign(defaultParams, deepClone(data || {}))
                    this.getPatientInfoById(this.appointInfo.sPatientId);
                } else {
                    // 存储预约结果
                    this.appointmentResult = {
                        dAppointmentTime: '',
                        sTimeText: '',
                        statusName: '未预约'
                    }
                    if (Object.keys(this.appointInfo.applyItem).length) {
                        // 点击申请单
                        this.defaultCheckInfo = deepClone(this.modelParams)
                        this.defaultCheckInfo.isApplyForm = true // 申请单来源未预约
                        this.formData = Object.assign(defaultParams, deepClone(this.appointInfo.applyItem || {}))
                    }else {
                        // 点击日历表盘
                        const selectItem = this.appointInfo.selectItem

                        this.formData = defaultParams

                        this.defaultCheckInfo = {
                            hospitalId: selectItem.sHospitalDistrictId,
                            roomId: selectItem.sRoomId,
                            sDeviceTypeId: selectItem.sDeviceTypeId,
                        }

                        curNumberSorceInfo.enterModel = 2  // 进入方式 - 日历进入
                        curNumberSorceInfo.dAppointmentTime = this.modelParams.date
                        curNumberSorceInfo.numSourceId = selectItem.sId
                        curNumberSorceInfo.sTimeText = selectItem.sTime
                    }
                    // 有项目，推荐号源
                    if (this.formData.sProjectId) {
                        const data = await this.recommendNumSource({
                            projectId: this.formData.sProjectId,
                            roomId: this.defaultCheckInfo.roomId,
                            applyDay: this.formData.dAppointDay,
                            districtId: this.modelParams.hospitalId
                        })
                        if (data) {
                            this.defaultCheckInfo.roomId = data.roomId
                            this.defaultCheckInfo.hospitalId = data.hospitalDistrictId
                            if (data.date) {
                                const dateString = data.date
                                const year = dateString.substr(0, 4)
                                const month = dateString.substr(4, 2) - 1 // 月份从0开始，所以需要减1
                                const day = dateString.substr(6, 2)
                                const date = new Date(year, month, day)
                                
                                curNumberSorceInfo.enterModel = 1  // 进入方式 - 推荐进入
                                curNumberSorceInfo.dAppointmentTime = date
                                curNumberSorceInfo.numSourceId = data.numSourceId
                            }
                            if(data.numSourceId) {
                                this.formData.isCheckNumberSource = true;
                            }
                        }
                    }
                }
                
                // 调用选择号源
                this.curNumberSorceInfo = curNumberSorceInfo
                this.$refs.selectNumberSource.setCurDate(curNumberSorceInfo)

                // 子组件刷新请求
                this.$nextTick(() => {
                    this.watchLoadData = true

                    this.loadingSkeleton = false
                })
            },
            getPatientInfoById(sId) {
                getPatientInfoById({sId}).then(res => {
                    if (res.success) {
                        const { iIsRegister } = res?.data || {};
                        this.iIsRegister = !!iIsRegister;
                    }
                }).catch()
            },
            async recommendNumSource(data) {
                try {
                    let jsonData = {
                        projectId: data.projectId,
                        roomId: data.roomId,
                        districtId: data.districtId
                    }
                    if(data.applyDay) {
                        jsonData.applyDay = moment(data.applyDay).format('YYYYMMDD');
                    }
                    const result = await recommendNumSource(jsonData)
                    if (!result || !result.data) {
                        return false
                    }
                    // TODO 匹配不项目，不显示 id 
                    
                    // 成功，有数据
                    return result.data
                } catch (error) {
                    return false
                }
            },
            // 获取已约患者信息
            async getPatientInfo (sPatientId) {
                let jsonData = {
                    sId: sPatientId
                }
                const result = await getPatientInfo(jsonData)
                if (!result) {
                    return {}
                }
                if (!result.success) {
                    this.$message.error(result.msg)
                }
                // TODO 匹配不项目，不显示 id 
                
                // 成功，有数据
                return result.data
            },
            changeSource() {
                this.closeRefreshStatus.numSource = true
                this.closeRefreshStatus.applyForm = true
            },
            dialogClosed() {
                const formData = deepClone(this.formData)

                // 关闭弹窗，刷新外部号源
                if (this.closeRefreshStatus.numSource) {
                    // 院区
                    if (this.modelParams.hospitalId != formData.sDistrictId) {
                        this.modelParams.hospitalId = formData.sDistrictId
                    }
                    // 在点击院区变化后，会重新赋值机房选中
                    this.$nextTick(() => {
                        // 机房、设备
                        if (this.modelParams.roomId != formData.sMachineryRoomId) {
                            this.modelParams.roomId = formData.sMachineryRoomId     // 机房
                            this.modelParams.roomName = formData.sMachineryRoomText // 机房名称
                            this.modelParams.sDeviceTypeId = formData.sRoomId       // 设备类型
                        }
                        // 号源高亮使用
                        useSetNumberSource(formData.numSourceId)

                        // 日期
                        if (moment(this.modelParams.date).format("YYYY-MM-DD") != moment(formData.dAppointmentTime).format("YYYY-MM-DD")) {
                            this.modelParams.date = new Date(formData.dAppointmentTime)
                        }
                        this.$emit('emitRfresh', 'numSource')
                    })
                }
                // 关闭弹窗，刷新申请单
                if (this.closeRefreshStatus.applyForm) {
                    this.$emit('emitRfresh', 'applyForm')
                }

                this.watchLoadData = false
                this.formData = {}
                this.$refs.selectNumberSource.clearData()
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].resetFields()
                this.moduleId = 'yyd'
                this.closeRefreshStatus = {}
                // this.loadingSkeleton = true 关闭也不管它，因为只有第一次打开慢
                
            },
            onClickActive() {
                this.$refs.selectNumberSource.setCurDate(this.curNumberSorceInfo)
            }
        },
        created() {
            this.optionData.ApricotReportFeeType = this.$store.getters['dict/map'].ApricotReportFeeType || []
            this.optionData.ApricotReportDoseUnit = this.$store.getters['dict/map'].ApricotReportDoseUnit || []
        },
        setup() {
            const refEditLayer = ref(null)

            const { configData } = useUserConfig()

            return {
                refEditLayer,
                configData
            }
        },
        provide () {
            return {
                patientInfo: computed(() =>  {
                    return {  ...this.formData }
                }),
            }
        },
    }
</script>
<style>
.dialog-appointment {
    display: flex;
    flex-direction: column;
    width: 94%;
    height: 94% !important;
    top: 3%;
}
.dialog-appointment .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 0;
}
.dialog-appointment .el-dialog__header {
    padding-bottom: 0 !important;
    padding: 6px 10px;
}
</style>
<style lang="scss" scoped>
.dialog-body {
    height: 100%;
    padding: 10px;
    box-sizing: border-box;
    background: var(--theme-menu-background-color);
    :deep(.el-form-item) {
        margin-bottom: 12px;
    }
    :deep(.el-form--label-left) {
        .el-form-item__label {
            justify-content: end;
        }
    }
    .box {
        background: white;
    }
    .title {
        position: relative;
        width: 100%;
        height: 18px;
        padding:11px 10px;
        font-size: 16px;
        font-weight: 400;
        color: rgba(26, 32, 44, 1);
        margin: 0 auto;
        > h4{
            border-left: 2px solid var(--el-color-primary);
            color: #494d4f;
            margin:0;
            padding-left:5px;
            font-weight: 400;
            line-height: 18px;
        }
        &.title-01 {
            border-bottom: 1px solid #eee;
        }
    }
    .item-01 {
        display: flex;
        height: 100%;
        width: 100%;
        .left {
            width: 600px;
            margin-right: 10px;
            background: initial;
            .form-container {
                height: 100%;
                // height: calc(100% - 44px);
                // padding-right: 10px;
                box-sizing: border-box;
                .container {
                    background: initial;
                    overflow: auto;
                }
            }
        }
        .right {
            flex: 1;
            display: flex;
            flex-direction: column;
            .head {
                margin-bottom: 10px;
                min-height: 120px;
            }
            .content {
                flex: 1;
                overflow: hidden;
            }
            .info-result {
                height: 34px;
                display: flex;
                align-items: center;
                border-top: 1px solid #eee;
                h5 {
                    margin: 0;
                    font-size: 14px;
                    font-weight: 700;
                    color: #3a3d40;
                    padding: 0px 16px 0px 8px;
                }
                p {
                    margin: 0;
                }
                .item {
                    display: flex;
                    align-items: center;
                    height: 100%;
                    margin-left: 16px;
                    padding-right: 10px;
                    &:hover {
                        .t-location {
                            font-size: 16px;
                        }
                    }
                }
                .t-location {
                    display: block;
                    color: black;
                    position: relative;
                    bottom: -1px;
                    left: 4px;
                }
            }
        }
    }
    .item-02 {
        height: 100%;
        padding: 12px;
        background: white;
        box-sizing: border-box;
    }
    
}
.btns {
    display: flex;
    justify-content: end;
    align-items: center;
    .btn-item {
        margin-left: 10px;
    }
}
</style>