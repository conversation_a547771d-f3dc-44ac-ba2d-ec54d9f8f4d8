.quill-editor {
  height: 100%;
  display: flex;
  flex-direction: column;

  >.ql-container {
      flex: 1;
      font-size: 16px;
      overflow: hidden;
  }
}

.ql-editor {
  padding: 10px;
  // border: 1px solid #eee;
  border-top-width: 0;
}

.u-border-bottom {
  border-bottom-color: #eee;
}

.g-businessMenu {
  border-color: #eee !important;
}

.u-btnArea {
  padding: 10px 15px;
}

.quill-editor { 
  .ql-disabled {
    &.ql-container  {
      // background-color: #e6e6e6;
      cursor: not-allowed;
    }
  }

  .ql-snow .ql-stroke {
      stroke: #656565;
  }

  .ql-snow .ql-picker {
      color: #656565;
  }

  .ql-snow .ql-fill,
  .ql-snow .ql-stroke.ql-fill {
      fill: #656565;
  }
}

.ql-picker-options {
  position: absolute;
  min-width: auto;
  max-height: 200px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
    height: 12px;
    background-color: transparent;
}

&::-webkit-scrollbar-thumb {
    background-color: #ccc;
    background-clip: padding-box;
    border: none;
    cursor: pointer;
    border-radius: 7px;
}
}

.ql-container {
  .ql-tag-blot-mention {
      position: relative;
      padding: 2px 4px;
      margin: 0px 2px;
      cursor: pointer;
      border-bottom: 1px solid #000000;
      border-radius: 2px;
      margin-top: 5px;
      text-align: center;

      &:hover {
        // background-color: #fff5d4;
        // border: 1px solid #dcac6c;
      }
      &:after {
        content: '';
        position: relative;
        top: 50%; 
        display: inline-block;
        width: 18px;
        height: 12px;
        background-position: center center;
        background-size: auto;
        background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAGZJREFUOE9jZKAQMFKon2EYGqDGwMDgy8DA0IsjbIoZGBg2MzAw3ILJo4cBSAEIgwxANwSrHLZAxKYQp8G4YgFZA8i1uFyFNxphhoAMwOYlcDAQSgcgQ2AGYA1XQgYQTKijBjAwAACD/BERb71b4gAAAABJRU5ErkJggg==')
      }
  }
    .ql-editor {
        white-space: break-spaces;
        word-break: break-word;

        ol,ul {
            padding-left: 0;
            list-style-position: inside;

            li:not(.ql-direction-rtl) {
                padding-left: 1.5em;
            }
            @for $num from 1 through 9 {

                .ql-indent-#{$num}:not(.ql-direction-rtl) {
                    padding-left: (1.3*$num + 1.5) * 1em;
                }
    
                li.ql-indent-#{$num}:not(.ql-direction-rtl) {
                    padding-left: (1.3*$num + 1.5) * 1em;
                }
    
                .ql-indent-#{$num}.ql-direction-rtl.ql-align-right {
                    padding-right: (1.3*$num) * 1em;
                }
    
                li.ql-indent-#{$num}.ql-direction-rtl.ql-align-right {
                    padding-right: (1.3*$num + 1.5) * 1em;
                }
            }
            li:not(.ql-direction-rtl)::before {
                margin-right: 0.3em;
                margin-left: -1.5em;
                text-align: center;
            }
        }
    }

    hr {
        position: relative;
        display: block;
        width: 100%;
        height: 40px;
        padding: 5px 0;
        margin: 5px 0 0;
        cursor: default;
        border-bottom: none;
        border-top: 1px dashed #aaa;
        user-select: none;

        &::after {
            font-size: 20px;
            font-weight: bold;
            padding: 0 6px;
            margin: 0 10px;
            margin-bottom: 10px;
            border-left: 3px solid #2384d3;
            user-select: none;
        }
    }

    // head      mark    mark
    //     检查技术 检查所见 诊断意见
    //     检查技术 诊断意见
    //     检查所见 诊断意见 
    hr[mark=head] {
        &.ql-head-type1 {
            &::after {
                content: '检查所见';
            }
        }

        &.ql-head-type23 {
            &::after {
                content: '检查技术';
            }
        }
    }

    hr[mark=divider] {
        margin: 15px 0 0;

        &.ql-divider-type3 {
            &:nth-of-type(2) {
                &::after {
                    content: '检查所见';
                }
            }

            &:nth-of-type(3) {
                &::after {
                    content: '诊断意见';
                }
            }

        }

        &.ql-divider-type12 {

            &:nth-of-type(2),
            &:nth-of-type(3) {
                &::after {
                    content: '诊断意见';
                }
            }
        }

    }
}

 
/*quill-editor-------------------------------------*/


// .ql-snow .ql-picker.ql-size .ql-picker-label::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item::before {
//   content: "14px";
// }
// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
//   content: "10px";
// }
// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
//   content: "18px";
// }
// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
//   content: "36px";
// }

// .ql-snow .ql-picker.ql-font .ql-picker-label::before,
// .ql-snow .ql-picker.ql-font .ql-picker-item::before {
//   content: "标准字体";
// }
// .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
// .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
//   content: "衬线字体";
// }
// .ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
// .ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
//   content: "等宽字体";
// }

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '小四';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="56px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="56px"]::before {
  content: '初号';
  // font-size: 56px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="48px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before {
  content: '小初';
  // font-size: 48px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="34px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="34px"]::before {
  content: '一号';
  // font-size: 34px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
  content: '小一';
  // font-size: 32px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="29px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="29px"]::before {
  content: '二号';
  // font-size: 29px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
  content: '小二';
  // font-size: 24px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="21px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="21px"]::before {
  content: '三号';
  // font-size: 21px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
  content: '小三';
  // font-size: 20px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
  content: '四号';
  // font-size: 18px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
  content: '小四';
  // font-size: 16px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
  content: '五号';
  // font-size: 14px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
  content: '小五';
  // font-size: 12px;
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
  content: '六号';
  // font-size: 10px;
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label::before {
  width: 46px;
} 

.ql-snow .ql-picker.ql-lineheight .ql-picker-label::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item::before {
  content: '1.5';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="1.0"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="1.0"]::before {
  content: '1.0';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="1.5"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="1.5"]::before {
  content: '1.5';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="2.0"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="2.0"]::before {
  content: '2.0';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="2.5"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="2.5"]::before {
  content: '2.5';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="16px"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="16px"]::before {
  content: '16px';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="24px"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="24px"]::before {
  content: '24px';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="32px"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="32px"]::before {
  content: '32px';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="48px"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="48px"]::before {
  content: '48px';
}

.ql-snow .ql-picker.ql-lineheight .ql-picker-label[data-value="56px"]::before,
.ql-snow .ql-picker.ql-lineheight .ql-picker-item[data-value="56px"]::before {
  content: '56px';
}

// .ql-snow .ql-picker.ql-size .ql-picker-label::before{
//     font-size: 14px !important;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item::before {
//     content: "14px";
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-label::before,
// .ql-bubble .ql-picker.ql-size .ql-picker-item::before {
//     content: '五号' !important;
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
//     content: '六号';
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=normal]::before,
// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=normal]::before {
//     content: '五号';
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
//     content: '四号';
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
//     content: '小一';
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
//     font-size: 12px;
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=normal]::before {
//     font-size: 14px;
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
//     font-size: 18px;
// }

// .ql-bubble .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
//     font-size: 32px;
// }


// .ql-editor .ql-size-10px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
//     font-size: 10px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="10px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="10px"]::before {
//     content: '六号';
// }

// .ql-editor .ql-size-12px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
//     font-size: 12px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="12px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="12px"]::before {
//     content: '小五';
// }

// .ql-editor .ql-size-14px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
//     font-size: 14px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="14px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="14px"]::before {
//     content: '五号';
// }

// .ql-editor .ql-size-16px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
//     font-size: 16px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="16px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="16px"]::before {
//     content: '小四';
// }


// .ql-editor .ql-size-18px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
//     font-size: 18px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="18px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="18px"]::before {
//     content: '四号';
// }

// .ql-editor .ql-size-20px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
//     font-size: 20px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="20px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="20px"]::before {
//     content: '小三';
// }

// .ql-editor .ql-size-21px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="21px"]::before {
//     font-size: 21px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="21px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="21px"]::before {
//     content: '三号';
// }

// .ql-editor .ql-size-24px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
//     font-size: 24px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="24px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="24px"]::before {
//     content: '小二';
// }

// .ql-editor .ql-size-29px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="29px"]::before {
//     font-size: 29px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="29px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="29px"]::before {
//     content: '二号';
// }

// .ql-editor .ql-size-32px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
//     font-size: 32px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="32px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="32px"]::before {
//     content: '小一';
// }

// .ql-editor .ql-size-34px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="34px"]::before {
//     font-size: 34px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="34px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="34px"]::before {
//     content: '一号';
// }

// .ql-editor .ql-size-48px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before {
//     font-size: 48px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="48px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="48px"]::before {
//     content: '小初';
// }

// .ql-editor .ql-size-56px,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="56px"]::before {
//     font-size: 56px;
// }

// .ql-snow .ql-picker.ql-size .ql-picker-label[data-value="56px"]::before,
// .ql-snow .ql-picker.ql-size .ql-picker-item[data-value="56px"]::before {
//     content: '初号';
// }



.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimSun]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimSun]::before {
  content: "宋体";
  font-family: "SimSun" !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=SimHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=SimHei]::before {
  content: "黑体";
  font-family: "SimHei";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=MicrosoftYaHei]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=MicrosoftYaHei]::before {
  content: "微软雅黑";
  font-family: "Microsoft YaHei";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=KaiTi]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=KaiTi]::before {
  content: "楷体";
  font-family: "KaiTi" !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=FangSong]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=FangSong]::before {
  content: "仿宋";
  font-family: "FangSong";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Arial]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Arial]::before {
  content: "Arial";
  font-family: "Arial";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=Times-New-Roman]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=Times-New-Roman]::before {
  content: "Times New Roman";
  font-family: "Times New Roman";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=sans-serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=sans-serif]::before {
  content: "默认字体";
  font-family: "sans-serif";
}

.ql-font-SimSun {
  font-family: "SimSun";
}

.ql-font-SimHei {
  font-family: "SimHei";
}

.ql-font-MicrosoftYaHei {
  font-family: "Microsoft YaHei";
}

.ql-font-KaiTi {
  font-family: "KaiTi";
}

.ql-font-FangSong {
  font-family: "FangSong";
}

.ql-font-Arial {
  font-family: "Arial";
}

.ql-font-Times-New-Roman {
  font-family: "Times New Roman";
}

.ql-font-sans-serif {
  font-family: "sans-serif";
}

.ql-font-spanHighlight {
  color: #f56c6c;
  font-weight: bold;
}

/*普通按钮样式及状态*/
.ql-toolbar .edui-icon {
  position: relative;
  display: inline-block;
  height: 20px;
  width: 20px;
  background-image: url(/img/editor/editor-icons.gif);
}

.ql-toolbar .edui-button .edui-button-wrap {
  padding: 1px;
  position: relative;
}

.ql-toolbar .edui-button .edui-state-hover .edui-button-wrap {
  background-color: #fff5d4;
  padding: 0;
  border: 1px solid #dcac6c;
}

.ql-toolbar .edui-button .edui-state-checked .edui-button-wrap {
  background-color: #ffe69f;
  padding: 0;
  border: 1px solid #dcac6c;
  border-radius: 2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
}

.ql-toolbar .edui-button .edui-state-active .edui-button-wrap {
  background-color: #ffffff;
  padding: 0;
  border: 1px solid gray;
}
.ql-toolbar .edui-state-disabled .edui-label {
  color: #ccc;
}
.ql-toolbar .edui-state-disabled .edui-icon {
  opacity: 0.3;
  filter: alpha(opacity = 30);
}

/* toolbar icons */
.edui-for-undo.edui-icon {
  background-position: -160px 0;
}

.edui-for-redo.edui-icon {
  background-position: -100px 0;
}

.edui-for-bold.edui-icon {
  background-position: 0 0;
}

.edui-for-italic.edui-icon {
  background-position: -60px 0;
}

.edui-for-fontborder.edui-icon {
  background-position:-160px -40px;
}
.edui-for-underline.edui-icon {
  background-position: -140px 0;
}

.edui-for-strikethrough.edui-icon, .edui-for-strike.edui-icon {
  background-position: -120px 0;
}

.edui-for-subscript.edui-icon {
  background-position: -600px 0;
}

.edui-for-superscript.edui-icon {
  background-position: -620px 0;
}

.edui-for-blockquote.edui-icon {
  background-position: -220px 0;
}

.edui-for-forecolor.edui-icon, .edui-for-color.edui-icon {
  background-position: -721px 0;
}

.edui-for-backcolor.edui-icon, .edui-for-background.edui-icon {
  background-position: -760px 0;
}

.edui-for-inserttable.edui-icon {
  background-position: -580px -20px;
}

.edui-for-autotypeset.edui-icon {
  background-position: -640px -40px;
}

.edui-for-justifyleft.edui-icon {
  background-position: -460px 0;
}

.edui-for-justifycenter.edui-icon {
  background-position: -420px 0;
}

.edui-for-justifyright.edui-icon {
  background-position: -480px 0;
}

.edui-for-justifyjustify.edui-icon {
  background-position: -440px 0;
}

.edui-for-insertorderedlist.edui-icon {
  background-position: -80px 0;
}

.edui-for-insertunorderedlist.edui-icon {
  background-position: -20px 0;
}

.edui-for-lineheight.edui-icon {
  background-position: -725px -40px;
}

.edui-for-rowspacingbottom.edui-icon {
  background-position: -745px -40px;
}

.edui-for-rowspacingtop.edui-icon {
  background-position: -765px -40px;
}

.edui-for-horizontal.edui-icon {
  background-position: -360px 0;
}

.edui-for-link.edui-icon {
  background-position: -500px 0;
}

.edui-for-code.edui-icon {
  background-position: -440px -40px;
}

.edui-for-insertimage.edui-icon {
  background-position: -726px -77px;
}

.edui-for-insertframe.edui-icon {
  background-position: -240px -40px;
}

.edui-for-emoticon.edui-icon {
  background-position: -60px -20px;
}

.edui-for-spechars.edui-icon, .edui-for-symbol.edui-icon {
  background-position: -240px 0;
}

.edui-for-help.edui-icon {
  background-position: -340px 0;
}

.edui-for-print.edui-icon {
  background-position: -440px -20px;
}

.edui-for-preview.edui-icon {
  background-position: -420px -20px;
}

.edui-for-selectall.edui-icon {
  background-position: -400px -20px;
}

.edui-for-searchreplace.edui-icon {
  background-position: -520px -20px;
}

.edui-for-map.edui-icon {
  background-position: -40px -40px;
}

.edui-for-gmap.edui-icon {
  background-position: -260px -40px;
}

.edui-for-insertvideo.edui-icon {
  background-position: -320px -20px;
}

.edui-for-time.edui-icon {
  background-position: -160px -20px;
}

.edui-for-date.edui-icon {
  background-position: -140px -20px;
}

.edui-for-cut.edui-icon {
  background-position: -680px 0;
}

.edui-for-copy.edui-icon {
  background-position: -700px 0;
}

.edui-for-paste.edui-icon {
  background-position: -560px 0;
}

.edui-for-formatmatch.edui-icon, .edui-for-brush.edui-icon {
  background-position: -40px 0;
}

.edui-for-pasteplain.edui-icon {
  background-position: -360px -20px;
}

.edui-for-directionalityltr.edui-icon {
  background-position: -20px -20px;
}

.edui-for-directionalityrtl.edui-icon {
  background-position: -40px -20px;
}

.edui-for-source.edui-icon {
  background-position: -261px -0px;
}

.edui-for-removeformat.edui-icon, .edui-for-clean.edui-icon {
  background-position: -580px 0;
}

.edui-for-unlink.edui-icon {
  background-position: -640px 0;
}

.edui-for-touppercase.edui-icon {
  background-position: -786px 0;
}

.edui-for-tolowercase.edui-icon {
  background-position: -806px 0;
}

.edui-for-insertrow.edui-icon {
  background-position: -478px -76px;
}

.edui-for-insertrownext.edui-icon {
  background-position: -498px -76px;
}

.edui-for-insertcol.edui-icon {
  background-position: -455px -76px;
}

.edui-for-insertcolnext .edui-icon {
  background-position: -429px -76px;
}

.edui-for-mergeright.edui-icon {
  background-position: -60px -40px;
}

.edui-for-mergedown.edui-icon {
  background-position: -80px -40px;
}

.edui-for-splittorows.edui-icon {
  background-position: -100px -40px;
}

.edui-for-splittocols.edui-icon {
  background-position: -120px -40px;
}

.edui-for-insertparagraphbeforetable.edui-icon {
  background-position: -140px -40px;
}

.edui-for-deleterow.edui-icon {
  background-position: -660px -20px;
}

.edui-for-deletecol.edui-icon {
  background-position: -640px -20px;
}

.edui-for-splittocells.edui-icon {
  background-position: -800px -20px;
}

.edui-for-mergecells.edui-icon {
  background-position: -760px -20px;
}

.edui-for-deletetable.edui-icon {
  background-position: -620px -20px;
}

.edui-for-cleardoc.edui-icon {
  background-position: -520px 0;
}

.edui-for-fullscreen.edui-icon {
  background-position: -100px -20px;
}

.edui-for-anchor.edui-icon {
  background-position: -200px 0;
}

.edui-for-pagebreak.edui-icon {
  background-position: -460px -40px;
}

.edui-for-imagenone.edui-icon {
  background-position: -480px -40px;
}

.edui-for-imageleft.edui-icon {
  background-position: -500px -40px;
}

.edui-for-wordimage.edui-icon {
  background-position: -660px -40px;
}

.edui-for-imageright.edui-icon {
  background-position: -520px -40px;
}

.edui-for-imagecenter.edui-icon {
  background-position: -540px -40px;
}

.edui-for-indent.edui-icon, .edui-for-addspace.edui-icon {
  background-position: -400px 0;
}

.edui-for-outdent.edui-icon {
  background-position: -540px 0;
}

.edui-for-webapp.edui-icon {
  background-position: -601px -40px
}

.edui-for-table.edui-icon {
  background-position: -580px -20px;
}

.edui-for-edittable.edui-icon {
  background-position: -420px -40px;
}

.edui-for-template.edui-icon {
  background-position: -339px -40px;
}

.edui-for-delete.edui-icon {
  background-position: -360px -40px;
}

.edui-for-attachment.edui-icon {
  background-position: -620px -40px;
}

.edui-for-edittd.edui-icon {
  background-position: -700px -40px;
}

.edui-for-snapscreen.edui-icon {
  background-position: -581px -40px
}

.edui-for-scrawl.edui-icon {
  background-position: -801px -41px
}

.edui-for-background2.edui-icon {
  background-position: -680px -40px;
}

.edui-for-music.edui-icon {
  background-position: -18px -40px
}

.edui-for-formula.edui-icon {
  background-position: -200px -40px
}

.edui-for-aligntd .edui-icon {
  background-position: -236px -76px;
}

.edui-for-insertparagraphtrue .edui-icon {
  background-position: -625px -76px;
}

.edui-for-insertparagraph .edui-icon {
  background-position: -602px -76px;
}

.edui-for-insertcaption .edui-icon {
  background-position: -336px -76px;
}

.edui-for-deletecaption .edui-icon {
  background-position: -362px -76px;
}

.edui-for-inserttitle .edui-icon {
  background-position: -286px -76px;
}

.edui-for-deletetitle .edui-icon {
  background-position: -311px -76px;
}

.edui-for-aligntable .edui-icon {
  background-position: -440px 0;
}

.edui-for-tablealignment-left .edui-icon {
  background-position: -460px 0;
}

.edui-for-tablealignment-center .edui-icon {
  background-position: -420px 0;
}

.edui-for-tablealignment-right .edui-icon {
  background-position: -480px 0;
}

.edui-for-drafts.edui-icon {
  background-position: -560px 0;
}

.edui-for-charts .edui-icon {
  /* background: url(/img/charts.png) no-repeat 2px 3px!important; */
}

.edui-for-inserttitlecol .edui-icon {
  background-position: -673px -76px;
}

.edui-for-deletetitlecol .edui-icon {
  background-position: -698px -76px;
}

.edui-for-simpleupload .edui-icon {
  background-position: -380px 0px;
}

.edui-for-markspace.edui-icon {
  background-image: url(/public/img/editor/markspace.svg);
  background-position: 2px 2px;
  background-size: 16px 16px;
  background-repeat: no-repeat;

}

.edui-for-removespace.edui-icon {
  background-image: url(/public/img/editor/removespace.svg);
  background-position: 2px 2px;
  background-size: 16px 16px;
  background-repeat: no-repeat;

}


