import { jiangmenzhongxinCAPinToken, jiangmenzhongxinCAPinSign } from '$supersetApi/projects/apricot/case/report.js'
import { getBPMSetFindKeys } from '$supersetApi/projects/apricot/system/bpmSet.js'
const mixinJiangMen = {
    data () {
        return {
            mxReportCloudSignLoginType: localStorage.getItem('reportCloudSignLoginType') ?? 1,
            mxPinForm: {
                sPass: ''
            },
            mxIsOpenPinCA: false
        }
    },
    methods: {
        mxClickCloudSignLoginType(value) {
            this.mxReportCloudSignLoginType = value;
            this.setReportCloudSignLoginType(value);
        },
        setReportCloudSignLoginType () {
            localStorage.setItem('reportCloudSignLoginType', this.mxReportCloudSignLoginType);
        },
        getReportCloudSignPinAuthId () {
            return localStorage.getItem('reportCloudSignPinAuthId');
        },
        setReportCloudSignPinAuthId (value) {
            return localStorage.setItem('reportCloudSignPinAuthId', value);
        },
        mxHandleJiangMenSign () {
            const sAuthId = this.getReportCloudSignPinAuthId();
            const businessType = this.signBusinessType;
            if (this.mxReportCloudSignLoginType == 2 && sAuthId) {
                let params = {
                    sPatientId: this.patientInfo.sId, //this.patientInfo.sId
                    sUserId: this.userInfo.sId,
                    sUserCode: this.userInfo.sNo,
                    sUserName: this.userInfo.sName,
                    sAuthId: sAuthId,
                    iBusinessType: businessType
                }
                jiangmenzhongxinCAPinSign(params).then(res => {
                    if (res.success) {
                        if (res.data.iReshow) {
                            // 需重新登录
                            this.mxPinForm.sPass = '';
                            this.isShowCloudSignature = true;
                            return
                        }
                        this.closeCloudQRCode();
                        if (businessType == 1) {
                            this.signStatus.signSavedStatus = res.success
                        }
                        // 2审核
                        if (businessType == 2) {
                            this.signStatus.signAuditedStatus = res.success
                            // key过期签名后重新审核
                            this.requestauditReport(1)
                        }
                        if (businessType == 3) {
                            this.signStatus.signReAuditedStatus = res.success
                            this.requestFinalAuditReport(1)
                        }
                        if (businessType == 4) {
                            this.signStatus.signCommitedStatus = res.success
                            this.requesCommitReport()
                        }
                        return
                    }
                    this.$message.error(res.msg)
                })
            }
        },
        mxClickJiangMenPin () {
            if(!this.mxPinForm.sPass) {
                this.$message.warning('请输入密码！');
                return
            }
            let params = {
                userCode: this.userInfo.userNo,
                pin: this.mxPinForm.sPass
            }
            jiangmenzhongxinCAPinToken(params).then(res => {
                if (res.success) {
                    this.setReportCloudSignPinAuthId(res.data);
                    this.mxHandleJiangMenSign()
                    return
                }
                this.$message.error(res.msg)
            })
        },
        // 判断是否开启CA Pin签章
        async judgeCaLoginInPin() {
            let open = false;
            this.mxIsOpenPinCA = false;
            const params = ['LoginInPin']
            await getBPMSetFindKeys(params).then(res => {
                if(res.success) {
                    const data = res?.data?.[0] || {};
                    open = (data?.sValue || 0) == 1;
                    this.mxIsOpenPinCA = open
                }
            }).catch(err => {
                console.log(err);
            })
        },
    }
}
export default mixinJiangMen;
