import { saveConfig, queryBy<PERSON>ey } from '$supersetApi/userConfig.js';

export function useUserConfigQueryByKey() {
    // configKey  配置键名，
    // _this   vue对象，
    // defaultConfig  默认配置，没有获取到配置数据时，赋值本地数据；
    return async function (configKey, _this, defaultConfig = {}) {
        let params = {
            configKey: configKey,
            moduleId: _this.iModuleId,
            userNo: _this.$store.getters['user/userSystemInfo'].sNo
        }
        const personalOnlineStorage = _this.$store.getters['user/personalOnlineStorage'];
        if (personalOnlineStorage?.[params.moduleId]?.[params.configKey]) {
            _this.MngIndexPenalConfig = personalOnlineStorage?.[params.moduleId]?.[params.configKey];
            return 
        }
        await queryByKey(params)
            .then(async (res) => {
                if (res.success) {
                    let data = res.data ? JSON.parse(res.data) : defaultConfig;
                    if(res.data && Object.prototype.toString.call(defaultConfig)==='[object Object]') {
                        // 将本地数据与获取到的数据合并
                        let target = {};
                        Object.keys(defaultConfig).map(key => {
                            target[key] = data[key] ?? defaultConfig[key]
                        })
                        data = target
                    }
                    const payload = { 
                        configKey: params.configKey,
                        configValue: data,
                        moduleId: params.moduleId,
                    }
                    await _this.$store.dispatch('user/setPersonalOnlineStorage', payload)
                    _this.MngIndexPenalConfig = data;
                    return;
                }
                _this.$message.error(res.msg);
            })
            .catch((err) => {
                console.log(err);
            });
    };
}

export function useUserConfigSaveConfig() {
    // requestParams  请求入参，
    // _this   vue对象，
    // callback  回调函数
    return async function (requestParams, _this, callback) {
        await saveConfig(requestParams)
            .then((res) => {
                if (res.success) {
                    _this.$message.success(res.msg);
                    const payload = { 
                        configKey: requestParams.configKey,
                        configValue: JSON.parse(requestParams.configValue) ,
                        moduleId: requestParams.moduleId,
                    }
                    _this.$store.dispatch('user/setPersonalOnlineStorage', payload)
                    callback && callback()
                    return;
                }
                _this.$message.error(res.msg);
            })
            .catch((err) => {
                console.log(err);
            });
    };
}
