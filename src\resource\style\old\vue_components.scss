$borderColor: #cdcecf;
$borderColor2: #DCDCDC;

.m-titlePanel1 {
    height: 100%;
    display: flex;
    flex-direction: column;

    >h5 {
        flex-shrink: 0;
        height: 30px;
        line-height: 28px;
        margin: 0;
        padding: 0 10px;
        border: solid 1px #DCDCDE;
        background-color: #F4F8FB;
    }

    >div {
        flex: 1;
        padding: 0;
        overflow: auto;
    }
}

.m-titlePanel2 {
    margin-top: 15px;

    >h5 {
        margin: 0;
        margin-left: 5px;
        margin-bottom: 6px;
        /* height: 24px; */
        /* line-height: 24px; */
        padding: 0 10px 0 6px;
        color: #3e3e3e;
        font-weight: bold;
        /* border-top: solid 1px #eee; */
        border-left: solid 4px #4597DA;
    }

    >div {
        padding: 4px;
    }

    &:first-child {
        margin-top: 5px;

        >h5 {
            border-top: none;
        }
    }
}


/*带label的只读信息--------------------------*/

.m-labelInfo {
    float: left;
    display: flex;
    flex-direction: row;
    margin: 0 10px 5px 0;
    line-height: 20px;

    >label {
        &:after {
            content: "：";
        }
    }

    >span {
        flex: 1;
        padding: 0 7px;
        border-bottom: solid 1px $borderColor;
        text-align: left;
        overflow: hidden;
        text-overflow: ellipsis;
        /*超出显示省略号*/
        white-space: nowrap;
        /*强制单行显示*/
    }

    // 稍微大一点的字体样式
    &.u-font-update1 {
        >span {
            font-size: 16px;
            font-weight: bolder;
        }
    }

    // 没有下划线
    &.u-border-none {
        >span {
            border-bottom: none;
        }
    }
}


/*--------------------------带label的只读信息*/


// 覆盖element table input --- hwl
// .xx-device-button,
// .xx-superset {

//     .c-convenientSearch,
//     .c-convenientSearch+.plan-item-01 {
//         background-color: #fafafa;
//         border-top: 10px solid #eee;
//     }

//     .el-form-item {
//         margin-bottom: 0;
//     }

//     .el-table {

//         // border-top-color: #F0F3F3;
//         td {
//             // padding: 8px 0px;
//             // height: 39px;
//             box-sizing: border-box;
//             // &.is-left:not(.is-hidden){
//             //     border-right: 1px solid #DCDCDC !important;
//             // }
//         }

//         th {
//             // background-color: #A4AEB8;
//             // border-bottom: 2px solid #94999D !important;
//             // color: #ffffff;
//             // font-size: 14px;
//             padding: 5px 0px;
//             font-weight: bold;
//             background-color: var(--theme-table-header-bg-color);
//             // font-size: 14px;
//             color: var(--theme-color);
//             border-bottom: 1px solid #dddddd !important;
//             border-right: none;
//         }

//         .el-table__fixed-body-wrapper {
//             border-right: 1px solid var(--theme-card-block-border) !important;
//         }

//         .el-table__fixed {
//             height: auto !important; // 此处的important表示优先于element.style
//             bottom: 10px; // 改为自动高度后，设置与父容器的底部距离，则高度会动态改变

//             &:before {
//                 display: none;
//             }
//         }

//         // 斑马纹颜色
//         &.el-table--striped .el-table__body tr.el-table__row--striped td {
//             // background: #F6F9FA;
//         }

//         .el-table__body tr.current-row>td {
//             // background-color: #b9d6f0;
//             // font-weight: bold;
//             // color: #303030;
//         }

//         &.el-table--striped .el-table__body tr.el-table__row--striped.current-row td {
//             // background-color: #b9d6f0;
//         }

//         // 移入行样式
//         .el-table__body tr.hover-row.current-row>td,
//         .el-table__body tr.hover-row.el-table__row--striped.current-row>td,
//         .el-table__body tr.hover-row.el-table__row--striped>td,
//         .el-table__body tr.hover-row>td {
//             // background-color: #e1edf8
//         }

//         &.el-table--border td,
//         &.el-table--border th,
//         &.el-table__body-wrapper y.el-table--border.is-scrolling-left~.el-table__fixed {
//             border-right: none;
//         }

//         // 没有下划线
//         &.el-table td,
//         &.el-table th.is-leaf {
//             border-bottom: none;
//         }

//         .caret-wrapper {
//             height: 20px;
//         }

//         .sort-caret.ascending {
//             top: -3px;
//         }

//         .sort-caret.descending {
//             bottom: 0px;
//         }
//     }

//     .xx-u-border,
//     &.xx-u-border {

//         // 下划线
//         .el-table td,
//         .el-table th.is-leaf,
//         &.el-table td,
//         &.el-table th.is-leaf {
//             border-bottom: 1px solid var(--theme-card-block-border);
//         }
//     }

//     .xx-table-border,
//     &.xx-table-border {

//         // 下划线
//         .el-table td,
//         .el-table th.is-leaf,
//         &.el-table td,
//         &.el-table th.is-leaf {
//             border-bottom: 1px solid var(--theme-card-block-border);
//             border-right: 1px solid var(--theme-card-block-border);
//         }
//     }

//     .el-form-item__content {
//         line-height: initial;
//         width: 100%;
//     }

//     .m-labelInput>label {
//         height: 20px;
//         line-height: 20px;
//         margin-left: 10px;
//         padding-left: 0px;
//         top: -2px;
//         // background: white;
//         border-radius: 2px;

//         &:before {
//             top: 50%;
//         }
//     }

//     .scope--rules .m-labelInput {
//         margin-bottom: 11px;
//     }

//     // .el-form-item__error {
//     //     font-size: 12px;
//     //     position: absolute;
//     //     bottom: -2px;
//     //     left: 10px;
//     //     top: initial;
//     //     padding-top: 0px;
//     // }
//     .el-input-number .el-input-number__decrease {
//         line-height: 14px !important;
//     }

//     .el-input-number.is-controls-right .el-input-number__decrease {
//         bottom: 2px;
//     }
// }

.el-time-panel {
    width: 160px !important;
}


/* 校验必填的提示样式------------------------------------------------------------------------- */
.el-form-item.is-required:not(.is-no-asterisk) .m-labelInput>label {
    padding-left: 10px;

    &:after {
        content: "";
        width: 7px;
        height: 7px;
        position: absolute;
        background-color: #F56C6C;
        border-radius: 50%;
        left: 0;
        top: 8px;
    }
}

/* -------------------------------------------------------------------------校验必填的提示样式 */
/* 弹出确认框颜色覆盖*/
.xx-button-white {
    color: #606266 !important;
    background-color: white !important;
    border-color: white !important;
}

/*/

/* 滚动条底部出现灰色问题 */
.xx-el-scrollbar .el-scrollbar__wrap {
    height: 100%;
    overflow-x: auto;
}

/**/

/*覆盖 element 提示框确定改在左侧*/
.el-message-box__btns {
    >button:first-child {
        // float: right;
        margin-left: 10px;
    }
}

/**/


/* element 输入框提示，按照内容自动撑开*/
.global-autocomplete {
    width: auto !important;
}

/**/
