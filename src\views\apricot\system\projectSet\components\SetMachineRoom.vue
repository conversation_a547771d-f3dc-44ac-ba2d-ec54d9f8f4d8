<template>
    <!-- 项目设置 机房 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div>
                <el-button-icon-fa type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxGetTableList">刷新</el-button-icon-fa> 
            </div>
        </div>
            
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table v-if="reRender" 
                    :data="tableData"
                    v-drag:[config]="tableData"
                    :row-class-name="tableRowClassName"
                    ref="mainTable"
                    size="small"
                    id="machineRoomTable"
                    :default-sort="{ prop: 'sDistrictName', order: 'ascending' }"
                    @row-dblclick="mxOpenDialog(4, '111')"
                    @row-click="onClickRow"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort">
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button size="small"
                                    link
                                    type="primary"
                                    @click="handleEdit(scope.row)"
                                    >编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" 
                                    link 
                                    @click="onClickDel(scope.row)">
                                        删除
                                        <template #icon>
                                            <Icon name="el-icon-delete" color="">
                                            </Icon>
                                        </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class="i-sort">
                                    排序
                                    <template #icon>
                                        <Icon name="el-icon-rank" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <!-- <i class="el-icon-rank i-sort">&nbsp;排序</i> -->
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                <span v-else> 否 </span>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                        <!-- <template v-slot:header="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex">
                <el-form :model="editLayer.form"
                    ref="refEditLayer"
                    label-width="120px"
                    :rules="rules">
                    <el-col :span="24">
                        <el-form-item prop="sDistrictId" label="院 区：">
                            <el-select style="width:100%"
                                v-model="editLayer.form.sDistrictId"
                                placeholder="院区"
                                @change="onChangeHospital"
                                >
                                <el-option v-for="item in districtData"
                                    :key="item.sValue"
                                    :value="item.sValue"
                                    :label="item.sName">
                                </el-option>
                            </el-select>
                            <!-- <el-cascader
                                size="small"
                                v-model="editLayer.form.sDistrictId"
                                :props="sHospitalProps"
                                :options="districtData"
                                @change="changeHospital"></el-cascader> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sRoomName" label="机房名称：">
                            <el-input 
                                v-model="editLayer.form.sRoomName"
                                clearable
                                placeholder="机房名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sConsultRoomId" label="问诊室：">
                            <el-select style="width:100%" 
                                v-model="editLayer.form.sConsultRoomId" 
                                clearable
                                placeholder="问诊室">
                                <el-option v-for="item in optionsLoc['consultOptions']"
                                    :key="item.index" 
                                    :label="item.sName"
                                    :value="item.sValue"
                                    ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sInjectionRoomId" label="注射室：">
                            <el-select v-model="editLayer.form.sInjectionRoomId"
                                clearable
                                placeholder="注射室"
                                style="width:100%">
                                <el-option v-for="item in optionsLoc['injectOptions']"
                                    :key="item.iIndex" 
                                    :label="item.sName"
                                    :value="item.sValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sDeviceTypeId" label="设备类型：">
                            <el-select v-model="editLayer.form.sDeviceTypeId"
                                clearable
                                placeholder="设备类型"
                                style="width:100%">
                                <el-option v-for="item in deviceTypeOptions"
                                    :key="item.sId"
                                    :label="item.sDeviceTypeName"
                                    :value="item.sId"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="设备型号：">
                            <el-input
                                v-model="editLayer.form.sEquipmentType"
                                clearable
                                placeholder="设备型号"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="" label="启  用：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>  
                </el-form>
            </div>
            <template #footer><div>
                <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { getWorkStationData } from '$supersetApi/projects/apricot/system/workStation.js'
import { getOptionName } from '$supersetResource/js/tools'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SetMachineRoom',
    mixins: [mixinTable, mixinTableDrag],
    props: {
    },
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            configTable: [
                
                {
                    sProp: 'sDistrictName', sLabel: '院区',
                    sAlign: 'left', sMinWidth: '250px', 
                    // iSort: 1
                },
                {
                    sProp: 'sRoomName', sLabel: '机房名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                
                {
                    sProp: 'sDeviceTypeName', sLabel: '设备类型',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'sEquipmentType', sLabel: '设备型号',
                    sAlign: 'left', sMinWidth: '100px',
                },
                // {
                //     sProp: 'sRoomPrefix', sLabel: '机房前缀',
                //     sAlign: 'left', sMinWidth: '100px',
                // },
                {
                    sProp: 'sConsultRoomName', sLabel: '问诊室',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'sInjectionRoomName', sLabel: '注射室',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '80px',
                },
                {
                    sProp: 'action', sLabel: '操作',
                    sAlign: 'center', sWidth: '210px',
                    sFixed: 'right'
                }
            ],
            rules: {
                sDistrictId:[{required: true, message: '请选择院区'}],
                sRoomName: [{ required: true, message: '请输入名称' }],
                sDeviceTypeId: [{ required: true, message: '请选择设备类型' }],
            },
            tableData: [],
            loading: false,
            reRender: true,
            defualtVal: {
                editLayer: {
                }
            },
            condition: {
                // iIsEnable: '1'
            },
            page: { pageCurrent: 1, pageSize: 9999 },
            optionsLoc: {
                isEnable: [
                    { sValue: '', sName: '全部' },
                    { sValue: '1', sName: '启用' },
                    { sValue: '0', sName: '禁用' },
                ],
            },
            districtData:[],
            defaultProps: {
                children: 'children',
                label: 'sHospitalDistrictName'
            },
            selectedNode: {},
            sHospitalProps: {
                expandTrigger: 'hover',
                value:'sId',
                label:'sHospitalDistrictName',
                children: 'children',
            },
            isSearch: false,
            editLayer:{
                form:{
                    iIsEnable: 1,
                    sRoomName:'',
                    sDistrictId:'',
                    sConsultRoomId:'',
                    sDeviceTypeId:'',
                    sInjectionRoomId:'',
                    sEquipmentType:''
                }
            },
            deviceTypeOptions:[],// 获取 设备类型
            workStationsOptions:{consultOptions:[],injectOptions:[] },
            sortApi: Api.sortMachineRoom,
        }
    },
    computed:{
    },

    methods: {
        tableRowClassName({ row, rowIndex }) {
            if (this.dragRow.sDistrictId && row.sDistrictId !== this.dragRow.sDistrictId) {
                return "row-filtered";
            }
            return "";
        },
        onChangeHospital(val) {
            this.filterOption(val)
            const consultOptions = this.optionsLoc.consultOptions
            const injectOptions = this.optionsLoc.injectOptions
            this.editLayer.form.sConsultRoomId = consultOptions.length ? consultOptions[0].sValue : ''
            this.editLayer.form.sInjectionRoomId = injectOptions.length ? injectOptions[0].sValue : ''
        },
        filterOption(val) {
            if(val) {
                this.optionsLoc.consultOptions = this.workStationsOptions['consultOptions'].filter( item => item.districtId === val)
                this.optionsLoc.injectOptions = this.workStationsOptions['injectOptions'].filter( item => item.districtId === val)
            }
            
        },
        // 新增
        handleAdd() {
            const districtId = this.districtData.length ? this.districtData[0].sValue : ''
            const sDeviceTypeId = this.deviceTypeOptions.length ? this.deviceTypeOptions[0].sId : ''
            this.filterOption(districtId)
            const consultOptions = this.optionsLoc.consultOptions
            const injectOptions = this.optionsLoc.injectOptions
            let params ={
                iIsEnable: 1,
                sRoomName:'',
                sDistrictId: districtId,
                sConsultRoomId: consultOptions.length ? consultOptions[0].sValue : '',
                sDeviceTypeId: sDeviceTypeId,
                sInjectionRoomId: injectOptions.length ? injectOptions[0].sValue : '',
                sEquipmentType:''
            }
            
            this.editLayer.form = Object.assign({},params)
            this.dialogVisible = true
            this.dialogTitle = '新增'
            let timeout = setTimeout(()=>{
                this.$refs.refEditLayer.clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.filterOption(row.sDistrictId)
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({},row)
            this.$nextTick(()=>{
                this.$refs['refEditLayer'] && this.$refs['refEditLayer'].clearValidate();
            })
        },
        handleSave() {
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                    this.editLayer.loading = true
                    this.saveData(params)  
                }
            })
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledMachineRoom({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sRoomName}】 吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delMachineRoom({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        if (this.editLayer.form.sId && this.editLayer.form.sId === row.sId) {
                            this.mxOpenDialog(1, 'no-title')
                        }
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            params.sDistrictName = getOptionName(params.sDistrictId, this.districtData)
            params.sInjectionRoomName = getOptionName(params.sInjectionRoomId, this.workStationsOptions['injectOptions']); // 注射室
            params.sConsultRoomName = getOptionName(params.sConsultRoomId,  this.workStationsOptions['consultOptions']); // 问诊室
            params.sDeviceTypeName = getOptionName(params.sDeviceTypeId, this.deviceTypeOptions)
            if(params.sInjectionRoomName == null ) {
                params.sInjectionRoomName = ''
            }
            if(params.sConsultRoomName == null ) {
                params.sConsultRoomName = ''
            }
            if (this.actionState == 1) {
                Api.addMachineRoom(params).then((res) => {
                        this.editLayer.loading = false;
                        if (res.success) {
                            this.$message({
                                message: res.msg,
                                type: 'success',
                                duration: 3000
                            });
                            this.mxGetTableList();
                            this.mxOpenDialog(1, 'no-title');
                            this.dialogVisible = false
                            return;
                        }
                        this.$message({
                            message: res.msg,
                            type: 'error',
                            duration: 3000
                        });
                    }).catch(() => {
                        this.editLayer.loading = false;
                })
                
            } else {
                Api.editMachineRoom(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        this.mxOpenDialog(1, 'no-title');
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        // 获取院区数据
        getHospitalDate(){
            Api.getHospitalData().then((res) => {
                this.isSearch = true;
                if(res.success) {
                    this.districtData = res?.data || [];
                    if(this.districtData.length > 0) {
                        this.districtData.map(item =>{
                            item.sValue = item.sId
                            item.sName = item.sHospitalDistrictName
                        })
                    }
                    return
                }
            }).catch((err) =>{
                this.isSearch = true;
                console.log(err)
            })
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            this.loading = true
            const jsonData =  params.condition;
            Api.getMachineRoomData(jsonData).then((res) => {
                if (res.success) {
                    this.tableData = res?.data || [];
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                    // this.$nextTick( ()=>{
                    //     this.$refs.mainTable.sort('sDistrictName', 'ascending')
                    // })
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        // 获取设备下拉
        getDeviceOptions () {
            // this.deviceLoading = true;
            Api.getDeviceTypeData().then((res) => {
                if (res.success) {
                    this.deviceTypeOptions = res?.data || [];
                    this.deviceTypeOptions.map(item =>{
                        item.sName = item.sDeviceTypeName
                        item.sValue = item.sId
                    })
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => {
                // this.isSearch = true;
                // this.deviceLoading = true;
            })
        },
        // 获取问诊室，注射室工作站'ApricotReportConsult'  ApricotReportInject
        getWorkStationByType (val,typeCode) {
            // this.loading = true;
            getWorkStationData({
                districtId: val,
                stationTypeCode: typeCode,
            }).then((res) => {
                if (res.success) {
                         if(typeCode == 3) {
                            this.workStationsOptions['consultOptions'] = res.data;
                            if(this.workStationsOptions['consultOptions'].length) {
                                this.workStationsOptions['consultOptions'].map(item =>{
                                item.sValue = item.stationId
                                item.sName = item.stationName
                            })
                            }
                           
                            return
                        }
                        this.workStationsOptions['injectOptions'] = res.data;
                        if(this.workStationsOptions['injectOptions'].length) {
                            this.workStationsOptions['injectOptions'].map(item =>{
                                item.sValue = item.stationId
                                item.sName = item.stationName
                            })
                        }
                }
            }).catch((err) => {
                console.log(err)
            })
        },
    },
    mounted () {
        this.getWorkStationByType (null,3)
        this.getWorkStationByType (null,4)
        this.getHospitalDate()
        this.getDeviceOptions()
        
    },
};
</script>
<style lang="scss" scoped>

.i-sort {
    cursor: pointer;
    font-size: 13px;
}
</style>
