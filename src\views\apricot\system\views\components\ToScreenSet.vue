<template>
   <div class="mx-content">
        <h4 v-if="stationName" class="modulesName">
            <span >{{seletedNode.stationName}}</span>
        </h4>
        <div class="c-flex-auto">
            <el-table
                v-loading="loading"
                :data="screenTableData"
                size="small"
                
                border
                stripe
                style="width: 100%">
                <el-table-column label="呼叫屏名称" >
                    <template v-slot="{row,$index}">
                        <div class="c-cell">
                            <el-checkbox v-model="row.iIsLink"
                                @change="onChangeLinkScreen($event, row, $index,'iIsLink')"></el-checkbox>
                            <span class="c-name">{{row.sName}}</span>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="呼叫屏IP" >
                    <template v-slot="{row,$index}">
                        {{row.sIp}}
                    </template>
                </el-table-column>
                <el-table-column label="显示选项">
                    <template v-slot="{row,$index}">
                        <span style="margin-right:25px">
                            <el-checkbox 
                                :disabled="!row.iIsLink" 
                                v-model="row.iShowWait"
                                @change="onChangeLinkScreen($event, row, $index,'iShowWait')">显示候诊患者</el-checkbox>
                        </span>
                        <span>
                            <el-checkbox 
                                :disabled="row.iIsLink && row.iShowWait ?false:true" 
                                v-model="row.iShowPast"
                                @change="onChangeLinkScreen($event, row, $index, 'iShowPast')">显示过号患者</el-checkbox>
                        </span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/common/callSet.js'
// import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'


export default {
    name: 'ToScreenSet',
    //mixins: [mixinTable],
    components: {},
    props: {
        seletedNode: {
            type: Object,
            default: () => ({})
        },
        stationName: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            loading:false,
            screenTableData:[],
            sHospitalProps: {
                expandTrigger: 'hover',
                value:'sId',
                label:'sHospitalDistrictName',
                children: 'children',
            },
        }
    },
    watch: {
        seletedNode: {
        handler(newVal,oldVal) {
                if(newVal.stationId) {
                    this.getScreenList()  
                }
            },
            deep: true,
            immediate: true
        }
        
    },
    methods: {
        // 关联屏幕
        onChangeLinkScreen(newVal, row, index, sKey) {           
            if (!(Object.keys(this.seletedNode)).includes('stationId')) {
                this.$message({
                    message: '请选择工作站',
                    type: 'warning',
                    duration: 3000
                });
                this.screenTableData[index]['iIsLink'] = false;
                return
            }
            // 关闭屏幕推送
            if( !newVal && sKey=='iIsLink') {
                let jsonData = {
                    sId: row.sLinkId
                };
                Api.delScreenLink(jsonData).then( res=>{
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.getLinkScreen()
                        delete this.screenTableData[index].sLinkId
                        // console.log(this.screenTableData[index])
                        // this.tableData[index]['sDeviceTypeName'] = '';
                        return
                    }
                    
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch( ()=>{
                    this.screenTableData[index]['iIsLink'] = true;
                    console.log(err)
                })
                return
            }
            // 关联屏幕推送
            if(newVal && sKey=='iIsLink') {
                let jsonData = {
                    sScreenId: row.sId,
                    sStationId: this.seletedNode.stationId,
                    sStationTypeCode: this.seletedNode.stationTypeCode,
                    iShowWait: 1
                };
                Api.addLinkScreen(jsonData).then( res=>{
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.getLinkScreen()
                        return
                    }
                    this.screenTableData[index]['iIsLink'] = false;
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch( ()=>{
                    this.screenTableData[index]['iIsLink'] = false;
                    console.log(err)
                })
                return
            }
            // 设置显示列表屏幕推送
            let jsonData = {
                sId: row.sLinkId,
                sScreenId: row.sId,
                sStationId: this.seletedNode.stationId,
                sStationTypeCode: this.seletedNode.stationTypeCode,
                iShowWait: row.iShowWait == true ? 1:0,
                iShowPast: row.iShowPast == true ? 1:0
            };
            Api.addLinkScreen(jsonData).then( res=>{
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });   
                    // this.screenTableData[index][sKey] = newVal;
                    // debugger
                    this.getLinkScreen()
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch( ()=>{
                console.log(err)
            })
        },
        // 获取屏幕列表
        getScreenList() {
            let params = {
                sDistrictId:this.seletedNode.districtId , 
                // iIsEnable: 1,
            }
            this.loading = true
            Api.getSreenList(params).then( res =>{
                this.loading = false
                if(res.success) {
                    this.screenTableData = res.data?res.data:[]
                    this.getLinkScreen()
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });  
            }).catch( ()=>{
                this.loading = false
            })
        },
        // 获取工作站选择的屏幕
        getLinkScreen() {
            let params = {
                stationId: this.seletedNode.stationId,
            }
            Api.getModuleSreen(params).then( res=>{
                this.loading = false
                if(res.success) {
                    let dataArr = res.data || [];
                    this.screenTableData.forEach(item2 => {
                        item2['iIsLink'] = false
                        item2['iShowPast'] = false
                        item2['iShowWait'] = false
                        // item2['sDeviceTypeName'] = '';
                    });
                    if (dataArr.length && this.screenTableData.length) {
                        dataArr.forEach(item => {
                            this.screenTableData.forEach(item2 => {
                                if (item2.sId == item.sScreenId) {
                                    item2['iIsLink'] = true;
                                    item2['sLinkId'] = item.sId;
                                    item2['iShowPast'] = item.iShowPast == 1? true:false 
                                    item2['iShowWait'] = item.iShowWait == 1? true:false
                                }
                            })
                        })
                    }
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });     
            }).catch( ()=>{
                this.loading = false
                console.log(err)
            })
        },

    },
    async mounted () {    
        // await this.getScreenList()
        // this.getLinkScreen()

    },
    created () {

    }

};
</script>
<style lang="scss" scoped>
.mx-content {
    height: 100%;
    padding-top:15px;
    box-sizing: border-box;
    .modulesName {
        font-size: 16px;
        padding: 14px 0px 17px 15px;
        height: 18px;
        margin: 0;
        span {
            font-weight: bold;
        }
    }
    .c-flex-auto {
        padding: 0px 5px 5px 15px;
    }
}
.danger {
    color: rgb(245, 108, 108)
}
.action {
    margin-left: 10px;
    font-size: 15px;
    cursor: pointer;
}
.c-cell {
    display: flex;
    align-items: center;
    .c-name {
        padding-left: 5px;
    }
}
</style>
