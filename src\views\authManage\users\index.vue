<template>
  <LayoutTable>
    <template v-slot:header>
      <SearchList v-model="condition" v-model:list="searchList" :loading="loading" storageKey="AuthManageUsers"
        @changeSearch="mxDoSearch" @reset="onClickReset" :options="{}"></SearchList>
    </template>
    <template #action>
      <div class="flex-x">
        <div>
          <el-button @click="mxOpenDialog(1, '新增用户')" type="primary">
            <template #icon>
              <Icon name="el-icon-plus" color="white">
              </Icon>
            </template>
            新增</el-button>
        </div>
        <div>
          <el-button @click="onClickReset">
            <template #icon>
              <Icon name="el-icon-refresh-left">
              </Icon>
            </template>
            重置</el-button>
          <el-button type="primary" @click="mxDoSearch">
            <template #icon>
              <Icon name="el-icon-search" color="white">
              </Icon>
            </template>
            查询</el-button>
        </div>
      </div>
    </template>
    <template v-slot:content>
      <!-- 内容 -->
      <el-table :data="tableData" style="width: 100%" height="100%" v-loading="loading">
        <xxTableColumn v-model="showColumn" storageKey="AuthManageUsers" :isLang="false">
          <template #userTypeId="scope">
            {{ userTypeMap[scope.row.userTypeId] }}
          </template>
          <template #roleNameList="scope">
            <template v-if="scope.row.roleNameList.length">
              <!-- <el-tag v-for="(item, index) in scope.row.roleNameList" :key="index" class="mx-1" effect="dark">{{ item
              }}</el-tag> -->
              <span v-for="(item, index) in scope.row.roleNameList" :key="index" class="mx-1">
                {{ item }}
              </span>
            </template>
            <span v-else>暂无</span>

          </template>
          <template #gender="scope">
            <span>{{ scope.row.gender == 1 ? '男' : '女' }}</span>
          </template>

          <template #state="scope">
            <span :class="[scope.row.state == 1 ? 'text-color-1' : 'text-color-2']">{{
              STATES[scope.row.state] }}</span>
          </template>
          <template #action="scope">

            <el-button title="编辑" :disabled="false"
              @click="mxOnClickRowAction(scope.row, 2, '编辑')" plain link type="primary" size="small">
              编辑
              <template #icon>
                <Icon name="el-icon-edit" color="">
                </Icon>
              </template>
            </el-button>
            <el-button title="删除" :disabled="false"
              @click="mxOnClickRowDel(scope.row, `确定要删除【 ${scope.row.userName} 】吗？`)" plain link size="small">
              删除
              <template #icon>
                <Icon name="el-icon-delete" color="">
                </Icon>
              </template>
            </el-button>
            <el-button title="重置密码" :disabled="false" @click="onClickResetPw(scope.row)"
              plain link size="small">
              重置密码
              <template #icon>
                <Icon name="el-icon-lock" color="">
                </Icon>
              </template>
            </el-button>
          </template>
        </xxTableColumn>
      </el-table>
      <!-- 新增-编辑弹窗  -->
      <el-dialog :close-on-click-modal="false" append-to-body :title="editLayer.playerText" v-model="editLayer.visible"
        :destroy-on-close="true" width="500px">
        <el-form ref="refEditLayer" :model="editLayer.form" :rules="rules">
          <el-row :gutter="24" style="margin-right: 20px;">
            <el-col :span="24">
              <el-form-item prop="userName" label="姓名" class="item-block" labelWidth="90px">
                <el-input v-model="editLayer.form.userName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="userNo" label="工号" class="item-block" labelWidth="90px">
                <el-input v-model="editLayer.form.userNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="gender" label="性别" class="item-block" labelWidth="90px">
                <el-select v-model="editLayer.form.gender" style="width: 100%">
                  <el-option label="男" :value="1"></el-option>
                  <el-option label="女" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="idNumber" label="身份证" class="item-block" labelWidth="90px">
                <el-input v-model="editLayer.form.idNumber"></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item prop="roleList" label="角色" class="item-block" labelWidth="90px">
                <el-select v-model="editLayer.form.roleList" multiple placeholder="选择">
                  <el-option v-for="item in roleList" :key="item.roleCode" :label="item.roleName"
                    :value="item.roleCode" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item prop="userTypeId" label="用户类型" class="item-block" labelWidth="90px">
                <el-select v-model="editLayer.form.userTypeList" multiple style="width: 100%">
                  <el-option v-for="item in userTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item prop="userTypeId" label="医生级别" class="item-block" labelWidth="90px">
                <el-select v-model="editLayer.form.docLevelId" style="width: 100%">
                  <el-option v-for="item in doctorLevelOptions" :key="item.value" :label="item.label"
                    :value="+item.value">
                  </el-option>
                  <el-option value="" label="无"></el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item prop="state" label="状态" class="item-block" labelWidth="90px">
                <el-select v-model="editLayer.form.state" style="width: 100%">
                  <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item prop="expireDate" label="过期日期" class="item-block" labelWidth="90px">
                <el-date-picker v-model="editLayer.form.expireDate" type="datetime" :placeholder="('过期日期')"
                  value-format="YYYY-MM-DD HH:mm:ss" />
              </el-form-item>
            </el-col>

          </el-row>
        </el-form>
        <template #footer>
          <div class="text-right">
            <el-button oldicon="document-checked" @click="mxDoSaveData('refEditLayer')" :loading="editLayer.loading"
              type="primary" class="i-primary">
              <template #icon>
                <Icon name="el-icon-folder-add" color="white">
                </Icon>
              </template>
              保 存</el-button>
            <el-button oldicon="close" @click="editLayer.visible = false">
              <template #icon>
                <Icon name="el-icon-close" color="">
                </Icon>
              </template>
              关 闭
            </el-button>

          </div>
        </template>

      </el-dialog>
    </template>
    <template #footer>
      <!-- 分页 -->
      <el-pagination @size-change="onSizeChange" @current-change="onCurrentChange" :current-page="page.pageCurrent"
        :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize" background
        layout="total, sizes, prev, pager, next" :total="page.total">
      </el-pagination>
    </template>
  </LayoutTable>
</template>
<script>
import { ElMessage } from 'element-plus'

// 接口
import ApiTable from '@/api/auth/user';
import apiRoles from "@/api/auth/roles";

import TableCURDMixin from "@/resource/js/common/mixin/TableCURDMixin";
import TableAPIMixin from "@/resource/js/common/mixin/TableAPIMixin";
import TableConfigs from "@/resource/js/common/mixin/TableConfigs";
const STATES = {
  '': '不限',
  1: "启用",
  2: "禁用",
};

const doctorLevelMap = {
  10: "实习医生",
  11: "规培医生",
  12: "进修医生",
  20: "报告医生",
  30: "审核医生",
  40: "复审医生"
}

const userTypeMap = {
  1: "预约护士",
  2: "签到护士",
  3: "问诊医生",
  4: "注射护士",
  5: "上机技师",
  6: "报告医生"
}

export default {
  name: 'AuthManageUsers',
  components: {
  },
  mixins: [TableCURDMixin, TableAPIMixin, TableConfigs],
  data() {
    return {
      STATES,
      userTypeMap,
      ApiTable,
      areaList: [],
      roleList: [],
      authCheckList: [],
      isShowAreaAuth: false,
      AreaAuthUserId: false,
      popoverUserTypeVisible: false,
      popoverRow: {},
      searchList: [
        { label: '姓名', prop: 'userName', componentType: 'el-input', width: '30%' },
        { label: '工号', prop: 'userNo', componentType: 'el-input', width: '30%' },
      ],
      showColumn: [
        { prop: 'userName', label: '姓名', width: 124, align: 'center', sortable: true },
        { prop: 'userNo', label: '工号', width: 124, align: 'center', sortable: true },
        { prop: 'gender', label: '性别', width: 80, align: 'center', sortable: true },
        { prop: 'userTypeName', label: '用户类型', align: 'center', width: 120 },
        { prop: 'roleNameList', label: '角色', align: 'center', width: null },
        { prop: 'docLevelName', label: '医生级别', align: 'center', width: null },
        { prop: 'state', label: '状态', align: 'center', width: 80 },
        { prop: 'expireDate', label: '过期日期', align: 'center', width: 150 },
        // { prop: 'createTime', label: '创建日期', align: 'center' },
        { prop: 'action', label: 'action', width: 220, fixed: 'right' },
      ],
      options: [
        { value: 1, label: '启用' },
        { value: 2, label: '禁用' }
      ],
      userTypeOptions: (this.$store.getters['dict/map'].UserType || []).map(k => ({ label: k.sName, value: k.sValue })),
      doctorLevelOptions: Object.keys(doctorLevelMap).map(k => ({ label: doctorLevelMap[k], value: +k })),
      defualtVal: {
        editLayer: {
          "docLevelId": 10,
          "docLevelName": "",
          "expireDate": "",
          "gender": 1,
          "idNumber": "",
          "roleList": [
            // {
            //   "roleCode": "",
            //   "roleName": "",
            //   "userId": ""
            // }
          ],
          "state": 1,
          "userId": "",
          "userName": "",
          "userNo": "",
          "userTypeId": 1,
          "userTypeName": ""
        }
      },
      rules: {
        userNo: [{ required: true, message: '必填' }],
        // password: [{ required: true, message: '必填' }],
        userName: [{ required: true, message: '必填' }],
      },
      condition: {
        userNo: '',
        userName: '',
        // email: '',
        // telephone: '',
        // state: '', 
      },
      keyOptions: [
        { label: '姓名', prop: 'userName', componentType: 'el-input', width: '30%' },
        { label: '工号', prop: 'userNo', componentType: 'el-input', width: '30%' },
        // { label: '用户状态', prop: 'state', componentType: 'el-select', width: '30%' },
      ],
      valueOptions: [
        {
          key: "state",
          options: Object.keys(STATES).map(key => {
            return {
              value: key,
              text: STATES[key]
            }
          })
        },
      ],

    }
  },
  watch: {
  },
  computed: {
    showTableCol() {
      return this.showColumn.filter(column => column.show);
    },
  },
  methods: {
    callBackPageList(params) {
        
      this.ApiTable.pageList(params).then((res) => {
        if (res.success) {
          this.tableData = (res.data.list || []).map(item => {
            return {
              ...item,
              roleList: (item.roleList || []).map(i => i.roleCode),
              roleNameList: (item.roleList || []).map(i => i.roleName),
              roleListString: (item.roleList || []).map(i => i.roleName).join(','),
              userTypeName: this.userTypeOptions.filter(o => item.userTypeList.includes(o.value)).map(i => i.label).join(','), 
              expireDate: item.expireDate ? moment(item.expireDate).format("YYYY-MM-DD HH:mm:ss") : '',
              createTime: item.createTime ? moment(item.createTime).format("YYYY-MM-DD HH:mm:ss") : ''
            }
          });
          this.page.total = res.data.total;
          this.loading = false;
          // 赋选中状态
          this.mxSetSelected();
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      })
    },
    callBackSave(_params) {
      const params = { ..._params }
      params.roleList = params.roleList.map(roleCode => {
        const target = this.roleList.find(item => item.roleCode === roleCode)
        return target ? { ...target, userId: params.userId } : null
      }).filter(i => !!i)

      params.docLevelName = doctorLevelMap[params.docLevelId] || ''

      delete params.createTime
      if (this.actionState == 1) {
        // 新增
        ApiTable.add(params).then((res) => {
          this.editLayer.loading = false;
          if (res.success) {
            ElMessage.success(res.msg);
            this.editLayer.visible = false;
            this.mxGetTableList();
            return;
          }
          ElMessage.error(res.msg);
        }).catch((res) => {
          this.editLayer.loading = false;
        })
      } else {
        // 编辑
        ApiTable.update(params).then((res) => {
          this.editLayer.loading = false;
          if (res.success) {
            ElMessage.success(res.msg);
            this.editLayer.visible = false;
            this.mxGetTableList();
            return;
          }
          ElMessage.error(res.msg);
        }).catch(() => {
          this.editLayer.loading = false;
        })
      }
    },
    callBackDel(item) {
      this.ApiTable.del({ userId: item.userId }).then((res) => {
        if (res.success) {
          ElMessage.success(res.msg);
          this.mxGetTableList();
          return;
        }
        ElMessage.error(res.msg);
      }).catch((e) => {
        console.log(e)
      })
    },
    onClickReset() {
      for (const key in this.condition) {
        if (this.condition.hasOwnProperty(key)) {
          this.condition[key] = ''
        }
      }
      this.mxDoSearch()
    },
    onClickResetPw(item) {
      this.$confirm( '确认要重置【' + item.userName + '】的密码？', '重置密码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ApiTable.resetPassword({
          userId: item.userId
        }).then(() => {
          ElMessage.success("重置成功");
        }).catch(() => {
        });
      }).catch((e) => {
        console.log(e)
      });
    }
  },
  mounted() {

    apiRoles.pageList({}).then((res) => {
      this.roleList = res.data
    })
      .catch(() => {
        ElMessage.error("获取角色数据出错");
      });
  }
}
</script>
<style lang="scss" scoped>
.flex-x {
  display: flex;
  justify-content: space-between;
}
</style>
