import { getOptionName } from '$supersetResource/js/tools'
//  混入 枚举表格内部打勾字段 时间段处理
const mixinTableInner = {
    data() {
        return {
            FlowStateEnum: ['iIsAppoint', 'iIsRegister', 'iIsConsult', 'iIsInject', 'iIsMachine',
                'iIsHoldOn', 'iIsImaging', 'iIsReport', 'iIsApprove', 'iIsCancel', 'iIsPrintText',
                'iIsPrintImg', 'iIsFinalApprove', 'iIsReportCommit'
            ],
            // FlowStateEnumText: {
            //     iIsAppoint: '已约',
            //     iIsRegister: '签到',
            //     iIsConsult: '问诊',
            //     iIsInject: '注射',
            //     iIsMachine: '上机',
            //     iIsHoldOn: '挂起',
            //     iIsImaging: '有图',
            //     iIsReport: '已写',
            //     iIsApprove: '审核',
            //     iIsCancel: '取消',
            //     iIsPrintText: '报告已打',
            //     iIsPrintImg: '图像已打',
            //     iIsFinalApprove: '复审'
            //     iIsReportCommit: '提交'
            // },
            FlowStateEnumIcon: {
                iIsAppoint: 'fa-appoint',
                iIsRegister: 'fa-register',
                iIsConsult: 'fa-stethoscope-1',
                iIsInject: 'fa-injection',
                iIsMachine: 'fa-machine',
                iIsHoldOn: 'fa-font-delay',
                iIsImaging: 'fa-image-1',
                iIsReport: 'fa-save',
                iIsApprove: 'fa-report-audit',
                iIsCancel: 'fa-cancle',
                iIsPrintText: 'fa-print-1',
                iIsPrintImg: 'fa-print-1',
                iIsFinalApprove: 'fa-report-1',
                iIsReportCommit: 'fa-tick-line'
            },
            flowTextColor: '#e6a23c',
            iIsPregnantOptions: [
                { sName: '否', sValue: 0 },
                { sName: '是', sValue: 1 },
                { sName: '未知', sValue: 2 },
                { sName: '不适用', sValue: 3 },
            ]
        }
    },
    methods: {
        // 表格行内处理    key-> name 怀孕字段转文字
        setPregnantText(val) {
            return getOptionName(val, this.iIsPregnantOptions);
        },
        // 表格行内处理 处方剂量值与其单位拼接
        setRecipeDose(val, sUnit) {
            val = (val ?? '') + '';
            sUnit = sUnit ?? '';
            return val.length ? val + sUnit: ''
        },
        // 表格行内处理 血糖单位值与其单位
        setBloodSugar(val) {
            val = (val ?? '') + '';
            return val.length ? val + 'mmol/L': ''
        }
    }
}
export default mixinTableInner;
