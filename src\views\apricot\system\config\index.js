export default {
    DA0: {
        type: 't-x',
        localStorageKey: '202009171013',
        panelConfig: [{
                size: 300,
                minSize: 300,
                maxSize: 500,
                name: "c1",
                isFlexible: false
            },
            {
                size: 0,
                minSize: 50,
                name: "c2",
                isFlexible: true
            }
        ]
    },
    TreeNodeConfigs: {
        system: [{
                sProp: 'sNodeLabel',
                sLabel: '节点标识',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 12
            },
            {
                sProp: 'sNodeName',
                sLabel: '节点名称',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 12
            },
            {
                sProp: 'sMemo',
                sLabel: '备注',
                sWidth: '100%',
                sInputType: 'text',
                iLayourValue: 12
            }
        ],
        module: [{
                sProp: 'sSystemLabel',
                sLabel: '系统标识',
                sWidth: '100%',
                iRequired: 1,
                iReadonly: 1,
                sInputType: 'text',
                iLayourValue: 12

            },
            {
                sProp: 'sNodeLabel',
                sLabel: '节点标识',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 12
            },
            {
                sProp: 'sNodeName',
                sLabel: '节点名称',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 12
            },
            {
                sProp: 'sMemo',
                sLabel: '备注',
                sWidth: '100%',
                sInputType: 'text',
                iLayourValue: 12
            }
        ],
        container: [{
                sProp: 'sSystemLabel',
                sLabel: '系统标识',
                sWidth: '100%',
                iRequired: 1,
                iReadonly: 1,
                sInputType: 'text',
                iLayourValue: 12

            },
            {
                sProp: 'sNodeLabel',
                sLabel: '节点标识',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 12
            },
            {
                sProp: 'sNodeName',
                sLabel: '节点名称',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'text',
                iLayourValue: 12
            },
            {
                sProp: 'sModelType',
                sLabel: '配置数据类型',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'option',
                sOptionProp: 'ModelTypeArray',
                iLayourValue: 12
            },
            {
                sProp: 'iSetClass',
                sLabel: '设置级别',
                sWidth: '100%',
                iRequired: 1,
                sInputType: 'option',
                sOptionProp: 'SetClassArray',
                iLayourValue: 12
            },
            {
                sProp: 'sMemo',
                sLabel: '备注',
                sWidth: '100%',
                sInputType: 'text',
                iLayourValue: 12
            }
        ],
        systemEdit: [],
        moduleEdit: [],
        containerEdit: []


    },
    searchElementConfig: [
    {
        sProp: 'iSetClass',
        sLabel: '设置级别',
        sWidth: '100%',
        iRequired: 1,
        sInputType: 'option',
        sOptionProp: 'SetClassArray',
        iLayourValue: 6,
        sConfigType: 'form',
        iCustom: 1,
    },
    {
        sProp: 'sDeviceId',
        sLabel: '设备类型',
        sWidth: '100%',
        iCustom: 1,
        iLayourValue: 6,
        sConfigType: 'form'
    }],
    inputElementConfig: [{
            sProp: 'sLabel',
            sLabel: '页面显示标题名称',
            sWidth: '100%',
            iRequired: 1,
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType:'table/form'
        },
        {
            sProp: 'iSetClass',
            sLabel: '设置级别',
            sWidth: '100%',
            iRequired: 1,
            sInputType: 'option',
            sOptionProp: 'SetClassArray',
            iLayourValue: 6,
            sConfigType: 'form',
        },
        {
            sProp: 'sDeviceId',
            sLabel: '设备类型',
            sWidth: '100%',
            iCustom: 1,
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sProp',
            sLabel: '属性名称',
            sWidth: '100%',
            iRequired: 1,
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'table/form'
        },
        // {
        //     sProp: 'sValue',
        //     sLabel: '默认值',
        //     sWidth: '100%',
        //     sInputType: 'text',
        //     iLayourValue: 6
        // },
        {
            sProp: 'sWidth',
            sLabel: '控件宽度',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'table/form'
        },
        {
            sProp: 'iLayourValue',
            sLabel: '栅栏布局值',
            sWidth: '100%',
            sInputType: 'number',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sHeight',
            sLabel: '控件高度',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'iRequired',
            sLabel: '必填',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'RequiredArray',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sFontSize',
            sLabel: '字体大小',
            sWidth: '100%',
            sInputType: 'text',
            iCustom: 1,
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sFontColor',
            sLabel: '字体颜色',
            sWidth: '100%',
            sInputType: 'color',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sBgColo',
            sLabel: '背景颜色',
            sWidth: '100%',
            sInputType: 'color',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'iFontWeight',
            sLabel: '字体加粗',
            sWidth: '100%',
            sInputType: 'option',
            iCustom: 1,
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sLabelFontSize',
            sLabel: '标题字体大小',
            sWidth: '100%',
            iCustom: 1,
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sLabelFontColor',
            sLabel: '标题字体颜色',
            sWidth: '100%',
            sInputType: 'color',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sLabelBgColor',
            sLabel: '标题字体背景',
            sWidth: '100%',
            sInputType: 'color',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'iLabelFontWeight',
            sLabel: '标题字体加粗',
            sWidth: '100%',
            sInputType: 'option',
            iLayourValue: 6,
            sConfigType: 'form',
            iCustom: 1,
        },
        {
            sProp: 'iLimitLength',
            sLabel: '限制长度',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'iReadonly',
            sLabel: '只读',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'ReadonlyArray',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'iIsHide',
            sLabel: '隐藏',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'IsHideArray',
            iLayourValue: 6,
            sConfigType: 'table/form'
        },
        {
            sProp: 'sInputType',
            sLabel: '表单类型',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'InputTypeArray',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sOptionProp',
            sLabel: '下拉相应码表',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'iCustom',
            sLabel: '自定义表单',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'IsCustomArray',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sRegEx',
            sLabel: '校验正则',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {
            sProp: 'sRegExText',
            sLabel: '校验提示文字',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'form'
        },
        {

            sProp: 'sFixed',
            sLabel: '列固定位置',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'FixedArray',
            iLayourValue: 6,
            sConfigType: 'table'
        },
        {
            sProp: 'sAlign',
            sLabel: '列对齐方式',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'AlignArray',
            iLayourValue: 6,
            sConfigType: 'table'
        },
        {
            sProp: 'sMinWidth',
            sLabel: '表格列最小宽度',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'table'
        },
        {
            sProp: 'iSort',
            sLabel: '列内容排序',
            sWidth: '100%',
            sInputType: 'option',
            sOptionProp: 'SortArray',
            iLayourValue: 6,
            sConfigType: 'table'
        },
        {
            sProp: 'sSortField',
            sLabel: '排序字段',
            sWidth: '100%',
            sInputType: 'text',
            iLayourValue: 6,
            sConfigType: 'table'
        },
        {
            sProp: 'iIndex',
            sLabel: '顺序',
            sWidth: '100%',
            sInputType: 'number',
            iLayourValue: 6,
            sConfigType: 'table/form'
        },
    ],
    optionsLoc: {
        SetClassArray: [{
                sName: '系统',
                sValue: '0'
            },
            {
                sName: '院区',
                sValue: '1'
            },
            {
                sName: '设备类型',
                sValue: '2'
            },
            {
                sName: '机房',
                sValue: '3'
            },
            {
                sName: '项目',
                sValue: '4'
            },
            {
                sName: '部门',
                sValue: '5'
            },
            {
                sName: '用户',
                sValue: '6'
            }
        ],
        ModelTypeArray: [{
                sName: 'form',
                sValue: 'form'
            },
            {
                sName: 'table',
                sValue: 'table'
            }
        ],
        RequiredArray: [{
                sName: '否',
                sValue: '0'
            },
            {
                sName: '是',
                sValue: '1'
            }
        ],
        ReadonlyArray: [{
                sName: '否',
                sValue: '0'
            },
            {
                sName: '是',
                sValue: '1'
            }
        ],
        IsHideArray: [{
                sName: '否',
                sValue: '0'
            },
            {
                sName: '是',
                sValue: '1'
            }
        ],
        InputTypeArray: [{
                sName: 'text',
                sValue: 'text'
            },
            {
                sName: 'date-picker',
                sValue: 'date-picker'
            },
            {
                sName: 'dateTime-picker',
                sValue: 'dateTime-picker'
            },
            {
                sName: 'option',
                sValue: 'option'
            },
            {
                sName: 'text-unit',
                sValue: 'text-unit'
            },
            {
                sName: 'number-unit',
                sValue: 'number-unit'
            },
            {
                sName: 'number',
                sValue: 'number'
            },
            {
                sName: 'textarea',
                sValue: 'textarea'
            },
            {
                sName: 'time-picker',
                sValue: 'time-picker'
            },
            {
                sName: 'input-01',
                sValue: 'input-01'
            },
            {
                sName: 'input-02',
                sValue: 'input-02'
            },
            {
                sName: 'option-01',
                sValue: 'option-01'
            }
        ],
        IsCustomArray: [{
                sName: '否',
                sValue: '0'
            },
            {
                sName: '是',
                sValue: '1'
            }
        ],
        FixedArray: [
            {
                sName: '无',
                sValue: null
            },
            {
                sName: '左',
                sValue: 'left'
            },
            {
                sName: '右',
                sValue: 'right'
            }
        ],
        AlignArray: [{
                sName: '左',
                sValue: 'left'
            },
            {
                sName: '中',
                sValue: 'center'
            },
            {
                sName: '右',
                sValue: 'right'
            }
        ],
        SortArray: [{
                sName: '否',
                sValue: '0'
            },
            {
                sName: '是',
                sValue: '1'
            }
        ],
        iSetClassOptions: [{
                sName: '否',
                sValue: '0'
            },
            {
                sName: '是',
                sValue: '1'
            }
        ],
        sFontWeight:[
            {
                sName: '正常',
                sValue: 400 ,
            },
            {
                sName: '加粗',
                sValue: 600,
            }
        ]
        
    },
    tableColumnConfig: [{
            sProp: 'action',
            sLabel: '排序',
            sAlign: 'center',
            sWidth: '80',
            className: "table/form"
        },
        // {
        //     sProp: "iSetClass",
        //     sLabel: "设置级别",
        //     sMinWidth: "90",
        //     className: "table"
        // },
        // {
        //     sProp: "sDeviceTypeName",
        //     sLabel: "设备类型",
        //     sMinWidth: "120",
        //     className: "form"
        // },
        // {
        //     sProp: "sItemName",
        //     sLabel: "项目",
        //     sMinWidth: "120",
        //     className: "form"
        // },
        // {
        //     sProp: "sSystemLabel",
        //     sLabel: "系统标识",
        //     sMinWidth: "100",
        //     className: "form/table"
        // },
        {
            sProp: "sLabel",
            sLabel: "页面显示标题名称",
            sMinWidth: "140",
            className: "form/table"
        },
        {
            sProp: "sProp",
            sLabel: "属性名称",
            sMinWidth: "150",
            className: "form/table"
        },
        {
            sProp: "iRequired",
            sLabel: "必填",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "iReadonly",
            sLabel: "只读",
            sAlign:'center',
            sWidth: "80",
            className: "form"
        }, {
            sProp: "iIsHide",
            sLabel: "隐藏",
            sAlign:'center',
            sWidth: "80",
            className: "form/table"
        },
        // {
        //     sProp: "sValue",
        //     sLabel: "默认值",
        //     sWidth: "120",
        //     className: "form"
        // },
        {
            sProp: "sWidth",
            sLabel: "控件宽度",
            sWidth: "100",
            sAlign:'center',
            className: "form/table"
        },
        {
            sProp: "iLayourValue",
            sLabel: "栅栏布局值",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sHeight",
            sLabel: "控件高度",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sInputType",
            sLabel: "表单类型",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "iCustom",
            sLabel: "自定义表单",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sOptionProp",
            sLabel: "下拉相应码表",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "iLimitLength",
            sLabel: "限制长度",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sBgColor",
            sLabel: "背景颜色",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sFontSize",
            sLabel: "字体大小",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sFontColor",
            sLabel: "字体颜色",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "iFontWeight",
            sLabel: "字体加粗",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sLabelFontSize",
            sLabel: "标题字体大小",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sLabelFontColor",
            sLabel: "标题字体颜色",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "sLabelBgColor",
            sLabel: "标题字体背景",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
        {
            sProp: "iLabelFontWeight",
            sLabel: "标题字体加粗",
            sWidth: "80",
            sAlign:'center',
            className: "form"
        },
       
        {
            sProp: "sFixed",
            sLabel: "列固定位置",
            sWidth: "100",
            sAlign:'center',
            className: "table"
        },
        {
            sProp: "sAlign",
            sLabel: "列对齐方式",
            sWidth: "100",
            sAlign:'center',
            className: "table"
        },
        {
            sProp: "sMinWidth",
            sLabel: "列最小宽度",
            sWidth: "100",
            sAlign:'center',
            className: "table"
        },
        {
            sProp: "iSort",
            sLabel: "列内容排序",
            sWidth: "100",
            sAlign:'center',
            className: "table",
        },
        {
            sProp: "iIndex",
            sLabel: "顺序",
            sWidth: "100",
            className: "form/table",
            iIsHide: 1
        }
    ],
    tableData: []
}