<template>
  <!-- 病例资料图片查看页面 -->
  <div class="g-content">
    <!-- 扫描件 -->
    <div class="c-content" ref="content">
      <img v-show="ImageUrls[curIdx]" :src="ImageUrls[curIdx]" @mousedown="myOnMove" @mousewheel="myOnWheel" ref="img"
        class="s-img" />
    </div>
    <div class="c-actions">
      <el-button-group>
        <el-button-icon-fa type="primary" icon="el-icon-refresh-left" @click="onRotate('right')">向左旋转90°</el-button-icon-fa>
        <el-button-icon-fa type="primary" icon="el-icon-refresh-right" @click="onRotate('left')">向右旋转90°</el-button-icon-fa>
        <el-button-icon-fa type="primary" icon="el-icon-arrow-left" @click="onSwitch('prev')">上一张</el-button-icon-fa>
        <el-button-icon-fa type="primary" icon="el-icon-arrow-right" @click="onSwitch('next')">下一张</el-button-icon-fa>
        <el-button-icon-fa type="primary" icon="el-icon-download" @click="onDownload">下载</el-button-icon-fa>
      </el-button-group>
    </div>
  </div>
</template>
<script>
import 'tiff.js';

import { getToken } from '$supersetUtils/auth'
import { getFiles } from '$supersetApi/projects/apricot/common/files.js'
export default {
  name: 'NewImagePage',
  components: {
    // LookImage,
  },
  data() {
    return {
      conditions: {},
      ImageUrls: [],
      xhrList: [],
      curIdx: 0,
      imgServerURL: `${window.configs.urls.apricot}/attachments/preview?sFilePath=`,
      downURL: `${window.configs.urls.apricot}/attachments/download?sCookie=${getToken()}&sFilePath=`,
      currentImg: '',
      scaleSize: 1,
      rotate: 0
    }
  },
  mounted() {
    document.title = '病例资料';
    let curIdx = this.$route.query.curIdx;
    this.curIdx = curIdx ? Number(curIdx) : 0;
    let { sPatientId, sInnerIndex, sNuclearNum, sName } = this.$route.query;
    if (sName) {
      document.title = `${sName}_${document.title}`
    }
    // console.log(sPatientId, sInnerIndex, sNuclearNum)
    if (sPatientId || sInnerIndex || sNuclearNum) {
      this.conditions = { sPatientId, sInnerIndex, sNuclearNum };
      this.getServerFiles(this.conditions);
      window.addEventListener('popstate', this.popstateFu, false);
    }
  },
  destroyed() {
    window.removeEventListener('popstate', this.popstateFu, false);

  },
  methods: {
    popstateFu() {
        setTimeout(() => {
            let curIdx = this.$route.query.curIdx;
            this.curIdx = curIdx ? Number(curIdx) : 0;
            this.resize()
        }, 100)
    },
    myOnMove(e) {
      // FireFox 图片拖拽出现禁止
      e.preventDefault();
      e.stopPropagation();
      let img = this.$refs.img;
      let content = this.$refs.content;
      let startX = e.pageX - img.offsetLeft;
      let startY = e.pageY - img.offsetTop;

      document.onmousemove = (ev) => {
        // IE等 图片拖拽出现禁止
        ev.cancelBubble = true;
        ev.returnValue = false;
        let oL = ev.pageX - startX
        let oT = ev.pageY - startY

        // 边界值检测
        let maxX = content.offsetWidth - (img.width / 14);
        let minX = img.width - (img.width / 14);
        let maxY = content.offsetHeight - (img.height / 14);
        let minY = img.height - (img.height / 14);
        oL > maxX ? oL = maxX : '';
        oL < -minX ? oL = -minX : '';
        oT > maxY ? oT = maxY : '';
        oT < -minY ? oT = -minY : '';

        img.style.left = oL + "px";
        img.style.top = oT + "px";
      }
      document.onmouseup = function () {
        document.onmousemove = document.onmouseup = null;
      }
      return false
    },
    myOnWheel(e) {
      let img = this.$refs.img;
      let content = this.$refs.content;
      let box = content.getBoundingClientRect();
      //以鼠标为中心缩放，同时进行位置调整
      // let deltaY = 0;
      let x = e.pageX;
      let y = e.pageY;
      e.preventDefault();
      if (e.target && (e.target === img)) {
        x = x - box.left;
        y = y - box.top;
        let p = -(e.deltaY) / 2000;
        let ns = this.scaleSize;
        ns += p;
        ns = ns < 0.1 ? 0.1 : (ns > 7 ? 7 : ns); // 可以缩小到0.1,放大到5倍
        // 计算位置，以鼠标所在位置为中心
        // 以每个点的x、y位置，计算其相对于图片的位置，再计算其相对放大后的图片的位置
        let bgX = parseFloat(img.style.left) - (x - parseFloat(img.style.left)) * (ns - this.scaleSize) / (this.scaleSize);
        let bgY = parseFloat(img.style.top) - (y - parseFloat(img.style.top)) * (ns - this.scaleSize) / (this.scaleSize);
        this.scaleSize = ns; //更新倍率
        img.style.width = parseFloat(img.naturalWidth) * ns + "px";
        img.style.height = parseFloat(img.naturalHeight) * ns + "px";
        img.style.top = bgY + "px";
        img.style.left = bgX + "px";
        // this.tip.msg = parseInt(ns * 100) + "%";
        // this.showTip()
      }
    },
    // 旋转
    onRotate(arg) {
      if (arg === 'left') {
        this.rotate == 360 ? this.rotate = 90 : this.rotate += 90
      } else {
        this.rotate == -360 ? this.rotate = -90 : this.rotate -= 90
      }
      let img = this.$refs.img;
      img.style.transform = "rotateZ(" + this.rotate + "deg)";
    },
    // 上下切换图
    onSwitch(arg) {
      // console.log(arg, this.curIdx)
      if (arg === 'prev' && this.curIdx > 0) {
        // 上
        this.onClickSelect(this.curIdx - 1);
      } else if (arg === 'next' && this.curIdx < (this.items.length - 1)) {
        // 下
        this.onClickSelect(this.curIdx + 1);
      } else {
        if (this.curIdx == 0) {
          this.$message.info('当前正在查看第一张图片！');
        } else {
          this.$message.info('当前正在查看最后一张图片！')
        }
      }
    },
    // 点击左侧选择其它图片
    onClickSelect(index) {
      // console.log(index)
      this.curIdx = index;
      this.resize();
    },
    onDownload() {
      if (!this.items.length) {
        this.$message.warning('文件不存在，无法下载！')
        return
      }
      let index = this.curIdx;
      let a = document.createElement('a');
      let content = this.$refs.content;
      let img = new Image()
      img.src = content.querySelector('img').src
      img.setAttribute('crossOrigin', 'Anonymous')
      img.onload = () => {
        let canvas = document.createElement('canvas')
        canvas.width = img.width
        canvas.height = img.height
        let ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        canvas.toBlob(blob => {
          a.href = window.URL.createObjectURL(blob)
          a.download = this.items[index].sUploadName;
          a.click()
        })

      }
      // a.href = this.downURL + this.items[index].sDiskSymbol + this.items[index].sFilePath;
      // a.setAttribute('target', '_blank'); 
    },
    // 重置图片  
    resize() {
      let content = this.$refs.content;
      let img = this.$refs.img;
      if (img == undefined) {
        return;
      }
      // 第一次加载确保图片已经加载完成
      img.addEventListener('load', () => {
        this.center(img, content)
      })
      // 在已经缓存的图片需要重置
      this.center(img, content)
    },
    center(img, content) {
      // console.log('naturalWidth=' + img.naturalWidth, 'naturalHeight' + img.naturalHeight)
      let offsetWidth = content.offsetWidth;
      let offsetHeight = content.offsetHeight;
      let img_Width = img.naturalWidth;
      let img_Height = img.naturalHeight;
      let multiple = 1;
      if (img_Height >= img_Width && img_Height > offsetHeight) {
        multiple = offsetHeight / img_Height
      }
      if (img_Width > img_Height && img_Width > offsetWidth) {
        multiple = offsetWidth / img_Width * 0.6
      }
      let left = (offsetWidth - img_Width * multiple) / 2;
      let top = (offsetHeight - img_Height * multiple) / 2;
      img.style.top = top + 'px';
      img.style.left = left + 'px';
      img.style.width = img_Width * multiple + 'px';
      img.style.height = img_Height * multiple + 'px';
      this.getImageViewerSetting()
      img.style.transform = 'rotateZ(' + this.rotate + 'deg)';
      this.scaleSize = multiple;
    },
    getImageViewerSetting() {
      let imageViewerSetting = localStorage.getItem('imageViewerSetting');
      imageViewerSetting = imageViewerSetting ? JSON.parse(imageViewerSetting) : {};
      this.rotate = 0;
      if (imageViewerSetting.rotate == 'clocelise') {
        this.rotate = 90
      } else if (imageViewerSetting.rotate == 'anticlocelise') {
        this.rotate = -90
      }
    },
    // 获取扫描资料列表
    getServerFiles(params) {
      this.ImageUrls = [];
      this.items = [];
      let jsonData = params;
      getFiles(jsonData).then(res => {
        if (res.success) {
          let serverImages = this.items = res.data || [];
          serverImages.map((item, index) => {
            let suffix = item.sFileType;
            let url = `${this.imgServerURL}${item.sDiskSymbol}${item.sFilePath}&sCookie=${getToken()}`
            item['url'] = url
            this.ImageUrls.push(url);
            if (suffix === 'tif' || suffix === 'tiff') {
              this.setTiffFile2DataURL(item, index);
            }
          })
          this.resize();
          return
        }
        this.$message.error(res.msg);
        return
      }).catch(err => {
        console.log(err)
      })
    },
    // tif文件 转 basedata
    setTiffFile2DataURL(item, index) {
      let filename = `${this.imgServerURL}${item.sDiskSymbol}${item.sFilePath}&sCookie=${getToken()}`
      let xhr = new XMLHttpRequest();
      xhr.open('GET', filename, true);
      xhr.responseType = 'arraybuffer';
      let that = this;
      xhr.onload = function (e) {
        var buffer = xhr.response;
        Tiff.initialize({ TOTAL_MEMORY: 50 * 1024 * 1024 }); //支持最大50M
        var tiff = new Tiff({
          buffer: buffer
        });
        var imgData = tiff.toDataURL();
        if (imgData) {
          item['url'] = imgData
          that.ImageUrls[index] = imgData;
        }
      }
      xhr.send();
      this.xhrList.push(xhr);
    },
  }
}
</script>
<style lang="scss" scoped>
.g-content {
  position: absolute;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  background: #eee;
  position: relative;

  .c-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 5px;
    background: rgba(0, 0, 0, 0.15);
    text-align: center;
  }

  .c-content {
    flex: 1;
    position: relative;
  }

  .s-img {
    cursor: pointer;
    position: absolute;
  }
}
</style>
