<template>
  <div class="form-item">
    <label v-if="label || offset" :style="labelStyle" class="form-item-label">
      <span v-if="required" class="required">*</span> {{ label }}
    </label>
    <div class="form-item-slot">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "FormItem",
  props: {
    label: { type: String },
    required: { type: Boolean },
    labelWidth: { type: String, default: '80px' },
    offset: { type: String },
  },
  data() {
    return {

    }
    
  },
  computed: {
    labelStyle() {
      if (!this.labelWidth) return {};
      return {
        width: this.labelWidth || "80px",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.form-item {
  display: inline-flex;
  position: relative;
  align-items: center;
  align-self: flex-start;
  margin: 5px 10px 5px 0;
  vertical-align: top;

  letter-spacing: normal;
  word-spacing: normal;
  text-rendering: auto;

  &:last-child {
    margin-right: 0;
  }

  [type="text"],
  select {
    width: 100%;
  }
  .form-item-label {
    align-self: flex-start;
    flex-shrink: 0;
    padding-right: 10px;
    line-height: 40px;
    text-align: center;
  }
  .form-item {
    align-self: flex-start;
    flex: 1;
    /* 统一 form control 行高 */
    line-height: 40px;
  }

  .required {
    color: red;
  }
}
</style>
