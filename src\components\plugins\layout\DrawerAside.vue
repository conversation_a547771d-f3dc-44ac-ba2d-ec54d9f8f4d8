<template>
  <div class="My-drawerAside" :class="{ 's-asideUnLock': !isAsideLock }">
    <div class="g-aside" :style="asideStyle">
      <div class="c-aside" :style="asideContentStyle">
        <div class="g-ctrl" >
          <!-- <div class="c-icon">
            <i v-show="isAsideLock" class="fa fa-lock-lump"></i>
            <i v-show="!isAsideLock" class="fa fa-unlock-lump"></i>
          </div> -->
          <h6>
            <i class="fa fa-navicon"></i>
            <span v-text="asideTitle"></span>
            <i class="fa fa-navicon"></i>
          </h6>
        </div>
        <div class="g-menu">
          <slot name="aside"></slot>
        </div>
      </div>
    </div>
    <div class="g-content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DrawerAside',
  data() {
    return {
      msg: 'heheda',
      // isAsideLock: true
    }
  },
  computed: {
    asideStyle() {
      return 'width:' + (this.isAsideLock ? this.asideWidth : this.spillage) + 'px;'
    },
    asideContentStyle() {
      let width = 'width:' + this.asideWidth + 'px;'
      let left = this.isAsideLock ? '' : 'left:' + (-(this.asideWidth - this.spillage)) + 'px;'
      return width + left
    }
  },
  props: {
    asideWidth: {
      type: Number,
      default: 220
    },
    asideTitle: {
      type: String,
      default: '导航栏'
    },
    spillage: {
      type: Number,
      default: 20
    },
    // 默认锁定导航栏
    isAsideLock: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    switchLockState() {
      // this.isAsideLock = !this.isAsideLock
      this.$emit('update:isAsideLock', !this.isAsideLock)
    }
  },
  created() {
  }
}
</script>

<style lang="scss" scoped>
.My-drawerAside {
  height: 100%;
  position: relative;

  >.g-aside {
    position: relative;
    float: left;
    height: 100%;
    transition: width .3s;
    margin-right: 8px;
    // background-color: var(--el-color-primary-light-9);
    background-color: #fff;
    z-index: 4;
    /*z-index:2 改为z-index:4 ---anni */

    >.c-aside {
      position: absolute;
      left: 0;
      display: flex;
      flex-direction: column;
      height: 100%;
    //   background-color: rgba(0, 0, 0, 0.1);
      transition: all .4s;
      

      >.g-ctrl {
        flex-shrink: 0;
        height: 45px;
        /*outline: solid 1px #0000FF;*/
        // cursor: pointer;
        user-select: none;
        position: relative;
        border-bottom: 1px solid #e3e3e3;;

        >h6 {
          margin: 0;
          line-height: 45px;
          text-align: center;
          font-size: 14px;

          >span {
            margin: 0 5px;
          }

          >i.fa {
            position: relative;
            top: 2px;
            font-size: 14px;
            opacity: .6;
          }
        }

        >.c-icon {
          position: absolute;
          top: 0;
          right: 0;
          padding: 0 5px;
          line-height: 30px;

          .fa-unlock-lump {
            opacity: .6;
          }
        }
      }

      >.g-menu {
        flex: 1;
        overflow: auto;
      }
    }

  }

  >.g-content {
    position: relative;
    height: 100%;
    overflow: hidden;
    background: #fff;
  }


}

/*菜单锁定后的状态-----------------------------------*/
.My-drawerAside.s-asideUnLock {
  >.g-aside {

    >.c-aside {
      background-color: rgba(240, 247, 255, .95);
    }

    &:hover {
      >.c-aside {
        left: 0 !important;
      }
    }
  }
}

/*-----------------------------------菜单锁定后的状态*/

.My-drawerAside {
  :deep(.g-content) {
    padding: 0;
  }

  :deep(.el-menu) {
    border-right: none;
  }

  :deep(.el-menu-item) {
    border-bottom: 1px solid #e9e9e9;
    > i {
        font-size: 18px;
    }
  }

  :deep(.el-menu-item.is-active) {
    color: var(--el-color-primary);
    border-right: 2px solid var(--el-color-primary);
    background-color: var(--el-color-primary-light-9);
    // color: #fff;
    // background-color: rgba(0, 0, 0, 0.3);
  }
  :deep(.el-menu-item:hover) {
    // background-color: rgba(0, 0, 0, 0.15);
  }
}
</style>
