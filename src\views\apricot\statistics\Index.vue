<template>
    <div class="stat-container">
        <DragAdjust :dragAdjustData="DA0">
            <template v-slot:c1>
                <div class="m-flexLaout-ty border border-solid border-y-0 border-gray-200 m-bg">
                    <div class="i-action" >
                        <el-button class="i-btn1" @click="onClickSetting"><i class="fa fa-cog"></i>
                            表格配置
                        </el-button>
                    </div>
                    <div class="g-flexChild ">
                        <el-tree :data="treeData" 
                            :props="defaultProps" 
                            ref="treeRef" 
                            node-key="sId"
                            :default-checked-keys="[selectedNode]" 
                            default-expand-all highlight-current
                            @node-click="handleNodeClick"></el-tree>
                    </div>
                </div>
            </template>
            <template v-slot:c2>
                <div class="m-flexLaout-tx m-bg">
                    <div class="c-item t-1 center-group-col ">
                        <div class="i-item" v-if="sItemType === 1">
                            <el-checkbox :indeterminate="isGroupIndeterminate" 
                                v-model="isCheckAllGroup" 
                                class="t-1"
                                @change="handleCheckGroupAllChange">
                                <span class=" text-lg font-bold">分组项</span> 
                            </el-checkbox>
                            <div class="ml-6">
                                <el-checkbox-group v-model="checkedGroupOptions" @change="handleCheckedGroupOptionsChange">
                                    <el-checkbox v-for="item in groupFields" 
                                        :label="item.sFieldName"
                                        :key="item.sFieldName">{{ item.sFieldDesc }}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                        <div class="i-item " v-if="sItemType === 1">
                            <el-checkbox :indeterminate="isTotalIndeterminate" 
                                v-model="isCheckAllTotal" 
                                class="t-1"
                                @change="handleCheckTotalAllChange">
                                <span class=" text-lg font-bold"> 求和项</span>
                            </el-checkbox>
                            <div class="ml-6">
                                <el-checkbox-group v-model="checkedTotalOptions" @change="handleCheckedTotalOptionsChange">
                                    <el-checkbox v-for="item in totalFields" 
                                        :label="item.sFieldName"
                                        :key="item.sFieldName">{{ item.sFieldDesc }}</el-checkbox>
                                </el-checkbox-group>
                            </div>
                        </div>
                        <div class="i-item" v-if="sItemType === 1">
                            <span class=" text-lg font-bold ml-4"> 图表项</span>
                            <div class="ml-6">
                                <el-radio-group v-model="eChartsGroup" 
                                    style="min-height: 50px" 
                                    @change="onSearchClick">
                                    <el-radio v-for="item in groupFields" 
                                        :label="item.sFieldName" 
                                        :key="item.sFieldName">{{ item.sFieldDesc }}</el-radio>
                                </el-radio-group>
                                <el-divider class=" divider"></el-divider>
                                <el-radio-group v-model="eChartsTotal"
                                    @change="onSearchClick">
                                    <el-radio v-for="item in totalFields" 
                                        :label="item.sFieldName" 
                                        :key="item.sFieldName">{{ item.sFieldDesc }}</el-radio>
                                </el-radio-group>
                            </div>
                        </div>

                        <div class="i-item" v-if="sItemType === 2">
                            <el-checkbox :indeterminate="isDisplayIndeterminate" 
                                v-model="isCheckAllDisplay" 
                                class="t-1"
                                @change="handleCheckDisplayAllChange">显示项</el-checkbox>
                            <el-checkbox-group v-model="checkedDisplayOptions" 
                                @change="handleCheckedDisplayOptionsChange">
                                <el-checkbox v-for="item in displayFields" :label="item.sFieldName"
                                    :key="item.sFieldName">{{ item.sFieldDesc }}</el-checkbox>
                            </el-checkbox-group>
                        </div>
                    </div>

                    <div class="c-item g-flexChild m-flexLaout-ty">
                        <div class="c-form m-flexLaout-tx u-autoHeight">
                            <div class="g-flexChild">
                                <SearchList v-model:modelValue="condition" :list="searchStyle" :optionData="optionsLoc" :iModuleId="iModuleId" storageKey="StatisticsIndexSearch">
                                    <template v-slot:startDate>
                                        <el-date-picker v-model="condition.startDate" type="date"
                                            :disabled-date="pickerOptionsStartDay" @change="onSearchClick" style="height:100%;">
                                        </el-date-picker>
                                    </template>
                                    <template v-slot:endDate>
                                        <el-date-picker v-model="condition.endDate" type="date"
                                            :disabled-dates="pickerOptionsEndDay" @change="onSearchClick" style="height:100%;">
                                        </el-date-picker>
                                    </template>
                                    <template v-for="item in searchStyle.filter(item => !['startDate', 'endDate'].includes(item.sProp))"
                                        v-slot:[item.sProp]>
                                        <el-select v-model="condition[item.sProp]" :ref="item.sProp" multiple
                                            collapse-tags clearable placeholder=" " class="my-multipSelect"
                                            popper-class="my-select-popper"
                                            @focus="()=>{onMutipleFocus(item.sProp)}"
                                            @visible-change="(bool) => !bool && onMutipleFinish(item.sProp)"
                                            @change="()=>onSelectChange(item.sProp, optionsLoc[item.sOptionProp])">
                                            <el-checkbox v-model="checkedKey[item.sProp]" 
                                                class="my-checkbox"
                                                @change="(val)=>selectAll(item.sProp , optionsLoc[item.sOptionProp])">全选</el-checkbox>
                                            <el-option v-for="(item, index) in optionsLoc[item.sOptionProp]"
                                                :key="index" :label="item.sName" :value="item.sValue">
                                            </el-option>
                                            <template v-if="focusItems[item.sProp]" #prefix>
                                                <el-button-icon-fa link class="el-input__icon" icon="el-icon-finished"
                                                    @click.stop="onMutipleFinish(item.sProp)"></el-button-icon-fa>
                                            </template>
                                            <!-- <div v-if="focusItems[item.sProp]" slot="prefix" class="my-prefix">
                                                <el-button link class="el-input__icon" icon="el-icon-finished"
                                                    @click.stop="onMutipleFinish(item.sProp)"></el-button>
                                            </div> -->
                                        </el-select>
                                    </template>
                                </SearchList>
                            </div>
                        </div>
                        <div class="c-btnArea">
                            <el-button-group>
                                <el-button title="报表" @click="handleChangeComponent('table')">
                                    <i class="fa fa-table" :class="{ 'el-button--text': componentType === 'table' }"></i>
                                </el-button>
                                <el-button v-if="sItemType === 1" title="图表" @click="handleChangeComponent('echarts')">
                                    <i class="fa fa-area-chart"
                                        :class="{ 'el-button--text': componentType === 'echarts' }"></i>
                                </el-button>
                            </el-button-group>
                            <div style="float:right;">
                                <el-button-icon-fa _icon="fa fa-rotate-right" @click="onResetClick">重置</el-button-icon-fa>
                                <el-button-icon-fa v-auth="'report:statistics:query'" type="primary" _icon="fa fa-search" :loading="loading"
                                    @click="onSearchClick">查询</el-button-icon-fa>
                            </div>
                        </div>
                        <div class="g-flexChild">
                            <MyTable v-show="componentType === 'table'" :title="selectedNode.label" :tableProps="tableProps"
                                :tableData="tableData" :eChartsData="eChartsData" :pageBaseInfo="pageBaseInfo"></MyTable>
                            <ECharts v-show="componentType === 'echarts'" :title="selectedNode.label"
                                :componentType="componentType" :eChartsData="eChartsData" :pageBaseInfo="pageBaseInfo">
                            </ECharts>
                        </div>
                    </div>
                </div>
            </template>
        </DragAdjust>
        <Setting :dialogVisible="d_Setting_v" :FieldsObject="FieldsObject" @closeDialog="closeDialog"></Setting>
    </div>
</template>

<script>
// 组件
import MyTable from '$supersetViews/apricot/statistics/components/Table.vue'
import ECharts from '$supersetViews/apricot/statistics/components/ECharts.vue'
import Setting from '$supersetViews/apricot/statistics/components/Setting.vue'

import { transformDate, deepClone } from '$supersetUtils/function'
import Api from '$supersetApi/projects/apricot/statistics/index.js'
import { getReportAboveDrData  } from '$supersetResource/js/projects/apricot/useHandlerSelect.js' // 获取
import { getApplyDept, getApplyDr } from '$supersetApi/projects/apricot/retrieve'

export default {
    name: 'apricot_Statistics',
    components: {
        MyTable,
        ECharts,
        Setting
    },
    data () {
        return {
            iModuleId: 12, // STATISTICS 统计
            loading: false,
            initLoading: false,
            DA0: {
                type: 't-x',
                localStorageKey: '202106021431',
                panelConfig: [{
                    size: 250,
                    minSize: 200,
                    maxSize: 350,
                    name: 'c1',
                    isFlexible: false
                },
                {
                    size: 0,
                    minSize: 50,
                    name: 'c2',
                    isFlexible: true
                }]
            },
            treeData: [],
            defaultProps: {
                children: 'items',
                label: 'sName'
            },
            searchStyle: [{
                sProp: 'startDate',
                sLabel: '开始时间',
                sInputType: 'date-picker',
                iLayourValue: 3,
                iCustom: 1
            }, {
                sProp: 'endDate',
                sLabel: '结束时间',
                sInputType: 'date-picker',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sDistrictIds',
                sLabel: '院区',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'districts',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sRoomIds',
                sLabel: '设备类型',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'deviceTypes',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sMachineryRoomIds',
                sLabel: '机房',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'machineryRooms',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sProjectIds',
                sLabel: '检查项目',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'checkItems',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sSources',
                sLabel: '患者来源',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'sources',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sApplyDepartTexts',
                sLabel: '申请人科室',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'applyDepartItems',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sApplyPersonNames',
                sLabel: '申请医生',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'applyDocsItems',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sPracticeIds',
                sLabel: '书写医生',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'practiceDocsItems',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sReporterIds',
                sLabel: '报告医生',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'reportDocsItems',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sApproveIds',
                sLabel: '审核医生',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'approveDocsItems',
                iLayourValue: 3,
                iCustom: 1
            },
            {
                sProp: 'sFinalIds',
                sLabel: '复审医生',
                sHeight: '30px',
                sInputType: 'option',
                sOptionProp: 'finalDocsItems',
                iLayourValue: 3,
                iCustom: 1
            }],
            condition: {
                startDate: new Date(moment().startOf('month')),
                endDate: new Date(moment().endOf('month')),
            },
            optionsLoc: {
                applyPersons: [],
                checkItems: [],
                deviceTypes: [],
                districts: [],
                machineryRooms: [],
                sources: [],
                practiceDocsItems: [],
                reportDocsItems: [],
                approveDocsItems: [],
                finalDocsItems: [],
                applyDepartItems: [],
                applyDocsItems: []
            },
            pickerOptionsStartDay: time => {
                if (this.condition.endDate) {
                    return time.getTime() > new Date(this.condition.endDate).getTime() || time.getTime() > new Date().getTime()
                }
                return time.getTime() > new Date().getTime()
            },
            pickerOptionsEndDay: time => {
                if (this.condition.startDate) {
                    return time.getTime() < new Date(this.condition.startDate).getTime() || time.getTime() > new Date().getTime()
                }
                return time.getTime() > new Date().getTime()
            },
            componentType: 'table',
            selectedNode: {},
            d_Setting_v: false,
            sItemType: undefined,
            FieldsObject: {},

            isGroupIndeterminate: false,
            isCheckAllGroup: false,
            checkedGroupOptions: [],
            groupFields: [],

            isTotalIndeterminate: false,
            isCheckAllTotal: false,
            checkedTotalOptions: [],
            totalFields: [],

            isDisplayIndeterminate: false,
            isCheckAllDisplay: false,
            checkedDisplayOptions: [],
            displayFields: [],

            eChartsGroup: '',
            eChartsTotal: '',
            tableProps: [],
            tableData: [],
            eChartsData: [],
            pageBaseInfo: {},  // 页面基本信息 
            timeOutCount: null,
            focusItems: {},
            checkedKey: {}
        }
    },
    methods: {
        // 打开设置弹窗
        onClickSetting () {
            this.d_Setting_v = true;
        },
        // 关闭设置弹窗
        closeDialog (isSearch = false) {
            this.d_Setting_v = false;
            isSearch && this.getTreeData()
        },
        selectAll (sProp, options) {
            this.condition[sProp] = []
            if (this.checkedKey[sProp]) {
                options.map((item) => {
                    this.condition[sProp].push(item.sValue)
                })
            }
            this.onSearchClick()
        },
        onSelectChange (sProp, options) {
            if (this.condition[sProp].length === options.length) {
                this.checkedKey[sProp] = true
            } else {
                this.checkedKey[sProp] = false
            }
            this.onSearchClick()
        },
        onMutipleFocus (sProp) {
            this.focusItems[sProp] = true
        },
        onMutipleFinish (sProp) {
            this.$refs[sProp].blur()
            this.$nextTick(() => {
                this.focusItems[sProp] = false;
            });
        },
        // 点击树节点
        handleNodeClick (data) {
            this.selectedNode = data;
            this.pageBaseInfo.headline = data.sItemName;
            this.pageBaseInfo.searchTimeStr = '';
            this.pageBaseInfo.printTimeStr = '';
            this.tableData = [];
            this.tableProps = [];
            this.eChartsData = [];
            this.handleCheckBoxData(data);
            if (data.sItemId) {
                this.sItemType = data.sItemType;
                // this.findStatsItemById(data);
                // this.handleCheckBoxData(data);
                if (this.sItemType === 2) {
                    this.handleChangeComponent('table');
                }
                this.onSearchClick()
            }
        },
        // 将表格字段配置项过滤显示
        handleCheckBoxData (data) {
            console.log(data)
            // 复选框数组赋值
            let FieldsObject = deepClone(this.FieldsObject);
            const sDisplayFields = data.sDisplayFields ? data.sDisplayFields.split(',') : [];
            const sGroupFields = data.sGroupFields ? data.sGroupFields.split(',') : [];
            const sTotalFields = data.sTotalFields ? data.sTotalFields.split(',') : [];
            // 显示项
            const displayFields = FieldsObject.displayFields ?? [];
            const filterDisplayFields = displayFields.filter(item => sDisplayFields.includes(item.sFieldName));
            this.displayFields = sDisplayFields.map(name => filterDisplayFields.find(item => name === item.sFieldName)).filter(item => item != undefined);
            
            // 分组项
            const groupFields = FieldsObject.groupFields;
            const filterGroupFields = groupFields.filter(item => sGroupFields.includes(item.sFieldName));
            this.groupFields = sGroupFields.map(name => filterGroupFields.find(item => name === item.sFieldName)).filter(item => item != undefined);
            
            // 求和项
            const totalFields = FieldsObject.totalFields;
            const filterTotalFields = totalFields.filter(item => sTotalFields.includes(item.sFieldName));
            this.totalFields = sTotalFields.map(name => filterTotalFields.find(item => name === item.sFieldName)).filter(item => item != undefined);
            
            // 处理复选框已选项
            this.checkedDisplayOptions = data.sDisplayFieldSelect ? data.sDisplayFieldSelect.split(',') : this.displayFields.map(item => item.sFieldName);
            this.checkedGroupOptions = data.sGroupFieldSelect ? data.sGroupFieldSelect.split(',') : this.groupFields.map(item => item.sFieldName);
            this.checkedTotalOptions = data.sTotalFieldSelect ? data.sTotalFieldSelect.split(',') : this.totalFields.map(item => item.sFieldName);
            this.eChartsGroup = this.checkedGroupOptions[0];
            this.eChartsTotal = this.checkedTotalOptions[0];
            this.handleCheckedGroupOptionsChange(this.checkedGroupOptions, true);
            this.handleCheckedTotalOptionsChange(this.checkedTotalOptions, true);
            this.handleCheckedDisplayOptionsChange(this.checkedDisplayOptions, true);
        },
        // 获取节点信息
        findStatsItemById (data) {
            Api.findStatsItemById({ sItemId: data.sItemId }).then(res => {
                if (res.success) {
                    if (res.data) {
                        this.handleCheckBoxData(res.data);
                    }
                    return
                }
            }).catch(err => {
                console.log(err);
                this.handleCheckBoxData(data);
            });
        },
        // 分组项
        handleCheckGroupAllChange (val) {
            this.checkedGroupOptions = val ? this.groupFields.map(item => item.sFieldName) : [];
            this.isGroupIndeterminate = false;
            this.checkedGroupOptions.length && this.onSearchClick();
        },
        handleCheckedGroupOptionsChange (value, isNotCheck) {
            let checkedCount = value.length;
            this.isCheckAllGroup = checkedCount === this.groupFields.length;
            this.isGroupIndeterminate = checkedCount > 0 && checkedCount < this.groupFields.length;
            !isNotCheck && this.onSearchClick();
        },
        // 统计项
        handleCheckTotalAllChange (val) {
            this.checkedTotalOptions = val ? this.totalFields.map(item => item.sFieldName) : [];
            this.isTotalIndeterminate = false;
            this.checkedTotalOptions.length && this.onSearchClick();
        },
        handleCheckedTotalOptionsChange (value, isNotCheck) {
            let checkedCount = value.length;
            this.isCheckAllTotal = checkedCount === this.totalFields.length;
            this.isTotalIndeterminate = checkedCount > 0 && checkedCount < this.totalFields.length;
            !isNotCheck && this.onSearchClick();
        },
        // 显示项
        handleCheckDisplayAllChange (val) {
            this.checkedDisplayOptions = val ? this.displayFields.map(item => item.sFieldName) : [];
            this.isDisplayIndeterminate = false;
            this.checkedDisplayOptions.length && this.onSearchClick();
        },
        handleCheckedDisplayOptionsChange (value, isNotCheck) {
            let checkedCount = value.length;
            this.isCheckAllDisplay = checkedCount === this.displayFields.length;
            this.isDisplayIndeterminate = checkedCount > 0 && checkedCount < this.displayFields.length;
            !isNotCheck && this.onSearchClick();
        },
        // 动态设置表头
        setTableHeader () {
            if (this.selectedNode.sItemType === 1) {
                this.tableProps = this.groupFields.filter(item => this.checkedGroupOptions.includes(item.sFieldName));
                this.tableProps = this.tableProps.concat(this.totalFields.filter(item => this.checkedTotalOptions.includes(item.sFieldName)));
            } else {
                this.tableProps = this.displayFields.filter(item => this.checkedDisplayOptions.includes(item.sFieldName));
            }

            this.tableProps.forEach(item => {
                item.sLabel = item.sFieldDesc;
                item.sProp = item.sFieldName;
                item.sMinWidth = '120px';
            });
            let startDate = this.condition.startDate ? transformDate(this.condition.startDate, false, 'yyyy年MM月dd日') : '';
            let endDate = this.condition.endDate ? transformDate(this.condition.endDate, false, 'yyyy年MM月dd日') : ''
            this.pageBaseInfo.searchTimeStr = startDate + '-' + endDate;
            this.pageBaseInfo.printTimeStr = transformDate(new Date(), false, 'yyyy年MM月dd日 HH时mm分');
        },
        // 查询操作事件
        onSearchClick () {
            this.setTableHeader();
            // 规整查询条件
            let jsonData = Object.assign({}, this.condition);
            jsonData.sItemId = this.selectedNode.sItemId;
            Object.keys(jsonData).forEach(item => {
                if (!Array.isArray(jsonData[item])) {
                    return
                }
                if (jsonData[item].length > 0) {
                    jsonData[item] = jsonData[item].join(',');
                } else {
                    delete jsonData[item];
                }
            })
            jsonData.startDate = transformDate(jsonData.startDate);
            jsonData.endDate = transformDate(jsonData.endDate);
            if (this.checkedDisplayOptions.length > 0) {
                jsonData.sDisplayFieldSelect = this.checkedDisplayOptions.join(',')
            }
            if (this.checkedGroupOptions.length > 0) {
                jsonData.sGroupFieldSelect = this.checkedGroupOptions.join(',');
            }
            if (this.checkedTotalOptions.length > 0) {
                jsonData.sTotalFieldSelect = this.checkedTotalOptions.join(',');
            }
            if (this.selectedNode.sItemType === 1) {
                jsonData.sChartSelect = `${this.eChartsGroup},${this.eChartsTotal}`;
            }
            this.loading = true;
            // 请求查询接口
            Api.queryStats(jsonData).then(res => {
                this.loading = false;
                this.tableData = [];
                if (res.success) {
                    this.tableData = res.data.resultSet || [];
                    this.eChartsData = res.data.chartResultSet || [];
                    this.eChartsData.forEach(item => {
                        item.sName = item[this.eChartsGroup];
                        item.iCount = item[this.eChartsTotal];
                    });
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
                this.loading = false;
            })
        },
        onResetClick () {
            for (let item in this.condition) {
                this.condition[item] = null
            }
            this.condition.startDate = moment().startOf('month')
            this.condition.endDate = moment().endOf('month')
        },
        // 切换表格图表组件
        handleChangeComponent (val) {
            this.componentType = val
        },
        // 获取所有字段
        findAllStatsFields () {
            Api.findAllStatsFields().then(res => {
                if (res.success) {
                    this.FieldsObject = res.data || {};
                }
                // this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            })
        },

        // 获取条件值查询
        queryConditionValue () {
            Api.queryConditionValue().then(res => {
                if (res.success) {
                    this.optionsLoc.applyPersons = res.data && res.data.applyPersons ? res.data.applyPersons : [];
                    this.optionsLoc.checkItems = res.data && res.data.checkItems ? res.data.checkItems : [];
                    this.optionsLoc.deviceTypes = res.data && res.data.deviceTypes ? res.data.deviceTypes : [];
                    this.optionsLoc.districts = res.data && res.data.districts ? res.data.districts : [];
                    this.optionsLoc.machineryRooms = res.data && res.data.machineryRooms ? res.data.machineryRooms : [];
                    this.optionsLoc.sources = res.data && res.data.sources ? res.data.sources : [];
                    Object.keys(this.optionsLoc).forEach(key => {
                        this.optionsLoc[key].forEach(item => {
                            if (item.sDistrictId) {
                                item.sValue = item.sDistrictId
                            }
                            if (item.sHospitalDistrictName) {
                                item.sName = item.sHospitalDistrictName
                            }
                            if (item.sRoomId) {
                                item.sValue = item.sRoomId
                            }
                            if (item.sRoomText) {
                                item.sName = item.sRoomText
                            }
                            if (item.sMachineryRoomId) {
                                item.sValue = item.sMachineryRoomId
                            }
                            if (item.sMachineryRoomText) {
                                item.sName = item.sMachineryRoomText
                            }
                            if (item.sProjectId) {
                                item.sValue = item.sProjectId
                            }
                            if (item.sProjectName) {
                                item.sName = item.sProjectName
                            }
                            if (item.sSource) {
                                item.sValue = item.sSource
                            }
                            if (item.sSourceText) {
                                item.sName = item.sSourceText
                            }
                        })
                    })
                }
            }).catch(err => {
                console.log(err);
            })
        },
        // 获取树数据
        async getTreeData () {
            await Api.getTreeData().then(res => {
                if (res.success) {
                    this.treeData = res.data || [];
                    this.setTreeDataNodeKeys(this.treeData);
                    this.$nextTick(() => {
                        if (this.selectedNode.sId) {
                            this.$refs.treeRef.setCurrentKey(this.selectedNode.sId);
                            this.handleNodeClick(this.$refs.treeRef.getCurrentNode());
                            return
                        }
                        if (this.treeData.length && this.treeData[0].items.length) {
                            this.selectedNode = this.treeData[0].items[0];
                            this.$refs.treeRef.setCurrentKey(this.selectedNode.sId);
                        }
                    })
                }
            })
        },
        // 构造树节点
        setTreeDataNodeKeys (data) {
            if (!data.length) {
                return
            }
            // 构建sId, sName属性
            data.forEach(item => {
                if (item.sItemId) {
                    item.sId = item.sItemId;
                    item.sName = item.sItemName;
                }
                if (!item.sId) {
                    item.sId = item.sCategoryId;
                    item.sName = item.sCategoryName;
                }
                if (item.items) {
                    this.setTreeDataNodeKeys(item.items);
                }
            })
        },
        async init () {
            try {
                this.initLoading = true;
                await this.getTreeData()
                let res1 = await Api.findAllStatsFields();
                if (res1.success) {
                    this.FieldsObject = res1.data || {};
                }
                this.handleNodeClick(this.selectedNode);
                this.initLoading = false;
            } catch (error) {
                this.initLoading = false;
                console.log(error);
            }
        },
        
        getApplyDept () {
            if (this.optionsLoc.applyDepartItems.length) {
                return
            }
            getApplyDept().then(res => {
                if (res.success) {
                    let data = res.data || [];
                    let list = []
                    data.map(item => {
                        list.push({ sName: item.sApplyDeptName, sValue: item.sApplyDeptName });
                    })
                    this.optionsLoc.applyDepartItems = list;
                    return
                }
                this.$message.error(res.msg);
            })
        },
        getApplyDr () {
            if (this.optionsLoc.applyDocsItems.length) {
                return
            }
            getApplyDr().then(res => {
                if (res.success) {
                    let data = res.data || [];
                    let list = []
                    data.map(item => {
                        list.push({ sName: item.sApplyDrName, sValue: item.sApplyDrName });
                    })
                    this.optionsLoc.applyDocsItems = list;
                    return
                }
                this.$message.error(res.msg);
            })
        },
    },
    async created () {
        this.queryConditionValue();
        this.init();

        const Doctors = await getReportAboveDrData()
        this.optionsLoc['approveDocsItems'] = Doctors.auditDoctors //获取审核医生
        this.optionsLoc['reportDocsItems'] = Doctors.reportDoctors // 获取报告医生
        this.optionsLoc['finalDocsItems'] = Doctors.recheckDoctors // 复审医生
        this.optionsLoc['practiceDocsItems'] = Doctors.practiceDoctors // 实习医生
        this.getApplyDept();
        this.getApplyDr();
    },


}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.stat-container {
    position: relative;
    width: 100%;
    height: 100%;
}
.m-bg{
    background: var(--theme-bg);
}


:deep(.el-tree .el-tree-node__content) {
    height: 35px;
}

.i-action {
    border-bottom: 1px solid #eee;
}

.i-btn1 {
    position: relative;
    display: block;
    width: 100%;
    height: 40px;
    padding-left: 10px;
    text-align: left;
    border-radius: 0;
    border: none;

    .fa-cog {
        margin-right: 5px;
        position: relative;
        top: 1px;
    }

    .fa-angle-right {
        position: absolute;
        right: 10px;
    }
}

.center-group-col {
    width: 200px;
    padding-right: 15px;
    border-right: 1px solid #eee;
    overflow: auto;
    background: var(--el-color-primary-light-9);

    .i-item {
        margin-bottom: 30px;
        min-height: 200px;
    }

    .el-checkbox,
    .el-radio {
        display: flex;
        padding-left: 15px;
        margin-top: 10px;

        &.t-1 {
            width: 100%;
            height: 30px;
            line-height: 1.5;
            padding-top: 5px;
            padding-bottom: 5px;
            margin-top: 0;
            margin-bottom: 15px;
            background-color: #eef4fa;
        }
    }
}


.c-btnArea {
    margin: 0 15px;
    padding: 10px 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
}

.my-checkbox {
    padding: 0 13px;
    width: 100%;
    box-sizing: border-box;
    justify-content: space-between;
    flex-direction: row-reverse;

    &:hover {
        background: var(--el-color-primary-light-9);
    }
}
:deep(.my-multipSelect .el-input__prefix) {
    position: absolute;
    right: 2px;
    z-index: 7;
    background: #fff;
    height: calc(100% - 2px);
}
</style>
