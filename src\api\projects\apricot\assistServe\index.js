import request, {
    baseURL, stringify
} from '$supersetUtils/request'

export default {

    // 图像查看客户端 : Rebuild Controller
    rebuildOpenView(params) {
        return request({
            url: baseURL.apricotAssist + '/rebuild/open/view',
            method: 'GET',
            params,
        })
    },
    openHpcamera,
    openFilmscanner,
    uploadFilmscanner,
}

// 高拍仪调用控制类: HP Camera Controller
export function openHpcamera(params) {
    return request({
        url: baseURL.apricotAssist + '/hpcamera/open/hpcamera',
        method: 'GET',
        params,
    })
}

// 扫描仪调用控制类 : Film Scanner Controller
export function openFilmscanner(params) {
    return request({
        url: baseURL.apricotAssist + '/filmscanner/open/filmscanner',
        method: 'GET',
        params,
    })
}

// 获取高拍仪文件列表
export function getHpcameraImages() {
    return request({
        url: baseURL.apricotAssist + '/hpcamera/get/img',
        method: 'POST',
    })
}

// 删除高拍仪图片
export function delHpcameraImg(params) {
    return request({
        url: baseURL.apricotAssist + '/hpcamera/del/img',
        method: 'POST',
        data: stringify(params),
    })
}

// 获取扫描仪文件列表
export function getFilmscannerImages() {
    return request({
        url: baseURL.apricotAssist + '/filmscanner/get/img',
        method: 'POST',
    })
}

// 删除扫描仪图片
export function delFilmscannerImg(params) {
    return request({
        url: baseURL.apricotAssist + '/filmscanner/del/img',
        method: 'POST',
        data: stringify(params),
    })
}

// 上传图片
export function uploadFilmscanner(params) {
    return request({
        url: baseURL.apricotAssist + '/filmscanner/upload',
        method: 'POST',
        data: stringify(params),
    })
}

// 获取本地参数配置接口
export function getLocalConfig(params) {
    return request({
        url: baseURL.apricotAssist + '/localConfig/getLocalConfig',
        method: 'GET',
        data: stringify(params),
    })
}

// 保存本地参数配置接口
export function saveLocalConfig(data) {
    return request({
        url: baseURL.apricotAssist + '/localConfig/saveLocalConfig',
        method: 'POST',
        data,
    })
}