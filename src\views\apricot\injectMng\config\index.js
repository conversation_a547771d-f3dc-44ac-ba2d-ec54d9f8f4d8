export default {
    /**
     * 注射管理
     */
    InjectMngPenalConfig: {
        leftLayoutWidth: '50%',
        tabInjectForm: true,
        tabActivityMeter: true,
        tabBloodAndDose: true,
        tabInjectRecord: true,
        // rightTabPanels: ['InjectForm', 'ActivityMeter', 'BloodAndDose', 'InjectRecord'],
        autoPrintType: 0,
        autoPrintClassifys: [],
        isShowConfigBtn: true,
        iFull: 3,
        iNull: 1,
        iActual: 2,
        readInterval: 3,
        medicineChooseType: 1,
        patientInfoBgColor: 'rgba(243, 250, 232, 1)',
        tabBgColor: '#fff',
        injectTimeSetType: 1,
        iTimeInterval: 1
    },
    DA0: {
        type: 't-x',
        localStorageKey: '202305290900',
        panelConfig: [{
            size: 0,
            minSize: 50,
            name: 'c-left',
            isFlexible: true
        },
        {
            size: '50%',
            minSize: 575,
            // maxSize: window.innerWidth / 4 * 3,
            name: 'c-right',
            isFlexible: false

        }]
    },
    // 搜索条件
    searchStyle: [{
            sProp: 'dAppointmentTimeSt',
            sLabel: '预约开始',
            width: '33.3%',
            sInputType: 'date-picker',
            iCustom: 1,
            dateType: 'date', //  element-plus datePicker组件中的Type属性 
            dateValueFormat: 'YYYY-MM-DD HH:mm:ss', //  element-plus datePicker组件中ValueFormat属性 
        },
        {
            sProp: 'dAppointmentTimeEd',
            sLabel: '预约结束',
            width: '33.3%',
            sInputType: 'date-picker',
            iCustom: 1,
            dateType: 'date', //  element-plus datePicker组件中的Type属性 
            dateValueFormat: 'YYYY-MM-DD HH:mm:ss', //  element-plus datePicker组件中ValueFormat属性 
        },
        {
            sProp: 'sDistrictId',
            sLabel: '院区',
            sInputType: 'option',
            sOptionProp:'districtArrOption',
            width: '33.3%',
            iCustom: 1,
        },
        // {
        //     sProp: 'sRecentDays',
        //     sLabel: '最近天数',
        //     width: '33.3%',
        //     sInputType: 'date-picker',
        //     iCustom: 1
        // },
        {
            sProp: 'sName',
            sLabel: '姓名',
            sInputType: 'text',
            width: '33.3%',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sInputType: 'text',
            width: '33.3%',
        },
        {
            sProp: 'sMachineryRoomId',
            sLabel: '机房',
            sInputType: 'option',
            sOptionProp:"machineRoomArrOption",
            width: '33.3%',
            iCustom: 1,
        },
        {
            sProp: 'sProjectId',
            sLabel: '项目',
            sInputType: 'option',
            sOptionProp: "itemsArrOption",
            width: '33.3%',
            iCustom: 1,
        },
        { 
            prop: 'sVisitCard', 
            label: '就诊卡号',
            sInputType: 'text',
            iLayourValue: 4,
            isShow: false
        },
        { 
            prop: 'sMedicalCaseNO', 
            label: '病案号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sImageNo', 
            label: '影像号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sOutpatientNO', 
            label: '门诊号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sPhysicalExamNo', 
            label: '体检号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sVitisNo', 
            label: '就诊号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sCardNum', 
            label: '社保卡号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sHealthCardNO', 
            label: '健康卡号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        // {
        //     sProp: 'sMedicalRecordNO',
        //     sLabel: '病历号',
        //     width: '33.3%',
        //     sInputType: 'text',
        // },
        // {
        //     sProp: 'sOrderNO',
        //     sLabel: '医嘱号',
        //     width: '33.3%',
        //     sInputType: 'text',
        // },
        // {
        //     sProp: 'sInHospitalNO',
        //     sLabel: '住院号',
        //     width: '33.3%',
        //     sInputType: 'text',
        // },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sOptionProp: 'iIsRegisterOptions',
            sInputType: 'option',
            width: '33.3%',
            iCustom: 1,
        },
        {
            sProp: 'iIsInject',
            sLabel: '注射',
            sOptionProp: 'iIsInjectOptions',
            sInputType: 'option',
            width: '33.3%', 
            iCustom: 1,
        },
    ],
    // 文本组件配置
    textListConfig: [
        {
            prop: 'sName',
            label: '姓名',
        },
        {
            prop: 'sSexText',
            label: '性别',
        },
        {
            prop: 'sAge',
            label: '年龄',
        },
        {
            prop: 'sSourceText',
            label: '来源',
        },
        {
            prop: 'sNuclearNum',
            label: '核医学号',
        },
        {
            prop: 'sMedicalRecordNO',
            label: '病历号',
        },
        {
            prop: 'fHeight',
            label: '身高(cm)',
        },
        {
            prop: 'fWeight',
            label: '体重(kg)',
        },
        {
            prop: 'sInjection',
            label: '注射要求',
            width: '50%'
        },
        {
            prop: 'sProjectName',
            label: '检查项目',
            width: '50%'
        },
        {
            prop: 'sNameSpell',
            label: '姓名拼音',
            isShow: false
        },
        {
            prop: 'sIdNum',
            label: '身份证号',
            isShow: false
        },
        {
            prop: 'dBirthday',
            label: '出生日期',
            isShow: false
        },
        {
            prop: 'sDistrictName',
            label: '院区',
            isShow: false
        },
        {
            prop: 'sRoomText',
            label: '设备类型',
            isShow: false
        },
        {
            prop: 'sMachineryRoomText',
            label: '机房',
            isShow: false
        },
        {
            prop: 'sMachineStationName',
            label: '工作站',
            isShow: false
        },
        {
            prop: 'dAppointmentTime',
            label: '预约日期',
            isShow: false
        },
        {
            prop: 'sOutpatientNO',
            label: '门诊号',
            isShow: false
        },
        {
            prop: 'sInHospitalNO',
            label: '住院号',
            isShow: false
        },
        {
            prop: 'sApplyNO',
            label: '申请单号',
            isShow: false
        },
        {
            prop: 'sOrderNO',
            label: '医嘱号',
            isShow: false
        },
        {
            prop: 'sVitisNo',
            label: '就诊号',
            isShow: false
        },
        {
            prop: 'sClinicalDiagnosis',
            label: '临床诊断',
            isShow: false
        },
        {
            prop: 'sMedicalHistory',
            label: '简要病史',
            isShow: false
        },
        {
            prop: 'sEncounter',
            label: '就诊次数',
            isShow: false
        },
        {
            prop: 'sPositionText',
            label: '检查部位',
            isShow: false
        },
        {
            prop: 'sTestModeText',
            label: '检查方式',
            isShow: false
        },
        {
            prop: 'sNuclideText',
            label: '核素',
            isShow: false
        },
        {
            prop: 'sNuclideSupName',
            label: '核素全称',
            isShow: false
        },
        {
            prop: 'sTracerText',
            label: '示踪剂',
            isShow: false
        },
        {
            prop: 'sTracerSupName',
            label: '示踪剂全称',
            isShow: false
        },
        {
            prop: 'fRecipeDose',
            label: '处方剂量',
            isShow: false
        },
        {
            prop: 'iIsPregnant',
            label: '怀孕',
            isShow: false
        },
        {
            prop: 'sPhone',
            label: '联系电话',
            isShow: false
        },
        {
            prop: 'sAddress',
            label: '住址',
            isShow: false
        },
        {
            prop: 'sMaritalStatusName',
            label: '婚姻状况',
            isShow: false
        },
        {
            prop: 'sMedicalCaseNO',
            label: '病案号',
            isShow: false
        },
        {
            prop: 'sImageNo',
            label: '影像号',
            isShow: false
        },
        {
            prop: 'sInpatientAreaText',
            label: '病区名称',
            isShow: false
        },
        {
            prop: 'sInpatientWardText',
            label: '病房名称',
            isShow: false
        },
        {
            prop: 'sBedNum',
            label: '床号',
            isShow: false
        },
        {
            prop: 'sVisitCard',
            label: '就诊卡号',
            isShow: false
        },
        {
            prop: 'sCardNum',
            label: '社保卡号',
            isShow: false
        },
        {
            prop: 'sHealthCardNO',
            label: '健康卡号',
            isShow: false
        },
        {
            prop: 'fBloodSugar',
            label: '检查空腹血糖',
            isShow: false
        },
        {
            prop: 'sPresentHistory',
            label: '现病史',
            isShow: false
        },
        {
            prop: 'sPastHistory',
            label: '既往史疾病',
            isShow: false
        },
        {
            prop: 'sCheckIntent',
            label: '检查目的',
            isShow: false
        },
        {
            prop: 'sClinicalSymptoms',
            label: '临床症状',
            isShow: false
        },
        {
            prop: 'sChiefComplaint',
            label: '主诉',
            isShow: false
        },
        {
            prop: 'sInvoiceNum',
            label: '发票号',
            isShow: false
        },
        {
            prop: 'sFeeTypeText',
            label: '费用类型',
            isShow: false
        },
        {
            prop: 'fFees',
            label: '费用',
            isShow: false
        },
        {
            prop: 'sChargeStateText',
            label: '收费状态',
            isShow: false
        },
        {
            prop: 'dApplyDate',
            label: '申请时间',
            isShow: false
        },
        {
            prop: 'sApplyPersonName',
            label: '申请医生',
            isShow: false
        },
        {
            prop: 'sApplyDepartText',
            label: '申请科室',
            isShow: false
        },
        {
            prop: 'sChiefPhysicianName',
            label: '主治医生',
            isShow: false
        },
        {
            prop: 'sChiefPhysicianPhone',
            label: '医生电话',
            isShow: false
        },
        {
            prop: 'sPhysicalExamNo',
            label: '体检号',
            isShow: false
        }
    ],

    // 患者 table 表
    patientTable: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sAlign: 'left',
            sMinWidth: '100px'
        },
        {
            sProp: 'sNameSpell',
            sLabel: '姓名拼音',
            sAlign: 'left',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsConsult',
            sLabel: '问诊',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsInject',
            sLabel: '注射',
            sAlign: 'center',
            sWidth: '80px'
        },
        {
            sProp: 'iIsMachine',
            sLabel: '上机',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsImaging',
            sLabel: '收图',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'iIsReport',
            sLabel: '报告',
            sAlign: 'center',
            sWidth: '60px'
        },
        {
            sProp: 'fHeight',
            sLabel: '身高（cm）',
            sAlign: 'center',
            sMinWidth: '100px'
        },
        {
            sProp: 'fWeight',
            sLabel: '体重（kg）',
            sAlign: 'center',
            sMinWidth: '100px'
        },
        {
            sProp: 'sInjectDrugDeliveryText',
            sLabel: '给药方式',
            sMinWidth: '110px'
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sMinWidth: '110px'
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约日期',
            sMinWidth: '130px'
        },
        {
            sProp: 'dInjectTime',
            sLabel: '注射时间',
            sMinWidth: '130px'
        },
        {
            sProp: 'fInjectFactDose',
            sLabel: '注射剂量',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInjectName',
            sLabel: '注射医生',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInjectionPositionText',
            sLabel: '注射部位',
            sMinWidth: '110px'
        },
        {
            sProp: 'sRoomText',
            sLabel: '设备类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sIdNum',
            sLabel: '身份证号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sVitisNo',
            sLabel: '就诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'dBirthday',
            sLabel: '出生日期',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sDistrictName',
            sLabel: '院区',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineryRoomText',
            sLabel: '机房',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sDiagnosticOpinionText',
            sLabel: '诊断意见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sInspectSeeText',
            sLabel: '检查所见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sEncounter',
            sLabel: '就诊次数',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPositionText',
            sLabel: '检查部位',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTestModeText',
            sLabel: '检查方式',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideSupName',
            sLabel: '核素全称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTracerText',
            sLabel: '示踪剂',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'sTracerSupName',
            sLabel: '示踪剂全称',
            sMinWidth: '120px',
            iIsHide: true
        },
        {
            sProp: 'fRecipeDose',
            sLabel: '处方剂量',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'iIsPregnant',
            sLabel: '怀孕',
            sAlign: 'center',
            sMinWidth: '75px',
            iIsHide: true
        },
        {
            sProp: 'sPhone',
            sLabel: '联系电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sAddress',
            sLabel: '住址',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMaritalStatusName',
            sLabel: '婚姻状况',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sImageNo',
            sLabel: '影像号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientAreaText',
            sLabel: '病区名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientWardText',
            sLabel: '病房名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sBedNum',
            sLabel: '床号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sCardNum',
            sLabel: '社保卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fBloodSugar',
            sLabel: '检查空腹血糖',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineStationName',
            sLabel: '工作站',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPresentHistory',
            sLabel: '现病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sPastHistory',
            sLabel: '既往史疾病',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalSymptoms',
            sLabel: '临床症状',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefComplaint',
            sLabel: '主诉',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInvoiceNum',
            sLabel: '发票号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sFeeTypeText',
            sLabel: '费用类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fFees',
            sLabel: '费用',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChargeStateText',
            sLabel: '收费状态',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dApplyDate',
            sLabel: '申请时间',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianName',
            sLabel: '主治医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPhysicalExamNo',
            sLabel: '体检号',
            sMinWidth: '110px',
            iIsHide: true
        }
    ],

    DA1: {
        type: 't-y',
        localStorageKey: '201911072238',
        panelConfig: [{
                size: 250,
                minSize: 180,
                maxSize: 500,
                name: "c1",
                isFlexible: false
            },
            {
                size: 0,
                minSize: 45,
                name: "c2",
                isFlexible: true
            }
        ]
    }
}

export const ReadonlyForm = [{
        sProp: 'sMedicalRecordNO',
        sLabel: '病历号',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iReadonly: 1,
        sInputType: 'text',
    },
    {
        sProp: 'sProjectName',
        sLabel: '检查项目',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 16,
        iReadonly: 1,
        sInputType: 'text',
    },
    {
        sProp: 'fHeight',
        sLabel: '身高（cm）',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iReadonly: 1,
        sInputType: 'text',
    },
    {
        sProp: 'fWeight',
        sLabel: '体重（kg）',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iReadonly: 1,
        sInputType: 'text',
    },
    {
        sProp: 'fRecipeDose',
        sLabel: '处方剂量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iReadonly: 1,
        sInputType: 'text',
        iCustom: 1,
    },
    {
        sProp: 'sNuclideText',
        sLabel: '核素',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iReadonly: 1,
        sInputType: 'text',
    },
    {
        sProp: 'sTracerText',
        sLabel: '药物',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iReadonly: 1,
        sInputType: 'text',
    },
    {
        sProp: 'fBloodSugar',
        sLabel: '空腹血糖（mmol/L）',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 8,
        iCustom: 1,
        sInputType: 'number',
    },
    {
        sProp: 'sInjection',
        sLabel: '特殊注射要求',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iLayourValue: 24,
        iReadonly: 1,
        sInputType: 'text',
    },
]

export const InjectList = [
    {
        sProp: 'sNuclideText',
        sLabel: '核素',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        iCustom: 1,
        disabledSetDefault: 1
    },
    {
        sProp: 'sTracerText',
        sLabel: '示踪剂',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        iCustom: 1,
        disabledSetDefault: 1
    },
    {
        sProp: 'fRecipeDose',
        sLabel: '处方剂量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        iCustom: 1,
        disabledSetDefault: 1 // 不允许填入默认值
    },
    {
        sProp: 'sStainPosition',
        sLabel: '渗漏',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'option',
        sOptionProp: 'ApricotReportStain',
        width: '33.3%',
        iCustom: 1,
    },
    {
        sProp: 'sDrugDeliveryCode',
        sLabel: '给药方式',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'option',
        sOptionProp: 'ApricotReportDrugDelivery',
        width: '33.3%',
    },
    {
        sProp: 'sInjectionPosition',
        sLabel: '注射部位',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'option',
        sOptionProp: 'ApricotReportInjectSite',
        iCustom: 1,
        width: '33.3%',
    },
    {
        sProp: 'dInjectionSta',
        sLabel: '满针时间',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'time-picker',
        iCustom: 1,
        width: '33.3%',
        defaultValue: '1'
    },
    {
        sProp: 'dInjectionTime',
        sLabel: '注射时间',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'time-picker',
        iCustom: 1,
        width: '33.3%',
        defaultValue: '1',
    },
    {
        sProp: 'dInjectionEnd',
        sLabel: '空针时间',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'time-picker',
        iCustom: 1,
        width: '33.3%',
        defaultValue: '1'
    },
    {
        sProp: 'fFullNeedle',
        sLabel: '满针剂量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'number',
        iCustom: 1,
        width: '33.3%',
    },
    {
        sProp: 'fFactDose',
        sLabel: '注射剂量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'number',
        iCustom: 1,
        width: '33.3%',
    },
    {
        sProp: 'fEmptyNeedle',
        sLabel: '空针剂量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'number',
        iCustom: 1,
        width: '33.3%',
    },
    
    {
        sProp: 'iIntake',
        sLabel: '注射前饮水量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'waterOptions',
        width: '33.3%',
        sUnit: 'ml'
    },
    {
        sProp: 'iAft3Intake',
        sLabel: '30min饮水量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'waterOptions',
        width: '33.3%',
        sUnit: 'ml'
    },
    {
        sProp: 'iAft6Intake',
        sLabel: '60min饮水量',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'waterOptions',
        width: '33.3%',
        sUnit: 'ml'
    },
    {
        sProp: 'sBefDrinkTypeCode',
        sLabel: '饮水类型',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'ApricotReportDrinkType',
        width: '33.3%',
    },
    {
        sProp: 'sAft3DrinkTypeCode',
        sLabel: '30min饮水类型',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'ApricotReportDrinkType',
        width: '33.3%',
    },
    {
        sProp: 'sAft6DrinkTypeCode',
        sLabel: '60min饮水类型',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'ApricotReportDrinkType',
        width: '33.3%',
    },

    {
        sProp: 'sMedicineSource',
        sLabel: '药物来源',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        sInputType: 'option',
        sOptionProp: 'ApricotReportMedicineSource',
        width: '33.3%',
    },
    {
        sProp: 'sNurseId',
        sLabel: '注射医生',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'option',
        sOptionProp: 'DoctorOptions',
        width: '33.3%',
        disabledSetDefault: 1
    },
    {
        sProp: 'dInjectDate',
        sLabel: '注射日期',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        iRequired: 1,
        sInputType: 'date-picker',
        width: '33.3%',
        defaultValue: '1'
    },
    {
        sProp: 'fBloodSugar',
        sLabel: '空腹血糖',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        iCustom: 1,
    },
    {
        sProp: 'fHeight',
        sLabel: '身高',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        sInputType: 'text',
        iCustom: 1,
        sUnit: 'cm'
    },
    {
        sProp: 'fWeight',
        sLabel: '体重',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        sInputType: 'text',
        iCustom: 1,
        sUnit: 'kg'
    },
    {
        sProp: 'fError',
        sLabel: '剂量误差率',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        sInputType: 'text',
        iCustom: 1,
    },
    {
        sProp: 'dTableDetectTime',
        sLabel: '注射台监测时间',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        sInputType: 'text',
        iCustom: 1,
    },
    {
        sProp: 'fTableDetectDose',
        sLabel: '注射台监测数值',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '33.3%',
        sInputType: 'text',
        iCustom: 1,
        sUnit: 'uSV/h'
    },
    {
        sProp: 'sMemo',
        sLabel: '备注',
        sHeight: '40px',
        sFontSize: '15px',
        sLabelFontSize: '15px',
        width: '100%',
        sInputType: 'textarea',
    },
]
