<template>
    <span @click="showDialog"
        class="i-item">
        <i class="fa fa-page-setting"></i>
        <span>页面设置</span>

        <el-dialog v-model="dialogFormVisible"
            title="注射管理设置"
            class="t-default"
            :close-on-click-modal="false"
            append-to-body
            destroy-on-close
            width="700px">
            <div style="max-height: 60vh; overflow: auto;">
                <el-form :model="form"
                    label-width="140px">
                    <el-form-item label="显示面板：">
                        <div class="flex flex-col">
                            <el-checkbox v-model="form.tabInjectForm"
                                label="InjectForm">注射操作</el-checkbox>
                            <el-checkbox v-model="form.tabInjectRecord"
                                label="InjectRecord">注射记录</el-checkbox>
                            <el-checkbox v-model="form.tabActivityMeter"
                                label="ActivityMeter">活度仪采集</el-checkbox>
                            <el-checkbox v-model="form.tabBloodAndDose"
                                label="BloodAndDose">血糖及给药</el-checkbox>
                        </div>
                    </el-form-item>
                    <el-form-item label="配置按钮：">
                        <el-radio-group v-model="form.isShowConfigBtn">
                            <el-radio :label="true">显示</el-radio>
                            <el-radio :label="false">隐藏</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="数据查询：">
                        <el-checkbox v-model="form.OnlySearchCurrentWStation">只查询当前工作站数据</el-checkbox>
                    </el-form-item>
                    <el-form-item label="自动打印：">
                        <el-radio-group v-model="form.autoPrintType">
                            <el-radio :label="2">每次保存打印</el-radio>
                            <el-radio :label="1">首次保存打印</el-radio>
                            <el-radio :label="0">保存不打印</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="打印文件：">
                        <el-checkbox-group v-model="form.autoPrintClassifys"
                            :disabled="!form.autoPrintType">
                            <template v-for="(item, index) in btnsOption"
                                :key="index">
                                <el-checkbox :label="item.iClassify">{{ item.sClassify }}</el-checkbox>
                            </template>
                        </el-checkbox-group>
                    </el-form-item>
                    <el-form-item label="药物选择方式：">
                        <el-radio-group v-model="form.medicineChooseType">
                            <el-radio :label="1">关联选择</el-radio>
                            <el-radio :label="2">随机选择</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="活度仪："
                        class="my-form-item">
                        <div class="c-setting padding-lr">
                            <span>读取：</span>
                            <el-input-number v-model="form.readInterval"
                                :precision="0"
                                :min="1"
                                controls-position="right"
                                style="width:200px;margin: 0 5px;"></el-input-number>
                            <span>秒</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="注射时间设置:">
                        <div class="w-full">
                            <el-radio-group v-model="form.injectTimeSetType">
                                <el-radio :label="1">方式一</el-radio>
                                <el-radio :label="2">方式二</el-radio>
                            </el-radio-group>
                        </div>
                        <template v-if="form.injectTimeSetType == 1">
                            <div class="c-setting m-1">
                                <span>满针前置：</span>
                                <el-input-number v-model="form.iFull"
                                    :precision="0"
                                    controls-position="right"
                                    style="width:200px;margin: 0 5px;"></el-input-number>
                                <span>分钟</span>
                            </div>
                            <div class="c-setting m-1">
                                <span>实际前置：</span>
                                <el-input-number v-model="form.iActual"
                                    :precision="0"
                                    controls-position="right"
                                    style="width:200px;margin: 0 5px;"></el-input-number>
                                <span>分钟</span>
                            </div>
                            <div class="c-setting m-1">
                                <span>空针前置：</span>
                                <el-input-number v-model="form.iNull"
                                    :precision="0"
                                    controls-position="right"
                                    style="width:200px;margin: 0 5px;"></el-input-number>
                                <span>分钟</span>
                            </div>
                            <div class="w-full mt-2 i-color">{{`示例：当前时间（${setComputedTime(0)}）`}}</div>
                            <div class="w-full i-color">{{`满针：${setComputedTime(form.iFull)} ，实际：${setComputedTime(form.iActual)} ，空针：${setComputedTime(form.iNull)}`}}</div>
                        </template>
                        <template v-else>
                            <div class="w-full mt-2">
                                <span>时间间隔：</span>
                                <el-input-number v-model="form.iTimeInterval"
                                    :precision="0"
                                    :min="0"
                                    controls-position="right"
                                    style="width:200px;margin: 0 5px;"></el-input-number>
                                <span>分钟</span>
                            </div>
                            <div class="mt-2 i-color">示例：{{`满针：${setComputedTime(form.iTimeInterval)} ，实际：${setComputedTime(0)} ，空针：${setComputedTime(-form.iTimeInterval)}`}}</div>
                        </template>
                    </el-form-item>
                    <!-- patientInfoBgColor -->
                    <el-form-item label="注射单背景色：">
                        <el-color-picker small="small"
                            v-model="form.patientInfoBgColor"
                            show-alpha
                            :predefine="predefineColors"
                            @active-change="changesTheme">
                        </el-color-picker>
                    </el-form-item>
                    <el-form-item label="tab页背景色：">
                        <el-color-picker small="small"
                            v-model="form.tabBgColor"
                            show-alpha
                            :predefine="predefineColors"
                            @active-change="changestabBgColor">
                        </el-color-picker>
                    </el-form-item>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary"
                        plain
                        @click="onSaveClick(1)">保存至全局</el-button>
                    <el-button-icon-fa icon="fa fa-save"
                        type="primary"
                        :disabled="loading"
                        @click="onSaveClick(0)">保存</el-button-icon-fa>
                    <el-button-icon-fa icon="el-icon-close"
                        @click="dialogFormVisible = false">关闭</el-button-icon-fa>
                </span>
            </template>
        </el-dialog>
    </span>
</template>


<script>
import { deepClone } from "$supersetUtils/function"

import { exGetPrintClassifyOfModule } from '$supersetResource/js/projects/apricot/mixinPrintPreview.js'
import { useUserConfigSaveConfig } from '$supersetResource/js/projects/apricot/useUserConfig.js'
import { saveConfig } from '$supersetApi/userConfig.js'

export default {
    emits: ['updateData', 'update:modelValue'],
    props: ['iModuleId', 'formData', 'configKey', 'modelValue'],
    data () {
        return {
            form: {
            },
            btnsOption: [],
            userInfo: {},
            loading: false,
            dialogFormVisible: false,
            predefineColors: [
                '#f3fae8',
                'rgba(229, 247, 231, 1)',
                'rgba(228, 237, 230, 1)',
                'rgba(238, 242, 252, 1)',
                'rgba(238, 239, 251, 1)',
                '#EAEAEF',
                'rgba(242, 244, 238, 1)',
                'rgba(227, 233, 228, 1)',
                'rgba(228, 235, 227, 1)',
                'rgba(252, 244, 234, 1)',
                'rgba(252, 240, 236, 1)',
                '#FFFFFF',
            ],
            currentTime: new Date(),
        }
    },

    watch: {
        dialogFormVisible (val) {
            if (val) {
                this.form = deepClone(this.formData);
                this.userInfo = this.$store.getters['user/userSystemInfo'];
                this.getPrintClassifyOfModule(this.iModuleId);
            }
        },
    },
    methods: {
        showDialog () {
            this.dialogFormVisible = true;
            this.currentTime = new Date();
        },
        setComputedTime (num) {
            num = isNaN(+num) ? 0 : num
            return moment(this.currentTime.getTime() - num * 60 * 1000).format('HH:mm')
        },
        // 获取可打印模板类型
        getPrintClassifyOfModule: exGetPrintClassifyOfModule(),
        // 保存配置数
        useUserConfigSaveConfig: useUserConfigSaveConfig(),
        // 更新父组件数据
        upDateParentData () {
            this.$emit('updateData', this.form);
            // setTimeout(() => {
            //     console.log(11111666, this.formData);
            // }, 3000);
        },
        async onSaveClick (configType) {
            let params = {
                configKey: this.configKey,
                configValue: JSON.stringify(this.form),
                configType: configType,
                moduleId: this.iModuleId,
                userNo: this.userInfo.sNo
            }
            this.loading = true;
            await this.useUserConfigSaveConfig(params, this, this.upDateParentData);
            this.loading = false;
            this.dialogFormVisible = false;
        },
        changesTheme (val) {
            this.form.patientInfoBgColor = val
        },
        changestabBgColor (val) {
            this.form.tabBgColor = val
        }
    }
}
</script>

<style lang="scss" scoped>
:deep(.el-input-number) {
    .el-input__inner {
        text-align: center;
    }
}
.i-item {
    margin: 0 -10px;
    padding: 0 10px;
    display: flex;
    > i {
        height: 20px;
    }
}
.my-form-item {
    :deep(.el-form-item__content) {
        display: block;
    }
}
.i-color {
    color: #999;
}
// .el-checkbox-group {
//     .el-checkbox {
//         display: block;
//     }
// }
</style>