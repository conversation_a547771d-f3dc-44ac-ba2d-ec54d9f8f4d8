<template>
    <!-- 工作站维护 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa> 
            </div>
            
            <div class="c-form-search">
                <div>
                    <el-select style="width: 100%"             
                        placeholder="院区"
                        clearable
                        v-model="condition.districtId"
                        @change="mxDoSearch()">
                        <el-option v-for="item in treeData"
                            :key="item.sValue"
                            :value="item.sValue"
                            :label="item.sName">
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <el-select v-model="condition.stationTypeCode"
                        placeholder="工作站类型"
                        clearable
                        style="width: 100%"
                        @change="mxDoSearch()">
                        <el-option v-for="(item,index) in stationTypeOption"
                            :key="index"
                            :label="item.sName"
                            :value="item.sValue">
                        </el-option>
                    </el-select>
                </div>
                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch"> </el-button-icon-fa>
                </div>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="140px"
                    >
                    <el-col :span="24">
                        <el-form-item prop="districtId" label="院  区：">
                            <el-select style="width: 100%"
                                v-model="editLayer.form.districtId"
                                @change="changeHospital">
                                <el-option v-for="item in treeData"
                                    :key="item.sValue"
                                    :value="item.sValue"
                                    :label="item.sName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="stationTypeCode" label="工作站类型：">
                            <el-radio-group v-model="editLayer.form.stationTypeCode">
                                <el-radio v-for="(item,index) in stationTypeOption"  :key="index" :label="item.sValue">{{item.sName}}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="stationName" label="工作站名称：">
                            <el-input v-model="editLayer.form.stationName" 
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="工作站简称：">
                            <el-input v-model="editLayer.form.stationSimpleName" 
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                   
                    <el-col :span="24" v-if="editLayer.form.stationTypeCode==='5' || editLayer.form.stationTypeCode==='2'">
                        <el-form-item label="工作站绑定机房：" :prop="editLayer.form.stationTypeCode==='5'?'roomId':''">
                            <el-select v-model="editLayer.form.roomId"
                                placeholder=""
                                clearable
                                style="width: 100%">
                                <el-option v-for="(item,index) in optionsLoc.machineRoomOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.sId">
                                        <span style="float: left">{{ item.sName }}</span>
                                        <span style="float: right; color: #8492a6; font-size: 13px; padding-left:10px">{{ item.workRoom }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                 
                    <el-col :span="24">
                        <el-form-item label="启  用：">
                            <el-radio-group v-model="editLayer.form.isEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer><div  class="dialog-footer">
                    <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保存</el-button-icon-fa>
                    <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                </div>
            </template>
        </el-dialog>

        
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <!-- @row-click="handleRowClick" -->
                <el-table :data="tableData"
                    id="itemTable"
                    ref="mainTable"
                    size="small"
                    @row-click="onClickRow"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <template v-for="item in configTable.filter(_i=> !_i.iIsHide)" :key="item.index">
                        <el-table-column show-overflow-tooltip
                            
                            :prop="item.sProp"
                            :label="item.sLabel"
                            :fixed="item.sFixed"
                            :align="item.sAlign"
                            :width="item.sWidth"
                            :min-width="item.sMinWidth"
                            :sortable="!!item.iSort"
                            >
                            <template v-slot="{row, $index}">
                                <template v-if="item.sProp === 'action'">
                                    <el-button size="small"
                                        link
                                        type="primary"
                                        @click="handleEdit(row)"
                                        >编辑
                                        <template #icon>
                                            <Icon name="el-icon-edit" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                    <el-divider direction="vertical"></el-divider>
                                    <el-button size="small" link class  @click="onClickDel(row)">
                                        删除
                                        <template #icon>
                                            <Icon name="el-icon-delete" color="">
                                            </Icon>
                                        </template>
                                    </el-button>
                                </template>
                                <template v-else-if="item.sProp === 'isEnable'">
                                    <!-- <el-switch @click.stop.native="onChangeEnable($event, row, $index)"
                                        v-model="row.isEnable"
                                        :active-value="1"
                                        :inactive-value="0"></el-switch> -->
                                    <span v-if="row.isEnable" class="icon-green"> 是 </span>
                                    <span v-else> 否 </span>
                                </template>
                                <template v-else>
                                    {{row[`${item.sProp}`]}}
                                </template>
                            </template>
                            <!-- <template v-slot:header>
                                <span>{{item.sLabel}}</span>
                                <i v-if="item.sProp === 'action'"
                                    class="el-icon-rank i-sort"
                                    style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                    title="首次或无法排序时，点击初始化排序"
                                    @click="autoSort"></i>
                            </template> -->
                        </el-table-column>
                    </template>
                </el-table>
            </div>
        </div>
    </div>
</template>
<script>
import Sortable from 'sortablejs'
import { getOptionName } from '$supersetResource/js/tools'
import { deepClone } from '$supersetUtils/function'
import { callTypeOptions } from '$supersetResource/js/projects/apricot/enum.js'
import { getHospitalData, getMachineRoomData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import Api from '$supersetApi/projects/apricot/system/workStation.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'WorkStation',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            saveLoading: false,
            treeData: [],
            defualtVal: {
                editLayer: {    
                                 
                }
            },
            editLayer:{
                form:{
                    districtId:'',
                    isEnable: 1,
                }
            },
            sHospitalProps: {
                expandTrigger: 'hover',
                value:'sId',
                label:'sHospitalDistrictName',
                children: 'children',
            },
            condition: {},
            configTable: [
            
            {
                sProp:'districtName',
                sLabel: '院区',
                sAlign: 'left',
                minWidth: '120px'
            },
            {
                sProp: 'stationName',
                sLabel: '工作站名称 ',
                sAlign: 'left',
                sMinWidth: '120px'
            },
            {
                sProp: 'stationSimpleName',
                sLabel: '简称',
                sAlign: 'left',
                sMinWidth: '70px'
            },
            {
                sProp: 'stationTypeName',
                sLabel: '类型',
                sAlign: 'center',
                sWidth: '120px'
            },
            
            {
                sProp: 'roomName',
                sLabel: '对应机房',
                sAlign: 'center',
                sWidth: '180px'
            },
            {
                sProp: 'isEnable',
                sLabel: '启用',
                sAlign: 'center',
                sWidth: '120px'
            },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '200px',
                sFixed: 'right'
            }],
            rules: {
                districtId:[ { required: true, message: '请选择院区',trigger: 'blur'  }],
                stationName: [{ required: true, message: '请输入工作站名称' }],
                stationTypeCode: [{ required: true, message: '请选择工作站类型' }],
                roomId:[{ required: true, message: '请选择机房',}]
            },
            optionsLoc: {
                enableStateOptions: [{
                    sName: '是',
                    sValue: 1
                }, {
                    sName: '否',
                    sValue: 0
                }],
                machineRoomOptions: []
            },
            stationTypeOption: [],
            isEnableRequest: false,
            roomDisabled: true,// 机房是否可编辑
        }
    },
    watch:{

    },
    methods: {
         // 新增
        handleAdd() {
            let params = {
                districtId:'',
                isEnable: 1,
            }
            this.editLayer.form = Object.assign({},params)
            if (this.treeData.length) {
                this.editLayer.form['districtId'] = this.treeData[0].sId
                this.getMachineRoomData(this.treeData[0].sId)      
            }
            this.dialogTitle = '新增';
            this.dialogVisible = true
            let timeout = setTimeout(()=>{
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            if(row.stationTypeCode==='5' || row.stationTypeCode==='2') {
                this.getMachineRoomData(row.districtId)
            }
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({},row)
            this.$nextTick(()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
            
        },
        handleSave() {
            this.editLayer.loading  = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)  
                  return
                }
                this.editLayer.loading  = false
            })
        },
        handleRowClick (row, isCancel = false, id = 'sId') {
            this.onClickRow(row, isCancel, id);
            this.mxOpenDialog(4, '111');
        },
        changeStationType(val) {
        },
        changeHospital(val){
            this.getMachineRoomData(val)
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.enableWorkStation({ stationId: row.stationId, iIsEnable: row.isEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    return;
                }
                this.tableData[index]['isEnable'] = !row.isEnable;
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(err => {
                this.tableData[index]['isEnable'] = !row.isEnable;
                console.log(err);
            })
        },
        mxOnClickReset () {
            this.condition = {};
        },
        
        /**
         * 保存数据
         */
        saveData (params) {
            let jsonData = deepClone(params);
            jsonData.stationTypeName = getOptionName(jsonData.stationTypeCode, this.stationTypeOption);
            jsonData.roomName = getOptionName(jsonData.roomId, this.optionsLoc.machineRoomOptions);
            jsonData.districtName = getOptionName(jsonData.districtId, this.treeData)
            // return;
            if (!jsonData.stationId) {
                // 新增
                Api.addWorkStation(jsonData).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return
            }
            // 修改
            Api.editWorkStation(jsonData).then((res) => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.mxOpenDialog(1, 'no-title');
                    this.mxGetTableList();
                    this.dialogVisible = false
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【${row.stationName}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delWorkStation({ stationId: row.stationId }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        // 获取表格数据
        getData () {
            let params = {
                districtId: this.condition.districtId,
                stationTypeCode: this.condition.stationTypeCode
            }
            this.loading = true;
            Api.getWorkStationData(params).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data || [];
                    this.mxSelected()
                    return
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.loading = false;
            })
        },
        // 赋值选中状态，还原之前选中状态
        mxSelected() {
            this.$nextTick(() => {
                if (this.$refs.mainTable) {
                    // 有些主键是 sId,iId
                    let idx = 0; // 通过id找到某行数据
                    let key = this.editLayer.selectedItem
                    if (key != undefined) {
                        this.tableData.find((item, index) => {
                            if (item.stationId == key.stationId) {
                                idx = index
                            }
                        })
                    }
                    // 赋值选中
                    this.editLayer.selectedItem = Object.assign({}, this.tableData[idx]);
                    this.$refs.mainTable.setCurrentRow(this.tableData[idx]);
                }
            })
        },
        // 获取机房数据
        getMachineRoomData (districtId) {
            let param = {
                condition: {
                    iIsEnable: 1,
                    sDistrictId: districtId
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 500
                },
            }
            getMachineRoomData(param).then(res => {
                if (res.success) {
                    let data = res.data ? res.data : [];
                    if(data.length > 0) {
                        data.map(item => {
                            item.sValue = item.sId;
                            item.sName = item.sRoomName
                            item.workRoom = `${item.sConsultRoomName?item.sConsultRoomName:''}` +'，'+ `${item.sInjectionRoomName?item.sInjectionRoomName:''}`
                        })
                        this.optionsLoc.machineRoomOptions = data
                    } 
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
        // 获取院区数据
        getTreeData () {
            getHospitalData().then((res) => {
                if (res.success) {
                    this.treeData = res?.data || [];
                    this.treeData.map(item =>{
                        item.sValue = item.sId
                        item.sName = item.sHospitalDistrictName
                    })
                   
                    return
                }
            }).catch(() => {
            })
        },
        // 获取工作站类型
        getWorkStationType(districtId) {
            let params = {
                districtId:districtId
            }
            Api.getWorkStationType(params).then((res) =>{
                if(res.success) {
                    this.stationTypeOption = res.data || []
                    this.stationTypeOption.map( item =>{
                        item.sValue = item.stationTypeCode
                        item.sName = item.stationTypeName
                    })
                }
            })
        }
    },
    mounted () {
        this.getTreeData();
        this.getWorkStationType()
        // this.getTableData();
    },
};
</script>
<style lang="scss" scoped>

.c-form .c-form-search {
    >div{
        width: 240px;
        margin:0 5px;
    }
}
.i-sort {
    font-size: 16px;
    margin-left: 10px;
}
</style>
