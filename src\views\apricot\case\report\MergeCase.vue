<template>
  <el-dialog :close-on-click-modal="false" append-to-body width="99vw" align-center @open="openDialog"
    v-model="visible" @close="closeDialog" class="my-dialog t-default">
    <template #header>
        <ul class="c-header el-dialog__title">
          <li>关联检查</li>
          <li>
            <strong class="c-bold">{{ patientInfo.sName }}</strong>
            <span>{{ patientInfo.sSexText }}</span>
            <span>{{ patientInfo.sAge }}</span>
            <span>{{ mxToDate(patientInfo.dBirthday) }}</span>
          </li>
          <li>
            <span>检查日期:</span>
            <span>{{ mxToDate(patientInfo.dAppointmentTime) }}</span>
          </li>
          <li>
            <span>核医学号:</span>
            <strong>{{ patientInfo.sNuclearNum }}</strong>
          </li>
          <li>
            <span>设备类型:</span>
            <strong>{{ patientInfo.sRoomText }}</strong>
          </li>
        </ul>
    </template>
    <div class="g-content m-flexLaout-tx">
      <div class=" g-flexChild">
        <div class="m-flexLaout-ty">
          <div class="g-flexChild" style="flex:3">
            <el-table :data="tablePartData" ref="partDataTable" stripe border height="100%"
              highlight-current-row v-loading="tableLoading1" @row-click="(row) => { handleRowClick(row, 0) }"
              style="width: 100%">
              <el-table-column align="center" label="序号" type="index" width="60">
              </el-table-column>
              <el-table-column v-for="(item, index) in tableHeaderProps" :key="index" :prop="item.sProp"
                :label="item.sLabel" :min-width="item.sMinWidth" show-overflow-tooltip>
              </el-table-column>
              <el-table-column label="操作" align="center" width="150" fixed="right">
                <template v-slot="scope">
                  <el-button-icon-fa type="primary" link _icon="fa fa-unlink" title="解除关联"
                    @click.stop="onClickDelete(scope.row)"></el-button-icon-fa>
                  <el-button-icon-fa type="primary" link @click.stop="openWebReadViewer(scope.row)"
                    _icon="fa fa-reading-image-computer-browse" title="web阅图">
                </el-button-icon-fa>
                  <el-button-icon-fa type="primary" link @click.stop="openWebReBuildViewer(scope.row)"
                    _icon="fa fa-poly-rebuildding-1" title="web重建"></el-button-icon-fa>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="g-flexChild m-flexLaout-ty" style="margin-top:15px;flex:5">
            <el-row class="c-headline">
              <strong style="font-size: 16px;">所有检查</strong>
            </el-row>
            <el-row class="c-search" :gutter="10">
              <el-col :span="4">
                <div class="m-labelInput">
                  <label>姓名拼音</label>
                  <el-input v-model="condition.patientName" clearable></el-input>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="m-labelInput">
                  <label>Patient Id</label>
                  <el-input v-model="condition.patientId" clearable></el-input>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="m-labelInput">
                  <label>Accession Number</label>
                  <el-input v-model="condition.accessionNumber" clearable></el-input>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="m-labelInput">
                  <label>检查日期</label>
                  <el-date-picker v-model="condition.studyDate" value-format="YYYYMMDD"></el-date-picker>
                </div>
              </el-col>
              <el-col :span="4">
                <div class="m-labelInput">
                  <label>导入日期</label>
                  <el-date-picker v-model="condition.createTime" value-format="YYYY-MM-DD"></el-date-picker>
                </div>
              </el-col>
              <el-col :span="4" class="c-button">
                <el-button-icon-fa :loading="loading" @click="mxDoSearch" class="i-button" type="primary" 
                  title="查询" _icon="fa fa-search">查询</el-button-icon-fa>
              </el-col>
            </el-row>

            <div class="g-flexChild c-box relative bottom-1.5">
              <el-table :data="tableData" ref="mainTable" v-loading="loading" stripe border height="100%"
                highlight-current-row @row-click="(row) => { handleRowClick(row, 1) }" style="width: 100%">
                <el-table-column align="center" label="序号" type="index" width="60">
                </el-table-column>
                <el-table-column v-for="(item, index) in tableHeaderProps" :key="index" :prop="item.sProp"
                  :label="item.sLabel" :min-width="item.sMinWidth" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="操作" align="center" width="150" fixed="right">
                  <template v-slot="scope">
                    <el-button-icon-fa type="primary" link _icon="fa fa-link1" @click.stop="onClickRele(scope.row)"
                      title="关联"></el-button-icon-fa>
                    <el-button-icon-fa type="primary" link @click.stop="openWebReadViewer(scope.row)"
                      _icon="fa fa-reading-image-computer-browse" title="web阅图"></el-button-icon-fa>
                    <el-button-icon-fa type="primary" link @click.stop="openWebReBuildViewer(scope.row)"
                      _icon="fa fa-poly-rebuildding-1" title="web重建"></el-button-icon-fa>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="c-pagination scope-pagination-around">
              <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
                :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next" :total="page.total">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <div class="g-flexChild m-flexLaout-ty c-item-01" style="min-width: 600px;">
        <!-- <div class="c-info">
          <el-row :gutter="10">
            <el-col :span="8">
              <strong>姓名：</strong>
              <strong v-show="editLayer.selectedItem.patientInfoName" :title="editLayer.selectedItem.patientNameSpell">{{
                editLayer.selectedItem.patientInfoName }}</strong>
            </el-col>
            <el-col :span="4">
              <strong>性别：</strong>
              <span v-show="editLayer.selectedItem.patientSex" :title="editLayer.selectedItem.patientSex">{{
                editLayer.selectedItem.patientSex }}</span>
            </el-col>
            <el-col :span="6">
              <strong>出生日期：</strong>
              <span v-show="editLayer.selectedItem.patientBirthDate" :title="editLayer.selectedItem.patientBirthDate">{{
                editLayer.selectedItem.patientBirthDate }}</span>
            </el-col>
            <el-col :span="6">
              <strong>Patient Id：</strong>
              <span v-show="editLayer.selectedItem.patientId" :title="editLayer.selectedItem.patientId">{{
                editLayer.selectedItem.patientId }}</span>
            </el-col>
          </el-row>
        </div>
        <div class="c-checkGroup">
          <el-checkbox v-model="enableRebuild" :true-label="1" :false-label="0">可重建</el-checkbox>
          <el-checkbox v-model="disableRebuild" :true-label="1" :false-label="0">不可重建</el-checkbox>
        </div> -->
        <div class="g-flexChild">
          <ImageStudy :params="editLayer.selectedItem" :isImageCase="false"
            :modelValue="visible" @closeDialog="closeDialog"></ImageStudy>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import ImageStudy from '$supersetViews/apricot/components/ImageStudy.vue'
import { deepClone } from '$supersetUtils/function'
import { mixinTable, getStoreNameByRoute, openWebReadImgOrRebuild } from '$supersetResource/js/projects/apricot/index.js'
import Api from '$supersetApi/projects/apricot/case/report.js'
import ApiAssist from '$supersetApi/projects/apricot/assistServe/index.js'
export default {
  name: 'MergeCase',
  mixins: [mixinTable],
  components: { ImageStudy },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    moduleName: {
      type: String,
      default: ''
    }
  },
  emits: ['update:dialogVisible'],
  data() {
    return {
      tableLoading1: false,
      onceLoad: true,
      visible: false,
      info: {},
      iModule: '',
      tableHeaderProps: [
        {
          sProp: 'patientName',
          sLabel: '拼音',
          sMinWidth: '120px',
        },
        {
          sProp: 'patientSex',
          sLabel: '性别',
          sMinWidth: '60px',
        },
        {
          sProp: 'patientBirthDate',
          sLabel: '出生日期',
          sMinWidth: '100px',
        },
        {
          sProp: 'patientId',
          sLabel: 'Patient Id',
          sMinWidth: '110px',
        },
        {
          sProp: 'accessionNumber',
          sLabel: 'Accession Number',
          sMinWidth: '150px',
        },
        {
          sProp: 'studyDate',
          sLabel: '检查日期',
          sMinWidth: '100px',
        },
        {
          sProp: 'studyTime',
          sLabel: '检查时间',
          sMinWidth: '100px',
        },
        {
          sProp: 'modality',
          sLabel: '检查类型',
          sMinWidth: '100px',
        },
        {
          sProp: 'studyId',
          sLabel: '检查Id',
          sMinWidth: '140px',
        },
      ],
      tablePartData: [],
      enableRebuild: 1,
      disableRebuild: 0,
      clientParams: {
        needVerifyLicence: null,//阅图是否需要授权
        verificationStatus: null, // 授权状态
        clientId: null,
      }
    }
  },
  computed: {
    userInfo() {
      let temp = this.$store.getters['user/userSystemInfo']
      if (temp.__proto__.constructor === Object) {
        return temp
      } else {
        return {}
      }
    },
    patientInfo() {
      if (this.$store.state.apricot[this.iModule]) {
        let patientInfo = this.$store.state.apricot[this.iModule].patientInfo || {}
        return patientInfo;
      }
      return {};
    }
  },
  watch: {
    dialogVisible() {
      this.visible = this.dialogVisible;
    }
  },
  methods: {
    // 点击行
    // isAllDataTable  是否所有检查表格
    handleRowClick(row, isAllDataTable) {
      if (isAllDataTable == 0) {
        row.sPatientInfoId = this.patientInfo.sId
      }
      isAllDataTable == 1 ? this.$refs.partDataTable.setCurrentRow(-1) : this.$refs.mainTable.setCurrentRow(-1);
      this.onClickRow(row)
    },
    // 打开web重建   openWebReadImgOrRebuild
    async openWebReBuildViewer(row, iIsRebuild = 1) {
      let load = this.$loading({
        lock: true,
        text: '正在加载中，请稍等',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      if (this.moduleName != 'remoteConsultMng') {
        await this.getClientConfig()
        const needVerifyLicence = this.clientParams.needVerifyLicence
        // 若接口链接超时，默认值为null,
        if (needVerifyLicence == null) {
          load.close()
          return
        }
        if (needVerifyLicence) {
          await this.getClientId()
          const clientId = this.clientParams.clientId
          // 没有获取到id，默认值为null,
          if (!clientId) {
            load.close()
            return
          }
          await this.getVerificationCode()
          if (!this.clientParams.verificationStatus) {
            load.close()
            return
          }
        }
      }
      load.close()
      let info = {
        accessNo: row.accessionNumber,
        studyDate: row.studyDate,
        patientId: row.patientId
      }
      openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info, true);
    },
    // 打开web阅图
    openWebReadViewer(row) {
      this.openWebReBuildViewer(row, 0)
    },
    onReduilbOpen(iIsRebuid = 0) {
      if (!Object.keys(this.editLayer.selectedItem).length) {
        this.$message.error('请选择一条患者数据');
        return;
      }
      // if (!this.editLayer.selectedItem.sImgPatientId) {
      //     this.$message.warning('图像患者标识不存在');
      //     return
      // }
      let load = this.$loading({
        lock: true,
        text: '正在加载中，请稍等',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      const params = {
        sImgPatientId: this.editLayer.selectedItem.patientId,
        sImgStudyDate: this.editLayer.selectedItem.studyDate,
        sImgAccessionNumber: this.editLayer.selectedItem.accessionNumber,
        iIsRebuid: iIsRebuid
      }
      ApiAssist.rebuildOpenView(params).then((res) => {
        load.close()
        if (res && !res.success) {
          this.$message.error(res.msg)
          return
        }
      }).catch(() => {
        load.close()
      })
    },
    // 获取关联检查数据
    getPatientStudy() {
      const params = {
          iIsRebuid: null,
          sPatientInfoId: this.patientInfo.sId
      } 
      this.tableLoading1 = true;
      Api.findStudyByPatientInfoId(params).then((res) => {
          this.tableLoading1 = false;
          this.tablePartData = [];
          if (res.success) {
              this.tablePartData = res?.data || []
              return
          }
          this.tablePartData = [];
          this.$message.error(res.msg);
      }).catch(() => {
          this.tableLoading1 = false;
          this.tablePartData = [];
      })
    },
    // 获取患者信息
    getInfo() {
      Api.getImgPatientInfo({ sPatientId: this.patientInfo.sId }).then(res => {
        if (res.success) {
          this.info = res.data
        }
      })
    },
    /**
     * 点击删除提示
     */
    onClickDelete(row) {
      this.$confirm('您确定要解除关联吗，是否继续？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Api.delPatientStudy({ studyInstanceUid: row.studyInstanceUid, sPatientInfoId: this.patientInfo.sId, }).then(res => {
          if (res.success) {
            this.$message({
              message: res.msg,
              type: 'success',
              duration: 3000
            });
            this.getPatientStudy();
            return;
          }
          this.$message({
            message: res.msg,
            type: 'error',
            duration: 3000
          });
        })
      }).catch(err => {
        console.log(err)
      });
    },
    // 点击关联
    onClickRele(row) {
      // TODO 是否需要判断一下患者与关联的图像关系问题
      const params = {
        studyInstanceUid: row.studyInstanceUid,
        sPatientInfoId: this.patientInfo.sId
      }
      // 请求保存
      Api.addPatientStudy(params).then(res => {
        if (res.success) {
          this.$message({
            message: '关联成功！',
            type: 'success',
            duration: 3000
          });
          this.getPatientStudy()
          return;
        }
        this.$message({
          message: res.msg,
          type: 'error',
          duration: 3000
        });
      })
    },
    // 获取所有检查数据
    getData(params) {
      if (this.onceLoad) {
        this.onceLoad = false
        return;
      }

      let jsonData = deepClone(params);
      if (jsonData.condition.sImgStudyDateSt) {
        jsonData.condition.sImgStudyDateEd = jsonData.condition.sImgStudyDateSt;
      }
      Api.getImgPatientData(jsonData).then((res) => {
        this.loading = false;
        if (res.success) {
          this.tableData = res.data?.recordList || [];
          this.page.total = res.data.countRow;

          this.$nextTick(() => {
            this.$refs.mainTable.doLayout();
          })
          // 赋选中状态
          // this.mxSetSelected()
          return
        }
        this.tableData = []
      }).catch(() => {
        this.loading = false;
        this.tableData = []
      })
    },
    openDialog() {
      // this.getInfo()
      this.mxDoSearch()
      this.getPatientStudy();
    },
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    // 查询客户端配置
    async getClientConfig() {
      await Api.queryClientConfig().then(res => {
        if (res.success) {
          this.clientParams.needVerifyLicence = res.data.needVerifyLicence
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    },
    // 查询本机主板序列号
    async getClientId() {
      await Api.readClientId().then(res => {
        if (res.success) {
          this.clientParams.clientId = res.data
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    },
    // 获取授权状态
    async getVerificationCode() {
      let params = {
        clientId: this.clientParams.clientId
      }
      await Api.verificationCode(params).then(res => {
        if (res.success) {
          this.clientParams.verificationStatus = res.success
          return
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    }
  },
  created() {
    this.iModule = getStoreNameByRoute(this.$route.name);
  }
}
</script>
<style lang="scss" scoped>

.c-header {
  padding: 0px;
  display: flex;
  font-size: 16px;
  margin: 0;

  li {
    margin-right: 20px;
    list-style-type: none;

    span:not(:last-child), strong:not(:last-child) {
      padding-right: 8px;
    }
  }
}

.g-content {
  height: 92vh;

  .g-flexChild {
    overflow: hidden;
  }

  .c-button {
    .el-button {
      margin-top: 12px;
    }
  }

  .c-headline {
    color: var(--el-color-primary)
  }

  .c-search .m-labelInput {
    margin-left: 0;
  }

  .c-group {
    padding-top: 10px;
    text-align: left;
    margin-left: 10px;
  }

  .c-item-01 {
    padding-left: 15px;

    .c-info {
      margin-bottom: 15px;

      .el-col {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .c-checkGroup {
      margin-bottom: 10px;
    }
  }
}
:deep(.el-pagination) {
    padding-right: 0;
}
</style>
