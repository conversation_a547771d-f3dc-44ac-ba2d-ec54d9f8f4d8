<template>
    <div class="g-content">
        <DragAdjust :dragAdjustData="DA0">
            <template v-slot:c1>
                <div class="c-table">
                    <el-table-extend v-loading="loading" 
                        :data="tableData"
                        :default-sort="defaultSort"  
                        ref="mainTable"
                        highlight-current-row 
                        border 
                        stripe 
                        height="100%"
                        :iModuleId="iModuleId"
                        :configBtn="isShowConfigBtn"
                        storageKey="PatientHistoryReportTable" 
                        @sort-change="mxOnSortTime" 
                        @row-click="onClickRow">
                        <el-table-column type="index" label="序号" prop="_index" align="center" width="60"
                            class-name="tableColOverflowVisible"></el-table-column>
                        <el-table-column v-for="item in tableConfig.filter(i => !i.iIsHide)"
                            :show-overflow-tooltip="item.sProp !== 'img'" :key="item.index" :prop="item.sProp"
                            :label="item.sLabel" :fixed="item.sFixed" :align="item.sAlign" :width="item.sWidth"
                            :min-width="item.sMinWidth" :sortable="(!!item.iSort) ? 'custom' : false"
                            :column-key="item.sSortField ? item.sSortField : null">
                            <template v-slot="scope">
                                <template v-if="item.sProp.slice(0, 1) === 'd'">
                                    {{ mxFormatterDate(scope.row[`${item.sProp}`]) }}
                                </template>
                                <template v-else>
                                    {{ scope.row[`${item.sProp}`] }}
                                </template>
                            </template>
                        </el-table-column>
                    </el-table-extend>
                </div>
            </template>
            <template v-slot:c2>
                <ul class="c-ul">
                    <el-scrollbar class="my-scrollbar">
                            <li>
                                <h4>临床诊断:</h4>
                                <div v-html="currentItem.sClinicalDiagnosis"></div>
                            </li>
                            <li>
                                <h4>简要病史:</h4>
                                <div v-html="currentItem.sMedicalHistory"></div>
                            </li>
                            <li>
                                <h4>既往病史:</h4>
                                <div v-html="currentItem.sInquiryOtherMessage"></div>
                            </li>
                            <li class="i-height">
                                <h4>检查所见:</h4>
                                <div v-html="currentItem.sInspectSee"></div>
                            </li>
                            <li class="i-height">
                                <h4>诊断意见:</h4>
                                <div v-html="currentItem.sDiagnosticOpinion"></div>
                            </li>
                    </el-scrollbar>
                </ul>
            </template>
        </DragAdjust>
    </div>
</template>
<script>
// 配置
import Configs from "../config"
import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { transformDate } from '$supersetUtils/function'
// 接口
import { getPatientQueryPastPatients, getPatientResultById } from '$supersetApi/projects/apricot/case/consult.js'
export default {
    name: 'HistoryReport',
    emits: ['changeActiveTab'],
    inject: {
        patientInfo: {
            from: 'patientInfo',
            default: () => ({})
        },
        iModuleId: {
            from: 'iModuleId',
            default: ''
        },
    },
    props: {
        historyRelatedCheck: {
            type: Object,
            default: () => ({
                hasRecordNo: 0,
                hasNuclearNum: 0,
                hasIdNum: 0,
            })
        },
        isShowConfigBtn: {
            type: Boolean,
            default: true
        }
    },
    data () {
        return {
            isMixinDynamicGetTableHead: true,
            tableConfig: [...Configs.historyTableConfig],
            loading: false,
            tableData: [],
            contentLoading: false,
            currentItem: {},
            condition: {},
            defaultSort: {
                // ascending 升序
                prop: 'dAppointmentTime', order: 'descending'
            },
            DA0: {
                type: 't-y',
                localStorageKey: '202307241451',
                panelConfig: [
                    {
                        size: 150,
                        minSize: 50,
                        name: "c1",
                        isFlexible: false

                    },
                    {
                        minSize: 0,
                        name: "c2",
                        isFlexible: true
                    }
                ]
            },
            DA1: {
                type: 't-x',
                localStorageKey: '202304231416',
                panelConfig: [
                    {
                        size: 300,
                        minSize: 200,
                        maxSize: 1000,
                        name: "c1",
                        isFlexible: false

                    },
                    {
                        size: 0,
                        minSize: 300,
                        name: "c2",
                        isFlexible: true
                    }
                ]
            },
            optionsLoc: {
                sex: appointmentEnum.sexOptions
            },
            isChecks: {
                sIdNum: false,
                sNuclearNum: false,
                sMedicalRecordNO: false,
                sOutpatientNO: false,
                sInHospitalNO: false
            },
            tableDom: undefined,
            selectedItem: {},
            isShowHistoryMatchPop: false
        }
    },
    watch: {
        patientInfo: {
            async handler (val, oldVal) {
                if (val && oldVal && val.sId === oldVal.sId) return
                this.$nextTick(async () => {
                    this.tableData = [];
                    this.currentItem = {};
                    val.sId && await this.getData();
                })
            }
        },
        historyRelatedCheck(val) {
            if(val) {
                this.patientInfo.sId && this.getData();
            }
        }
    },
    methods: {
        mxFormatterDate (time) {
            return transformDate(time, false, 'yyyy-MM-dd HH:mm')
        },
        mxOnSortTime (column) {
            if (column.order == null) {
                delete this.condition.ascOrDesc
                delete this.condition.orderField
                this.mxDoSearch();
                return
            }
            if (column.order == 'ascending') {
                this.condition.ascOrDesc = 'asc'
            } else {
                this.condition.ascOrDesc = 'desc'
            }
            this.condition.orderField = column.prop
            this.mxDoSearch();
        },
        onClickRow (row) {
            this.selectedItem = row;
            this.getPatientResultById(row)
        },
        // mxDoRefresh 会回调这个方法--获取表格数据
        getData () {
            let jsonData = {
                sName: this.patientInfo.sName,
                sSex: this.patientInfo.sSex,
                sPatientInfoId: this.patientInfo.sId
            }
            if (this.historyRelatedCheck.hasRecordNo) {
                jsonData.sMedicalRecordNO = this.patientInfo.sMedicalRecordNO
            }
            if (this.historyRelatedCheck.hasNuclearNum) {
                jsonData.sNuclearNum = this.patientInfo.sNuclearNum
            }
            if (this.historyRelatedCheck.hasIdNum) {
                jsonData.sIdNum = this.patientInfo.sIdNum
            }

            Object.assign(jsonData, this.condition);

            this.loading = true;
            getPatientQueryPastPatients(jsonData).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data?.pastPatientInfos || [];
                    // 赋选中状态
                    if (this.tableData.length) {
                        this.onClickRow(this.tableData[0]);
                        this.$refs.mainTable.setCurrentRow(this.tableData[0])
                    }
                    this.$emit('changeActiveTab', this.tableData.length);
                    return
                }
                this.tableData = [];
            }).catch(() => {
                this.loading = false;
                this.tableData = [];
            })
        },
        // 获取患者结果通过id
        getPatientResultById (row) {
            this.contentLoading = true
            getPatientResultById({ sPatientId: row.sId }).then(res => {
                this.contentLoading = false
                if (res.success) {
                    this.currentItem = res.data;
                    return;
                }
                this.currentItem = {}
                this.$message.error(res.msg)
            }).catch(() => {
                this.contentLoading = false
            })
        },
        onClickRow (row) {
            this.getPatientResultById(row)
        },
    },
}
</script>
<style lang="scss" scoped>
.g-content {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;

    .c-table {
        height: 100%;
    }
    .c-search {
        padding: 0 5px 0;
        text-align: right;

        .c-line {
            display: flex;
            align-items: center;
        }
    }

    .c-ul {
        height: 100%;
        margin: 0;
        padding: 0;
        border: 1px solid var(--el-border-color-light);
        box-sizing: border-box;
        overflow: auto;

        li {
            list-style: none;
            width: 100%;
            display: flex;
            // margin-bottom: 2px;
            // background: rgba(0,0,0, 0.03);
            border-bottom: 1px solid var(--el-border-color-light);
            box-sizing: border-box;
            &:last-child {
                border-bottom: none;
                margin-bottom: 0;
            }
            &.i-height {
                min-height: calc((100% - 95px) / 2);
            }

            h4 {
                width: 80px;
                height: 30px;
                margin: 0px;
                font-weight: normal;
                line-height: 30px;
                text-indent: 10px;
                // color: var(--el-color-info);
                color: #838a9d;
            }

            >div {
                flex: 1;
                padding: 7px 10px;
                overflow: auto;
                box-sizing: border-box;
            }

            * {
                white-space: break-spaces;
                word-break: break-word;
            }
        }
    }
    
}
</style>
