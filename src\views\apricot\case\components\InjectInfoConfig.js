export const InjectForm = [{
  sProp: 'sDrugDeliveryCode',
  sLabel: '给药方式',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'option',
  sOptionProp: 'ApricotReportDrugDelivery',
  iLayourValue: 8
},
{
  sProp: 'sInjectionPosition',
  sLabel: '注射部位',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'option',
  sOptionProp: 'ApricotReportInjectSite',
  iLayourValue: 8
},
{
  sProp: 'sStainPosition',
  sLabel: '渗漏',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'option',
  sOptionProp: 'ApricotReportStain',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'dInjectionSta',
  sLabel: '满针时间',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'time-picker',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'dInjectionTime',
  sLabel: '注射时间',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'time-picker',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'dInjectionEnd',
  sLabel: '空针时间',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'time-picker',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'fFullNeedle',
  sLabel: '满针剂量',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'number',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'fFactDose',
  sLabel: '实际注射剂量',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'number',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'fEmptyNeedle',
  sLabel: '空针剂量',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'number',
  iCustom: 1,
  iLayourValue: 8
},
{
  sProp: 'sMedicineSource',
  sLabel: '药物来源',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'option',
  sOptionProp: 'ApricotReportMedicineSource',
  iLayourValue: 8
},
{
  sProp: 'sNurseId',
  sLabel: '注射人',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'option',
  sOptionProp: 'DoctorOptions',
  iLayourValue: 8
},
{
  sProp: 'dInjectDate',
  sLabel: '注射日期',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  iRequired: 1,
  sInputType: 'date-picker',
  iLayourValue: 8
},
{
  sProp: 'iIntake',
  sLabel: '注射前饮水量(ml)',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'number',
  iLayourValue: 8
},
{
  sProp: 'iAft3Intake',
  sLabel: '30分钟后饮水量(ml)',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'number',
  iLayourValue: 8
},
{
  sProp: 'iAft6Intake',
  sLabel: '60分钟后饮水量(ml)',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'number',
  iLayourValue: 8
},
{
  sProp: 'sBefDrinkTypeCode',
  sLabel: '饮水类型',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'option',
  sOptionProp: 'ApricotReportDrinkType',
  iLayourValue: 8
},
{
  sProp: 'sAft3DrinkTypeCode',
  sLabel: '饮水类型',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'option',
  sOptionProp: 'ApricotReportDrinkType',
  iLayourValue: 8
},
{
  sProp: 'sAft6DrinkTypeCode',
  sLabel: '饮水类型',
  sWidth: '100%',
  sHeight: '40px',
  sFontSize: '15px',
  sLabelFontSize: '15px',
  sInputType: 'option',
  sOptionProp: 'ApricotReportDrinkType',
  iLayourValue: 8
}
]
