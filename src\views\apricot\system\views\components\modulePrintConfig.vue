<template>
    <div class="c-container">
        <div class="c-left m-flexLaout-ty">
            <h4 style="padding:8px; border-bottom: 1px solid #eee;">模块</h4>
            <div class="g-flexChild">
                <el-tree ref="tree"
                    :data="treeData"
                    node-key="sValue"
                    highlight-current
                    empty-text=""
                    :props="defaultProps"
                    @node-click="onNodeClick">
                </el-tree>
            </div>
        </div>
        <div class="c-right">
            <div class="c-flex-context">
                <div class="c-form">
                    <h4 class="modulesName">
                        模块名称：<span>{{ seletedNode.sName }}</span>
                    </h4>
                    <div class="pull-right">
                        <el-button-icon-fa _icon="fa fa-save"
                            type="primary"
                            @click="saveSelected"
                            :loading="btnLoading">保存</el-button-icon-fa>
                    </div>
                </div>
                <div class="c-flex-auto">
                    <div class="c-content"
                        v-loading="loading">
                        <el-table :data="tableData"
                            id="itemTable"
                            ref="mainTable"
                            
                            border
                            stripe
                            height="100%"
                            style="width: 100%"
                            @selection-change="handleSelectedTemplate">
                            <el-table-column type="selection"
                                width="55"
                                align="center"></el-table-column>
                            <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                                show-overflow-tooltip
                                :key="item.index"
                                :prop="item.sProp"
                                :label="item.sLabel"
                                :fixed="item.sFixed"
                                :align="item.sAlign"
                                :width="item.sWidth"
                                :min-width="item.sMinWidth"
                                :sortable="!!item.iSort">
                                <!-- <template v-slot="scope">
                                    <template>
                                        {{scope.row[`${item.sProp}`]}}
                                    </template>
                                </template> -->
                                <!-- <template v-slot:header
                                    v-slot="scope">
                                    <span>{{item.sLabel}}</span>
                                    <i v-if="item.sProp === 'action'"
                                        class="el-icon-rank i-sort"
                                        style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                        title="首次或无法排序时，点击初始化排序"
                                        @click="autoSort"></i>
                                </template> -->
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>
<script>
import { systemModuleOption } from '$supersetResource/js/projects/apricot/enum.js'
import Api from '$supersetApi/projects/apricot/system/modulePrintConfig.js'
// import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'ThirdLink',
    //mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            loading: false,
            tableData: [],
            selectedTemplates: [],
            btnLoading: false,
            // treeData: systemModuleOption,
            treeData:[],
            defaultProps: {
                children: 'children',
                label: 'sName'
            },
            seletedNode: {},
            configTable: [
                {
                    sProp: 'sClassify',
                    sLabel: '模板类型',
                    sAlign: 'center',
                    sMinWidth: '120px',
                },
                {
                    sProp: 'sDeviceTypeName',
                    sLabel: '设备类型',
                    sAlign: 'center',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sType',
                    sLabel: '内容类型',
                    sAlign: 'center',
                    sMinWidth: '100px',
                },
                {
                    sProp: 'sName',
                    sLabel: '模板名称',
                    sAlign: 'left',
                    sMinWidth: '300px'
                }],
        }
    },
    methods: {
        onNodeClick (node) {
            this.seletedNode = node
            this.getModuleTemlateSelected(node.sValue)
            // this.mxGetTableList();
        },
        // 选择模板
        handleSelectedTemplate (val) {
            this.selectedTemplates = val
        },
        /**
         * 保存数据
         */
        saveSelected () {
            this.btnLoading = true
            const iModuleId = this.seletedNode.sValue
            let params = []
            if (this.selectedTemplates.length > 0) {
                this.selectedTemplates.forEach(item => {
                    params.push({
                        iModuleId: iModuleId,
                        sTemplateId: item.sId,
                    })
                })
            } else {
                params = [{
                    iModuleId: iModuleId,
                    sTemplateId: '',
                }]
            }
            Api.saveTemlateSelect(params).then(res => {
                this.btnLoading = false
                if (res.success) {
                    this.$message.success(res.msg)
                    return
                }
                this.$message.error(res.msg)
                this.getModuleTemlateSelected(iModuleId)
            }).catch(err => {
                this.btnLoading = false
                console.log(err)
            })
        },

        // 获取模块打印设置数据
        getModuleTemlateSelected (moduleId) {
            this.loading = true
            let params = {
                iModuleId: moduleId
            }
            let selected = []
            this.$refs.mainTable.clearSelection()
            Api.getTemlateSelected(params).then(res => {
                this.loading = false
                if (res.success) {
                    selected = res.data;
                    if (selected.length > 0) {
                        let arrSelect = []
                        this.tableData.forEach(item => {
                            //debugger
                            const ressult = selected.find(obj => item.sId === obj.sTemplateId)
                            if (ressult) {
                                arrSelect.push(item)
                            }
                        })
                        if (this.$refs.mainTable) {
                            arrSelect.forEach(item => {
                                this.$refs.mainTable.toggleRowSelection(item);
                            })
                        }
                    }
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
                this.loading = false;
            })
        },


        /**
         * 获取表格数据
         */
        async getData () {
            this.loading = true
            await Api.getTemplateData().then((res) => {
                this.loading = false
                if (res.success) {
                    this.tableData = res.data
                    return
                }
                this.$message.error(res.msg)
            }).catch((err) => {
                console.log(err)
                this.loading = false
            })
        },
        // 获取模块id
        async getAllModule() {
            await Api.getModulesId({}).then( res =>{
                if(res.success) {
                    this.treeData = res.data?res.data:[]
                    this.treeData.map( item =>{
                        item.sValue = item.sysModuleCode
                        item.sName = item.sysModuleName
                    })
                }
            })
        }
    },
    async mounted () {
        await this.getAllModule()
        await this.getData()
        this.$nextTick(() => {
            this.seletedNode = this.treeData[0]
            this.$refs.tree.setCurrentKey(this.seletedNode.sValue)
            this.getModuleTemlateSelected(this.seletedNode.sValue)
        });

    },
    created () {

    }

};
</script>
<style lang="scss" scoped>
.c-container {
    height: 100%;
    display: flex;
    padding-left: 10px;
    padding-bottom: 10px;
    overflow: hidden;
    .c-left {
        padding: 10px 0 0 0;
        width: 260px;
        border-right: 1px solid #eee;
        overflow: hidden;
        overflow: auto;
        :deep(.el-tree-node__content ){
            height: 36px;
        }
    }
    .c-right {
        flex: 1;
        overflow: hidden;
    }
}
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    // padding-top: 22px;
    .c-form {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #eee;
        align-items: center;
        .modulesName {
            padding-left: 15px;
            font-size: 16px;
            span {
                font-weight: bold;
            }
        }
        .pull-right {
            margin-right: 15px;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 10px 15px 0;
        overflow: auto;
        .c-search {
            display: flex;
            align-items: flex-end;
            flex-wrap: wrap;
            padding: 10px;
            margin-left: -10px;
            > button {
                margin-top: 13px;
            }
        }
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
    // .m-labelInput {
    //     width: 100%;
    //     margin-left: 0;
    // }
}
</style>
