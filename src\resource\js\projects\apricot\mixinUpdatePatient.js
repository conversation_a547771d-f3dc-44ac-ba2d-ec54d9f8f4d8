import Api from '$supersetApi/projects/apricot/case/index.js'
//  混入更新一条table患者信息
const mixinUpdatePatient = {
	methods: {
        updateTableInfo(id = null, idx = null){
            const sId = id !== null ? id : this.editLayer.selectedItem.sId
            const index = idx !== null ? idx : this.editLayer.selectedItem.index
            Api.getPatientInfoById({sId}).then(res => {
                if (res.success){
                    // console.log(res.data)
                    this.tableData.splice(index, 1, res.data)
                    // console.log(this.tableData)
                    this.editLayer.selectedItem = this.tableData[index]; // 更新选中值
                    this.$refs.mainTable && this.$refs.mainTable.setCurrentRow(this.tableData[index]) // 设置选中值
                    return;
                }
            })
        }
	},
}
export default mixinUpdatePatient;
