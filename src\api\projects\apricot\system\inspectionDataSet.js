import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'
// 表格数据
export function getInspectionData(data) {
    return request({
        url: baseURL.apricot + '/inspection/set/find/page',
        method: 'POST',
        data
    })
}
// 保存数据
export function saveInspectionData(data) {
    return request({
        url: baseURL.apricot + '/inspection/set/save/batch',
        method: 'POST',
        data
    })
}
// 删除数据
export function delInspectionData(params) {
    return request({
        url: baseURL.apricot + '/inspection/set/del/sId',
        method: 'POST',
        params: params
    })
}