@use "element-plus/theme-chalk/src/message.scss" as *;
@use "element-plus/theme-chalk/src/message-box.scss" as *;
 
// 加入自定义变量
html {
  &:root {
    --el-input-font-weight: 400;
    --el-input-text-align: inherit;
    --el-border-radius-base: 2px;
  }
}
.el-cascader__dropdown.el-popper {
  position: fixed !important;
}

div.el-step__icon.is-icon {
  width: auto;
  border-radius: 50%;
  border: 2px solid;
  border-color: inherit;
}

.el-step__title {
  line-height: 1.2;
}

[class*=el-col-] {
  float: left;
}

.el-scrollbar__view {
  height: 100%;
}
 

.el-header {
  --el-header-padding: 0;
}

.el-button--primary:not(.is-link):active {
  background-color: $base-color-primary !important;
  border-color: $base-color-primary !important;
} 
.el-button.is-link:not(.is-disabled):hover {
    background-color: rgba(0, 0, 0, 0.1);
}

:focus-visible {
  // border: none !important;
  // outline: none !important;
}
.icon-hover{
  cursor: pointer;
  &:hover {
    background-color: #ffffff63;
  }
}


html {
  --table-selected-row-bg: #fff3e0;
  --table-highlight-row-bg: #ffebbc;
  // 主题色
  &:root {
    --el-fill-color-lighter: #f5f5f5;
    --el-text-color-regular: #3c4353;
    --theme-color: #3c4353;
    --theme-bg: #fff;
    --theme-table-header-bg-color: #f5f7fa;
    --theme-card-bg-color: #fcfcfc;
    --theme-header-color: #fff;
    --theme-menu-background-color: #e6e6e6;
    --theme-menu-title-background-color: #fdfdfd; 

  }

  &.theme1:root {
    --theme-header-bg: #0d9488;
    --el-color-primary: #0d9488;
    --el-color-primary-light-3: #14b8a6;
    --el-color-primary-light-5: #0d9488;
    --el-color-primary-light-7: #0d9488;
    --el-color-primary-light-8: #f0fdfa;
    --el-color-primary-light-9: #f0fdfa;
    --el-color-primary-dark-2: #0f766e;
  }

  &.theme2:root {
    --theme-header-bg: #2384d3;
    --el-color-primary: #2384d3;
    --el-color-primary-light-3: #3789d5;
    --el-color-primary-light-5: #2384d3;
    --el-color-primary-light-7: #2384d3;
    --el-color-primary-light-8: #ecf5ff;
    --el-color-primary-light-9: #ecf5ff;
    --el-color-primary-dark-2: #4f9ddc;
  }

  &.theme3:root { 
    --theme-header-bg: #114f8e;
    --el-color-primary: #114f8e;
    --el-color-primary-light-3: #1767b6;
    --el-color-primary-light-5: #114f8e;
    --el-color-primary-light-7: #114f8e;
    --el-color-primary-light-8: #edf3fe;
    --el-color-primary-light-9: #edf3fe;
    --el-color-primary-dark-2: #036;  
  }

  &.theme4:root { 
    --theme-header-bg: #304269;
    --el-color-primary: #304269;
    --el-color-primary-light-3: #466097;
    --el-color-primary-light-5: #304269;
    --el-color-primary-light-7: #304269;
    --el-color-primary-light-8: #ebf1f9;
    --el-color-primary-light-9: #ebf1f9;
    --el-color-primary-dark-2: #23304d; 
  }

  body {
    @include base-scrollbar;
    position: relative;
    box-sizing: border-box;
    height: 100vh;
    padding: 0;
    overflow: hidden;
    * {
      position: relative;
    }
    .el-button {
      border-color: var(--el-button-border-color);
    }
    .el-button--default {
      border-color: var(--el-button-border-color);
    }
    .el-button--primary.is-link, .el-button--primary.is-plain, .el-button--primary.is-text {
      border-color: var(--el-button-border-color);
    }
    .el-button--primary.is-link {
      color: var(--el-color-primary);
    }

    .el-button.is-disabled, .el-button.is-disabled:focus, .el-button.is-disabled:hover {
      filter: brightness(0.75);
    }

    .el-input-group__append, .el-input-group__prepend {
      // padding: 0;
    }
    .el-date-editor.el-date-editor--date, .el-date-editor.el-date-editor--time, .el-date-editor.el-date-editor--datetime {
      .el-input__wrapper .el-input__prefix  {
        display: none;
      }

    }

    .el-date-editor.el-input__wrapper {
      width: auto;
    }

    .el-loading-spinner {
      background: var(--theme-card-bg-color);
      width: 240px;
      /* height: auto; */
      margin: auto;
      margin-top: calc((0px - var(--el-loading-spinner-size)));
      left: 0;
      right: 0;
      padding: 24px 0;
      border-radius: 4px;
      box-shadow: 0 0 10px gray;
    }

    .el-notification {
      --el-notification-padding: 10px 28px 18px 13px;
    }

    .el-notification .el-notification__closeBtn {
      top: 3px;
      right: -23px;
    }

    .el-table__body tr.hover-row.el-table__row--striped.current-row>td.el-table__cell {
      background-color: var(--table-highlight-row-bg);
    }

    .eltableex-container .el-popper.is-dark {
        max-width: 50vw;
    }

  }

  div {
    @include base-scrollbar;
  }



  // 覆盖element 样式

  .el-menu--popup { 
    min-width: 0px; 
  }

//   .el-tabs {
//     --el-tabs-header-height: 30px;
//   }
  
  // table 多选
  .el-checkbox {
    --el-checkbox-input-height: 20px;
    --el-checkbox-input-width: 20px;
    height: 28px;

    .el-checkbox__inner::after {
      height: 12px;
      left: 7px;
    }
  }

  .el-dialog__header {
    margin-right: 0;
    padding-top: 11px;
    color: var(--theme-color);
    // padding: 10px 14px;
    // background-color: #f5f5f5;
    // border-bottom: 1px solid #dbdbdb;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-size: 16px;
    border-bottom: var(--el-border);
    background: var(--el-color-primary-light-9);
  }

  .el-dialog__headerbtn {
    width: 45px;
    height: 38px;
  }

  .el-dialog__body {
    color: var(--theme-color);
    // min-height: 300px;
    padding: 30px 20px 30px 20px;
    .el-dialog-body-default {
      padding-right: 10px;
      max-height: 600px;
      overflow-y: auto;
    }
  }

  .el-dialog__footer {
    // border-top: 1px solid #dbdbdb;
    padding-top: 11px;
    padding-bottom: 10px;
    color: var(--theme-color);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    border-top: var(--el-border);
    background: #f8f8f8;
  }

  .el-dialog__title {
    color: var(--theme-color);
  }

  .el-table .el-table__cell {
    padding: 0px;
    height: 35px;
    box-sizing: border-box;

    .el-input__wrapper {
      font-size: 13px;
    }
  }

  .el-table .el-table__header-wrapper .el-table__cell,
  .el-table .el-table__fixed-header-wrapper .el-table__cell {
    padding: 5px 0px;
    height: 36px;
  }

  .el-table {
    color: var(--theme-color);
    background-color: var(--theme-card-bg-color);
    --el-table-current-row-bg-color: var(--table-highlight-row-bg);
    // --el-table-row-hover-bg-color: var(--table-highlight-row-bg);
    font-size: 13px;
    vertical-align: middle;
  }

  .el-table--border .el-table__header {
      border-collapse: collapse;
  }

  .el-table__header-wrapper th.el-table__cell.is-leaf {
    border-bottom: 2px solid #ddd;
  }

  .el-table thead {
    color: var(--theme-color);
    font-size: 13px;
  }

  .el-table tbody {
    color: var(--theme-color);
    position: relative;
  }


  .el-form-item__label {
    color: #484D54;
  }

  .el-table tr th.el-table__cell.is-leaf {
    background: var(--theme-table-header-bg-color);
  }
  .el-table__body-wrapper tr td.el-table-fixed-column--left {
    z-index: 2;
  }
  .el-table.is-scrolling-middle .el-table-fixed-column--left  {
    z-index: 7;
  }
  .el-table__body-wrapper .el-scrollbar__bar {
    z-index: 8;
  }
  .el-table__body tr.current-row.hover-row>td.el-table__cell {
    background: var(--table-highlight-row-bg);
    filter: brightness(0.97);
  }
  .el-table__body tr.hover-row>td.el-table__cell {
    // background: var(--el-color-primary-light-9);
    filter: brightness(0.97) saturate(175%);
  }
 

  .el-table th.el-table__cell.is-sortable:hover {
    color: var(--el-color-primary);

    // .sort-caret.ascending {
    //   border-bottom-color: var(--el-color-primary);
    // }

    // .sort-caret.descending {
    //   border-top-color: var(--el-color-primary);
    // }
  }

  .el-table__body tr.current-row>td.el-table__cell {
    vertical-align: inherit;
    // padding: 5px 0;
    // border-top: 1px solid rgba(0, 0, 0, 0);
    // border-bottom: 1px solid rgba(0, 0, 0, 0);
    // background-color: lighten(var(--el-color-primary), 0.5);
    // &:first-child {
      // border-top-left-radius: 6px;
      // border-bottom-left-radius: 6px;
    // }
  }

    .el-table__body .row-filtered {
        background-color: #ddd !important;
        td.el-table__cell{
            background-color: #ddd !important;
        }
    }
 

  // 输入框前缀 
 
 
  .el-input--small {
    font-size: 13px;
  }

  .el-select,
  .el-date-editor.el-input {
    width: 100%;

    .el-input__wrapper {
      width: 100%;
      box-sizing: border-box;
    }
  }

  .el-button {
    font-size: 13px;
    padding: 6px 14px 6px 14px;
    // height: 28px;
    // line-height: 1.1;

    +.el-button {
      margin-left: 9px;
    }

    > span {
      line-height: 1;
      label {
        cursor: pointer;
      }
    }

    >.el-icon {
      font-size: 16px;
    }
    &.el-button--primary.is-link {
        border: none;
    }
    &.el-button--small {
      font-size: 12px;
      height: 24px;
    }
    &.el-button--large {
      font-size: 14px;
      padding: 12px 10px;
      height: auto;
    }

    &:focus-visible {
      outline: initial;
    }
  }

  // 数字输入框 
    
    .el-input-number  {
        .el-input__inner {
            text-align: left;
        }   
    }

  .el-table--scrollable-x {

    // 滚动条样式
    .el-scrollbar {
      .el-scrollbar__view .el-table__body {
        margin-bottom: 16px;
      }

      .el-scrollbar__bar {
        &.is-horizontal {
          height: 12px;
          border-radius: 30px;
        }
      }

      .el-table__empty-block {
        margin-top: -16px;
      }
    }
  }

  // 滚动条样式
  .el-scrollbar {
    .el-scrollbar__bar {
      &.is-vertical {
        width: 12px;
        border-radius: 30px;
      }
    }
  }

  .el-popconfirm__main {
    white-space: pre-line;
  }
}



.el-button-group {
  margin-left: 2px;
  margin-right: 2px;
}

.dialog-blue-header {
  .el-dialog__header {
    background-color: var(--el-color-info);
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;

    .el-dialog__title {
      color: #fff;
    }

    .el-icon.el-dialog__close {
      color: #fff;
    }
  }
}

.dialog-infusion-plan {
  .el-dialog__header {
    text-align: center;
  }

  .el-dialog__body {
    padding: 10px 20px;
  }
}

.el-message--info {
  // background: white;
}

// 所有分页样式
.pagination {
  // border-top: 1px solid #eee;
  // border-left: 1px solid #edf0f6;
  // border-right: 1px solid #edf0f6;
  // border-bottom: 1px solid #edf0f6;
  text-align: right; 
}

.el-pagination { 
//   background: var(--theme-card-bg-color);
  padding: 3px 10px;
  justify-content: right;

  // border-top: 1px solid #DBDBDB;
  &.is-background {

    .btn-prev,
    .btn-next,
    .el-pager li {
      height: 28px;
    }
  }

  .el-pagination__total {
    margin-right: 4px;
  }

  .el-pagination__sizes {
    flex: 0 0 auto;
    justify-content: flex-start;
  }

  .el-input {
    height: 28px;
  }

  span:not([class*=suffix]),
  .el-pagination button {
    font-size: 13px;
  }

  .el-select .el-input {
    width: 100px;
    font-size: 13px;
  }

  .el-input__wrapper {
    border-radius: 4px;
  }
}

button, input, optgroup, select, textarea {
  font-weight: 400;
}
 
html .container-menu .el-scrollbar .el-scrollbar__bar.is-vertical {
  width: 7px;
}

.el-menu .menu-icon {
  color: var(--theme-menu-text-color);
}

html .el-popover.el-popper {
  min-width: 180px;
}
.el-popover.el-popper.select-popover{
  padding: 0;
  .el-pager {
    display: flex;
    justify-content: center;
  }
}







// 基础样式



.iconfont {
  font-size: 13px;
}
.text-color-1 {
  /*绿色*/
  color: #00ab1b;
}

.text-color-2 {
  /*红色*/
  color: #d44a0b;
}
.text-color-3 {
  color: #e6a23c;
}
.panel-view {
  .input-search {
    // padding: 5px 0 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    // flex-wrap: wrap;
    .search-input {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      >.el-input,
      .el-select.el-select--mini {
        flex: 1;
        margin: 5px 10px 5px 0;
        // margin-bottom: 5px;
        min-width: 100px;
        max-width: 250px;
      }

      .el-select--mini {
        margin-right: 10px;
      }
    }

    .search-button {
      margin-right: 10px;
      min-width: 70px;
    }

    .el-date-editor.el-range-editor--mini {
      flex: 1;
      max-width: 340px;
      min-width: 200px;
      height: 30px;
      margin-right: 10px;
      line-height: 1;

      .el-range-separator {
        padding: 0px 1px;
      }
    }
  }

  .button-group {
    >div {
      display: inline-block;
      padding-right: 25px;
      margin-bottom: 5px;
    }

    .c-dropdown {
      margin-left: 10px;
      // .el-button{
      //     margin: 0px;
      // }
    }
  }

  .table-layout-fixed {
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-title {
      height: 40px;
      line-height: 40px;
      padding: 0px 10px;
    }

    .table-container {
      flex: 1;
      overflow: hidden;
    }
  }
}

.el-input__inner {
  font-weight: var(--el-input-font-weight);
  text-align: var(--el-input-text-align);
}

.el-table td.el-table__cell .cell .el-popper.el-color-picker__panel.el-color-dropdown {
  box-sizing: content-box;
  div {
    box-sizing: content-box;
  }
}



.c-template {
    height: 100%;
}

.v-contextmenu {
    z-index: 9999;
    .v-contextmenu-inner {
      padding: 0;
      margin: 0;
      list-style: none;
    }
    .custom-contextmenu-inner {
      padding: 5px 0;
      margin: 0;
      list-style: none; 
      .no-hover.v-contextmenu-item--hover.v-contextmenu-item {
        background: transparent;
        color: inherit;
      }
      .v-contextmenu-group__menus .v-contextmenu-item {
        display: block;
      }
      .context-text {
        position: relative;
        width: auto;
        max-width: max-content;
        height: 1.5em;
        line-height: 1.5;
        font-size: 14px;
        overflow-x: hidden;
        display: block;
        text-overflow: ellipsis;
      }
      
      .menu-group {
        max-height: 40vh;
        overflow-y: auto;
      }
    }

    .call-contextmenu-inner {
      margin: 0;
      list-style: none; 
      padding: 0 0 10px 0;
      background-color: #fbf5e9;//f5f6eb
      .el-button {
        width: 100%;
      } 
      .v-contextmenu-item--hover {
          background-color: transparent;
      }
      .name {
          min-width: 140px;
          padding: 10px;
          background-color: #f7e2c7;
          font-size: 18px;
          text-align: center;
          border-bottom: 2px dotted #aeaeae;
          margin-bottom: 10px;
      }
    }

}

.el-table .el-table__cell.tableColOverflowVisible {
    z-index: 2;
    overflow: visible;

    vertical-align: middle;

    .cell {
        overflow: visible;
        .c-assist {
            position: relative;
            width: 1px;
            height: 20px;
        }

        .row-drawer {
          position: relative;
          display: block;
          width: 3600px;
          box-sizing: border-box;
          margin: 32px 0 0px 0;
          padding: 0 0 0 1090px;
          left: -999px;
          top: 0;
          border-top: 1px solid rgba(0, 0, 0, 0.08);
          height: auto;
          text-align: left;
          background-color: var(--el-table-row-hover-bg-color);
          filter: brightness(0.97) saturate(125%);
 
          .flex-box {
            display: flex;
          }

          .avatar-box {
            display: block;
            width: 70px;
            height: 101px;
          }
          .drawer-right {
            display: block;
          }

            .g-content {
              display: block;
              position: relative;
              top: 0;
              justify-content: center;
              align-items: center;
              padding-top: 18px;
              padding-bottom: 18px;
              // left: 12px;
                .c-collect {
                  display: inline-block;
                  margin: 0 17px 0 3px;
                  vertical-align: bottom;
                }
                .el-button {
                  padding: 9px 23px;
                }
                
            }

            .step-box {
              position: relative;
              display: block;
              width: 1120px;
              left: -56px;
              top: 0;
              text-align: left;
              padding-left: 46px;
              padding-bottom: 0;
            }
        }
        
    }
}

// 图标样式
.icon-register {
    color: #3acebf;
}

.icon-green {
    color: #2cc964;
}

.icon-blue {
    color: #308fe8;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"]{
    -moz-appearance: textfield;
}


/* 旧全局样式 */

// 自定义表格边框样式
// .el-table--border,
// .el-table--group {
//     border: 1px solid #eee;
// }

// .el-table--border::after,
// .el-table--group::after,
// .el-table::before,
// .el-table__fixed-right::before,
// .el-table__fixed::before {
//     background-color: #eee;
// }

// .el-table--border {
//     border-right: none;
//     border-bottom: none;
// }

// .el-table td,
// .el-table th.is-leaf,
// .el-table--border th,
// .el-table__fixed-right-patch,
// .el-table--border td,
// .el-table--border th,
// .el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed {
//     border-color: #eee;
// }


// // 自定义文本域边框样式
// .el-textarea__inner {
//     border: 1px solid #dcdfe6;
// }
 
.el-drawer__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: hidden;
  margin: 0;

  :deep( :focus ) {
      outline: 0;
  }
}

.el-cascader {
  width: 100%;
}
 
.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}

.el-cascader-panel .el-radio__input {
  visibility: hidden;
}

.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}
 

/* 旧全局样式 end */


.el-col-0 {
  width: 0
}

.el-col-offset-0 {
  margin-left: 0
}

.el-col-pull-0 {
  right: 0
}

.el-col-push-0 {
  left: 0
}

.el-col-1 {
  width: 4.16667%
}

.el-col-offset-1 {
  margin-left: 4.16667%
}

.el-col-pull-1 {
  right: 4.16667%
}

.el-col-push-1 {
  left: 4.16667%
}

.el-col-2 {
  width: 8.33333%
}

.el-col-offset-2 {
  margin-left: 8.33333%
}

.el-col-pull-2 {
  right: 8.33333%
}

.el-col-push-2 {
  left: 8.33333%
}

.el-col-3 {
  width: 12.5%
}

.el-col-offset-3 {
  margin-left: 12.5%
}

.el-col-pull-3 {
  right: 12.5%
}

.el-col-push-3 {
  left: 12.5%
}

.el-col-4 {
  width: 16.66667%
}

.el-col-offset-4 {
  margin-left: 16.66667%
}

.el-col-pull-4 {
  right: 16.66667%
}

.el-col-push-4 {
  left: 16.66667%
}

.el-col-5 {
  width: 20.83333%
}

.el-col-offset-5 {
  margin-left: 20.83333%
}

.el-col-pull-5 {
  right: 20.83333%
}

.el-col-push-5 {
  left: 20.83333%
}

.el-col-6 {
  width: 25%
}

.el-col-offset-6 {
  margin-left: 25%
}

.el-col-pull-6 {
  right: 25%
}

.el-col-push-6 {
  left: 25%
}

.el-col-7 {
  width: 29.16667%
}

.el-col-offset-7 {
  margin-left: 29.16667%
}

.el-col-pull-7 {
  right: 29.16667%
}

.el-col-push-7 {
  left: 29.16667%
}

.el-col-8 {
  width: 33.33333%
}

.el-col-offset-8 {
  margin-left: 33.33333%
}

.el-col-pull-8 {
  right: 33.33333%
}

.el-col-push-8 {
  left: 33.33333%
}

.el-col-9 {
  width: 37.5%
}

.el-col-offset-9 {
  margin-left: 37.5%
}

.el-col-pull-9 {
  right: 37.5%
}

.el-col-push-9 {
  left: 37.5%
}

.el-col-10 {
  width: 41.66667%
}

.el-col-offset-10 {
  margin-left: 41.66667%
}

.el-col-pull-10 {
  right: 41.66667%
}

.el-col-push-10 {
  left: 41.66667%
}

.el-col-11 {
  width: 45.83333%
}

.el-col-offset-11 {
  margin-left: 45.83333%
}

.el-col-pull-11 {
  right: 45.83333%
}

.el-col-push-11 {
  left: 45.83333%
}

.el-col-12 {
  width: 50%
}

.el-col-offset-12 {
  margin-left: 50%
}

.el-col-pull-12 {
  position: relative;
  right: 50%
}

.el-col-push-12 {
  left: 50%
}

.el-col-13 {
  width: 54.16667%
}

.el-col-offset-13 {
  margin-left: 54.16667%
}

.el-col-pull-13 {
  right: 54.16667%
}

.el-col-push-13 {
  left: 54.16667%
}

.el-col-14 {
  width: 58.33333%
}

.el-col-offset-14 {
  margin-left: 58.33333%
}

.el-col-pull-14 {
  right: 58.33333%
}

.el-col-push-14 {
  left: 58.33333%
}

.el-col-15 {
  width: 62.5%
}

.el-col-offset-15 {
  margin-left: 62.5%
}

.el-col-pull-15 {
  right: 62.5%
}

.el-col-push-15 {
  left: 62.5%
}

.el-col-16 {
  width: 66.66667%
}

.el-col-offset-16 {
  margin-left: 66.66667%
}

.el-col-pull-16 {
  right: 66.66667%
}

.el-col-push-16 {
  left: 66.66667%
}

.el-col-17 {
  width: 70.83333%
}

.el-col-offset-17 {
  margin-left: 70.83333%
}

.el-col-pull-17 {
  right: 70.83333%
}

.el-col-push-17 {
  left: 70.83333%
}

.el-col-18 {
  width: 75%
}

.el-col-offset-18 {
  margin-left: 75%
}

.el-col-pull-18 {
  right: 75%
}

.el-col-push-18 {
  left: 75%
}

.el-col-19 {
  width: 79.16667%
}

.el-col-offset-19 {
  margin-left: 79.16667%
}

.el-col-pull-19 {
  right: 79.16667%
}

.el-col-push-19 {
  left: 79.16667%
}

.el-col-20 {
  width: 83.33333%
}

.el-col-offset-20 {
  margin-left: 83.33333%
}

.el-col-pull-20 {
  right: 83.33333%
}

.el-col-push-20 {
  left: 83.33333%
}

.el-col-21 {
  width: 87.5%
}

.el-col-offset-21 {
  margin-left: 87.5%
}

.el-col-pull-21 {
  right: 87.5%
}

.el-col-push-21 {
  left: 87.5%
}

.el-col-22 {
  width: 91.66667%
}

.el-col-offset-22 {
  margin-left: 91.66667%
}

.el-col-pull-22 {
  right: 91.66667%
}

.el-col-push-22 {
  left: 91.66667%
}

.el-col-23 {
  width: 95.83333%
}

.el-col-offset-23 {
  margin-left: 95.83333%
}

.el-col-pull-23 {
  right: 95.83333%
}

.el-col-push-23 {
  left: 95.83333%
}

.el-col-24 {
  width: 100%
}

.el-col-offset-24 {
  margin-left: 100%
}

.el-col-pull-24 {
  right: 100%
}

.el-col-push-24 {
  left: 100%
}
.el-message {
  padding: 14px 16px !important;
  background: #fff !important;
  border-width: 0 !important;
  box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014,
  0 9px 28px 8px #0000000d !important;
  
  &.el-message.is-closable .el-message__content {
    padding-right: 17px !important;
  }
  
  & .el-message__content {
    font-size: 16px;
    color: #000000d9 !important;
    pointer-events: all !important;
    background-image: initial !important;
  }

  & .el-message__icon {
    font-size: 20px;
    margin-right: 8px !important;
  }

  & .el-message__closeBtn {
    right: 9px !important;
    border-radius: 4px;
    outline: none;
    transition: background-color 0.2s, color 0.2s;

    &:hover {
      background-color: rgb(0 0 0 / 6%);
    }
  }
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: var(--table-highlight-row-bg);
}

.el-form {
    .el-input.el-date-editor {
        height: 100% !important; /* 或具体的高度值 */  
    }  
}