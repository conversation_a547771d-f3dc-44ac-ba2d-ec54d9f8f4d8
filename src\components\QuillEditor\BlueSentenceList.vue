<template>
  <el-dialog append-to-body align-center title="书签设置" v-model="visible" width="570px" :close-on-click-modal="false"
    destroy-on-close @close="closeDialog" class=" ">
    <div class="g-content">

      <el-table :data="StructBookmarkList" height="500" border stripe>
        <el-table-column label="书签名称">
          <template v-slot="scope"> {{ scope.row.sBookmarkName }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="210">
          <template v-slot:header="scope">
            <el-button-icon-fa title="新增" link size="small" icon="fa fa-plus" @click="showEditDialog">
              新增
            </el-button-icon-fa>

          </template>
          <template v-slot="scope">
            <el-button-icon-fa link title="编辑" icon="el-icon-edit" size="small"
              @click="showEditDialog(scope.row, scope.$index)">编辑</el-button-icon-fa>
            <el-button-icon-fa :loading="isSaveLoading" link title="删除" icon="el-icon-delete" size="small"
              @click="delData(scope.row, scope.$index)">删除</el-button-icon-fa>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button-icon-fa icon="el-icon-close" @click="closeDialog">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
  <el-dialog append-to-body align-center title="书签" v-model="isDialogShow" width="430px" :close-on-click-modal="false"
    destroy-on-close @close="isDialogShow = false" class=" ">
    <div class="g-content">
      <FormItem label="书签名称" labelWidth="80px">
        <el-input v-model="editForm.sBookmarkName"></el-input>
      </FormItem>
    </div>
    <div class="g-content">
      <div class="" style="float: right; width: 150px;">
        <el-popover v-model:visible="isPopoverShow" trigger="manual" placement="bottom" width="300">
          <div class="">
            <el-input v-model="inputVal" :rows="2" type="textarea" placeholder="请输入内容"></el-input>
          </div>
          <div style="margin-top: 10px;">
            <el-button @click="onClickAddOption">确定</el-button>
            <el-button @click="() => { inputVal = ''; isPopoverShow = false; }">取消</el-button>
          </div>
          <template #reference>
            <span></span>
          </template>
        </el-popover>
      </div>
      <el-table :data="optionTableList" height="400" border stripe>
        <el-table-column label="选项内容">
          <template v-slot="scope">
            <el-input v-if="editRowIndexMap[scope.$index]" v-model="editRowIndexMap[scope.$index]" />
            <span v-else>{{ scope.row.text }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="160">
          <template v-slot:header="scope">
            <el-button-icon-fa title="新增" link size="small" icon="fa fa-plus" @click="isPopoverShow = true">
              新增
            </el-button-icon-fa>

          </template>
          <template v-slot="scope">
            <el-button-icon-fa v-if="!editRowIndexMap[scope.$index]" link title="编辑" icon="el-icon-edit" size="small"
              @click="handleEditOption(scope.row, scope.$index)">编辑</el-button-icon-fa>
            <el-button-icon-fa v-if="editRowIndexMap[scope.$index]" :loading="isSaveLoading" link title="保存"
              icon="fa fa-save" size="small" @click="handleSaveOption(scope.row, scope.$index)">保存</el-button-icon-fa>
            <el-button-icon-fa :loading="isSaveLoading" link title="删除" icon="el-icon-delete" size="small"
              @click="delOption(scope.row, scope.$index)">删除</el-button-icon-fa>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <el-button-icon-fa type="primary" icon="el-icon-document-checked" @click="handleSaveTable">保存</el-button-icon-fa>
      <el-button-icon-fa icon="el-icon-close" @click="isDialogShow = false">关闭</el-button-icon-fa>
    </template>
  </el-dialog>
</template>
<script>

import Api from '$supersetApi/projects/apricot/case/report.js'
export default {
  name: 'BlueSentence',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      isSaveLoading: false,
      isPopoverShow: false,
      isDialogShow: false,
      inputVal: '',

      editRowIndexMap: {},
      optionTableList: [],
      editForm: {
        sBookmarkName: '',
        sBookmarkId: '',
        sOptionListJson: ''
      },
    }
  },
  watch: {
    dialogVisible() {
      this.visible = this.dialogVisible;
      if (this.visible) {
        this.getData()
      }
    }
  },
  computed: {
    StructBookmarkList() {
      return this.$store.getters['apricot/report_module/StructBookmark']
    }
  },
  mounted() {
  },
  methods: {
    handleEditOption(row, index) {
      this.editRowIndexMap[index] = row.text
    },
    handleSaveOption(row, index) {
      // this.editRowIndexMap[index] = row.text
      row.text = this.editRowIndexMap[index]
      this.editRowIndexMap[index] = false

    },
    delOption(row, index) {

      var title = row.text ? row.text : ''
      this.$confirm(`确定要删除【${title}】吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: "warning"
      }).then(() => {
        this.optionTableList.splice(index, 1)
      })
    },
    delData(row, index) {
      var title = row.sBookmarkName ? row.sBookmarkName : ''
      this.$confirm(`确定要删除【${title}】吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: "warning"
      }).then(() => {
        Api.deleteBookmark({ sBookmarkId: row.sBookmarkId }).then(res => {
          if (res.success) {
            this.$message.success(res.msg)
            this.getData()
            return
          }
          this.$message.error(res.msg)
        }).catch((err) => {
          console.log(err)
        })
      })

    }, 
    handleSaveTable() {
      this.isSaveLoading = true
      const sOptionListJson = JSON.stringify(this.optionTableList)

      const params = {
        sBookmarkId: this.editForm.sBookmarkId,
        sBookmarkName: this.editForm.sBookmarkName,
        sOptionListJson
      }
      const send = params.sBookmarkId ? Api.editBookmark : Api.createBookmark
      send(params).then(res => {
        this.isSaveLoading = false
        if (res.success) {
          this.isDialogShow = false

          this.$message.success(res.msg)
          this.getData()
          return
        }
        this.$message.error(res.msg)
      }).catch((err) => {
        this.isSaveLoading = false
        this.isDialogShow = false
        console.log(err)
      })


    },
    showEditDialog(item) {
      if (item && item.sBookmarkId) {
        this.editForm.sBookmarkId = item.sBookmarkId
        this.editForm.sBookmarkName = item.sBookmarkName
        let optionTableList = []
        try {
          optionTableList = JSON.parse(item.sOptionListJson)
        } catch (error) {
          console.log(error)

        }
        this.optionTableList = optionTableList
      } else {
        this.editForm.sBookmarkId = ''
        this.editForm.sBookmarkName = ''
        this.optionTableList = []
      }

      for (const key in this.editRowIndexMap ) {
        this.editRowIndexMap[key] = false
      }
      
      this.inputVal = ''

      this.isDialogShow = true
    }, 

    onClickAddOption() {
      if (!(this.inputVal)) {
        this.$message.warning('请填写内容！')
        return
      }
      this.optionTableList.push({ text: this.inputVal })
      this.inputVal = ''
      this.isPopoverShow = false
    },
    closeDialog() {
      this.$emit('update:dialogVisible', false);
    },
    getData() {
      this.$store.dispatch('apricot/report_module/loadStructBookmark', true).then(list => {
         
      })

    },
  }
}
</script>
<style lang="scss" scoped></style>
