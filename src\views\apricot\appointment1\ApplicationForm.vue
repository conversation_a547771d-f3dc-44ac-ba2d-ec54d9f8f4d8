<template>
    <div class="container-top">
        <div class="search-box">
            <SearchList 
                v-model:modelValue="condition" 
                v-model:list="searchList" 
                :optionData="optionsLoc" 
                :iModuleId="iModuleId"
                storageKey="appointment1-applicationForm" 
                @clickSearch="onQueryApply"
                @changeSearch="isAutomaticLoadApply && onQueryApply()"
                @reset="mxOnClickReset">
                <template v-slot:beginOrderTime>
                    <el-date-picker v-model="condition.beginOrderTime"
                        type="date"
                        align="right"
                        unlink-panels
                        range-separator="至"
                        start-placeholder=""
                        end-placeholder=""
                        value-format="YYYY-MM-DD"
                        :disabled-date="disabledDateBegin"
                        :shortcuts="shortcuts"
                        @change="isAutomaticLoadApply && onQueryApply()">
                    </el-date-picker>
                </template>
                <template v-slot:endOrderTime>
                    <el-date-picker v-model="condition.endOrderTime"
                        type="date"
                        align="right"
                        unlink-panels
                        range-separator="至"
                        start-placeholder=""
                        end-placeholder=""
                        value-format="YYYY-MM-DD"
                        :disabled-date="disabledDateEnd"
                        :shortcuts="shortcuts"
                        @change="isAutomaticLoadApply && onQueryApply()">
                    </el-date-picker>
                </template>
                <template v-slot:execState>
                    <el-select v-model="condition.execState" placeholder="" clearable @change="isAutomaticLoadApply && onSelectCondition('execState')">
                        <el-option v-for="item in optionsLoc.execStateOptions" :key="item.sValue" :value="item.sValue"
                        :label="item.sName">
                        </el-option>
                    </el-select>
                </template>
                <template v-slot:iAppointState>
                    <el-select v-model="condition.iAppointState" placeholder="" clearable @change="isAutomaticLoadApply && onSelectCondition('iAppointState')">
                        <el-option v-for="item in optionsLoc.iAppointStateOptions" :key="item.sValue" :value="item.sValue"
                        :label="item.sName">
                        </el-option>
                    </el-select>
                </template>
                <template v-slot:sDeviceTypeCode>
                    <el-select v-model="condition.sDeviceTypeCode" placeholder="" clearable @change="isAutomaticLoadApply && onQueryApply()">
                        <el-option v-for="item in deviceTypeOption" :key="item.sId" :value="item.sDeviceTypeCode"
                            :label="item.sDeviceTypeName">
                        </el-option>
                    </el-select>
                </template>
            </SearchList>
            <div class="button-group">
                <el-button @click="mxOnClickReset">
                    <template #icon><Icon name="el-icon-refresh-right"></Icon></template>重置
                </el-button>
                <el-button v-auth="'report:appoint:applyQuery'" type="primary" @click="onQueryApply"
                    :loading="loading">
                    <template #icon><Icon name="el-icon-search"></Icon></template>查询
                </el-button>
            </div>
        </div>
        <el-table-extend
            :data="applyTableData"
            ref="mainTable"
            highlight-current-row
            border
            height="100%"
            @row-dblclick="onRowClick"
            v-loading="loading"
            :iModuleId="iModuleId"
            storageKey="appointment1-ApplicationTable"
            class="apply-table">
            <el-table-column type="index" label="序号" prop="_index" align="center" width="60" fixed
                class-name="tableColOverflowVisible"></el-table-column>
            <el-table-column 
                v-for="item in tableList"
                :key="item.sProp"
                :prop="item.sProp" 
                :sortable="(!!item.iSort) ? 'custom' : defaultItem.iSort"
                :isSortable="item.isSortable" 
                :sOrder="item.sOrder"
                :column-key="item.sSortField ? item.sSortField : null"
                :width="item.sWidth || defaultItem.sWidth"
                :min-width="item.sMinWidth || defaultItem.sMinWidth"
                :show-overflow-tooltip="item.showOverflowTooltip != undefined ? item.showOverflowTooltip : defaultItem.showOverflowTooltip"  
                :label="item.sLabel"
                :align="item.sAlign || defaultItem.sAlign"
                :fixed="item.sFixed"
                :formatter="item.formatter || defaultItem.formatter">
                    <template v-slot="scope" v-if="['iAge'].includes(item.sProp)">
                        {{ scope.row[item.sProp] }}{{ setAgeUnitName(scope.row.sAgeUnit)}}
                    </template>
                    <template v-slot="scope" v-else-if="['sAppointStateName'].includes(item.sProp)">
                        <span :class="{ 'icon-green font-bold':  scope.row.iAppointState}">{{ scope.row[item.sProp] }}</span>
                    </template>
                    <template v-slot="scope" v-else-if="['iExecState'].includes(item.sProp)">
                        {{ execStateTxt[scope.row[item.sProp]] }}
                    </template>
                    <template v-slot="scope" v-else-if="!item.formatter && !defaultItem.formatter">
                        {{ scope.row[`${item.sProp}`] }}
                    </template>
            </el-table-column>
        </el-table-extend>

        <ApplicationRelation v-model="visibleRelation" :selectedItem="selectedItem" @onQueryApply="onQueryApply"></ApplicationRelation>
    </div>
</template>
<script setup>
    import { queryApply } from '$supersetApi/projects/apricot/appointment/index.js'
    import { getDeviceTypeData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
    import { ElMessage } from 'element-plus'
    import { deepClone } from '$supersetUtils/function'
    import { transformDate, getOptionName } from '$supersetResource/js/tools'

    import useUserConfig from './useUserConfig.js'

    import { appointmentEnum } from '$supersetResource/js/projects/apricot/enum.js'
    import ConfigsItems from './configs/configsItems.js'

    import scannerKeyCodeEvent from '$supersetResource/js/projects/apricot/scannerKeyCodeEvent.js';

    import ApplicationRelation from './components/ApplicationRelation.vue'

    import { useRouter } from 'vue-router'

    const iModuleId = ref(2)


    // 用户配置
    const { configData } = useUserConfig();
    
    const isAutomaticLoadApply = computed(() => configData.value?.isAutomaticLoadApply ?? true);

    const searchList = ref(ConfigsItems.searchConfig)
    const optionsLoc = {
        visitTypeOptions: appointmentEnum.visitTypeOptions,
        execStateOptions: appointmentEnum.execStateOptions,
        iAppointStateOptions:[
            { sName: '全部', sValue: '' },
            { sName: '未预约', sValue: 0 },
            { sName: '已预约', sValue: 1 },
        ]
    }
    optionsLoc.execStateOptions.unshift({ sName: '全部', sValue: '' })

    const execStateTxt = ref({
        '-1': '已撤销申请',
        '0': '未登记',
        '1': '已登记',
        '-': '-'
    })


    let defaultCondition = {}
    let condition = reactive({})
    const stopWatch = watchEffect(() => {
        if (configData.value.applyTimeDays) {
            const start = new Date()
            const applySearchValues = getCacheSearchValues()
            defaultCondition = { 
                execState: applySearchValues.execState !== undefined ? applySearchValues.execState : '', 
                iAppointState: applySearchValues.iAppointState !== undefined ? applySearchValues.iAppointState : '', 
                beginOrderTime: transformDate(start.setTime(start.getTime() - 3600 * 1000 * 24 * 14)),
                endOrderTime: transformDate(new Date()),
                machineryRoomText: applySearchValues.machineryRoomText
            }
            if(configData.value.applyTimeDays != undefined && configData.value.applyTimeDays != null) {

                const start = new Date()
                let day = Number(configData.value.applyTimeDays) - 1
                if (day === -1) {
                    defaultCondition.beginOrderTime = null
                }else {
                    defaultCondition.beginOrderTime = transformDate(start.setTime(start.getTime() - (3600 * 1000 * 24 * day) ))
                }
                defaultCondition.endOrderTime = configData.value.applyTimeDays == 0 ? null : transformDate(new Date())
            }

            for (const key in defaultCondition) {
                if (Object.hasOwnProperty.call(defaultCondition, key)) {
                    if (defaultCondition[key] != undefined) {
                        condition[key] = defaultCondition[key]
                    }else {
                        condition[key] = null
                    }
                }
            }
            // onQueryApply()
            stopWatch()
        }
    })

    const setAgeUnitName = (val)  => {
        val = val || '';
        let name = getOptionName(val, appointmentEnum.ageUnitOptions);
        return name || val;
    }

    // 点击重置条件
    const mxOnClickReset = () => {
        for (const key in condition) {
            if (Object.hasOwnProperty.call(condition, key)) {
                if (defaultCondition[key] != undefined) {
                    condition[key] = defaultCondition[key]
                }else {
                    condition[key] = null
                }
            }
        }
        onSelectCondition('execState')
        onSelectCondition('iAppointState')

        onQueryApply()
    }

    const defaultItem = { 
        iIsHide: true,
        iSort: false,
        sWidth: null,
        showOverflowTooltip: true,
        sAlign: 'center',
        sMinWidth: 140,
        formatter: null
    }

    const shortcuts = [{
        text: '当天',
        onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime());
            
            picker.emit("pick");
            condition.beginOrderTime = transformDate(start)
            condition.endOrderTime = transformDate(end)
        }
    },
    {
        text: '近两天',
        onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
            
            picker.emit("pick");
            condition.beginOrderTime = transformDate(start)
            condition.endOrderTime = transformDate(end)
        }
    },
    {
        text: '最近一周',
        onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
            
            picker.emit("pick");
            condition.beginOrderTime = transformDate(start)
            condition.endOrderTime = transformDate(end)
        }
    },
    {
        text: '最近两周',
        onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 13);

            picker.emit("pick");
            condition.beginOrderTime = transformDate(start)
            condition.endOrderTime = transformDate(end)
        }
    }, {
        text: '最近一个月',
        onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            
            picker.emit("pick");
            condition.beginOrderTime = transformDate(start)
            condition.endOrderTime = transformDate(end)
        }
    }, {
        text: '最近三个月',
        onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            
            picker.emit("pick");
            condition.beginOrderTime = transformDate(start)
            condition.endOrderTime = transformDate(end)
        }
    }]

    const disabledDateBegin = (time) => {
        // console.log(time.getTime(), new Date(condition.endOrderTime).getTime())
        return time.getTime() > new Date(condition.endOrderTime).getTime()
    }
    const disabledDateEnd = (time) => {
        // console.log(time.getTime(), new Date(condition.endOrderTime).getTime())
        return time.getTime() + (3600 * 1000 * 24) < new Date(condition.beginOrderTime).getTime()
    }

    const onSelectCondition = (key) => { 
        // console.log(key, condition[key])
        setCacheSearchValues(key, condition[key])
        onQueryApply()
    }

    // 设置缓存
    function setCacheSearchValues (key, value) {
        let applySearchValues = localStorage.getItem('applySearchValues')
        applySearchValues = applySearchValues ? JSON.parse(applySearchValues) : {}
        applySearchValues[key] = value
        localStorage.setItem('applySearchValues', JSON.stringify(applySearchValues)) 
    }
    
    // 获取查询条件默认值
    function getCacheSearchValues () {
        let applySearchValues = localStorage.getItem('applySearchValues')
        applySearchValues = applySearchValues ? JSON.parse(applySearchValues) : {}
        return applySearchValues
    }

    const allTableData   = ref([]) // 申请单所有数据
    const applyTableData = ref([]) // 申请单显示的数据
    const loading = ref(false)
    const tableList = ref(ConfigsItems.applyTableConfig)  // 表格配置

    // 查询数据
    const onQueryApply = async () => {

        try {
            const cacheCondition = getCacheSearchValues();
            if(cacheCondition.machineryRoomText !== condition.machineryRoomText) {
                const key = 'machineryRoomText'
                setCacheSearchValues(key, condition[key])
            }

            const params = deepClone(condition);

            delete params.iAppointState;

            Object.keys(params).map(keyName => {
                if(['', undefined, null].includes(params[keyName])){
                    delete params[keyName]
                }
            })

            loading.value = true;
            
            const res = await queryApply(params)
            loading.value = false

            if (!res || !res.success) {
                ElMessage.error(res.msg)
                return
            }
            allTableData.value = deepClone(res.data)
            // 根据查询条件过滤相对应的数据
            if (condition.iAppointState === 0) {
                applyTableData.value = res.data.filter(item => item.iAppointState !== 1)
            } else if(condition.iAppointState === 1) {
                applyTableData.value = res.data.filter(item => item.iAppointState === 1)
            } else {
                applyTableData.value = res.data
            }
            // 自动选择申请单
            setTimeout(() => {
                if (configData.value.isLoadApplyFirst && applyTableData.value && applyTableData.value.length == 1) {
                    onRowClick(applyTableData.value[0])
                }
            }, 300);
        } catch (error) {
            loading.value = false
        }
    }

    const doRefreshQueryApply = (sPatientInfoId) => {
        if (!sPatientInfoId) {
            allTableData.value.length && onQueryApply()
            return
        }
        // allTableData 根本没有 sPatientInfoId
        // let target = allTableData.value.find(item => item.sPatientInfoId === sPatientInfoId);
        // target && onQueryApply()

        onQueryApply()
    }

    const emits = defineEmits(['selectDataItem'])

    const selectedItem = ref({})
    const visibleRelation = ref(false)
    // 行点击事件
    const onRowClick = (item) => {
        if (!item.sProjectId) {
            selectedItem.value = deepClone(item)
            visibleRelation.value = true
            return
        }
        emits('selectDataItem', item)
    }
    onMounted(async () => {
        getDeviceTypeDataOptions();
    })

    const deviceTypeOption = ref([]);
    const getDeviceTypeDataOptions = () => {
        getDeviceTypeData({iIsEnable: 1}).then(res => {
            if (res.success) {
                deviceTypeOption.value = res?.data || [];
                return
            }
            this.$success.error(res.msg);
        })
    }
    
    // 设备扫描
    const router = useRouter()

    scannerKeyCodeEvent.onMachineEvent = (res => {
        if (router.currentRoute.value.name === 'apricot_Appointment1') {
            // this.scannerSearch(res);
            queryApply({ applyNo: res }).then(res => {
                if (res.data && res.data[0]) {
                    emits('selectDataItem', res.data[0])
                }
            })
        }
    })

    defineExpose({
        doRefreshQueryApply
    })
</script>
<style lang="scss" scoped>
    .container-top {
        display: flex;
        flex-direction: column;
        background: white;
        height: 100%;
        // padding: 1px;
        box-sizing: border-box;
    }
    .search-box {
        display: flex;
        justify-content: center;
        // padding: 8px 0px;
        .container {
            flex: 1;
            overflow: hidden;
        }
        .button-group {
            width: 175px;
            padding-top: 14px;
            box-sizing: border-box;
        }
    }

    .apply-table {
      :deep(.el-table__body-wrapper tr td.el-table-fixed-column--left) {
        z-index: 2;
      }
    }
</style>
