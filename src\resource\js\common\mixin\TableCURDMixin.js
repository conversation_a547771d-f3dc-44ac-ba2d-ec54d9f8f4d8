import { ElMessageBox, ElMessage } from 'element-plus';
import { typeOf, deepClone } from '@/utils/function'
export default {
    data() {
        return {
            loading: false,
            tableData: [],              // 表格数据
            actionState: 1,             // 1 新增 2编辑 3 查看
            page: {                     // 分页	
                pageCurrent: 1,
                pageSize: 20,
                total: 0
            },
            mxPageSizes: [10, 20, 30, 40, 60],
            condition: {},              // 查询参数-调用页面可覆盖
            editLayer: {                // 新增-编辑-查看
                loading: false,         // 加载
                visible: false,         // 是否显示弹窗
                playerText: '',         // 弹窗标题文字
                form: {},
                selectedItem: {},       // 选中表格行数据
                look: false,
            },
            mxPickerOptions: {
                shortcuts: [
                    {
                        text: '最近三天',
                        onClick(picker){
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一周',
                        onClick(picker){
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近二周',
                        onClick(picker){
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 14);
                            picker.$emit('pick', [start, end])
                        }
                    },
                    {
                        text: '最近一个月',
                        onClick(picker){
                            const end = new Date();
                            const start = new Date();
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
                            picker.$emit('pick', [start, end])
                        }
                    }
                ]
            },
            rules: {},
            sort: '',
        }
    },
    filters: {
        // 转换时间
        mxToDate: function(t, status){
            let dd = new Date(t);
            let y = dd.getFullYear();
            if (isNaN(y) || t == null){
                return;
            }
            let m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
            let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
            if (status){
                let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
                let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
                let s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
                return y + '-' + m + '-' + d + ' ' + h + ':' + mm + ':' + s;
            }
            return y + '-' + m + '-' + d;
        }
    },
    methods: {
        // 转换 结束 开始 特殊日期格式。
        mxTransformDate(t) {
            let ddd = t;
            if (t.__proto__.constructor !== Date){
             ddd = new Date(ddd);
            }
            let dd = new Date(ddd.getTime());
            let y = dd.getFullYear();
            let m = dd.getMonth() + 1 < 10 ? "0" + (dd.getMonth() + 1) : dd.getMonth() + 1;
            let d = dd.getDate() < 10 ? "0" + dd.getDate() : dd.getDate();
            return y + "-" + m + "-" + d;
        },
        //查询
        mxDoSearch() {
            this.editLayer.selectedItem = {};
            this.page.pageCurrent = 1;
            this.mxGetTableList();
        },
        // 刷新
        mxDoRefresh(){
            this.mxGetTableList();
            this.mxSetSelected();
        },
        /**
         * 重置
         */
        mxOnClickReset(callback) {
            for (const key in this.condition) {
                if (this.condition.hasOwnProperty(key)) {
                    this.condition[key] = ''
                }
            }
            this.mxDoSearch()
            // 回调
            if (callback && toString.call(callback).slice(8, -1) === 'Function'){
                callback()
            }

        },
        // 翻页
        onSizeChange(val) {
            this.page.pageSize = val
            this.mxGetTableList();
            this.editLayer.selectedItem = {};
        },
        // 切页
        onCurrentChange(val) {
            this.page.pageCurrent = val
            this.mxGetTableList();
            this.editLayer.selectedItem = {};
        },
        // 点击行
        onClickRow(row) {
            this.editLayer.selectedItem = row;
        },
        /**
         * 点击行的新增、编辑、查看按钮
         * @param {*} row      点击的数据
         * @param {*} status   1、新增，2、编辑，3、复制，4、查看
         * @param {*} title    打开的框标题
         * @param {*} callBack 回调
         */
        mxOnClickRowAction(row, status, title, callBack) {
            this.editLayer.selectedItem = row;
            this.mxOpenDialog(status, title, callBack);
        },
        /**
         * 点击行删除按钮
         * @param {*} row 
         * @param {*} title 
         * @param {*} callBack 
         */
        mxOnClickRowDel(row, title, callBack) {
            this.editLayer.selectedItem = row;
            this.mxOnClickDel(title, callBack);
        },
        mxOnClickDel(title, callBack){
            if(!Object.keys(this.editLayer.selectedItem).length) {
                ElMessage.error('请选择某一行');
                return;
            }

            ElMessageBox.confirm(title, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                this.callBackDel(this.editLayer.selectedItem)  // axios 方法，在调用页面
            }).catch((e) => {        
                console.log(e)
            });
            callBack && callBack(this.editLayer.form)
        },
        /**
         * 打开弹窗
         */
        mxOpenDialog(status, title, editCallBack) {
            let formName = 'refEditLayer'
            this.editLayer.look = false
            this.editLayer.visible = false
            this.actionState = status == 2 ? 2 : 1
            if (status == 1){
                // 新增
                this.editLayer.playerText = title

                // 清空值
                for (const key in this.editLayer.form) {
                    if (this.editLayer.form.hasOwnProperty(key)) {
                        switch (typeOf(this.editLayer.form[key])) {
                            case 'Array':
                                this.editLayer.form[key] = []
                                break;
                            case 'Object':
                                this.editLayer.form[key] = {}
                                break;
                            default:
                                this.editLayer.form[key] = '';
                                break;
                        }
                        
                    }
                }
                // 子类是否需要添加其它值
                if (this.defualtVal != undefined){

                    for (const key in this.defualtVal.editLayer) {
                        if (this.defualtVal.editLayer.hasOwnProperty(key)) {
                            this.editLayer.form[key] = typeOf(this.defualtVal.editLayer[key]) == 'Array' || 
                                typeOf(this.defualtVal.editLayer[key]) == 'Object' 
                                ? deepClone(this.defualtVal.editLayer[key]) : this.defualtVal.editLayer[key]
                        }
                    }
                }
                for (const key in this.editLayer.form) {
                    if (this.editLayer.form.hasOwnProperty(key) && this.editLayer.form[key] === '') {
                        delete this.editLayer.form[key]
                    }
                }
                this.editLayer.visible = true;
            }else if (status == 2){
                // 编辑
                if(!Object.keys(this.editLayer.selectedItem).length) {
                    ElMessage.error('请选择某一行');
                    return;
                }
                this.editLayer.playerText = title
                this.editLayer.form = Object.assign({}, this.editLayer.selectedItem) 
                this.editLayer.visible = true;
            }else if (status == 3) {
                // 复制
                if(!Object.keys(this.editLayer.selectedItem).length) {
                    ElMessage.error('请选择某一行');
                    return;
                }
                this.editLayer.playerText = title
                this.editLayer.form = Object.assign({}, this.editLayer.selectedItem) 
                delete this.editLayer.form.sId
                this.editLayer.visible = true;
            } else{
                this.actionState = 3;
                // 查看
                if(!Object.keys(this.editLayer.selectedItem).length) {
                    ElMessage.error('请选择某一行');
                    return;
                }
                this.editLayer.playerText = title
                this.editLayer.form = Object.assign({}, this.editLayer.selectedItem) 
                this.editLayer.look = true
            }
            editCallBack && editCallBack(this.editLayer.form) // 回调 用于不同操作
            this.$nextTick(() => {
                if (formName != undefined && this.$refs[formName] != undefined){
                    this.$refs[formName].clearValidate();
                }
            })
        },
        /**
         * 验证数据
         */
        mxDoSaveData(formName) {
            this.editLayer.loading = true; 
            this.$refs[formName].validate((valid) => {
                if(!valid) {
                    ElMessage.info('填写正确信息');
                    this.editLayer.loading = false; 
                    return false;
                }
                // 验证成功
                this.callBackSave(this.editLayer.form) // axios 方法，在调用页面
            })
        },

        /**
         * 获取表格数据
         */
        mxGetTableList() {
            this.loading = true;
            let params = {
                pageNum: this.page.pageCurrent,
                pageSize: this.page.pageSize
            };
            // 去除空格
            for (const key in this.condition) {
                if (this.condition.hasOwnProperty(key)) {
                    const element = this.condition[key];
                    if (typeof element === 'string'){
                        this.condition[key] = element.trim()
                    }
                }
            }

            params = Object.assign({}, params, {condition: this.condition, sort: this.sort})
            this.callBackPageList(params); // axios 方法，在调用页面
        },
        // 赋值选中状态，还原之前选中状态
        mxSetSelected(){
            this.$nextTick(() => {
                if(this.$refs.mainTable){

                    // 有些主键是 sId,iId
                    let sId = this.editLayer.selectedItem.sId;
                    let idx = 0;   // 通过id找到某行数据
                    if (sId == undefined){
                        let iId = this.editLayer.selectedItem.iId;
                        if (iId != undefined){
                            this.tableData.forEach((item, index) => {
                                if (item.iId == iId){
                                    idx = index
                                }
                            })
                        }

                    }else {
                        this.tableData.forEach((item, index) => {
                            if (item.sId == sId){
                                idx = index
                            }
                        })
                    }
                    // 赋值选中
                    this.editLayer.selectedItem = Object.assign({}, this.tableData[idx]);
                    this.$refs.mainTable.setCurrentRow(this.tableData[idx]);
                }
            })
        },
    },
    mounted() {
        // 不首次加载
        if (!this.notFirstLoad) {
            this.mxGetTableList();  // 获取表格数据
        }
    },
}
