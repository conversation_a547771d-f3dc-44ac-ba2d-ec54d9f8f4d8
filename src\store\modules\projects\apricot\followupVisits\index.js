
const state = {
  patientInfo: {},
  currentModule: '',
}

const getters = {
  
}

const mutations  = {
  setPatientInfo(state, payload){
     state.patientInfo = payload.patientInfo
  },
  setCurrentModule(state, payload){
    state.currentModule = payload.name
  }
}

const actions = {
  actions1(){
  	
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
  modules: {
  }
}