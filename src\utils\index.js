/**
 * <AUTHOR> @description 常用公共工具函数
 */
import { queryByKey, saveConfig, queryGlobalConfigByModuleIdAndKey } from '@/api/userConfig'


import mitt from 'mitt'
const eventbus = mitt()


export const getEventbus = () => {
  return eventbus;
};

/**
 * @description 绑定事件 on(element, event, handler)
 */
export const on = (function () {
  if (document.addEventListener) {
    return function (element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false)
      }
    }
  } else {
    return function (element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler)
      }
    }
  }
})()

/**
* @description 解绑事件 off(element, event, handler)
*/
export const off = (function () {
  if (document.removeEventListener) {
    return function (element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false)
      }
    }
  } else {
    return function (element, event, handler) {
      if (element && event) {
        element.detachEvent('on' + event, handler)
      }
    }
  }
})()

export const getOnlineConfig = async function(_iModuleId, _storageKey, _userNo ) {
  const iModuleId = _iModuleId || this.iModuleId
  const storageKey = _storageKey || this.storageKey
  const userNo = _userNo || this.userNo
  return await new Promise(async (resolve, reject) => {
    const personalOnlineStorage = this.$store.getters['user/personalOnlineStorage']
    if (personalOnlineStorage && personalOnlineStorage[iModuleId]
      && personalOnlineStorage[iModuleId][storageKey]) {
        return resolve({ data: personalOnlineStorage[iModuleId][storageKey] })
    }
    
    await queryByKey({
      configKey: storageKey,
      moduleId: iModuleId,
      userNo: userNo
    }).then(res => {
      if (res.success) {

        const onlineConfigData = res.data
        let obj = {}
        try {
          obj = JSON.parse(onlineConfigData || '{}')
        } catch (error) {
          console.error(error)
        }

        const payload = {
          "configKey": storageKey,
          "configValue": obj,
          "moduleId": iModuleId,
        }
        this.$store.dispatch('user/setPersonalOnlineStorage', payload)

        resolve(obj)
      } else {

        console.error('获取线上配置出错:' + storageKey)
        reject()
      }

    }).catch((err) => {
      console.error(err)

      reject()
    })
  })
}

/* 获取全局配置 */
export const getGlobalOnlineConfig = async function(_iModuleId, _storageKey ) {
  const iModuleId = _iModuleId || this.iModuleId
  const storageKey = _storageKey || this.storageKey
  return await new Promise(async (resolve, reject) => {
    await queryGlobalConfigByModuleIdAndKey({
      configKey: storageKey,
      moduleId: iModuleId,
    }).then(res => {
      if (res.success) {
        let onlineConfigData = res.data;
        if(onlineConfigData !== null) {
            let obj = {}
            try {
                obj = JSON.parse(onlineConfigData || '{}')
            } catch (error) {
                console.error(error)
            }
            const payload = {
                "configKey": storageKey,
                "configValue": obj,
                "moduleId": iModuleId,
            }
            this.$store.dispatch('user/setPersonalOnlineStorage', payload)
            onlineConfigData = { ...obj };
        }
        resolve(onlineConfigData)
      } else {

        console.error('获取线上配置出错:' + storageKey)
        reject()
      }

    }).catch((err) => {
      console.error(err)

      reject()
    })
  })
}



export const saveOnlineConfig = function(obj, isGlobal = 0, _iModuleId, _storageKey, _userNo) {
  const iModuleId = _iModuleId || this.iModuleId
  const storageKey = _storageKey || this.storageKey
  const userNo = _userNo || this.userNo
  if (!iModuleId || !storageKey) return
  const configValue = JSON.stringify(obj)
  return saveConfig({
    "configKey": storageKey,
    "configType": isGlobal,
    "configValue": configValue,
    "moduleId": iModuleId,
    "userNo": userNo
  }).then(res => {
    if (res.success && isGlobal) {
      this.$message.success('配置保存成功！');
    }
    if (!isGlobal) {
      const payload = {
        "configKey": storageKey,
        "configValue": obj,
        "moduleId": iModuleId,
      }
      this.$store.dispatch('user/setPersonalOnlineStorage', payload)
    }

    return res
  }).catch(() => {

  })
}

// 缓存配置与本地配置进行比较，合并差异
export function compareAndModifyArrays(localArr, storageArr, keyName='prop') {
    const key = keyName;
    if(!storageArr.length) {
        return localArr;
    }
    const propArr1 = localArr.map(o=> o[key]);
    // 过滤出与本地配置相同的数据，（有可能本地配置删除了某一项）
    storageArr = storageArr.filter(item => propArr1.includes(item[key]));
    // 当过滤后的本地数据和缓存数据长度一致，返回缓存数据；
    if(storageArr.length === localArr.length) return storageArr;
    let propArr2 = storageArr.map(o=> o[key]);

    let result = [...storageArr];

    let nowAddArr = localArr.filter((item, index) => {
        if(!propArr2.includes(item[key])) {
            item.prevKey = localArr[index - 1]?.[key];
            return true
        } 
    });
    nowAddArr.map(item => {
        let temp = { ... item };
        delete temp.prevKey;
        if(!item.prevKey) {
            result.unshift(temp);
        } else {
            let idx = undefined
            result.map(o=> o[key]).find((sKey, i) => {
                if(item.prevKey == sKey) {
                    idx = i
                }
            })
            idx!== undefined && result.splice(idx + 1, 0, temp)
        }
    })
    // 返回新数组
    return result;
}

export function compareAndModifyMenuList(localArr, storageArr, keyName ='prop') {
    let tempList = compareAndModifyArrays(localArr, storageArr, keyName);
    let oMarks = {};
    let nameList = []
    localArr.map(item => {
        if(item.mark) {
            oMarks[item.name] = item.mark
            nameList.push(item.name)
        }
    })
    tempList.map(item => {
        if(nameList.includes(item.name)){
            item.mark = oMarks[item.name]
        } else {
            delete item.mark
        }
    });
    return tempList
}

// 缓存配置与本地配置进行比较，合并差异
export function compareAndModifyTableHead(localArr, storageArr) {
    const key = 'prop'
    const propArr1 = storageArr.map(o => o[key])
    if(!storageArr.length) {
        return localArr
    }
    let nowAddArr = localArr.filter((item, index) => {
        if(!propArr1.includes(item?.props?.prop)){
            item.prevKey = localArr[index - 1]?.['props']?.[key];
            return true
        }
    });

    let result = [];
    const localPropList = localArr.map(item => item?.props?.prop);
    const exitStorageArr = storageArr.filter(item => localPropList.includes(item[key]))
    if(!exitStorageArr.length) {
        return  localArr
    }
    const deduplicationArr = []
    exitStorageArr.map(item => {
        if(deduplicationArr.includes(item[key])) return;
        deduplicationArr.push(item[key])
        const targetItem = localArr.find(o => item[key] === o?.props?.prop)  
        targetItem && result.push(targetItem);
        setNewItem(item)
    })

    function setNewItem (currItem) {
        let newItem = nowAddArr.find(o => currItem[key] === o.prevKey);
        if(!newItem) return
        result.push(newItem);
        setNewItem(newItem.props)
    }

    // 返回新数组
    return result;
}



const directivesFiles = import.meta.globEager('./directives/*.js');

// 全局注册指令
export const initDirectives = (app) => {
  for (const path in directivesFiles) {
    const directiveName = directivesFiles[path].default.name;
    app.directive(directiveName, directivesFiles[path].default); 
  }
};

