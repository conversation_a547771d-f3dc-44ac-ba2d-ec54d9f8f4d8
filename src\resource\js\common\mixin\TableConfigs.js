export default {
    data() {
        return {
            renderTable: true,
            renderFilterInput: true,
            keyOptions: [],
        }
    },
    computed: {
        filterKeyOptions() {
            // 过滤只显示 isShow 的内容 (keep是默认显示，hide是默认不显示，不能冲突)
            return this.keyOptions.map(item => ({ ...item, keep: item.hide ? false : item.isShow })); 
        }
    },
    methods: {
        // 改变 table 列，需要重新渲染整个 table
        changeColumn() {
            this.renderTable = false;
            this.$nextTick(() => {
                this.renderTable = true;
            })
        },
        // 改变条件项，需要重新渲染整个 条件框
        changeFilterInput() {
            this.renderFilterInput = false;
            this.$nextTick(() => {
                this.renderFilterInput = true;
            })
        }
    }
}