import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'
export default {
    // 病例管理表操作接口 : case type Controller
    caseTypeTree,

    caseTypeAdd(data) {
        return request({
            url: baseURL.apricot + '/case/type/add',
            method: 'POST',
            data
        })
    },

    caseTypeEdit(data) {
        return request({
            url: baseURL.apricot + '/case/type/edit',
            method: 'POST',
            data
        })
    },

    caseTypeDel(params) {
        return request({
            url: baseURL.apricot + '/case/type/del',
            method: 'POST',
            params
        })
    },
    caseTypeSort(params) {
        return request({
            url: baseURL.apricot + '/case/type/sort',
            method: 'POST',
            params
        })
    },
}

export function caseTypeTree(data) {
    return request({
        url: baseURL.apricot + '/case/type/tree',
        method: 'POST',
        data
    })
}