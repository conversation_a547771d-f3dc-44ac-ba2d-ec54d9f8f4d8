<template>
<div style="height: 100%;">
   <DragAdjust :dragAdjustData="DA0" >
        <template v-slot:c-left >
            <LayoutTable>
                <template v-slot:header>
                    <SearchList v-model:modelValue="condition" 
                        :list="searchListConfig" 
                        :optionData="optionsLoc"
                        :configBtn="InjectMngPenalConfig.isShowConfigBtn"
                        :iModuleId="iModuleId"
                        storageKey="InjectSearchLsit" 
                        @changeSearch="mxDoSearch"
                        @reset="mxOnClickReset">
                        <template v-slot:dAppointmentTimeSt>
                            <!-- :disabledDate="pickerOptionsAppointDayStart"  -->
                            <el-date-picker v-model="condition.dAppointmentTimeSt" 
                                type="date"
                                @change="changeAppointTime" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:dAppointmentTimeEd>
                            <el-date-picker v-model="condition.dAppointmentTimeEd" 
                                type="date" 
                                @change="changeAppointTime" style="height:100%;">
                            </el-date-picker>
                        </template>
                        <template v-slot:sDistrictId>
                            <el-select v-model="condition.sDistrictId"
                                @change="onChangeHospital"
                                :clearable="true">
                                <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                                    :key="index"
                                    :label="item.sDistrictPrefix"
                                    :value="item.sId">
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:sMachineryRoomId>
                            <el-select v-model="condition.sMachineryRoomId"
                                :clearable="true"
                                @change="onChangeMachineRoom">
                                <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                                    :key="index"
                                    :label="item.sRoomName"
                                    :value="item.sId">
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:sProjectId>
                            <el-select v-model="condition.sProjectId"
                                @change="mxDoSearch"
                                filterable
                                :clearable="true">
                                <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                                    :key="index"
                                    :label="item.sItemName"
                                    :value="item.sId">
                                    <span style="float: left">{{ item.sItemName }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                                </el-option>
                            </el-select>
                        </template>
                        <template v-slot:iIsRegister>
                            <el-select v-model="condition.iIsRegister" clearable
                                @change="changeOptionCondition">
                                <el-option v-for="(item, index) in optionsLoc.iIsRegisterOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.sValue"
                                    ></el-option>    
                            </el-select>
                        </template>
                         <template v-slot:iIsInject>
                            <el-select v-model="condition.iIsInject" clearable
                                @change="changeOptionCondition">
                                <el-option v-for="(item, index) in optionsLoc.iIsInjectOptions"
                                    :key="index"
                                    :label="item.sName"
                                    :value="item.sValue"
                                    ></el-option>    
                            </el-select>
                        </template>
                    </SearchList>
                </template>
                <template #action> 
                    <div class="action-box">
                        <div>
                            <el-dropdown >
                                <el-button type="primary" plain>
                                    <template #icon><Icon name="el-icon-setting"></Icon></template>
                                    设置<Icon name="el-icon-arrow-down"></Icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item v-if="$auth['report:injection:callConfig']"  @click="openCallSet"><i class="fa fa-call-fill i-height"></i>呼叫设置</el-dropdown-item>
                                        <el-dropdown-item v-if="$auth['report:injection:printConfig']"  @click="openPrintSetDialog"><i class="fa fa-print-setup i-height"></i>打印设置</el-dropdown-item>
                                        <el-dropdown-item v-if="$auth['report:injection:pageConfig']">
                                            <!-- 页面配置 -->
                                            <PanelConfigDialog :iModuleId="iModuleId" 
                                                :formData="InjectMngPenalConfig" 
                                                :configKey="'InjectMngPenalConfig'" 
                                                @updateData="(data)=>InjectMngPenalConfig=data"></PanelConfigDialog>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        
                        </div>
                        <div class="right-box">
                            <span class="ml-4"></span>
                            <span class="checkbox">
                                <el-button @click="mxOnClickReset">
                                    <template #icon>
                                        <Icon name="el-icon-refresh-left" >
                                        </Icon>
                                    </template>重置</el-button>
                            </span>
                            <span class="checkbox">
                                <el-button v-auth="'report:injection:query'" type="primary" @click="mxDoSearch()" :loading="loading">
                                    <template #icon><Icon name="el-icon-search" color="white">
                                        </Icon></template>查询</el-button>
                            </span>
                        </div>
                    </div>
                </template>
                <template #content style="position: relative;">
                    <el-table-extend v-loading="loading"
                        oncontextmenu='return false'
                        v-contextmenu:contextmenuDiv 
                        :data="tableData"
                        ref="mainTable"
                        stripe
                        highlight-current-row
                        height="100%"
                        :configBtn="InjectMngPenalConfig.isShowConfigBtn"
                        :iModuleId="iModuleId"
                        storageKey="InjectMngIndexTable"
                        @row-click="handleRowClick"
                        @row-contextmenu="handleRightClick">
                            <el-table-column type="index" label="序号" prop="_index" align="center" width="60">
                                <template v-slot="scope">
                                    <span>{{ scope.$index + 1 }}</span>
                                </template>
                            </el-table-column>
                            <template v-for="item in tableList" :key="item.sProp">
                                <el-table-column 
                                    :show-overflow-tooltip="item.sProp !== 'img'"
                                    :prop="item.sProp"
                                    :label="item.sLabel" 
                                    :fixed="item.sFixed" 
                                    :align="item.sAlign" 
                                    :width="item.sWidth"
                                    :min-width="item.sMinWidth" 
                                    :sortable="(!!item.iSort) ? 'custom' : false"
                                    :isSortable="item.isSortable" 
                                    :sOrder="item.sOrder" 
                                    :iIsHide="item.iIsHide"
                                    :column-key="item.sSortField ? item.sSortField : null">
                                    <template v-slot="scope">
                                        <template v-if="FlowStateEnum.includes(item.sProp)">
                                            <!-- 0:未上机;1:上机准备;2:上机中;3:延迟中;4:上机完成 -->
                                            <span v-if="item.sProp === 'iIsMachine' && scope.row[`${item.sProp}`] == 1"
                                                class="icon-blue">准备</span>
                                            <span v-else-if="item.sProp==='iIsMachine'&& scope.row[`${item.sProp}`] == 3"
                                                class="icon-blue">延迟</span>
                                            <i v-else-if="scope.row[`${item.sProp}`] == 2"
                                                class="icon-blue" :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                                
                                            <template v-else-if="item.sProp === 'iIsInject' && scope.row['injectionCount'] >= 1">
                                                <template v-for="(_ , idx) in scope.row['injectionCount']" :key="idx">
                                                    <i class="icon-green" :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                                </template>
                                            </template>
                                            <i v-else-if="scope.row[`${item.sProp}`]" class="icon-green"
                                                :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                                        </template>
                                        <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                            {{mxFormatterDate( scope.row[`${item.sProp}`])}}
                                        </template>
                                        <template v-else-if="['sNuclideSupName', 'sTracerSupName'].includes(item.sProp)">
                                            <span v-if="scope.row[item.sProp]" v-html="scope.row[item.sProp]"></span>
                                        </template>
                                        <template v-else-if="['fRecipeDose'].includes(item.sProp)">
                                            {{ setRecipeDose(scope.row[item.sProp], scope.row.sRecipeDoseUnit) }}
                                        </template>
                                        <template v-else-if="['fBloodSugar'].includes(item.sProp)">
                                            {{ setBloodSugar(scope.row[item.sProp]) }}
                                        </template>
                                        <template v-else-if="['iIsPregnant'].includes(item.sProp)">
                                            {{ setPregnantText(scope.row[item.sProp]) }}
                                        </template>
                                        <template v-else>
                                            {{ scope.row[`${item.sProp}`] }}
                                        </template>
                                    </template>
                                </el-table-column>
                            </template>
                    </el-table-extend>
                     <!-- 鼠标右击 -->
                    <v-contextmenu  :disabled="!isFixedWostation || !tableData.length || !$auth['report:injection:call']" ref="contextmenuDiv">
                        <div class="call-contextmenu-inner">
                            <div class="name">{{rightClickRow.sName}}</div>
                            <div :loading="calledBtnLoading">
                                <template v-if="modulesCallBtn?.length">
                                    <v-contextmenu-item v-for="(item, index) in modulesCallBtn" :key="index">
                                        <div>
                                            <el-button 
                                                plain
                                                :loading="item.loading"
                                                _icon="fa fa-volume-up"
                                                @click.stop="handleQuickCall(item, index)">
                                                    <span :class="item.isCalled? 'icon-green': ''">{{item.buttonName}}</span>
                                            </el-button>
                                            <i v-if="item.isCalled" class="el-icon-check icon-green i-position"></i>
                                        </div>
                                    </v-contextmenu-item>
                                </template>
                                <div v-else>
                                    <el-empty :image-size="80" description="未配置按钮" />
                                </div>
                          </div> 
                        </div>
                    </v-contextmenu>

                </template>
                <template #footer>
                    <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange" :current-page="page.pageCurrent"
                        :page-sizes="pageSizes" :pager-count="5" :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next" :total="page.total">
                    </el-pagination>
                </template>
            </LayoutTable>
        </template>
        <template v-slot:c-right>
            <div class="m-flexLaout-ty">
                <div class="c-right-item">
                    <div class="c-text-title">
                        <h3>注射单</h3>    
                        <el-button class="i-button" :disabled="!selectedRow.sId" link type="primary" @click="openPatientInfoModifi">
                            <el-icon size="14"><Tickets/></el-icon>
                            患者详情
                        </el-button>
                    </div>
                    <div :style="{ backgroundColor: InjectMngPenalConfig.patientInfoBgColor}">
                        <TextList :list="textList" 
                            :data="injectPatientInfo" 
                            :configBtn="InjectMngPenalConfig.isShowConfigBtn"
                            :iModuleId="iModuleId" 
                            storageKey="injectIndexInfoList">
                            <template v-for="(item, index) in ['dAppointmentTime', 'dApplyDate']"
                                v-slot:[item]="{ style, row }" :key="index">
                                <span :style="style" :title="mxFormatterDate(injectPatientInfo[item])">
                                    {{ mxFormatterDate(injectPatientInfo[item]) || '（空）' }}
                                </span>
                            </template>
                            <template #dBirthday="{ row, style }" :title="mxToDate(injectPatientInfo[row.prop]) || '（空）'">
                                {{ mxToDate(injectPatientInfo[row.prop]) || '（空）' }}
                            </template>
                            <template #sNuclideSupName="{ row, style }">
                                <span :style="style" v-html="injectPatientInfo.sNuclideSupName || '（空）'"> </span>
                            </template>
                            <template #sTracerSupName="{ row, style }">
                                <span :style="style" v-html="injectPatientInfo.sTracerSupName || '（空）'"> </span>
                            </template>
                            <template #fRecipeDose="{ row, style }">
                                <span :style="style" :title="setRecipeDose(injectPatientInfo[row.prop], injectPatientInfo.sRecipeDoseUnit)">
                                    {{ setRecipeDose(injectPatientInfo[row.prop], injectPatientInfo.sRecipeDoseUnit)  || '（空）' }}
                                </span>
                            </template>
                            <template #fBloodSugar="{ row, style }">
                                <span :style="style" :title="setBloodSugar(injectPatientInfo[row.prop])">
                                    {{ setBloodSugar(injectPatientInfo[row.prop]) || '（空）' }}
                                </span>
                            </template>
                            <template #iIsPregnant="{ row, style }">
                                <span :style="style" :title="setPregnantText(injectPatientInfo[row.prop])">
                                    {{ setPregnantText(injectPatientInfo[row.prop]) || '（空）' }}
                                </span>
                            </template>
                        </TextList>
                    </div>
                </div>
                <div class="c-right-item g-flexChild">
                    <OperateArea :callBtnArray="callBtnArray" 
                        :isFixedWostation="isFixedWostation"
                        @updateRowData="updateRowData"
                        @getInitInJectData="getInitInJectData"
                    ></OperateArea>
                </div>
            </div>
        </template>
   </DragAdjust>
   <!-- 打印设置 -->
    <PrintSet :dialogVisible="dialog_printSet_v"
        :iModuleId="iModuleId"
        @closeDialog="closePrintSetDialog"></PrintSet>
    <!-- 上机呼叫 呼叫-->

    <!-- 呼叫设置-->
    <CallSet :dialogVisible="callSet_DialogVisible" :iModule="{iModuleId:iModuleId, iModuleName:'注射管理'}"
        @closeDialog="onCloseCallSet"></CallSet>

    <!-- 患者详情 -->
    <PatientInfoRead :dialogVisible="dialog_PatientInfoModifi_v" 
        :patientSid="selectedRow.sId" 
        :iModuleId="iModuleId"
        @closeDialog="closePatientInfoModifi">
    </PatientInfoRead>
        
    <!-- 上机呼叫 呼叫-->
    <Call v-model:dialogVisible="call_DialogVisible" 
        :patientInfo="rightClickRow" 
        :iModule="{iModuleId:iModuleId, iModuleName:'注射管理'}"
        @closeDialog="closeCallDialog"></Call>
    
</div>
</template>

<script>
    export default {
        name: 'apricot_injectMng'
    }
</script>

<script setup>
import { Tickets } from '@element-plus/icons-vue';
 //模块辅助样式
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';

import Call from '$supersetViews/apricot/components/Call.vue' // 上机呼叫
import CallSet from '$supersetViews/apricot/components/CallSet.vue' // 呼叫设置
import PrintSet from '$supersetViews/apricot/components/PrintSet.vue' // 打印设置
import PatientInfoRead from '$supersetViews/apricot/common/PatientInfoRead.vue'
import OperateArea from './components/OperateArea.vue'
// import RightCall from './components/RightCall.vue'

import PanelConfigDialog from './components/PanelConfigDialog.vue'
// 配置文件
import Configs from './config'
import numIcons from './config/numIcons'
import { mxToDate, mxFormatterDate, setPregnantText, setRecipeDose, setBloodSugar} from './config/famaterData'

// 方法
import { deepClone } from '$supersetUtils/function'
import { getCallButtonSetOfModules,getCalledBtn, handleCallAction} from '$supersetResource/js/projects/apricot/call.js'

// 枚举类
import { recentDayOptions, iIsRegisterOptions, iIsInjectOptions, callTypeOptions } from '$supersetResource/js/projects/apricot/enum.js'

// 接口
import { queryByKey } from '$supersetApi/userConfig.js';
import Api from '$supersetApi/projects/apricot/injectMng/index.js'
import { getHospitalData, getMachineRoomData, getItemData } from '$supersetApi/projects/apricot/common/baseData.js'
import { getInjectInit } from '$supersetApi/projects/apricot/injectMng/inject.js'
import { getItemSetData as getItemSetDataApi, getNuclideData, getTracerData } from '$supersetApi/projects/apricot/appointment/projectSet.js'

// import { callAction } from '$supersetApi/projects/apricot/common/call.js' // 呼叫接口

import scannerKeyCodeEvent from '$supersetResource/js/projects/apricot/scannerKeyCodeEvent.js';

    const iModuleId = 4 ; //模块id标识
    // 分屏配置
    const DA0 = reactive(Configs.DA0)
    var InjectMngPenalConfig = ref({
        rightTabPanels: []
    })

    // 路由
    const route = useRoute()
    // 搜索条件配置
    const searchListConfig = ref(Configs.searchStyle)
    const optionsLoc = reactive({
        iIsRegisterOptions: iIsRegisterOptions,
        iIsInjectOptions: iIsInjectOptions,
        districtArrOption: [], //院区
        machineRoomArrOption: [], // 机房
        itemsArrOption: [],    // 项目

    })
    // 表格表头配置
    const tableList = reactive(Configs.patientTable)
    const FlowStateEnum = numIcons.FlowStateEnum
    const FlowStateEnumIcon = numIcons.FlowStateEnumIcon

    // 文本组件配置
    var textList = reactive(Configs.textListConfig);

    var selectedRow = ref({})
    var injectPatientInfo = ref({})
    
    var callSet_DialogVisible = ref(false); // 呼叫设置
    var dialog_printSet_v = ref(false); // 打印设置
    var dialog_PatientInfoModifi_v = ref(false); //个人信息弹框 
    var call_DialogVisible = ref(false)
    var rightClickRow = reactive({})// 右键单击行
    var modulesCallBtn = ref([])  // 呼叫按钮
    var callBtnArray = ref([])
    var isFixedWostation = ref(false) // 工作站类型和模块是否匹配
    var calledBtnLoading = ref(false)
    const mainTable = ref(null) // 定义表格实体

    var medicationTableData = ref([]);
    
    const nuclideOptions = ref([]);
    const tracerOptions = ref([]);


     // 获取行数据
    const  updateRowData = (id = null, idx = null)=> {
        let sId = id !== null ? id : selectedRow.value.sId
        let index = idx !== null ? idx : selectedRow.value.index
        Api.getInjectionRow({ sId }).then(res => {
            if (res.success) {
                const data = res.data || {};
                data.index = index
                tableData.value.splice(index, 1, data);
                if(sId != selectedRow.value.sId) return
                selectedRow.value = tableData.value[index]; // 更新选中值

                mainTable.value && mainTable.value.setCurrentRow(tableData.value[index]) // 设置选中值
                return;
            }
        })

    }
    // h获取注射初始化信息
    const getInitInJectData =(val)=> {
        getInjectInit({
                sPatientId: val
            }).then(res => {
                if (res.success) {
                    let data = res.data || {};
                    injectPatientInfo.value = { ...selectedRow.value};
                    Object.keys(data).map(key => {
                        if(data[key] ?? false) {
                            injectPatientInfo.value[key] = data[key];
                        }
                    })
                    return
                }
                ElMessage.error(res.msg);
                injectPatientInfo.value = {}
            }).catch(err => {
                console.log(err);
                injectPatientInfo.value= {}
        })
    }
    provide('patientInfo', selectedRow);// 
    provide('injectPatientInfo', injectPatientInfo);
    provide('configValue', InjectMngPenalConfig);
    provide('iModuleId', iModuleId);
    provide('updateInjectRowData', updateRowData)
    provide('getInitInJectData', getInitInJectData)
    provide('medicationTableData', medicationTableData)
    provide('getItemSetData', getItemSetData)
    provide('nuclideOptions', nuclideOptions)
    provide('tracerOptions', tracerOptions)

    // 获取检查用药数据
    async function  getItemSetData () {
        // medicationTableData.value = []
        let patientInfo = selectedRow.value;
        await getItemSetDataApi({
            sItemId: patientInfo.sProjectId
        }).then(res => {
            if (res.success) {
                medicationTableData.value = res.data || [];
                return
            }
            medicationTableData.value = []
        }).catch(err => {
            console.log(err)
            medicationTableData.value = []
        })
    }

    // 核素
    async function handleGetNuclideData () {
        await getNuclideData({}).then(res => {
            if (res.success) {
                nuclideOptions.value = res?.data || [];
                return
            }
            nuclideOptions.value = []
        }).catch(err => {
            nuclideOptions.value = []
        })
    }

    // 示踪剂
    async function handleGetTracerData () {
        await getTracerData({}).then(res => {
            if (res.success) {
                tracerOptions.value = res?.data || [];
                return
            }
            tracerOptions.value = []
        }).catch(err => {
            tracerOptions.value = []
        })
    }

    
    //获取工作站
    const store = useStore();
    const workStation = computed( ()=>{
        const workStation = store._state.data.user.workStation
        if(workStation) {
            return workStation
        }
        return {}
    })
    const auth = store.getters['user/buttonRightMap']
    // 工作站改变时
    watch(() => workStation,
        async(newValue, oldValue) => {
            isFixedWostation.value = newValue.value.stationTypeCode === iModuleId.toString() ? true: false
            if( oldValue && oldValue.value ) {
                condition.sDistrictId = newValue.value.districtId;
                onChangeHospital(condition.sDistrictId);
            }
            if(isFixedWostation.value && auth['report:injection:call']) {
                modulesCallBtn.value = await getCallButtonSetOfModules(iModuleId, workStation.value.stationId);
                callBtnArray.value = deepClone(modulesCallBtn.value)
            } 
        },
        {   immediate: true, deep: true,}
    )

    // 打开呼叫设置
    function openCallSet() {
        if(!isFixedWostation.value) {
            ElMessage.warning('请切换到注射工作站！')
            return
        }
        callSet_DialogVisible.value = true
    }
    // 关闭呼叫设置
    async function onCloseCallSet(isEditBtn) {
        callSet_DialogVisible.value = false
        if(isEditBtn && auth['report:injection:call']) {
            modulesCallBtn.value = await getCallButtonSetOfModules(iModuleId, workStation.value.stationId);
            callBtnArray.value = deepClone(modulesCallBtn.value)
        }
    }
    // 打开打印设置
    function openPrintSetDialog() {
        if(!isFixedWostation.value) {
            ElMessage.warning('请切换到注射工作站！')
            return
        }
        dialog_printSet_v.value = true
    }
    // 关闭打印设置
    function closePrintSetDialog() {
        dialog_printSet_v.value = false
    }
 
    // 打开个人信息修改
     // 打开患者信息弹窗
    function openPatientInfoModifi () {
        //打开弹窗
        dialog_PatientInfoModifi_v.value = true
    };
    // 关闭个人信息弹框
    function closePatientInfoModifi() {
        dialog_PatientInfoModifi_v.value = false
    }
    // 查询条件
    //sWorkStationId 只看本工作站
    let sWorkStationId = false
    // 查询条件
    let condition = reactive({
        dAppointmentTimeSt: new Date(),
        dAppointmentTimeEd: new Date(),
        sDistrictId:'',
        sMachineryRoomId:'',
        sProjectId: '',
        iIsRegister: JSON.parse(localStorage.getItem('injectSearchCondition')) ? JSON.parse(localStorage.getItem('injectSearchCondition'))['iIsRegister'] + '' : '',
        iIsInject: JSON.parse(localStorage.getItem('injectSearchCondition')) ? JSON.parse(localStorage.getItem('injectSearchCondition'))['iIsInject'] + '' : '',
    })
    function changeOptionCondition() {
        mxDoSearch()
        const params = {iIsRegister: condition.iIsRegister, iIsInject: condition.iIsInject}
        localStorage.setItem('injectSearchCondition', JSON.stringify(params))
    }
    // 预约开始时间不可用日期
    const pickerOptionsAppointDayStart = (time)=> {
        if (condition.dAppointmentTimeEd) {
            return time.getTime() > new Date(condition.dAppointmentTimeEd).getTime() || time.getTime() > new Date().getTime()
        }
        return time.getTime() > new Date().getTime()
    };

    
    // var calledBtnArray = ref([])
    //右键单击行
    function handleRightClick(row) {
        if(rightClickRow.sId === row.sId) {
            return
        }
        if(!isFixedWostation.value) {
            ElMessage.warning('请切换到注射工作站！');
            return
        }
        rightClickRow = row
        getRowCalled()
    }
    async function getRowCalled() {
        calledBtnLoading.value = true
        modulesCallBtn.value.map( item=>{
            item.isCalled = false
        })
        const id = rightClickRow.sId
        const calledBtnData = await getCalledBtn(id)
        calledBtnLoading.value = false
        modulesCallBtn.value.forEach(item =>{
            if (calledBtnData.includes(item.buttonCode)) {
                item.isCalled = true
            }
        })
    }
    async function handleQuickCall(data, index) {
        const row = rightClickRow
        if(data.buttonCode == 0) {
            onOpenCall()
            return
        }
        data.loading = true
        let jsonData = {
            callBtnCode: data.buttonCode,
            captionsId: "",
            patientInfoId: row.sId,
            stationId: data.stationId,
            sysModuleCode: data.moduleId,
        }
        const isCalled = await handleCallAction(jsonData)
        data.loading = false
        if(isCalled.isCalled) {
            data.isCalled = true
        }
        updateRowData(row.sId,row.index) 
    };
    const onOpenCall =() =>{
        call_DialogVisible.value = true
    }
     // 关闭呼叫  
    const closeCallDialog = () => {
        call_DialogVisible.value = false
    };
    // 数据查询
    const loadingSearch = ref(false)
    const loading = ref(false)
    let tableData = ref([]);
    
    let page = reactive({
      // 分页
      pageCurrent: 1,
      pageSize: localStorage.injectMngIndexPageSize ? JSON.parse(localStorage.injectMngIndexPageSize) : 30,
      total: 0,
    });

    const pageSizes = reactive([10, 20, 30, 50, 100, 200]);

    const onSizeChange = (val) => {
        page.pageSize = val;
        localStorage.setItem('injectMngIndexPageSize', val);
        selectedRow.value = {}
        initGetTableData()

    };
    // 
    const onCurrentChange = (val) => {
        page.pageCurrent = val;
        selectedRow.value = {}
        initGetTableData()
        
    };
    // 格式化一天开始时间
    function mxFormateOneDayStart (time) {
        if (!time) return '';
        return moment(time).startOf('day').toDate();
    }
    // 格式化一天结束时间
    function mxFormateOneDayEnd (time) {
        if (!time) return '';
        return moment(time).endOf('day').toDate();
    }
    const getData = () =>{
        loading.value = true
        const params = {
            page: {
                pageCurrent: page.pageCurrent,
                pageSize: page.pageSize,
            }
        }
        
        params.condition = deepClone(condition)
        const dAppointmentTimeSt = params.condition.dAppointmentTimeSt;
        params.condition.dAppointmentTimeSt = mxFormateOneDayStart(dAppointmentTimeSt);
        // 当选择了结束时间，转换成23：59：59
        const dAppointmentTimeEd = params.condition.dAppointmentTimeEd;
        params.condition.dAppointmentTimeEd = mxFormateOneDayEnd(dAppointmentTimeEd);
        // 不勾选预约日期的时候 删除 params对象 时间起止属性
        // 只查当前工作站数据
        if(InjectMngPenalConfig.value.OnlySearchCurrentWStation) {
            params.condition.sWorkStationId = workStation.value.stationId
        }
        Api.getInjectionData(params).then((res) => {
            loading.value = false
            if(res.success) {
                tableData.value = res.data.recordList ? res.data.recordList : []
                page.pageCurrent = res.data.pageCurrent;
                page.total = res.data.countRow;
                if(!tableData.value.length) {
                    // 查询不到数据清理注射单内容
                    injectPatientInfo.value = {}
                    return
                }
                const isNoSelected = !selectedRow.value.sId;
                mxSetSelectedRow(); 
                nextTick(async () => {
                    selectedRow.value.sId && InjectMngPenalConfig.value.medicineChooseType == 1 && await getItemSetData();
                    if(selectedRow.value.sId && InjectMngPenalConfig.value.medicineChooseType == 2) {
                        await handleGetNuclideData();
                        await handleGetTracerData();
                    }
                    isNoSelected && getInitInJectData(selectedRow.value.sId);
                })   
                return
            }
            ElMessage.error(res.msg)
        }).catch( err => {
            loading.value = false
            console.log(err)
        })
    }
    /**
    /*查询数据
    **/
    function mxDoSearch() {
        page.pageCurrent = 1
        selectedRow.value = {}
        getData();
    }
    // 初始化数据 
    function initGetTableData() {
        getData();
    }

    //
    function mxRowClassName()  { }    

    function mxOnClickReset() {
        Object.keys(condition).forEach(key => (condition[key] = null))
        condition.dAppointmentTimeSt = new Date()
        condition.dAppointmentTimeEd = new Date()
        const target = localStorage.getItem('injectSearchCondition')
        if(target){
            const cd = JSON.parse(target)
            condition.iIsRegister = cd.iIsRegister?cd.iIsRegister : ''
            condition.iIsInject = cd.iIsInject?cd.iIsInject : ''
        } else{
            condition.iIsRegister = ''
            condition.iIsInject = ''
        }
        condition.sDistrictId = workStation.value.districtId
        getMachineRoomOptionData(condition.sDistrictId)
        mxDoSearch()
    }
 
    // 修改预约时间
    function changeAppointTime() {
        mxDoSearch()
    }

    // 点击行
    async function handleRowClick(row) {    
        selectedRow.value = row;
        medicationTableData.value = [];
        if(InjectMngPenalConfig.value.medicineChooseType == 2) {
            await handleGetNuclideData();
            await handleGetTracerData();
        } else {
            await getItemSetData()
        }
        getInitInJectData(row.sId);
    };
    
    // 初始化赋值选中
    function mxSetSelectedRow () {
        // 定义表格实例
        nextTick( ()=>{
            if(mainTable.value) {
                const target = (mainTable.value.tableRef.$refs.tableHeaderRef.store.states.data.value[0] ) // 获取排序后的表格数组
                if (target) { 
                    // 选中排序后的第一条
                    selectedRow.value = target
                    mainTable.value.setCurrentRow(target);
                }
                // let idx = 0
                // if(selectedRow.value?.sId) {
                //     tableData.value.find((item,index) =>{
                //         if(item.sId == selectedRow.value.sId) {
                //             idx = index
                //         }
                //      })
                // }
                // selectedRow.value = tableData.value[idx]
                // mainTable.value.setCurrentRow(tableData.value[idx]);
                
            }    
        })
    };
   

    //  获取院区
    async function getHospitalOptionData() {
        await getHospitalData().then((res) => {
            if (res.success) {
                optionsLoc.districtArrOption = res?.data || [];
                
                // optionsLoc.districtArrOption.map( (item)=>{
                //     item.sName = item.sHospitalDistrictName
                //     item.sValue = item.sId
                // })
            }
        }).catch((err) => {
            console.log(err)
        })
    };

    //选择院区时
    function onChangeHospital (val) {
        if(condition.sMachineryRoomId) {
            condition.sMachineryRoomId = ''
            getItemsArrData()
        }
        getMachineRoomOptionData()
        mxDoSearch()
    }
    
    // 获取机房
    function getMachineRoomOptionData() {
        const params = {
            iIsEnable: 1,
            sDistrictId: condition.sDistrictId
        }
        !condition.sDistrictId && delete params.sDistrictId;
        getMachineRoomData(params).then((res) => {
            if (res.success) {
                optionsLoc.machineRoomArrOption = res?.data || [];
            }
        }).catch((err) => {
            console.log(err)
        })
    };

    //选择机房时
    function onChangeMachineRoom (val) {
        var target = optionsLoc.machineRoomArrOption.find( element => element.sId === val)
        if(target) {
            getItemsArrData(target.sDeviceTypeId)
        } else {
            getItemsArrData()
        }
        mxDoSearch()
    }

    // 获取项目
     const getItemsArrData = (val) =>{
        const params = {
            iIsEnable: 1,
            sDeviceTypeId: val || ''
        }
        getItemData(params).then((res) => {
            if (res.success) {
                optionsLoc.itemsArrOption = res?.data || []
            }
        }).catch((res) => {
            console.log(res.err)
        })
    };
    async function getPageConfigData() {
        let userConfigParam = {
            configKey: 'InjectMngPenalConfig',
            moduleId: iModuleId,
            userNo: store.getters['user/userSystemInfo'].sNo
        }
        await queryByKey(userConfigParam).then( (res)=>{
            if(res.success) {
                InjectMngPenalConfig.value = res.data?JSON.parse(res.data) : Configs.InjectMngPenalConfig;
                if(res.data) {
                    // 将本地数据与获取到的数据合并
                    let data = InjectMngPenalConfig.value;
                    let defaultConfig = Configs.InjectMngPenalConfig;
                    let target = {};
                    Object.keys(defaultConfig).map(key => {
                        target[key] = data[key] ?? defaultConfig[key]
                    })
                    InjectMngPenalConfig.value = target;
                }
                return
            }
            ElMessage.error(res.msg)
        }).catch( (err)=>{
            console.log(err)
        })
        // InjectMngPenalConfig
    }

    function scannerMonitor() {
        // 设备扫描
        scannerKeyCodeEvent.onMachineEvent = (res => {
            if (route.name === 'apricot_injectMng') {
                scannerSearch(res);
            }
        })
    }
    function scannerSearch(code) {
        const params = {
            condition: {
                sNuclearNum: code 
            },
            page: {
                pageCurrent: 1,
                pageSize: 30
            }
        }
        loading.value = true
        Api.getInjectionData(params).then((res) => {
            tableData.value = [];
            loading.value = false
            if (res.success) {
                let data = res.data.recordList || [];
                tableData.value = data;
                page.pageCurrent = 1;
                page.pageSize = 10;
                page.total = res.data.countRow;
                // 赋选中状态
                mxSetSelectedRow();
                return
            }
            ElMessage.error(res.msg)
        }).catch(() => {
            loading.value = false
        })
    }
    onBeforeMount( async()=>{
        
    }) 
       
    onMounted( async() => {
        await getPageConfigData()
        await getHospitalOptionData();
        condition.sDistrictId = workStation.value.districtId
        getMachineRoomOptionData(condition.sDistrictId);

         
        // if(isFixedWostation.value) {
        //     modulesCallBtn.value = await getCallButtonSetOfModules(iModuleId, workStation.value.stationId);
        // } 
        // let conSultSearchKey = localStorage.getItem('conSultSearchKey')
        initGetTableData();
        getItemsArrData();
        scannerMonitor();
    });


</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.c-template {
    position: relative;
}
.i-height {
    height: 20px;
}
.i-position {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.action-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right-box {
        display: flex;
        align-items: center;
        span.checkbox {
            margin-left: 10px;
        }
    }
}

.c-right-item {
    padding: 10px 10px;
    background: #fff;
    .c-text-title {
        position: relative;
        h3 {
            padding: 10px 0;
            margin: 0;
            text-align: center;
           
        }
        .i-button {
            position: absolute;
            top: 11px;
            right: 8px;
        }
    }
}

.slotEdit {
    display: inline-block;
    margin: 4px 0px 0 8px;
    cursor: pointer;
    color: var(--el-color-primary);
}

</style>
