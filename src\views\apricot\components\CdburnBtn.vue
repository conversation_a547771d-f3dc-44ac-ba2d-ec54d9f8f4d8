<template>
  <div class="inline-block" @click="cdburnSeries">
    <slot>
      <el-button :class="{
        'm-vertical-btn t2': buttonMsg.icon,
        'margin_l': !buttonMsg.icon,
        'm-vertical-text': buttonMsg.isFold,
        't-border': buttonMsg.isBorder
        }" 
        :disabled="buttonMsg.isReadOnly" :plain="buttonMsg.plain" :link="buttonMsg.link" :type="buttonMsg.type">
        <svg v-if="buttonMsg.icon" class="fa" aria-hidden="true" style="font-size:34px">
          <use :xlink:href="'#' + buttonMsg.icon"></use>
        </svg>
        <label>{{ buttonMsg.name }}</label>
      </el-button>
    </slot>
    
    <!-- 刻录序列 -->
    <el-dialog append-to-body :modelValue="dialogVisible" class="t-default my-dialog" width="900px" top="10vh"
        :close-on-click-modal="false" destroy-on-close @close="handleCloseDialog">
        <template #header>
        <div class="header-title el-dialog__title">
            <span>刻录序列：</span>
            <span v-if="patientInfo.sName">{{ patientInfo.sName }}</span>
            <span v-if="patientInfo.sSexText">{{ patientInfo.sSexText }}</span>
            <span v-if="patientInfo.sAge">{{ patientInfo.sAge }}</span>
        </div>
        </template>
        <div class="c-dialog-body">
        <div class="c-checkGroup">
            <el-checkbox v-model="enableRebuild" :true-label="1" :false-label="0"
            @change="handleCheckBoxChange">可重建</el-checkbox>
            <el-checkbox v-model="disableRebuild" :true-label="1" :false-label="0"
            @change="handleCheckBoxChange">不可重建</el-checkbox>
        </div>
        <div class="c-item t-1 m-flexLaout-ty">
            <!-- 表格 -->
            <div class="g-flexChild">
            <el-table :data="filterList" ref="mainTree" @selection-change="handleSelectionChange" @row-click="onRowClick"
                border height="60vh">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column label="设备" prop="sModality" min-width="120" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="数目" prop="iInstanceCount" min-width="80" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="序列描述" prop="sSeriesDesc" min-width="200" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="日期" prop="iSeriesDate" min-width="120" show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="时间" prop="sSeriesTime" min-width="120" show-overflow-tooltip>
                </el-table-column>
            </el-table>
            </div>
        </div>
        </div>
        <template #footer>
        <el-button-icon-fa type="primary" icon="el-icon-check" :loading="loading"
            @click="handleBurn">开始刻录</el-button-icon-fa>
        <el-button-icon-fa _icon="fa fa-close-1" @click="handleCloseDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
  </div>
</template>

<script>
import { deepClone } from "$supersetUtils/function"
import { cdburnSeries, cdburnRecord } from '$supersetApi/projects/apricot/case/report.js'
export default {
  name: 'CdburnBtn',
  props: {
    buttonMsg: {
      type: Object,
      default: () => ({})
    },
    patientInfo: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      enableRebuild: 1, // 是否可重建 1=是；0 =否
      disableRebuild: 0, // 是否不可重建 1=是；0 =否
      multipleSelection: [],
      filterList: [],
      seriesList: [],
      dialogVisible: false,
      loading: false
    }
  },
  methods: {
    handleCheckBoxChange() {
      this.handleFilterList();
    },
    handleFilterList() {
      if (!this.enableRebuild && !this.disableRebuild) {
        this.filterList = [];
        return
      }
      if (this.enableRebuild && !this.disableRebuild) {
        this.filterList = this.seriesList.filter(data => data.iIsRebuid === 1);
        return
      }
      if (this.disableRebuild && !this.enableRebuild) {
        this.filterList = this.seriesList.filter(data => data.iIsRebuid === 0);
        return
      }
      this.filterList = deepClone(this.seriesList);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onRowClick(row) {
      this.$refs.mainTree.toggleRowSelection(row);
    },
    handleBurn() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择需要刻录的序列！');
        return
      }
      let series = [];
      this.multipleSelection.map(item => {
        series.push({
          studyInstanceUid: item.sStudyInstanceUid,
          seriesInstanceUid: item.sSeriesInstanceUid
        })
      });
      this.loading = true;
      cdburnRecord({
        sPatientId: this.patientInfo.sId,
        // destDir: this.destDir,
        includeReportFile: true,
        series: series
      }).then(res => {
        this.loading = false;
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        res.msg && this.$message.success(res.msg);
        this.handleCloseDialog();
      }).catch(err => {
        this.loading = false;
        console.log(err);
      })
    },
    // 关闭弹窗 
    handleCloseDialog() {
      this.dialogVisible = false
    },
    cdburnSeries() {
      let item = this.patientInfo;
      if (!Object.keys(item).length) {
        this.$message.warning('请选择一条患者数据！')
        return
      }
      if (!item.sImgPatientId) {
        this.$message.warning('图像患者标识不存在！')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      cdburnSeries({
        iImgStudyDate: item.sImgStudyDate,
        sImgAccessionNumber: item.sImgAccessionNumber,
        sImgPatientId: item.sImgPatientId,
        sNuclearNum: item.sNuclearNum
      }).then(res => {
        loading.close();
        this.seriesList = [];
        this.filterList = [];
        if (!res.success) {
          this.$message.error(res.msg);
          return
        }
        if (!res.data || res.data.length === 0) {
          // console.log('没有查找图像序列')
          this.$message.error('没有查找图像序列');
          return
        }
        this.dialogVisible = true;
        this.seriesList = res.data;
        this.handleFilterList();
      }).catch(err => {
        console.log(err);
        loading.close();
      })
    },
  },
  created() {
    this.handleFilterList()
  }
}
</script>

<style lang="scss" scoped>
.header-title {
  >span:not(:first-child) {
    margin-right: 15px;
  }
}

.c-dialog-body {
  .c-checkGroup {
    margin-bottom: 10px;
  }

  .c-item.t-1 {
    padding-bottom: 10px;

    .i-upload {
      width: 180px;
      padding-left: 20px;
    }
  }
}


.margin_l {
  margin-left: 10px;
}
</style>
