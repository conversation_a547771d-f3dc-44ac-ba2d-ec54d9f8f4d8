<template>
    <div class="m-businessMenu"
        :class="{ 'm-hide': !menuLen }">
        <slot name="menu"></slot>
        <el-popover v-if="foldPanelShow"
            :popper-class="popperClass"
            class="f-openFoldMenu"
            placement="bottom"
            :width="foldPanelWidth"
            trigger="hover">
            <slot name="settingMenu"></slot>
            <div class="c-foldBusinessMenu">
                <slot name="foldMenu"></slot>
            </div>
            <template #reference>
                <el-button class="m-vertical-btn t2">
                    <svg class="fa"
                        aria-hidden="true">
                        <use :xlink:href="'#fa-wenjianjiafile'"></use>
                    </svg>
                    <label class="moreMenu">更多功能<i class="el-icon-caret-bottom"
                            style="font-size:12px; display: inline"></i></label>
                </el-button>
            </template>
        </el-popover>
        <div class="flex flex-grow justify-end">
            <slot name="close"></slot>

        </div>
    </div>
</template>

<script>
export default {
    name: 'MenuPanel',
    data () {
        return {
        }
    },
    props: {
        // 按钮长度
        menuLen: {
            type: Number,
            default: 1
        },
        foldPanelWidth: {
            type: Number,
            default: 300
        },
        popperClass: {
            type: String,
            default: 'mengmengda'
        },
        foldPanelShow: {
            /*type: Boolean,*/
            default: true
        }
    }
}
</script>

<style lang="scss">
$borderColor: #cdcecf;

.cicles {
    width: 26px;
    height: 26px;
    background-color: #ddd;
    border-radius: 50%;
    padding: 6px;
    margin: 0 auto;
    margin-top: 5px;

    .cicle-item {
        display: block;
        width: 3px;
        height: 3px;
        background: #888;
        margin: 2px auto;
        border-radius: 50%;
    }
}

.moreMenu {
    display: block;
    margin: 5px auto 0 auto;
    color: #606266;
    font-size: 12px;
}

.m-hide {
    display: none;
}

.m-businessMenu {
    min-height: 60px;
    padding: 5px;
    box-sizing: border-box;
    background-color: #fafafa;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    //   border-bottom: var(--el-border);
    margin-bottom: 8px;
    .el-button {
        background-color: transparent;
        min-width: 4em;
        &:hover {
            background-color: var(--el-color-primary-light-9);
        }
    }

    > .f-openFoldMenu {
        display: inline-block;

        // border: solid 1px $borderColor;
        > button {
            padding: 0;
            height: 100%;
            border: none;
            color: #888;
            // padding-left: 20px;
            margin-left: 4px;
            position: relative;

            span {
                display: block;
            }

            i.fa {
                font-size: 24px;
            }

            .border::before {
                content: '';
                position: absolute;
                top: 15%;
                left: 0;
                margin-right: -12px;
                bottom: 5%;
                width: 1px;
                //background-color: #ccc;
                z-index: 1;
            }
        }
    }
}

.c-foldBusinessMenu {
    display: flex;
    flex-wrap: wrap;
    min-height: 90px;

    .el-button {
        width: 90px;    
        border: 1px dashed rgba(0, 0, 0, 0.01);
    }
}
</style>
