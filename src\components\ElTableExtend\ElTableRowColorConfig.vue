<template>
    <el-popover ref="popoverRef" :visible="visible" trigger="manual" placement="left-start" width="auto"
      @after-enter="onPopAfterEnter" @after-leave="onPopAfterLeave">
      <template #default>
        <div class="pop-header">
          <h3>{{ ('表格配置') }}</h3>

        </div> 
        <div class="flex items-center justify-between pt-2">
          <span class="flex">
            <el-button type="primary" @click="setStorage(1)">{{ ('保存到全局') }}</el-button>
            <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否恢复默认设置？')"
              :teleported="false" @confirm="onClickResetConfig">
              <template #reference>
                <el-button type="primary" plain>{{ ('恢复默认设置') }}</el-button>
              </template>
            </el-popconfirm>
          </span>
          <span class="flex">
            <el-button type="default" @click="visible = false">{{ ('关闭') }}</el-button>
          </span>
        </div>
      </template>
      <template #reference>

        <div v-if="configBtn === true" class="config-btn" @click="visible = true">
          <!-- <img src="@/assets/svg/setting.svg" class="img" /> -->
          <el-icon class="img">
            <Tools />
          </el-icon>
        </div>
      </template>
    </el-popover>
</template>
<script>
import draggable from 'vuedraggable';
import { Rank, Tools } from '@element-plus/icons-vue'

import { getOnlineConfig, saveOnlineConfig, compareAndModifyTableHead } from '@/utils'
import { useStore } from 'vuex';
import { isFunction } from 'lodash-es';


const defaultStorageData = {
  list: [],
  sortingProp: null,
  order: null
}

  

export default {
  name: "ElTableRowColorConfig",
  components: {
    draggable,
    Rank, Tools
  },
  props: {
    storageKey: {
      type: String,
      default: "", // 不填不启用localstorage
    },
    iModuleId: {
      default: "", // 不填不启用数据上传
    },
    configBtn: {
      type: [Object, Boolean],
      default: () => true
    }, 
  },
  // @sort-change="mxOnSortTable" 接管事件
  emits: ['sort-change', 'selection-change'],
  setup(props) {
    const _store = useStore()
    const buttonRef = ref();
    const popoverRef = ref();
    const tableRef = ref();
    const react = ref({
      slotItemList: [],
      tableData: [],
      hiddenSlotsList: []
    });
    const configButtonRef = computed(() => {
      if (props.configBtn && props.configBtn.ref) {
        return props.configBtn
      }
      return buttonRef
    });
    const elTableDefaultSortObj = ref(
      { prop: null, order: null }
    )
    const isElTableMethodsReady = computed(() => {
      return !!tableRef.value.doLayout
    })

    const userNo = computed(() => {
      let userInfo = _store.getters["user/userSystemInfo"] || {};
      return userInfo.sId
    })

    return {
      userNo,
      react,

      isTableShow: ref(false),
      visible: ref(false),
      buttonRef,
      popoverRef,
      tableRef,
      configButtonRef,
      selectionArr: ref([]),
      elTableDefaultSortObj,
      isElTableMethodsReady,

      getOnlineConfig,
      saveOnlineConfig,
    };
  },
  created() {
    this.setDefaultData()
    this.getStorage();
  },
  computed: {
     

  },
  methods: { 
 
    async getStorage() {
      if (!this.storageKey) return;
      let storageStr = localStorage.getItem('ElTableRowColorConfig-' + this.storageKey);
      let storageObj = {
        ...defaultStorageData
      }


      if (this.iModuleId && this.storageKey) {
        await this.getOnlineConfig()
        Object.assign(storageObj, 
        this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])
      } else {
        try {
          const parsed = JSON.parse(storageStr)
          if (Array.isArray(parsed)) {
            storageObj.list = parsed
          } else if (parsed && parsed.list) {
            storageObj = parsed
          }
        } catch (error) {
          console.error(error)
        }
      }


      let storageList = storageObj.list;

      const propList = (storageList || []).map((i) => i.prop);
      const slotsList = this.getSlotList();
      let newList = compareAndModifyTableHead(slotsList, storageList);
      
    //   let newList = []

    //   if (propList.length !== slotsList.length) {
    //     // 如果表格加了新的一列数据，恢复成默认顺序
    //     newList = slotsList
    //   } else {

    //     // newList // 按localstor排序后的slot列表
    //     propList.forEach(prop => {
    //       const index = slotsList.findIndex(slot => slot.props.prop === prop)
    //       if (index > -1) {
    //         newList.push(slotsList.splice(index, 1)[0])
    //       }
    //     })
    //     newList = [...slotsList].concat(newList)
    //   }

      this.react.slotItemList = newList
      this.react.tableData = newList.map(this.dataObjConvert);

      // 通过给slot加属性
      let index, slotItem, tableItem
      storageList.forEach((item) => {
        index = this.react.tableData.findIndex(target => item.prop === target.prop)
        if (index > -1) {
          slotItem = this.react.slotItemList[index];
          tableItem = this.react.tableData[index];
          tableItem.isShow = item.isShow;
          tableItem.isSortable = !!item.isSortable;

          slotItem.props.align = tableItem.align = item.align;
          slotItem.props.sortable = item.isSortable ? (item.customSort ? 'custom' : true) : false;

          if (item.width) {
            tableItem.width = item.width;
            slotItem.props.width = item.width;
          }

          if (item['min-width']) {
            tableItem['min-width'] = item['min-width'];
            slotItem.props['min-width'] = item['min-width'];
          }

        }
      });

      // 设置 elTableDefaultSortObj  
      this.elTableDefaultSortObj.prop = storageObj.sortingProp
      this.elTableDefaultSortObj.order = storageObj.order

      this.isTableShow = true
    },
    // 设置缓存
    setStorage(isGlobal = 0) {
      if (!this.storageKey) return;

      const storageObj = {
        list: this.react.tableData,
        sortingProp: this.elTableDefaultSortObj.prop,
        order: this.elTableDefaultSortObj.order,
      }

      const storageString = JSON.stringify(storageObj)

      localStorage.setItem(
        "ElTableExtend-" + this.storageKey,
        storageString
      );
      this.saveOnlineConfig(storageObj, isGlobal)
    }, 
    setDefaultData() {
      this.react.slotItemList = this.getSlotList()
      // console.log(this.react.slotItemList)
      this.react.tableData = this.react.slotItemList.map(this.dataObjConvert);
    },
    dataObjConvert(item) {
      return ({
        prop: item.props.prop || " ",
        label: item.props.label || " ",
        // isShow: true,
        isShow: !item.props.iIsHide,
        align: item.props.align || "left",
        width: String(item.props.width || '').replace(/[px]/g, ''),
        'min-width': item.props['min-width'] || item.props.width || '',
        isSortable: false,
        customSort: item.props.sortable === 'custom'
      })
    },
    // 点击重置
    onClickResetConfig() {
      this.setDefaultData()
      this.setStorage();
    },
    onPopAfterLeave() {

    },
    onPopAfterEnter() {
    }, 
  },
  mounted() { 
  },
};
</script>

<style lang="scss" scoped> 
</style>
