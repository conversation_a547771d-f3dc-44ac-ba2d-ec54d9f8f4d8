<template>
    <div class="g-main">
        <div class="c-item" v-if="!isOnlyShowUpload">
            <!-- 本地图片 -->
            <div class="c-header c-bothend">
                <div class="c-flex">
                    <el-radio-group v-model="activeName" size="small" @change="handleTabClick">
                        <el-radio-button label="2">高拍仪</el-radio-button>
                        <el-radio-button label="1">扫描仪</el-radio-button>
                    </el-radio-group>
                </div>
                <div class="c-flex">
                    <strong>未上传图片</strong>
                    <span>(共{{ localImages.length }}张)</span>
                    <el-checkbox v-model="localImgChecked" @change="onChangeCheckbox" style="margin-left: 15px">全选</el-checkbox>
                </div>
            </div>
            <div class="c-content" v-loading="loading1">
                <el-scrollbar class="my-scrollbar">
                    <div class="c-list">
                        <div class="c-list-item t-2" v-for="(item, index) in localImages" 
                            :key="index"
                            :class="{ 'c-active': selectedLocalItem.includes(item.name) }" 
                            @click="onLocalImgClick(item, index)"
                            @dblclick="onLocalImgDblClick(item, index)">
                            <div class="c-triangle t-1">
                                <span>{{ index + 1 }}</span>
                            </div>
                            <el-image :src="item.url" fit="contain"></el-image>
                        </div>
                        <el-image-viewer v-if="imgModal2" @switch="onSwitch2" @close="onClose2" :url-list="localImages.map(item => item.url)"
                            :initial-index="imgSrcIndex2" />
                    </div>
                </el-scrollbar>
            </div>
            <div class="c-footer c-bothend">
                <span class="i-text" :title="localFilePath">所在目录：{{ localFilePath }}</span>
                <div>
                    <el-button-icon-fa size="small" icon="el-icon-set-up" 
                        @click="onDriverClick">打开驱动</el-button-icon-fa>
                    <el-button-icon-fa size="small" icon="el-icon-delete" :disabled="!patientInfo.sId"
                        @click="onDelLocalFiles">刪除</el-button-icon-fa>
                </div>
            </div>
        </div>
        <div class="c-middle" v-if="!isOnlyShowUpload" >
            <div v-if="rights.uploadFile">
                <el-button type="primary" size="small" :disabled="!patientInfo.sId" @click="onUploadFiles">
                    <span>上传</span>
                    <Icon name="el-icon-d-arrow-right"></Icon>
                </el-button>
            </div>
        </div>
        <div class="c-item c-right-penal">
            <!-- 上传图片 -->
            <div class="c-header c-bothend">
                <div class="c-flex">
                    <el-radio-group v-model="imageViewerSetting.rotate" size="small" @change="handleRotateChange">
                        <el-radio-button label="anticlocelise">-90°</el-radio-button>
                        <el-radio-button :label="null">0°</el-radio-button>
                        <el-radio-button label="clocelise">+90°</el-radio-button>
                    </el-radio-group>
                </div>
                <div class="c-flex">
                    <strong>已上传图片</strong>
                    <span>(共{{ serverImages.length }}张)</span>
                    <el-checkbox v-model="serverImgChecked" @change="onServerImgChangeCheckbox" style="margin-left: 15px">全选
                    </el-checkbox>
                </div>
            </div>
            <div class="c-content" v-if="isVisible">
                <el-scrollbar class="my-scrollbar">
                    <div class="c-list" :class="{
                        'img-anticlocelise': imageViewerSetting.rotate == 'anticlocelise',
                        'img-clocelise': imageViewerSetting.rotate == 'clocelise'}">
                        <div class="c-list-item t-2" v-for="(item, index) in serverImages"
                            :key="index" 
                            :class="{ 
                                't-4': isOnlyShowUpload, 
                                'i-height-10': isOnlyShowUpload && rightPenalWidth <= 1000,
                                'i-height-15': isOnlyShowUpload && rightPenalWidth > 1000 && rightPenalWidth < 1500,
                                'c-active': selectedServeItem.includes(item.sId)}" 
                            @click="onClickImage(item, index)"
                            @dblclick="onDblClickImage(item, index)">
                            <div class="c-triangle t-1">
                                <span>{{ index + 1 }}</span>
                            </div>
                            
                            <el-image :src="item.url" fit="contain" lazy class="" :class="`img-${index}`" ></el-image>
                            
                            <div class="c-actions" @click.stop>
                                <el-dropdown placement="top">
                                    <span class="el-dropdown-link">
                                        <el-button-icon-fa link icon="el-icon-more"></el-button-icon-fa>
                                    </span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item @click="onPrint(`img-${index}`)">
                                                <i class="el-icon-printer i-height"></i>
                                                <span>打印</span>
                                            </el-dropdown-item>
                                            <el-dropdown-item @click="onDownServerFile(item)">
                                                <i class="el-icon-download i-height"></i>
                                                <span>下载</span>
                                            </el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </div>
                        </div>
                        <el-image-viewer v-if="imgModal" @switch="onSwitch" @close="onClose" :url-list="ImageUrls"
                            :initial-index="imgSrcIndex" />
                    </div>
                </el-scrollbar>
            </div>
            <div class="c-footer c-bothend">
                <div>
                    <!-- <el-progress :percentage="uploadParams.percent" style="width: 150px"></el-progress> -->
                    <span v-if="uploadParams.isShow">进度：{{ uploadParams.currentIdx }}/{{ uploadParams.totalIdx }}</span>
                </div>
                <div v-if="rights.manualUpload">
                    <el-upload ref="uploadRef" class="upload-demo" action="void" :http-request="customUpload"
                        :on-progress="handleProgress" :data="{
                            sInnerIndex: patientInfo.sInnerIndex,
                            sPatientId: patientInfo.sId,
                            sNuclearNum: patientInfo.sNuclearNum,
                            iUploadMode: 1,
                        }" multiple accept="*" :show-file-list="false">
                        <el-button-icon-fa :disabled="!patientInfo.sId" size="small" icon="el-icon-upload2">手工上传</el-button-icon-fa>
                    </el-upload>
                    <el-button-icon-fa  v-if="rights.deleteFile"
                        size="small" 
                        icon="el-icon-delete" 
                        :disabled="!patientInfo.sId"
                        @click="onMatchDelServerFiles">刪除</el-button-icon-fa>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import 'tiff.js';
import Print from '$supersetResource/js/print';
import { getToken } from '$supersetUtils/auth';

import { getFiles, attachmentsDel } from '$supersetApi/projects/apricot/common/files.js';
import {
    getHpcameraImages,
    uploadFilmscanner,
    delHpcameraImg,
    getFilmscannerImages,
    delFilmscannerImg,
    openHpcamera,
    openFilmscanner,
    getLocalConfig
} from '$supersetApi/projects/apricot/assistServe/index.js';

export default {
    name: 'ScanImage',
    props: {
        isVisible: {
            type: Boolean,
            default: false,
        },
        isOnlyShowUpload: {
            type: Boolean,
            default: false,
        },
        rights: {
            type: Object,
            default: () => ({
                deleteFile: true,
                uploadFile: true,
                manualUpload: true,
            })
        }
    },
    inject: {
        patientInfo: {
            from: 'patientInfo',
            default: () => ({})
        }
    },
    data () {
        return {
            activeName: '2', // 高拍仪=2；扫描仪=1
            localImages: [], // 本地图片
            serverImages: [], // 服务器图片
            imgServerURL: `${window.configs.urls.apricot}/attachments/preview?sFilePath=`,
            imgHpcameraLocalURL: `${window.configs.urls.apricotAssist}/hpcamera/img/`,
            imgScannarLocalURL: `${window.configs.urls.apricotAssist}/filmscanner/img/`,
            xhrList: [],
            localFilePath: '',
            selectedLocalItem: [],
            selectedServeItem: [],
            loading1: false,
            ImageUrls: [],
            time: null,
            localImgChecked: false,
            serverImgChecked: false,
            apricotUrl: window.configs.urls.apricot,
            uploadParams: {
                totalIdx: 0,
                currentIdx: 0,
                percent: 0,
                isShow: false,
            },
            imgModal: false, // 大图模态框控制
            imgSrcIndex: 0, // 当前点击图片下标
            imageViewerSetting: {
                // 图片显示缓存配置
                rotate: null,
            },
            clickTime: null,
            imgModal2: false,
            imgSrcIndex2: 0,
            resizeObserver: null,
            rightPenalWidth: 0,
            clientParams: {
                cameraExe: undefined,
                cameraFilePath: undefined,
                scannerExe: undefined,
                scannerFilePath: undefined
            },
            isGetSuccess: true  // 是否成功读取本地图片
        };
    },
    watch: {
        isVisible: {
            async handler (val) {
                clearInterval(this.time);
                if (val && this.patientInfo.sId) {
                    !this.isOnlyShowUpload && await this.getLocalConfig();
                    !this.isOnlyShowUpload && this.intervalFn();
                }
                if(val) {
                    this.resizeObserver.observe(document.querySelector('.c-right-penal'));
                } else {
                    this.resizeObserver.disconnect();
                }
            }
        },
        patientInfo: {
            async handler (val, oldVal) {
                clearInterval(this.time);
                if (!val.sId) {
                    // 页面关闭时终止xhr请求
                    this.xhrList.forEach(item => {
                        item.abort()
                    });
                    this.xhrList = [];
                    return
                }
                if (val && oldVal && val.sId === oldVal.sId) return
                this.activeName = '2';
                this.initImageViewer();
                this.getServerFiles();
                
                this.isVisible && !this.isOnlyShowUpload && await this.getLocalConfig();
                this.isVisible && !this.isOnlyShowUpload && this.intervalFn();
            }
        }
    },
    mounted() {
        this.resizeObserver = new ResizeObserver((entries, b) => {
            for (let entry of entries) {
                if(entry.target.className.includes('c-right-penal')) {
                    this.rightPenalWidth = entry.contentRect.width;
                }
            }
        });
    },
    beforeUnmount () {
        clearInterval(this.time);
    },
    methods: {
        setLocalFilePath () {
            if(this.activeName == '2') {
                this.localFilePath = this.clientParams.cameraFilePath
            } else {
                this.localFilePath = this.clientParams.scannerFilePath
            }
        },
        // 获取客户端参数配置
        async getLocalConfig () {
            await getLocalConfig().then(res => {
                if (res.success) {
                    this.clientParams = res.data || this.$options.data().clientParams;
                    this.setLocalFilePath();
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
            })
        },
        // 初始化，获取图片显示配置缓存信息
        initImageViewer () {
            let imageViewerSetting = localStorage.getItem('imageViewerSetting');
            this.imageViewerSetting = imageViewerSetting
                ? JSON.parse(imageViewerSetting)
                : this.$options.data().imageViewerSetting;
        },
        // 旋转图片
        handleRotateChange () {
            localStorage.setItem('imageViewerSetting', JSON.stringify(this.imageViewerSetting));
        },
        // 点击显示大图浏览
        onClickImage (select, index) {
            if (this.clickTime) {
                clearTimeout(this.clickTime)
            }
            this.clickTime = setTimeout(() => {
                let idx = null,
                target = null;
                this.selectedServeItem.find((item, i) => {
                    if (select.sId === item) {
                        target = item;
                        idx = i;
                    }
                });
                if (target) {
                    this.selectedServeItem.splice(idx, 1);
                    return;
                }
                this.selectedServeItem.push(select.sId);
            }, 300);
        },
        onDblClickImage(select, index){
            if(this.clickTime){
                clearTimeout(this.clickTime)
            }
            // 双击事件
            this.imgModal = true;
            this.onSwitch(index, true);
            return
        },
        // 切页大图
        onSwitch (index) {
            this.imgSrcIndex = index;
        },
        // 关闭浏览
        onClose () {
            this.imgModal = false;
        },
        // 打印
        onPrint (calssname) {
            Print(document.querySelector(`.${calssname} img`));
        },
        onChangeCheckbox (val) {
            if (!val) {
                this.selectedLocalItem = [];
                return;
            }
            this.localImages.map((item) => {
                this.selectedLocalItem.push(item.name);
            });
        },
        onServerImgChangeCheckbox (val) {
            this.selectedServeItem = [];
            if(!val) return;
            this.serverImages.map((item) => {
                this.selectedServeItem.push(item.sId);
            });
        },
        // tab 切换
        async handleTabClick (val) {
            this.activeName = val;
            if (!this.patientInfo.sId) {
                clearInterval(this.time);
                return;
            }
            this.setLocalFilePath();
            this.getServerFiles();
            !this.isOnlyShowUpload && this.intervalFn();
        },
        // 打开驱动程序
        onDriverClick () {
            let activeFn = this.activeName === '2' ? openHpcamera : openFilmscanner;
            let loading = this.$loading({
                lock: true,
                text: '请稍等...',
                background: 'rgba(0, 0, 0, 0.5)',
            });
            activeFn()
                .then((res) => {
                    this.$message.success(res.data);
                    loading.close();
                })
                .catch((err) => {
                    console.log(err);
                    loading.close();
                });
        },
        // 处理本地图片选择
        onLocalImgClick (select, index) {
            if (this.clickTime) {
                clearTimeout(this.clickTime)
            }
            this.clickTime = setTimeout(() => {
                let index = null,
                    target = null;
                this.selectedLocalItem.find((item, idx) => {
                    if (select.name === item) {
                        target = item;
                        index = idx;
                    }
                });
                if (target) {
                    this.selectedLocalItem.splice(index, 1);
                    return;
                }
                this.selectedLocalItem.push(select.name);
            }, 300);
        },
        onLocalImgDblClick(select, index) {
            if(this.clickTime){
                clearTimeout(this.clickTime)
            }
            // 双击事件
            this.imgModal2 = true;
            this.onSwitch2(index, true);
            return
        },
        onSwitch2 (index) {
            this.imgSrcIndex2 = index;
        }, 
        onClose2 () {
            this.imgModal2 = false;
        },
        // 删除本机电脑图片
        onDelLocalFiles () {
            if (!this.selectedLocalItem.length) {
                this.$message.warning('请选择需要删除的图片！');
                return;
            }
            this.$confirm('确定删除图片文件，是否继续？', '提示', { type: 'warning' })
                .then(async () => {
                    let loading = this.$loading({
                        lock: true,
                        text: '处理中...',
                        //
                        background: 'rgba(0, 0, 0, 0.5)',
                    });
                    let fileNames = this.selectedLocalItem.join(',');
                    let jsonData = {
                        fileNames: fileNames,
                    };
                    let activeFn = this.activeName === '2' ? delHpcameraImg : delFilmscannerImg;
                    activeFn(jsonData)
                        .then((res) => {
                            this.intervalFn();
                            loading.close();
                        })
                        .catch((err) => {
                            loading.close();
                            console.log(err);
                        });
                })
                .catch((err) => err);
        },
        onUploadFiles () {
            if (!this.selectedLocalItem.length) {
                this.$message.warning('请选择需要上传的图片！');
                return;
            }
            let fileNames = this.selectedLocalItem.join(',');
            let patientInfo = this.patientInfo;
            let jsonData = {
                sInnerIndex: patientInfo.sInnerIndex,
                sPatientId: patientInfo.sId,
                sNuclearNum: patientInfo.sNuclearNum,
                iUploadMode: this.activeName === '2' ? 2 : 1,
                del: 1,
                fileNames: fileNames,
            };
            let loading = this.$loading({
                lock: true,
                text: '上传中...',
                background: 'rgba(0, 0, 0, 0.1)',
            });
            uploadFilmscanner(jsonData)
                .then((res) => {
                    if (res && res.data) {
                        if (res.success) {
                            this.$message.success(res.data);
                            if(this.localImgChecked) {
                                this.localImgChecked = false;
                            }
                        } else {
                            this.$message.error(res.msg);
                        }
                    }
                    loading.close();
                    this.getLocalImages();
                    this.getServerFiles();
                })
                .catch((err) => {
                    loading.close();
                    console.log(err);
                });
        },
        async customUpload (file) {
            let FormDatas = new FormData();
            FormDatas.append('file', file.file);
            try {
                this.uploadParams.isShow = true;
                let res = await axios({
                    url: `${this.apricotUrl}/attachments/upload`,
                    method: 'post',
                    data: FormDatas,
                    params: {
                        sInnerIndex: this.patientInfo.sInnerIndex,
                        sPatientId: this.patientInfo.sId,
                        sNuclearNum: this.patientInfo.sNuclearNum,
                        iUploadMode: 1,
                    },
                    headers: {
                        aptSessionId: getToken(),
                        'Content-Type': 'multipart/form-data',
                    },
                    //上传进度
                    onUploadProgress: (progressEvent) => {
                        let num = ((progressEvent.loaded / progressEvent.total) * 100) | 0; //百分比
                        file.onProgress({
                            percent: num,
                        }); //进度条
                    },
                });
                let { success, data } = res.data;
                if (success) {
                    file.onSuccess(); //上传成功(打钩的小图标)
                    data = data || {};
                    // console.log(file.file);
                    if (this.uploadParams.totalIdx === this.uploadParams.currentIdx) {
                        this.getServerFiles();
                        setTimeout(() => {
                            this.uploadParams.isShow = false;
                            this.uploadParams.percent = 0;
                            this.uploadParams.currentIdx = 0;
                            this.$refs.uploadRef.clearFiles();
                        }, 5000);
                    }
                    return;
                }
                this.$message.error(res.msg);
                setTimeout(() => {
                    this.uploadParams.isShow = false;
                    this.uploadParams.percent = 0;
                    this.uploadParams.currentIdx = 0;
                    this.$refs.uploadRef.clearFiles();
                }, 5000);
            } catch (err) {
                console.log(err);
            }
        },
        handleProgress (event, file, fileList) {
            this.uploadParams.totalIdx = fileList.length;
            this.uploadParams.percent = event.percent;
            if (event.percent == 100) {
                this.uploadParams.currentIdx++;
            }
        },
        // 下载
        onDownServerFile (item) {
            // let downURL = `${window.configs.urls.apricot}/attachments/download?aptSessionId=${getToken()}&sFilePath=`;
            let downURL = item.url + item.sDiskSymbol + item.sFilePath;
            fetch(downURL)
              .then(response => response.blob())
              .then(blob => {
                const urlBlob = window.URL.createObjectURL(new Blob([blob]));
                const link = document.createElement('a');
                link.href = urlBlob;
                link.setAttribute('download', item.sUploadName);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
              });
        },
        // 删除已上传的文件
        onMatchDelServerFiles () {
            let checkedArr = this.serverImages.filter((item) => this.selectedServeItem.includes(item.sId));
            if (!checkedArr.length) {
                this.$message.warning('请选择需要删除的图片！');
                return;
            }
            let _this = this;
            this.$confirm('确定删除图片文件，是否继续？', '提示', { type: 'warning' })
                .then(async () => {
                    let isDelSuccess = false;
                    let loading = this.$loading({
                        lock: true,
                        text: '处理中...',
                        //
                        background: 'rgba(0, 0, 0, 0.5)',
                    });
                    for (let i = 0; i < checkedArr.length; i++) {
                        let item = checkedArr[i];
                        let jsonData = {
                            iVersion: item.iVersion,
                            sId: item.sId,
                        };
                        let res = await attachmentsDel(jsonData);
                        try {
                            if (!res.success) {
                                loading.close();
                                this.$message.error(res.msg);
                                isDelSuccess && this.getServerFiles();
                                return;
                            }
                            isDelSuccess = true;
                        } catch (error) {
                            loading.close();
                            _this.$message.error('删除失败!');
                            isDelSuccess && this.getServerFiles();
                            return;
                        }
                    }
                    this.$message.success('删除成功!');
                    if(this.serverImgChecked) {
                        this.serverImgChecked = false;
                    }
                    this.getServerFiles();
                    loading.close();
                })
                .catch((err) => err);
        },
        // 定时获取本地文件
        async intervalFn () {
            clearInterval(this.time);
            if (!this.localFilePath) {
                this.$message.warning('请配置资料所在目录！');
                // 没有配置地址的时候
                return;
            }
            await this.getLocalImages(true);

            if(!this.isGetSuccess) return

            this.time = setInterval(async () => {
                await this.getLocalImages(false)
            }, 3000);
        },
        // 获取本地文件 isNormalGet 是否定时获取
        async getLocalImages (isNormalGet = true ) {
            if(isNormalGet) {
                this.localImages = [];
                this.selectedLocalItem = [];
                this.loading1 = true;
            }
            let activeFn = this.activeName === '2' ? getHpcameraImages : getFilmscannerImages;
            await activeFn()
                .then((res) => {
                    this.loading1 = false;
                    if (res.success) {
                        this.isGetSuccess = true;
                        let dataImgs = res.data.imgs || res.data.imgs;
                        let localImages = this.localImages;
                        let resultArr = dataImgs.filter((item) =>
                            localImages.every((subItem) => subItem.name != item)
                        );
                        if (resultArr.length) {
                            resultArr.map((item, index) => {
                                let temp = {
                                    name: item,
                                    url: this.activeName === '2' ? `${this.imgHpcameraLocalURL}${item}` : `${this.imgScannarLocalURL}${item}`,
                                };
                                this.localImages.push(temp);
                                if (item.includes('.tif') || item.includes('.tiff')) {
                                    this.setLocalTiffFile2DataURL(item, index);
                                }
                            });
                        }
                        return;
                    }
                    this.$message.error(res.msg);
                    clearInterval(this.time);
                    this.isGetSuccess = false;
                })
                .catch((err) => {
                    console.log(err);
                    this.loading1 = false;
                    this.isGetSuccess = false;
                    clearInterval(this.time);
                });
        },
        // tif文件 转 basedata
        setLocalTiffFile2DataURL (item, index) {
            let filename = this.activeName === '2' ? `${this.imgHpcameraLocalURL}${item}` : `${this.imgScannarLocalURL}${item}`;
            let xhr = new XMLHttpRequest();
            xhr.open('GET', filename, true);
            xhr.responseType = 'arraybuffer';
            let that = this;
            xhr.onload = function (e) {
                var buffer = xhr.response;
                Tiff.initialize({ TOTAL_MEMORY: 50 * 1024 * 1024 }); //支持最大50M
                var tiff = new Tiff({
                    buffer: buffer,
                });
                var imgData = tiff.toDataURL();
                if (imgData) {
                    let target = {
                        name: item,
                        url: imgData,
                    };
                    that.localImages[index] = target;
                }
            };
            xhr.send();
            this.xhrList.push(xhr);
        },
        // 获取图片列表
        getServerFiles () {
            this.serverImages = [];
            this.ImageUrls = [];
            let jsonData = {
                sPatientId: this.patientInfo.sId,
                sInnerIndex: this.patientInfo.sInnerIndex,
                sNuclearNum: this.patientInfo.sNuclearNum,
            };
            getFiles(jsonData).then((res) => {
                if (res.success) {
                    this.serverImages = res.data || [];
                    this.serverImages.map((item, index) => {
                        let suffix = item.sFileType;
                        let url = `${this.imgServerURL}${item.sDiskSymbol}${item.sFilePath}&aptSessionId=${getToken()}`;
                        item['url'] = url;
                        this.ImageUrls.push(url);
                        if (suffix === 'tif' || suffix === 'tiff') {
                            this.setTiffFile2DataURL(item, index);
                        }
                    });
                    return;
                }
                this.$message.error(res.msg);
                return;
            }).catch((err) => {
                console.log(err);
            });
        },
        // tif文件 转 basedata
        setTiffFile2DataURL (item, index) {
            let filename = `${this.imgServerURL}${item.sDiskSymbol}${item.sFilePath}&aptSessionId=${getToken()}`;
            let xhr = new XMLHttpRequest();
            xhr.open('GET', filename, true);
            xhr.responseType = 'arraybuffer';
            let that = this;
            xhr.onload = function (e) {
                var buffer = xhr.response;
                Tiff.initialize({ TOTAL_MEMORY: 50 * 1024 * 1024 }); //支持最大50M
                var tiff = new Tiff({
                    buffer: buffer,
                });
                var imgData = tiff.toDataURL();
                if (imgData) {
                    item['url'] = imgData;
                    that.ImageUrls[index] = imgData;
                }
            };
            xhr.send();
            this.xhrList.push(xhr);
        },
    },
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs) {
    .el-tabs__header {
        margin: 0;
    }

    .el-tabs__nav {
        margin-left: 10px;
    }

    .el-tabs__item {
        height: 35px;
        line-height: 35px;
    }
}

.g-main {
    position: relative;
    flex: 1;
    display: flex;
    overflow: hidden;

    .c-middle {
        padding: 0 5px;
        display: flex;
        align-items: center;
    }
    .c-item {
        flex: 2;
        display: flex;
        flex-direction: column;
        border: 1px solid #cfd5e6;

        .c-header {
            background-color: var(--el-color-primary-light-8);
        }

        .c-bothend {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            min-height: 40px;
            line-height: 40px;
            padding: 0 10px;

            .c-flex {
                display: flex;
                align-items: center;
            }
            .i-text {
                display: inline-block;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                max-width: calc(100% - 185px);
            }
        }

        .c-footer {
            background: var(--el-color-info-light-9);
        }

        .upload-demo {
            display: inline-block;
            margin-right: 10px;
        }

        .c-content {
            flex: 1;
            padding: 10px;
            background-color: #fff;
            border-top: 1px solid #cfd5e6;
            border-bottom: 1px solid #cfd5e6;
            overflow: auto;
        }

        .c-list {
            display: flex;
            flex-wrap: wrap;

            .c-list-item {
                width: calc(50% - 5px);
                height: 200px;
                margin-bottom: 10px;
                margin-right: 10px;
                padding: 10px;
                border: 1px solid #ccc;
                box-sizing: border-box;
                text-align: center;
                position: relative;
                cursor: pointer;

                &.t-1:nth-child(2n + 1) {
                    margin-right: 0;
                }

                &.t-2:nth-child(2n) {
                    margin-right: 0;
                }

                &.t-4 {
                    width: calc(25% - 10px);    
                    height: 20vw;
                    margin-right: 10px !important;
                    &.i-height-10 {
                        height: 10vw;
                    }
                    &.i-height-15 {
                        height: 15vw;
                    }
                }
                

                &.t-4:nth-child(4n) {
                    margin-right: 0;
                }

                .c-actions {
                    display: none;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    padding-left: 4px;
                    text-align: right;
                    // &:hover {
                    //     border-radius: 2px;
                    //     background: rgba(0, 0, 0, 0.2);
                    // }
                }

                &:hover {
                    .c-actions {
                        display: inline-block;
                    }
                }

                &.c-active {
                    border-color: var(--el-color-primary);
                    border-width: 2px;
                }

                .el-image {
                    width: 100%;
                    height: 100%;
                }

                .c-triangle {
                    position: absolute;
                    right: 6px;
                    top: 6px;
                    width: 25px;
                    height: 25px;

                    &.t-1 {
                        left: 6px;
                        right: unset;
                        z-index: 1;
                        line-height: 25px;
                        text-align: center;
                        font-weight: bold;
                        color: var(--el-color-primary);
                        border-radius: 50%;
                        border: 2px solid var(--el-border-color);
                        background: #fff;
                    }
                }
            }
        }
    }
}

:deep(.el-radio-button__original-radio:checked+.el-radio-button__inner){
    color: var(--el-color-primary);
    background-color: var(--el-color-white);
}

:deep(.el-image-viewer__mask) {
    opacity: 0.95;
}

:deep(.el-image-viewer__close .el-icon-circle-close) {
    color: #cdcdcd;
}


.img-clocelise {

  :deep(.el-image img) {
    transform: scale(0.8) rotate(90deg);
    cursor: pointer;
  }

  :deep(.el-image-viewer__canvas) {
    transform: scale(0.8) rotate(90deg);
  }
}

.img-anticlocelise {

  :deep(.el-image img) {
    transform: scale(0.8) rotate(-90deg);
    cursor: pointer;
  }

  :deep(.el-image-viewer__canvas) {
    transform: scale(0.8) rotate(-90deg);
  }
}
</style>
