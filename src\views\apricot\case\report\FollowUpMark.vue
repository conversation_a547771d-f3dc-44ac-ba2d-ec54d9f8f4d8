<template>
    <el-dialog append-to-body title="随访标记" v-model="visible" :close-on-click-modal="false"
        width="75%" align-center class="my-dialog h-auto t-default"
        @close="emits('update:dialogVisible', false)">
        <div class="g-content g-flexChild m-flexLaout-tx" style="height:70vh;">
            <div class="m-flexLaout-ty c-report">
                <div class="g-flexChild c-item t-1">
                    <p>检查所见</p>
                    <div class="g-flexChild i-item" v-html="reportContent.sInspectSee"></div>
                </div>
                <div class="g-flexChild c-item t-2">
                    <p>诊断意见</p>
                    <div class="g-flexChild i-item" v-html="reportContent.sDiagnosticOpinion"></div>
                </div>
            </div>
            <div class="g-flexChild m-flexLaout-ty minHeight">
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="随访详情" name="visitForm">
                        <div class="content">
                            <el-form ref="formRef" label-position="right" :model="followForm" :disabled="isEdit">
                                <FormList v-model:formData="followForm" storageKey="ReperIndexVisiteForm" :iModuleId="iModuleId" 
                                    :list="followRecordInput" :optionData="optionsLoc">
                                </FormList>
                            </el-form>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="随访记录" name="visitedRecord">
                        <template v-slot:label>
                            <span>随访记录</span>
                            <span class="i-count">{{ `(${followUpList.length})` }}</span>
                        </template>
                        <div class="content">
                            <el-table-extend ref="mainTable" storageKey="visitedTable" :iModuleId="iModuleId"
                                oncontextmenu='return false' :data="followUpList" highlight-current-row height="100%">
                                <el-table-column type="index" label="序号" prop="_index" align="center" width="60">
                                    <template v-slot="scope">
                                        <span>{{ scope.$index + 1 }}</span>
                                    </template>
                                </el-table-column>
                                <template v-for="(item, index) in Configs.visitedTable" :key="index">
                                    <el-table-column :prop="item.sProp" :label="item.sLabel" :fixed="item.sFixed"
                                        :align="item.sAlign" :width="item.sWidth" :show-overflow-tooltip="true"
                                        :min-width="item.sMinWidth">
                                        <template v-slot="scope">
                                            <template v-if="item.sProp === 'dFollowDate'">
                                                {{ transformDate(scope.row.dFollowDate) }}
                                            </template>
                                            <template v-else>
                                                {{ scope.row[item.sProp] }}
                                            </template>
                                            <template v-if="item.sProp === 'Actions'">
                                                <el-button v-auth="'report:followup:edit'" title="编辑"
                                                    @click="onEdit(scope.row)" type="primary" link size="small">
                                                    <template #icon>
                                                        <Icon name="el-icon-edit" color="">
                                                        </Icon>
                                                    </template>
                                                    编辑
                                                </el-button>
                                                <el-button v-auth="'report:followup:delete'" title="删除"
                                                    @click="onClickDelete(scope.row, scope.$index)" type="default" link
                                                    size="small">
                                                    <template #icon>
                                                        <Icon name="el-icon-delete" color="">
                                                        </Icon>
                                                    </template>
                                                    删除
                                                </el-button>
                                            </template>
                                        </template>
                                    </el-table-column>
                                </template>
                            </el-table-extend>
                        </div>
                    </el-tab-pane>
                    <el-tab-pane label="病理报告" name="pathological">

                        <template v-slot:label>
                            <span>病理报告</span>
                            <span class="i-count">({{ PathologicalNum }})</span>
                        </template>
                        <div class="content">
                            <Pathological ref="PathologicalRef" :paramsData="patientInfo" @updateLoading="updateLoading" @getPathologicalNum="getPathologicalNum">
                            </Pathological>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
        <template #footer>
            <div style="text-align: right">
                <el-button-icon-fa v-if="activeTab == 'pathological'" _icon="el-icon-refresh-left"
                    @click="handleResetPathologicalForm">清空</el-button-icon-fa>

                <el-button-icon-fa v-if="activeTab != 'visitedRecord'" type="primary" _icon="fa fa-save" :loading="saveLoading"
                    @click="handleSaveData">保存</el-button-icon-fa>
                    
                <el-button-icon-fa _icon="fa fa-close-1" @click="emits('update:dialogVisible', false)">关闭</el-button-icon-fa>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { provide } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import Configs from '$supersetViews/apricot/followUpVisitMng/config/visiteForm'
import Pathological from '$supersetViews/apricot/followUpVisitMng/components/Pathological.vue'

import { getOptionName, transformDate } from '$supersetResource/js/tools'
import { addFollowupRecord, editFollowupRecord, delFollowupRecord } from '$supersetApi/projects/apricot/followupVisits/index.js'
import { getPatientResultById } from '$supersetApi/projects/apricot/case/report.js'
import { getAllUsersData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
//获取
const _store = useStore();

const emits = defineEmits(['update:dialogVisible', 'getFollowList'])
const props = defineProps({
    dialogVisible: {
        type: Boolean,
        default: false
    },
    userInfo: {
        type: Object,
        default: () => ({ })
    },
    patientInfo: {
        type: Object,
        default: () => ({ })
    },
    selectedVisitRow: {
        type: Object,
        default: () => ({ })
    },
    followList: {
        type: Object,
        default: () => []
    }
});

const iModuleId = 9;    // 随访模块标识
var isEdit = ref(false);    // 是否编辑
const storageKey = 'ReperIndexVisiteForm';  // 表单存储标识
var followForm = ref({});   // 表单数据
var activeTab = ref('visitForm');   // 当前激活tab
var reportContent = ref({});    // 报告内容
var followRecordInput = ref([...Configs.followRecordInput]);    // 表单配置
let PathologicalNum = ref(0);   // 病理报告数量
var saveLoading = ref(false);   // 加载状态
var optionsLoc = reactive({
    iIsAccordOptions: [{
        sName: '符合',
        sValue: 1
    },
    {
        sName: '不符合',
        sValue: 2
    }],
    sFollowModeOptions: _store.getters['dict/map'].ApricotReportFollowMode || [],
    sDoctorIdOptions: [],
    sVisitStateOptions: _store.getters['dict/map'].ApricotReportFollowUp || []
})


const followUpList = computed(() => props.followList);  // 随访表格数据

let patientInfo = reactive({});
patientInfo = computed(() => {
    return props.patientInfo
})
provide('patientInfo', patientInfo.value);

const visible = computed({
    get: function () {
        return props.dialogVisible
    },
    set: function (val) {
        emits('update:dialogVisible', val)
    }
})


const formRef = ref(null)
const PathologicalRef = ref(null)

// 监听属性
watch(visible, (val) => {
    if (val) {
        getPatientResultDataById(patientInfo.value.sId)
        followForm.value = Object.assign({}, props.selectedVisitRow)
        nextTick(() => {
            formRef.value.clearValidate()
        })
    }
})

// 清空病理表单
const handleResetPathologicalForm = () => {
    PathologicalRef.value.onResetForm();
}

// 保存
const handleSaveData = () => {
    if(activeTab.value == 'visitForm') {
        saveFollowupRecord()
    } else if(activeTab.value == 'pathological') {
        PathologicalRef.value.saveData()
    }
}

// 更新加载状态
const updateLoading = () => {
    saveLoading.value = false
}

// 保随访记录
const saveFollowupRecord = () => {
    saveLoading.value = true
    formRef.value.validate(valid => {
        if (!valid) {
            ElMessage({
                message: '填写正确信息',
                type: 'warning'
            });
            saveLoading.value = false
            return false
        }
        let data = followForm.value;
        data.sPatientId = patientInfo.value.sId;     //id赋值
        data.sIsAccordText = getOptionName(data['iIsAccord'], optionsLoc.iIsAccordOptions);
        data.sFollowModeText = getOptionName(data['sFollowMode'], optionsLoc.sFollowModeOptions);
        data.sVisitStateText = getOptionName(data['sVisitState'], optionsLoc.sVisitStateOptions);
        let target = optionsLoc.sDoctorIdOptions.find(item => item.sValue == followForm.value.sDoctorId)
        data.sDoctorName = target.sName
        data.sDoctorNo = target.sUserNo
        editFollowupRecord(data).then(res => {
            saveLoading.value = false
            if (res.success) {
                followForm.iVersion = res.data.iVersion;
                ElMessage.success(res.msg);
                // 保存成功后刷新表格
                emits('getFollowList', patientInfo.value.sId, false);
                return
            }
            ElMessage.error(res.msg);
        }).catch(err => {
            saveLoading.value = false
            console.log(err)
        })
    });
}

// 编辑行
const onEdit = (row) => {
    followForm.value = Object.assign({}, row)
    activeTab.value = 'visitForm'
}
// 删除行
const onClickDelete = (row, index) => {
    ElMessageBox.confirm('您确定要删除该行吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        let jsonData = {
            iVersion: row.iVersion,
            sId: row.sId
        }
        delFollowupRecord(jsonData).then(res => {
            let { success, msg } = res;
            if (success) {
                ElMessage.success(msg);
                emits('getFollowList', patientInfo.value.sId, false);
                if(followForm.value.sId === row.sId) {
                    // 清空表单
                    followForm.value = {}
                }
                return
            }
            ElMessage.error(msg);
        }).catch(err => {
            console.log(err)
        })
    }).catch(() => { });
}

const getPatientResultDataById = (id) => {
    getPatientResultById({ sPatientId: id }).then(res => {
        if (res.success) {
            reportContent.value = res.data || {}
            return;
        }
        ElMessage.error(res.msg)
    })
}

const getPathologicalNum= (val) => {
    PathologicalNum.value = val
}

onBeforeMount(async () => {
    optionsLoc.sDoctorIdOptions = await getAllUsersData()
})
</script>
<style lang="scss" scoped>
.my-dialog {
    :deep(.el-form-item__error) {
        top: 19px;
        left: 19px;
    }
}

.g-content {
    height: 100%;
    overflow: hidden;

    .c-item-01 {
        padding: 0;

        :deep(.el-form-item__error) {
            top: 19px;
            left: 19px;
        }
    }

    .c-report {
        // width: 350px;
        // height: auto;
        width: 35%;
        height: calc(100% - 10px);
        border: 1px solid #eee;

        // padding: 0 15px;
        .c-item {
            display: flex;
            flex-direction: column;
            overflow: auto;

            &.t-1 {
                flex: 3;
                border-bottom: 1px solid #ddd;
            }

            &.t-2 {
                flex: 2;
            }

            >p {
                margin: 0;
                padding: 10px 5px;
                background-color: var(--el-color-primary-light-8);
                text-indent: 10px;
                font-weight: bold;
                // border-top: 1px solid #eee;
                // border-bottom: 1px solid #eee;
                // margin-bottom: 0;
            }

            .i-item {
                overflow: auto;
                padding: 10px;
                white-space: break-spaces;
                word-break: break-all;
                // background-color: #f1f1f1;
            }
        }

        * {
            white-space: break-spaces;
            word-break: break-word;
        }
    }

    .c-follow-content {
        padding-bottom: 10px;

        .g-flexChild {
            overflow: hidden;
        }
    }

    .t-right {
        margin-left: 10px;
        padding: 10px 0;
        border-top: 1px solid #eee;
        text-align: right;
    }

}

.minHeight {
    padding-left: 10px;
    // height: 100;
}

.content {
    padding: 10px;
    height: 100%;
    box-sizing: border-box;
}

.i-delete {
    cursor: pointer;
}

.flex-x {
    display: flex;
    flex-wrap: wrap;

    .list-item {
        display: flex;
        align-items: center;
        line-height: 36px;

        .label {
            color: #838a9d;
            text-align: right;
            width: 100px;
        }
    }
}
</style>
