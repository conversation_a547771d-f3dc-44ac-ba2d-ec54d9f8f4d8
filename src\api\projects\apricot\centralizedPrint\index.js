import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'

export function printOutList(data) {
    return request({
        url: baseURL.apricot + '/printout/printList',
        method: 'POST',
        data
    })
}
export function printOutReportPath(data) {
    return request({
        url: baseURL.apricot + '/printout/reportPath',
        method: 'POST',
        data: stringify(data)
    })
}

export function printOutImgReport(data) {
    return request({
        url: baseURL.apricot + '/printout/imgReport',
        method: 'POST',
        data: stringify(data)
    })
}
export function printOutMarkPrint(data) {
    return request({
        url: baseURL.apricot + '/printout/markPrint',
        method: 'POST',
        data: stringify(data)
    })
}