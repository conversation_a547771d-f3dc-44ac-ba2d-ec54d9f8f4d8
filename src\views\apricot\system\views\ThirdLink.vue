<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa> 
            </div>
            
            <div class="c-form-search">
                <div>
                    <el-select v-model="condition.sLinkTypeCode" 
                        style="width: 100%"
                        clearable
                        @change="mxDoSearch()">
                        <el-option v-for="(item,index) in typeOption"
                            :key="index"
                            :label="item.sLabel"
                            :value="item.sValue"></el-option>
                    </el-select>
                </div>
                <div>
                    <el-input v-model="condition.sLinkTypeName"
                            clearable
                            placeholder="名称"
                            @keyup.enter.native="mxDoSearch()">
                        </el-input>
                </div>
                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch" :loading="loading"></el-button-icon-fa>
                </div>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content">
                <!-- @row-click="handleRowClick" -->
                <el-table :data="filteredTableData"
                    id="itemTable"
                    ref="mainTable"
                    size="small"
                    
                    border
                    stripe
                    height="100%"
                    style="width: 100%"
                    v-loading="loading">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort"
                        >
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button size="small" link type="primary" @click="handleEdit(scope.row)">
                                    编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class @click="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <!-- <i class="el-icon-rank i-sort" style="cursor: pointer;font-size: 16px; margin-left:10px"></i> -->
                            </template>
                            <template v-else-if="item.sProp === 'sLinkTypeCode'">
                                {{ scope.row.sLinkTypeCode == 1?'外部链接':'资源下载'  }}
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                        <!-- <template 
                            v-slot="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>      
        </div>
        <el-dialog :title="dialogTitle"
            v-model="dialogVisible"
            append-to-body
            class="t-default"
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex">
                <el-form :model="editLayer.form"
                    ref="refEditLayer"
                    label-width="100px"
                    :rules="rules">
                    <el-col :span="24">
                        <el-form-item prop="sLinkTypeCode" label="链接类型：">
                            <el-radio-group v-model="editLayer.form.sLinkTypeCode" >
                                <el-radio label="1">外部链接</el-radio>
                                <el-radio label="2">资源下载</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    
                    <template v-if="editLayer.form.sLinkTypeCode === '1'">
                        <el-col :span="24">
                            <el-form-item prop="sCallType" label="调用类型：">  
                                <el-radio-group v-model="editLayer.form.sCallType" >
                                    <el-radio v-for="item in optionsLoc.sCallTypeOptions"
                                        :key="item.sValue" 
                                        :label="item.sValue">{{item.sName}}</el-radio>
                                </el-radio-group>  
                                <!-- <el-select v-model="editLayer.form.sCallType"
                                    placeholder="请选择调用类型"
                                    style="width:100%">
                                    <el-option v-for="item in optionsLoc.sCallTypeOptions"
                                        :key="item.sValue"
                                        :label="item.sName"
                                        :value="item.sValue"></el-option>
                                </el-select> -->
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="名 称：" prop="sLinkTypeName">
                                <el-input v-model="editLayer.form.sLinkTypeName"
                                    clearable
                                    placeholder="请输入名称"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="sLinkURL" label="链接地址：">
                                <el-input v-model="editLayer.form.sLinkURL"
                                    clearable
                                    placeholder="请输入链接地址"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" >
                            <el-form-item label="门诊参数：" prop="sOutPatientParam">
                                <el-input v-model="editLayer.form.sOutPatientParam"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入门诊参数"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" >
                            <el-form-item label="住院参数：" prop="sInPatientParam">
                                <el-input v-model="editLayer.form.sInPatientParam"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入住院参数"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" >
                            <el-form-item label="急诊参数：" prop="sEmergencyParam">
                                <el-input v-model="editLayer.form.sEmergencyParam"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入急诊参数"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24" >
                            <el-form-item label="体检参数：" prop="sExamParam">
                                <el-input v-model="editLayer.form.sExamParam"
                                    type="textarea"
                                    :rows="2"
                                    placeholder="请输入体检参数"></el-input>
                            </el-form-item>
                        </el-col>
                    </template>
                    <!-- 资源下载 -->
                    <template v-if="editLayer.form.sLinkTypeCode === '2'">
                        <el-col :span="24">
                            <el-form-item prop="name" label="名 称：">
                                <el-input v-model="editLayer.form.name"
                                    clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item prop="fileName" label="上传文件：">
                                <el-input v-model="editLayer.form.fileName">
                                    <template #append>
                                        <el-upload 
                                            ref="upload"
                                            action="#"
                                            :show-file-list="false"
                                            :on-progress="handleUploadFile">
                                            <el-button-icon-fa 
                                                class="text-color"
                                                type="primary"
                                                _icon="el-icon-upload">上传</el-button-icon-fa>
                                        </el-upload>
                                    </template>
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </template>
                </el-form>
                
            </div>
            <template #footer><div >
                <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保 存</el-button-icon-fa>
                <el-button-icon-fa  @click="closeDialog" icon="el-icon-close">取 消</el-button-icon-fa>
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
// import Sortable from 'sortablejs'
import Api from '$supersetApi/projects/apricot/system/thirdLink.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'ThirdLink',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            dialogVisible:false,
            dialogTitle:'',
            applyDropCondition:[{sProp:'sLinkTypeName',sLabel:'名称'}],
            selectKey:'sLinkTypeName',
            loading: false,
            typeOption:[
                {
                    sLabel:'全部',
                    sValue:''
                },
                {
                    sLabel:'外部链接',
                    sValue:'1'
                },
                {
                    sLabel:'资源下载',
                    sValue:'2'
                }
            ],
            defaultProps:{
                children: 'children',
                label: 'label'
            },
            configTable: [{
                sProp:'sLinkTypeCode',
                sLabel: '链接类型',
                sAlign: 'center',
                sMinWidth: '50px',
            },
                {
                sProp: 'sCallType',
                sLabel: '调用类型',
                sAlign: 'left',
                sMinWidth: '50px',
                includeStr: '1'
            },
            {
                sProp: 'sLinkTypeName',
                sLabel: '链接名称',
                sAlign: 'left',
                sMinWidth: '100px',
                includeStr: '1,2'
            },
            {
                sProp: 'sLinkURL',
                sLabel: '链接地址',
                sAlign: 'left',
                sMinWidth: '100px',
                includeStr: '1,2'
            },
            {
                sProp: 'sOutPatientParam',
                sLabel: '门诊参数',
                sAlign: 'left',
                sMinWidth: '100px',
                includeStr: '1'
            },
             {
                sProp: 'sInPatientParam',
                sLabel: '住院参数',
                sAlign: 'left',
                sMinWidth: '100px',
                includeStr: '1'
            },
             {
                sProp: 'sEmergencyParam',
                sLabel: '急诊参数',
                sAlign: 'left',
                sMinWidth: '100px',
                includeStr: '1'
            },
             {
                sProp: 'sExamParam',
                sLabel: '体检参数',
                sAlign: 'left',
                sMinWidth: '100px',
                includeStr: '1'
            },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '170px',
                includeStr: '1,2'
            }],
            rules: {
                sCallType: [{ required: true, message: '调用类型不能为空',trigger:'change' }],
                sLinkTypeCode: [{ required: true, message: '不能为空',trigger:'change'}],
                sLinkTypeName: [{ required: true, message: '链接名称不能为空',trigger:'blur'}],
                sLinkURL: [{ required: true, message: '链接地址不能为空',trigger:'blur' }],
                fileName: [{ required: true, message: '文件名不能为空',trigger:'blur' }],
                name: [{ required: true, message: '名称不能为空',trigger:'blur' }],
            },
            condition: {sLinkTypeCode:''},
            optionsLoc: {
                sCallTypeOptions: [{
                    sName: 'web_get请求',
                    sValue: 'web_get'
                    },{
                    sName: 'web_post请求',
                    sValue: 'web_post'
                    }, {
                    sName: 'client_post请求',
                    sValue: 'client_post'
                    }, {
                    sName: 'client_get请求',
                    sValue: 'client_get'
                }]
            },
            fileBase64: '',
            defualtVal: {
                editLayer: {
                    sLinkTypeCode:'1',
                    sCallType:'web_get'
                },
            },
            
        }
    },
    computed: {
      filteredTableData() {
        const string = this.condition.sLinkTypeName || ''
        if(!string) return this.tableData
        return this.tableData.filter(item => {
          const name = item.sLinkTypeName || ''
          return name.indexOf(string) > -1
        })
      }
    },
    methods: {
        onChangeConditionKey(val) {
            this.selectKey = val
        },
      // 新增
        handleAdd() {
            this.actionState = 1
            this.dialogVisible = true
            this.dialogTitle = '新增'
            this.mxOpenDialog(1,'no-title')
            // let params = {
            //     sLinkTypeCode:'1',
            //     sCallType:'web_get'
            // }     
            // this.editLayer.form = Object.assign({},params)
            // this.$nextTick( ()=>{
            //     this.$refs['refEditLayer'].clearValidate();
            // })
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            if(row.sLinkTypeCode == 2) {
                row.name = row.sLinkTypeName;
                row.fileName = row.sLinkURL
            }
            this.editLayer.form = Object.assign({},row)
            this.$nextTick( ()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
        },
        handleSave() {
            this.editLayer.loading = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)
                  return
                }
                this.editLayer.loading = false
            })
        },
        // 行点击事件
        // handleRowClick (row, isCancel = false, id = 'sId') {
        //     if(this.condition.sLinkTypeCode == 2) {
        //         row.name = row.sLinkTypeName;
        //         row.fileName = row.sLinkURL
        //     }
        //     this.onClickRow(row, isCancel, id);
        //     this.mxOpenDialog(4, '111')
        // },
        // 上传文件
        handleUploadFile (e, file) {
            const fileObj = file.raw;
            this.fileBase64 = new FormData();
            this.fileBase64.append('file', fileObj);
            this.editLayer.form['fileName'] = file.name;
            // this.getBase64(fileObj).then(res => {
            //     this.editLayer.form['fileName'] = file.name;
            //     let data = res.split(';base64,')[1];
            //     this.fileBase64 = data;
            //     console.log(this.fileBase64)
            //     // console.log(res);
            // });
        },
        // 然后自定义一个方法，用来把图片内容转为base64格式，
        // imgResult就是base64格式的内容了。
        // 转为base64字符串要调用h5特性中的FileReader这个api,
        // 但是这个api不能return，所以用promise封装一下。
        getBase64 (file) {
            return new Promise(function (resolve, reject) {
                let reader = new FileReader();
                let imgResult = "";
                reader.readAsDataURL(file);
                reader.onload = function () {
                    imgResult = reader.result;
                };
                reader.onerror = function (error) {
                    reject(error);
                };
                reader.onloadend = function () {
                    resolve(imgResult);
                };
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            if(this.editLayer.form.sLinkTypeCode == 2) {
                this.editLayer.loading = false;
                let jsonData = this.fileBase64;
                if(!jsonData) {
                    jsonData = new FormData();
                }
                jsonData.append('name', this.editLayer.form.name);
                this.editLayer.form.iId &&  jsonData.append('iId', this.editLayer.form.iId);
                let loading = this.$loading({
                    lock: true,
                    text: '上传中,请稍等...',
                    
                    background: 'rgba(0, 0, 0, 0.2)'
                });
                Api.saveThirdLinkResources(jsonData).then(res => {
                    loading.close();
                    if(res.success) {
                        this.dialogVisible = false
                        this.mxOpenDialog(1, 'no-title');
                        this.fileBase64 = '';
                        this.mxGetTableList();
                        return
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(err => {
                    console.log(err);
                    loading.close()
                })
                return
            }
            if (this.actionState == 1) {
                Api.addThirdLink(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.dialogVisible = false
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // this.mxOpenDialog(1, 'no-title');
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return
            } 
            Api.editThirdLink(params).then((res) => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.dialogVisible = false
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    // this.mxOpenDialog(1, 'no-title');
                    this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【${row.sLinkTypeName}】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: "warning"
            }).then(() => {
                Api.delThirdLink({ iId: row.iId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        if (this.editLayer.form.iId && this.editLayer.form.iId === row.iId) {
                            this.mxOpenDialog(1, 'no-title')
                        }
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },

        // 获取表格数据
        getData (params) {
            const p = Object.assign({}, params, { page: { pageSize: 500 } })
            delete p.orders
            Api.getThirdLinkData(p).then((res) => {
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.loading = false;
                    // 赋选中状态
                    // this.mxSetSelected();
                    return
                }
                this.$message.error(res.msg)
            }).catch(() => {
                this.loading = false;
            })
        }
    },
    mounted () { },
};
</script>
<style lang="scss" scoped>



.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px;
    :deep(.c-form) {
        display: flex;
        padding-bottom: 10px;
        justify-content: space-between;

        .c-form-search {
            min-width: 400px;
            width: 36%;
            display: flex;
            >div {
                width: 50%;
                margin: 0 5px;
            }
        }
        .el-textarea__inner {
            border-color: #dcdfe6;
        }
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-content {
            flex: 1;
            height: 0px;
        }
    }

}

.text-color {
    color: var(--el-color-primary) !important;
    &:active {
        color: white !important;
    }
}
</style>

