import Api from "$supersetApi/projects/common";  // 码表
import store from '$supersetStore'
const mixinCodeTable = {
    data() {
        return {
            optionsLoc: {},
            // options: {},
        }
    },
    methods: {
        /**
         * 获取 name 值
         */
        mxGetName(obj, value){
            let result = ''
            for (const e of obj) {
                if (e.sValue == value){
                    result = e.sName
                    break;
                }
            }
            return result
        },
        // 获取码表下拉，并且存入到 vuex 中，
        async mxGetCodeTable(name) {

            if (typeof name !== 'string'){
                name.forEach(key => {
                    this.mxGetCodeTable(key)
                });
                return;
            }
            // vuex 中取码表
            // const codeTable = store.state.user.codeTable[name];
            // const map = store.getters['dict/map'][name] || {};

            // const codeTable = Object.keys(map).map(key => {
            //   return {
            //     sValue: key,
            //     sName: map[key]
            //   }
            // })
            this.optionsLoc[name] = store.getters['dict/map'][name] || []

            // if (codeTable){
            //     // 已经码表，直接返回，结束
            //     this.optionsLoc[name] = codeTable
            //     return codeTable;
            // }
            // 码表
            // this.optionsLoc[name] = this.optionsLoc[name] || []
            // await Api.dictionaryGroup({ sGroupName: name }).then(e => {
            //     if (e.success) {
            //         this.optionsLoc[name] = e.data

            //         // 存入 vuex 中
            //         store.commit('user/setCodeTable', {
            //             key: name,
            //             value: e.data
            //         })
            //     }
            // })
            // .catch(() => {});
        },
    }
}
export default mixinCodeTable;
