import request, {baseURL, stringify} from '$supersetUtils/request'

export default {
    getCallCaptionsData,
    addCallCaptions,
    editCallCaptions,
    delCallCaptions,
    tovoiceCallCaptions,
    getModuleCallBtn,
    saveModuleCallBtn,
    getWorkStations,
    getSreenList,
    getModuleSreen,
    addLinkScreen,
    delScreenLink,
    getCallCaptionsType,
    getCallButtonSetOfModule,
    workStationSyncSet
}

// 呼叫内容列表
export function getCallCaptionsData(params) {
    return request({
        url: baseURL.apricot + '/call/captions/get/captions',
        method: 'POST',
        data: stringify(params)
    })
}

// 新增
export function addCallCaptions(data) {
    return request({
        url: baseURL.apricot + '/call/captions/add',
        method: 'POST',
        data
    })
}
// 修改
export function editCallCaptions(data) {
    return request({
        url: baseURL.apricot + '/call/captions/edit',
        method: 'POST',
        data
    })
}
// 删除
export function delCallCaptions(params) {
    return request({
        url: baseURL.apricot + '/call/captions/del',
        method: 'POST',
        data: stringify(params)
    })
}
// // 自动排序
// export function autoSortCallCaptions(params) {
//     return request({
//         url: baseURL.apricot + '/call/captions/autoSort',
//         method: 'POST',
//         data: stringify(params)
//     })
// }
// // 排序
// export function sortCallCaptions(params) {
//     return request({
//         url: baseURL.apricot + '/call/captions/sort',
//         method: 'POST',
//         data: stringify(params)
//     })
// }

// 转语音
export function tovoiceCallCaptions(params) {
    return request({
        url: baseURL.apricot + '/call/captions/tovoice',
        method: 'POST',
        data: stringify(params)
    })
}
// 获取模块呼叫按钮
export function getModuleCallBtn(params) {
    return request({
        url: baseURL.apricot + '/callButtonSet/getCallButtonSetOfModule',
        method: 'POST',
        data: stringify(params)
    })
}
// 保存模块呼叫按钮 
export function saveModuleCallBtn(data) {
    return request({
        url: baseURL.apricot + '/callButtonSet/saveCallButtonSet',
        method: 'POST',
        data
    })
}
// 
// 呼叫列表
export function callScreenCallList(params) {
    return request({
        url: baseURL.apricot + '/call/screen/callList',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取院区工作站树
export function getWorkStations() {
    return request({
        url: baseURL.apricot + '/work/station/getWorkStationGroupByDistrict',
        method: 'POST',
    })
}

// 获取所属院区显示屏
export function getSreenList(data) {
    return request({
        url: baseURL.apricot + '/call/screen/find/page',
        method: 'POST',
        data
    })
}

///report/call/screen/find/page
// 获取勾选的显示屏
export function getModuleSreen(params) {
    return request({
        url: baseURL.apricot + '/call/push/set/list',
        method: 'POST',
        data: stringify(params)
    })
}

// 关联/关闭屏幕推送
export function addLinkScreen(data) {
    return request({
        url: baseURL.apricot + '/call/push/set/add',
        method: 'POST',
        data
    })
}
// 关闭屏幕推送
export function delScreenLink(params) {
    return request({
        url: baseURL.apricot + '/call/push/set/del',
        method: 'POST',
        data:stringify(params)
    })
}
// 获取语音分类
export function getCallCaptionsType() {
    return request({
        url: baseURL.apricot + '/call/captions/getCallCaptionsType',
        method: 'POST', 
    })
}
// 获取患者列表显示的呼叫按钮
export function getCallButtonSetOfModule(params) {
    return request({
        url: baseURL.apricot + '/callButtonSet/getCallButtonOfPatientList',
        method: 'POST', 
        data:stringify(params)
    })
}
// 同步语音设置

export function workStationSyncSet(params) {
    return request({
        url: baseURL.apricot + '/call/captions/workStationSyncSet',
        method: 'POST', 
        data:stringify(params)
    })
}