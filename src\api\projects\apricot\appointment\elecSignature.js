import request, { baseURL, stringify } from '$supersetUtils/request'
export default {
    // 电子签章管理操作接口: Elec Signature Controller
    getData(data) {
        return request({
            url: baseURL.apricot + '/elec/signature/find/page',
            method: 'POST',
            data
        })
    },
    addSignature(data) {
        return request({
            url: baseURL.apricot + '/elec/signature/add',
            method: 'POST',
            data
        })
    },
    editSignature(data) {
        return request({
            url: baseURL.apricot + '/elec/signature/edit',
            method: 'POST',
            data
        })
    },
    delSignature(params) {
        return request({
            url: baseURL.apricot + '/elec/signature/del',
            method: 'POST',
            params
        })
    },
    getUserByNo(params) {
        return request({
            url: baseURL.system + '/user/getUserByNo',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 上传
    uploadFile(data) {
        return request({
            url: baseURL.apricot + '/template/upload',
            method: 'POST',
            data
        })
    }
    
}