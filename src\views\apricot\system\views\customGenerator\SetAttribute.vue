<template>
    <div class="right-plane">
        <el-tabs v-model="currentTab" type="border-card" class="center-tabs">
            <el-tab-pane label="组件属性" name="field" />
            <el-tab-pane label="默认属性" name="default" />
        </el-tabs>
        <div class="field-box" v-show="currentTab === 'field'">
            <el-scrollbar class="scrollbar xx-el-scrollbar">
                <!-- defaultAttribute 表单 -->
                <template v-if="activeData.layout === 'formItem'">
                    <el-form label-width="70px">
                        <el-form-item label="组件类型">
                            <el-select
                                v-model="activeData.tag"
                                placeholder="请选择组件类型"
                                @change="tagChange" >
                                <el-option v-for="item in tagList"
                                    :label="item.label"
                                    :value="item.tag" ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="字段">
                            <el-autocomplete
                                style="width: 200px;"
                                value-key="sField"
                                popper-class="my-autocomplete-consult"
                                @select="onChangeField"
                                v-model="activeData.field"
                                :fetch-suggestions="querySearch"
                                placeholder="请输入内容" >
                                <template v-slot="{ item }">
                                    <div class="name">{{ item.sTitle }}</div>
                                    <span class="addr">{{ item.sField }}</span>
                                </template>
                            </el-autocomplete>
                        </el-form-item>
                        <el-form-item label="标题">
                            <el-input v-model="activeData.label"></el-input>
                        </el-form-item>
                        <el-form-item label="内容值" v-if="!valueNots.includes(activeData.tag)">
                            <el-input v-model="activeData.value"></el-input>
                        </el-form-item>
                        <el-form-item v-if="activeData.span !== undefined" label="占据宽度">
                            <div style="display: flex; align-items: center; flex: 1;">
                                <el-slider style="flex: 1;" v-model="activeData.span" :step="1" :min="1" :max="24" :marks="{6:'', 12:'', 18:''}"></el-slider> 
                                <span style="width: 26px;text-align: right;">{{ activeData.span }}</span>
                            </div>
                        </el-form-item>
                        <el-form-item v-if="activeData.placeholder !== undefined" label="占位提示">
                            <el-input v-model="activeData.placeholder" @change="onChnagePlaceholder"/>
                        </el-form-item>
                        <el-form-item label="显示标题" v-if="activeData.titleShow !== undefined">
                            <el-switch v-model="activeData.titleShow"/>
                        </el-form-item>
                        <el-form-item label="标题宽度">
                            <el-input-number v-model="activeData.labelWidth" :min="10" placeholder="100" controls-position="right" />
                        </el-form-item>
                        <el-form-item v-if="activeData.rows !== undefined" label="行数">
                            <el-input-number v-model="activeData.rows" :min="1" placeholder="行数" controls-position="right" />
                        </el-form-item>
                        <el-form-item v-if="activeData.clearable !== undefined" label="显示清除">
                            <el-switch v-model="activeData.clearable" />
                        </el-form-item>
                        <el-form-item v-if="activeData.readonly !== undefined" label="是否只读">
                            <el-switch v-model="activeData.readonly" />
                        </el-form-item>
                        <el-form-item v-if="activeData.disabled !== undefined" label="是否禁用">
                            <el-switch v-model="activeData.disabled" />
                        </el-form-item>

                        <!-- 多行文本 -->
                        <el-form-item v-if="activeData.maxlength !== undefined" label="最大字数">
                            <el-input-number v-model="activeData.maxlength" controls-position="right" />
                        </el-form-item>
                        <el-form-item v-if="activeData.showWordLimit !== undefined" label="字数统计">
                            <el-switch v-model="activeData.showWordLimit" />
                        </el-form-item>

                        <!-- 勾选输入框 -->
                        <el-form-item label="输入勾选" v-if="activeData.checkStyle !== undefined">
                            <el-switch v-model="activeData.checkStyle"/>
                        </el-form-item>

                        <!-- 计数器 -->
                        <el-form-item v-if="activeData.min !== undefined" label="最小值">
                            <el-input-number v-model="activeData.min" controls-position="right" />
                        </el-form-item>
                        <el-form-item v-if="activeData.max !== undefined" label="最大值">
                            <el-input-number v-model="activeData.max" controls-position="right" />
                        </el-form-item>
                        <el-form-item v-if="activeData.step !== undefined" label="步长">
                            <el-input-number v-model="activeData.step" controls-position="right" />
                        </el-form-item>
                        <el-form-item v-if="activeData.stepStrictly !== undefined" label="步长倍数">
                            <el-switch v-model="activeData.stepStrictly" />
                        </el-form-item>

                        <!-- 下拉选择 -->
                        <el-form-item v-if="activeData.multiple !== undefined" label="是否多选">
                            <el-switch v-model="activeData.multiple" @change="onChangeMultiple"/>
                        </el-form-item>

                        <el-form-item v-if="activeData.filterable !== undefined" label="可搜索">
                            <el-switch v-model="activeData.filterable"/>
                        </el-form-item>

                        <el-form-item v-if="activeData.allowCreate !== undefined" label="可创建">
                            <el-switch v-model="activeData.allowCreate" @change="onChangeallowCreate"/>
                        </el-form-item>

                        <el-form-item v-if="activeData.required !== undefined" label="是否必填">
                            <el-switch v-model="activeData.required"/>
                        </el-form-item>

                        <!-- 单选框 -->
                        <el-form-item v-if="activeData.optionType" label="按钮样式">
                            <el-radio-group v-model="activeData.optionType">
                                <el-radio-button label="default">默认</el-radio-button>
                                <el-radio-button label="button">按钮</el-radio-button>
                            </el-radio-group>
                        </el-form-item>

                        <div v-if="activeData.options">
                            <el-divider>选项内容</el-divider>
                            <draggable
                                v-model="activeData.options"
                                item-key="id"
                                :animation="340"
                                group="selectItem"
                                handle=".drag-btn">
                                <template #item="{ element: item }"> 
                                  <div class="select-item">
                                      <div class="drag-btn"><i class="el-icon-rank"></i></div>
                                      <el-input
                                      placeholder="选项值"
                                      v-model="item.value"
                                      @input="setOptionValue(item, $event)"
                                      />
                                      <div class="close-btn select-line-icon" @click="activeData.options.splice(index, 1)">
                                          <i class="el-icon-remove-outline" />
                                      </div>
                                  </div>
                                </template>
                            </draggable>
                            <el-button
                                type="primary" style="width: 100%;"
                                icon="el-icon-circle-plus-outline"
                                @click="addSelectItem">添加选项</el-button>
                        </div>
                        <el-form-item v-if="activeData.pickerOptions !== undefined" label="时间段">
                            <el-input
                            v-model="activeData.pickerOptions.selectableRange"
                            placeholder="请输入时间段"
                            />
                        </el-form-item>
                        <!-- 日期 -->
                        <el-form-item
                            v-if="activeData.type !== undefined && 'date-picker' === activeData.tag"
                            label="时间类型">
                            <el-select
                            v-model="activeData.type"
                            placeholder="请选择时间类型"
                            :style="{ width: '100%' }"
                            @change="dateTypeChange" >
                            <el-option
                                v-for="(item, index) in dateTypeOptions"
                                :key="index"
                                :label="item.label"
                                :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-if="activeData.format" label="显示格式">
                            <el-input v-model="activeData.format" placeholder="显示格式" @input="setTimeValue($event)" />
                        </el-form-item>

                        <div v-if="activeData.labelStyle">
                            <el-divider>标题样式</el-divider>
                            <el-form-item v-if="activeData.labelStyle.fontSize != undefined" label="字体大小">
                                <el-input v-model="activeData.labelStyle.fontSize" placeholder="字体大小"/>
                            </el-form-item>
                            <el-form-item v-if="activeData.labelStyle.fontWeight" label="字体粗细">
                                <el-radio-group v-model="activeData.labelStyle.fontWeight">
                                    <el-radio-button label="400">正常</el-radio-button>
                                    <el-radio-button label="600"><span style="font-weight: 600;">加粗</span></el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item v-if="activeData.labelStyle.color" label="字体颜色">
                                <el-color-picker v-model="activeData.labelStyle.color"></el-color-picker>
                            </el-form-item>
                        </div>

                        <div v-if="activeData.style">
                            <el-divider>输入样式</el-divider>
                            <el-form-item v-if="activeData.style.height != undefined" label="高度">
                                <el-input v-model="activeData.style.height"/>
                            </el-form-item>
                            <el-form-item v-if="activeData.style.fontSize != undefined" label="字体大小">
                                <el-input v-model="activeData.style.fontSize"/>
                            </el-form-item>
                            <el-form-item v-if="activeData.style.fontWeight" label="字体粗细">
                                <el-radio-group v-model="activeData.style.fontWeight">
                                    <el-radio-button label="400">正常</el-radio-button>
                                    <el-radio-button label="600"><span style="font-weight: 600;">加粗</span></el-radio-button>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item v-if="activeData.style.color" label="字体颜色">
                                <el-color-picker v-model="activeData.style.color"></el-color-picker>
                            </el-form-item>
                            <el-form-item v-if="activeData.style.background" label="背景颜色">
                                <el-color-picker v-model="activeData.style.background"></el-color-picker>
                            </el-form-item>
                        </div>

                    </el-form>
                </template>
                <!-- layoutComponents 布局 -->
                <template v-else-if="activeData.layout === 'layoutItem'">
                    <el-form label-width="70px">
                        <el-form-item label="显示导航">
                            <el-switch v-model="activeData.navigation" @change="onChangeNavigation"/>
                        </el-form-item>
                        <el-form-item label="标题">
                            <el-input v-model="activeData.label"></el-input>
                        </el-form-item>
                        <el-form-item v-if="activeData.tag === 'plane-box-title'" label="标题位置">
                            <el-radio-group v-model="activeData.titlePosition">
                                <el-radio-button label="left">左侧</el-radio-button>
                                <el-radio-button label="right">右侧</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="activeData.span !== undefined" label="表格布局">
                            <div style="display: flex; align-items: center; width: 100%;">
                                <el-slider style="flex: 1;" v-model="activeData.span" :step="1" :min="1" :max="24" :marks="{6:'', 12:'', 18:''}"></el-slider> <span style="width: 26px;text-align: right;">{{ activeData.span }}</span>
                            </div>
                        </el-form-item>
                        <!-- <div class="attr-box">
                            <el-tree
                                :data="{...activeData}"
                                :props="defaultProps"
                                @node-drag-enter="nodeDragEnter"
                                draggable
                                default-expand-all>
                                <template v-slot="{ node, data }">
                                    <span class="node-label">
                                        <i class="fa" :class="data.tagIcon"></i>
                                        {{ node.label }}
                                    </span>
                                </template>
                            </el-tree>
                        </div> -->
                    </el-form>
                </template>
                <!-- modifierComponents 其它 -->
                <template v-else-if="activeData.layout === 'modifierItem'">
                    <el-form label-width="70px">
                        <el-form-item v-if="activeData.span !== undefined" label="表格布局">
                            <div style="display: flex; align-items: center; width: 100%;">
                                <el-slider style="flex: 1;" v-model="activeData.span" :step="1" :min="1" :max="24" :marks="{6:'', 12:'', 18:''}"></el-slider> <span style="width: 26px;text-align: right;">{{ activeData.span }}</span>
                            </div>
                        </el-form-item>
                        <el-form-item v-if="activeData.label != null" label="标题">
                            <el-input v-model="activeData.label"></el-input>
                        </el-form-item>
                        <el-form-item v-if="activeData.label != null" label="显示导航">
                            <el-switch v-model="activeData.navigation" @change="onChangeNavigation"/>
                        </el-form-item>
                        <el-form-item v-if="activeData.height !== undefined" label="高度">
                            <el-input v-model="activeData.height"/>
                        </el-form-item>
                        <el-form-item v-if="activeData.backgroundColor" label="颜色">
                            <el-color-picker v-model="activeData.backgroundColor"></el-color-picker>
                        </el-form-item>
                        <el-form-item v-if="activeData.fontSize != undefined" label="字体大小">
                            <el-input-number v-model="activeData.fontSize" :min="12" controls-position="right" @change="onChangeFontSize" />
                        </el-form-item>
                        <el-form-item v-if="activeData.fontWeight" label="字体粗细">
                            <el-radio-group v-model="activeData.fontWeight">
                                <el-radio-button label="400">正常</el-radio-button>
                                <el-radio-button label="600"><span style="font-weight: 600;">加粗</span></el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="activeData.textAlign" label="显示位置">
                            <el-radio-group v-model="activeData.textAlign">
                                <el-radio-button label="left">左侧</el-radio-button>
                                <el-radio-button label="center">居中</el-radio-button>
                                <el-radio-button label="right">右侧</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="activeData.color" label="字体颜色">
                            <el-color-picker v-model="activeData.color"></el-color-picker>
                        </el-form-item>
                        <el-form-item v-if="activeData.marginTop !== undefined" label="上边距离">
                            <el-input-number v-model="activeData.marginTop" :min="0" controls-position="right" @change="onChangeMarginTop" />
                        </el-form-item>
                        <el-form-item v-if="activeData.marginBottom !== undefined" label="下边距离">
                            <el-input-number v-model="activeData.marginBottom" :min="0" controls-position="right" @change="onChangeMarginBottom" />
                        </el-form-item>
                    </el-form>
                </template>
                <div v-else class="tip">请选择</div>


            </el-scrollbar>
        </div>
        <div class="field-box" v-show="currentTab === 'default'">
            <el-scrollbar class="scrollbar xx-el-scrollbar">
                <el-form label-width="70px">
                    <el-form-item v-if="defaultAttribute.span !== undefined" label="表格布局">
                        <div style="display: flex; align-items: center; width: 100%;">
                            <el-slider style="flex: 1;" v-model="defaultAttribute.span" :step="1" :min="1" :max="24" :marks="{6:'', 12:'', 18:''}"></el-slider> <span style="width: 26px;text-align: right;">{{ activeData.span }}</span>
                        </div>
                    </el-form-item>
                    <el-form-item v-if="defaultAttribute.required !== undefined" label="是否必填">
                        <el-switch v-model="defaultAttribute.required"/>
                    </el-form-item>
                    <el-form-item label="显示标题" v-if="defaultAttribute.titleShow !== undefined">
                        <el-switch v-model="defaultAttribute.titleShow"/>
                    </el-form-item>
                    <div v-if="defaultAttribute.labelStyle">
                        <el-divider>标题样式</el-divider>
                        <el-form-item v-if="defaultAttribute.labelStyle.fontSize" label="字体大小">
                            <el-input v-model="defaultAttribute.labelStyle.fontSize" placeholder="字体大小"/>
                        </el-form-item>
                        <el-form-item v-if="defaultAttribute.labelStyle.fontWeight" label="字体粗细">
                            <el-radio-group v-model="defaultAttribute.labelStyle.fontWeight">
                                <el-radio-button label="400">正常</el-radio-button>
                                <el-radio-button label="600"><span style="font-weight: 600;">加粗</span></el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="defaultAttribute.labelStyle.color" label="字体颜色">
                            <el-color-picker v-model="defaultAttribute.labelStyle.color"></el-color-picker>
                        </el-form-item>
                    </div>
                    <div v-if="defaultAttribute.style">
                        <el-divider>输入框样式</el-divider>
                        <el-form-item v-if="defaultAttribute.style.height !== undefined" label="高度">
                            <el-input v-model="defaultAttribute.style.height"/>
                        </el-form-item>
                        <el-form-item v-if="defaultAttribute.style.fontSize !== undefined" label="字体大小">
                            <el-input v-model="defaultAttribute.style.fontSize" :minlength="12"/>
                        </el-form-item>
                        <el-form-item v-if="defaultAttribute.style.fontWeight" label="字体粗细">
                            <el-radio-group v-model="defaultAttribute.style.fontWeight">
                                <el-radio-button label="400">正常</el-radio-button>
                                <el-radio-button label="600"><span style="font-weight: 600;">加粗</span></el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="defaultAttribute.style.color" label="字体颜色">
                            <el-color-picker v-model="defaultAttribute.style.color"></el-color-picker>
                        </el-form-item>
                        <el-form-item v-if="defaultAttribute.style.background" label="背景颜色">
                            <el-color-picker v-model="defaultAttribute.style.background"></el-color-picker>
                        </el-form-item>
                    </div>
                </el-form>

            </el-scrollbar>
        </div>
    </div>
</template>
<script>
import { getCustomTemplateField } from '$supersetApi/projects/apricot/case/consult.js'
import draggable from 'vuedraggable';
import { fromComponents } from '../../config/generator.js';
export default {
    components: {
        draggable
    },
    emits: ['changeComponent', 'changeNavigation'],
    props: {
        activeData: {
            default: () => ({})
        },
        defaultAttribute: {
            default: () => ({})
        }
    },
    data() {
        return {
            currentTab: 'field',
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            dateTypeOptions: [
                { label: '日(date)', value: 'date' },
                { label: '周(week)', value: 'week' },
                { label: '月(month)', value: 'month' },
                { label: '年(year)', value: 'year' },
                { label: '日期时间(datetime)', value: 'datetime' }
            ],
            dateTimeFormat: {
                date: 'YYYY-MM-DD',
                week: 'YYYY 第 ww 周',
                month: 'YYYY-MM',
                year: 'YYYY',
                datetime: 'YYYY-MM-DD HH:mm:ss',
                daterange: 'YYYY-MM-DD',
                monthrange: 'YYYY-MM',
                datetimerange: 'YYYY-MM-DD HH:mm:ss'
            },
            valueNots: ['select', 'checkbox-group'],
            templateField: [],
        }
    },
    computed: {
        tagList() {
            return fromComponents;
        }
    },
    mounted() {
        this.getCustomTemplateField()
    },
    methods: {
        querySearch(queryString, cb) {
            // 调用 callback 返回建议列表的数据
            cb(this.templateField);
        },
        getCustomTemplateField() {
            getCustomTemplateField({}).then(res => {
                this.templateField = res.data || []
            })
        },
        // 下拉多选改变
        onChangeMultiple(val) {
            if (val) {
                this.activeData.value = [];
            }else {
                this.activeData.value = '';
            }
        },
        // 下拉多选，可填写改变
        onChangeallowCreate(val) {
            if (val) {
                this.activeData.filterable = true;
                if (!this.activeData.multiple) {
                    this.activeData.multiple = true;
                }
            }
        },
        nodeDragEnter(node, event) {
            console.log(node);
            console.log(event)
        },
        // 下拉添加选项
        addSelectItem() {
            this.activeData.options.push({
                value: ''
            })
        },
        // 下拉选择设置值，数值转换
        setOptionValue(item, val) {
            item.value = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(val) ? +val : val;
        },
        setTimeValue(val, type) {
            const valueFormat = type === 'week' ? this.dateTimeFormat.date : val;
            this.activeData['value'] = '';
            this.activeData['valueFormat'] = valueFormat;
            this.activeData['format'] = val;
        },
        dateTypeChange(val) {
            console.log(val)
            this.setTimeValue(this.dateTimeFormat[val], val);
        },  
        // 改变组件属性
        tagChange(formItem) {
            const item = this.tagList.find(item => item.tag === formItem);
            if (item) {
                this.$emit('changeComponent', item);
            }
            console.log(formItem);
        },
        // 显示隐藏导航
        onChangeNavigation() {
            this.$emit('changeNavigation');
        },

        // 输入框内容限制
        onChangeMarginBottom(val) {
            if (val === undefined) {
                this.activeData.marginBottom = 0;
            } 
        },
        onChangeMarginTop(val) {
            if (val === undefined) {
                this.activeData.marginTop = 0;
            }
        },
        onChangeFontSize(val) {
            if (val === undefined) {
                this.activeData.fontSize = 12;
            }
        },
        onChnagePlaceholder(val) {
            if(val === undefined) {
                this.activeData.placeholder = '';
            }
        },
        onChangeField(item) {
            this.activeData.label = item.sTitle;
        }
    }
}
</script>
<style lang="scss">
.my-autocomplete-consult{
    li {
        line-height: normal;
        padding: 7px;
        .name {
            text-overflow: ellipsis;
            overflow: hidden;
        }
        .addr {
            font-size: 12px;
            color: #b4b4b4;
        }

        .highlighted .addr {
            color: #ddd;
        }
    }
}
</style>
<style lang="scss" scoped>
.right-plane{
    height: 100%;
    display: flex;
    flex-direction: column;
    :deep(.el-form-item) {
        margin-bottom: 18px;
    }
    :deep(.el-form-item__content) {
        width: initial;
    }
}
:deep(.center-tabs){
    height: 38px;
    .el-tabs__header{
        margin-bottom: 0!important;
    }
    .el-tabs__item{
        width: 50%;
        text-align: center;
        padding: 0px;
    }
    .el-tabs__nav{
        width: 100%;
    }
}
.field-box{
    flex: 1;
    height: 0px;
    
}
.el-tabs{
    :deep(.el-tabs__header ){
        margin: 0;
    }
}
.scrollbar{
    height: 100%;
    :deep(.el-scrollbar__view){
        height: 100%;
    }
}
.el-form{
    height: 100%;
    padding: 20px 20px 0px 10px;
    box-sizing: border-box;
    .el-select{
        width: 100%;
    }
}
.tip{
    position: absolute;
    top: 40px;
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #ccc;
}
.attr-box{
    padding-top: 20px;
    border-top: 1px solid #eee;
}
.select-item{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .el-input{
        margin-right: 10px;
    }
    .drag-btn{
        font-size: 18px;
        padding-right: 10px;
    }
    .close-btn{
        display: block;
        font-size: 18px;
        cursor: pointer;
        color: #e36f6f;
    }
}
</style>
