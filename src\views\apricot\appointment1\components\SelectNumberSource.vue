<template>
    <div class="container">
        <h4 class="title">
            <span class="title-yyyy">{{ yyyy }}
                <span class="toDay"
                    @click="onClickToDay">今</span>
            </span>
            <span class="t-title">选择号源</span>
        </h4>
        <div class="header">
            <div class="action"
                @click="setWeekList(-7)">
                <el-icon :size="24">
                    <ArrowLeft />
                </el-icon>
            </div>
            <ul class="dates">
                <li v-for="item in dateArray"
                    :class="getDatesClass(item)"
                    @click="onClickDate(item.id)">
                    <div class="text-01">
                        <span>{{ item.mmdd }}</span>
                        <span class="dddd">{{ item.id === today ? '今天' : item.dddd }}</span>
                    </div>
                    <span>({{ item.registered || 0 }}/{{ item.limit || 0 }})</span>
                </li>
            </ul>
            <div class="action"
                @click="setWeekList(7)">
                <el-icon :size="24">
                    <ArrowRight />
                </el-icon>
            </div>
        </div>
        <div class="content"
            v-loading="loadingNumSource">
            <!-- <span v-if="isBeforeDay" class="tip">今天之前不可预约</span> -->
            <el-scrollbar class="my-scrollbar"
                ref="scrollbar"
                wrap-class="smooth-scrollbar">
                <div v-if="!hourItems.length"
                    style="display: flex;height: 100%;justify-content: center;align-items: center;">
                    <img src="../svg/empty.svg">
                </div>
                <div style="padding-right: 10px">
                    <div class="date-item"
                        v-for="(item, index) in hourItems"
                        :key="index">
                        <div class="time">{{ item.time }}</div>
                        <div class="sourse">
                            <el-col :sm="8"
                                :lg="6"
                                :xl="4"
                                class="col-box"
                                v-for="(grid, index) in item.gridItems"
                                :key="index">
                                <div :class="getItemClasses(grid)"
                                    class="sourse-box"
                                    :style="{ backgroundColor: configData.appointCardBgColor, 'border-color': configData.appointCardBgColor }"
                                    @click="onClickSelect(grid)">
                                    <div v-if="props.formData.sProjectId && grid.sItemId != props.formData.sProjectId && props.formData.sId"
                                        class="location badge anima-blink">项目变更</div>
                                    <div v-else
                                        class="location badge">当前预约</div>
                                    <!-- 存在号源，号源不等于提交的号源(改约)，当前号源等于点击提交的号源 -->
                                    <div class="badge"
                                        v-if="props.currentSelectInfo.numSourceId && props.currentSelectInfo.numSourceId != props.formData.numSourceId && grid.sId == props.formData.numSourceId">改约</div>
                                    <div class="recommend badge">推荐</div>

                                    <div v-if="grid.iReserved == 1 && grid.checkeStatus == 0"
                                        class="reserved badge-1">[预留]</div>

                                    <span class="time-1">{{ grid.sTime }}</span>
                                    
                                    <div class="text-1">{{ grid.sPatientName }}</div>
                                    <div class="text-2">{{ grid.sItemName }}</div>
                                    <div class="text-2"
                                        v-show="grid.sId == selectId"
                                        style="color: var(--el-color-primary);">{{ projectName }}</div>
                                </div>
                            </el-col>
                        </div>
                    </div>
                    <div v-if="isBeforeDay"
                        class="not-yy"
                        @click="onClickNot"
                        title="今天之前不可预约"></div>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>
<script setup>
    /**
     * 预约单-号源选择
     */
    import { ArrowLeft, ArrowRight, Location } from '@element-plus/icons-vue'
    import { queryNumSource, queryCalendar, appointmentChange } from '$supersetApi/projects/apricot/appointment/index.js'

    import { ElMessage, ElMessageBox } from 'element-plus'
    import { transformDate } from '$supersetResource/js/tools'

    import { deepClone } from '$supersetUtils/function'

    import useUserConfig from '../useUserConfig.js'
    const { configData } = useUserConfig()

    const props = defineProps({
        formData: {},
        optionData: Object,
        currentSelectInfo: {
            numSourceId: ''
        }
    })

    const emits = defineEmits(['changeSource'])

    const today     = moment().format('YYYY-MM-DD')
    const curDate   = ref('')
    const dateArray = ref([])

    const yyyy = computed(() => {
        if (curDate.value) {
           return moment(curDate.value).format("YYYY") + '年'
        }
    })

    // 设置选择星期几
    function setWeekListToday(date) {
        curDate.value = moment(date).format("YYYY-MM-DD")

        const lastDay = moment(curDate.value).weekday(0)
        dateArray.value = [0,1,2,3,4,5,6].map(num => {
            const m = moment(lastDay).add(num, 'days')
            return {
            id: m.format("YYYY-MM-DD"),
            mmdd: m.format("MM-DD"),
            dddd: m.format("ddd")
            }
        })
    }
    // 左右改变一周
    function setWeekList(num) {
        dateArray.value = dateArray.value.map(item => {
            const m = moment(item.id).add(num, 'days')
            return {
                id: m.format("YYYY-MM-DD"),
                mmdd: m.format("MM-DD"),
                dddd: m.format("ddd")
            }
        })
        curDate.value = dateArray.value[0].id
        // 清除选中
        setSelectData()
    }

    const isBeforeDay = computed(() => {
        return moment(curDate.value).isBefore(moment(), 'day')
    })

    // 获取日历数据
    const queryCalendarData = (params) => {
        if (!props.formData.sDistrictId || !props.formData.sMachineryRoomId) {
            return
        }

        let dateItem = params || new Date(curDate.value)
        let year = dateItem.getFullYear()
        let month = dateItem.getMonth() + 1
        month = month < 10 ? '0' + month : month
        queryCalendar({
            hospitalDistrictId: props.formData.sDistrictId,
            roomId: props.formData.sMachineryRoomId,
            yearMonth: year + '' + month
        }).then((res) => {
            dateArray.value.forEach(dateObj => {
                dateObj.registered = 0
                dateObj.limit = 0
            });
            if (res.success) {
                let data = res.data || [];
                data.map(item => {
                    let iDate = item.iDate + ''
                    const newDate = `${iDate.substring(0, 4)}-${iDate.substring(4, 6)}-${iDate.substring(6, 8)}`

                    const idx = dateArray.value.findIndex(item => item.id === newDate)
                    if (idx != -1) {
                        dateArray.value[idx].limit = item.limit 
                        dateArray.value[idx].registered = item.registered
                    }
                })
                return
            }
            ElMessage.error(res.msg)
        })
    }

    // 监听
    // 日期，院区、机房变化就查询
    watch([() => curDate.value, () => props.formData.sDistrictId, () => props.formData.sMachineryRoomId], (later, before) => {

        const l = later[2], b = before[2]
        if (l && b && l != b) {
            props.formData.numSourceId = null
        }
        queryCalendarData()
        getNumSource()
    })

    // 点击改变当前选择
    function onClickDate(date) {
        if (curDate.value == date) {
            return
        }
        curDate.value = date
        selectId.value = ''
        setSelectData()
    }

    const scrollbar = ref(null)
    const hourItems = ref([])
    const loadingNumSource = ref(false)
    const recommendInfo = ref({})      // 推荐号源
    // 获取号源
    /**
     * 获取号源
     * location 参数 是否滚动到选择、选中位置
     */
    function getNumSource (location = true) {
        if (!props.formData.sDistrictId || !props.formData.sMachineryRoomId) {
            return
        }
        loadingNumSource.value = true
        let params = {
            date: transformDate(curDate.value, false, ''),
            roomId: props.formData.sMachineryRoomId,
            hospitalDistrictId: props.formData.sDistrictId
        }
        queryNumSource(params).then((res) => {
            if (res.success && res.data) {
                hourItems.value = res.data.morning?.hourItems.concat(res.data.afternoon?.hourItems).concat(res.data.night?.hourItems)
                
                // 滚动到相应时间
                nextTick(() => {
                    if (!location) {
                        return
                    }
                    const selectDom = document.getElementsByClassName('select-numsource')[0] || document.getElementsByClassName('active-numsource')[0]
                    
                    // 有号源推荐 ，未预约的时候，是可以选择的，选择后，获取项目名称
                    if (recommendInfo.value.enterModel && document.getElementsByClassName('active-numsource')[0]) {

                        const gridItem = hourItems.value.map(item => 
                            item.gridItems.find(grid => grid.sId == recommendInfo.value.numSourceId)
                        ).find(gridItem => gridItem !== undefined)
                        // 设置选中的其它数据
                        if (gridItem) {
                            setSelectData({ sId: gridItem.sId, sTime: gridItem.sTime })
                        }
                    }
                    if (selectDom) {
                        const offsetTop = selectDom.parentNode.parentNode.parentNode.offsetTop
                        scrollbar.value.setScrollTop(offsetTop)
                        selectDom.classList.add('flash-animation')

                        setTimeout(() => {
                            selectDom.classList.remove('flash-animation')
                        }, 800)
                    }
                })
                return
            }
            ElMessage.error(res.msg)
        }).finally(() => {
            loadingNumSource.value = false
        })
    }

    // 日历样式
    const getDatesClass = (item) => {
        return { 
            'active': item.id == curDate.value,
            'today': item.id === today,
            'before-date': moment(item.id).isBefore(moment(), 'day')
        }
    }
    const getItemClasses = (grid) => {
        return {
            'out-patient': grid.iPatientType === 1,
            'emergency-patient': grid.iPatientType === 2,
            'physical-patient': grid.iPatientType === 3,
            'hospital-patient': grid.iPatientType === 4,
            'others': grid.iPatientType === 9,
            'not-select': grid.registered,
            'reserve': grid.iReserved === 1,
            'active-numsource': grid.sId === selectId.value,
            'select-numsource': grid.sId === props.currentSelectInfo.numSourceId,
            'recommend-numsource': recommendInfo.value.enterModel === 1 && grid.sId == recommendInfo.value.numSourceId && !props.currentSelectInfo.numSourceId
        }
    }

    const projectName = ref('')

    const setProjectName = () => {
        // 项目名称
        const findObj = props.optionData.projectOptions?.find(item => item.sValue == props.formData.sProjectId)
        if (findObj) {
            projectName.value = findObj.sName
        }else {
            projectName.value = ''
        }
    }
    
    watch(() => props.formData.sProjectId, () => {
        setProjectName()
    })

    // 选择号源
    const selectId = ref('')
    const onClickSelect = (item) => {
        if (item.registered) {
            return
        }

        // 存在 sId 点击做号源改约
        // if (props.formData.sId) {
        //     if (props.formData.iFlowState > 3) {
        //         ElMessage.warning('已问诊，不可改约')
        //         return
        //     }

        //     ElMessageBox.confirm(`确定改约时间为 ${curDate.value} ${item.sTime}，是否继续？`,
        //     '提示',
        //     {
        //         confirmButtonText: '确定',
        //         cancelButtonText: '取消',
        //         type: 'warning',
        //     }).then(() => {
        //         appointmentChange({
        //             sNumSourceGrid: item.sId,
        //             sPatientInfoId: props.formData.sId,
        //         }).then(res => {
        //             if (res.success) {
        //                 ElMessage.success(res.msg)
        //                 queryCalendarData()
        //                 getNumSource(false)
        //                 // 更新预约时间，在关闭的时候，跳转日期使用
        //                 props.formData.dAppointmentTime = moment(moment(curDate.value).format('YYYYMMDD') + item.sTime + '00', 'YYYYMMDDHH:mmss').valueOf()
        //                 props.formData.sTimeText = item.sTime
        //                 props.formData.numSourceId = item.sId
        //                 // 更新当前选择信息
        //                 props.currentSelectInfo.numSourceId = item.sId
        //                 props.currentSelectInfo.dAppointmentTime = curDate.value
        //                 props.currentSelectInfo.sTimeText = item.sTime
        //                 emits('changeSource')
        //                 return
        //             }
        //             ElMessage.error(res.msg)
        //         })
                
        //     })
            
        //     return
        // }

        // 再次点击，清除
        if (item.sId === selectId.value) {
            setSelectData({ 
                sId: props.currentSelectInfo.numSourceId, 
                sTime: props.currentSelectInfo.sTimeText 
            })
            projectName.value = ''
            selectId.value = ''
            props.formData.isCheckNumberSource = false;
            delete props.formData.isTransformNumSource;
            return
        }

        if(props.formData.sId &&  props.formData.numSourceId !== item.sId) {
            // 改约
            props.formData.isTransformNumSource = true;
        }

        // 项目名称
        setProjectName()
        // 设置选中数据
        setSelectData(item)

        selectId.value = item.sId;
        
        if(!props.formData.sId) {
            props.formData.isCheckNumberSource = true;
        }
    }

    const setSelectData = (obj = { sId: null, sTime: null }) => {

        props.formData.sAppointmentTime = curDate.value.replace(/-/g,'')
        props.formData.numSourceId = obj.sId
        props.formData.sTimeText = obj.sTime
    }
    const onClickToDay = () => {
        setCurDate({
            dAppointmentTime: moment(),
            sTimeText: '',
            numSourceId: '',
        })
    }
    // 设置当前选择时间
    const setCurDate = (data) => {
        // 有默认号源
        // enterModel 1 、日历点击进入。 2 申请单推荐进入
        if (data.enterModel && data.numSourceId) {
            selectId.value = data.numSourceId
            if (props.formData.sProjectName) {
                projectName.value = props.formData.sProjectName
            }
            recommendInfo.value = deepClone(data)
        }
        
        let appointmentTime = data.dAppointmentTime
        if (!appointmentTime) {
            appointmentTime = new Date()
            ElMessage.warning('当前患者，无预约时间！')
        }
        
        // 当前时间跟预约时间一样，查询号源
        if (curDate.value == moment(appointmentTime).format("YYYY-MM-DD")) {
            queryCalendarData()
            return
        }
        setWeekListToday(appointmentTime)
    }

    const onClickNot = () => {
        ElMessage.warning('今天之前不可预约')
    }

    // 清除数据
    const clearData = () => {
        selectId.value = null
        recommendInfo.value = {}
        hourItems.value = []
        setSelectData()
    }
    // 修改号源后，刷新数据
    const refreshNumSource = (isCalenderData) => {

        if (isCalenderData) {
            queryCalendarData()
        }
        getNumSource(false)
        // 更新预约时间，在关闭的时候，跳转日期使用
        props.formData.dAppointmentTime = moment(moment(curDate.value).format('YYYYMMDD') + props.formData.sTimeText + '00', 'YYYYMMDDHH:mmss').valueOf()
        // props.formData.sTimeText = item.sTime
        // props.formData.numSourceId = item.sId
        // // 更新当前选择信息
        props.currentSelectInfo.numSourceId = props.formData.numSourceId
        props.currentSelectInfo.sTimeText = props.formData.sTimeText
        props.currentSelectInfo.dAppointmentTime = curDate.value
        selectId.value = null
    }

    defineExpose({
        setCurDate,
        clearData,
        refreshNumSource
    })
</script>
<style lang="scss" scoped>
:deep(.smooth-scrollbar) {
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
}
.container {
    height: calc(100% - 34px);
    display: flex;
    flex-direction: column;
    .title {
        position: relative;
        height: 42px;
        padding: 10px;
        font-size: 16px;
        font-weight: 400;
        color: rgb(26, 32, 44);
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;
        line-height: 21px;
        .t-title {
            border-left: 2px solid var(--el-color-primary);
            padding-left: 5px;
        }
        .title-yyyy {
            width: 74px;
            text-align: center;
            position: absolute;
            left: calc(50% - 25px);
            font-size: 14px;
            height: 20px;
            
        }
        .toDay {
            font-size: 12px;
            padding: 0px;
            display: inline-block;
            background: var(--el-color-primary);
            border-radius: 50%;
            width: 19px;
            height: 19px;
            color: white;
            cursor: pointer;
            margin-left: 4px;
            text-align: center;
        }
    }
    .header {
        display: flex;
        height: 52px;
        border: 1px solid rgba(220, 223, 230, 1);
        border-left: 0;
        border-right: 0;
        user-select: none;
        background-color: #fefee0;
        .action {
            width: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            &:hover {
                background: #eff1f3;
            }
        }
        .dates {
            display: flex;
            flex: 1;
            margin: 0;
            padding: 0;
            li {
                list-style-type: none;
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                border-right: 1px solid rgba(220, 223, 230, 1);
                font-weight: 400;
                cursor: pointer;
                &:first-child {
                    border-left: 1px solid rgba(220, 223, 230, 1);
                }
                &:hover {
                    background: #ebf6ff;
                }
                &.active {
                    position: relative;
                    color: var(--el-color-primary);
                    background: rgba(245, 249, 252, 1);
                    font-weight: 500;
                    &:after {
                        content: "";
                        position: absolute;
                        width: 100%;
                        height: 2px;
                        background: #f9fafc;
                        bottom: -1px;
                    }
                    &:before {
                        content: '';
                        position: absolute;
                        top: 0;
                        width: 100%;
                        height: 2px;
                        background: var(--el-color-primary);
                    }
                }
                &.today {
                    position: relative;
                    .dddd {
                        font-weight: bold;
                    }
                }
                .dddd {
                    padding-left: 2px;
                }
                &.before-date {
                    color: #bdb4b4;
                }
                .text-01 {
                    padding-bottom: 4px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    width: 100%;
                    text-align: center;
                }
            }
        }
    }
    .content {
        flex: 1;
        padding: 10px 0 5px 0;
        // background: rgba(245, 249, 252, 1);
        overflow: hidden;
        height: 0px;
        user-select: none;
        .tip {
            position: absolute;
            top: 2px;
            font-size: 12px;
            text-align: center;
            width: 100px;
            text-align: center;
            left: calc(50% - 50px);
            z-index: 1;
            background: #e6a23c;
            color: white;
            border-radius: 4px;
        }
        .date-item {
            display: flex;
            padding-left: 15px;
            .time {
                width: 50px;
                text-align: center;
                padding-top: 10px;
            }
            .col-box {
                padding: 5px;
            }
            .sourse {
                width: 100%;
                .sourse-box {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    height: 56px;
                    // background: rgba(240, 244, 255, 1);
                    background: rgb(235 240 252);
                    padding: 4px;
                    border-radius: 4px;
                    cursor: pointer;
                    border: 1px solid rgba(240, 244, 255, 1);
                    // box-shadow: 0px 1px 4px rgba(175, 175, 175, 0.3);
                    .location, .recommend {
                        display: none;
                    }
                    &.not-select {
                        cursor: no-drop;
                        background: rgba(232, 232, 232, 0.9) !important;
                        border: 1px solid rgba(232, 232, 232, 0.9) !important;
                        border-color: #dcdcdc !important;
                    }
                    &.select-numsource {
                        box-shadow: 1px 1px 1px #eee;
                        border-color: var(--el-color-primary-light-5) !important;
                        background: #fcfcfc !important;
                        .location {
                            display: block;
                        }
                        &::after {
                            content: "";
                            width: 0;
                            height: 0;
                            position: absolute;
                            right: 0;
                            bottom: 0;
                            border-bottom: 10px solid var(--el-color-primary-light-5);
                            border-left: 10px solid transparent;
                        }
                    }
                    &.active-numsource {
                        position: relative;
                        border: 1px solid var(--el-color-primary-light-5) !important;
                        background: #fcfcfc !important;
                        &::after {
                            content: "";
                            width: 0;
                            height: 0;
                            position: absolute;
                            right: 0;
                            bottom: 0;
                            border-bottom: 15px solid var(--el-color-primary-light-5);
                            border-left: 15px solid transparent;
                        }
                    }
                    &.recommend-numsource {
                        .recommend {
                            display: block;
                        }
                    }
                    .badge-1 {
                        position: absolute;
                        right: 4px;
                        top: 4px;
                        font-size: 12px;
                        color: var(--el-color-warning);
                        padding: 2px 4px;
                        background-color: #fff;
                        z-index: 1;
                    }
                    .badge {
                        position: absolute;
                        left: 0px;
                        top: 0px;
                        font-size: 12px;
                        background: black;
                        background: var(--el-color-primary);
                        color: var(--el-color-white);
                        padding: 2px;
                        border-radius: 0px 0px 2px;
                    }
                    .time-1 {
                        position: absolute; 
                        top: 4px; 
                        right: 4px;
                    }
                    .text-1 {
                        width: 100%;
                        height: 25px;
                        margin-top: 6px;
                        padding-bottom: 2px;
                        line-height: 25px;
                        overflow: hidden;
                        text-align: center;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                    }
                    .text-2 {
                        width: 100%;
                        text-align: center;
                        overflow: hidden;
                        text-overflow:ellipsis;
                        white-space: nowrap;
                        color: #333;
                    }
                    &:not(.not-select):hover {
                        background: #fcfcfc !important;
                    }
                }
            }
            &.beforeDay {
                cursor: no-drop;
                .sourse .sourse-box {
                    cursor: no-drop;
                }
            }
        }
        .not-yy {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            cursor: no-drop;
        }
    }
    .flash-animation {
        animation: flash-animation 0.8s infinite;
    }
    .anima-blink {
        animation: blink 0.6s 2;
    }
    @keyframes flash-animation {
        0% {
            box-shadow: 0 0 2px #fff;
        }
        50% {
            box-shadow: 0 0 5px #0d9488, 0 0 10px #0d9488;
        }
        100% {
            box-shadow: 0 0 2px #fff;
        }
    }
    @keyframes blink {
        0% { opacity: 1; }
        50% { opacity: 0.1; }
        100% { opacity: 1; }
    }
}
</style>