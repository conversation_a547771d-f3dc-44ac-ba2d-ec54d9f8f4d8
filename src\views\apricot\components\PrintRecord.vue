<template>
    <el-dialog append-to-body
        title="打印记录"
        v-model="visible"
        :close-on-click-modal="false"
        destroy-on-close
        width="70vw"
        class="my-dialog t-default"
        @close="closeDialog">
        <div class="g-content m-flexLaout-ty" style="height: 50vh;">
            <div class="g-flexChild">
                <el-table v-loading="loading"
                    :data="tableData"
                    border
                    highlight-current-row
                    @row-click="onRowClick"
                    height="100%">
                    <el-table-column min-width="100"
                        property="sName"
                        label="姓名"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column min-width="100"
                        property="sType"
                        label="报告类型"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column min-width="100"
                        property="sTemplateName"
                        label="模板名称"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column min-width="200"
                        property="sFileName"
                        label="文件名称"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column min-width="100"
                        property="sUserName"
                        label="打印人"
                        show-overflow-tooltip>
                    </el-table-column>
                    <el-table-column width="150"
                        property="sTagName"
                        label="打印时间"
                        show-overflow-tooltip>
                        <template v-slot="{ row }">
                            {{setTime2yyyyMMDDHHmm( row.dCreateTime)}}
                        </template>
                    </el-table-column>
                    <el-table-column width="170"
                        label="操作"
                        align="center">
                        <template v-slot="{ row }">
                            <el-button type="primary" link
                                @click="onDownLoadClick(row, 'docx')">下载word</el-button>
                            <el-divider direction="vertical"></el-divider>
                            <el-button type="primary" link
                                @click="onDownLoadClick(row, 'pdf')">下载pdf</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <template #footer>
            <el-button-icon-fa _icon="fa fa-close-1" @click="closeDialog">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>
<script>
import { deepClone, transformDate } from '$supersetUtils/function'

import { printFileList } from '$supersetApi/projects/apricot/system/templateSet.js'
export default {
    name: 'PrintRecord',
    emits: ['update:dialogVisible'],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data () {
        return {
            visible: false,
            tableData: [{}],
            form: {},
            loading: false,
            saveLoading: false
        }
    },
    watch: {
        dialogVisible (val) {
            this.visible = this.dialogVisible;
            this.tableData = [];
            if (val) {
                this.getTableData()
            }
        },
    }, 
    methods: {
      setTime2yyyyMMDDHHmm (val) {
            return transformDate(val, false, 'yyyy-MM-dd HH:mm')
        },
        closeDialog () {
            this.$emit('update:dialogVisible', false);
        },
        onDownLoadClick(row, fileType) {
            this.downloadFile(row, fileType);
        },
        // a标签下载
        downloadFile(params, fileType) {
            let a = document.createElement('a');
            let downURL = `${window.configs.urls.apricot}/printFile/webDownloadFileWithType?sFileId=${params.sFileId}&sFileType=${fileType}`
            a.href = downURL;
            a.setAttribute('target', '_blank');
            document.body.append(a);
            a.click();
            a.remove();
        },
        // 行点击
        onRowClick (row) {
            this.form = deepClone(row);
        },
        // 获取表格数据
        getTableData () {
            this.loading = true;
            printFileList({sPatientId: this.patientInfo.sId}).then(res => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data || []
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                this.loading = false;
                console.log(err);
            })
        },
    }
}
</script>
<style lang="scss" scoped>

</style>
