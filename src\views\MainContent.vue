<template>
  <div class="app-main-container">
    <!--路由插槽-->
    <router-view v-slot="{ Component }">
      <keep-alive :include="tabsList">
        <component :is="Component" />
      </keep-alive>
    </router-view>
    <!-- <transition name="slide-fade">
			<keep-alive :include="tabsList"> 
				<router-view></router-view>
			</keep-alive>
		</transition> -->
  </div>
</template>
  
<script>
import { useStore } from 'vuex';

export default {
  name: 'MainContent',
  setup() {
    const store = useStore();
 
    return {
    }
  },
  computed: {
    activeRouterName() {
      return this.$store.getters['module_router/activeRouterName']
    },
    moduleRouterDataList() {
      return this.$store.getters['module_router/moduleRouterDataList']
    },
    moduleComponentsMaps() {
      //构建映射 {"routerName1": "component1", "routerName2": "component2", ...}
      let obj = {}
      let data = this.moduleRouterDataList || []
      data.some(function (item, i) {
        obj[item.routerName] = item.componentName
      })
      return obj
    },
    tabsList() {
      /**
       * keep-alive list
       */
      let RecentOpens = this.$store.getters['module_router/RecentOpens']
      let moduleRouters = this.moduleComponentsMaps

      let arr = []
      for (let i = 0; i < RecentOpens.length; i++) {
        arr[i] = moduleRouters[RecentOpens[i].routerName]
      }

      return arr
    }
  },
  watch: {
    activeRouterName() {
      this.$router.push({ name: this.activeRouterName })
    }
  },
  mounted() {
  }
}
</script>
 
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.app-main-container {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: $base-width;
  padding: 10px;
  height: 100%;
  overflow: hidden;
  text-align: left;
 

  // .footer-copyright {
  //    height: $footer-copyright-height;
  //   line-height: $footer-copyright-height;
  //   color: $base-color-3;
  //   text-align: center;
  //   border-top: 1px dashed $base-border-color;
  // }
}
</style>
