import { getCallButtonSetOfModule } from '$supersetApi/projects/apricot/common/callSet.js' // 获取列表呼叫按钮
import { callAction, isCalledBtns } from '$supersetApi/projects/apricot/common/call.js' // 呼叫接口
import { ElMessage } from 'element-plus';



// 获取呼叫按钮  
 async function getCallButtonSetOfModules (moduleId, stationId) {
    var btnsArr = []
    const params = {
        moduleId: moduleId,
        stationId: stationId,
    }
    await getCallButtonSetOfModule(params).then(res => {
        if (res.success) {
            let arr = res.data || [];
            arr.forEach((item) => {
                var ele = {
                    ...item,
                    isCalled: false,
                    isLoading: false
                }
                btnsArr.push(ele)
            })
            return 
        }
        ElMessage.error(res.msg)
    }).catch((err) => {
        console.log(err)
    })
    return btnsArr
};
// 获取已呼叫按钮
async function getCalledBtn (patientInfoId) {
    const params = {
        patientInfoId: patientInfoId
    }
    var calledBtnArray = []
    var res = await isCalledBtns(params)
    if (res && res.success) {
        calledBtnArray = res.data || []
    }
    return calledBtnArray
};

// 呼叫
async function handleCallAction (jsonData) {
    var isCalled = false
    var code = null
    await callAction(jsonData).then(res =>{
        if (res.success) {
            isCalled = true
            ElMessage.success(res.msg)
            return
        }
        isCalled = false
        code = res.code
    }).catch( err =>{
        isCalled = false
        code = err.code
    })
    return { isCalled, code }
};
export { getCallButtonSetOfModules, getCalledBtn, handleCallAction }

