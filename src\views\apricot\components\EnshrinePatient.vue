<template>
  <div style="display: inline-block;" ref="collectDom">
    <el-popover v-model:visible="visible" trigger="click" placement="right" width="650" @show="onShowPop">
      <div style="padding:10px;" v-loading="loading">
        <div class="m-flexLaout-tx u-autoHeight" style="align-items:center; margin-bottom: 15px;">
          <span>患者姓名：</span>
          <div class="g-flexChild">
            {{ patient.sName }}
          </div>
        </div>
        <div class="m-flexLaout-tx u-autoHeight" style="margin-bottom: 15px;">
          <span>收藏标签：</span>
          <div class="g-flexChild" style="max-height:400px">
            <el-checkbox-group v-model="checkList" @change="onCheckBoxChange">
              <el-checkbox v-for="(item, index) in options" :key="index"
                :label="item.sTagName">{{ item.sTagName }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="m-flexLaout-tx u-autoHeight" style="margin-bottom: 15px;">
          <span>收藏备注：</span>
          <div class="g-flexChild" style="max-height:400px">
            <el-input type="textarea" :rows="2" placeholder="" v-model="sTagReason"></el-input>
          </div>
        </div>
        <!-- <p>确定要{{patient.iCollect ? '取消收藏' : '收藏'}}吗？</p> -->
        <div style="text-align: right; margin: 0">
          <el-button class="pull-left" type="primary" plain @click="addTag">添加标签</el-button>
          <el-button type="primary" @click="onClickSave">保存</el-button>
          <!-- <el-button :disabled="!patient.iCollect" type="primary" 
            @click="onClickSure(false)">取消收藏</el-button> -->
          <el-button  @click="visible = false">关闭</el-button>
        </div>
      </div>
      <template v-if="buttonMsg && buttonMsg.icon" #reference>

        <el-button :class="{
          'm-vertical-btn t2': buttonMsg.icon,
          'margin_l': !buttonMsg.icon,
          'm-vertical-text': buttonMsg.isFold,
          't-border': buttonMsg.isBorder
        }" :disabled="buttonMsg.isReadOnly" >
          <svg v-if="buttonMsg.icon" class="fa" aria-hidden="true">
            <use :xlink:href="'#' + buttonMsg.icon"></use>
          </svg>
          <label>{{ patient.iCollect ? '取消收藏' : '收藏' }}</label>
        </el-button>
      </template>
      <template v-else #reference>
        <i  v-if="patient.iCollect" class="fa fa-star-fill  i-star-fill" title="取消收藏" >
          </i>
        <i  v-else class="fa fa-star-line i-star" title="收藏" >
          </i>
      </template>
    </el-popover>

    <!-- 收藏标签管理 -->
  </div>
  <CollectTag v-model:dialogVisible="d_CollectTag_v"></CollectTag>
</template>
<script>

import { getCollectionTagEnable } from '$supersetApi/projects/apricot/common/collectTag.js'
import Api from '$supersetApi/projects/apricot/case/index.js'
export default {
  name: 'EnshrinePatient',
  components: {
    CollectTag: defineAsyncComponent(() => import('$supersetViews/apricot/components/CollectTag.vue'))
  },
  props: {
    buttonMsg: {
      type: Object,
      default: () => ({})
    },
    patient: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['callBack'],
  data() {
    return {
      visible: false,
      loading: false,
      sCollectionTag: '',
      checkList: [],
      options: [],
      d_CollectTag_v: false,
      sTagReason: '',
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.patient.sCollectionTagId) {
          let id = this.patient.sCollectionTagId
          this.getsTagsContentById(id)
        } else {
          // 当sCollectionTagId为false时，重置表单数据
          this.sCollectionTag = '';
          this.checkList = [];
          this.sTagReason = '';
        }
        this.getCollectionTagEnable();
      }
    },
  },
  methods: {
    onShowPop(val) {
      // console.log(this.visible)
    },
    addTag() {
      this.d_CollectTag_v = true;
      this.visible = false
    },
    onCheckBoxChange() {
      this.sCollectionTag = this.checkList.join(',');
    },
    onClickSave() {
      this.onClickSure()
    },
    onClickSure() {
      // true 是已经收藏的
      const iCollect = this.checkList.length > 0 ? 1 : 0;
      if (!iCollect) {
        Api.delCollection({ sPatientId: this.patient.sId }).then(res => {
          if (res.success) {
            this.$message.success('取消收藏成功！');
            this.$emit('callBack', { iCollect: 0, sCollectionTag: '', sCollectionTagId: null, sCollectionReason: '' });
            this.sTagReason = ''
            this.sCollectionTag = ''
            this.checkList = []
            this.visible = false;
            return;
          }
          this.$message.error(res.msg)
        })
        return
      }
      if (this.patient.sCollectionTagId) {
        let jsonData = {
          sPatientId: this.patient.sId,
          sInnerIndex: this.patient.sInnerIndex,
          sId: this.patient.sCollectionTagId,
          sTags: this.sCollectionTag,
          sReason: this.sTagReason
        }
        Api.editCollection(jsonData).then(res => {
          if (res.success) {
            this.$message.success('保存成功！');
            this.updateRowData(res.data);
            this.visible = false;
            return;
          }
          this.$message.error(res.msg)
        })
        return
      }
      let jsonData = {
        sPatientId: this.patient.sId,
        sInnerIndex: this.patient.sInnerIndex,
        sTags: this.sCollectionTag,
        sReason: this.sTagReason
      }
      Api.addCollection(jsonData).then(res => {
        if (res.success) {
          this.$message.success('添加收藏成功！');
          this.updateRowData(res.data);
          this.visible = false;
          return;
        }
        this.$message.error(res.msg)
      })
    },
    // 更新表格当前选中行数据
    updateRowData(data){
        data = data || {};
        let tempObject = {
            iCollect: 1, 
            sCollectionTag: data.sTags, 
            sCollectionTagId: data.sId, 
            sCollectionReason: data.sReason 
        }
        this.$emit('callBack', tempObject);
    },
    getsTagsContentById(id) {
      this.loading = true
      let params = {
        sCollectionId: id
      }
      Api.getsCollectionById(params).then(res => {
        this.loading = false
        if (res.success) {
          this.sCollectionTag = res.data.sTags
          this.checkList = res.data.sTags ? res.data.sTags.split(',') : []
          this.sTagReason = res.data.sReason
          return
        }
        this.$message.error(res.msg)
      }).catch(() => {
        this.loading = false
      })
    },
    getCollectionTagEnable() {
      getCollectionTagEnable().then(res => {
        if (res.success) {
          this.options = res.data || [];
          return;
        }
        this.$message.error(res.msg)
      }).catch(err => {
        console.log(err)
      })
    }
  },
}
</script>
<style lang="scss" scoped>
.i-star {
  cursor: pointer;
  font-size:20px;
  &:hover {
    border-color: #ffc43e;
    color: #ffc43e;
  }
}

.i-star-fill {
  cursor: pointer;
  font-size:20px;
  color: #ffc43e;
}

.el-checkbox {
  margin-bottom: 10px;
}
</style>
