<template>
    <div class="c-template">
        <div class="c-box-01  bg-light-300 flex flex-col">
            <div>
                <h5 class="text-center m-2">study信息</h5>
                <TextList :list="studyTextList" :data="params" labelWidth="130px"
                    :iModuleId="iModuleId" 
                    :storageKey="isImageCase ? 'ImageCaseStudyTextList' : 'MergeCaseStudyTextList'"></TextList>
            </div>
            <div>
                <h5 class="text-center m-2">关联病例信息</h5>
                <TextList :list="associationTextList" :data="params" labelWidth="90px"
                    :iModuleId="iModuleId" 
                    :storageKey="isImageCase ? 'ImageCaseRelatedTextList': 'MergeCaseRelatedTextList'"></TextList>
            </div>
            <div>
                 <h5 class="text-center m-2">序列信息</h5>
            </div>
            <div class="c-scroll flex-grow"
                style="display: flex;flex-direction: column; flex: 1;overflow: hidden;"
                v-loading="loading">
                <div class="c-item">
                    <el-table class="n-borderTop-none"
                        ref="mainTable"
                        :data="tableData"
                        border
                        highlight-current-row
                        stripe
                        width="100%"
                        height="100%">
                        <el-table-column 
                            type="selection"
                            align="center"
                            width="60">
                        </el-table-column>
                        <el-table-column prop="modality"
                            label="设备"
                            min-width="80"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="instanceCount"
                            label="数目"
                            min-width="60"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="isRebuid"
                            label="可重建"
                            min-width="70"
                            show-overflow-tooltip
                            align="center">
                            <template #default="scope">
                                {{scope.row.isRebuid ? '是' : '否'}}
                            </template>
                        </el-table-column>
                        <el-table-column prop="seriesDate"
                            label="序列日期"
                            min-width="100"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="seriesTime"
                            label="序列时间"
                            min-width="100"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column :fit="false"
                            prop="seriesDesc"
                            min-width="120"
                            label="序列描述"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column v-if="rights.deleteSeries"
                            width="100"
                            align="center"
                            fixed="right"
                            label="操作"
                            show-overflow-tooltip>
                            <template v-slot="{row}">
                                <el-button-icon-fa link
                                    icon="el-icon-delete"
                                    @click="onDelSeriesClick(row)">删除</el-button-icon-fa>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <div class="c-button">
            <div class="c-item c-message">
                <template v-if="isImageCase">
                    <el-button-icon-fa type="primary" plain
                        icon="el-icon-circle-plus-outline"
                        :disabled="!!params.patientInfoId"
                        @click="onCreateClick">{{(!!params.patientInfoId) ? '已创建' : '创建病例'}}</el-button-icon-fa>
                    <el-button-icon-fa type="primary" plain
                        icon="fa fa-poly-rebuildding-1"
                        @click="onWebRebuildClick">web重建</el-button-icon-fa>
                    <el-button-icon-fa type="primary" plain
                        icon="el-icon-s-grid"
                        @click="onSplitStudyClick">拆分图像</el-button-icon-fa>
                </template>
                <el-button-icon-fa
                    type="primary" plain
                    _icon="el-icon-position"
                    @click="onClickSend">发送序列</el-button-icon-fa>
                <el-button-icon-fa
                    _icon="fa fa-close-1"
                    @click="$emit('closeDialog')">关闭</el-button-icon-fa>
            </div>
        </div>
        <div class="c-item-list"
            v-show="msgObj.visible"
            v-loading="nodeLoading">
            <h5 class="m-2 ml-0">节点列表</h5>
            <div class="i-checkBox">
                <el-checkbox-group v-if="msgObj.messageList.length"
                    v-model="msgObj.formMessage">
                    <div v-for="(item, index) in msgObj.messageList" :key="index">
                        <el-checkbox
                            :label="item.sId"
                            :key="item.sId">
                            <span>{{item.sServerName}}</span>  
                            <span v-if="item.sIp" class="i-text">{{ `(${item.sIp})`}}</span>  
                        </el-checkbox>
                    </div>
                </el-checkbox-group>
                <el-empty v-else :image-size="50" description=" " />
            </div>
            <div class="i-button">
                <el-button-icon-fa type="primary"
                    :loading="msgObj.loading"
                    icon="el-icon-check"
                    :disabled="msgObj.messageList.length===0"
                    @click="onSend">确定</el-button-icon-fa>
                <el-button-icon-fa 
                    icon="el-icon-close"
                    @click="msgObj.visible=false">关闭</el-button-icon-fa>
            </div>
        </div>

        <CreatePatient v-model:dialogVisible="d_CreatePatient_v"
            :patientInfo="params"
            @isUpdateRowData="isUpdateRowData"></CreatePatient>
    </div>
</template>
<script>
import CreatePatient from '$supersetViews/apricot/components/CreatePatient.vue' // 创建病例

import mixinClientVerifyCode from '$supersetResource/js/projects/apricot/mixinClientVerifyCode.js'

import { deepClone } from '$supersetUtils/function'
import Api, { splitStudyBySeriesDate } from '$supersetApi/projects/apricot/case/report.js'

import { getDicomNode } from '$supersetApi/projects/apricot/system/dicomSet.js'

export default {
    name: 'ImageStudy',
    components: {
        CreatePatient,
    },
    mixins: [mixinClientVerifyCode],
    emits: ['closeDialog', 'isUpdateRowData'],
    props: {
        params: {
            type: Object,
            default: () => ({})
        },
        visible: {
            type: Boolean,
            default: false
        },
        iModuleId: {
            type: Number,
        },
        rights: {
            type: Object,
            default: () => ({
                deleteSeries: true,
            })
        },
        isImageCase: {
            type: Boolean,
            default: true
        },
    },
    data () {
        return {
            msgObj: {           // 消息发送
                formMessage: [],
                messageList: [],
                loading: false,
                visible: false,
            },
            nodeLoading: false,
            loading: false,
            studyTextList: [
                {
                    sProp: 'patientName',
                    sLabel: '姓名拼音',
                    iLayourValue: 8,
                },
                {
                    sProp: 'patientSex',
                    sLabel: '性别',
                    iLayourValue: 8,
                },
                {
                    sProp: 'patientBirthDate',
                    sLabel: '生日',
                    iLayourValue: 8,
                },
                {
                    sProp: 'patientAge',
                    sLabel: '年龄',
                    sMinWidth: '80px',
                    iLayourValue: 8,
                },
                {
                    sProp: 'patientWeight',
                    sLabel: '体重',
                    iLayourValue: 8,
                },
                {
                    sProp: 'patientId',
                    sLabel: 'PatientId',
                    iLayourValue: 8,
                },
                {
                    sProp: 'accessionNumber',
                    sLabel: 'AccessionNumber',
                    iLayourValue: 8,
                },
                {
                    sProp: 'studyInstanceUid',
                    sLabel: 'StudyUid',
                    iLayourValue: 8,
                },
                {
                    sProp: 'studyDate',
                    sLabel: '检查日期',
                    iLayourValue: 8,
                },
                {
                    sProp: 'studyTime',
                    sLabel: '检查时间',
                    iLayourValue: 8,
                },
                {
                    sProp: 'modality',
                    sLabel: 'Modality',
                    iLayourValue: 8,
                },
                {
                    sProp: 'studyDesc',
                    sLabel: 'StudyDesc',
                    iLayourValue: 8,
                },
                {
                    sProp: 'studyId',
                    sLabel: 'StudyId',
                    iLayourValue: 8,
                },
                {
                    sProp: 'createTime',
                    sLabel: '导入日期',
                    iLayourValue: 8,
                },
                {
                    sProp: 'related',
                    sLabel: '是否关联',
                    iLayourValue: 8,
                },
                {
                    sProp: 'memo',
                    sLabel: '关联描述',
                    iLayourValue: 8,
                },
            ],
            associationTextList: [
                {
                    sProp: 'patientInfoName',
                    sLabel: '病例姓名',
                    iLayourValue: 8,
                },
                {
                    sProp: 'nuclearNum',
                    sLabel: '核医学号',
                    iLayourValue: 8,
                },
                {
                    sProp: 'appointmentDate',
                    sLabel: '预约日期',
                    iLayourValue: 8,
                },
                {
                    sProp: 'deviceTypeName',
                    sLabel: '设备类型',
                    iLayourValue: 8,
                },
                {
                    sProp: 'itemName',
                    sLabel: '检查项目',
                    iLayourValue: 8,
                },
            ],
            d_CreatePatient_v: false,
            tableData: []
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters['user/userSystemInfo']
            if (temp.__proto__.constructor === Object) {
                return temp
            } else {
                return {}
            }
        },
    },
    watch: {
        'params': {
            handler (val, oldVal) {
                if (!Object.keys(this.params).length) {
                    this.tableData = [];
                    return
                }
                this.getData();
            },
            immediate: true
        },
        visible (val) {
            if (val) {
                this.msgObj.visible = false;
            }
        },

    },
    methods: {
        isUpdateRowData() {
            this.$emit('isUpdateRowData')
        },
        // 创建病例
        onCreateClick() {
            if(!Object.keys(this.params).length) {
                this.$message.warning('请选择左侧表格数据！')
                return
            }
            this.d_CreatePatient_v = true
        },
        // web重建
        onWebRebuildClick() {
            if(!Object.keys(this.params).length) {
                this.$message.warning('请选择左侧表格数据！')
                return
            }
            this.openWebReBuildViewer(this.params);
        },
        onSplitStudyClick() {
            if(!Object.keys(this.params).length) {
                this.$message.warning('请选择左侧表格数据！')
                return
            }
            let loading = this.$loading({
                lock: true,
                text: '正在拆分中，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            const jsonData = {
                studyInstanceUid: this.params.studyInstanceUid
            }
            splitStudyBySeriesDate(jsonData).then(res => {
                loading.close();
                if(res.success) {
                    this.$message.success(res.msg);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close()
                console.log(err);
            })
        },
        // 删除序列
        onDelSeriesClick (row) {
            this.$confirm(`确定要删除吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                let jsonData = {
                    delPerson: this.userInfo.sName,
                    seriesUid: row.seriesInstanceUid,
                    studyDate: this.params.studyDate
                }
                let loading = this.$loading({
                    lock: true,
                    text: '正在删除中，请稍等',
                    background: 'rgba(0, 0, 0, 0.2)'
                });
                Api.delSeriesUid(jsonData).then((res) => {
                    loading.close()
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.getData();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    loading.close();
                })
            })
        },
        // 发送数据
        async onSend () {
            if (!this.msgObj.formMessage.length) {
                this.$message.warning('请选择发送地址！');
                return
            }
            let dom = this.$refs.mainTable;
            let selections = dom.getSelectionRows();
            let multipleSelection = selections.map(item => item.seriesInstanceUid);
            const nodes = this.msgObj.messageList.filter(item => this.msgObj.formMessage.includes(item.sId));
            
            var count = 0;
            var isStopSend = false;
            let loading = this.$loading({
                lock: true,
                text: '正在发送中，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            // 发送请求
            for(var i= 0; i < nodes.length ; i++) {
                if(isStopSend) {
                    break;
                }
                const item = nodes[i];
                const jsonData = {
                    sAETitle: item.sAETitle,
                    sIp: item.sIp,
                    iPort: item.iPort,
                    seriesInstanceUidList: multipleSelection
                }
                await  Api.imgPatientDicomSend(jsonData).then(res => {
                    if (res.success) {
                        count++;
                        this.$message({
                            message: item.sServerName + '节点，提示：' + res.msg,
                            type: 'success',
                            duration: 5000
                        });
                        return
                    }
                    this.$message({
                        message: item.sServerName + '节点，提示：' + res.msg,
                        type: 'error',
                        duration: 5000
                    });
                    isStopSend = true;
                    loading.close()
                }).catch(err => {
                    console.log(err);
                    isStopSend = true;
                    loading.close()
                })
            }
            if(count === nodes.length) {
                loading.close();
                this.msgObj.visible = false;
            }
        },
        // 点击发送Dicom按钮
        onClickSend () {
            let dom = this.$refs.mainTable;
            let selections = dom.getSelectionRows();
            if (!selections.length) {
                this.$message.warning('请勾选发送序列！');
                this.msgObj.visible = false;
                return
            }
            this.msgObj.formMessage = [];
            this.msgObj.visible = !this.msgObj.visible;
            this.msgObj.visible && this.getDicomNode();
        },
        // 获取dicom 节点数据
        getDicomNode () {
            if (!this.msgObj.visible) {
                return
            }
            this.nodeLoading = true;
            getDicomNode().then(res => {
                this.nodeLoading = false;
                if (res.success) {
                    this.msgObj.messageList = res.data || [];
                    return
                }
            }).catch(err => {
                this.nodeLoading = false;
                console.log(err)
            })
        },
        // 获取表格数据
        getData () {
            const params = {
                studyInstanceUid: this.params.studyInstanceUid,
            }
            this.loading = true;
            Api.findStudySeries(params).then(res => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res?.data || [];
                    return
                }
                this.tableData = [];
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading = false;
                this.tableData = [];
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.c-template {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    .c-box-01 {
        display: flex;
        flex: 1;
        height: 0px;
        overflow: auto;
        border: var(--el-border);
        .c-item {
            // height: 100%;
            flex: 1;
            overflow: auto;
        }
        .c-table {
            margin: 0 auto;
            padding: 0px 0px 20px 60px;
            .el-table {
                border-top: 0px;
            }
        }
    }

    .c-message {
        position: relative;
        text-align: right;
        padding-top: 10px;
    }
    
    .c-item-list {
        position: absolute;
        right: 0;
        bottom: 38px;
        width: 300px;
        min-height: 70px;
        max-height: calc(100% - 80px);;
        padding: 15px 5px 15px 15px;
        border: 1px solid #eee;
        background: white;
        box-shadow: 1px 1px 10px 1px #eee;
        display: flex;
        flex-direction: column;
        overflow: auto;
        z-index: 5;
        .i-checkBox {
            flex: 1;
            overflow: auto;
        }
        .i-button {
            margin-top: 10px;
            border-top: 1px solid #eee;
            padding-top: 10px;
            text-align: right;
        }
    }

    :deep(.el-col ){
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        display: -webkit-box;
    }
    :deep(.el-collapse-item > div) {
        .el-collapse-item__header  {
            height: 35px;
        }
        .el-collapse-item__arrow {
            color: white;
        }
    }
    .i-text {
        color: #666;
        padding-left: 10px;
    }
}
</style>
