<template>
    <!-- 状态回传 -->
    <el-button class="m-vertical-btn t2"
        :class="{'m-vertical-text': buttonMsg.isFold}"
        @click="d_sendMsg_v = true">
        <svg class="fa"
            aria-hidden="true">
            <use xlink:href="#fa-send1"></use>
        </svg>
        <label>状态回传</label>
    </el-button>

    <el-dialog append-to-body
        title="消息类型"
        v-model="d_sendMsg_v"
        :close-on-click-modal="false"
        width="520"
        top="15vh"
        class="t-default my-dialog">
        <div class="c-body"
            style="padding-left: 190px;min-height: 200px;">
            <el-checkbox-group v-model="formMessage"
                class="c-item-list"
                style="overflow: hidden;">
                <div v-for="item in BPMMsgOption"
                    :key="item.sValue">
                    <el-checkbox :label="item.sValue"
                        style="margin-bottom:10px;">{{ item.sName }}
                    </el-checkbox>
                </div>
            </el-checkbox-group>
        </div>
        <template #footer>
            <el-button-icon-fa type="primary"
                @click="onSendBPMMsgClick"
                icon="fa fa-send">发送</el-button-icon-fa>
            <el-button-icon-fa @click="d_sendMsg_v = false"
                icon="fa fa-close-1">关闭</el-button-icon-fa>
        </template>
    </el-dialog>

</template>

<script>
import { BPMMsgCreate } from '$supersetApi/projects/apricot/case/report.js'

export default {
    name: 'SendMessage',
    props: {
        patientInfo: {
            type: Object,
            default: () => ({})
        },
        buttonMsg: {
            type: Object,
            default: () => ({})
        }
    },
    data () {
        return {
            BPMMsgOption: this.$store.getters['dict/map'].BPMMsg || [],
            d_sendMsg_v: false,
            formMessage: []
        }
    },
    watch: {
        d_sendMsg_v (value) {
            if (value) {
                if (!this.BPMMsgOption.length) {
                    this.BPMMsgOption = this.$store.getters['dict/map'].BPMMsg || []
                }
            }
        }
    },
    methods: {
        onSendBPMMsgClick () {
            let sMsgPoint = this.formMessage.join(';');
            let jsonData = {
                sPatientId: this.patientInfo.sId,
                sMsgPoint: sMsgPoint
            }
            let load = this.$loading({
                lock: true,
                text: '正在发送中，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            BPMMsgCreate(jsonData).then(res => {
                load.close()
                if (res.success) {
                    this.$message.success(res.msg);
                    this.d_sendMsg_v = false;
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                load.close()
                console.log(err)
            })
        }
    },
    mounted () {

    }
}
</script>

<style lang="sass" scoped>

</style>