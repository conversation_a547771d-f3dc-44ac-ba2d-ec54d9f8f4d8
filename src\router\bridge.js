import welcome from './projects/welcome'
import apricot from './projects/apricot'
/**
 * 用于配置需要打包的项目，可多项目一起打包
 */
/*需要打包的项目列表-------------------------------------*/
let projects = [

	welcome,               // 欢迎页
    apricot,               // 报告系统
    
]
/*-------------------------------------需要打包的项目列表*/
let moduleList = (function(){
	return [].concat(...projects);
})();

// main 路径下的路由
const mainModuleList = moduleList.filter(item => {
	return !item.layer
})
// layer 路径下的路由
export const layerModuleList = moduleList.filter(item => {
	return item.layer
})

export default mainModuleList;
