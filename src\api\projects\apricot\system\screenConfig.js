import request from '$supersetUtils/request'
import {
    baseURL, stringify
} from '$supersetUtils/request'
export default {
   // 根据院区获取屏幕列表
    getScreenByDistricts(data) {
        return request({
            url: baseURL.apricot + '/call/screen/find/page',
            method: 'POST',
            data
        })
    },
    // 添加呼叫屏/显示屏
    addScreen(data) {
        return request({
            url: baseURL.apricot + '/call/screen/add',
            method: 'POST',
            data
        })
    },

    // 修改呼叫屏/显示屏
    editScreen(data) {
        return request({
            url: baseURL.apricot + '/call/screen/edit',
            method: 'POST',
            data
        })
    },
    // 删除呼叫屏/显示屏
    delScreen(params) {
        return request({
            url: baseURL.apricot + '/call/screen/del',
            method: 'POST',
            data:stringify(params)
        })
    },
    // 修改音频相关属性
    videoSet(data) {
        return request({
            url: baseURL.apricot + '/call/screen/mediaSet',
            method: 'POST',
            data
        })
    },
    // 启用/禁用头像
    changeAvatar(params) {
        return request({
            url: baseURL.apricot + '/call/screen/enable/disabled/portrait',
            method: 'POST',
            data:stringify(params)
        })
    },
    // 字幕  // 匿名显示
    changeShowCaptions(data) {
        return request({
            url: baseURL.apricot + '/call/screen/show/captions',
            method: 'POST',
            data
        })
    },
    // 启用禁用屏幕
    changeEnable(params) {
        return request({
            url: baseURL.apricot + '/call/screen/enable/disabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 获取视频/文字/图片列表
    getVideoList(params) {
        return request({
            url: baseURL.apricot + '/call/screen/init',
            method: 'POST',
            data:stringify(params)
        })
    },
    // 上传视频，图片
    upLoadVideo(data) {
        return request({
            url: baseURL.apricot + '/call/screen/video/upload',
            method: 'POST',
            data
        })
    },
    // 删除
    delVideo(params) {
        return request({
            url: baseURL.apricot + '/call/screen/video/del',
            method: 'POST',
            data:stringify(params)
        })
    },
    // 保存文字
    saveText(data) {
        return request({
            url: baseURL.apricot + '/call/screen/media/upload/text',
            method: 'POST',
            data
        })
    },

    // 启用、禁用
    saveOnOrOff(params) {
        return request({
            url: baseURL.apricot + '/call/screen/enable/disabled/video',
            method: 'POST',
            data:stringify(params)
        })
    },
    // 同步
    asyncVideo(params) {
        return request({
            url: baseURL.apricot + '/call/screen/video/sync',
            method: 'POST',
            data:stringify(params)
        })
    },

}