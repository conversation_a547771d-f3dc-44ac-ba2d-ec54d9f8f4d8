<template>
    <!-- 项目匹配 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa
                    type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    icon="el-icon-refresh"
                    @click="getTableData">刷新</el-button-icon-fa> 
            </div>
            <div class="c-form-search">
                <div>
                    <el-select v-model="condition.sDeviceTypeName"
                        placeholder="设备类型"
                        clearable>
                        <el-option v-for="(item, index) in optionsLoc.deviceTypeDataOptions"
                            :key="index"
                            :label="item.sDeviceTypeName"
                            :value="item.sDeviceTypeName">
                        </el-option>
                    </el-select>
                </div>
                <div>
                    <!-- @keyup.enter.native="getTableData" -->
                    <el-input v-model="condition.sOrderItemName"
                        placeholder="申请单项目"
                        clearable>
                    </el-input>
                </div>

                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="getTableData" :loading="loading"></el-button-icon-fa>
                </div>
            </div>
        </div>
         
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table :data="filterTableData"
                    ref="mainTable"
                    size="small"
                    @row-click="onClickRow"
                    :default-sort="{ prop: 'sDeviceTypeName', order: 'ascending' }"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip 
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort"
                        >
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button size="small"
                                    link
                                    type="primary"
                                    @click="handleEdit(scope.row)"
                                    >编辑
                                    <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class @click="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <!-- <i style="cursor: pointer;font-size: 14.8px;"
                                    @click.stop="onClickDel(scope.row)"
                                    class="el-icon-delete"></i> -->
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                <span v-else> 否 </span>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <v-contextmenu ref="contextmenuDiv">
            <v-contextmenu-item @click="getTreeData">刷新</v-contextmenu-item>
        </v-contextmenu>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="700"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="108px"
                    >
                    <el-col :span="24">
                        <el-form-item prop="sDeviceTypeId" label="设备类型：">
                            <el-select v-model="editLayer.form.sDeviceTypeId"
                                clearable
                                @change="handleChangeDeviceType">
                                <el-option v-for="(item, index) in optionsLoc.deviceTypeDataOptions"
                                    :key="index"
                                    :label="item.sDeviceTypeName"
                                    :value="item.sId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sItemId" label="检查项目：">
                            <el-select v-model="editLayer.form.sItemId">
                                <el-option v-for="(item,index) in optionsLoc.itemDataOptions"
                                    :key="index"
                                    :label="item.sItemName"
                                    :value="item.sId"></el-option>
                            </el-select>
                            <!-- <el-input readonly v-model="editLayer.form.sItemName"
                                placeholder="检查项目名称"></el-input> -->
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sOrderItemName" label="申请单项目：">
                            <el-input v-model="editLayer.form.sOrderItemName"
                                clearable
                                placeholder="申请单项目"></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="24">
                        <el-form-item label="项目编码：">
                            <el-input v-model="editLayer.form.sOrderItemCode"
                                clearable
                                placeholder="项目编码"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="iIsEnable" label="启  用：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-form>
            </div>
            <template #footer><div  class="dialog-footer">
                <el-button-icon-fa :loading="this.editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保存</el-button-icon-fa>
            
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
            </div></template>    
        </el-dialog>
    </div>
</template>
<script>
/** 自动登记匹配 */
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'AutoItemSet',
    mixins: [mixinTable],
    components: {},
    props: {},
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            loading: false,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                    iIsInvariable: 1
                }
            },
            condition: {},
            configTable: [
            {
                sProp: 'sOrderItemName',
                sLabel: '申请单项目',
                sAlign: 'left',
                sMinWidth: '120px'
            },
            {
                sProp: 'sItemName',
                sLabel: '检查项目',
                sAlign: '',
                sMinWidth: '120px'
            },
            {
                sProp: 'sDeviceTypeName',
                sLabel: '设备类型',
                sAlign: '',
                sMinWidth: '120px',
                iSort: 1
            },
            {
                sProp: 'iIsEnable',
                sLabel: '启用',
                sAlign: 'center',
                sMinWidth: '70px'
            },
            {
                sProp: 'action',
                sLabel: '操作',
                sAlign: 'center',
                sWidth: '200px',
                sFixed: 'right'
            }],
            rules: {
                sDeviceTypeId: [{required: true, message: '请选择设备类型'}],
                sItemId: [{required: true, message: '请选择检查项目'}],
                sApplyHospital: [{ required: true, message: '请输入编码' }],
                sApplyHospitalText: [{ required: true, message: '请输入名称' }],
                sOrderItemCode: [{ required: true, message: '请输入编码' }],
                sOrderItemName: [{ required: true, message: '请输入名称' }],
            },
            optionsLoc: {}
        }
    },
    computed: {
        filterTableData() {
            let data = [...this.tableData];
            const sOrderItemName = this.condition.sOrderItemName;
            const sDeviceTypeName = this.condition.sDeviceTypeName;
            if(sOrderItemName) {
                data = data.filter(item => item.sOrderItemName.toLocaleLowerCase().includes(sOrderItemName.toLocaleLowerCase()));
            }
            if(sDeviceTypeName) {
                data = data.filter(item => item.sDeviceTypeName.toLocaleLowerCase() == sDeviceTypeName.toLocaleLowerCase());
            }
            data.length && this.defaultSort()
            return data || [];
        }
    },
    methods: {
        defaultSort() {
           this.$nextTick(() => {
                const mainTable = this.$refs.mainTable;
                if (mainTable) {
                    const sortOrder = mainTable.store.states.sortOrder.value;
                    mainTable.sort('sDeviceTypeName', sortOrder);
                }
            })
        },
        // 新增
        handleAdd() {
            // this.defualtVal.editLayer.sItemName = this.selectTreeData.obj.sName
            this.mxOpenDialog(1, 'no-title');
            this.dialogTitle = '新增';
            this.dialogVisible = true
            let timeout = setTimeout(()=>{
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false
        },
        handleEdit(row) {
            this.getItemData(row.sDeviceTypeId)
            this.actionState = 2
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = Object.assign({},row)
            this.$nextTick(()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
            
        },

        handleSave() {
            this.editLayer.loading  = true
            let params = Object.assign({},this.editLayer.form)
            const target = this.optionsLoc.deviceTypeDataOptions.find( item => item.sId === params.sDeviceTypeId)
            params.sDeviceTypeName = target.sDeviceTypeName
            const target1 = this.optionsLoc.itemDataOptions.find( item => item.sId === params.sItemId)
            params.sItemName = target1.sItemName
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                  this.saveData(params)  
                  return
                }
                this.editLayer.loading = false
            })
        },
        // null2Undefin () {
        //     for (let i in this.editLayer.form) {
        //         if (this.editLayer.form[i] === null) {
        //             this.editLayer.form[i] = undefined
        //         }

        //     }
        // },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledAutoItemSet({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1;
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        mxOnClickReset () {
            this.condition = {};
        },

        // 获取设备类型
        getDeviceTypeData() {
            Api.getDeviceTypeData().then( res=>{
                if(res.success) {
                    this.optionsLoc.deviceTypeDataOptions = res?.data || []
                } 
            }).catch( err=>{
                console.log(err)
            })
        },
        handleChangeDeviceType(val) {
            this.editLayer.form.sItemId = ''
            this.getItemData(val)
        },
        getItemData(val) {
            const params = {
               condition:{
                    sDeviceTypeId: val
               },
                page:{
                    pageCurrent: 1,
                    pageSize: 9999
                }
            }
            Api.getItemData(params).then( res=>{
                if(res.success) {
                    this.optionsLoc.itemDataOptions = res.data || [];
                }
            }).catch( err=>{
                console.log(err)
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            if (this.actionState == 1) {
                
                // 新增
                Api.addAutoItemSet(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title');
                        this.getTableData();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return
            }
            // 修改
            Api.editAutoItemSet(params).then((res) => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.mxOpenDialog(1, 'no-title');
                    this.getTableData();
                    this.dialogVisible = false
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sOrderItemName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delAutoItemSet({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        if (this.editLayer.form.sId && this.editLayer.form.sId === row.sId) {
                            this.mxOpenDialog(1, 'no-title')
                        }
                        this.getTableData();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        // 获取表格数据
        getTableData () {
            this.loading = true;
            Api.getAutoItemSetData({
                // condition: this.condition,
                condition: {},
                page: {
                    pageSize: 500,
                    pageCurrent: 1
                }
            }).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.$nextTick( ()=>{
                        this.$refs.mainTable.sort('sDeviceTypeName', 'ascending')
                    })
                    this.mxSelected()
                }
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    created () {
    },
    mounted () {
        // this.getTreeData();
        this.getDeviceTypeData()
        this.getTableData()
    },
};
</script>
<style lang="scss" scoped>


.c-form .c-form-search {
    >div{
        width: 240px;
        margin:0 5px;
    }
}
.i-sort {
    font-size: 16px;
    margin-left: 10px;
}
</style>
