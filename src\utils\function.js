/**
 * 方法存放点
 * 2020/06/02
 */


/**
 * 深拷贝
 * @param {*} source 需要拷贝的数组、对象
 * @return 返回拷贝好的数据 
 */
export function deepClone(source) {
    const targetObj = source.constructor === Array ? [] : {}; // 判断复制的目标是数组还是对象
    for (let keys in source) { // 遍历目标
        if (source.hasOwnProperty(keys)) {
            if (source[keys] && ['Array', 'Object'].includes(typeOf(source[keys]))) { // 如果值是对象，就递归一下
                targetObj[keys] = source[keys].constructor === Array ? [] : {};
                targetObj[keys] = deepClone(source[keys]);
            } else { // 如果不是，就直接赋值
                targetObj[keys] = source[keys];
            }
        }
    }
    return targetObj;
}
/**
 * 时间戳转换时间
 * @param {*} t 时间
 * @param {*} isHMS 是否要时分秒
 * @param {String} format 年月日间隔符合
 */
export function transformDate(t, isHMS, format = '-') {
    if (t === undefined || t === null || t === '') {
        return;
    }
    let dd = new Date(t);
    let y = dd.getFullYear();
    if (isNaN(y) || t == null) {
        return;
    }
    let m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
    let d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
    let h = dd.getHours() < 10 ? '0' + dd.getHours() : dd.getHours();
    let mm = dd.getMinutes() < 10 ? '0' + dd.getMinutes() : dd.getMinutes();
    let s = dd.getSeconds() < 10 ? '0' + dd.getSeconds() : dd.getSeconds();
    if (isHMS) {
        return y + format + m + format + d + ' ' + h + ':' + mm + ':' + s;
    }
    if (format === '-') {
        return y + format + m + format + d;
    }
    if (format === null) {
        return `${y}${m}${d}`;
    }
    if (format === 'HH:mm') {
        return `${h}:${mm}`;
    }
    if (format === 'MM-dd HH:mm') {
        return `${m}-${d} ${h}:${mm}`;
    }
    if (format === 'yyyy-MM-dd HH:mm') {
        return `${y}-${m}-${d} ${h}:${mm}`;
    }
    if (format === 'yyyy年MM月dd日') {
        return `${y}年${m}月${d}日`;
    }
    if (format === 'yyyy年MM月dd日 HH时mm分') {
        return `${y}年${m}月${d}日 ${h}时${mm}分`;
    }
    if (format === 'yyyy年MM月dd日 HH:mm:ss') {
        return `${y}年${m}月${d}日 ${h}:${mm}:${s}`;
    }
    if (format === 'yy') {
        return `${y}`.substring(2);
    }
    if (format === 'yyMM') {
        return `${y}`.substring(2) + `${m}`;
    }
    if (format === 'yyMMdd') {
        return `${y}`.substring(2) + `${m}${d}`;
    }
    if (format === 'yyyy') {
        return `${y}`;
    }
    if (format === 'yyyyMM') {
        return `${y}${m}`;
    }
    if (format === 'yyyyMMdd') {
        return `${y}${m}${d}`;
    }
}
/**
 * 获取 Name
 * @param {any} value 
 * @param {array} array 
 */
export function getName(value, array) {
    let res = value;
    for (const _ of array) {
        if (value == _.sValue) {
            res = _.sName
            break;
        }
    }
    return res;
}

/**
 * 判断数据类型
 * @param {*} obj 数据
 * @return 返回类型 Array, String, Number, Date, Function, Object, Null, Undefined
 */
export function typeOf(obj) {
    return toString.call(obj).slice(8, -1)
}

/**
 * 获取时间类型
 * @param {*} t  时间
 * @param {String} key y，m，d，weekday
 * @param {Number} day 需要添加或减少天数
 */
export function getTimeType(t, key, day) {
    if (t === null || t === undefined) {
        return '';
    }
    let result = ''
    let dd = {}
    if (day != undefined || day != null) {
        dd = new Date(new Date().setDate(new Date(t).getDate() + day));
    } else {
        dd = new Date(t);
    }

    switch (key) {
        case 'y':
            var y = dd.getFullYear();
            result = y;
            break;
        case 'm':
            var m = (dd.getMonth() + 1) < 10 ? '0' + (dd.getMonth() + 1) : (dd.getMonth() + 1);
            result = m
            break;
        case 'd':
            var d = dd.getDate() < 10 ? '0' + dd.getDate() : dd.getDate();
            result = d
            break;
        case 'weekday':
            var weekday = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];
            var myddy = dd.getDay()
            result = weekday[myddy]
            break;

        default:
            break;
    }
    return result;
}

// 判断是否是移动端，是返回 true
export function isMobileDevice() {
    let agents = ["Android", "iPhone",
        "SymbianOS", "Windows Phone",
        "iPad", "iPod"
    ]

    let userAgentInfo = navigator.userAgent;
    let flag = false;
    for (let v = 0; v < agents.length; v++) {
        if (userAgentInfo.indexOf(agents[v]) > 0) {
            flag = true;
            break;
        }
    }
    return flag
}

// 添加下拉隐藏项，目的：避免value在array中没有匹配到的时候，不显示label的问题。
export function setSelectOptionHideAuxItem(arr, value, label, val_key = 'sValue', label_key = 'sName') {
    if(Object.prototype.toString.call(arr) !== '[object Array]' || [undefined, null].includes(value)) {
        // arr不是数组类型或value = undefined(null)
        return
    }
    let targetItem = arr.find(item => item[val_key] == value);
    if (!targetItem) {
        let temp = {};
        temp.iIsAuxiliary = 1
        temp[val_key] = value;
        temp[label_key] = label;
        arr.push(temp)
    }
}