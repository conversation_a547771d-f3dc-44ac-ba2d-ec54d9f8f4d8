<template>
    <el-dialog
        title="还原病例"
        :close-on-click-modal="false"
        :modelValue="visible"
        width="70%"
        align-center
        class="t-default my-dialog "
        @close="handleClose">
            <div class="m-content">
                <div class="flex items-center pb-2">
                    <el-input style="width:300px" v-model="condition.sName" class="mr-2" placeholder="姓名" @keyup.enter.native="mxDoSearch" />
                    <el-button-icon-fa plain type="primary" icon="el-icon-search" @click="mxDoSearch">搜索</el-button-icon-fa>
                </div>
                <el-table ref="mainTable"
                    :loaing="tableLoading "
                    :data="tableData"
                    stripe
                    border
                    height="60vh"
                    style="width: 100%;">
                    <el-table-column type="index"
                        label="序号"
                        align="center"
                        width="60">
                        <template v-slot="scope">
                            {{scope.$index + 1}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="sName" label="姓名">
                        <template v-slot="scope">
                            {{scope.row.sName}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="sSexText" label="性别">
                         <template v-slot="scope">
                            {{scope.row.sSexText}}
                        </template>
                    </el-table-column> 
                    <el-table-column prop="sAge" label="年龄">
                         <template v-slot="scope">
                            {{scope.row.sAge}}
                        </template>
                    </el-table-column>    
                    <el-table-column prop="sNuclearNum" label="核医学号" show-overflow-tooltip>
                         <template v-slot="scope">
                            {{scope.row.sNuclearNum}}
                        </template>
                    </el-table-column>  
                    <el-table-column prop="sProjectName" label="检查项目" show-overflow-tooltip>
                         <template v-slot="scope">
                            {{scope.row.sProjectName}}
                        </template>
                    </el-table-column> 
                    <el-table-column prop="" label="操作" width="110px" align="center">
                         <template v-slot="scope">
                            <el-button-icon-fa type="primary" link icon="fa fa-restore"
                                @click="restorePatient(scope.row)">还原</el-button-icon-fa>
                        </template>
                    </el-table-column>     
                </el-table>
                <div class="c-pagination scope-pagination-around">
                    <el-pagination background @size-change="onSizeChange"
                        @current-change="onCurrentChange"
                        :current-page="page.pageCurrent"
                        :page-sizes="mxPageSizes"
                        :pager-count="5"
                        :page-size="page.pageSize"
                        layout="total, sizes, prev, pager, next"
                        :total="page.total">
                    </el-pagination>
                </div>
            </div>
            <template #footer>
                <el-button-icon-fa icon="fa fa-close-1" @click="handleClose">关闭</el-button-icon-fa>
            </template>
    </el-dialog>
</template>
<script>

import Api from '$supersetApi/projects/apricot/appointment/patientInfo.js'
// 混入
import {  mixinTable } from '$supersetResource/js/projects/apricot/index.js'

export default {
    name: 'Preview',
    mixins:[mixinTable],
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        }
    },
    data () {
        return {
            isMixinDynamicGetTableHead: true, // 是否动态获取表头
            tableLoading : false,
            tableData:[],
            rePage: { // 分页	
                pageCurrent: 1,
                pageSize: 30,
                total: 0
            },
            doDeled: false,
            condition:{
                sName:'',
            }
        }
    },
    computed: {
        visible() {
            return this.dialogVisible
        }
    },
    watch: {
       dialogVisible(val) {
        if(val) {
            this.mxDoSearch()
        }
       }
    },
    methods: {
       handleClose() {
            // this.visible = false
            this.doDeled = false
            this.$emit('update:dialogVisible', false)
       },
       restorePatient(row) {
            let params = {
                sId: row.sId
            }
            Api.restorePatient(params).then( res=>{
                if(res.success) {
                    this.doDeled = true
                    this.$message.success(res.msg)
                    this.mxDoRefresh()
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{

            })
       },
       getData(params) {
            this.tableLoading = true
            Api.deledList(params).then( res=>{
                this.tableLoading = true
                if(res.success) {
                    
                    this.page.total = res.data.total
                    this.page['totalPage'] = res.data.pages;
                    this.tableData = res.data.records?res.data.records:[]
                    this.mxSetSelected()
                    return
                }
                this.$message.error(res.msg)
            }).catch( ()=>{

            })
       },
    //    // 翻页
    //     onReSizeChange(val) {
    //         this.page.pageSize = val
    //         this.mxGetTableList();
    //         this.editLayer.selectedItem = {};
    //     },
    //     // 切页
    //     onReCurrentChange(val) {
    //         this.rePage.pageCurrent = val
    //         this.mxGetTableList();
    //         this.editLayer.selectedItem = {};
    //     },

    },
    mounted () {

    }

}
</script>
<style lang="scss" scoped>
// :deep(.el-dialog ){
//     min-width: 800px;
//     height: 70%;
//     overflow: hidden;
//     .el-dialog__body {
//         height: calc(100% - 40px);
//     }
//     .m-content {
//         height: calc(100% - 90px);
//     }
// }
:deep(.el-table td) {
    padding: 2px 0;
}
.pb-2 {
    padding-top: 5px;
    border-bottom: var(--el-border);
}
</style> 
