import dictApi from '@/api/projects/apricot/system/dictionary.js'
const state = {
  map: {
  },
  list: [],
  loaded: false
}
const getters = {
  map: (state) => state.map,
  list: (state) => state.list
}

const mutations = {

}

const actions = {
  async getDictionary({ state }, force = false) {
    if (state.loaded && !force) return
    const res = await dictApi.queryAllDictAndItem({})
    const map = {}
    res?.data.forEach(dict => {
      const dictList = []
      dict.items.forEach(item => {
        dictList.push({
            sName: item.itemName,
            sValue: item.itemCode
        })
      });
      map[dict.dictCode] = dictList;
    })

    state.map = map
    state.list = res?.data
    state.loaded = true
  }

}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
