<template>
  <LayoutTable class="c-template">
    <template #header>
      <SearchList v-model:modelValue="condition" v-model:list="searchListConfig" :optionData="optionsLoc"
        :iModuleId="iModuleId" storageKey="RetrieveIndexSearch" labelWidth="100px"
        @changeSearch="mxDoSearch" @reset="onClickReset" >
        <template v-slot:dMachineTimeSt>
          <el-date-picker v-model="condition.dMachineTimeSt" type="date" :picker-options="pickerOptiondMachineTimeStart"
            @change="changeMachineTime" style="height:100%;">
          </el-date-picker>
        </template>
        <template v-slot:dMachineTimeEd>
          <el-date-picker v-model="condition.dMachineTimeEd" type="date" :picker-options="pickerOptiondMachineTimeEnd"
            @change="changeMachineTime" style="height:100%;">
          </el-date-picker>
        </template>
        <template v-slot:sApplyDeptName>
          <el-select v-model="condition.sApplyDeptName" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="(item, index) in optionsLoc[`sApplyDeptNameOptions`]" :key="index"
              :label="item.sApplyDeptName" :value="item.sApplyDeptName">
            </el-option>
          </el-select>
        </template>
         <template v-slot:sDistrictId>
            <el-select v-model="condition.sDistrictId"
                placeholder=""
                @change="useChangeHospital"
                clearable>
                <el-option v-for="(item, index) in optionsLoc.districtArrOption"
                    :key="index"
                    :label="item.sDistrictPrefix"
                    :value="item.sId">
                </el-option>
            </el-select>
        </template>
        <template v-slot:sMachineryRoomId>
            <el-select v-model="condition.sMachineryRoomId"
                @change="useChangeMachineRoom"
                placeholder=""
                clearable>
                <el-option v-for="(item, index) in optionsLoc.machineRoomArrOption"
                    :key="index"
                    :label="item.sRoomName"
                    :value="item.sId">
                </el-option>
            </el-select>
        </template>
        <template v-slot:sProjectIds>
            <el-select v-model="condition.sProjectIds"
                placeholder=""
                @change="mxDoSearch"
                clearable
                class="no-wrap-select" multiple collapse-tags collapse-tags-tooltip>
                <el-option v-for="(item, index) in optionsLoc.itemsArrOption"
                    :key="index"
                    :label="item.sItemName"
                    :value="item.sId">
                    <span style="float: left">{{ item.sItemName }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.sDeviceTypeName }}</span>
                </el-option>
            </el-select>
        </template>
        <template v-slot:sPositionText>
          <el-select v-model="condition.sPositionText" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.positionOptions" :key="item.sId" :label="item.sItemPositionName"
              :value="item.sItemPositionName"></el-option>
          </el-select>
        </template>
        <template v-slot:sNuclideText>
          <el-select v-model="condition.sNuclideText" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.nuclideOptions" :key="item.sId" :label="item.sNuclideName"
              :value="item.sNuclideName">
              <span style="float: left">{{ item.sNuclideName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px" v-html="item.sNuclideSupName"></span>
            </el-option>
          </el-select>
        </template>
        <template v-slot:caseTree>
          <el-cascader ref="myCascader1" placeholder="" v-model="condition.recordTeach" clearable :props="defaultProps2"
            :options="optionsLoc.ApricotReportCaseType" @change="setConditionCaseTypeIds"></el-cascader>
        </template>
        <template v-slot:sApplyDrName>
          <el-select v-model="condition.sApplyDrName" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="(item, index) in optionsLoc[`sApplyDrNameOptions`]" :key="index" :label="item.sApplyDrName"
              :value="item.sApplyDrName">
            </el-option>
          </el-select>
        </template>
        <!-- [注射医生, 上机医生, 报告医生, 审核医生, 报告评级医生, 影像评级医生] -->
        <template v-for="sProp in ['sInjectName','sMachineName', 'sReporterName', 'sExamineName', 'sReportQualityDoc', 'sImgQualityDoc']" :key="sProp" v-slot:[sProp]>
          <el-select v-model="condition[sProp]" placeholder="" clearable filterable allow-create default-first-option
            style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc[`${sProp}Options`]" :key="item.sId" :label="item.userName"
              :value="item.userName">
            </el-option>
          </el-select>
        </template>
        <template
          v-for="sProp in ['sSex', 'sQualitative', 'sDiagnosticAccord', 'sImgQuality', 'sReportQuality', 'iIsPregnant']"
          :key="sProp" v-slot:[sProp]="{ data }">
          <el-select v-model="condition[sProp]" placeholder="" clearable @change="mxDoSearch">
            <el-option v-for="item in optionsLoc[data.sOptionProp]" :key="item.sValue" :label="item.sName"
              :value="item.sValue"></el-option>
          </el-select>
        </template>
        <template v-slot:sCollectionMemo>
          <el-select v-model="condition.sCollectionMemo" placeholder="" clearable filterable allow-create
            default-first-option style="width:100%" @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.sCollectionMemoOptions" :key="item.sId" :label="item.sTagName"
              :value="item.sTagName">
            </el-option>
          </el-select>
        </template>
        <template v-slot:sSource>
          <el-select v-model="condition.sSource" placeholder="" clearable @change="mxDoSearch">
            <el-option v-for="item in optionsLoc.visitTypeOptions" :key="item.sValue" :label="item.sName"
              :value="item.sValue"></el-option>
          </el-select>
        </template>
      </SearchList>
    </template>
    <template #action>
      <div class=" flex items-center justify-between">
        <div class="flex items-center">
          <span class="mr-2">
            <el-button-icon-fa v-auth="'report:search:export'" icon="fa fa-file-excel-o" type="primary" plain @click="doExportTable(false)" >
               批量导出
            </el-button-icon-fa>
            <el-button-icon-fa v-auth="'report:search:export'" icon="fa fa-file-excel-o" type="primary" plain @click="doExportTable(true)" >
               匿名导出
            </el-button-icon-fa>
            <el-button-icon-fa v-auth="'report:search:export'" icon="fa fa-file-excel-o" type="primary" plain @click="mxExportSelectionTable" >
               勾选导出
            </el-button-icon-fa>
            <!-- <el-button type="primary" plain class="mr-2" @click="onOpenPrintSet">打印设置</el-button> -->
          </span>
        </div>
        <div class="flex items-center" ref="scrollPane01">
          <el-checkbox v-auth="'report:search:browserReport'" v-model="isPreview" :disabled="!tableData.length">预览模式</el-checkbox>
          <!-- <el-checkbox v-model="IsdMachineTime" class="mr-2" @change="mxDoSearch">检查日期</el-checkbox> -->
          <span class="ml-4">
          </span>
          <el-button @click="onClickReset"><template #icon>
                <Icon name="el-icon-refresh-left" >
                </Icon>
            </template>重置</el-button>
          <el-button v-auth="'report:search:query'" type="primary" @click="mxDoSearch"
            :loading="loading"> <template #icon><Icon name="el-icon-search" color="white"></Icon></template>查询</el-button>
        </div>
      </div>
    </template>
    <template #content>
      <DragAdjust class="flex w-full h-full" :dragAdjustData="computedDA1">
        <template v-slot:c1>
          <el-table-extend v-if="patientTableConfig" ref='mainTable' :iModuleId="iModuleId" class="mainTable"
            storageKey="RetrieveIndexTable" v-loading="loading" :data="tableData" stripe :row-class-name="mxRowClassName"
            @row-dblclick="mxOpenDialog(4, '')" @row-click="onClickRow" @sort-change="mxOnSort"
            @selection-change="handleSelectionChange" highlight-current-row height="100%" style="width: 100%">
            <el-table-column fixed type="selection" label="选择" prop="_select" align="center" width="50">
            </el-table-column>
            <el-table-column label=" " prop="_index2" isController="true" align="center" width="1"
              :class-name="'tableColOverflowVisible'">
              <template v-slot="scope">
                <template v-if="scope.row.index === editLayer.selectedItem.index">
                    <div class="row-drawer" :style="styleVar">
                      <div class="g-content ">

                        <el-button v-auth="'report:search:csView'"  oldicon="fa fa-human-organ-image" type="primary" plain  @click="rebuildOpen(scope.row)">阅图</el-button>
                        <el-button v-auth="'report:search:csRebuild'"  oldicon="fa fa-poly-rebuildding" type="primary" plain  @click="rebuildOpen(scope.row, 1)">重建</el-button>
                        <el-button v-auth="'report:search:webView'" oldicon="fa fa-reading-image-computer-browse" type="primary" plain @click="openWebReadViewer()">web阅图</el-button>
                        <el-button v-auth="'report:search:webRebuild'" oldicon="fa fa-zhongjian" type="primary" plain  @click="openWebReBuildViewer()">web重建</el-button>

                        <!-- 报告预览 -->
                        <ReportPreviewBtn v-auth="'report:search:browserReport'"
                          :propParams="{ patient: scope.row, idKey: 'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }">
                          <el-button oldicon="fa fa-article" type="primary" plain
                              style="margin-left: 9px;">预览报告</el-button>
                        </ReportPreviewBtn>

                        <!-- 报告打印 -->
                        <!-- <ReportPrintBtn
                          :propParams="{ patient: scope.row, isBatch: false, idKey: 'sId', deviceTypeIdKey: 'sRoomId', iModuleId: iModuleId }">
                        </ReportPrintBtn> -->
                      </div>
                    </div>
                  </template>
              </template>
            </el-table-column>
            <el-table-column type="index" prop="_index" label="序号" align="center" width="60">
            </el-table-column>

            <el-table-column v-for="item in patientTableConfig" :key="item.index"
              :show-overflow-tooltip="item.sProp !== 'img'" :prop="item.sProp" :label="item.sLabel" :fixed="item.sFixed"
              :align="item.sAlign" :width="item.sWidth" :min-width="item.sMinWidth"
              :sortable="(!!item.iSort) ? 'custom' : false" :column-key="item.sSortField ? item.sSortField : null"
              :iIsHide="item.iIsHide">
              <template v-slot="scope">
                <template v-if="FlowStateEnum.includes(item.sProp)">
                  <i v-if="scope.row[`${item.sProp}`]" :class="`fa ${FlowStateEnumIcon[item.sProp]}`"></i>
                </template>
                <template v-else-if="['sNuclideSupName', 'sTracerSupName'].includes(item.sProp)">
                    <span v-if="scope.row[item.sProp]" v-html="scope.row[item.sProp]"></span>
                </template>
                <template v-else-if="['sImgQualityText', 'sReportQualityText'].includes(item.sProp)">
                    {{ scope.row[item.sProp] ??  getQualityName(item.sProp, scope.row)   }}
                </template>
                <template v-else-if="['fRecipeDose'].includes(item.sProp)">
                    {{ setRecipeDose(scope.row[item.sProp], scope.row.sRecipeDoseUnit) }}
                </template>
                <template v-else-if="['fBloodSugar'].includes(item.sProp)">
                    {{ setBloodSugar(scope.row[item.sProp]) }}
                </template>
                <template v-else-if="['iIsPregnant'].includes(item.sProp)">
                    {{ setPregnantText(scope.row[item.sProp]) }}
                </template>
                <template v-else-if="item.sProp.slice(0, 1) === 'd'">
                  {{ mxFormatterDate(scope.row[`${item.sProp}`]) }}
                </template>
                <template v-else>
                  {{ scope.row[`${item.sProp}`] }}
                </template>
              </template>
            </el-table-column>
          </el-table-extend>
        </template>
        <template v-slot:c2>
          <Preview v-model:patient="editLayer.selectedItem"></Preview>
        </template>
      </DragAdjust>
    </template>
    <template #footer>
      <el-pagination background @size-change="onSizeChange" @current-change="onCurrentChange"
        :current-page="page.pageCurrent" :page-sizes="mxPageSizes" :pager-count="5" :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next" :total="page.total">
      </el-pagination>
    </template>
  </LayoutTable>

  <!-- 采集头像 -->
  <CollectAvatar :dialogVisible="d_CollectAvatar_v" :patientInfo="editLayer.selectedItem"
    :index="editLayer.selectedItem.index" @refreshImg="refreshImg" @closeDialog="closeCollectAvatarDialog">
  </CollectAvatar>
  

  <!-- 打印设置 -->
  <PrintSet :dialogVisible="d_printSet_v" :iModuleId="iModuleId" @closeDialog="closePrintSetDialog"></PrintSet>
</template>

<script>
import { Upload } from '@element-plus/icons-vue';
//模块辅助样式
import FormStyle from '$supersetViews/components/FormStyle.vue'
import ScrollPane from '$supersetViews/components/ScrollPane.vue'
// 混入
import {
  mixinTable, mixinAvatar, mixinElementConfigs, mixinTableInner, openWebReadImgOrRebuild, 
  mixinCaseType, mixinExportExcel } from '$supersetResource/js/projects/apricot/index.js'

// 配置
import Configs from './configs'
import { deepClone } from '$supersetUtils/function'
import { appointmentEnum, caseEnum, reportEnum } from '$supersetResource/js/projects/apricot/enum.js'

import { researchPage, getApplyDept, getApplyDr } from '$supersetApi/projects/apricot/retrieve'
import ApiAssist from '$supersetApi/projects/apricot/assistServe/index.js'
import { getItemPositionData, getNuclideData } from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { getCollectionTagData } from '$supersetApi/projects/apricot/common/collectTag.js'
import { queryUserListByType } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'

import { createNamespacedHelpers } from 'vuex'
// 引入获取院区，机房，项目方法
import { useGetHospitalData, useGetMachineRoomData, useGetItemData, useChangeHospital, 
    useChangeMachineRoom, getAllUsersData, getReportAboveDrData} from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
const { mapMutations } = createNamespacedHelpers('apricot/consult_module')

export default defineComponent({
  name: 'apricot_Retrieve',
  mixins: [mixinTable, mixinAvatar, mixinElementConfigs, mixinTableInner, mixinCaseType, mixinExportExcel],
  components: {
    FormStyle,
    ScrollPane,
    Upload,
    TableAvatar: defineAsyncComponent(() => import('$supersetViews/apricot/components/TableAvatar.vue')),
    CollectAvatar: defineAsyncComponent(() => import('$supersetViews/apricot/components/CollectAvatar.vue')),
    ReportPreviewBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPreviewBtn.vue')),
    // ReportPrintBtn: defineAsyncComponent(() => import('$supersetViews/apricot/components/ReportPrintBtn.vue')),
    Preview: defineAsyncComponent(() => import('$supersetViews/apricot/reportCase/components/Preview.vue')),  // 报告预览
    PrintSet: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintSet.vue'))
  },
  data() {
    return {
      iModuleId: 10, // 综合查询标识 ，eName: 'RETRIEVE_SEARCH'， 在mixinPrintPreview混合模块中调用
      isMixinDynamicGetTableHead: true,   // 是否动态获取表头
      // patientTable: Configs.patientTable,
      searchStyle0: Configs.searchStyle0,
      searchStyle1: Configs.searchStyle1,
      searchListConfig: [...Configs.searchStyle0, ...Configs.searchStyle1],
      patientTableConfig: [...Configs.patientTable],
      DA1: Configs.DA1,
      currentProcess: '',
      checkboxDateStatus: false,
      isShow1: false,
      optionsLoc: {
        iIsReportCommitOptions: reportEnum.report,
        iIsApproveOptions: reportEnum.approve,
        visitTypeOptions: appointmentEnum.visitTypeOptions,
        sAgeUnitOptions: appointmentEnum.ageUnitOptions,
        sQualitativeOptions: caseEnum.qualitative,
        sDiagnosticAccordOptions: caseEnum.diagnosticAccord,
        sImgQualityOptions: caseEnum.imgQuality,
        sReportQualityOptions: caseEnum.reportQuality,
        sSexOptions: appointmentEnum.sexOptions,
        positionOptions: [],
        nuclideOptions: [],
        sMachineNameOptions: [],
        sReporterNameOptions: [],
        sExamineNameOptions: [],
        sReportQualityDocOptions:[], 
        sImgQualityDocOptions:[],
        iIsPregnantOptions: [
          { sName: '否', sValue: 0 },
          { sName: '是', sValue: 1 },
          { sName: '未知', sValue: 2 },
          { sName: '不适用', sValue: 3 },
        ],
        sApplyDeptNameOptions: [],
        sApplyDrNameOptions: [],
        sDistrictOptions: [],
        sCollectionMemoOptions: [],
        districtArrOption: [], //院区
        machineRoomArrOption: [], // 机房
        itemsArrOption: [],    // 项目
        iIsFinalApproveOptions: reportEnum.finalApprove,
        sInjectNameOptions: []
      },
      defaultProps2: {
        children: 'childs',
        label: 'sName',
        value: 'sId',
        checkStrictly: true,
        expandTrigger: 'hover'
      },
      config202101221346: {
        value: 'sId',
        label: 'sName',
        children: 'childs',
        checkStrictly: true,
        expandTrigger: 'hover'
      },
      pickerOptiondMachineTimeStart: {
        disabledDate: time => {
          if (this.condition.dMachineTimeEd) {
            return time.getTime() > new Date(this.condition.dMachineTimeEd).getTime() || time.getTime() > new Date().getTime()
          }
          return time.getTime() > new Date().getTime()
        }
      },
      pickerOptiondMachineTimeEnd: {
        disabledDate: time => {
          if (this.condition.dMachineTimeSt) {
            return time.getTime() < new Date(this.condition.dMachineTimeSt).getTime() || time.getTime() > new Date().getTime()
          }
          return time.getTime() > new Date().getTime()
        }
      },
      IsdMachineTime: true,
      multipleSelection: [],
      isPreview: window.localStorage.getItem('RetrieveIndex-isShowPreviewModel') == 'true',
      moreSearchHeight: null,
      d_printSet_v: false,
      condition: {
        dMachineTimeSt: '',
        dMachineTimeEd: new Date(),
      },
      page: {  // 分页	
        pageCurrent: 1,
        pageSize: localStorage.retrieveIndexPageSize ? JSON.parse(localStorage.retrieveIndexPageSize) : 30,
        total: 0
      },
    }
  },
  computed: {
    userInfo() {
      let temp = this.$store.getters['user/userSystemInfo']
      if (temp.__proto__.constructor === Object) {
        return temp
      } else {
        return {}
      }
    },
    computedDA1() {
      const panelConfig = [{
        size: 0,
        minSize: 100,
        name: "c1",
        isFlexible: true
      },
      {
        size: 750,
        minSize: 10,
        maxSize: 1700,
        name: "c2",
        isFlexible: false
      }
      ].filter(i => this.isPreview ? 1 : i.name === 'c1')
      const DA1 = {
        type: 't-x',
        localStorageKey: '202308100000',
        panelConfig: panelConfig
      }
      return DA1
    },
    styleVar() {
    //   console.log(this.$refs.mainTable?.tableRef);
        return {
          "--activeTableWidth": this.$refs.mainTable?.tableRef?.bodyWidth
        }
    },
  },
  watch: {
    'page.pageSize'(val) {
        localStorage.setItem('retrieveIndexPageSize', val);
    },
    checkboxDateStatus(value) {
      if (!value) {
        this.condition['dates'] = ''
      }
    },
    isPreview(val) {
      window.localStorage.setItem('RetrieveIndex-isShowPreviewModel', val)
    }
  },
  methods: {
    ...mapMutations([
      'setPatientInfo'
    ]),
    useGetHospitalData, 
    useGetMachineRoomData, 
    useGetItemData,
    useChangeHospital,
    useChangeMachineRoom,
    // 获取sName属性
    getQualityName (text, row) {
        let props = text.substring(0, text.length- 4)
        let item = this.optionsLoc.sReportQualityOptions.find(item => item.sValue == row[props]);
        return item ? item.sName : null;
    },
    //  打开打印设置
    onOpenPrintSet() {
      this.d_printSet_v = true;
    },
    closePrintSetDialog() {
      this.d_printSet_v = false;
    },
    // 导出, 处理数据并发起导出请求
    doExportTable(isAnonymous = false) {
      // 复制查询条件
      let condition = deepClone(this.condition);
      condition.iIsCancel = 0;
      // 删除多余的字段
      delete condition.caseTree;

      const sModuleName = 'research';

      const filename = '综合查询';
       // 调用统一混入导出方法   
      this.mxDoExportTableByModule(condition, sModuleName, isAnonymous, filename);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 重置
    onClickReset() {
      this.condition = this.$options.data().condition
      this.setDefaultSearchTimes()
    },
    // 打开web重建
    openWebReBuildViewer(iIsRebuild = 1) {
      if (!Object.keys(this.editLayer.selectedItem).length) {
        this.$message.warning('请选择一条患者数据');
        return
      }
      let info = {
        sPatientId: this.editLayer.selectedItem.sPatientId,
        deviceTypeId: this.editLayer.selectedItem.sRoomId
      }
      openWebReadImgOrRebuild(iIsRebuild, this.userInfo.sId, info);
    },
    // 打开web阅图
    openWebReadViewer() {
      this.openWebReBuildViewer(0)
    },
    // 打开阅图、重建
    rebuildOpen(row, iIsRebuid = 0) {
      // if (!row.sImgPatientId) {
      //     this.$message.warning('图像患者标识不存在');
      //     return
      // }
      const params = {
        sImgPatientId: row.sImgPatientId,
        sImgStudyDate: row.sImgStudyDate,
        sImgAccessionNumber: row.sImgAccessionNumber,
        iIsRebuid: iIsRebuid
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中...',

        background: 'rgba(0, 0, 0, 0.2)'
      });
      ApiAssist.rebuildOpenView(params).then(res => {
        loading.close();
        if (res && !res.success) {
          this.$message.error(res.msg)
          return
        }
      }).catch(() => {
        loading.close()
      })
    },
    getSelectedTreeNodeArr(filterArr, seletedsIds, skey = 'sId') {
      if (!seletedsIds.length) {
        return []
      }
      let target = [], orign = [...filterArr];
      let temp = null;
      for (let i = 0; i < seletedsIds.length; i++) {
        temp = orign.find(item => item[skey] == seletedsIds[i]);
        if (temp) {
          target.push(temp);
          orign = temp.childs;
        }
      }
      return target
    },

    changeMachineTime() {
      // if (!this.IsdMachineTime) {
      //   this.IsdMachineTime = true
      // }
      // if (!this.condition.dMachineTimeSt && !this.condition.dMachineTimeEd) {
      //   this.IsdMachineTime = false
      // }
      this.mxDoSearch()

    },
    // 查询数据
    getData(obj) {
      let params = deepClone(obj);
      delete params.orders;
      delete params.condition.recordTeach;
      let dMachineTimeSt = params.condition.dMachineTimeSt;
      params.condition.dMachineTimeSt = this.mxFormateOneDayStart(dMachineTimeSt);

      // 当选择了结束时间，转换成23：59：59
      let dMachineTimeEd = params.condition.dMachineTimeEd;
      params.condition.dMachineTimeEd = this.mxFormateOneDayEnd(dMachineTimeEd);

      // 不勾选预约日期的时候 删除 params对象 时间起止属性
      // if (!this.IsdMachineTime) {
      //   delete params.condition.dMachineTimeSt;
      //   delete params.condition.dMachineTimeEd
      // }
      if(!params.condition.sProjectIds.length) {
        delete params.condition.sProjectIds
      }
      delete params.condition.itemTree
      params.condition.iIsCancel = 0
      this.tableData = [];
      researchPage(params).then((res) => {
        this.tableData = [];
        // this.multipleSelection = [];
        if (res.success) {
          this.tableData = res.data.recordList == null ? [] : res.data.recordList;
          if (this.tableData.length) {
            this.tableData.forEach((item) => {
              item.sId = item.sPatientId;
            })
          }
          this.page.total = res.data.countRow;
          this.loading = false;
          // 赋选中状态
          this.mxSetSelected();
          return
        }
        this.loading = false;
        this.$message.error(res.msg);
      }).catch(() => {
        this.loading = false;
      })
    },
    closeProcess() {
      this.currentProcess = ''
    },
    switchRetrieveKey() {
      this.isShow1 = !this.isShow1;
      this.getOptionsData('positionOptions', getItemPositionData);
      this.getOptionsData('nuclideOptions', getNuclideData);
      // this.getOptionsData('tracerOptions', getTracerData);
      // this.getOptionsData('testModeOptions', getTestModeData);
      this.mxCaseTypeTree(); // 病例类型
      this.getApplyDr();
      this.getCollectionTagData();
    },
    onPreportPrint() {
      if (!this.templateList.length) {
        this.$message.info('不存在预览模板，请前往打印模板设置');
        return
      }
      if (this.templateList.length === 1) {
        this.openPintWindow(this.templateList[0].sId)
        return
      }
    },
    // 请求接口获取下拉数据
    getOptionsData(optionName, callback) {
      if (this.optionsLoc[optionName].length) {
        return
      }
      let jsonData = { }
      callback(jsonData).then(res => {
        if (res.success) {
          this.optionsLoc[optionName] = res?.data || [];
          return
        }
        this.$message.error(res.msg);
      }).catch(() => {
      })
    },

    getApplyDept() {
      if (this.optionsLoc.sApplyDeptNameOptions.length) {
        return
      }
      getApplyDept().then(res => {
        if (res.success) {
          this.optionsLoc.sApplyDeptNameOptions = res.data || [];
          return
        }
        this.$message.error(res.msg);
      })
    },
    getApplyDr() {
      if (this.optionsLoc.sApplyDrNameOptions.length) {
        return
      }
      getApplyDr().then(res => {
        if (res.success) {
          this.optionsLoc.sApplyDrNameOptions = res.data || [];
          return
        }
        this.$message.error(res.msg);
      })
    },
   
    // 获取收藏标签下拉框数据
    getCollectionTagData() {
      getCollectionTagData().then(res => {
        this.loading = false;
        if (res.success) {
          this.optionsLoc.sCollectionMemoOptions = res.data || [];
          return
        }
      }).catch(err => {
        console.log(err);
      })
    },
    setDefaultSearchTimes() {
      let date = new Date();
      this.condition.dMachineTimeSt = new Date(date.setDate(1))
      this.condition.dMachineTimeEd = new Date()
    },
  },
  async mounted() {
    this.setDefaultSearchTimes()
    await this.useGetHospitalData();
    this.optionsLoc['sInjectNameOptions'] = await queryUserListByType(4); 
    this.optionsLoc['sMachineNameOptions'] = await queryUserListByType(5); 
    const doctorsData = await getReportAboveDrData()
    this.optionsLoc['sReporterNameOptions'] = doctorsData['reportDoctors'] // 获取报告
    this.optionsLoc['sExamineNameOptions'] = doctorsData['auditDoctors'] // 获取审核医生
    
    this.optionsLoc['sReportQualityDocOptions'] = this.optionsLoc['sReporterNameOptions'];
    this.optionsLoc['sImgQualityDocOptions'] =  this.optionsLoc['sExamineNameOptions'] 
    this.useGetMachineRoomData();
    // 查询项目
    this.useGetItemData()
    // 等待表头数据加载完成，再请求表给数据
    this.mxGetTableList();
    this.getApplyDept();
    this.switchRetrieveKey()
    

  }
})
</script>

<style lang="scss" scoped>
$borderColor: #cdcecf;
$borderColor2: #eeeeee;

.c-template {
  position: relative;
}

.c-convenientSearch {
  padding: 10px 10px 10px 5px;
}

.c-star {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .i-star {
    font-size: 24px;
    color: #ffc43e;
  }
}

.plan-item-01 {
  display: flex;
  align-items: center;
  border-top: 1px solid #eee;

  .c-checkbox {
    display: inline-block;
    margin-right: 15px;
  }
}

.c-retrieve1 {
  width: 100%;
  height: 0;
  overflow: hidden;
  transition: height 0.5s;

  &.isShow {
    height: auto;
  }
}

.mainTable {
  border-right: 1px solid #ddd;
  :deep(.tableColOverflowVisible .cell .row-drawer) {
    // left: -1030px;
    // width: 4570px;
    padding: 0 0 0 120px;
    margin-bottom: -5px;
    left: -62px;
    width: var(--activeTableWidth);
  }
  :deep(.el-table__body tr.current-row > td.el-table__cell) {
    padding-top: 7px;
  }
}

:deep(.el-select.no-wrap-select)  {
  .el-select-tags-wrapper{
    display: flex;
    overflow: hidden;
    .el-tag {
        flex: 1;
    }
  }
}
</style>
