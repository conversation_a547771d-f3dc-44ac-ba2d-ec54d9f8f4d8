import request, {
    baseURL,
    stringify
} from '$supersetUtils/request'
export default {
    // 院区维护操作接口 : Hospital District Controller
    getHospitalData,
    addHospital(data) {
        return request({
            url: baseURL.apricot + '/hospital/district/addHospital',
            method: 'POST',
            data
        })
    },
    editHospital(data) {
        return request({
            url: baseURL.apricot + '/hospital/district/editHospital',
            method: 'POST',
            data
        })
    },
    delHospital(params) {
        return request({
            url: baseURL.apricot + '/hospital/district/delHospital',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledHospital(params) {
        return request({
            url: baseURL.apricot + '/hospital/district/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortHospital(data) {
        return request({
            url: baseURL.apricot + '/hospital/district/sort',
            method: 'POST',
            data
        })
    },
    // 院区维护---end


    // 设备类型维护操作接口 : Device Type Controller
    getDeviceTypeData,
    addDeviceType(data) {
        return request({
            url: baseURL.apricot + '/device/type/addDeviceType',
            method: 'POST',
            data
        })
    },
    editDeviceType(data) {
        return request({
            url: baseURL.apricot + '/device/type/editDeviceType',
            method: 'POST',
            data
        })
    },
    delDeviceType(params) {
        return request({
            url: baseURL.apricot + '/device/type/delDeviceType',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledDeviceType(params) {
        return request({
            url: baseURL.apricot + '/device/type/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortDeviceType(data) {
        return request({
            url: baseURL.apricot + '/device/type/sort',
            method: 'POST',
            data
        })
    },
    // 设备类型维护---end


    // 机房操作接口 : Machine Room Controller
    getMachineRoomData,
    addMachineRoom(data) {
        return request({
            url: baseURL.apricot + '/machine/room/addMachineRoom',
            method: 'POST',
            data
        })
    },
    editMachineRoom(data) {
        return request({
            url: baseURL.apricot + '/machine/room/editMachineRoom',
            method: 'POST',
            data
        })
    },
    delMachineRoom(params) {
        return request({
            url: baseURL.apricot + '/machine/room/delMachineRoom',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledMachineRoom(params) {
        return request({
            url: baseURL.apricot + '/machine/room/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortMachineRoom(data) {
        return request({
            url: baseURL.apricot + '/machine/room/sort',
            method: 'POST',
            data
        })
    },
    // 机房操作接口---end


    // 核素操作接口 : Nuclide Controller
    getNuclideData,
    addNuclide(data) {
        return request({
            url: baseURL.apricot + '/nuclide/addNuclide',
            method: 'POST',
            data
        })
    },
    editNuclide(data) {
        return request({
            url: baseURL.apricot + '/nuclide/editNuclide',
            method: 'POST',
            data
        })
    },
    delNuclide(params) {
        return request({
            url: baseURL.apricot + '/nuclide/delNuclide',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledNuclide(params) {
        return request({
            url: baseURL.apricot + '/nuclide/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortNuclide(data) {
        return request({
            url: baseURL.apricot + '/nuclide/sort',
            method: 'POST',
            data
        })
    },
    // 核素操作接口---end


    // 示踪剂操作接口 : Tracer Controller
    getTracerData,
    addTracer(data) {
        return request({
            url: baseURL.apricot + '/tracer/addTracer',
            method: 'POST',
            data
        })
    },
    editTracer(data) {
        return request({
            url: baseURL.apricot + '/tracer/editTracer',
            method: 'POST',
            data
        })
    },
    delTracer(params) {
        return request({
            url: baseURL.apricot + '/tracer/delTracer',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledTracer(params) {
        return request({
            url: baseURL.apricot + '/tracer/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortTracer(data) {
        return request({
            url: baseURL.apricot + '/tracer/sort',
            method: 'POST',
            data
        })
    },
    // 示踪剂操作接口 : end


    // 项目检查部位操作接口 : Item Position Controller
    getItemPositionData,

    addItemPosition(data) {
        return request({
            url: baseURL.apricot + '/item/position/addItemPosition',
            method: 'POST',
            data
        })
    },
    editItemPosition(data) {
        return request({
            url: baseURL.apricot + '/item/position/editItemPosition',
            method: 'POST',
            data
        })
    },
    delItemPosition(params) {
        return request({
            url: baseURL.apricot + '/item/position/delItemPosition',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledItemPosition(params) {
        return request({
            url: baseURL.apricot + '/item/position/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortItemPosition(data) {
        return request({
            url: baseURL.apricot + '/item/position/sort',
            method: 'POST',
            data
        })
    },
    // 项目检查部位操作接口 : end

    // 检查项目维护操作接口 : Item Controller
    getItemData,
    // 获取项目树
    getItemTreeData,
    addItem: addItemAndAutoItem,
    editItem(data) {
        return request({
            url: baseURL.apricot + '/item/editItem',
            method: 'POST',
            data
        })
    },
    delItem(params) {
        return request({
            url: baseURL.apricot + '/item/delItem',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledItem(params) {
        return request({
            url: baseURL.apricot + '/item/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortItem(data) {
        return request({
            url: baseURL.apricot + '/item/sort',
            method: 'POST',
            data
        })
    },
    // 检查项目维护操作接口 : end


    // 检查项目维护操作接口 : Item Controller
    getTestModeData,
    addTestMode(data) {
        return request({
            url: baseURL.apricot + '/test/mode/addTestMode',
            method: 'POST',
            data
        })
    },
    editTestMode(data) {
        return request({
            url: baseURL.apricot + '/test/mode/editTestMode',
            method: 'POST',
            data
        })
    },
    delTestMode(params) {
        return request({
            url: baseURL.apricot + '/test/mode/delTestMode',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledTestMode(params) {
        return request({
            url: baseURL.apricot + '/test/mode/enableDisabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortTestMode(data) {
        return request({
            url: baseURL.apricot + '/test/mode/sort',
            method: 'POST',
            data
        })
    },
    // 检查项目维护操作接口 : end


    // 核医学号规则操作接口 : Access Num Rule Controller
    getAccessNumRuleData(data) {
        return request({
            url: baseURL.apricot + '/access/num/rule/find/page',
            method: 'POST',
            data
        })
    },
    addAccessNumRule(data) {
        return request({
            url: baseURL.apricot + '/access/num/rule/add',
            method: 'POST',
            data
        })
    },
    editAccessNumRule(data) {
        return request({
            url: baseURL.apricot + '/access/num/rule/edit',
            method: 'POST',
            data
        })
    },
    delAccessNumRule(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/del',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledAccessNumRule(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/enable/disabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortAccessNumRule(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/sort',
            method: 'POST',
            data: stringify(params)
        })
    },
    autoSortAccessNumRule(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/autoSort',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 核医学号规则操作接口 : end


    // 院区设备类型关联操作接口 : District Related Device Controller
    addDistrictRelDevice(data) {
        return request({
            url: baseURL.apricot + '/district/related/device/addBatch',
            method: 'POST',
            data
        })
    },
    delDistrictRelDevice(params) {
        return request({
            url: baseURL.apricot + '/district/related/device/delBatch',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 院区设备类型关联操作接口 : end


    // 设备机房配置操作接口 : Device Related Room Controller
    addDeviceRelRoom(data) {
        return request({
            url: baseURL.apricot + '/device/related/room/addBatch',
            method: 'POST',
            data
        })
    },
    // 设备机房配置操作接口 : end


    // 机房检查项目操作接口 : Room Related Item Controller
    addRoomRelItem(data) {
        return request({
            url: baseURL.apricot + '/room/related/item/addBatch',
            method: 'POST',
            data
        })
    },
    // 特殊合并接口（后端处理移除）
    delTreeItems(params) {
        return request({
            url: baseURL.apricot + '/room/related/item/delBatch',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortRoomRelItem(data) {
        return request({
            url: baseURL.apricot + '/room/related/item/sort/tree',
            method: 'POST',
            data
        })
    },
    // 机房检查项目操作接口 : end

    // 检查用药操作接口 : Item Set Controller
    getItemSetData,
    itemSetBatchAdd (data) {
        return request({
            url: baseURL.apricot + '/item/set/batchAdd',
            method: 'POST',
            data
        })
    },
    addItemSet(data) {
        return request({
            url: baseURL.apricot + '/item/set/addItemSet',
            method: 'POST',
            data
        })
    },
    editItemSet(data) {
        return request({
            url: baseURL.apricot + '/item/set/editItemSet',
            method: 'POST',
            data
        })
    },
    delItemSet(params) {
        return request({
            url: baseURL.apricot + '/item/set/delItemSet',
            method: 'POST',
            data: stringify(params)
        })
    },
    sortItemSet(data) {
        return request({
            url: baseURL.apricot + '/item/set/sort',
            method: 'POST',
            data
        })
    },
    // 项目检查配置操作接口 : end

    // 自动登记项目匹配操作接口 : Auto Item Set Controller
    getAutoItemSetData(data) {
        return request({
            url: baseURL.apricot + '/auto/item/set/find/page',
            method: 'POST',
            data
        })
    },
    addAutoItemSet,
    editAutoItemSet(data) {
        return request({
            url: baseURL.apricot + '/auto/item/set/edit',
            method: 'POST',
            data
        })
    },
    delAutoItemSet(params) {
        return request({
            url: baseURL.apricot + '/auto/item/set/del',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledAutoItemSet(params) {
        return request({
            url: baseURL.apricot + '/auto/item/set/enable/disabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 自动登记项目匹配操作接口 : end


    // 核医学号规则设置操作接口 : Access Num Rule Related Controller
    getAccessNumRuleRelByKey(data) {
        return request({
            url: baseURL.apricot + '/access/num/rule/related/find/key',
            method: 'POST',
            data
        })
    },
    addAccessNumRuleRel(data) {
        return request({
            url: baseURL.apricot + '/access/num/rule/related/add',
            method: 'POST',
            data
        })
    },
    editAccessNumRuleRel(data) {
        return request({
            url: baseURL.apricot + '/access/num/rule/related/edit',
            method: 'POST',
            data
        })
    },
    delAccessNumRuleRel(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/related/del',
            method: 'POST',
            data: stringify(params)
        })
    },
    disabledAccessNumRuleRel(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/related/enable/disabled',
            method: 'POST',
            data: stringify(params)
        })
    },
    queryAccessNumRule(params) {
        return request({
            url: baseURL.apricot + '/access/num/rule/query',
            method: 'POST',
            data: stringify(params)
        })
    },
    // 核医学号规则设置操作接口 
    getDistrictMachineRoom,
    addItemAndAutoItem
}

// 获取院区数据
export function getHospitalAndRoomData(data) {
    return request({
        url: baseURL.apricot + '/register/query/queryAllDistrictAndRoom',
        method: 'POST',
        data
    })
}

// 获取院区数据
export function getHospitalData(data) {
    return request({
        url: baseURL.apricot + '/hospital/district/findAllHospital',
        method: 'POST',
        data
    })
}

// 获取院区机房数据
export function getDistrictMachineRoom(params) {
    return request({
        url: baseURL.apricot + '/machine/room/find/tree',
        method: 'POST',
        data: stringify(params)
    })
}
// 根据机房id获取关联的问诊室和注射室
export function getMachineRoomById(params) {
    return request({
        url: baseURL.apricot + '/machine/room/findMachineRoomById',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取项目数据
export function getItemData(data) {
    return request({
        url: baseURL.apricot + '/item/findItemList',
        method: 'POST',
        data
    })
}

// 获取项目数据
export function getItemTreeData(data) {
    return request({
        url: baseURL.apricot + '/item/find/tree',
        method: 'POST',
        data
    })
}

// 获取部位
export function getItemPositionData(data) {
    return request({
        url: baseURL.apricot + '/item/position/findItemPositionList',
        method: 'POST',
        data
    })
}

// 获取机房数据
export function getMachineRoomData(data) {
    return request({
        url: baseURL.apricot + '/machine/room/findMachineRoomList',
        method: 'POST',
        data
    })
}

// 核素
export function getNuclideData(data) {
    return request({
        url: baseURL.apricot + '/nuclide/findNuclideList',
        method: 'POST',
        data
    })
}

// 示踪剂（药物）
export function getTracerData(data) {
    return request({
        url: baseURL.apricot + '/tracer/findTracerList',
        method: 'POST',
        data
    })
}
// 检查方式
export function getTestModeData(data) {
    return request({
        url: baseURL.apricot + '/test/mode/findTestModeList',
        method: 'POST',
        data
    })
}
// 检查用药
export function getItemSetData (data) {
    return request({
        url: baseURL.apricot + '/item/set/findItemSetList',
        method: 'POST',
        data
    })
}
// 设备类型
export function getDeviceTypeData(data) {
    return request({
        url: baseURL.apricot + '/device/type/findAllDeviceType',
        method: 'POST',
        data
    })
}

// 新建号源模板 
export function addRegisterTemplate(data) {
    return request({
        url: baseURL.apricot + '/register/template/addTemplate',
        method: 'POST',
        data
    })
}

// 修改号源模板 
export function editRegisterTemplate(data) {
    return request({
        url: baseURL.apricot + '/register/template/editTemplate',
        method: 'POST',
        data
    })
}

// 删除号源模板
export function delRegisterTemplate(data) {
    return request({
        url: baseURL.apricot + '/register/template/deleteTemplate',
        method: 'POST',
        data: stringify(data)
    })
}

// 获取所有号源模板
export function queryAllRegisterTemplate(data) {
    return request({
        url: baseURL.apricot + '/register/template/queryAllTemplate',
        method: 'POST',
        data
    })
}
// 新增项目匹配
export function  addAutoItemSet(data) {
    return request({
        url: baseURL.apricot + '/auto/item/set/add',
        method: 'POST',
        data
    })
}
// 新增检查项目并建立申请项目匹配
export function  addItemAndAutoItem(data) {
    return request({
        url: baseURL.apricot + '/item/addItem',
        method: 'POST',
        data
    })
}