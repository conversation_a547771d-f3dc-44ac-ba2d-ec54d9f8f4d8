<template>
    <el-dialog v-model="visible"
        title="UKEY登录"
        draggable
        align-center
        :close-on-click-modal="false"
        class="t-default my-dialog"
        width="500px"
        @open="dialogOpen"
        @closed="dialogClosed">
        <!-- UKey密码登录 -->
        <form class="block passwordLogin">
            <div class="c-input">
                <!-- 用户名 -->
                <el-select v-model="uKeyForm.userCertId"
                    class="c-input-item"
                    ref="uNameInput"
                    placeholder=" "
                    clearable
                    size="large">
                    <template #prefix>
                        <div class="inline-block px-0">
                            <Icon name="el-icon-user"
                                :strokeWidth="4"
                                size="20"
                                color="#999" />
                        </div>
                    </template>
                    <el-option v-for="(item, index) in uKeyUsersList"
                        :key="index"
                        :label="item.label"
                        :value="item.value" />
                </el-select>
                <!-- 密码 -->
                <div class="c-input-item">
                    <el-input v-model="uKeyForm.userPwd"
                        name="u_password"
                        show-password
                        size="large"
                        @keyup.native.enter="onClickUKeyLogin"
                        type="password">
                        <template #prefix>
                            <div class="inline-block px-0">
                                <Icon name="el-icon-lock"
                                    :strokeWidth="4"
                                    size="20"
                                    color="#999" />
                            </div>
                        </template>
                    </el-input>
                </div>
            </div>

        </form>
        <template #footer>
            <div class="c-btn">
                <!-- <el-button size="large"
                    @click="onClickClosed">取消</el-button> -->
                <el-button size="large"
                    type="primary"
                    @click="onClickUKeyLogin"
                    @keyup.enter="onClickUKeyLogin">登录</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script>
import mixinUKeyLogin from '$supersetViews/login/mixinUKeyLogin.js'
import { guangXiQuRenMinUKeyLogin } from '$supersetApi/user'
import { setLoginCertId } from '$supersetUtils/auth'
export default {
    name: 'UKeyDialog',
    mixins: [mixinUKeyLogin],
    emits: ['update:modelValue', 'handleUKeySign'],
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
        mxBusinessType: {
            type: [String, Number],
            default: ''
        }
    },
    data () {
        return {
            isLoginSuccess: false,  // 是否登录成功
        }
    },
    computed: {
        visible: {
            get: function () {
                return this.modelValue
            },
            set: function (val) {
                this.$emit('update:modelValue', val)
            }
        },
    },
    methods: {
        dialogOpen () {
            // console.log('dialogOpen')
            this.uKeyForm.userPwd = '';
            this.mxInitUKeyMode();
        },
        dialogClosed () {
            if (this.isLoginSuccess) {
                return
            }
            this.$confirm('签名未成功，关闭弹框会终止签名和后续操作, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.visible = false;
                this.isLoginSuccess = false
            }).catch(() => {
            });
        },
        // 混入mixinUKeyLogin，重写sendUKeyLogin方法
        // 请求登录接口
        sendUKeyLogin (userCertInfo) {
            let loading = this.loginLoading()
            guangXiQuRenMinUKeyLogin(userCertInfo).then(res => {
                if (res.success) {
                    this.isLoginSuccess = true;
                    let userCertId = userCertInfo.userCertId;
                    setLoginCertId(userCertId);
                    this.$store.commit({
                        type: 'user/setAccessToken',
                        accessToken: res.data.sessionId,
                        expires: new Date(Number(res.data.expireTime))
                    })
                    this.visible = false;
                    // 重新进行UKEY 签章环节
                    this.$emit('handleUKeySign', userCertId, this.mxBusinessType);
                    return
                }
                this.$message.error(res.msg)
            }).catch((err) => {
                console.log(err)
            }).finally(() => {
                loading.close()
            })
        },
    }
}
</script>
<style lang="scss" scoped>
$mainColor: var(--theme-header-bg);
.passwordLogin {
    padding: 30px;

    .c-input-item:first-child {
        margin-bottom: 15px;
    }

    .c-btn {
        padding: 20px 0 0 0;

        :deep(.el-button) {
            width: 100%;
            height: 38px;
            font-size: 18px;
        }

        button {
            background-color: $mainColor;
            border-color: $mainColor;
        }
    }
}
</style>