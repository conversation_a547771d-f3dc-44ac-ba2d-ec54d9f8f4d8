<template>
    <el-button 
        :disabled="buttonMsg.isReadOnly"
        :class="{
            'm-vertical-btn t2': buttonMsg.icon,
            'margin_l': !buttonMsg.icon,
            'm-vertical-text': buttonMsg.isFold,
            't-border': buttonMsg.isBorder
        }"
        @click="dropDownClick(buttonMsg)">
        <svg class="fa"
            aria-hidden="true">
            <use :xlink:href="'#' + buttonMsg.icon"></use>
        </svg>
        <label>{{ buttonMsg.name }}</label>

        <el-dialog v-model="visible"
            :title="buttonMsg.name"
            :close-on-click-modal="false"
            append-to-body
            @close="closeDialog"
            width="800px"
            class="my-dialog t-default">
            <div class="c-dialog-body">
                <div class="radio-box"
                    v-loading="loading">
                    <el-radio-group v-model="radio"
                        v-if="options.length">
                        <el-col :span="8" v-for="(item, index) in options"
                            :key="index">
                            <el-radio :label="item.userId">{{item.userName}}</el-radio>
                        </el-col>
                    </el-radio-group>
                    <div v-else
                        style="text-align:center;">暂无数据</div>
                </div>
            </div>
            <template #footer>
                <el-button-icon-fa type="primary"
                    icon="fa fa-save"
                    :loading="saving"
                    @click="onClick(buttonMsg)">确 定</el-button-icon-fa>
                <el-button-icon-fa
                    icon="fa fa-close-1"
                    @click="visible = false">关 闭</el-button-icon-fa>
            </template>
        </el-dialog>
    </el-button>
</template>

<script>
import { reportChangeWriter, reportChangeAuditor
    ,reportChangePractice } from '$supersetApi/projects/apricot/case/report.js'
import { getReportAboveDrData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js'
export default {
    name: 'ChangeDoctor',
    props: {
        buttonMsg: {
            type: Object,
            default: () => ({})
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        },
        reportInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data () {
        return {
            loading: false,
            options: [],
            visible: false,
            radio: null,
            saving: false
        }
    },
    watch: {
        visible (val) {
            if (val) {
                this.radio = null;
            }
        }
    },
    methods: {
        async dropDownClick (msg) {
            // BUS.$emit('closePop');
            this.visible = true;
            if (this.options.length || this.loading) {
                return
            }
            this.loading = true;
            const doctors = await getReportAboveDrData()
            this.loading = false;
            if(msg.mark === 'isChangeWrite') {
                this.options = doctors.reportDoctors
            }
            if(msg.mark === 'isChangeAuditor') {
                this.options = doctors.auditDoctors 
            }
            if(msg.mark === 'isChangePractice') {
                this.options = doctors.practiceDoctors 
            }
        },
        closeDialog () {
            this.visible = false;
        },
        onClick (msg) {
            if (!this.radio) {
                this.$message.warning('请选择要修改的医生！')
                return
            }
            let fns = {
                isChangeWrite: reportChangeWriter,
                isChangeAuditor: reportChangeAuditor,
                isChangePractice: reportChangePractice
            }
            this.saving = true;
            fns[msg.mark]({
                destDoctorId: this.radio,
                reportId: this.reportInfo.reportId,
            }).then(res => {
                this.saving = false;
                if (res.success) {
                    this.visible = false;
                    this.$message.success(res.msg);
                    this.$emit('refresh')
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                this.saving = false;
            })
        }
    },
    mounted() {
        // BUS.$on('closePop', () => {
        //     this.visible = false;
        // });
    }
}
</script>

<style lang="scss" scoped>
.margin_l {
    margin-left: 10px;
}
.radio-box {
    padding: 10px 20px; 
    min-height: 200px;
    max-height:500px;
    overflow:auto;
}
.el-radio-group {
    width: 100%;
}
.el-col{
    margin-bottom: 5px;
}
</style>
