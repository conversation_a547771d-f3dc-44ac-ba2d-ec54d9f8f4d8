<template>
  <Transition name="slide-fade">
    <div v-show="currentProcess" class="transition-slide-body">
      <slot></slot>
    </div>
  </Transition>
</template>
<script>
import NProgress from 'nprogress';

export default {
  name: 'TransitionSlide',
  props: {
    currentProcess: {
      type: Boolean,
      default: false,
    }
  },
  watch: {
    currentProcess(later) {
      if (later) {
        NProgress.start()
        NProgress.done()
      }
    }
  },
  mounted() {
    NProgress.configure({ ease: 'linear', speed: 600, showSpinner: false });
  },
}
</script>

<style lang="scss" scoped>
$borderColor: #cdcecf;

.transition-slide-body {
  position: absolute;
  top: 0;
  width: 100%;
  bottom: 0;
  left: 0;
  // border: solid 1px $borderColor;
  background: var(--theme-menu-background-color);
  z-index: 2000; // v-loading
  border-right: none;
  background-image: url(/img/loading.gif);
  background-repeat: no-repeat;
  background-size: 62px;
  background-position: center;
}


.slide-fade-enter-active {
  transition: all .3s ease;
  transition-property: opacity, transform;
}
.slide-fade-leave-active {
  transition: none;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-100%);
}</style>
