<template>
    <!-- 活度仪采集 -->
    <div class="m-flexLaout-ty c-page-box" v-loading="loading">
        <div class="c-item ">
            <div class="c-row">
                <div class="label">活度仪：</div>
                <div class="flex-x">
                    <div class="c-text">
                        <strong>{{activityMeterData.isotopeName}}</strong>
                    </div>
                    <div class="c-text">
                        <strong>{{activityMeterData.value}}{{activityMeterData.unit}}</strong>
                    </div>
                    <div class="c-text">
                        <strong>{{timeStr}}</strong>
                    </div>  
                </div>
                <!-- v-if="isOffline" -->
                <div class="action"  style="text-align: center;margin-left:5px;">
                    <div v-if="isOffline" style="font-size: 22px; color: #F56C6C;">
                        <el-tooltip content="无法连接服务器"
                            placement="left">
                            <i class="el-icon-info"></i>
                        </el-tooltip>
                    </div>
                    
                </div>
            </div>
        </div>

        <div class="c-item-2">
            <div class="c-row">
                <div class="label">满针剂量：</div>
                <div class="c-content">
                    <el-input size="large"
                        v-model="form.fFullNeedle"
                        placeholder="满针剂量"
                        ref="r0c2"
                        @input="computedInjectDose">
                        <template #append>
                            <el-select 
                                v-model="form.sFullUnit"
                                class="i-suffix-value"
                                clearable
                                placeholder="单位">
                                <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </template>
                    </el-input>
                </div>
                <div class="label">满针时间：</div>
                <div class="c-content">
                    <el-time-picker v-model="form.dInjectionSta"
                        size="large"
                        placeholder="满针时间"
                        ref="r0c3"
                        :clearable="false"
                        popper-class="popper-show"
                        :default-value="defaultValue.iFull"
                        @focus="onFocusFn('iFull')"
                        @change="computedInjectDose"></el-time-picker>
                </div>
                <div class="action">
                    <el-button type="primary" size="large"
                        plain
                        @click="onFullNeedleClick">获取</el-button>
                </div>
            </div>
            <div class="c-row">
                <div class="label">空针剂量：</div>
                <div class="c-content">
                    <el-input v-model="form.fEmptyNeedle"
                        size="large"
                        controls-position="right"
                        placeholder="空针剂量"
                        ref="r1c2"
                        @input="computedInjectDose">
                        <template #append>
                            <el-select 
                                v-model="form.sFullUnit"
                                class="i-suffix-value"
                                clearable
                                placeholder="单位">
                                <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </template>
                    </el-input>
                </div>
                <div class="label">空针时间：</div>
                <div class="c-content">
                    <el-time-picker v-model="form.dInjectionEnd"
                        size="large"
                        placeholder="空针时间"
                        ref="r1c3"
                        popper-class="popper-show"
                        :default-value="defaultValue.iNull"
                        @focus="onFocusFn('iNull')"
                        @change="computedInjectDose"></el-time-picker>
                </div>
                <div class="action">
                    <el-button type="primary" size="large"
                        plain
                        @click="onNullNeedleClick">获取</el-button>
                </div>
            </div>
            <div class="c-row">
                <div class="label">实际剂量：</div>
                <div class="c-content">
                    <el-input v-model="form.fFactDose"
                        size="large"
                        placeholder="实际剂量"
                        ref="r2c2">
                        <template #append>
                            <el-select 
                                v-model="form.sFullUnit"
                                class="i-suffix-value"
                                clearable
                                placeholder="单位">
                                <el-option v-for="item in optionsLoc.ApricotReportDoseUnit"
                                    :key="item.sId"
                                    :label="item.sName"
                                    :value="item.sValue">
                                </el-option>
                            </el-select>
                        </template>
                    </el-input>
                </div>
                <div class="label">实际时间：</div>
                <div class="c-content">
                    <el-time-picker v-model="form.dInjectionTime"
                        size="large"
                        placeholder="实际时间"
                        ref="r2c3"
                        popper-class="popper-show"
                        :default-value="defaultValue.iActual"
                        @focus="onFocusFn('iActual')"
                        @input="computedInjectDose"></el-time-picker>
                </div>
                <div class="action">   
                </div>
            </div>
            <div class="c-row">
                <div class="label">核素：</div>
                <div class="c-content">
                    <el-select v-if="configValue?.medicineChooseType == 2"
                        v-model="form['sNuclide']"
                        filterable
                        clearable
                        placeholder=" "
                        @change="onMedicineChange">
                        <el-option v-for="item in optionsLoc.nuclideOptions"
                            :key="item.sId"
                            :label="item.sNuclideName"
                            :value="item.sId" />
                    </el-select>
                    <el-input v-else v-model="form.sNuclideText"
                        size="large"
                        readonly
                        @click="(e) => showLogin(e, 'sNuclideText')"></el-input>
                </div>
                <div class="label">示踪剂：</div>
                <div class="c-content">
                    <el-select v-if="configValue?.medicineChooseType == 2"
                        v-model="form['sTracer']"
                        filterable
                        clearable
                        placeholder=" "
                        @change="onMedicineChange">
                        <el-option v-for="item in optionsLoc.tracerOptions"
                            :key="item.sId"
                            :label="item.sTracerName"
                            :value="item.sId" />
                    </el-select>
                    <el-input v-else v-model="form.sTracerText"
                        size="large"
                        readonly
                        @click="(e) => showLogin(e, 'sTracerText')"></el-input>
                </div>
                <div class="action">   
                </div>
            </div>
            <div class="c-row inject">
                <div class="label">注射部位：</div>
                <div class="c-content">
                    <el-select v-model="form.sInjectionPosition" size="large" filterable
                        allow-create
                        placeholder="注射部位">
                        <el-option v-for="(item, index) in optionsLoc.ApricotReportInjectSite"
                            :key="index"
                            :value="item.sValue"
                            :label="item.sName"
                        ></el-option>
                    </el-select>
                </div>
                <div class="label">给药方式：</div>
                <div class="c-content">
                    <el-select v-model="form.sDrugDeliveryCode"
                        size="large"
                        filterable
                        clearable
                        placeholder="给药方式"
                        ref="r3c1"
                        @keyup.up.native.stop.prevent
                        @keyup.down.native.stop.prevent>
                        <el-option v-for="(item, index) in optionsLoc.ApricotReportDrugDelivery"
                            :key="index"
                            :label="item.sName"
                            :value="item.sValue">
                        </el-option>
                    </el-select>
                </div>
                <div class="action">   
                </div>
            </div>
            <div class="c-row inject">
                <div class="label">药物来源：</div>
                <div class="c-content"> 
                    <el-select v-model="form.sMedicineSource"
                        size="large"
                        filterable
                        clearable
                        placeholder="药物来源"
                        ref="r4c1"
                        @keyup.up.native.stop.prevent
                        @keyup.down.native.stop.prevent>
                        <el-option v-for="(item, index)  in optionsLoc.ApricotReportMedicineSource"
                            :key="index"
                            :label="item.sName"
                            :value="item.sValue">
                        </el-option>
                    </el-select>
                </div>
                <div class="label">空腹血糖：</div>
                <div class="c-content">
                    <el-input
                        v-model="form.fBloodSugar"
                        size="large"
                        type="number"
                        ref="r7c1">
                        <template #suffix>
                            <span style="padding: 0 10px">mmol/L</span>
                        </template>
                    </el-input>
                </div>
                <div class="action">   
                </div>
            </div>
            <div class="c-row inject">
                <div class="label">身高：</div>
                <div class="c-content">
                    <el-input v-model="form.fHeight"
                        type="number"
                        size="large"
                        :min="0"
                        @input="inputHeight">
                        <template #suffix>
                            <span>cm</span>
                        </template>
                    </el-input>
                </div>
                <div class="label">体重：</div>
                <div class="c-content">
                    <el-input v-model="form.fWeight"
                        type="number"
                        size="large"
                        :min="0"
                        @input="inputWeight">
                        <template #suffix>
                            <span>kg</span>
                        </template>
                    </el-input>
                </div>
                <div class="action">   
                </div>
            </div>
            <div class="c-row inject">
                <div class="label">注射日期：</div>
                <div class="c-content">
                    <el-date-picker v-model="form.dInjectDate"
                        size="large"
                        type="date"
                        placeholder="注射日期"
                        ref="r6c1">
                    </el-date-picker>
                </div>
                <div class="label">注射医生：</div>
                <div class="c-content">
                    <el-select v-model="form.sNurseId"
                        size="large"
                        clearable
                        placeholder="注射医生"
                        ref="r5c1"
                        @keyup.up.native.stop.prevent
                        @keyup.down.native.stop.prevent>
                        <el-option v-for="(item, index) in optionsLoc.DoctorOptions"
                            v-show="!item.iIsAuxiliary"
                            :key="index"
                            :label="item.sName"
                            :value="item.sValue">
                        </el-option>
                    </el-select>
                </div>
                <div class="action">   
                </div>
            </div>
        </div>
    </div>

    <el-popover
        v-if="popVisible"
        :width="420"
        trigger="click"
        v-model:visible="popVisible"
        virtual-triggering
        :virtual-ref="tempRef"
        popper-class="pop-class"
        @show="onPopoverShow">
            <div style="margin-bottom: 10px;">
                <el-input v-model="medicationCondition.nuclearName"
                    placeholder="核素" 
                    clearable
                    style="width: 148px;margin-right: 4px"></el-input>
                <el-input v-model="medicationCondition.tracerName" 
                    placeholder="示踪剂" 
                    clearable
                    style="width: 148px"></el-input>
                <el-input ref="sTextInput"
                    style="width: 0;opacity: 0;"></el-input>
                <!-- <i class="el-icon-s-tools float-right"
                    @click="handlerSetCheckMedicine"
                    style="float: right;font-size: 22px;position: relative;top: 6px;cursor: pointer;"></i> -->
            </div>
            <el-table :data="medicationTableData.filter(item => item.sNuclideName.toLowerCase().includes(medicationCondition.nuclearName.toLowerCase())).filter(item =>item.sTracerName.toLowerCase().includes(medicationCondition.tracerName.toLowerCase()))"
                ref="medicineRef"
                highlight-current-row
                row-key="sId"
                border 
                max-height="300"
                @row-click="(o) => nuclearTableRowClick(o)">
                <el-table-column width="150"
                    property="sNuclideName"
                    label="核素"
                    show-overflow-tooltip></el-table-column>
                <el-table-column min-width="150"
                    property="sTracerName"
                    label="示踪剂"
                    show-overflow-tooltip></el-table-column>
                <el-table-column width="80"
                    property="sTestModeName"
                    label="衰变周期"
                    show-overflow-tooltip>
                    <template v-slot="{ row }">
                        {{row.fDecayPeriod }}
                        {{row.fDecayPeriod && row.sDecayUnitName ? row.sDecayUnitName : '' }}
                    </template>
                </el-table-column>
            </el-table>
            <div style="margin-top: 10px;text-align: right;">
                <el-button-icon-fa
                    icon="fa fa-close-1"
                    @click="popoverClickOutside">关闭</el-button-icon-fa>
            </div>
    </el-popover>

    <el-dialog append-to-body
        title="选择模板"
        v-model="visible_Template"
        @close="closeTemplateDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="my-dialog"
        width="500px">
        <div style="margin: 10px 10px 15px;">
            <el-radio-group v-model="selectedTemplateId"
                style="display: flex; flex-direction: column;">
                <el-radio v-for="(item, index) in injectTemplateList"
                    :key="index"
                    :label="item.sTemplateId"
                    style="margin-top: 18px;">
                    <span style="font-size: 16px;">{{item.sTemplateName}}</span>
                </el-radio>
            </el-radio-group>
        </div>
        <div class="footer-item"
            style="text-align: right; margin: 5px;">
            <el-button size="small"
                type="primary"
                plain
                icon="el-icon-check"
                @click="onPrintPocket">确 认</el-button>
            <el-button size="small"
                type="primary"
                plain
                icon="el-icon-close"
                @click="closeTemplateDialog">取 消</el-button>
        </div>
    </el-dialog>
    <PrintTemDialog v-model:dialogVisible="visibleTemplate"></PrintTemDialog>
</template>
<script>

import { deepClone } from '$supersetUtils/function'
import { mixinPrintPreview, } from '$supersetResource/js/projects/apricot/index.js'
// 接口
import { activityMeterRead, saveInject, editInject } from '$supersetApi/projects/apricot/injectMng/inject.js'


const defaultForm = {
    sDrugDeliveryCode: '',      // 给药方式
    sInjectionPosition: '',     // 注射部位
    dInjectionSta: '',  // 满针时间
    dInjectionTime: '',     // 注射时间
    dInjectionEnd: '',  // 空针时间
    fFullNeedle: '',    // 满针剂量
    fFactDose: '',  // 实际注射剂量
    fEmptyNeedle: '',   // 空针剂量
    sMedicineSource: '',    // 药物来源
    sNurseName: '',     // 注射人
    dInjectDate: new Date(),    // 注射日期
    sFullUnit: '',  // 注射单位
    fHeight: undefined,
    fWeight: undefined,
};

export default {
    name: 'ActivityMeter',
    mixins: [mixinPrintPreview],
    props: {
        isActivePane: {
            type: Boolean,
            default: () => false
        },
        doctorOptions: {
            type: Array,
            default: []
        }
    },
    inject:{
        providePatient :{
            from: 'patientInfo',
            default: {}
        },
        // 注射初始化信息
        injectPatientInfo: {
            from: 'injectPatientInfo',
            default: {}
        },
        injectModuleSetData: {
            from: 'configValue',
            default: {}
        },
        updateRowData: {
            from:'updateInjectRowData'
        },
        getInitInJectData: {
            from:'getInitInJectData'
        },
        medicationTableData: {
            from:'medicationTableData',
        },
        getItemSetData: {
            from:'getItemSetData',
        },
        configValue: {
            from: 'configValue',
            default: {}
        },
        nuclideOptions: {
            from: 'nuclideOptions',
            default: []
        },
        tracerOptions: {
            from: 'tracerOptions',
            default: []
        }
    },
    emits:['upDateLoading', 'onChangeActiveTab'],
    components: {
        PrintTemDialog: defineAsyncComponent(() => import('$supersetViews/apricot/components/PrintTemDialog.vue'))
    },
    data () {
        return {
            loading: false,
            iModuleId: 4, // 注射管理标识 ，eName: 'INJECTION'， 在mixinPrintPreview混合模块中调用
            defaultValue: {
                iFull: undefined,
                iActual: undefined,
                iNull: undefined
            },
            form: deepClone(defaultForm),
            row: 0,
            col: 0,
            colCount: [{
                max: 3
            }, {
                max: 3
            }, {
                max: 3
            }, {
                max: 2
            }, {
                max: 1
            }, {
                max: 2
            }],
            lastKeyCode: 39, // 右
            automaticDropdown: false,
            bodyPartCode: '',
            loading3: false,
            dateTimes: null,
            timeStr: '',
            activityMeterData: [],
            activityMeterTimes: null,
            isConfirm: false,
            isOffline: false,
            visible_Template: false,
            selectedTemplateId: '',
            injectTemplateList: [],
            optionsLoc:{
                DoctorOptions: this.doctorOptions,
                ApricotReportDoseUnit: this.$store.getters['dict/map'].ApricotReportDoseUnit || [],
                ApricotReportDrugDelivery: this.$store.getters['dict/map'].ApricotReportDrugDelivery || [],
                ApricotReportMedicineSource: this.$store.getters['dict/map'].ApricotReportMedicineSource || [],
                ApricotReportInjectSite: this.$store.getters['dict/map'].ApricotReportInjectSite || [],
                nuclideOptions: [],
                tracerOptions: []
            },
            injectData: [],
            nuclearParams: {},  // 用药参数
            medicationCondition: {
                nuclearName: '',
                tracerName: ''
            },
            tempRef: null,
            popVisible: false,
            keyWordProp: '',
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
        currentRouter () {
            return this.$store.getters['module_router/activeRouterName']
        },
        patientInfo() {
            return this.providePatient
        }
    },
    watch: {  
        isActivePane: {
            handler (val) {          
                if (val) {
                    // 页面激活时
                    
                    this.dateTimeRead();
                    this.activityMeterTimesRead();
                    // this.mxGetPreviewTemplate();
                    return
                }
                clearInterval(this.dateTimes);
                clearInterval(this.activityMeterTimes);
                // window.onkeyup = () => { }
            },
            immediate: true,
        },
        injectPatientInfo: {
            async handler(val, oldVal) {
                if(!val.sId) {
                    this.initForm()
                    return
                }
                if(val?.sPatientId === oldVal?.sPatientId) true;
                this.initForm()
                if(this.configValue?.medicineChooseType == 2) {
                    this.optionsLoc.nuclideOptions = this.nuclideOptions;
                    this.optionsLoc.tracerOptions = this.tracerOptions;
                    this.setMedicineData();
                } else {
                    this.findNuclearRow(this.medicationTableData)
                }
            },
            immediate: true,
        },
        currentRouter (val) {
            if (val && val === 'apricot_injectMng' && this.isActivePane == 1) {
                // 页面激活时
                this.dateTimeRead();
                this.activityMeterTimesRead();
                // this.mxGetPreviewTemplate();
                return
            }
            clearInterval(this.dateTimes);
            clearInterval(this.activityMeterTimes);
        }
    },
    methods: {
        setMedicineData() {
            if(!this.form.sId && Object.keys(this.injectPatientInfo).length) {
                const tempNuclideItem = this.optionsLoc.nuclideOptions.find(item => item.sNuclideName === this.injectPatientInfo.sNuclideText);
                if( tempNuclideItem?.sId) {
                    this.form.sNuclide = tempNuclideItem.sId;
                    this.form.sNuclideSupName = tempNuclideItem.sNuclideSupName;
                }
                const tempTracerItem = this.optionsLoc.tracerOptions.find(item => item.sTracerName === this.injectPatientInfo.sTracerText);
                if(tempTracerItem?.sId) {
                   this.form.sTracer = tempTracerItem.sId 
                   this.form.sTracerSupName = tempTracerItem.sTracerSupName 
                }
            }
        },
        onMedicineChange() {
            if(this.configValue.medicineChooseType == 1) return;
            const { sNuclide, sTracer} = this.form;
            this.setParamsOfNuclideToForm(sNuclide);
            this.setParamsOfTracerToForm(sTracer);
        },
        // 选中核素相关参数赋值给表单对象
        setParamsOfNuclideToForm(id) {
            const tempItem = this.optionsLoc.nuclideOptions.find(item => item.sId === id);
            if(!tempItem) return
            this.form.sNuclideText = tempItem.sNuclideName;
            this.form.sNuclideSupName = tempItem.sNuclideSupName;
        },
        // 选中示踪剂相关参数赋值给表单对象
        setParamsOfTracerToForm(id) {
            const tempItem = this.optionsLoc.tracerOptions.find(item => item.sId === id);
            if(!tempItem) return
            this.form.sTracerText = tempItem.sTracerName;
            this.form.sTracerSupName = tempItem.sTracerSupName;
        },
        inputHeight(value) {
            this.form.fHeight = this.saveTwoDecimal(value);
        },
        inputWeight(value) {
            this.form.fWeight = this.saveTwoDecimal(value);
            let nuclearParams = this.nuclearParams;
            if(this.configValue?.medicineChooseType == 2) {
                this.form['fRecipeDose'] = value > 0 ? (value * 0.1).toFixed(2) : undefined;
                return  
            }
            if (!Object.keys(nuclearParams).length || nuclearParams.iIsInvariable) {
                return
            }
            let bool = (this.form.fWeight >= 0 && nuclearParams.fCoefficient);
            this.form['fRecipeDose'] = bool  ? ( this.form.fWeight * nuclearParams.fCoefficient).toFixed(2) : undefined;
        },
        saveTwoDecimal(value) {
            if (value.indexOf(".") < 0 && value !== "") {
                // 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
                value = parseFloat(value) + "";
            } else if (value.indexOf(".") >= 0) {
                value = value.replace(/^()*(\d+)\.(\d\d).*$/, "$1$2.$3"); // 只能输入两个小数
            }
            return value
        },
        showLogin (e, prop) {
            const evt = e || window.e || window.event;
            if(this.keyWordProp === prop && this.popVisible) {
                this.popVisible = false;
                return
            }
            if (this.tempRef) this.popVisible = false;
            this.medicationCondition.nuclearName = '';
            this.medicationCondition.tracerName = '';
            nextTick(() => {
                this.tempRef = evt.currentTarget
                this.popVisible = true;
                this.keyWordProp = prop;
            })
        },
        async onPopoverShow() {
            await this.getItemSetData();
            const tableRef = this.$refs['medicineRef'];
            tableRef && tableRef.setCurrentRow(this.nuclearParams);
            this.$refs['sTextInput'] && this.$refs['sTextInput'].focus();
        },
        popoverClickOutside() {
            this.popVisible = false;
        },
        // 核素相关表格行点击事件
        nuclearTableRowClick (row) {
            this.nuclearParams = row;
            this.popoverClickOutside()
            // 核素、示踪剂、检查部位、检查方式相关字段的赋值
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sMedicineSource', 'sMedicineSourceText'];
            let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName', 'sMedicineSource', 'sMedicineSourceText'];
            formKeys.map((item, index) => {
                this.form[item] = row[oKeys[index]];
            })
            this.doComputeDosageBySetting();
            this.computedInjectDose();
        },
        findNuclearRow(arr) {
            this.nuclearParams = {};
            if(!arr.length) {
                return
            }
            let formKeys = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName'];
            let oKeys = ['sNuclideName', 'sNuclideId', 'sNuclideSupName', 'sTracerName', 'sTracerId', 'sTracerSupName'];
            let finalItem = arr.find(item => {
                let isEq = true;
                oKeys.map((key, index) => {
                    if(this.form[formKeys[index]] && item[key] !== this.form[formKeys[index]]){
                        isEq = false;
                    }
                })
                let targetItem  = null;
                if(isEq) {
                    targetItem = item;
                }
                return targetItem;
            })
            if(finalItem) {
                this.nuclearParams = finalItem;
                this.form.sMedicineSource = finalItem.sMedicineSource;
                this.form.sMedicineSourceText = finalItem.sMedicineSourceText;
            }
        },
        // 根据核素设置计算处方剂量
        doComputeDosageBySetting() {
            let val = this.nuclearParams
            if (!Object.keys(val).length) return;
            if (val.iIsInvariable) {
                this.form['fRecipeDose'] = Number(val.fDosage);
                return
            }
            let fWeight = this.injectPatientInfo.fWeight;
            this.form['fRecipeDose'] = (fWeight && val.fCoefficient) ? Number((fWeight * val.fCoefficient).toFixed(2)) : undefined;
        },
        // 清除核素相关属性数据
        resetFormNuclearData () {
            let temp = ['sNuclideText', 'sNuclide', 'sNuclideSupName', 'sTracerText', 'sTracer', 'sTracerSupName', 'sPositionText', 'sPosition', 'sTestModeText', 'sTestMode', 'fRecipeDose'];
            for (let item in temp) {
                if (this.form[temp[item]]) {
                    this.form[temp[item]] = null;
                }
            }
        },
        // 初始化表单
        initForm () {
            this.form = deepClone(defaultForm);
            this.form.sPatientId = this.patientInfo.sId;
            this.form.sNurseNo = this.userInfo.sNo;
            this.form.sNurseName = this.userInfo.sName;
            this.form['sNurseId'] = this.userInfo.sId;
            this.form['dInjectDate'] = new Date();
            this.form['fHeight'] = this.patientInfo.fHeight;
            this.form['fWeight'] = this.patientInfo.fWeight;

            const ApricotReportDoseUnit = this.optionsLoc.ApricotReportDoseUnit || [];
            if(ApricotReportDoseUnit.length) {
                this.form['sFullUnit'] = ApricotReportDoseUnit[0].sValue;
            }
            
            let ApricotReportDrugDelivery = this.optionsLoc.ApricotReportDrugDelivery;
            if (ApricotReportDrugDelivery.length) this.form['sDrugDeliveryCode'] = ApricotReportDrugDelivery[0].sValue;

            let ApricotReportMedicineSource = this.optionsLoc.ApricotReportMedicineSource;
            if(ApricotReportMedicineSource.length === 1) this.form['sMedicineSource'] = ApricotReportMedicineSource[0].sValue;

            const ApricotReportInjectSite = this.optionsLoc.ApricotReportInjectSite
            if (ApricotReportInjectSite.length) {
                this.form['sInjectionPosition'] = ApricotReportInjectSite[0].sValue
            }
            // this.bodyPartCode = 1
            this.form['sStainPosition'] = '无';

            if(this.injectPatientInfo?.sPatientId === this.providePatient?.sId) {
                const fieldList = ['sNuclide', 'sTracer','sNuclideText', 'sTracerText','fRecipeDose', 'sRecipeDoseUnit', 'fBloodSugar'];
                fieldList.map(item => {
                    this.form[item] = this.injectPatientInfo[item];
                })
            }
            if(!this.form['sRecipeDoseUnit']) {
                this.form['sRecipeDoseUnit'] = ApricotReportDoseUnit[0].sValue;
            }
        },
        closeOptions (refName) {
            this.$refs[refName] && this.$refs[refName].blur();
        },
        onClickRow (row) {
            this.row = row;
            this.col = 0;
        },
        // 获取下拉框组件选中值的标签名
        getName (arr, val) {
            let item = arr.find(item => item.sValue == val);
            return item ? item.sName : null;
        },
        onSaveClick () {
            this.patientInfo.sId && this.saveData();
        },
        // 保存数据
        saveData () {
            if (!this.patientInfo.sId) {
                this.$message.warning('请选择一条患者数据！');
                this.$emit('upDateLoading', 'saveLoading')
                return
            }
            if (!this.form.fFullNeedle) {
                this.$message.warning('请录入满针剂量！');
                return
            }
            if (!this.form.fEmptyNeedle) {
                this.$message.warning('请录入空针剂量！');
                return
            }
            let form = deepClone(this.form);
            form.sDrugDeliveryText = this.getName(this.optionsLoc.ApricotReportDrugDelivery, form.sDrugDeliveryCode);
            form.sMedicineSourceText = this.getName(this.optionsLoc.ApricotReportMedicineSource, form.sMedicineSource);
            form.sFullUnitText = this.getName(this.optionsLoc.ApricotReportDoseUnit, form.sFullUnit);
            form.sInjectionPositionText = this.getName(this.optionsLoc.ApricotReportInjectSite, form.sInjectionPosition);
            this.optionsLoc.DoctorOptions.find(item => {
                if (item.userId === form.sNurseId) {
                    form.sNurseNo = item.userNo;
                    form.sNurseName = item.userName;
                }
            })
            
            // if (this.bodyPartCode) {
            //     form.sInjectionPositionText = this.optionsLoc.ApricotReportInjectSite[this.bodyPartCode - 1].sName;
            //     form.sInjectionPosition = this.optionsLoc.ApricotReportInjectSite[this.bodyPartCode - 1].sValue;
            // }

            if (form.dInjectDate && form.dInjectionSta) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dInjectionSta).toTimeString().substring(0, 8)
                form.dInjectionSta = new Date(`${sDate} ${sTime}`)
            }
            if (form.dInjectDate && form.dInjectionTime) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dInjectionTime).toTimeString().substring(0, 8)
                form.dInjectionTime = new Date(`${sDate} ${sTime}`)
            }
            if (form.dInjectDate && form.dInjectionEnd) {
                let sDate = new Date(form.dInjectDate).toLocaleDateString();
                let sTime = new Date(form.dInjectionEnd).toTimeString().substring(0, 8)
                form.dInjectionEnd = new Date(`${sDate} ${sTime}`)
            }
            this.loading3 = true;
            if (!this.form.sId) {
                saveInject(form).then(res => {
                    this.$emit('upDateLoading', 'saveLoading')
                    this.loading3 = false;
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.updateData();
                        // 新增时，根据设置判断是否需要打印；
                        if(this.$auth['report:injection:print']) {
                            this.mxOnPrintByCache(this.injectModuleSetData);
                        }
                        this.initForm();
                        this.$eventbus.emit('onRefreshInjectRecordCard', res.data);
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(() => {
                    this.loading3 = false;
                    this.$emit('upDateLoading', 'saveLoading')
                })
                return
            }
            editInject(form).then(res => {
                this.loading3 = false;
                this.$emit('upDateLoading', 'saveLoading')
                if (res.success) {
                    this.$message.success(res.msg);
                    this.updateData();
                    // 修改时，根据缓存设置判断是否需要打印；
                    if(this.$auth['report:injection:print']) {
                        this.mxOnPrintByCache(this.injectModuleSetData, false);
                    }
                    this.initForm();
                    this.$eventbus.emit('onRefreshInjectRecordCard', res.data);
                    return;
                }
                this.$message.error(res.msg);
            }).catch(() => {
                this.loading3 = false;
                this.$emit('upDateLoading', 'saveLoading')
            })
        },
        updateData () {
            this.updateRowData();
            this.getInitInJectData(this.patientInfo.sId)
        },
        // 定时读取活度仪数据
        intervalActivityMeterRead () {
            activityMeterRead().then(res => {
                this.isOffline = false;
                if (res.success) {
                    let data = res.data || {};
                    this.activityMeterData = data;
                    return
                }
                // this.isOffline = true;
                this.$notify.closeAll();
                this.$notify.error({
                    message: res.msg,
                    position: 'bottom-right'
                });
            }).catch((e) => {
                this.isOffline = true;
                this.$message.closeAll()
            })
        },
        // 活度仪定时器
        activityMeterTimesRead () {
            if (!this.activityMeterData.length) {
                // 当没有页面没有数据的时候发起一次请求
                this.intervalActivityMeterRead()
            }
            clearInterval(this.activityMeterTimes);
            this.isConfirm = false;
            let configData = this.injectModuleSetData;
            let speedhq = configData.readInterval || 3;
            this.activityMeterTimes = setInterval(() => {
                this.intervalActivityMeterRead()
            }, speedhq * 1000)
        },
        // 时间定时器
        dateTimeRead () {
            clearInterval(this.dateTimes);
            let speedhq = 200;
            this.dateTimes = setInterval(() => {
                //获取系统当前的小时、分钟
                var date = new Date();
                var hour = date.getHours();
                hour = hour < 10 ? '0' + hour : hour;
                var minutes = date.getMinutes();
                minutes = minutes < 10 ? '0' + minutes : minutes;
                var senconds = date.getSeconds();
                senconds = senconds < 10 ? '0' + senconds : senconds;
                this.timeStr = hour + ":" + minutes + ":" + senconds;
            }, speedhq)
        },
        toVerifyNuclide( callback ) {
            const data = this.activityMeterData;
            // 提示用户，活度仪核素与登记核素不一致
            const sNuclideText = this.form.sNuclideText;
            const isotopeName = data.isotopeName;
            if(this.configValue.medicineChooseType == 2) {
                if(sNuclideText != isotopeName) {
                    this.$message.warning('请先配置对应的检查用药！');
                    return
                }
            } else {
                const oItem = this.medicationTableData.find(item => item.sNuclideName == isotopeName);
                if(!oItem) {
                    this.$message.warning('请先配置对应的检查用药！');
                    return
                }
            }
            if (sNuclideText && isotopeName && sNuclideText !== isotopeName) {
                // if (this.isConfirm) return
                // this.isConfirm = false;
                this.$confirm(`活度仪核素与当前核素不一致！`, '提示', {
                    confirmButtonText: '知道了',
                    showCancelButton: false,
                    customClass: 'c-activity-tip',
                    type: 'warning'
                }).then(() => {
                    // this.isConfirm = true;
                }).catch(() => {
                    // this.isConfirm = false;
                });
            } else {
                this.configValue.medicineChooseType == 1 && this.nuclearTableRowClick(oItem);
                callback && callback()
            }
        },
        // 获取满针数据
        onFullNeedleClick () {
            if (this.isOffline) {
                // this.$message.warning('活度仪数据读取失败！');
                this.$confirm('数据读取失败，请检查设备或手动输入信息！', '提示', {
                    confirmButtonText: '知道了',
                    showCancelButton: false,
                    customClass: 'c-activity-tip',
                    type: 'warning'
                }).then(() => {
                }).catch(() => {
                });
                return
            }
            this.toVerifyNuclide(this.setFullNeedleData);
        },
        // 获取空针剂量
        onNullNeedleClick () {
            if (this.isOffline) {
                // this.$message.warning('活度仪数据读取失败！')
                this.$confirm('数据读取失败，请检查设备或手动输入信息！', '提示', {
                    confirmButtonText: '知道了',
                    showCancelButton: false,
                    customClass: 'c-activity-tip',
                    type: 'warning'
                }).then(() => {
                }).catch(() => {
                });
                return
            }
            this.toVerifyNuclide(this.setNullNeedleData);
        },
        onPrintClick () {
            this.autoPrintPocket();
        },
        // 加载提示
        loadFindTip (text) {
            return this.$loading({
                lock: true,
                text: text || '文件生成中...',
                // 
                background: 'rgba(0, 0, 0, 0.1)',
                customClass: 'my-loading'
            });
        },
        // 获取衰变周期和衰变单位
        getDecayParams() {
            let fDecayPeriod = undefined;
            let iDecayUnit = undefined;
            if(this.configValue?.medicineChooseType == 2) {
                console.log('随机选择模式')
                // 随机选择模式
                this.optionsLoc.nuclideOptions.find(item => {
                    if(item.sId === this.form.sNuclide){
                        fDecayPeriod = item.fDecayPeriod;
                        iDecayUnit = item.iDecayUnit;
                    }
                })
                return { fDecayPeriod, iDecayUnit }
            }
            // 关联选择模式
            fDecayPeriod = this.nuclearParams?.fDecayPeriod;
            iDecayUnit = this.nuclearParams?.iDecayUnit;
            return { fDecayPeriod, iDecayUnit }
        },
        computedInjectDose() {
            let A0 = this.form.fFullNeedle;     // 满针剂量
            let A2 = this.form.fEmptyNeedle;    // 空针剂量
            let t0 = this.form.dInjectionSta;   // 满针时间
            let t1 = this.form.dInjectionTime;  // 注射时间
            let t2 = this.form.dInjectionEnd;   // 空针时间
            const isNumeric = this.isNumeric;
            const isFalseArr = [null, undefined, ''];
            if(!isNumeric(A0) || !isNumeric(A2)) {
            // if(!isNumeric(A0) || !isNumeric(A2)  || isFalseArr.includes(t0) || isFalseArr.includes(t1) || isFalseArr.includes(t2)) {
                // 实际注射量
                this.form['fFactDose'] = '';
                console.log('注射信息不完整，无法计算');
                return
            }
            // 衰变周期 , 衰变单位
            const { fDecayPeriod, iDecayUnit } = this.getDecayParams();
            if(!isNumeric(fDecayPeriod) || !isNumeric(iDecayUnit)) {
                // 两者有不是数字的时候
                this.form['fFactDose'] = (A0 - A2).toFixed(2);
                console.log('衰变周期未设置, A1 = A0 - A2', this.form['fFactDose'])
                return 
            }

            // let period = 109.8 * 60 * 1000;
            // 衰变单位：1=秒；2=分；3=时；4=天；5=月；6=年；
            // 单位对应秒转换
            const oToSeconds = {
                1 : 1000,
                2 : 60 * 1000,
                3 : 60 * 60  * 1000,
                4 : 24 * 60 * 60  * 1000,
                5 : 30 * 24 * 60 * 60  * 1000,
                6 : 365 * 24 * 60 * 60  * 1000,
            }
            // 衰变周期
            const period = fDecayPeriod * oToSeconds[iDecayUnit];
            t0 = new Date(this.form.dInjectionSta).getTime();   
            t1 = new Date(this.form.dInjectionTime).getTime();  
            t2 = new Date(this.form.dInjectionEnd).getTime();   
            if(isFalseArr.includes(t0) || isFalseArr.includes(t1) || isFalseArr.includes(t2)) {
                this.$message.warning('（满针时间、实际注射时间、空针时间）有空值！');
                this.form['fFactDose'] = '';
                return
            }
            if(t0 > t2) {
                this.$message.warning('满针时间不能大于空针时间');
                this.form['fFactDose'] = '';
                return
            }
            let A1 = A0 * Math.exp(-Math.LN2 / period * Math.abs(t1 - t0)) - A2 * Math.exp(-Math.LN2 / period * Math.abs(t2 - t1));
            console.log('半衰期公式参数fDecayPeriod，iDecayUnit，A1', fDecayPeriod, iDecayUnit, A1);
            A1 = A1.toFixed(2);
            console.log('使用半衰期公式计算，A1=', A1);
            this.form['fFactDose'] = A1;
        },
        // 判断是一个变量是否为数字或字符串数字
        isNumeric(value) {
            // 使用 typeof 来判断变量是否为数字类型
            if (typeof value === 'number') {
                return true;
            }
            // 使用正则表达式来检查字符串是否表示数字
            if (typeof value === 'string' && value.trim() !== '') {
                // 去除字符串两端的空格，并检查是否为空字符串
                return /^\d+(\.\d+)?$/.test(value.trim());
            }
            return false;
        },
        // // 满针数据
        setFullNeedleData (data) {
            let activityMeterData = data ? data : this.activityMeterData;
            let configData = this.injectModuleSetData;
            this.form.sNuclideText = activityMeterData.isotopeName;
            this.form.dInjectionSta = activityMeterData.readTime ? new Date(new Date(activityMeterData.readTime).getTime() - configData.iFull * 60 * 1000) : '';
            this.form.fFullNeedle = activityMeterData.value;
            this.form.sFullUnit = activityMeterData.unit;
            this.computedInjectDose();
        },
        // 空针数据
        setNullNeedleData (data) {
            let activityMeterData = data ? data : this.activityMeterData;
            let configData = this.injectModuleSetData;
            this.form.dInjectionEnd = activityMeterData.readTime  ? new Date(new Date(activityMeterData.readTime).getTime() - configData.iNull * 60 * 1000) : '';
            this.form.fEmptyNeedle = activityMeterData.value;
            this.form.sFullUnit = activityMeterData.unit;
            this.form.dInjectionTime =  this.form.dInjectionEnd ? new Date(new Date(activityMeterData.readTime).getTime() - configData.iActual * 60 * 1000) : '';
            this.computedInjectDose();
        },
        onFocusFn (keyword) {
            let target = this.injectModuleSetData;
            target[keyword] = target[keyword] || 0;
            this.defaultValue[keyword] = new Date(new Date().getTime() - target[keyword] * 60 * 1000);
        },
        // 关闭打印弹窗
        closeTemplateDialog () {
            this.selectedTemplateId = '';
            this.visible_Template = false;
        },
        // 打印
        async autoPrintPocket () {
            this.btnsOption = [{
                iClassify: 7,
                sClassify: '注射单',
                templateList: []
            }]
            // let deviceTypeIdKey = this.propParams.deviceTypeIdKey || 'sRoomId';
            let params = {
                iClassify: this.btnsOption[0].iClassify,
                iModuleId: this.iModuleId,
                sDeviceTypeId: this.patientInfo.sRoomId
            }
            await this.mxGetWorkStationPrintShow(this.iModuleId);
            await this.mxGetTemplateOfPrintClassify(params);
            let targetArr = this.btnsOption[0].templateList;
            if (targetArr.length === 0) {
                this.$message.warning('未找到可打印模板！');
                return
            }
            if (targetArr.length === 1) {
                // 执行打印步骤
                let params = {
                    patient: this.patientInfo,
                    isBatch: false,
                    idKey: 'sId',
                    template: targetArr[0]
                }
                this.mxHandlePrint(params);
                return
            }
            // 显示模板弹窗
            this.visible_Template = true;
            this.injectTemplateList = targetArr;
        },
        // 确认打印
        onPrintPocket () {
            let targetObj = this.injectTemplateList.find(item => item.sTemplateId === this.selectedTemplateId);
            if (targetObj) {
                let params = {
                    patient: this.patientInfo,
                    isBatch: false,
                    idKey: 'sId',
                    // deviceTypeIdKey: 'sDeviceTypeId',
                    template: targetObj
                }
                this.mxHandlePrint(params);
                this.selectedTemplateId = '';
                this.visible_Template = false;
            }
        },
        // 显示显示非该用户类型的医生
        AddDocOptionToShow(params) {
            const targetItem = this.optionsLoc.DoctorOptions.find(item => item.userId === params.sNurseId);
            if(!targetItem) {
                this.optionsLoc.DoctorOptions.push({
                    userId: params.sNurseId,
                    userNo: params.sNurseNo,
                    userName: params.sNurseName,
                    sValue: params.sNurseId,
                    sName: params.sNurseName,
                    iIsAuxiliary: 1,
                })
            }
        },
    },
    mounted () {
        // 兄弟组件 接收Machine.vue组件上机信息修改
        this.$eventbus.on('injectActivityMeterEdit', res => {
            if(res.sNurseId) {
                this.AddDocOptionToShow(res)
            }
            this.$emit('onChangeActiveTab');
            this.$nextTick(() => {
                this.form = res;
            })
        })

        this.$eventbus.on('injectActivityMeterInit', row => {
            if(row.sId === this.form.sId) {
                this.initForm();
            }
        })
    },
    destroyed() {
        clearInterval(this.dateTimes);
        clearInterval(this.activityMeterTimes);
        this.$eventbus.off('injectActivityMeterEdit');
        this.$eventbus.off('injectActivityMeterInit');
    }
};
</script>
<style lang="scss" scoped>
.c-page-box {
    display: flex;
    flex-direction: column;
    height: 100%;
    .c-item {
        padding: 18px 0px;
        position: relative;
        //border-bottom: var(--el-border);
        .c-text{
            width: 33.333%;
            padding-left: 10px;
             > strong {
                color: var(--el-color-primary);
                // color: rgb(252, 88, 88);
                font-size: 20px;
            }

        }
    }
    .c-item-2 {
        // flex: 1;
        // height: 0;
        box-sizing: border-box;
        width: 100%;
        margin-bottom: 5px;
        .el-button.el-button--large {
            font-size: 16px;
            padding: 10px 14px;
        }
        .inject {
            
        } 
    }
    .c-row {
        display: flex;
        margin: 10px 0 5px 0;
        padding: 0 10px;
        justify-content: center;
        align-items: center;
        .c-content {
            margin-right: 5px;
            &:last-child {
                margin-right: 0px;
            }
            :deep(.el-input--large ) {
                height: 38px;
                .el-input__inner {
                    height: 36px;
                    line-height: 36px;
                }
            }
            :deep(.el-date-editor.el-input) {
                height: 100%;
            }
            
        }
        
        & > div{
            box-sizing: border-box;
        }
        .c-content {
            width: 33.333%;
            flex-shrink: 0;
            flex: 1;
        }
        .label {
            width: 80px;
            flex-shrink: 0;
            text-align: right;
            padding-right: 10px;
            color: var(--el-color-primary);
            color: #333;
            text-align: justify;
            text-align-last: justify;
            font-weight: bold;
            &:nth-child(2n+1) {
                margin-left: 10px;
            }
        }
        .action {
            min-width: 100px;
            min-height: 30px;
            text-align: right;
            .el-button.is-plain {
                width: 88px;
            }
        }
        .flex-x {
            display: flex;
            flex: 1;
            justify-content: space-between;
            padding: 10px 0;
            border: var(--el-border);
            border-color: var(--el-color-primary);
        }
        .c-active {
            background: var(--el-color-primary);
            // background: #1cd65a;
            // border-color: #1cd65a;
            color: #fff;
        }
    }
}
</style>
<style lang="scss" scoped>
.c-activity-tip {
    vertical-align: unset;
    .el-message-box__message {
        font-size: 16px;
    }
}
:deep(.el-input-group__append) {
    padding: 0;
    width: 80px;
    flex-shrink: 0;
}
</style>
