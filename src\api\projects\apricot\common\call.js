import request, {baseURL, stringify} from '$supersetUtils/request'

export default {
    getCallCaptionsData,
    getCalledData,
    getScreen,
    callAction,
    isCalledBtns
}

// 呼叫内容
export function getCallCaptionsData(params) {
    return request({
        url: baseURL.apricot + '/call/captions/get/captions',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取呼叫记录 
export function getCalledData(params) {
    return request({
        url: baseURL.apricot + '/call/record/callRecords',
        method: 'POST',
        data: stringify(params)
    })
}

// 获取屏幕
export function getScreen(params) {
    return request({
        url: baseURL.apricot + '/call/route/screen',
        method: 'POST',
        data: stringify(params)
    })
}

// 呼叫
export function callAction(data) {
    return request({
        url: baseURL.apricot + '/call/route/call',
        method: 'POST',
        data
    })
}

// 获取患者列表已呼叫按钮  
export function isCalledBtns(params) {
    return request({
        url: baseURL.apricot + '/call/record/callBtnRecords',
        method: 'POST',
        data: stringify(params)
    })
}