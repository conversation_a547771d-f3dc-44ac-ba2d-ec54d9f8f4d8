/**
 * 子项目路由配置
 */
/*组件引入------------------------------*/

import Index from '$supersetViews/welcome/Index.vue'
import Jump from '$supersetViews/welcome/Jump.vue'

/*------------------------------组件引入*/

/*方法引入------------------------------*/
//添加模块权限的方法
import { moduleListInit } from '$supersetResource/js/tools'
/*------------------------------方法引入*/



// 2. 定义路由
const moduleList = [
	{
		path: 'index', 
		name: 'Index',
		component: Index,
		meta: {
			name: '主页',
			icon: 'fa-home1'
		}
	},
  {
		path: 'jump', 
		name: 'Jump',
		component: Jump,
    hidden: true,
		meta: {
			name: '跳转页',
			icon: 'fa-home1'
		}
	},
]

//路由模块初始化
moduleListInit(moduleList, {
	projectName: 'welcome'
})

export default moduleList
