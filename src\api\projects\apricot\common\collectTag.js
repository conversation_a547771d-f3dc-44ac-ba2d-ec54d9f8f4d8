import request from '$supersetUtils/request'
import {
    baseURL
} from '$supersetUtils/request'

// 收藏管理
export function getCollectionTagData() {
    return request({
        url: baseURL.apricot + '/collectiontag/find/loginuser',
        method: 'POST',
    })
}

// 启用标签
export function getCollectionTagEnable() {
    return request({
        url: baseURL.apricot + '/collectiontag/find/enable',
        method: 'POST',
    })
}

// 新增
export function addCollectionTag(params) {
    return request({
        url: baseURL.apricot + '/collectiontag/add',
        method: 'POST',
        params
    })
}
// 修改
export function editCollectionTag(params) {
    return request({
        url: baseURL.apricot + '/collectiontag/edit',
        method: 'POST',
        params
    })
}
// 删除
export function delCollectionTag(params) {
    return request({
        url: baseURL.apricot + '/collectiontag/del',
        method: 'POST',
        params
    })
}
