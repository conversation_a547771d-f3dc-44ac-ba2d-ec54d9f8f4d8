<template>
  <!-- <div :title="isFullscreen ? '取消全屏' : '全屏' "  @click="click"> -->
    <!-- <i class="fa" :class="isFullscreen?'fa-circle-thin':'fa-square-o'" @click="click"></i> -->
    <!-- <svg v-if="isFullscreen" t="1594603818153" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="93962" width="14" height="14"><path d="M351.665658 639.937506h0.699931c17.398301 0 31.696905 14.298604 31.696905 31.696905v320.668684c0 17.398301-14.298604 31.696905-31.696905 31.696905h-0.699931c-17.398301 0-31.696905-14.298604-31.696905-31.696905V671.634411c0-17.398301 14.298604-31.696905 31.696905-31.696905z" p-id="93963"></path><path d="M0 672.234352v-0.699932C0 654.23611 14.198613 639.937506 31.696905 639.937506h320.668684c17.398301 0 31.696905 14.298604 31.696905 31.696905v0.699931c0 17.398301-14.298604 31.696905-31.696905 31.696905H31.696905C14.198613 703.931257 0 689.732643 0 672.234352z" p-id="93964"></path><path d="M374.263451 649.136608l0.499951 0.499951c12.298799 12.298799 12.298799 32.496826 0 44.795625L55.494581 1013.601016c-12.298799 12.298799-32.496826 12.298799-44.795626 0l-0.499951-0.499952c-12.298799-12.298799-12.298799-32.496826 0-44.795625l319.168831-319.168831c12.398789-12.298799 32.596817-12.298799 44.895616 0z" p-id="93965"></path><path d="M672.234352 639.937506h-0.699932c-17.398301 0-31.696905 14.298604-31.696904 31.696905v320.668684c0 17.398301 14.298604 31.696905 31.696904 31.696905h0.699932c17.398301 0 31.696905-14.298604 31.696905-31.696905V671.634411c0-17.398301-14.198613-31.696905-31.696905-31.696905z" p-id="93966"></path><path d="M1023.90001 672.234352v-0.699932c0-17.398301-14.298604-31.696905-31.696905-31.696904H671.634411c-17.398301 0-31.696905 14.298604-31.696905 31.696904v0.699932c0 17.398301 14.298604 31.696905 31.696905 31.696905h320.668684c17.398301 0 31.596914-14.198613 31.596915-31.696905z" p-id="93967"></path><path d="M649.636559 649.136608l-0.499951 0.499951c-12.298799 12.298799-12.298799 32.496826 0 44.795625l319.168831 319.168832c12.298799 12.298799 32.496826 12.298799 44.795625 0l0.499952-0.499952c12.298799-12.298799 12.298799-32.496826 0-44.795625L694.432184 649.136608c-12.298799-12.298799-32.496826-12.298799-44.795625 0z" p-id="93968"></path><path d="M351.665658 383.962504h0.699931c17.398301 0 31.696905-14.298604 31.696905-31.696905V31.696905C383.962504 14.198613 369.76389 0 352.265599 0h-0.699932C334.267357 0 319.968753 14.198613 319.968753 31.696905v320.668684c0 17.398301 14.298604 31.596914 31.696905 31.596915z" p-id="93969"></path><path d="M0 351.665658v0.699931C0 369.76389 14.198613 383.962504 31.696905 383.962504h320.668684c17.398301 0 31.696905-14.298604 31.696905-31.696905v-0.699932c0-17.398301-14.298604-31.696905-31.696905-31.696904H31.696905C14.198613 319.968753 0 334.267357 0 351.665658z" p-id="93970"></path><path d="M374.263451 374.763402l0.499951-0.499951c12.298799-12.298799 12.298799-32.496826 0-44.795626L55.494581 10.298994C43.195782-1.999805 22.997754-1.999805 10.698955 10.298994l-0.499951 0.499951c-12.298799 12.298799-12.298799 32.496826 0 44.795626l319.168831 319.168831c12.398789 12.298799 32.596817 12.298799 44.895616 0z" p-id="93971"></path><path d="M672.234352 383.962504h-0.699932c-17.398301 0-31.696905-14.298604-31.696904-31.696905V31.696905C639.937506 14.198613 654.23611 0 671.634411 0h0.699931C689.732643 0 703.931257 14.198613 703.931257 31.696905v320.668684c0 17.398301-14.198613 31.596914-31.696905 31.596915z" p-id="93972"></path><path d="M1023.90001 351.665658v0.699931c0 17.398301-14.298604 31.696905-31.696905 31.696905H671.634411c-17.398301 0-31.696905-14.298604-31.696905-31.696905v-0.699931c0-17.398301 14.298604-31.696905 31.696905-31.696905h320.668684c17.398301 0 31.596914 14.298604 31.596915 31.696905z" p-id="93973"></path><path d="M649.636559 374.763402l-0.499951-0.499951c-12.298799-12.298799-12.298799-32.496826 0-44.795626L968.405429 10.298994c12.298799-12.298799 32.496826-12.298799 44.795626 0l0.499951 0.499951c12.298799 12.298799 12.298799 32.496826 0 44.795626L694.432184 374.763402c-12.298799 12.298799-32.496826 12.298799-44.795625 0z" p-id="93974"></path></svg> -->
    <!-- <svg v-else t="1594603881524" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="94328" width="14" height="14"><path d="M32.321058 1023.900012h-0.699915C14.223267 1023.900012 0.025 1009.701745 0.025 992.203881V671.543025C0.025 654.245136 14.223267 639.946881 31.721131 639.946881h0.699914c17.397876 0 31.696131 14.298255 31.696131 31.696131v320.660857c-0.099988 17.397876-14.298255 31.596143-31.796118 31.596143z" p-id="94329"></path><path d="M383.978131 991.603955v0.699914c0 17.397876-14.298255 31.696131-31.696131 31.696131H31.721131C14.223267 1023.900012 0.025 1009.701745 0.025 992.203881v-0.699914C0.025 974.206078 14.223267 959.907824 31.721131 959.907824h320.660857c17.397876 0 31.596143 14.298255 31.596143 31.696131z" p-id="94330"></path><path d="M9.723816 1014.701135l-0.499939-0.499939c-12.298499-12.298499-12.298499-32.496033 0-44.794532l294.564043-294.564042c12.298499-12.298499 32.496033-12.298499 44.794531 0l0.499939 0.499939c12.298499 12.298499 12.298499 32.496033 0 44.794532L54.518348 1014.701135c-12.298499 12.298499-32.496033 12.298499-44.794532 0z" p-id="94331"></path><path d="M32.321058 0.024997h-0.699915C14.223267 0.024997 0.025 14.223264 0.025 31.721128v320.660857C0.025 369.779861 14.223267 383.978128 31.721131 383.978128h0.699914c17.397876 0 31.696131-14.298255 31.696131-31.696131V31.721128C64.017188 14.223264 49.818922 0.024997 32.321058 0.024997z" p-id="94332"></path><path d="M383.978131 32.321055v-0.699915C383.978131 14.223264 369.779864 0.024997 352.282 0.024997H31.721131C14.223267 0.024997 0.025 14.223264 0.025 31.721128v0.699914C0.025 49.818919 14.223267 64.017185 31.721131 64.017185h320.660857c17.397876 0 31.596143-14.198267 31.596143-31.69613z" p-id="94333"></path><path d="M9.723816 9.223874l-0.499939 0.499939C-3.074622 22.022312-3.074622 42.219846 9.223877 54.518345l294.564043 294.564042c12.298499 12.298499 32.496033 12.298499 44.794531 0l0.499939-0.499939c12.298499-12.298499 12.298499-32.496033 0-44.794531L54.518348 9.223874C42.219849-3.074625 22.022315-3.074625 9.723816 9.223874z" p-id="94334"></path><path d="M991.603958 1023.900012h0.699914c17.397876 0 31.696131-14.298255 31.696131-31.696131V671.643012c0-17.397876-14.298255-31.696131-31.696131-31.696131h-0.699914c-17.397876 0-31.696131 14.298255-31.696131 31.696131v320.660857c0 17.397876 14.298255 31.596143 31.696131 31.596143z" p-id="94335"></path><path d="M639.946885 991.603955v0.699914c0 17.397876 14.298255 31.696131 31.69613 31.696131h320.660857c17.397876 0 31.696131-14.298255 31.696131-31.696131v-0.699914c0-17.397876-14.298255-31.696131-31.696131-31.696131H671.643015c-17.397876 0-31.696131 14.298255-31.69613 31.696131z" p-id="94336"></path><path d="M1014.201199 1014.701135l0.499939-0.499939c12.298499-12.298499 12.298499-32.496033 0-44.794532L720.037108 674.842622c-12.298499-12.298499-32.496033-12.298499-44.794532 0l-0.499939 0.499939c-12.298499 12.298499-12.298499 32.496033 0 44.794532l294.564042 294.564042c12.398487 12.298499 32.596021 12.298499 44.89452 0z" p-id="94337"></path><path d="M991.603958 0.024997h0.699914c17.397876 0 31.696131 14.198267 31.696131 31.696131v320.660857c0 17.397876-14.298255 31.696131-31.696131 31.69613h-0.699914c-17.397876 0-31.696131-14.298255-31.696131-31.69613V31.721128C959.907827 14.223264 974.206081 0.024997 991.603958 0.024997z" p-id="94338"></path><path d="M639.946885 32.321055v-0.699915C639.946885 14.223264 654.245139 0.024997 671.643015 0.024997h320.660857c17.397876 0 31.696131 14.198267 31.696131 31.696131v0.699914c0 17.397876-14.298255 31.696131-31.696131 31.696131H671.643015C654.245139 64.017185 639.946885 49.818919 639.946885 32.321055z" p-id="94339"></path><path d="M1014.201199 9.223874l0.499939 0.499939c12.298499 12.298499 12.298499 32.496033 0 44.794532L720.037108 349.082387c-12.298499 12.298499-32.496033 12.298499-44.794532 0l-0.499939-0.499939c-12.298499-12.298499-12.298499-32.496033 0-44.794531L969.406667 9.223874c12.298499-12.298499 32.496033-12.298499 44.794532 0z" p-id="94340"></path></svg> -->
    <!-- <el-button v-if="isFullscreen" class="f-lockApp n-menuBtn"><i class="fa fa-full-screen"></i></el-button> -->
    <!-- <el-button class="f-lockApp n-menuBtn"><i class="fa fa-exit-full-screen"></i></el-button> -->
  <!-- </div> -->
  <el-button :title="isFullscreen ? '取消全屏' : '全屏' " link  @click="click" class="f-lockApp n-menuBtn">
    <i v-if="isFullscreen" class="fa fa-exit-full-screen"></i>
    <i v-else class="fa fa-full-screen"></i>
  </el-button>
</template>

<script>
import screenfull from 'screenfull'

export default {
  name: 'Screenfull',
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    this.init()
  },
  beforeUnmount() {
    this.destroy()
  },
  methods: {
    click() {
    //   console.log(screenfull)
      if (!screenfull.isEnabled) {
        this.$message({
          message: 'you browser can not work',
          type: 'warning'
        })
        return false
      }
      screenfull.toggle()
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.isEnabled) {
        screenfull.on('change', this.change)
      }
    },
    destroy() {
      if (screenfull.isEnabled) {
        screenfull.off('change', this.change)
      }
    }
  }
}
</script>

<style scoped>
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}

.n-menuBtn{
  border: none;
}
.f-lockApp{
    padding: 8px;
}
</style>
