<template>
    <div class="scope-pathological">
        <div class="xx-el-scrollbar"
            ref="scrollbar">
            <el-form :model="editLayer.form"
                ref="refEditLayer"
                :rules="rules">
                <el-row>
                    <el-col :span="8">
                        <el-form-item prop="dReportDate" label="检验时间：">
                            <el-date-picker v-model="editLayer.form.dReportDate"
                                :clearable="false"
                                type="date">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="1"></el-col>
                    <el-col :span="15">
                        <el-form-item label="病理类别：">
                            <el-input style="width:"
                                v-model="editLayer.form.sPathologyTypeText">
                                <template #append>
                                    <el-button type="primary"
                                        @click="project.visible = true">选择</el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="病理结果：">
                    <el-input type="textarea"
                        :rows="rows"
                        v-model="editLayer.form.sPathologyResult"></el-input>
                </el-form-item>
            </el-form>
            <div class="c-action">
                <el-button @click="getData"
                    type="primary"
                    plain
                    size="small"
                    :loading="loading"><el-icon><Refresh /></el-icon>刷 新</el-button>
            </div>
        </div>
        <div class="c-flex-auto">
            <!-- <h3>病理结果列表</h3> -->
            <!-- v-loading="loading" -->
            <el-table v-loading="loading"
                :data="tableData"
                border
                stripe
                height="100%"
                highlight-current-row
                @row-click="onClickRow"
                >
                <el-table-column prop="dReportDate"
                    label="检验时间"
                    align="center"
                    width="140">
                    <template v-slot="scope">
                        {{ transformDate(scope.row.dReportDate) }}
                    </template>
                </el-table-column>
                <el-table-column prop="sPathologyTypeText"
                    label="病理类别"
                    min-width="200"
                    show-overflow-tooltip>
                </el-table-column>
                <el-table-column prop="sPathologyResult"
                    label="病理结果"
                    min-width="200"
                    show-overflow-tooltip>
                </el-table-column>
                <el-table-column label="操作"
                    align="center"
                    width="90"
                    fixed="right">
                    <template v-slot="scope">
                        <el-button-icon-fa link icon="el-icon-delete" @click="onClickDelete(scope.$index, tableData)">
                            删除
                        </el-button-icon-fa>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-dialog :close-on-click-modal="false"
            append-to-body
            title="病理类别"
            v-model="project.visible"
            width="700px"
            top="9vh"
            class="t-default my-dialog">
            <div class="c-content" style="height:65vh;">
                <CheckboxTree1 :list="showProject" />
            </div>
            <template #footer>
                <el-button 
                    @click="project.visible = false"> 
                    <template #icon><el-icon><Close /></el-icon></template>关 闭</el-button>
                <el-button
                    type="primary"
                    @click="getOperationDataText">
                   <template #icon><el-icon><Check/></el-icon></template>
                    确 定</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { Refresh,Close,Check } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import CheckboxTree1 from '$plugins/other/CheckboxTree1.vue'
import { deepClone } from '$supersetUtils/function'
import { transformDate } from '$supersetResource/js/tools'
// 接口
import Api from '$supersetApi/projects/apricot/case/consult.js'

export default {
    name: 'Pathological',
    components: {
        CheckboxTree1,
        Refresh,
        Close,
        Check
    },
    inject:{
        patientInfo:{
            from:'patientInfo',
            default:  ({})
        }
    },
    props:{
        paramsData: {
            //  父组件是弹窗的传参,非必填
            type: Object,
            default: ({})
        },
    },
    emits:['updateLoading','getPathologicalNum'],
    data () {
        return {
            showProject: [],
            project: {
                visible: false,
            },
            rules:{

            },
            tableData:[],
            loading: false,
            editLayer: {
                form: {
                    dReportDate: new Date(),
                    sPathologyTypeCode: '',
                    sPathologyTypeName: '',
                    sPathologyResult: '',
                    sPathologyTypeJson: '',
                    sPathologyTypeText: '',
                }
            },
            rights: {},
            rows: 4,
            sPatientId: ''
        }
    },
    watch: {
        patientInfo: {
            handler(val,oldVal) { 
                if(val && oldVal && val.sId === oldVal.sId) return;
                if(val.sId) {
                    this.onResetForm('refEditLayer')
                    this.sPatientId = val.sId
                    this.getData()
                }
            },
        },
        paramsData: {
             handler(val) { 
                if(val.sId) {
                    this.onResetForm('refEditLayer');
                    this.sPatientId = val.sId;
                    this.getData();
                }
             },
            immediate: true
        }
    },
    methods: {
        transformDate: transformDate,
        /**
         * 清空
         */
        onResetForm (formName) {
            this.$refs[formName] && this.$refs[formName].resetFields();
            delete this.editLayer.form.sId
            delete this.editLayer.form.iVersion
            // 清空项目值
            this.editLayer.form.sPathologyTypeText = ''
            this.editLayer.form.sPathologyTypeJson = []
            this.editLayer.form.sPathologyResult = ''
            this.getOperationType()
            // this.form.sPathologyTypeJson = this.deepClone(this.showProject)
        },
        resetFormNull() {
            this.onResetForm('refEditLayer')
        },
        /**
         * 点击删除提示
         */
        onClickDelete (index, rows) {
            ElMessageBox.confirm('您确定要删除该行吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.delItemData(rows[index], index) // s删除
            }).catch(() => { });
        },
        recursiveData (data) {
            var result = ''
            for (let index = 0; index < data.length; index++) {
                const el = data[index];
                if (el.sValue) {
                    if (el.type === 'checkbox') {
                        // 多选值
                        result += el.sName
                        if (el.child === undefined && el.children === undefined) {
                            result += "+"
                        } else {
                            result += ","
                        }
                    } else if (el.type === 'radio') {
                        // 单选值
                        el.option.forEach(_ => {
                            if (_.sValue === el.sValue) {
                                result += _.sName + "+"
                            }
                        });
                    }
                    if (el.child) {
                        result += el.child.sValue + "+"
                    }
                }
                if (el.children) {
                    result += this.recursiveData(el.children)
                }
            }
            return result
        },
        getOperationType () {
            this.showProject = deepClone(window.configs.businessData.pathologyNew.option)
        },
        getOperationDataText () {
            let result = this.recursiveData(this.showProject)
            let le = result.length
            if (le) {
                result = result.substr(0, le - 1)
            }
            this.editLayer.form.sPathologyTypeText = result

            this.project.visible = false
        },
        // 点击表格赋值
        onClickRow (row,) {
            Object.keys(this.editLayer.form).forEach(key => {

                if (row[key] != undefined) {
                    if (key === 'sPathologyTypeJson') {
                        this.editLayer.form[key] = JSON.parse(row[key])
                    } else {
                        this.editLayer.form[key] = row[key]
                    }
                }
            })

            this.editLayer.form.sId = row.sId
            this.editLayer.form.iVersion = row.iVersion
            this.showProject = this.editLayer.form.sPathologyTypeJson
        },
        delItemData (param, index) {
            Api.delPathologys({
                sId: param.sId,
                iVersion: param.iVersion
            }).then((e) => {
                if (e.success) {
                    this.$message({
                        message: e.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData.splice(index, 1);
                    if (this.editLayer.form.sId == param.sId) {
                        this.onResetForm('refEditLayer')
                    }
                    this.getData()// 触发更新表格数据
                } else {
                    this.$message({
                        message: e.msg,
                        type: 'error',
                        duration: 3000
                    });
                }
            })
        },
        // mxDoRefresh 会回调这个方法--获取表格数据
        getData () {
            if(!this.sPatientId) {
                this.$message.warning('请选择患者数据！');
                return
            }
            this.loading = true;
            const params = {
                condition:{
                    sPatientId: this.sPatientId
                },
                page: {
                    pageCurrent: 1,
                    pageSize: 999,
                }
            }
            Api.getPathologysData(params).then((res) => {
                this.loading = false;
                if (res.success) {
                    this.tableData = res.data.recordList == null ? [] : res.data.recordList
                    this.$emit('getPathologicalNum', this.tableData.length);
                }
            }).catch(() => {
                this.loading = false;
            })
        },
        /**
         * 保存数据
         */
        saveData () {
            const params = Object.assign({}, this.editLayer.form, {
                sPathologyTypeJson: JSON.stringify(this.showProject),
                sPatientId: this.sPatientId
            })

            if (this.editLayer.form.sId) {
                Api.editPathologys(params).then(res => {
                    this.$emit('updateLoading')
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.onResetForm('refEditLayer')
                        this.getData()
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(()=>{
                    this.$emit('updateLoading')
                })
            } else {
                Api.addPathologys(params).then(res => {
                    this.$emit('updateLoading')
                    if (res.success) {
                        this.$message.success(res.msg);
                        this.onResetForm('refEditLayer')
                        this.getData()
                        return;
                    }
                    this.$message.error(res.msg);
                }).catch(()=>{
                    this.$emit('updateLoading')
                })
            }
        },
    },
    mounted () {
        this.getOperationType();
    }
}
</script>

<style lang="scss" scoped>
.c-content {
    min-height: 300px;
    max-height: 600px;
    overflow: auto;
}


.scope-pathological {
    height: 100%;
    display: flex;
    padding: 20px 5px 5px 5px;
    width: 100%;
    flex-direction: column;
    box-sizing: border-box;
    min-height: 350px;
    overflow: auto;
    .xx-el-scrollbar {
        .c-action {
            margin: 20px 0 10px 0;
        }
    }
    .c-flex-auto {
        flex: 1;
        height: 0;
        overflow: auto;
    }
    .c-page {
        background-color: #f9f9f9;
        text-align: right;
        height: 34px;
    }

   
}
:deep(.el-input-group__append) {
    padding: 0 20px;
}
:deep(.el-scrollbar__bar.is-horizontal) {
    height: 0;
}
.i-delete{
   cursor: pointer;  
}
</style>
