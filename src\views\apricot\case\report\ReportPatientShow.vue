<template>
      <el-dialog
        append-to-body
        title="可合并病历"
        v-model="visible"
        width="970px"
        @close="closeDialog"
        class="my-dialog my-padding-dialog">
        <div class="g-content">
            <div class="c-info">
                <el-row  :gutter="10">
                    <el-col :span="4"><div class="i-info">当前病例:<span>神经病</span></div></el-col>
                    <el-col :span="3"><div class="i-info">姓名:<span>小才子</span></div></el-col>
                    <el-col :span="2"><div class="i-info">性别:<span>男</span></div></el-col>
                    <el-col :span="2"><div class="i-info">年龄:<span>102</span></div></el-col>
                    <el-col :span="4"><div class="i-info">核医学号:<span>9527114</span></div></el-col>
                    <el-col :span="4"><div class="i-info">检查日期:<span>2019-11-12</span></div></el-col>
                    <el-col :span="5"><div class="i-info">检查项目:<span></span></div></el-col>
                </el-row>
            </div>
            <div class="c-group">
                <div class="c-item">
                    <div class="m-labelInput">
						<label>开始日期</label>
						<el-date-picker
                            v-model="filtrate.sAppointDayBegin"
                            type="date"
                            size="small"
                            style="width: 180px;"
                            value-format="YYYY-MM-DD"
                            format="yyyy 年 MM 月 dd 日"
                            placeholder="选择日期">
                        </el-date-picker>
					</div>
                    <div class="m-labelInput">
						<label>结束日期</label>
						<el-date-picker
                            v-model="filtrate.sAppointDayEnd"
                            size="small"
                            type="date"
                            style="width: 180px;"
                            value-format="YYYY-MM-DD"
                            format="yyyy 年 MM 月 dd 日"
                            placeholder="选择日期">
                        </el-date-picker>
					</div>
                    <div class="m-labelInput">
						<label>病历号</label>
                        <el-input size="small" style="width: 120px;" v-model="filtrate.accessNum"></el-input>
					</div>
                    <div class="m-labelInput">
						<label>姓名拼音</label>
                        <el-input size="small" style="width: 120px;" v-model="filtrate.accessNum"></el-input>
					</div>
                    <div class="c-action">
                        <el-button size="small" icon="el-icon-zoom-in" type="primary" @click="onClickSearch">查询(Q)</el-button>
                        <el-button size="small" icon="el-icon-setting" @click="onClickReset">重置(R)</el-button>
                    </div>
                </div>
            </div>
            <div class="c-box">
                <el-table
                    :data="tableData"
                    stripe
                    border
                    size="small"
                    height="400"
                    v-loading="loading"
                    highlight-current-row
                    @row-click="onClickRow"
                    style="width: 100%">
                    <el-table-column
                        align="center"
                        label="序号"
                        type="index"
                        width="50">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="患者姓名"
                        width="80">
                    </el-table-column>
                    <el-table-column
                        prop="spell"
                        label="拼音"
                        width="90">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="核医学号"
                        width="90">
                    </el-table-column>
                    <el-table-column
                        prop="sex"
                        label="性别"
                        align="center"
                        width="80">
                    </el-table-column>
                    <el-table-column
                        prop="age"
                        label="年龄"
                        align="center"
                        width="80">
                    </el-table-column>
                    <el-table-column
                        prop="date"
                        label="检查日期"
                        width="110">
                    </el-table-column>
                    <el-table-column
                        prop="time"
                        label="检查时间">
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <template #footer><div  class="my-dialog-footer">
            <div class="g-page-footer">
                <el-button size="small" icon="el-icon-news" @click="onClickMerge">合并(M)</el-button>
                <el-button size="small" icon="el-icon-error" @click="visible = false">关闭(X)</el-button>
            </div>
        </div></template>
      </el-dialog>
</template>
<script>
export default {
    name: 'ReportPatientShow',
	props: {
		dialogVisible: {
			type: Boolean,
			default: false
		}
    },
    data() {
        return {
            visible: false,
            loading: false,
            filtrate: {  // 筛选条件
                sAppointDayBegin: '',
                sAppointDayEnd: '',
                accessNum: ''
            }, 
            currentRow: {}, // 选择行
            tableData: [
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19',
                    time: '11:02'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19',
                    spell: 'guanliyuan',
                    time: '12:00'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19',
                    spell: 'guanliyuan'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                }
            ]
        }
    },
	watch: {
		dialogVisible(later){
			if (later) {
				this.visible = this.dialogVisible;
			}
		}
    },
    methods: {
        onClickSearch(){
            this.loading = true
            setTimeout(() => {
                this.loading = false
            }, 1000);
        },
        onClickReset(){
            this.filtrate = {
                sAppointDayBegin: '',
                sAppointDayEnd: '',
                accessNum: ''
            }
        },
        onClickRow(row){ // 选中行
            this.currentRow = row 
        },
        onClickMerge(){ // 点击合并
            if (!Object.keys(this.currentRow).length){
                this.$message.error('请选择一条数据！');
                return;
            }
            this.$message.info('合并');
        },
        closeDialog(){
			this.$emit('update:dialogVisible', false);
		},
    }
}
</script>
<style lang="scss" scoped>
    .g-content{
        .c-group{
            margin-bottom: 15px;
            pad: 10px 10px 0px;
        }
        .c-box{
            padding: 0px 10px 10px;
        }
        .c-info{
            height: 43px;
            line-height: 43px;
            border-bottom: 1px solid #C7D2D8;
            padding-left: 10px;
            font-size: 15px;
            color: black;
            span{
                font-size: 15px;
                color: #606266;
                padding-left: 5px;
            }
        }
        .c-action{
            padding-top: 14px;
            > button:first-child{
                margin-left: 10px;
            }
        }
    }
</style>
