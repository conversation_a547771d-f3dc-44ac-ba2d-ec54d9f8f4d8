import { tokenName } from '@/utils/base_config';

import Cookies from 'js-cookie';
export function getAccessToken(name = tokenName) {
  // console.log(name, Cookies.get(name))
  return Cookies.get(name);
}

export function setAccessToken(accessToken, expires = 14) {
  return Cookies.set(tokenName, accessToken, { expires });
}

export function removeAccessToken() {
  return Cookies.remove(tokenName);
}
