import case_module from './case_module'
import inject_module from './inject_module'
import machine_module from './machine_module' 
import report_module from './report_module'
import consult_module from './consult_module'
import appointment from './appointment'
import followupVisits from './followupVisits'
import remoteConsult_module from './remoteConsult_module'
import collectMng_module from './remoteConsult_module'

const state = {
}

const getters = {

}

const mutations = {
}

const actions = {

}

export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations,
    modules: {
        case_module,
        consult_module,
        inject_module,
        machine_module, 
        report_module,
        appointment,
        followupVisits,
        remoteConsult_module,
        collectMng_module
    }
}
