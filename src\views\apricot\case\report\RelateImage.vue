<template>
      <el-dialog
        append-to-body
        title="关联图像"
        v-model="visible"
        width="700px"
        @close="closeDialog"
        class="my-dialog">
        <div class="g-content">
            <div class="c-box">
                <el-table
                    :data="tableData"
                    size="small"
                    stripe
                    border
                    highlight-current-row
                    @row-click="onClickRow"
                    height="400"
                    style="width: 100%">
                    <el-table-column
                        align="center"
                        label="序号"
                        type="index"
                        width="50">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="姓名"
                        width="80">
                    </el-table-column>
                    <el-table-column
                        prop="sex"
                        label="性别"
                        align="center"
                        width="80">
                    </el-table-column>
                    <el-table-column
                        prop="age"
                        label="年龄"
                        align="center"
                        width="80">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="核医学号"
                        width="90">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="病历号"
                        width="90">
                    </el-table-column>
                    <el-table-column
                        prop="date"
                        label="检查日期"
                        width="90">
                    </el-table-column>
                    <el-table-column
                        prop="name"
                        label="检查设备">
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <template #footer><div  class="my-dialog-footer">
            <div class="g-page-footer">
                <el-button size="small" icon="el-icon-guide" @click="onClickMerge">合并(M)</el-button>
                <el-button size="small" icon="el-icon-error" @click="visible = false">关闭(X)</el-button>
            </div>
        </div></template>
      </el-dialog>
</template>
<script>
export default {
    name: 'dialog_RelateImage',
	props: {
		dialogVisible: {
			type: Boolean,
			default: false
		}
    },
    data() {
        return {
            visible: false,
            tableData: [
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                },
                {
                    name: '',
                    sex: '男',
                    age: '21',
                    date: '2019-11-19'
                }
            ]
        }
    },
	watch: {
		dialogVisible(later){
			if (later) {
				this.visible = this.dialogVisible;
			}
		}
    },
    methods: {
        onClickRow(row){
            console.log(row)
        },
        onClickMerge(){
            this.$message.info('合并');
        },
        closeDialog(){
			this.$emit('update:dialogVisible', false);
		},
    }
}
</script>
<style lang="scss" scoped>
</style>
