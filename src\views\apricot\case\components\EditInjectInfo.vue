<template>
    <!-- 注射信息弹窗 -->
    <el-dialog title="注射信息" destroy-on-close append-to-body v-model="visible" class="t-default my-dialog"
        center :close-on-click-modal="false" @close="$emit('closeDialog')">
        <div class="c-dialog-body" >
            <Inject v-if="visible && isLoadInjectComponent" 
                :patientInfo="patientInfo"
                :injectDate="{}"
                :isFormDatafromParent="false"
                :optionsLoc="optionsLoc"
                @updateTable="handleUpdate"
                @close="$emit('closeDialog')"></Inject>
        </div>
    </el-dialog>
</template>

<script>
import { caseEnum } from '$supersetResource/js/projects/apricot/enum.js'
import { mixinDictionaryGroup } from '$supersetResource/js/projects/apricot/index.js'
import { getAllUsersData } from '$supersetResource/js/projects/apricot/useHandlerSelect.js' // 获取全部用户

export default {
    name: 'EditInjectInfo', 
    mixins: [mixinDictionaryGroup],
    emits: ['closeDialog'],

    components: {
        Inject: defineAsyncComponent(() => import('./EditInjectInfoBox.vue'))
    },
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => new Object()
        },
    },
    data() {
        return {
            visible: false,
            isLoadInjectComponent: false,
            optionsLoc: {
                ApricotReportDrinkType: [],
                ApricotReportMedicineSource: [],
                ApricotReportInjectSite: [],
                ApricotReportDoseUnit: [],
                ApricotReportDrugDelivery: [],
                ApricotReportStain: [],
                DoctorOptions: []
            },
        }
    },
    computed: {
        userInfo () {
            let temp = this.$store.getters["user/userSystemInfo"];
            if (temp.__proto__.constructor === Object) {
                return temp;
            } else {
                return {};
            }
        },
    },
    watch: {
        async dialogVisible(val) {
            this.visible = val;
            if(val){
                await this.mxGetCodeTable('ApricotReportDoseUnit');  // 剂量单位
                await this.mxGetCodeTable('ApricotReportDrugDelivery');  // 给药方式
                await this.mxGetCodeTable('ApricotReportMedicineSource');  // 药物来源
                await this.mxGetCodeTable('ApricotReportStain');  // 污染情况
                await this.mxGetCodeTable('ApricotReportInjectSite');  // 注射部位
                await this.mxGetCodeTable('ApricotReportDrinkType');  // 饮水类型
                this.optionsLoc.DoctorOptions =  await getAllUsersData();
                this.isLoadInjectComponent = true;
            }
        },
    },
    methods: {
        handleUpdate() {
            this.$emit('updateData');
            this.$emit('closeDialog');
        },
        
    }
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog.is-fullscreen) {
    width: 60%;
    height: 60%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    overflow: auto;
}

:deep(.my-dialog .el-dialog__body) {
    height: calc(100% - 45px);

    .c-dialog-body {
        height: 100%;
        border: 1px solid #eee;
    }
}
</style>
