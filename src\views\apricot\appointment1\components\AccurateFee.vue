<template>
    <el-button-icon-fa v-if="settingData"
        class="btn-item"
        icon="el-icon-money"
        plain
        @click="onClick">百慧</el-button-icon-fa>
</template>
<script setup>

import { ElMessage } from 'element-plus'

import { getThirdLinkData } from '$supersetApi/projects/apricot/system/thirdLink.js'

import { useHandleThirdLinkItem } from '$supersetResource/js/projects/apricot/useHandlerThirdLink.js';

import { useStore } from 'vuex';

const props = defineProps({
    formData: {
        type: Object,
        default: ({})
    },
})

const settingData = ref(null);

const store = useStore();
const onClick = () => {
    const patientInfo_ = props.formData;
    const userInfo_ = store._state.data.user.userSystemInfo;
    const item = settingData.value;
    useHandleThirdLinkItem(item, patientInfo_, userInfo_)
}

const getAccurateFeeData = () => {
    const params = {
        page: { pageSize: 50 }, condition: { sLinkTypeCode: '1', sLinkTypeName: '分屏扩展程序' }
    }
    getThirdLinkData(params).then(res => {
        if (res.success) {
            let data = (res.data?.recordList || []).find(item => item.sLinkTypeName === '百慧');
            settingData.value = data;
            return
        }
    }).catch(err => {
        console.log(err)
    })
}
onMounted(() => {
    getAccurateFeeData()
})

</script>
<style lang="scss" scoped>
</style>
