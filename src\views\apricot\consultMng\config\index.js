export default {
    MngIndexPenalConfig: {
        leftLayoutWidth: '50%',
        tabConsultForm: true,
        tabHistoryReport: true,
        tabScannerData: true,
        tabClinicData: true,
        // rightTabPanels: ['ConsultForm', 'HistoryReport', 'ScannerData', 'ClinicData'],
        isHistoryPanelFirst: false,
        historyReportMatchConditions: {
            hasRecordNo: 0,
            hasNuclearNum: 0,
            hasIdNum: 0,
        },
        autoPrintType: 0, // 是否自动打印，0-否，1-是
        autoPrintClassifys: [], // 打印文件类型
        isShowConfigBtn: true, // 是否显示配置按钮
        isSearchFormCurrentWStation: false,  // 是否只查询当前工作站数据
        patientInfoBgColor: '#fff', // 患者信息元素背景颜色
        tabBgColor: '#fff', // 患者信息元素背景颜色
    },
    // 搜索条件
    searchStyle: [
        {
            sProp: 'dAppointmentTimeSt',
            sLabel: '预约开始',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1,
        },
        {
            sProp: 'dAppointmentTimeEd',
            sLabel: '预约结束',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1,
        },
        {
            sProp: 'sDistrictId',
            sLabel: '院区',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sName',
            sLabel: '姓名',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            iLayourValue: 4,
            sInputType: 'text',
        },
        { 
            prop: 'sVisitCard', 
            label: '就诊卡号',
            sInputType: 'text',
            iLayourValue: 4,
            isShow: false
        },
        { 
            prop: 'sMedicalCaseNO', 
            label: '病案号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sImageNo', 
            label: '影像号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sOutpatientNO', 
            label: '门诊号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sPhysicalExamNo', 
            label: '体检号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sVitisNo', 
            label: '就诊号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sCardNum', 
            label: '社保卡号', 
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        { 
            prop: 'sHealthCardNO', 
            label: '健康卡号',
            sInputType: 'text',
            iLayourValue: 4, 
            isShow: false
        },
        {
            sProp: 'sMachineryRoomId',
            sLabel: '机房',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'sProjectId',
            sLabel: '项目',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'dInjectTime',
            sLabel: '注射日期',
            iLayourValue: 4,
            sInputType: 'date-picker',
            iCustom: 1,
        },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sOptionProp: 'iIsRegisterOptions',
            sInputType: 'option',
            iLayourValue: 4,
            iCustom: 1,
        },
        {
            sProp: 'iIsConsult',
            sLabel: '问诊',
            sOptionProp: 'iIsConsultOptions',
            sInputType: 'option',
            iLayourValue: 4,
            iCustom: 1,
        },
    ],
    textLableConfig: [
        {
            prop: 'sName',
            label: '姓名'
        },
        {
            prop: 'sSexText',
            label: '性别'
        },
        {
            prop: 'sAge',
            label: '年龄'
        },
        {
            prop: 'sNuclearNum',
            label: '核医学号'
        },
        {
            prop: 'sMedicalRecordNO',
            label: '病历号'
        },
        {
            prop: 'sApplyDepartText',
            label: '申请科室'
        },
        {
            prop: 'sApplyPersonName',
            label: '申请医生'
        },
        {
            prop: 'dAppointmentTime',
            label: '预约日期'
        },
        {
            prop: 'fBloodSugar',
            label: '空腹血糖',
            'sUnit': 'mmol/L'
        },
        {
            prop: 'sDistrictName',
            label: '检查院区'
        },
        {
            prop: 'sRoomText',
            label: '检查类型'
        },
        {
            prop: 'sProjectName',
            label: '检查项目'
        },
        {
            prop: 'sPositionText',
            label: '检查部位'
        },
        {
            prop: 'sTestModeText',
            label: '检查方式'
        },
        {
            prop: 'sCheckIntent',
            label: '检查目的'
        },
        {
            prop: 'sMemo',
            label: '申请备注'
        },
        {
            prop: 'sNameSpell',
            label: '姓名拼音',
            isShow: false
        },
        {
            prop: 'fHeight',
            label: '身高（cm）',
            isShow: false
        },
        {
            prop: 'fWeight',
            label: '体重（kg）',
            isShow: false
        },
        {
            prop: 'sIdNum',
            label: '身份证号',
            isShow: false
        },
        {
            prop: 'dBirthday',
            label: '出生日期',
            isShow: false
        },
        {
            prop: 'sMachineryRoomText',
            label: '机房',
            isShow: false
        },
        {
            prop: 'sMachineStationName',
            label: '工作站',
            isShow: false
        },
        {
            prop: 'sOutpatientNO',
            label: '门诊号',
            isShow: false
        },
        {
            prop: 'sInHospitalNO',
            label: '住院号',
            isShow: false
        },
        {
            prop: 'sApplyNO',
            label: '申请单号',
            isShow: false
        },
        {
            prop: 'sOrderNO',
            label: '医嘱号',
            isShow: false
        },
        {
            prop: 'sVitisNo',
            label: '就诊号',
            isShow: false
        },
        {
            prop: 'sClinicalDiagnosis',
            label: '临床诊断',
            isShow: false
        },
        {
            prop: 'sMedicalHistory',
            label: '简要病史',
            isShow: false
        },
        {
            prop: 'sSourceText',
            label: '就诊类型',
            isShow: false
        },
        {
            prop: 'sEncounter',
            label: '就诊次数',
            isShow: false
        },
        {
            prop: 'sNuclideText',
            label: '核素',
            isShow: false
        },
        {
            prop: 'sNuclideSupName',
            label: '核素全称',
            isShow: false
        },
        {
            prop: 'sTracerText',
            label: '示踪剂',
            isShow: false
        },
        {
            prop: 'sTracerSupName',
            label: '示踪剂全称',
            isShow: false
        },
        {
            prop: 'fRecipeDose',
            label: '处方剂量',
            isShow: false
        },
        {
            prop: 'iIsPregnant',
            label: '怀孕',
            isShow: false
        },
        {
            prop: 'sPhone',
            label: '联系电话',
            isShow: false
        },
        {
            prop: 'sAddress',
            label: '住址',
            isShow: false
        },
        {
            prop: 'sMaritalStatusName',
            label: '婚姻状况',
            isShow: false
        },
        {
            prop: 'sMedicalCaseNO',
            label: '病案号',
            isShow: false
        },
        {
            prop: 'sImageNo',
            label: '影像号',
            isShow: false
        },
        {
            prop: 'sInpatientAreaText',
            label: '病区名称',
            isShow: false
        },
        {
            prop: 'sInpatientWardText',
            label: '病房名称',
            isShow: false
        },
        {
            prop: 'sBedNum',
            label: '床号',
            isShow: false
        },
        {
            prop: 'sVisitCard',
            label: '就诊卡号',
            isShow: false
        },
        {
            prop: 'sCardNum',
            label: '社保卡号',
            isShow: false
        },
        {
            prop: 'sHealthCardNO',
            label: '健康卡号',
            isShow: false
        },
        {
            prop: 'sPresentHistory',
            label: '现病史',
            isShow: false
        },
        {
            prop: 'sPastHistory',
            label: '既往史疾病',
            isShow: false
        },
        {
            prop: 'sClinicalSymptoms',
            label: '临床症状',
            isShow: false
        },
        {
            prop: 'sChiefComplaint',
            label: '主诉',
            isShow: false
        },
        {
            prop: 'sInvoiceNum',
            label: '发票号',
            isShow: false
        },
        {
            prop: 'sFeeTypeText',
            label: '费用类型',
            isShow: false
        },
        {
            prop: 'fFees',
            label: '费用',
            isShow: false
        },
        {
            prop: 'sChargeStateText',
            label: '收费状态',
            isShow: false
        },
        {
            prop: 'dApplyDate',
            label: '申请时间',
            isShow: false
        },
        {
            prop: 'sApplyPersonName',
            label: '申请医生',
            isShow: false
        },
        {
            prop: 'sChiefPhysicianName',
            label: '主治医生',
            isShow: false
        },
        {
            prop: 'sChiefPhysicianPhone',
            label: '医生电话',
            isShow: false
        },
        {
            prop: 'sPhysicalExamNo',
            label: '体检号',
            isShow: false
        }
    ],
    // 患者 table 表
    patientTable: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sMinWidth: '100px'
        },
        {
            sProp: 'sNameSpell',
            sLabel: '姓名拼音',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sMinWidth: '70px'
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sMinWidth: '70px'
        },
        {
            sProp: 'fHeight',
            sLabel: '身高（cm）',
            sAlign: 'center',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'fWeight',
            sLabel: '体重（kg）',
            sAlign: 'center',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsConsult',
            sLabel: '问诊',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsInject',
            sLabel: '注射',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsMachine',
            sLabel: '上机',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsImaging',
            sLabel: '收图',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsReport',
            sLabel: '报告',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'sRoomText',
            sLabel: '设备类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sMinWidth: '110px'
        },
        {
            sProp: 'sDistrictName',
            sLabel: '院区',
            sMinWidth: '110px'
        },
        {
            sProp: 'sMachineryRoomText',
            sLabel: '机房',
            sMinWidth: '110px'
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约日期',
            sMinWidth: '150px',
            isSortable: true,
            sOrder: 'ascending'
        },
        {
            sProp: 'dConsultTime',
            sLabel: '问诊时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'sConsultName',
            sLabel: '问诊医生',
            sMinWidth: '110px'
        },
        {
            sProp: 'dInjectTime',
            sLabel: '注射日期',
            sMinWidth: '150px'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sPatientIndex',
            sLabel: '患者索引',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sIdNum',
            sLabel: '身份证号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dBirthday',
            sLabel: '出生日期',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sVitisNo',
            sLabel: '就诊号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sDiagnosticOpinionText',
            sLabel: '诊断意见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sInspectSeeText',
            sLabel: '检查所见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sEncounter',
            sLabel: '就诊次数',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPositionText',
            sLabel: '检查部位',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTestModeText',
            sLabel: '检查方式',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideSupName',
            sLabel: '核素全称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTracerText',
            sLabel: '示踪剂',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'sTracerSupName',
            sLabel: '示踪剂全称',
            sMinWidth: '120px',
            iIsHide: true
        },
        {
            sProp: 'fRecipeDose',
            sLabel: '处方剂量',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'iIsPregnant',
            sLabel: '怀孕',
            sAlign: 'center',
            sMinWidth: '80px',
            iIsHide: true
        },
        {
            sProp: 'sPhone',
            sLabel: '联系电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sAddress',
            sLabel: '住址',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMaritalStatusName',
            sLabel: '婚姻状况',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sImageNo',
            sLabel: '影像号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientAreaText',
            sLabel: '病区名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientWardText',
            sLabel: '病房名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sBedNum',
            sLabel: '床号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sCardNum',
            sLabel: '社保卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fBloodSugar',
            sLabel: '检查空腹血糖',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineStationName',
            sLabel: '工作站',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPresentHistory',
            sLabel: '现病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sPastHistory',
            sLabel: '既往史疾病',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalSymptoms',
            sLabel: '临床症状',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefComplaint',
            sLabel: '主诉',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInvoiceNum',
            sLabel: '发票号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sFeeTypeText',
            sLabel: '费用类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fFees',
            sLabel: '费用',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChargeStateText',
            sLabel: '收费状态',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dApplyDate',
            sLabel: '申请时间',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianName',
            sLabel: '主治医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPhysicalExamNo',
            sLabel: '体检号',
            sMinWidth: '110px',
            iIsHide: true
        }
    ],

    historyTableConfig: [{
        sProp: 'sName',
        sLabel: '姓名',
        sAlign: 'left',
        sMinWidth: '110px'
    },
    {
        sProp: 'sSexText',
        sLabel: '性别',
        sAlign: 'left',
        sWidth: '80px'
    },
    {
        sProp: 'sAge',
        sLabel: '年龄',
        sAlign: 'center',
        sWidth: '80px'
    },
    {
        sProp: 'sSourceText',
        sLabel: '来源',
        sAlign: 'left',
        sMinWidth: '110px',
    },
    {
        sProp: 'sNuclearNum',
        sLabel: '核医学号',
        sAlign: 'left',
        sMinWidth: '110px',
    },
    {
        sProp: 'sMedicalRecordNO',
        sLabel: '病历号',
        sAlign: 'left',
        sMinWidth: '110px',
    },
    {
        sProp: 'dAppointmentTime',
        sLabel: '检查日期',
        sAlign: 'left',
        sMinWidth: '110px',
    },
    {
        sProp: 'sProjectName',
        sLabel: '检查项目',
        sAlign: 'left',
        sMinWidth: '110px',
    },
    {
        sProp: 'sPositionText',
        sLabel: '部位',
        sAlign: 'left',
        sMinWidth: '110px',
    }, 
    {
        sProp: 'sOutpatientNO',
        sLabel: '门诊号',
        sAlign: 'left',
        sMinWidth: '110px',
    },
    {
        sProp: 'sInHospitalNO',
        sLabel: '住院号',
        sAlign: 'left',
        sMinWidth: '110px',
    }],

    DA1: {
        type: 't-x',
        localStorageKey: '202306280906',
        panelConfig: [
            {
                size: 0,
                minSize: 0,
                name: 'c1',
                isFlexible: true
            },
            {
                size: window.innerWidth / 2,
                minSize: 588,
                maxSize: window.innerWidth / 4 * 3,
                name: 'c2',
                isFlexible: false
            },
        ],
    },
};
