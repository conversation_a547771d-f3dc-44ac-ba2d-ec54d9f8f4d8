<template>
    <div class=" my-preview" >
        <!-- <div class="pre-title">报告预览</div> -->
        <div class="patient-baseinfo">
            <div class="label-box">
                <span class="label-title">姓名：</span>
                <span class="label-value bold" >{{patient.sName}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">性别：</span>
                <span class="label-value">{{patient.sSexText}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">年龄:</span>
                <span class="label-value">{{patient.sAge}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">病历号：</span>
                <span class="label-value">{{patient.sMedicalRecordNO}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">核医学号：</span>
                <span class="label-value">{{patient.sNuclearNum}}</span>
            </div> 
            <div class="label-box">
                <span class="label-title">检查日期：</span>
                <span class="label-value">{{ timeFormate(patient.sImgStudyDate || patient.dInjectTime || patient.dMachineTime) }}</span>
            </div>
            <div class="label-box u-width">
                <span class="label-title">检查项目：</span>
                <span class="label-value">{{patient.sProjectName}}</span>
            </div>
        </div>
        <div class="main-content"
            >
            <DragAdjust :dragAdjustData="DA0" >
                <template v-slot:c-top >
                    <div class="examination-see c-sinspect">
                        <div class="c-title">检查所见</div>
                        <div class="c-centent" v-loading="loading" :style="{paddingTop: reportCase.sInspectSee==='报告未书写！' ? '15px':'',backgroundColor: bgColor }"
                            v-html="reportCase.sInspectSee"></div>
                    </div>
                </template>
                <template v-slot:c-buttom >
                    <div class="examination-see c-option">
                        <div class="c-title">诊断意见</div>
                        <div class="c-centent" v-loading="loading" :style="{ backgroundColor: bgColor}"
                            v-html="reportCase.sDiagnosticOpinion"></div>
                    </div>
                </template>
            </DragAdjust>
           
            

        </div>
        <div class="patient-baseinfo">
            <div class="label-box">
                <span class="label-title">报告医生：</span>
                <span class="label-value">{{reportCase.sReporterName}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">报告日期：</span>
                <span class="label-value">{{reportCase.dReporterTime}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">审核医生：</span>
                <span class="label-value">{{reportCase.sExamineName}}</span>
            </div>
            <div class="label-box">
                <span class="label-title">审核日期：</span>
                <span class="label-value">{{reportCase.dExamineDate}}</span>
            </div>
        </div>
    </div>
</template>
<script>
import { getPatientReportById } from '$supersetApi/projects/apricot/case/index.js'
export default {
    name: 'Preview',
    props: {
        patient: new Object(),
        height: null
    },
    data () {
        return {
            sPatientId: this.patient.sId,
            loading: false,
            reportCase: {},
            DA0: {
                type: 't-y',
                localStorageKey: '202308021710',
                panelConfig: [{
                    size: 400,
                    minSize: 50,
                    name: "c-top",
                    isFlexible: false
                },
                {
                    minSize: 0,
                    name: "c-buttom",
                    isFlexible: true

                }]
            },
            bgColor: localStorage.getItem('reportTheme')?localStorage.getItem('reportTheme') : 'rgba(206, 231, 209, 1)'
        }
    },
    watch: {
        patient: {
            handler (val) {
                if(!val.sId) {
                   this.reportCase = {} 
                }
                this.sPatientId = val.sId
                this.handleGetReport()
            },
            deep: true,
        }
    },
    methods: {
      moment: window.moment,
        handleGetReport () {
            if (!this.sPatientId) {
                // this.$message.warning('请选中患者数据！');
                return
            }
            this.loading = true;
            let params = {
                sPatientId: this.sPatientId
            }
            getPatientReportById(params).then(res => {
                this.loading = false;
                if (res.success) {
                    this.reportCase = res.data
                    this.reportCase.dReporterTime = this.timeFormate(this.reportCase.dReporterTime);
                    this.reportCase.dExamineDate = this.timeFormate(this.reportCase.dExamineDate);
                    return
                }
                this.$message.error(res.msg);
            }).catch(err => {
                console.log(err);
                this.loading = false
            })
        },
        timeFormate (timeStamp) {
            return timeStamp > 0 ? this.moment(timeStamp).format('YYYY-MM-DD') : '-'
        },

    },
    mounted () {
        this.handleGetReport()

    }

}
</script>
<style lang="scss" scoped>

.main-content {
  position: relative;
  height: calc(100% - 80px);
  border-top: var(--el-border);
  border-bottom: var(--el-border);
}
.m-flexLaout-ty > .main-content {
    overflow: hidden;
}
.my-preview {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    min-width: 680px;
    height: calc(100% - 1px);
    box-sizing: border-box;
    /* background: blanchedalmond; #fbf5e9 */
    background-color:var(--el-color-primary-light-9);
    box-shadow: -1px -1px 6px #d8d8d8;
    border-bottom: var(--el-border);
    border-left: var(--el-border);
}

.patient-baseinfo {
    position: relative;
    width: 100%;
    padding: 10px 0px; 
    padding-left: 32px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    // margin-top: 15px;
    
}
.patient-baseinfo .bold {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-color-primary);
}

.label-box {
  position: relative;
  display: flex;
  width: 23%;
  padding-right: 2%;
  align-items: baseline;
  min-height: 24px;
    &.u-width {
        width: 45%;
    }
  // padding-bottom: 6px;

}

.label-title {
    position: relative;
    display: inline-block;
    width: 75px;
    color: rgb(103, 112, 126);
}
.label-value { 
  position: relative;
  display: inline-block;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pre-title {
    font-size: 15px;
    font-weight: 600;
    color: #606266;
    padding: 5px 0 10px 0;
}
.main-content .examination-see {
    display: flex;
    height: 100%;
    align-items: center;

    * {
        white-space: break-spaces;
        word-break: break-word;
    }
}
.main-content .examination-see p{
    margin:0px 0px 10px 0px
    
}
.main-content .examination-see .c-title {
    width: 25px;
    font-weight: 600;
    padding-left: 10px;
}


.main-content .examination-see .c-centent {
    height: 100%;
    overflow: auto;
    background-color: rgba(255, 255, 255, 0.75);
    padding: 0px 15px;
    width: 100%;
    box-sizing: border-box;
    background-color: rgba(206, 231, 209, 1);
    /* background-color: #eee; */
}
</style>
