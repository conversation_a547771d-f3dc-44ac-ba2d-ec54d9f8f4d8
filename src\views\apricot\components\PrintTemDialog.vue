<template>
    <el-dialog append-to-body
        v-model="visible"
        :title="printDialogList.length ? `选择${getName(printDialogList[0].iClassify)}模板` : '选择模板'"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        class="my-dialog"
        width="500px"
        @close="closeDialog">
        <div v-if="printDialogList.length" 
            class="c-btns">
            <div v-for="(item, index) in printDialogList[0].templateList"
                :key="index"
                style="margin-top: 20px;">
                <el-button size="large"
                    @click="onPrintClick(item)">{{ item.sTemplateName }}</el-button>
            </div>
        </div>
    </el-dialog>
</template>

<script>
import { templateEnum }  from '$supersetResource/js/projects/apricot/enum.js'
export default {
    props: {
        dialogVisible: {
            type: Boolean,
            default: false
        },
    },
    data () {
        return {
            visible: false,
            printDialogList: [],
            selectedTemplateId: '',
        }
    },
    watch: {
        dialogVisible(val) {
            this.visible = val;
            if(val) {
                // console.log(this.$parent);
                let $parent = this.$parent.$parent;
                this.printDialogList = $parent.printDialogList;
                this.selectedTemplateId = ''
            }
        }
    },
    methods: {
        onPrintClick (item) {
            let $parent = this.$parent.$parent;
            $parent.mxDialogPrintClick(item)
        },
        closeDialog () {
            let $parent = this.$parent.$parent;
            $parent.printDialogList.shift();
            $parent.visibleTemplate = false;
        },
        // 
        getName (val) {
            let classifyOptions = templateEnum.classifyOptions
            let item = classifyOptions.find(item => item.sValue == val);
            return item ? item.sName : null;
        },
    }
}
</script>

<style lang="scss" scoped>
.c-btns {
    min-height: 150px;
    margin: 15px;
    padding: 15px;
    background: var(--el-color-primary-light-8);
    .el-button {
        width: 100%; 
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        /*超出显示省略号*/
        white-space: nowrap;
        /*强制单行显示*/
    }
}
</style>
