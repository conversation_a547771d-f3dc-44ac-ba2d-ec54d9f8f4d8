import request from '$supersetUtils/request'
import {
    baseURL, stringify
} from '$supersetUtils/request'

export default {
    // 预约患者信息表操作接口 : Patient Info Controller
    // 患者列表数据
    getData(data) {
        return request({
            url: baseURL.apricot + '/patient/info/find/patients',
            method: 'POST',
            data
        })
    },

    // 修改预约时间
    patientInfoChangeTime(data) {
        return request({
            url: baseURL.apricot + '/patient/info/change/time',
            method: 'POST',
            data
        })
    },


    // 预约检查过程记录表操作接口 : Project And Patient Controller
    // 患者签到
    patientAppointRegister,

    // 患者取消签到
    patientCancelAppointRegister,

    // 患者取消检查
    patientAppointCancellation(params) {
        return request({
            url: baseURL.apricot + '/project/and/patient/cancellations',
            method: 'POST',
            params
        })
    },


    // 项目启用方案操作接口 : Plan And Project Controller
    // 获取项目的预约时间段以及时间段内的预约结果
    planAndProjectTime(params) {
        return request({
            url: baseURL.apricot + '/plan/and/project/project/time',
            method: 'POST',
            params
        })
    },
    // 批量报告审核
    reportMultiAuditing(data) {
        return request({
            url: baseURL.apricot + '/project/and/patient/report/multi/auditing',
            method: 'POST',
            data
        })
    },
    // 批量发布报告
    reportMultiRelease(data) {
        return request({
            url: baseURL.apricot + '/project/and/patient/report/release/multi',
            method: 'POST',
            data
        })
    },
    // 获取未审核报告数量
    getAuditCount(params) {
        return request({
            url: baseURL.apricot + '/report/count/unaudit',
            method: 'POST',
            params
        })
    },
    // 批量发送会诊
    batchSendConsultation(data) {
        return request({
            url: baseURL.apricot + '/consultation/batchSendConsultation',
            method: 'POST',
            data
        })
    },
    // 批量取消会诊
    batchCancelConsultation(data) {
        return request({
            url: baseURL.apricot + '/consultation/batchCancelSendConsultation',
            method: 'POST',
            data
        })
    },
    getReportMngData,
    getReportMngRow,
    appointmentMachinery,
    patientExport,
    exportTable,
    setAuditReport,
    delPatient,
    restorePatient,
    deledList,
    setSecretPatients,
    removeSecretPatients,
    getUserSecretRights,
    getReportDoctors,
    saveUserSecretRights,
    cancelUserSecretRights
}

// 统计机房1天的预约情况
export function appointmentMachinery(params) {
    return request({
        url: baseURL.apricot + '/project/and/patient/appointment/collect/machinery',
        method: 'POST',
        params
    })
}

// 患者签到
export function patientAppointRegister(params) {
    return request({
        url: baseURL.apricot + '/project/and/patient/appoint/register',
        method: 'POST',
        params
    })
}
// 患者取消签到
export function patientCancelAppointRegister(params) {
    return request({
        url: baseURL.apricot + '/project/and/patient/appoint/register/cancel',
        method: 'POST',
        params
    })
}

export function getConsultData(data) {
    // 问诊模块患者列表数据
    return request({
        url: baseURL.apricot + '/briefly/illness/find/patients',
        method: 'POST',
        data
    })
}
export function getConsultRow(params) {
    // 问诊模块患者列表数据
    return request({
        url: baseURL.apricot + '/briefly/illness/find/patients/row',
        method: 'POST',
        params
    })
}
export function getInjectionData(data) {
    // 注射模块患者列表数据
    return request({
        url: baseURL.apricot + '/injection/info/find/patients',
        method: 'POST',
        data
    })
}
export function getInjectionRow(params) {
    // 注射模块患者列表数据
    return request({
        url: baseURL.apricot + '/injection/info/find/patients/row',
        method: 'POST',
        params
    })
}

export function getOperateComputerData(data) {
    // 上机模块患者列表数据
    return request({
        url: baseURL.apricot + '/operate/computer/find/patients',
        method: 'POST',
        data
    })
}
export function getOperateComputerRow(params) {
    // 上机模块患者列表数据
    return request({
        url: baseURL.apricot + '/operate/computer/find/patients/row',
        method: 'POST',
        params
    })
}

export function getReportMngData(data) {
    // 报告模块患者列表数据
    return request({
        url: baseURL.apricot + '/report/find/patients',
        method: 'POST',
        data
    })
}
export function getReportMngRow(params) {
    // 报告模块患者列表数据
    return request({
        url: baseURL.apricot + '/report/find/patients/row',
        method: 'POST',
        params
    })
}

export function getInternReportData(data) {
    // 教学病例模块患者列表数据
    return request({
        url: baseURL.apricot + '/intern/report/find/patients',
        method: 'POST',
        data
    })
}

export function getInternReportRow(params) {
    // 教学病例模块患者列表数据
    return request({
        url: baseURL.apricot + '/intern/report/find/patients/row',
        method: 'POST',
        params
    })
}
export function exportFollowupTable(data) {
    // 随访导出
    return request({
        url: baseURL.apricot + '/follow/up/export/excel',
        method: 'POST',
        data
    })
}

export function getFollowUpData(data) {
    // 随访模块患者列表数据
    return request({
        url: baseURL.apricot + '/follow/up/find/patients',
        method: 'POST',
        data
    })
}

export function getFollowUpCount(data) {
    // 随访模块随访总数
    return request({
        url: baseURL.apricot + '/follow/up/query/patients/followup/count',
        method: 'POST',
        data
    })
}

export function getFollowUpRow(params) {
    // 随访模块患者列表数据
    return request({
        url: baseURL.apricot + '/follow/up/find/patients/row',
        method: 'POST',
        params
    })
}
export function getCollectMngData(data) {
    // 收藏管理患者列表数据
    return request({
        url: baseURL.apricot + '/collect/find/patients',
        method: 'POST',
        data
    })
}
export function getCollectMngRow(params) {
    // 收藏管理患者列表数据
    return request({
        url: baseURL.apricot + '/collect/find/patients/row',
        method: 'POST',
        params
    })
}

// 查询导出
export function patientExport(data) {
    return request({
        url: baseURL.apricot + '/patient/info/export/data',
        method: 'POST',
        data
    })
}

// 导出数据到EXCEL
export function exportTable(data) {
    return request({
        url: baseURL.apricot + '/export/template/export',
        method: 'POST',
        data
    })
}

export function exportReportTable(data) {
    return request({
        url: baseURL.apricot + '/report/export/excel',
        method: 'POST',
        data
    })
}

export function exportResearchTable(data) {
    return request({
        url: baseURL.apricot + '/research/controller/export/excel',
        method: 'POST',
        data
    })
}

export function setAuditReport(params) {
    // 逐个批量审批报告
    return request({
        url: baseURL.apricot + '/process/auditReport',
        method: 'POST',
        params
    })
}

 // 批量删除病历
export function  delPatient(data) {
    return request({
        url: baseURL.apricot + '/patient/info/batch/remove',
        method: 'post',
        data
    })
}
// 删除的列表
export function  deledList(data) {
    return request({
        url: baseURL.apricot + '/patient/info/bak/list',
        method: 'post',
        data
    })
}
// 还原病历 
export function  restorePatient(params) {
    return request({
        url: baseURL.apricot + '/patient/info/bak/restore',
        method: 'post',
        params
    })
}

// 设为保密病例
export function setSecretPatients(data) {
  return request({
      url: baseURL.apricot + '/secret/set/classified',
      method: 'post',
      data
  })
}
// 解除保密病例
export function removeSecretPatients(data) {
  return request({
      url: baseURL.apricot + '/secret/set/unclassified',
      method: 'post',
      data
  })
}
// 保密病例list
export function secretPatientsList(data) {
  return request({
      url: baseURL.apricot + '/secret/query/secret/patient/list',
      method: 'post',
      data
  })
}
// 当前登录医生是否有保密病例权限
export function getUserSecretRights(params) {
  return request({
      url: baseURL.apricot + '/secret/validate/secret/right',
      method: 'post',
      params
  })
}
// 获取报告医生及以上级别医生
export function getReportDoctors(params) {
  return request({
      url: baseURL.apricot + '/secret/getReporterList',
      method: 'post',
      params
  })
}
// 保存医生保密权限
export function saveUserSecretRights(params) {
  return request({
      url: baseURL.apricot + '/secret/user/secret/right/auth',
      method: 'post',
      data:stringify(params)
  })
}
// 取消医生保密权限
export function  cancelUserSecretRights(params) {
  return request({
      url: baseURL.apricot + '/secret/user/secret/right/cancel',
      method: 'post',
      data:stringify(params)
  })
}
