<template>
  <div class="container ">
    <div class="main" :class="[showMore ? 'showMore' : '']"
      :style="{ '--custom-height': `${showedRowsNum * rowHeight}px` }">
      <div v-for="item in showingContent" :key="item.prop" class="cell-box" :style="{ width: item.width + '%' }">
        <div class="cell-label" :style="{
          width: item.labelWidth
        }">
          <span class="label-span">{{ item.label }}：</span>
        </div>
        <div class="cell-content">
          <template v-if="!!item.iCustom">
            <slot :name="item.prop" :data="item"></slot>
          </template>
          <template v-else-if="item.sInputType === 'option'">
            <!-- 下拉 -->
            <el-select v-model="modelValue[item.prop]" @change="onChangeInput($event, item.prop, item.sInputType)"
              class="item-content" :clearable="item.clearable !== undefined ? item.clearable : defaultItem.clearable"
              :placeholder="item.placeholder">
              <template v-if="Array.isArray(optionData[item.sOptionProp || item.prop])">
                <el-option label="全部" v-if="!optionData[item.sOptionProp || item.prop].find(i => i.sName === '全部')"
                  :key="''" :value="''"></el-option>
                <!-- 数组下拉 -->
                <el-option v-for="option in optionData[item.sOptionProp || item.prop]"
                  :multiple="item.isMultiSelect || defaultItem.isMultiSelect"
                  :key="option[item.optionValue || defaultItem.optionValue]"
                  :label="option[item.optionLabel || defaultItem.optionLabel]"
                  :value="option[item.optionValue || defaultItem.optionValue]" />
              </template>
              <template v-else>
                <!-- 对象下拉 -->
                <el-option v-for="(val, key) in optionData[item.sOptionProp || item.prop]" :key="key" :label="val"
                  :value="key" />
              </template>

            </el-select>
          </template>
          <template v-else-if="item.sInputType === 'date-picker'">
            <!-- 时间 -->
            <el-date-picker v-model="modelValue[item.prop]" class="item-content"
              @change="onChangeInput($event, item.prop, item.sInputType)" :type="item.dateType || defaultItem.pickerType"
              :placeholder="item.placeholder" :value-format="item.dateValueFormat || defaultItem.dateValueFormat"
              :clearable="item.clearable !== undefined ? item.clearable : defaultItem.clearable" />
          </template>
          <template v-else>
            <!-- 输入框 -->
            <el-input v-model="modelValue[item.prop]" @keyup.enter="onClickSearch"
              @change="onChangeInput($event, item.prop, item.sInputType)" class="item-content"
              :clearable="item.clearable !== undefined ? item.clearable : defaultItem.clearable"
              :placeholder="item.placeholder"></el-input>
          </template>
        </div>
      </div>
    </div>

    <div class="right-part" :style="{'--not-collapsed-height': notCollapsedHeight }">
      <div class="toolbar">

        <!-- <el-popover :width="120" placement="left-start" popper-class="m-popover" trigger="click">
          <div class="flex w-20 items-center">
            <el-icon v-if="configBtn" ref="buttonRef" :size="22" class="icon setting">
              <Setting />
            </el-icon>
            <el-icon :size="22" class="icon reset" @click="onClickReset">
              <RefreshRight />
            </el-icon>
            <el-icon :size="22" class="icon reset" @click="onClickSearch">
              <Search />
            </el-icon>
          </div>
          <template #reference>
            
          </template>
        </el-popover> -->

        <el-popover v-if="configBtn" ref="popoverRef" trigger="click" placement="right-end" width="auto" :teleported="true"
          transition="none" :hide-after="0" @after-enter="onPopAfterEnter">
          <template #default>
            <div class="pop-header">
              <h3>{{ ('查询条件配置') }}</h3>
            </div>
            <div class="h-12">
              <FormItem label="默认显示行数" labelWidth="120px">
                <el-select v-model.number="showedRowsNum" @change="onChangeShowedRowsNum" :teleported="false">
                  <el-option v-for="i in 10" :label="i" :value="i"></el-option>
                </el-select>
              </FormItem>
            </div>
            <!-- el-skeleton 的 width=表头宽度 -->
            <div v-if="!isRenderPopover" style="width: 400px; height:450px; overflow: hidden;">
                <el-skeleton :rows="12" />
            </div>
            <el-table v-else class="pop-table" :data="react.tableData" row-key="prop" ref="popTableRef" height="450">
              <el-table-column prop="label" :label="('排序')" align="center" width="60">
                <el-icon class="drag-icon" :size="18">
                  <Rank />
                </el-icon>
              </el-table-column>
              <el-table-column prop="label" :label="('显示')" align="left" width="80">
                <template #header="scope">
                  <div class=" flex items-center justify-start">
                    <el-checkbox v-model="selectAllVal"></el-checkbox>
                  </div>
                </template>
                <template #default="scope">
                  <el-checkbox v-model="scope.row.isShow" @change="onChangeItemProperty(scope, 'isShow')"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="label" :label="('字段名称')" width="120" align="left">
                <template #default="scope">
                  {{ scope.row.label }}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="align" :label="('对齐方式')" align="center" width="130">
            <template #default="scope">
              <el-radio-group class="radio-group" @change="onChangeItemProperty(scope, 'align')"
                v-model="scope.row.align">
                <el-radio-button label="left">左</el-radio-button>
                <el-radio-button label="center">中</el-radio-button>
                <el-radio-button label="right">右</el-radio-button>
              </el-radio-group>
            </template>
          </el-table-column> -->
              <el-table-column prop="width" :label="('')" width="140" align="center">
                <template #header="scope">
                  <div class=" flex items-center justify-between">
                    宽度(%)
                    <span class="ml-2 w-8">
                      <el-dropdown :teleported="false">
                        <span class=" cursor-pointer" title="统一设置">
                          <el-icon :size="22" :class="['icon', showMore ? 'showMore' : '']">
                            <ArrowDown />
                          </el-icon>
                        </span>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item v-for="i in widthOptions" :label="i" :value="i" @click="setAllWidth = i">
                              全部设为{{ i }}%
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </span>
                  </div>
                </template>
                <template #default="scope">
                  <!-- <el-autocomplete v-model="scope.row.width" :fetch-suggestions="querySearchForWidth" :trigger-on-focus="true"
                clearable class="inline-input " placeholder=" "
                @change="onChangeItemProperty(scope, 'width')" size="default" /> -->

                  <el-select v-model="scope.row.width" allow-create filterable :teleported="false"
                    @change="onChangeItemProperty(scope, 'width')">
                    <el-option v-for="i in widthOptions" :label="i" :value="i"></el-option>
                  </el-select>

                </template>
              </el-table-column>


            </el-table>
            <div class="flex items-center justify-between pt-2">
              <span class="flex">
                <el-button type="primary" @click="setStorage(1)">{{ ('保存到全局') }}</el-button>
                <el-popconfirm :confirm-button-text="('确定')" :cancel-button-text="('取消')" :title="('是否恢复默认设置？')"
                  :teleported="false" @confirm="onClickResetConfig">
                  <template #reference>
                    <el-button type="primary" plain>{{ ('恢复默认设置') }}</el-button>
                  </template>
                </el-popconfirm>
              </span>

              <span class="flex">
                <el-button type="default" @click="onClickClose">{{ ('关闭') }}</el-button>
              </span>

            </div>
          </template>
          <template #reference>
            <el-icon v-if="configBtn" ref="buttonRef" :size="18" class="icon " @click="onClickShowPop">
              <MoreFilled />
            </el-icon>
          </template>
        </el-popover>



        <el-icon :size="18" :class="['icon', showMore ? 'showMore' : '']" @click="onClickShowMore">
          <ArrowDown />
        </el-icon>


        <!-- <el-button-icon-fa icon="fa fa-search" class="button " type="primary" @click="onClickSearch">
            <span>{{ ('查询') }}</span>
          </el-button-icon-fa> -->
      </div>

    </div>


  </div>
</template>
<script>
import draggable from 'vuedraggable';
import Sortable from 'sortablejs'
import { ArrowDown, RefreshRight, Search, Rank, Setting, MoreFilled } from '@element-plus/icons-vue'
import { cloneDeep, isArray, pickBy } from 'lodash-es';
import { useStore } from 'vuex';

import { getOnlineConfig, saveOnlineConfig, compareAndModifyArrays } from '@/utils'

const defaultWidth = '25'
const defaultlabelWidth = '86px'


const defaultStorageData = {
  list: [],
  showedRowsNum: 1
}

export default {
  name: "SearchList",
  components: {
    draggable,
    ArrowDown, RefreshRight, Search, Rank, Setting, MoreFilled
  },
  emits: ['reset', 'changeSearch', 'clickSearch'],

  props: {
    modelValue: {
      type: Object,
      default: () => {
        return {}
      }
    },
    storageKey: {
      type: String,
      default: "", // 不填不启用localstorage
    },
    iModuleId: {
      default: "", // 不填不启用数据上传
    },
    configBtn: {
      type: [Object, Boolean],
      default: () => true
    },
    list: {  // 自定义数据
      type: Array,
      default: [], //  
    },
    optionData: {
      type: Object,
      default: () => ({})
    },
    defaultCondition: {
      type: Object,
      default: () => ({})
    },

    labelWidth: {
      default: "",
    },

    notCollapsedHeight: { // 默认显示高度
      default: '32px',
    },

    minRowsNum: { // 至少显示几行
      default: 1,
    },

  },
  setup(props) {
    const _store = useStore()

    const buttonRef = ref();
    const popoverRef = ref();
    const mainRef = ref();
    const react = ref({
      configItemsList: [],
      tableData: [],

    });


    const configButtonRef = computed(() => {
      if (props.configBtn && props.configBtn.ref) {
        return props.configBtn
      }
      return buttonRef
    });

    const userNo = computed(() => {
      let userInfo = _store.getters["user/userSystemInfo"] || {};
      return userInfo.sId
    })

    return {
      userNo,
      react,

      buttonRef,
      configButtonRef,
      popoverRef,
      mainRef,
      showMore: ref(false),
      visible: ref(false),
      isRenderPopover: ref(false),
      showedRowsNum: ref(Math.max(1, props.minRowsNum)),
      rowHeight: ref(36),
      getOnlineConfig,
      saveOnlineConfig,
      // 默认值
      defaultItem: {
        width: '25',
        isMultiSelect: false,
        clearable: true,
        placeholder: '',
        dateValueFormat: 'YYYY-MM-DD HH:mm:ss',
        dateType: 'date',
        optionLabel: 'sName',            //  
        optionValue: 'sValue',            //  
      },
      widthOptions: [12.5, 14.2, 16.6, 20, 25, 33.3, 50, 66.6, 75, 80, 100],
    };
  },

  computed: {
    showingContent() {
      return this.react.tableData.filter((i) => i.isShow)
    },
    selectAllVal: {
      get() {
        return this.showingContent.length === this.react.tableData.length
      },
      set(val) {
        const table = this.react.tableData;
        table.forEach(i => {
          i.isShow = !!val
        })
        this.setStorage();
      }
    },
    setAllWidth: {
      get() {
        return ''
      },
      set(val) {
        const table = this.react.tableData;
        table.forEach(i => {
          i.width = val
        })
        this.setStorage();
      }
    },
  },
  methods: {
    onChangeItemProperty(scope, key) {
      const index = scope.$index;
      const table = this.react.tableData[index];
      table[key] = scope.row[key];

      this.setStorage();
    },
    // 获取本地缓存
    async getStorage() {
      if (!this.storageKey) return;
      let storageStr = localStorage.getItem('SearchList-' + this.storageKey);
      let storageObj = {
        ...defaultStorageData
      }


      if (this.iModuleId && this.storageKey) {
        await this.getOnlineConfig()
        Object.assign(storageObj, 
        this.$store.getters['user/personalOnlineStorage'][this.iModuleId][this.storageKey])
      } else {
        try {
          const parsed = JSON.parse(storageStr)
          if (Array.isArray(parsed)) {
            storageObj.list = parsed
          } else if (parsed && parsed.list) {
            storageObj = parsed
          }
        } catch (error) {
          console.error(error)
        }
      }

      let storageList = storageObj.list || []

      const propList = storageList.map((i) => i.prop);
      const inputList = this.getInputList();
      let newList = compareAndModifyArrays(inputList, storageList);
    //   let newList = []

    //   if (propList.length !== inputList.length) {
    //     // 如果表格加了新的一列数据，恢复成默认顺序
    //     newList = inputList
    //   } else {
    //     // newList // 按localstor排序后的slot列表
    //     propList.forEach(prop => {
    //       const index = inputList.findIndex(slot => slot.prop === prop)
    //       if (index > -1) {
    //         newList.push(inputList.splice(index, 1)[0])
    //       }
    //     })
    //     newList = [...inputList].concat(newList)
    //   }

      this.react.configItemsList = newList

      // 将store存贮的属性值还原到当前数据
      let index
      let newTable = cloneDeep(newList).map((item) => {
        index = storageList.findIndex(target => item.prop === target.prop)
        if (index > -1) {
          return this.dataObjConvert({ ...item, ...storageList[index] })
        }
        return this.dataObjConvert(item)
      });

      this.react.tableData = newTable
      this.showedRowsNum = Math.max(storageObj.showedRowsNum, this.minRowsNum)

    },
    // 设置缓存
    setStorage(isGlobal = 0) {
      if (!this.storageKey) return;

      const storageObj = {
        list: this.react.tableData,
        showedRowsNum: this.showedRowsNum,
      }

      const storageString = JSON.stringify(storageObj)

      localStorage.setItem(
        "SearchList-" + this.storageKey,
        storageString
      );
      this.saveOnlineConfig(storageObj, isGlobal)
    },
    getInputList() {
      const inputList = isArray(this.list) ? this.list : []
      return inputList.map(i => ({
        ...i,
        prop: i.sProp || i.prop,
        label: i.sLabel || i.label,
        width: i.width || (i.iLayourValue ? `${(i.iLayourValue / 12 * 100).toFixed(2).slice(0, -1)}` : '') || '',
      }))
        .filter(item => item.prop)
        .filter(item => !item.iIsHide)


    },
    setDefaultData() {
      this.react.configItemsList = this.getInputList()
      this.react.tableData = this.react.configItemsList.map(this.dataObjConvert);
      this.showedRowsNum = Math.max(1, this.minRowsNum)
    },
    dataObjConvert(item) {
      return {
        ...item,
        prop: item.prop || "",
        label: item.label || " ",
        isShow: item.isShow === false ? false : true,
        align: item.align || "left",
        width: String(item.width || defaultWidth).replace(/\%/g, ''),
        labelWidth: item.labelWidth || this.labelWidth || defaultlabelWidth,
      }
    },
    // 点击重置
    onClickResetConfig() {
      this.setDefaultData()
      this.setStorage();
    },
    onChangeShowedRowsNum(val) {
      this.setStorage()
    },
    onClickClose() {
      this.$el.click()
    },
    onPopAfterEnter() {
      this.isRenderPopover = true;
      this.$nextTick(() => {
        this.rowDrop()
      })
    },
    //行拖拽
    rowDrop() {
      const popTable = (this.$refs.popTableRef.$el)
      const tbody = popTable.querySelector('tbody')
      Sortable.create(tbody, {
        disabled: false, // 是否开启拖拽
        handle: ".drag-icon",
        // ghostClass: 'sortable-ghost', //拖拽样式
        animation: 150, // 拖拽延时，效果更好看
        group: { // 是否开启跨表拖拽
          pull: false,
          put: false
        },
        onEnd: ({ newIndex, oldIndex }) => {
          const currRow = this.react.tableData.splice(oldIndex, 1)[0]
          this.react.tableData.splice(newIndex, 0, currRow)
          const _currRow = this.react.configItemsList.splice(oldIndex, 1)[0]
          this.react.configItemsList.splice(newIndex, 0, _currRow)
          this.$nextTick(() => {
            this.setStorage();
          });
        }
      })
    },
    onClickShowMore() {
      this.showMore = !this.showMore;
    },
    onClickShowPop() {
      this.visible = true
    },
    // 点击搜索
    onClickSearch() {
      this.$emit('clickSearch');
    },
    // 点击重置
    onClickReset() {
      for (const key in this.modelValue) {
        if (this.modelValue.hasOwnProperty(key)) {
          // 重置的时候，如果有默认值查询条件，使用默认值
          if (this.defaultCondition[key]) {
            this.modelValue[key] = this.defaultCondition[key]
          } else {
            this.modelValue[key] = ''
          }
        }
      }
      this.$emit('reset');
    },
    /**
     * 输入框内容改变，失去焦点或用户按Enter时触发
     * value 改变的值
     * sProp  改变的字段
     * sInputType  改变的组件
     **/
    onChangeInput(value, sProp, sInputType) {
      this.$emit('changeSearch', { value, sProp, sInputType });
    },
    querySearchForWidth(queryString, cb) {
      const results = this.widthOptions.map(i => ({ value: i, label: i }))
      cb(results)
    },
  },
  created() {
    this.setDefaultData()
    this.getStorage();
  },
  mounted() {
  }
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  display: flex;
  width: 100%;
  background: #fff;

  .main {
    position: relative;
    float: right;
    flex: 1;
    display: block;
    align-items: center;
    flex-wrap: wrap;
    overflow: hidden;
    margin: 12px 12px;
    height: var(--custom-height);

    &.showMore {
      height: auto !important;
    }


    .cell-box {
      position: relative;
      float: left;
      display: flex;
      flex-direction: row;
      align-items: stretch;
      height: auto;
      box-sizing: border-box;
      padding: 2px 5px;

      .cell-label {
        display: flex;
        flex: 0 0 auto;
        box-sizing: border-box;
        padding: 0 0px;
        height: auto;

        color: rgba(103, 112, 126, 1);
        align-items: center;
        justify-content: flex-end;


        span {
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 1;

        }

      }

      .cell-content {
        display: flex;
        flex: 1 1 auto;
        box-sizing: border-box;
        padding: 0px 0px;

        color: rgba(26, 32, 44, 1);
        align-items: flex-start;
        justify-content: flex-start;


        word-break: break-all;
        overflow: visible;

        span {
          font-size: 14px;
          font-weight: 400;
          letter-spacing: 0px;
          line-height: 1;

        }

      }
    }


  }

  .right-part {
    position: relative;
    float: right;
    width: auto;
    // height: 32px;
    height: var(--not-collapsed-height);
    padding: 14px 12px 0 0;


    // &::after {
    //   content: "";
    //   position: absolute;
    //   top: 8px;
    //   left: -1px;
    //   display: inline-block;
    //   width: 1px;
    //   height: 16px;
    //   background: var(--theme-color);
    // }

    .toolbar {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: auto;
      padding: 0 0;

      .icon {
        color: #888;
        cursor: pointer;
        height: 20px;
        padding: 2px;
        margin: 4px 3px;
        border-radius: 4px;
        transition: all ease 0.3s;


        &.showMore {
          transform: rotate(180deg);

        }

        &.reset {
          &:active {
            transform: rotate(45deg);
          }
        }

        &.setting {
          padding-right: 15px;
          border-right: 1px solid var(--el-disabled-border-color);
        }

        &:hover {
          background: #ccc;
          filter: contrast(1.5);
          transition: all ease .4s;
        }
      }

    }



  }


}

.pop-header {
  display: flex;
  align-items: center;
  padding: 6px 0 10px;
  justify-content: space-between;

  h3 {
    margin: 0 0 0 10px;
  }
}

.drag-icon {
  position: relative;
  display: inline-flex;
  min-height: 25px;
  align-items: center;
  justify-content: center;
  cursor: move;
  vertical-align: middle;
}

.radio-group {
  height: 30px;

  :deep(.el-radio-button__inner) {
    padding: 5px 11px;
    font-size: 12px;
    border-radius: 0;
  }
}


// 处理popover内弹窗遮挡
.pop-table {
  min-height: 420px;

  // .el-table--enable-row-transition .el-table__body td.el-table__cell
  :deep(.el-table__cell) {
    position: static;

    .cell {
      overflow: visible;
    }
  }

  :deep(.el-table__header-wrapper) {
    overflow: visible;
  }
}
</style>
