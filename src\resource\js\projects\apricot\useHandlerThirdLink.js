
import { ElMessage } from 'element-plus';
import axios from 'axios'

export function useHandleThirdLinkItem (item, patientInfo, userInfo) {
    // let url = item.sLinkURL + '?';   // url参数
    let url = item.sLinkURL;   // url参数
    url = url[url.length - 1].includes('/') ? url : url + '?';
    // 来源类型参数处理，整理成一个对象
    let sourceTypeTemp = {
        '1': useJoinThirdLink(item.sOutPatientParam, patientInfo, userInfo), // 门诊
        '2': useJoinThirdLink(item.sEmergencyParam, patientInfo, userInfo), // 急诊
        '3': useJoinThirdLink(item.sExamParam, patientInfo, userInfo), // 体检
        '4': useJoinThirdLink(item.sInPatientParam, patientInfo, userInfo) // 住院
    }
    if (item.sCallType == 'web_get' || item.sCallType == 'client_get') {
        // 打开网页链接
        // 1门诊 2急诊 3体检  4住院
        let sSource = patientInfo.sSource
        if (Object.keys(sourceTypeTemp).includes(sSource + '')) {
            window.open(url + sourceTypeTemp[sSource].newArr.join('&'), '_blank', 'noopener noreferrer');
        } else {
            ElMessage.warning('未找到到患者就诊类型！');
        }
    } else if (item.sCallType === 'client_post' || item.sCallType === 'web_post') {
        // 客户端接口调用
        if (Object.keys(sourceTypeTemp).includes(sSource + '')) {
            axios({
                url: url,
                methods: 'post',
                params: sourceTypeTemp[sSource].params
            }).then(res => {
                if (res.data.success) {
                    return
                }
                ElMessage.error(res.data.msg)
            }).catch(err => {
                console.log(err);
            })
        } else {
            ElMessage.warning('未找到到患者就诊类型！');
        }
    }
}

// 拼接第三方地址参数
export function useJoinThirdLink (val, patientInfo_, userInfo_) {
    // val  配置信息
    // patientInfo_   患者信息
    // userInfo_   用户信息
    if (!val) {
        // 参数为空字符串或者参数为null时
        return { newArr: [], params: {} }
    }
    // 用户字段映射
    const userFieldMapping = {
        userName: 'sName',
        userId: 'sId',
        userNo: 'sNo',
    }
    // 转变后的参数数组
    let newArr = [];
    let params = {};
    // 确保字符串
    let tempStr = val + '';
    // 每行参数转数组组装
    let tempArr = tempStr.split('&');
    tempArr.forEach(item => {
        // 找 {} 取其值
        let left = item.indexOf('{')
        let right = item.indexOf('}')

        if (left != -1 && right != -1) {
            // 有括号，提取系统内相关字段信息
            let key = item.slice(left + 1, right)
            let keyArr = key.split("#")
            let newKey = ""
            keyArr.forEach(key => {
                params[key] = undefined;
                if (patientInfo_.hasOwnProperty(key)) {
                    // 获取患者信息
                    newKey = patientInfo_[key] ?? '';
                    params[key] = patientInfo_[key] ?? ''
                }
                if (userInfo_.hasOwnProperty(key) && !Object.values(userFieldMapping).includes(key)) {
                    // 获取登录用户信息
                    newKey = userInfo_[key] ?? '';
                    params[key] = userInfo_[key] ?? ''
                }
                if (Object.keys(userFieldMapping).includes(key)) {
                    // 特殊字段处理，用户信息相关映射字段的处理
                    newKey = userInfo_[userFieldMapping[key]] ?? '';
                    params[key] = userInfo_[userFieldMapping[key]] ?? ''
                }
            });
            newArr.push(item.slice(0, left) + newKey)
        } else {
            // 没有括号, 非本系统信息
            newArr.push(item)
            let keyValArr = item.split('=');
            if (keyValArr.length) {
                params[keyValArr[0]] = keyValArr[1]
            }
        }
    });
    return {
        newArr: newArr,
        params: params
    }
}

