<template>
    <div class="c-flex-context c-container">
        <div class="c-form">
            <div class="c-form-button">
                <el-row :gutter="10">
                    <el-col :span="24">
                        <div>
                            <el-button-icon-fa type="primary"
                                icon="el-icon-plus"
                                @click="mxOpenDialog(1, '新增导出模板信息', addCallBack)">新增</el-button-icon-fa>
                            <el-button-icon-fa
                                type="primary"
                                icon="el-icon-refresh"
                                :loading="loading"
                                plain
                                @click="mxDoRefresh">刷新</el-button-icon-fa>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
        <div class="c-flex-auto">
            <div class="c-content"
                v-loading="loading">
                <el-table :data="tableData"
                    id="itemTable"
                    ref="mainTable"
                    size="small"
                    @row-click="onClickRow"
                    
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column type="index"
                        label="序号"
                        align="center"
                        width="50">
                    </el-table-column>
                    <el-table-column prop="sName"
                        label="模板名称"
                        show-overflow-tooltip
                        min-width="180">
                    </el-table-column>
                    <el-table-column prop="sModuleName"
                        label="所属模块"
                        min-width="110"
                        show-overflow-tooltip>
                        <template v-slot="scope">
                            <span>{{ getName(options.TreamentPrintTplType, scope.row.sModuleName)}} </span>
                        </template>
                    </el-table-column>
                    
                    <el-table-column prop="sContact"
                        label="是否默认"
                        width="90"
                        align="center">
                        <template v-slot="scope">
                            <span v-if="scope.row.iIsDefaulted == 1"
                                class="i-text-01"
                                style="color:#67C23A">是</span>
                            <span v-else
                                class="i-text-02">否</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="160px" align="center" fixed="right">
                        <template v-slot="scope">
                            <el-button size="small" 
                                link 
                                type="primary"
                                @click="handleEdit(scope.row)">
                                编辑
                                <template #icon>
                                    <Icon name="el-icon-edit" color="">
                                    </Icon>
                                </template>
                            </el-button>
                            <el-divider direction="vertical"></el-divider>
                            <el-button size="small" link @click="onClickDel(scope.row)">
                                删除
                                <template #icon>
                                    <Icon name="el-icon-delete" color="">
                                    </Icon>
                                </template>
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <!-- <div class="c-pagination scope-pagination-around">
            <el-pagination background @size-change="onSizeChange"
                @current-change="onCurrentChange"
                :current-page="page.pageCurrent"
                :page-sizes="mxPageSizes"
                :pager-count="5"
                :page-size="page.pageSize"
                layout="total, sizes, prev, pager, next"
                :total="page.total">
            </el-pagination>
        </div> -->

        <el-dialog :close-on-click-modal="false"
            append-to-body
            :title="editLayer.playerText"
            v-model="editLayer.visible"
            :destroy-on-close="true"
            @close="closeDialog"
            top="8vh"
            width="900px"
            class="my-dialog">
            <div class="c-inner-content"
                v-bind:class="{ 'scope-input-look': editLayer.look }">
                <el-form ref="refEditLayer"
                    class="scope-treatment-rules"
                    label-width="100px"
                    label-position="right"
                    :model="editLayer.form"
                    :rules="rules">
                    <el-form-item prop="sName" label="模板名称：">
                        <el-input 
                            placeholder="模板名称"
                            v-model="editLayer.form.sName"
                            style="width: 100%"
                            clearable></el-input>
                    </el-form-item>
                    <el-form-item prop="sModuleName" label="所属模块：">
                        <el-select v-model="editLayer.form.sModuleName"
                            placeholder="所属模块"
                            @change="onChangeModule"
                            style="width: 100%">
                            <el-option v-for="item in options.TreamentPrintTplType"
                                :key="item.sModuleName"
                                :label="item.sName"
                                :value="item.sModuleName">
                            </el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="iIsDefaulted" label="是否默认：">
                        <el-radio-group v-model="editLayer.form.iIsDefaulted">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item prop="iIsOrderNumber" label="展开序号：">
                        <el-radio-group v-model="editLayer.form.iIsOrderNumber">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item prop="sFile" label="导出列："
                        class="c-block">
                        <div class="header">
                            <div class="item">
                                <drag-set-template :key="1"
                                    :list="list1"
                                    class="drag-set i-show"
                                    header-text="导出列">
                                    <el-button size="small"
                                        @click="onClickAddRemove"
                                        style="position: absolute;right: 6px;top: 6px;">移除全部</el-button>
                                </drag-set-template>
                                
                            </div>
                            
                            <i class="i-center"></i>
                            <div class="item">
                                <drag-set-template :key="2"
                                    :list="list2"
                                    class="drag-set i-hide"
                                    header-text="可选列">
                                    <el-button size="small"
                                        @click="onClickAddAll"
                                        style="position: absolute;right: 6px;top: 6px;">全部添加</el-button>
                                </drag-set-template>
                            </div>
                           
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <div>提示：不需要导出的列可从【导出列】拖拽到【可选列】中</div>
                    </el-form-item>
                   
                </el-form>
            </div>
            <template #footer><div 
                class="my-dialog-footer">
                <div class="g-page-footer">
                    <el-button-icon-fa v-show="!editLayer.look"
                        icon="el-icon-check"
                        @click="mxDoSaveData('refEditLayer')"
                        :loading="editLayer.loading"
                        type="primary">保存</el-button-icon-fa>
                    <el-button-icon-fa 
                        icon="el-icon-close"
                        @click="editLayer.visible = false">取消</el-button-icon-fa>
                    
                </div>
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/system/exportTemplate.js'
import { mixinTable } from '$supersetResource/js/projects/apricot/index.js'
import DragSetTemplate from './page_config/DragSetTemplate'
export default {
    name: 'ExportTemplate',
    mixins: [mixinTable],
    components: {
        DragSetTemplate
    },
    props: {},
    data () {
        return {
            isEdit: false, 			// 是否是编辑状态
            defualtVal: {
                editLayer: {
                    iIsDefaulted: 1,
                    iIsOrderNumber: 1
                }
            },
            options: {
                TreamentPrintTplType: [],
                treatmentItem: [],
            },
            rules: {
                sModuleName: [{
                    required: true,
                    message: '必须',
                    trigger: 'change'
                }],
                sName: [{
                    required: true,
                    message: '请输入模板名称',
                    trigger: 'blur'
                },
                {
                    validator: (rule, value, callback) => {
                        if (value == undefined) {
                            callback()
                            return;
                        }
                        if (value.includes('.') || value.includes('。')) {
                            callback(new Error('不能包含点 . '))
                        }
                        callback()

                    }
                }],
                iIsDefaulted: [{
                    required: true,
                    message: '必须',
                    trigger: 'blur'
                }],
            },
            list1: [],
            list2: [],
        }
    }, 
    methods: {
        /**
         * 获取 name 值
         */
        getName (obj, value) {
            let result = ''
            for (const e of obj) {
                if (e.sModuleName == value) {
                    result = e.sName
                    break;
                }
            }
            return result
        },
        // 改变所属模块
        onChangeModule (value) {
            this.options.TreamentPrintTplType.forEach(item => {
                if (item.sModuleName === value) {
                    this.doAssignModule(item)
                    return;
                }
            })
        },
        addCallBack (form) {
            if (this.options.TreamentPrintTplType.length) {
                this.doAssignModule(this.options.TreamentPrintTplType[0])
            }
        },
        // 赋值表头
        doAssignModule (obj) {
            if (obj.templateFields) {
                this.editLayer.form.sModuleName = obj.sModuleName
                let list1 = obj.templateFields.filter(item =>
                    item.iShow
                );
                let list2 = obj.templateFields.filter(item =>
                    !item.iShow
                );
                this.list1 = list1.slice()
                this.list2 = list2.slice()
            } else {
                this.list1 = []
                this.list2 = []
            }
        },
        getObject (obj, value) {
            let result = ''
            for (const e of obj) {
                if (e.sProjectCode == value) {
                    result = e.sProjectName
                    break;
                }
            }
            return result
        },
        handleEdit(row) {
            this.editLayer.form = Object.assign({},row) // 深拷贝
            this.editLayer.visible = true;
            this.editLayer.playerText = '编辑导出模板'
            this.callBackSite(row)
        },
        /**
         * 保存数据
         */
        saveData (params) {
            // this.editLayer.loading = false;
            // this.options.TreamentPrintTplType.forEach(item => {
            // 	if (item.sModuleName === params.sModuleName){
            // 		params.iIsOrderNumber = item.iIsOrderNumber || 0
            // 		return;
            // 	}
            // })
            let fields = []
            let newList1 = this.list1.map(item => {
                item.iShow = 1
                return item
            })
            let newList2 = this.list2.map(item => {
                item.iShow = 0
                return item
            })
            fields = fields.concat(newList1, newList2);

            params.templateFields = fields.map((item, index) => {
                item.iIndex = index
                return item
            })
            if (this.isEdit) {
                // 编辑提交
                Api.editData(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.editLayer.visible = false;
                        this.mxDoRefresh();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
                return
            }
            // 新增提交
            Api.saveData(params).then((res) => {
                this.editLayer.loading = false;
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.editLayer.visible = false;
                    this.mxDoRefresh();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            }).catch(() => {
                this.editLayer.loading = false;
            })
        },

        /**
         * 获取表格数据
         */
        getData (params) {
            Api.getData({}).then((res) => {
                if (res.success) {
                    this.tableData = res.data?res.data:[]
                    this.loading = false;

                    // 赋选中状态
                    this.setSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`您确定要删除【${row.sName}】吗？`, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                Api.delData({
                    sId: row.sId,
                    sModuleName: row.sModuleName,
                    iIsOrderNumber: row.iIsOrderNumber,
                    sName: row.sName,
                    iIsDefaulted: row.iIsDefaulted
                }).then((e) => {
                    if (e.success) {
                        this.$message({
                            message: e.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // let page = Math.ceil((this.page.total - 1) / this.page.pageSize) 
                        // let pageCurrent = this.page.pageCurrent > page ? page : this.page.pageCurrent
                        // this.page.pageCurrent = pageCurrent < 1 ? 1 : pageCurrent

                        this.mxDoRefresh(); // 触发更新表格数据
                    } else {
                        this.$message({
                            message: e.msg,
                            type: 'error',
                            duration: 3000
                        });
                    }
                })

            }).catch(() => { });
        },
        callBackSite (row) {
            this.isEdit = true
            Api.findDataById({ sId: row.sId }).then(e => {
                if (e.success && e.data) {
                    this.options.TreamentPrintTplType.forEach(item => {
                        if (item.sModuleName === e.data.sModuleName) {
                            this.doAssignModule(e.data)
                            return;
                        }
                    })
                    return;
                }
                this.$message.error('获取编辑信息失败');
            })
        },
        // 关闭弹窗取消状态
        closeDialog () {
            this.isEdit = false
        },
        onClickAddAll () {
            this.list2.forEach(item => {
                this.list1.push(item)
            });
            this.list2.splice(0)
        },
        onClickAddRemove () {
            this.list1.forEach(item => {
                this.list2.push(item)
            });
            this.list1.splice(0)
        },
    },
    mounted () {
        // this.getCodeTable('TreamentPrintTplType');
        Api.getModules().then(e => {
            if (e.success && e.data) {
                this.options.TreamentPrintTplType = e.data
                return;
            }
            this.options.TreamentPrintTplType = []
        }).catch(() => {
            console.log('err')
        })
        // this.getTreatmentItem();
    }
};
</script>
<style lang="scss" scoped>
.c-flex-context {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 15px ;
    :deep(.c-form) {
        display: flex;
        flex-direction: column;
        .c-form-button {
            padding: 0 0px 10px 0px;
        }
        
    }
    :deep(.c-flex-auto) {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: auto;
        .c-content {
            flex: 1;
            height: 0px;
        }
    }
}
.c-inner-content {
    .el-form {
        padding: 10px;
        .c-radio-groups {
            padding: 5px 0px;
            color: #606266;
            &.c-file-parent {
                display: flex;
                align-items: center;
            }
            > span {
                color: #2384d3;
                padding-right: 10px;
                &:before {
                    content: "";
                    display: inline-block;
                    width: 8px;
                    height: 8px;
                    background-color: #f56c6c;
                    border-radius: 50%;
                    margin-right: 5px;
                    position: relative;
                    top: -1px;
                }
            }
            .c-file {
                display: flex;
                align-items: center;
                .i-file {
                    padding-left: 10px;
                    position: relative;
                    .i-remore {
                        right: -20px;
                    }
                }
            }
        }
        .c-block {
            width: 100%;
        }
    }

}
.header {
    display: flex;
    .item {
        width: 50%;
        margin: 0 10px
    }
}
:deep(.drag-set) {
    &.i-show {
        .board-column-header {
            background: #28b9a8;
        }
    }
    &.i-hide {
        .board-column-header {
            background: #ccc;
        }
        .board-column-content {
            flex-wrap: wrap;
        }
    }
}
.i-center {
    display: inline-block;
    height: 40px;
}


</style>
