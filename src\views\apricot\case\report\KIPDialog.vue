<template>
    <el-dialog v-model="visible"
        title="KIP"
        draggable
        :close-on-click-modal="false"
        append-to-body
        destroy-on-close
        class="t-default my-dialog"
        @opened="openDialog"
        width="750px">
        <div class="dialog-body" v-loading="loading">
            <el-form ref="form"
                :model="form"
                label-width="100px"
                label-suffix=":">
                <el-form-item label="报告关键字">
                    <el-tag :key="tag"
                        v-for="tag in sReportKeyList"
                        size="large">
                        {{ tag }}
                    </el-tag>
                </el-form-item>
                <el-form-item label="发送短信">
                    <el-radio-group v-model="form.iIsSendSms">
                        <el-radio :label="1">发送</el-radio>
                        <el-radio :label="0">不发送</el-radio>
                    </el-radio-group>
                </el-form-item>
                <template v-if="form.iIsSendSms">
                    <el-form-item v-for="item in msgData.kipSmsList">
                        <template #label>
                            <el-checkbox v-model="item.checked"
                                :true-label="1"
                                :false-value="0">{{ item.sReceiverType }}</el-checkbox>
                        </template>
                        <div class="flex">
                            <el-input v-model="item.sReceiverName"
                                class="i-input"></el-input>
                            <div class="flex">
                                <span class="i-text">手机号码:</span>
                                <el-input v-model="item.sReceiverPhone"
                                    class="i-input"></el-input>
                            </div>
                        </div>
                        <el-col :span="24"
                            class="i-col">
                            <el-input v-model="item.sSmsContent"
                                type="textarea"
                                :rows="6"></el-input>
                        </el-col>
                    </el-form-item>
                </template>

                <el-form-item v-else
                    label="不发送原因">
                    <el-radio-group v-model="form.reason">
                        <el-radio label="非首次诊断"></el-radio>
                        <el-radio label="不合适"></el-radio>
                        <el-radio label="其他"></el-radio>
                    </el-radio-group>
                    <el-col v-if="form.reason == '其他'"
                        :span="24"
                        class="i-col">
                        <el-input v-model="form.sUnsendReason"
                            type="textarea"
                            :rows="6"></el-input>
                    </el-col>
                </el-form-item>
            </el-form>
        </div>
        <template #footer>
            <el-button-icon-fa type="primary"
                icon="fa fa-tick-line"
                @click="onSubmitClick">提交</el-button-icon-fa>
            <el-button-icon-fa plain
                icon="fa fa-cancle"
                @click="onNoSubmitClick">不提交</el-button-icon-fa>
            <el-button-icon-fa plain
                icon="fa fa-close-1"
                @click="visible = false">关闭</el-button-icon-fa>
        </template>
    </el-dialog>
</template>

<script>

import { deepClone } from '$supersetUtils/function'
import { getKip, kipSave } from '$supersetApi/projects/apricot/reportCase/kip.js';

export default {
    props: {
        modelValue: {
            type: Boolean,
            default: false
        },
        patientInfo: {
            type: Object,
            default: () => ({})
        },
    },
    emits: ['update:modelValue', 'onKIPAfterSubmitStep'],
    data () {
        return {
            form: {
                iIsSendSms: 1
            },
            msgData: {},
            sReportKeyList: [],
            loading: false
        }
    },
    computed: {
        visible: {
            get: function () {
                return this.modelValue
            },
            set: function (value) {
                this.$emit('update:modelValue', value)
            }
        },
    },
    methods: {
        // 打开弹窗
        async openDialog () {
            await this.getKipMessage();
            this.form = this.$options.data().form;
        },
        // 提交
        onSubmitClick () {
            let params = {
                iIsSendSms: this.form.iIsSendSms,
                sPatientId: this.patientInfo.sId,
                sReportKeywords: this.msgData.sReportKeys,
                sUnsendReason: this.form.reason === '其他' ? this.form.sUnsendReason : this.form.reason,
            }
            if (params.iIsSendSms) {
                params.kipSmsList = this.msgData.kipSmsList.filter(item => item.checked);
                params.kipSmsList = deepClone(params.kipSmsList)
                params.kipSmsList.map(item => { delete item.checked; })
            }
            let loading = this.$loading({
                lock: true,
                text: '正在提交中，请稍等',
                background: 'rgba(0, 0, 0, 0.2)'
            });
            kipSave(params).then(res => {
                loading.close();
                if (res.success) {
                    this.$message.success(res.msg); 
                    setTimeout(() => {
                        this.$emit('onKIPAfterSubmitStep');
                        this.visible = false;
                    }, 500)
                    return;
                }
                this.$message.error(res.msg);
            }).catch(err => {
                loading.close();
                console.log(err);
            })
        },
        // 不提交
        onNoSubmitClick () {
            this.$emit('onKIPAfterSubmitStep');
            this.visible = false;
        },
        // 获取KIP信息
        async getKipMessage () {
            const { sId } = this.patientInfo;
            this.loading = true;
            await getKip({ sPatientId: sId }).then(res => {
                if (res.success) {
                    this.msgData = res.data || {};
                    let sReportKeys = this.msgData.sReportKeys;
                    this.sReportKeyList = sReportKeys.length ? sReportKeys.split(',') : [];
                    return
                }
                this.$message.error(res.msg)
            }).catch(err => {
                console.log(err)
            }).finally(() => {
                this.loading = false;
            })
        }
    },
    created () { }
}
</script>
<style lang="scss" scoped>
.dialog-body {
    height: 65vh;
    padding: 10px;
    box-sizing: border-box;
}

.el-tag {
    margin-left: 10px;
}

.btns {
    display: flex;
    justify-content: end;
    align-items: center;
    .btn-item {
        margin-left: 10px;
    }
}
.i-col {
    margin-top: 15px;
}
.i-text {
    margin: 0 10px 0 30px;
}
.i-input {
    width: 180px;
}
</style>