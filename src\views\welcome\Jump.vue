<template>
  <div class="absolute w-full h-full">
    <div class="app-loading">
      <div class="app-loading-wrap">
        <div class="app-loading-dots">
          <span class="dot dot-spin"><i></i><i></i><i></i><i></i></span>
        </div>
        <div class="app-loading-title"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDefaultModuleRouterName } from '$supersetResource/js/common.js'

export default {
  name: 'welcome_jump',
  data() {
    return {

    }
  },
  methods: {
  },
  created() {
    // console.log(this.$route.query)
    // //打开默认模块
    if (this.$route.query.redirect === 'auto') {
      const checkTimer = setInterval(() => {
        const userId = this.$store.getters['user/userId']
        if (userId) {
          this.$store.commit({
            type: 'module_router/switchModuleRouter',
            activeRouterName: getDefaultModuleRouterName()
          })
          this.$router.push({ name: getDefaultModuleRouterName(), })
          clearInterval(checkTimer)
        }
      }, 100);
    }
  }
}
</script>

<style scoped lang='scss'></style>
