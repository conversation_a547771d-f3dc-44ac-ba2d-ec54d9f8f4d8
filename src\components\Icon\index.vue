<template>
  <span class="icon-comp">
    <i v-if="name.indexOf('el-icon') !== -1" :class="name" :style="{ color, 'font-size': size + 'px' }"></i>
    <el-icon v-else-if="type === 'el-icon'" :style="{ color, 'font-size': size + 'px' }">
      <component :is="ElementIcon[name]" />
    </el-icon>
  </span>
</template>
<script>

// icon

export default {
  name: 'Icon'
}
</script>
<script setup>
// el-icon
import { Rank, MoreFilled, ArrowDown } from '@element-plus/icons-vue'

const ElementIcon = {
  Rank, MoreFilled, ArrowDown
}

defineProps({
  type: {
    default: 'el-icon',
  },
  size: {
    default: 14,
  },
  color: {
    default: '',
  },
  theme: {
    default: 'outline',
  },
  strokeWidth: {
    default: 3,
  },
  name: {
    default: '',
  },
  className: {
    default: '',
  },
});


</script>
<style lang="scss" scoped>
.icon-comp {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
