export default {
    dragA1: {
        type: 'column',
        localStorageKey: '202306281738',
        panelConfig: [
            {
                slot: 'c1',
                size: 416,
                minSize: 416,
                maxSize: 510
            },
            {
                slot: 'c2',
                flex: 1
            }
        ]
    },
    dragA2: {
        type: 'row',
        localStorageKey: '202306211434',
        panelConfig: [
            {
                slot: 'c1',
                size: 200,
                minSize: 200,
                maxSize: 400
            },
            {
                slot: 'c2',
                flex: 1
            }
        ]
    },
    searchConfig: [
        {
            sProp: 'beginOrderTime',
            sLabel: '开单开始',
            iLayourValue: 3,
            iCustom: 1
        },
        {
            sProp: 'endOrderTime',
            sLabel: '开单结束',
            iLayourValue: 3,
            iCustom: 1
        },
        {
            sProp: 'execState',
            sLabel: '执行状态',
            iLayourValue: 3,
            iCustom: 1,
            sOptionProp: 'execStateOptions'
        },
        {
            sProp: 'iAppointState',
            sLabel: '预约状态',
            iLayourValue: 3,
            iCustom: 1,
            sOptionProp: 'iAppointStateOptions'
        },
        {
            sProp: 'medicalRecordNO',
            sLabel: '病历号',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'outpatientNo',
            sLabel: '门诊号',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'inHospitalNO',
            sLabel: '住院号',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'physicalExamSerial',
            sLabel: '体检流水号',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'applyNo',
            sLabel: '申请单号',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'patientName',
            sLabel: '患者姓名',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'visitType',
            sLabel: '就诊类型',
            iLayourValue: 3,
            sInputType: 'option',
            sOptionProp: 'visitTypeOptions'
        },
        {
            sProp: 'visitCard',
            sLabel: '就诊卡号',
            iLayourValue: 3,
            sInputType: 'input', 
            isShow: false
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            iLayourValue: 3,
            sInputType: 'input', 
            isShow: false
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            iLayourValue: 3,
            sInputType: 'input', 
            isShow: false
        },
        {
            sProp: 'orderNO',
            sLabel: '医嘱号',
            iLayourValue: 3,
            sInputType: 'input'
        },
        {
            sProp: 'machineryRoomText',
            sLabel: '检查机房',
            iLayourValue: 3,
            sInputType: 'input',
        },
        {
            sProp: 'sDeviceTypeCode',
            sLabel: '设备类型',
            iLayourValue: 3,
            iCustom: 1
        },
        {
            sProp: 'applyMemo',
            sLabel: '申请单备注',
            iLayourValue: 3, 
            isShow: false
        },
    ],
    applyTableConfig: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sFixed: 'left',
            sMinWidth: '80px',
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sFixed: 'left',
            sMinWidth: '60px',
        },
        {
            sProp: 'iAge',
            sLabel: '年龄',
            sFixed: 'left',
            sMinWidth: '60px',
        },
        {
            sProp: 'sOrderItemName',
            sLabel: '检查项目',
            sFixed: 'left',
            sMinWidth: '160px',
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sMinWidth: '100px',
        },
        {
            sProp: 'dApplyDate',
            sLabel: '开单日期',
            sMinWidth: '140px',
            formatter: (row) => moment(row.dApplyDate).format("YYYY-MM-DD HH:mm"),
            isSortable: true,
            sOrder: 'descending'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sChargeStateText',
            sLabel: '收费状态',
            sMinWidth: '100px',
        },
        {
            sProp: 'sPhysicalExamSerial',
            sLabel: '体检流水号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sMinWidth: '100px',
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sMinWidth: '100px',
        },
        {
            sProp: 'sMachineryRoomText',
            sLabel: '检查机房',
            sMinWidth: '100px',
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            sMinWidth: '100px',
        }, {
            sProp: 'iExecState',
            sLabel: '执行状态',
            sMinWidth: '100px',
        }, {
            sProp: 'sAppointStateName',
            sLabel: '预约状态',
            sMinWidth: '100px',
        },
        {
            sProp: 'sAppointTime',
            sLabel: '预约时间段',
            sMinWidth: '140px',
        },
        {
            sProp: 'dAppointDay',
            sLabel: '推送预约时间',
            sMinWidth: '140px',
            formatter: (row) => row.dAppointDay && moment(row.dAppointDay).format("YYYY-MM-DD")
        },
        {
            sProp: 'sPhone',
            sLabel: '患者电话',
            sMinWidth: '140px',
        },

        {
            sProp: 'sPhysicalExamNo',
            sLabel: '体检号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            sMinWidth: '140px',
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sMinWidth: '150px',
        },
        {
            sProp: 'sMemo',
            sLabel: '申请单备注',
            sMinWidth: '150px',
        }
    ],
    statisticsTableConfig: [{
        sProp: "iDate",
        sLabel: '日期',
        sFixed: null,
        sAlign: null,
        sWidth: '100px',
        sMinWidth: '100px',
    },
    {
        sProp: "sItemName",
        sLabel: '检查项目',
        sFixed: null,
        sAlign: null,
        sWidth: null,
        sMinWidth: '100px',
    },
    {
        sProp: "sRoomName",
        sLabel: '机房',
        sFixed: null,
        sAlign: null,
        sWidth: null,
        sMinWidth: '100px',
        isShow: false
    },
    {
        sProp: "sHospitalDistrictName",
        sLabel: '院区',
        sFixed: null,
        sAlign: null,
        sWidth: null,
        sMinWidth: '100px',
        isShow: false
    },
    {
        sProp: "registered",
        sLabel: '人数',
        sFixed: null,
        sAlign: null,
        sWidth: '100px',
        sMinWidth: '100px',
    }],
    appointmentInputList1: [
        {
            sProp: 'sName', sLabel: '患者姓名',
            iRequired: 1, sInputType: 'text',
            iLayourValue: 12, iCustom: 1, height: '32px'
        }, {
            sProp: 'sNameSpell', sLabel: '姓名拼音',
            iRequired: 1, sInputType: 'text',
            iLayourValue: 12, iCustom: 1, height: '32px'
        }, {
            sProp: 'sSex', sLabel: '性别',
            iRequired: 1, sOptionProp: 'iSexList', sPropName: 'sSexText',
            sInputType: 'option', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'dBirthday', sLabel: '出生日期', iRequired: 1, 
            sInputType: 'date-picker', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'iAge', sLabel: '年龄',
            iRequired: 1, sInputType: 'number',
            iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sIdNum', sLabel: '身份证号',
            sInputType: 'text', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'fHeight', sLabel: '身高',
            sInputType: 'text-unit',
            iLayourValue: 12, sUnit: 'cm', height: '32px', iCustom: 1
        },
        {
            sProp: 'fWeight', sLabel: '体重',
            sInputType: 'number-unit',
            iLayourValue: 12, sUnit: 'kg', height: '32px', iCustom: 1
        },
        {
            sProp: 'iIsPregnant', sLabel: '是否怀孕', iReadonly: 0, iCustom: 1,
            sOptionProp: 'pregnantOptions', sInputType: 'option', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sPhone', sLabel: '联系电话',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sAddress', sLabel: '患者地址',
            sInputType: 'text', iLayourValue: 24, height: '32px'
        },
    ],
    appointmentInputList1_1: [
        {
            sProp: 'sConsultRoomId', sLabel: '问诊室', sPropName: 'sConsultRoomName',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sInjectionRoomId', sLabel: '注射室', sPropName: 'sInjectionRoomName',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sMedicalRecordNO', sLabel: '病历号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'fBloodSugar', sLabel: '空腹血糖',
            sInputType: 'text-unit', iLayourValue: 12, sUnit: 'mmol/L', height: '32px', iCustom: 1
        },
        {
            sProp: 'sSource', sLabel: '就诊类型', sPropName: 'sSourceText',
            sOptionProp: 'sSourceOptions', sInputType: 'option', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sEncounter', sLabel: '就诊次数',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sApplyNO', sLabel: '申请单号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sOutpatientNO', sLabel: '门诊号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sInHospitalNO', sLabel: '住院号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sPhysicalExamNo', sLabel: '体检号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sPhysicalExamSerial', sLabel: '体检流水号',
            sInputType: 'text', iLayourValue: 12, height: '32px',
            isShow: false
        },
        {
            sProp: 'sVisitCard', sLabel: '就诊卡号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sHealthCardNO', sLabel: '健康卡号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sPatientIndex', sLabel: '患者索引',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sBedNum', sLabel: '床号',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sImageNo', sLabel: '影像号',
            sInputType: 'text', iLayourValue: 12,  iCustom: 1, height: '32px'
        },
        {
            sProp: 'sEthnicGroupCode', sLabel: '民族',
            sInputType: 'option', iLayourValue: 12,  sOptionProp: 'ethnicGroupCodeOption', sPropName: 'sEthnicGroupName'
        },
        {
            sProp: 'sApplyDepartText', sLabel: '申请科室',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sApplyPersonName', sLabel: '申请医生',
            sInputType: 'text', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'dApplyDate', sLabel: '申请日期',
            sInputType: 'date-picker', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sFeeType', sLabel: '费用类型', sPropName: 'sFeeTypeText',
            sOptionProp: 'ApricotReportFeeType',
            sInputType: 'option', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'fFees', sLabel: '总费用',
            sInputType: 'text-unit', iLayourValue: 12, height: '32px', sUnit: '元', iCustom: 1
        },
        {
            sProp: 'sChargeState', sLabel: '收费状态', sPropName: 'sChargeStateText',
            sOptionProp: 'ChargeStateOptions',
            sInputType: 'option', iLayourValue: 12, height: '32px'
        },
        {
            sProp: 'sClinicalDiagnosis', sLabel: '临床诊断',
            sInputType: 'text', iLayourValue: 24, height: '32px'
        }
    ],
    appointmentInputList2: [
        {
            sProp: 'sDistrictId', sLabel: '院区', sPropName: 'sDistrictName',
            iRequired: 1, sOptionProp: 'hospitalOptions',
            sInputType: 'option', iLayourValue: 12, height: '32px', iCustom: 1
        }, {
            sProp: 'sMachineryRoomId', sLabel: '机房', sPropName: 'sMachineryRoomText',
            iRequired: 1, sOptionProp: 'machineRoomOptions',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sProjectId', sLabel: '项目名称', sPropName: 'sProjectName',
            iRequired: 1, sOptionProp: 'projectOptions',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sNuclearNum', sLabel: '核医学号',
            iRequired: 1, sInputType: 'text', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sNuclideText', sLabel: '核素', sPropName: 'sNuclideText',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px',
        },
        {
            sProp: 'sTracerText', sLabel: '示踪剂', sPropName: 'sTracerText',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sPosition', sLabel: '检查部位', sPropName: 'sPositionText',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'sTestMode', sLabel: '检查方式', sPropName: 'sTestModeText',
            sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        {
            sProp: 'fRecipeDose', sLabel: '处方剂量',
            sInputType: 'number', iLayourValue: 12, iCustom: 1, height: '32px'
        },
        // {
        //     sProp: 'iIsInvariable', sLabel: '是否定量',
        //     sInputType: 'option', iLayourValue: 12, iCustom: 1, height: '32px'
        // },
        // {
        //     sProp: 'fCoefficient', sLabel: '处方系数',
        //     sInputType: 'number', iLayourValue: 12, iCustom: 1, height: '32px'
        // },
        // {
        //     sProp: 'fDosage', sLabel: '定量剂量',
        //     sInputType: 'number', iLayourValue: 12, iCustom: 1, height: '32px'
        // },
    ],
    // 文本组件配置
    textListApplyFormInfo: [
        { prop: 'sName', label: '姓名', width: '25%' },
        { prop: 'sMedicalRecordNO', label: '病历号', width: '25%' },
        { prop: 'sApplyNO', label: '申请单号', width: '25%' },
        { prop: 'sSexText', label: '性别' },
        { prop: 'iAge', label: '年龄', width: '25%' },
        { prop: 'dBirthday', label: '出生日期', iCustom: 1, width: '25%' },
        { prop: 'sPhone', label: '患者电话', width: '25%' },
        { prop: 'sAddress', label: '患者地址', width: '100%' },
        { prop: 'sSourceText', label: '就诊类型', width: '25%' },
        { prop: 'sOutpatientNO', label: '门诊号', width: '25%' },
        { prop: 'sInHospitalNO', label: '住院号', width: '25%' },
        { prop: 'sVisitCard', label: '就诊卡号', width: '25%' },
        { prop: 'sVitisNo', label: '就诊流水号', width: '25%' },
        { prop: 'iExecState', label: '执行状态', iCustom: 1, width: '25%' },
        { prop: 'sPastHistory', label: '既往病史', width: '100%' },
        { prop: 'sMedicalHistory', label: '简要病史', width: '100%' },
        { prop: 'sClinicalDiagnosis', label: '临床诊断', width: '100%' },
        { prop: 'sTargetSiteName', label: '检查项目及部位', width: '100%', labelWidth: '120px' },
        { prop: 'sCheckIntent', label: '检查目的', width: '100%' },
        { prop: 'sMemo', label: '开单备注', width: '100%' },
        { prop: 'sChargeStateText', label: '收费状态', width: '25%' },
        { prop: 'sInpatientAreaId', label: '病区', width: '25%' },
        { prop: 'sBedNum', label: '床号', width: '25%' },
        { prop: 'sApplyDepartText', label: '申请科室', width: '25%' },
        { prop: 'sApplyPersonName', label: '申请医生', width: '25%' },
        { prop: 'dApplyDate', label: '申请日期', iCustom: 1, width: '25%' },
        { prop: 'sChiefPhysicianPhone', label: '医生电话', width: '50%' }
    ],
    panelConfig: {
        isCallSignIn: true,
        isSaveAutoPrintPocket: true,
        isLoadApplyFirst: false,
        isAutomaticLoadApply: true,
        isSaveAndCloseAppointment: true,
        isShowHistoryPatient: true,
        appointCardBgColor: '#d9eae8',
        applyTimeDays: 14,
        statisticsDays: 1,
        medicineChooseType: 1,
    },
    patientQueryTableConfig: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sMinWidth: '100px'
        },
        {
            sProp: 'sNameSpell',
            sLabel: '姓名拼音',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sAlign: 'center',
            sMinWidth: '80px'
        },
        {
            sProp: 'fHeight',
            sLabel: '身高（cm）',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'fWeight',
            sLabel: '体重（kg）',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'iIsRegister',
            sLabel: '签到',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsConsult',
            sLabel: '问诊',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsInject',
            sLabel: '注射',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsMachine',
            sLabel: '上机',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsImaging',
            sLabel: '收图',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'iIsReport',
            sLabel: '报告',
            sAlign: 'center',
            sMinWidth: '60px'
        },
        {
            sProp: 'sRoomText',
            sLabel: '设备类型',
            sMinWidth: '110px'
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sMinWidth: '110px'
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约时间',
            sMinWidth: '150px'
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sMinWidth: '110px'
        },
        {
            sProp: 'sApplyPersonName',
            sLabel: '申请医生',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sMedicalRecordNO',
            sLabel: '病历号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sOrderNO',
            sLabel: '医嘱号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sIdNum',
            sLabel: '身份证号',
            sMinWidth: '110px'
        },
        {
            sProp: 'sVitisNo',
            sLabel: '就诊号',
            sMinWidth: '110px'
        },
        {
            sProp: 'dBirthday',
            sLabel: '出生日期',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sDistrictName',
            sLabel: '院区',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineryRoomText',
            sLabel: '机房',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMachineStationName',
            sLabel: '工作站',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sApplyNO',
            sLabel: '申请单号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sDiagnosticOpinionText',
            sLabel: '诊断意见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sInspectSeeText',
            sLabel: '检查所见',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalHistory',
            sLabel: '简要病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sSourceText',
            sLabel: '就诊类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sEncounter',
            sLabel: '就诊次数',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPositionText',
            sLabel: '检查部位',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTestModeText',
            sLabel: '检查方式',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideText',
            sLabel: '核素',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sNuclideSupName',
            sLabel: '核素全称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sTracerText',
            sLabel: '示踪剂',
            sMinWidth: '100px',
            iIsHide: true
        },
        {
            sProp: 'sTracerSupName',
            sLabel: '示踪剂全称',
            sMinWidth: '120px',
            iIsHide: true
        },
        {
            sProp: 'fRecipeDose',
            sLabel: '处方剂量',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'iIsPregnant',
            sLabel: '怀孕',
            sAlign: 'center',
            sMinWidth: '80px',
            iIsHide: true
        },
        {
            sProp: 'sPhone',
            sLabel: '联系电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sAddress',
            sLabel: '住址',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMaritalStatusName',
            sLabel: '婚姻状况',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sMedicalCaseNO',
            sLabel: '病案号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sImageNo',
            sLabel: '影像号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientAreaText',
            sLabel: '病区名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInpatientWardText',
            sLabel: '病房名称',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sBedNum',
            sLabel: '床号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sCardNum',
            sLabel: '社保卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fBloodSugar',
            sLabel: '检查空腹血糖',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPresentHistory',
            sLabel: '现病史',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sPastHistory',
            sLabel: '既往史疾病',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sCheckIntent',
            sLabel: '检查目的',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sClinicalSymptoms',
            sLabel: '临床症状',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefComplaint',
            sLabel: '主诉',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sInvoiceNum',
            sLabel: '发票号',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sFeeTypeText',
            sLabel: '费用类型',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'fFees',
            sLabel: '费用',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChargeStateText',
            sLabel: '收费状态',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'dApplyDate',
            sLabel: '申请时间',
            sMinWidth: '150px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianName',
            sLabel: '主治医生',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sChiefPhysicianPhone',
            sLabel: '医生电话',
            sMinWidth: '110px',
            iIsHide: true
        },
        {
            sProp: 'sPhysicalExamNo',
            sLabel: '体检号',
            sMinWidth: '110px',
            iIsHide: true
        }
    ],
    historyTableConfig: [
        {
            sProp: 'sName',
            sLabel: '姓名',
            sMinWidth: '110px',
        },
        {
            sProp: 'sNuclearNum',
            sLabel: '核医学号',
            sMinWidth: '150px',
        },
        {
            sProp: 'sSexText',
            sLabel: '性别',
            sMinWidth: '80px',
        },
        {
            sProp: 'sAge',
            sLabel: '年龄',
            sMinWidth: '80px',
        },
        {
            sProp: 'dBirthday',
            sLabel: '出生日期',
            sMinWidth: '110px',
        },
        {
            sProp: 'sProjectName',
            sLabel: '检查项目',
            sMinWidth: '110px',
        },
        {
            sProp: 'dAppointmentTime',
            sLabel: '预约日期',
            sMinWidth: '110px',
        },
        {
            sProp: 'sPhone',
            sLabel: '电话',
            sMinWidth: '150px',
        },
        {
            sProp: 'sIdNum',
            sLabel: '身份证号',
            sMinWidth: '150px',
        },
        {
            sProp: 'sCardNum',
            sLabel: '社保号',
            sMinWidth: '150px',
        },
        {
            sProp: 'sInHospitalNO',
            sLabel: '住院号',
            sMinWidth: '110px',
        },
        {
            sProp: 'sOutpatientNO',
            sLabel: '门诊号',
            sMinWidth: '110px',
        },
        {
            sProp: 'sVisitCard',
            sLabel: '就诊卡号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sHealthCardNO',
            sLabel: '健康卡号',
            sMinWidth: '100px',
        },
        {
            sProp: 'sApplyDepartText',
            sLabel: '申请科室',
            sMinWidth: '110px',
        },
        {
            sProp: 'sClinicalDiagnosis',
            sLabel: '临床诊断',
            sMinWidth: '200px',
        },
    ],

}
