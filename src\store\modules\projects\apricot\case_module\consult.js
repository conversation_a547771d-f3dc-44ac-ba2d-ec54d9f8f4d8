import { getBPMSetFindKeys } from '$supersetApi/projects/apricot/system/bpmSet.js'
const state = {
  businessMenus: [],
  switchTemplate: null,
  showTemplate: null,
}

const getters = {
  
}

const mutations  = {
	setBusinessMenus(state, payload){
    state.businessMenus = payload.businessMenus
	},
  isCustomTemplate(state, payload) {
    // 默认显示模板
    let showTemplate = true;

    if (state.switchTemplate === 'false' || state.switchTemplate === false) {
      showTemplate = false
    }

    if (payload.callBack) {
      payload.callBack(showTemplate);
    }
  }
}

const actions = {
  async getSystemParams({ state }){
    // 获取时间，判断显示什么模板
    // 有时间，不在请求获取时间
    if (state.switchTemplate !== null) {
      return;
    }
  	await getBPMSetFindKeys(['SwitchTemplateDate']).then(res => {
      if (res.success && res.data[0]) {
        state.switchTemplate = res.data[0].sValue || 'true'
      }else {
        // 查询不到配置,显示新模板
        state.switchTemplate = 'true'
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}