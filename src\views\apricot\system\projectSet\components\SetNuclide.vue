<template>
    <!-- 项目设置 核素 -->
    <div class="c-flex-context">
        <div class="c-form">
            <div class="c-form-btn">
                <el-button-icon-fa
                    type="primary" 
                    icon="el-icon-plus"
                    @click="handleAdd">新增</el-button-icon-fa>
                <el-button-icon-fa plain 
                    type="primary" 
                    :loading="loading"
                    icon="el-icon-refresh"
                    @click="mxDoRefresh()">刷新</el-button-icon-fa> 
            </div>
            
            <div class="c-form-search">
                <div>
                    <el-input v-model="condition.sNuclideName"
                        placeholder="核素名称"
                        clearable
                        @keyup.enter.native="mxDoSearch">
                    </el-input>
                </div>
                <div>
                    <el-input
                        placeholder="上下标名称"
                        v-model="condition.sNuclideSupName"
                        clearable
                        @keyup.enter.native="mxDoSearch"></el-input>
                </div>

                <div style="width: auto;">
                    <el-button-icon-fa icon="el-icon-search" type="primary" @click="mxDoSearch" :loading="loading"></el-button-icon-fa>
                </div>
            </div>

        </div>
        <div class="c-flex-auto">

            <div class="c-content">
                <el-table :data="tableData"
                    v-drag:[config]="tableData"
                    v-loading="loading"
                    ref="mainTable"
                    size="small"
                    id="nuclideTable"
                    v-if="reRender"
                    @row-click="onClickRow"
                    border
                    stripe
                    height="100%"
                    style="width: 100%">
                    <el-table-column v-for="item in configTable.filter(_i=> !_i.iIsHide)"
                        show-overflow-tooltip
                        :key="item.index"
                        :prop="item.sProp"
                        :label="item.sLabel"
                        :fixed="item.sFixed"
                        :align="item.sAlign"
                        :width="item.sWidth"
                        :min-width="item.sMinWidth"
                        :sortable="!!item.iSort" >
                        <template v-slot="scope">
                            <template v-if="item.sProp === 'action'">
                                <el-button
                                    size="small"
                                    link
                                    type="primary"
                                    @click="handleEdit(scope.row)"
                                    >编辑
                                     <template #icon>
                                        <Icon name="el-icon-edit" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class @click="onClickDel(scope.row)">
                                    删除
                                    <template #icon>
                                        <Icon name="el-icon-delete" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                                <el-divider direction="vertical"></el-divider>
                                <el-button size="small" link class="i-sort">排序
                                    <template #icon>
                                        <Icon name="el-icon-rank" color="">
                                        </Icon>
                                    </template>
                                </el-button>
                            </template>
                            <template v-else-if="item.sProp.slice(0,1) === 'd'">
                                {{ scope.row[`${item.sProp}`] | mxToDate() }}
                            </template>
                            <template v-else-if="item.sProp === 'iIsEnable'">
                                <!-- <el-switch @click.stop.native="onChangeEnable($event, scope.row, scope.$index)"
                                    v-model="scope.row.iIsEnable"
                                    :active-value="1"
                                    :inactive-value="0"></el-switch> -->
                                    <span v-if="scope.row.iIsEnable" class="icon-green"> 是 </span>
                                    <span v-else> 否 </span>
                            </template>
                            <template v-else-if="item.sProp === 'sNuclideSupName'">
                                <span v-html="scope.row[`${item.sProp}`]"></span>
                            </template>
                            <template v-else>
                                {{scope.row[`${item.sProp}`]}}
                            </template>
                        </template>
                        <!-- <template 
                            v-slot:header="scope">
                            <span>{{item.sLabel}}</span>
                            <i v-if="item.sProp === 'action'"
                                class="el-icon-rank i-sort"
                                style="cursor: pointer;font-size: 14px;padding-left: 5px;"
                                title="首次或无法排序时，点击初始化排序"
                                @click="autoSort"></i>
                        </template> -->
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <el-dialog :title="dialogTitle"
            :modelValue="dialogVisible"
            append-to-body
            class="t-default"
            width="600"
            :close-on-click-modal="false"
            @close="closeDialog"
            >
            <div class="flex flex-col">
                <el-form ref="refEditLayer"
                    :model="editLayer.form"
                    :rules="rules"
                    label-width="140px"
                    >
                    <el-col :span="24">
                        <el-form-item prop="sNuclideName" label="核素名称：">
                            <el-input
                                v-model="editLayer.form.sNuclideName"
                                clearable
                                placeholder="核素名称"></el-input>
                        </el-form-item>
                    </el-col>
                    
                    <el-col :span="24">
                        <el-form-item prop="sNuclideSupName" label="上下标名称：">
                            <el-input v-model="editLayer.form.sNuclideSupName"
                                placeholder="核素上下标名称"
                                clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="sNuclideSupName" label="显示效果：">
                            <div class="i-show">
                                <span v-html="editLayer.form.sNuclideSupName"></span>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="衰变周期：">
                            <el-input-number v-model="editLayer.form.fDecayPeriod"
                                controls-position="right"
                                :min="0"
                                style="width:100%"></el-input-number>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item prop="iDecayUnit" label="衰变单位：">
                            <el-select v-model="editLayer.form.iDecayUnit"
                                v-select-name="{ formData: editLayer.form, fields: { sProp: 'iDecayUnit', sPropName: 'sDecayUnitName' } }"
                                placeholder=" "
                                style="width:100%">
                                <el-option v-for="item in optionsLoc.iDecayUnitOption"
                                    :key="item.sValue"
                                    :value="item.sValue"
                                    :label="item.sName">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="启  用：">
                            <el-radio-group v-model="editLayer.form.iIsEnable">
                                <el-radio :label="1">启用</el-radio>
                                <el-radio :label="0">禁用</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-form>

                <div class="c-tip">
                    <p><span>上标请添加:</span>&lt;sup&gt;&lt;/sup&gt;标签,<span>如:</span>&lt;sup&gt;99m&lt;/sup&gt;Tc-SC<span style="padding-left: 20px;">,效果:</span><sup>99m</sup>Tc-SC</p>
                    <p><span>下标请添加:</span>&lt;sub&gt;&lt;/sub&gt;标签,<span>如:</span>NaCHO&lt;sub&gt;99m&lt;/sub&gt;<span>,效果:</span>NaCHO<sub>99m</sub></p>
                </div>
            </div>
            <template #footer><div >
                <el-button-icon-fa :loading="editLayer.loading" icon="el-icon-check" type="primary" @click="handleSave">保存</el-button-icon-fa>
                <el-button-icon-fa @click="closeDialog" icon="el-icon-close">取消</el-button-icon-fa>
                
            </div></template>
        </el-dialog>
    </div>
</template>
<script>
import Api from '$supersetApi/projects/apricot/appointment/projectSet.js'
import { mixinTable, mixinTableDrag } from '$supersetResource/js/projects/apricot/index.js'
export default {
    name: 'SetNuclide',
    mixins: [mixinTable, mixinTableDrag],
    props: {
    },
    data () {
        return {
            dialogTitle:'新增',
            dialogVisible: false,
            visible: true,
            configTable: [
                {
                    sProp: 'sNuclideName', sLabel: '核素名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                
                {
                    sProp: 'sNuclideSupName', sLabel: '核素上下标名称',
                    sAlign: 'left', sMinWidth: '100px',
                },
                {
                    sProp: 'fDecayPeriod', sLabel: '衰变周期',
                    sAlign: 'left', sWidth: '100px',
                },
                {
                    sProp: 'sDecayUnitName', sLabel: '衰变单位',
                    sAlign: 'left', sWidth: '100px',
                },
                {
                    sProp: 'iIsEnable', sLabel: '启用',
                    sAlign: 'center', sWidth: '120px',
                },
                {
                    sProp: 'action', sLabel: '操作',
                    sAlign: 'center', sWidth: '220px',
                }
            ],
            rules: {
                sNuclideName: [{ required: true, message: '请输入名称' }]
            },
            tableData: [],
            reRender: true,
            defualtVal: {
                editLayer: {
                    iIsEnable: 1,
                    iDecayUnit: 2,
                }
            },
            condition: {
                // iIsEnable: '1'
            },
            page: { pageCurrent: 1, pageSize: 9999 },
            optionsLoc: {
                isEnable: [
                    { sValue: '', sName: '全部' },
                    { sValue: '1', sName: '启用' },
                    { sValue: '0', sName: '禁用' },
                ],
                iDecayUnitOption: [
                    // 1=秒；2=分；3=时；4=天；5=月；6=年；
                    { sValue: 1, sName: '秒' },
                    { sValue: 2, sName: '分' },
                    { sValue: 3, sName: '时' },
                    { sValue: 4, sName: '天' },
                    { sValue: 5, sName: '月' },
                    { sValue: 6, sName: '年' },
                ]
            },
            hidePopover: false,
            sortApi: Api.sortNuclide
        }
    },
    methods: {
          // 新增
        handleAdd() {
            let params = {
               iIsEnable: 1,
               iDecayUnit: 2
            }
            this.editLayer.form = { ...params };
            this.dialogTitle = '新增';
            this.dialogVisible = true
            let timeout = setTimeout(()=>{
                this.$refs['refEditLayer'].clearValidate();
                clearTimeout(timeout)
            }, 100)
        },
        closeDialog() {
            this.dialogVisible = false;
        },
        handleEdit(row) {
            this.dialogTitle = '编辑'
            this.dialogVisible = true
            this.editLayer.form = { ...row };
            this.$nextTick(()=>{
                this.$refs['refEditLayer'].clearValidate();
            })
            
        },
        handleSave() {
            this.editLayer.loading = true
            let params = Object.assign({},this.editLayer.form)
            this.$refs['refEditLayer'].validate( (valid) =>{
                if(valid) {
                    this.saveData(params)  
                    return
                }
                this.editLayer.loading = false
            })
        },
        // 把null转成 undefined 避免el-input-number标签元素 显示0
        null2Undefin () {
            for (let i in this.editLayer.form) {
                if (this.editLayer.form[i] === null) {
                    this.editLayer.form[i] = undefined
                }

            }
        },
        rowDblclick () {
            this.mxOpenDialog(4, '111');
            this.null2Undefin();
        },
        // 改变状态
        onChangeEnable (e, row, index) {
            Api.disabledNuclide({ sId: row.sId, iVersion: row.iVersion, iIsEnable: row.iIsEnable }).then((res) => {
                if (res.success) {
                    this.$message({
                        message: res.msg,
                        type: 'success',
                        duration: 3000
                    });
                    this.tableData[index].iVersion += 1
                    // this.mxGetTableList();
                    return;
                }
                this.$message({
                    message: res.msg,
                    type: 'error',
                    duration: 3000
                });
            })
        },
        // 删除
        onClickDel (row) {
            this.$confirm(`确定要删除【 ${row.sNuclideName} 】吗？`, '提示', {
                confirmButtonClass: 'i-device-primary',
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'error'
            }).then(() => {
                Api.delNuclide({ sId: row.sId, iVersion: row.iVersion }).then((res) => {
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        // 如果编辑的是删除的，清空编辑内容
                        if (this.editLayer.form.sId && this.editLayer.form.sId === row.sId) {
                            this.mxOpenDialog(1, 'no-title')
                        }
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                })
            })
        },
        /**
         * 保存数据
         */
        saveData (params) {
            if( !params.sId) {
                Api.addNuclide(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.mxGetTableList();
                        this.dialogVisible = false
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            } else {
                Api.editNuclide(params).then((res) => {
                    this.editLayer.loading = false;
                    if (res.success) {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 3000
                        });
                        this.mxOpenDialog(1, 'no-title')
                        this.dialogVisible = false
                        this.mxGetTableList();
                        return;
                    }
                    this.$message({
                        message: res.msg,
                        type: 'error',
                        duration: 3000
                    });
                }).catch(() => {
                    this.editLayer.loading = false;
                })
            }
        },
        /**
         * 获取表格数据
         */
        getData (params) {
            const jsonData = params.condition
            Api.getNuclideData(jsonData).then((res) => {
                if (res.success) {
                    this.tableData = res?.data || []
                    this.loading = false;
                    // 赋选中状态
                    this.mxSetSelected()
                }
                this.loading = false;
            }).catch(() => {
                this.loading = false;
            })
        },
    },
    mounted () {
        this.mxOpenDialog(1, 'no-title')
    },
};
</script>
<style lang="scss" scoped>


.delete-color {
    color: #f56c6c;
}

.c-form .c-form-search {
    >div{
        width: 240px;
        margin:0 5px;
    }
}
.c-tip {
    border: 1px solid #f1f1f1;
    background-color: #fafafa;
    border-radius: 3px;
    user-select: text;
    color: #999;
    p > span {
        font-weight: bold;
        padding: 0px 5px;
    }
}
.i-show {
    width: 100%;
    height: 100%;
    background-color: #f4f8fb;
    text-indent: 10px;
}

</style>
